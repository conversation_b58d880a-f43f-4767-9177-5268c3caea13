#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbdwfref.h>
#include <realdwg/base/dbdgnref.h>
#include <realdwg/base/dbpdfref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gematrix3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// Image and Media Data Structures (Based on RealDwgFileIO rdImage.cpp)
//=======================================================================================

struct ImageGeometry {
    std::string imagePath;              // Image file path
    std::string imageFileName;          // Image file name
    Point3d insertionPoint;             // Image insertion point
    Vector3d uVector;                   // U direction vector
    Vector3d vVector;                   // V direction vector
    
    // Image dimensions
    double imageWidth = 0.0;            // Image width in drawing units
    double imageHeight = 0.0;           // Image height in drawing units
    int pixelWidth = 0;                 // Image width in pixels
    int pixelHeight = 0;                // Image height in pixels
    
    // Image properties
    double brightness = 50.0;           // Brightness (0-100)
    double contrast = 50.0;             // Contrast (0-100)
    double fade = 0.0;                  // Fade (0-100)
    bool isClipped = false;             // Image is clipped
    bool isOn = true;                   // Image is visible
    bool isTransparent = false;         // Background is transparent
    
    // Clipping boundary
    std::vector<Point3d> clipBoundary;  // Clipping polygon vertices
    bool clipInverted = false;          // Inverted clipping
    
    // Image format information
    enum class Format {
        Unknown, BMP, JPEG, PNG, TIFF, GIF, PCX, TGA, CALS, FLIC, GEOSPOT, IG4, IGS, PICT, RLC
    } format = Format::Unknown;
    
    int bitsPerPixel = 24;              // Bits per pixel
    bool hasColorMap = false;           // Has color map
    int colorMapSize = 0;               // Color map size
    
    bool IsValid() const;
    bool HasClipping() const { return isClipped && !clipBoundary.empty(); }
    double GetAspectRatio() const;
    BoundingBox3D CalculateBounds() const;
};

struct OLEGeometry {
    enum class Type {
        Embedded,           // Embedded OLE object
        Linked,             // Linked OLE object
        Static              // Static OLE object
    } type = Type::Embedded;
    
    std::string applicationName;        // OLE application name
    std::string className;              // OLE class name
    std::string fileName;               // File name for linked objects
    std::string displayName;            // Display name
    
    // OLE object position and size
    Point3d insertionPoint;             // Insertion point
    Vector3d uVector;                   // U direction vector
    Vector3d vVector;                   // V direction vector
    double width = 0.0;                 // Object width
    double height = 0.0;                // Object height
    
    // OLE object properties
    bool isLocked = false;              // Object is locked
    bool isLinked = false;              // Object is linked
    bool isAutomatic = true;            // Automatic update
    bool isManual = false;              // Manual update
    
    // OLE data
    std::vector<uint8_t> oleData;       // OLE object data
    std::vector<uint8_t> metafileData;  // Metafile data for display
    
    // Display properties
    enum class Quality {
        Draft, Normal, High
    } quality = Quality::Normal;
    
    bool isVisible = true;              // Object is visible
    double transparency = 0.0;          // Transparency (0-100)
    
    bool IsValid() const;
    bool HasData() const { return !oleData.empty(); }
    size_t GetDataSize() const { return oleData.size(); }
};

struct UnderlayGeometry {
    enum class Type {
        DWF,                // DWF underlay
        DGN,                // DGN underlay
        PDF                 // PDF underlay
    } type = Type::PDF;
    
    std::string filePath;               // Underlay file path
    std::string fileName;               // Underlay file name
    std::string layoutName;             // Layout/sheet name
    
    // Underlay position and transformation
    Point3d insertionPoint;             // Insertion point
    Vector3d uVector;                   // U direction vector
    Vector3d vVector;                   // V direction vector
    Vector3d normal = Vector3d(0, 0, 1); // Normal vector
    
    // Underlay properties
    double scaleX = 1.0;                // X scale factor
    double scaleY = 1.0;                // Y scale factor
    double rotation = 0.0;              // Rotation angle
    
    // Display properties
    bool isOn = true;                   // Underlay is visible
    bool isMonochrome = false;          // Display in monochrome
    bool isAdjustedForBackground = true; // Adjust for background
    double contrast = 50.0;             // Contrast (0-100)
    double fade = 0.0;                  // Fade (0-100)
    
    // Clipping
    bool isClipped = false;             // Underlay is clipped
    std::vector<Point3d> clipBoundary;  // Clipping polygon vertices
    bool clipInverted = false;          // Inverted clipping
    
    // Underlay-specific properties
    union {
        struct {
            bool showPaperBackground;   // Show paper background
            bool useHostUnits;          // Use host drawing units
        } dwf;
        
        struct {
            bool showRasterRef;         // Show raster references
            bool useHostUnits;          // Use host drawing units
        } dgn;
        
        struct {
            int pageNumber;             // PDF page number
            bool showAnnotations;       // Show PDF annotations
            bool useGeometryCache;      // Use geometry cache
        } pdf;
    };
    
    bool IsValid() const;
    bool HasClipping() const { return isClipped && !clipBoundary.empty(); }
    BoundingBox3D CalculateBounds() const;
};

struct PointCloudGeometry {
    std::string filePath;               // Point cloud file path
    std::string fileName;               // Point cloud file name
    
    // Point cloud position and transformation
    Point3d insertionPoint;             // Insertion point
    Vector3d uVector = Vector3d(1, 0, 0); // U direction vector
    Vector3d vVector = Vector3d(0, 1, 0); // V direction vector
    Vector3d normal = Vector3d(0, 0, 1);  // Normal vector
    
    // Point cloud properties
    double scale = 1.0;                 // Scale factor
    double rotation = 0.0;              // Rotation angle
    bool isLocked = false;              // Point cloud is locked
    bool isOn = true;                   // Point cloud is visible
    
    // Display properties
    enum class StyleType {
        Intensity, Elevation, Classification, Normal, RGB
    } styleType = StyleType::RGB;
    
    double pointSize = 1.0;             // Point display size
    int maxPointsToDisplay = 1000000;   // Maximum points to display
    
    // Clipping
    bool isClipped = false;             // Point cloud is clipped
    std::vector<Point3d> clipBoundary;  // Clipping polygon vertices
    bool clipInverted = false;          // Inverted clipping
    
    // Point cloud statistics
    size_t totalPoints = 0;             // Total number of points
    BoundingBox3D boundingBox;          // Point cloud bounding box
    
    bool IsValid() const;
    bool HasClipping() const { return isClipped && !clipBoundary.empty(); }
    bool HasData() const { return totalPoints > 0; }
};

struct WipeoutGeometry {
    std::vector<Point3d> boundary;      // Wipeout boundary vertices
    Point3d insertionPoint;             // Insertion point
    Vector3d uVector = Vector3d(1, 0, 0); // U direction vector
    Vector3d vVector = Vector3d(0, 1, 0); // V direction vector
    
    // Wipeout properties
    bool isOn = true;                   // Wipeout is visible
    bool isFrameVisible = false;        // Frame is visible
    
    bool IsValid() const;
    bool IsClosed() const;
    double CalculateArea() const;
};

//=======================================================================================
// Image and Media Validation Results
//=======================================================================================

struct ImageValidationResult : public DWGValidationResult {
    bool hasValidPath = false;
    bool hasValidDimensions = false;
    bool hasValidPosition = false;
    bool hasValidClipping = false;
    bool fileExists = false;
    ImageGeometry::Format detectedFormat = ImageGeometry::Format::Unknown;
    
    void AddImageError(const std::string& error) {
        AddError("Image: " + error);
    }
    
    void AddImageWarning(const std::string& warning) {
        AddWarning("Image: " + warning);
    }
};

struct UnderlayValidationResult : public DWGValidationResult {
    bool hasValidPath = false;
    bool hasValidLayout = false;
    bool hasValidPosition = false;
    bool hasValidClipping = false;
    bool fileExists = false;
    
    void AddUnderlayError(const std::string& error) {
        AddError("Underlay: " + error);
    }
    
    void AddUnderlayWarning(const std::string& warning) {
        AddWarning("Underlay: " + warning);
    }
};

//=======================================================================================
// DWG Image and Media Processor (Based on RealDwgFileIO rdImage.cpp)
//=======================================================================================

class DWGImageProcessor : public DWGEntityProcessor {
public:
    DWGImageProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGImageProcessor"; }

    // Image and media processing methods
    DWGProcessingStatus ProcessImage(const ImageGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessOLE(const OLEGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessUnderlay(const UnderlayGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPointCloud(const PointCloudGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessWipeout(const WipeoutGeometry& geometry, const std::string& layer = "");

    // Specific underlay types
    DWGProcessingStatus ProcessDWFUnderlay(const UnderlayGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessDGNUnderlay(const UnderlayGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPDFUnderlay(const UnderlayGeometry& geometry, const std::string& layer = "");

    // Validation methods
    ImageValidationResult ValidateImageGeometry(const ImageGeometry& geometry) const;
    UnderlayValidationResult ValidateUnderlayGeometry(const UnderlayGeometry& geometry) const;
    bool ValidateOLEGeometry(const OLEGeometry& geometry) const;
    bool ValidatePointCloudGeometry(const PointCloudGeometry& geometry) const;
    bool ValidateWipeoutGeometry(const WipeoutGeometry& geometry) const;

    // File and path validation
    bool ValidateImageFile(const std::string& filePath) const;
    bool ValidateUnderlayFile(const std::string& filePath, UnderlayGeometry::Type type) const;
    bool ValidatePointCloudFile(const std::string& filePath) const;
    ImageGeometry::Format DetectImageFormat(const std::string& filePath) const;
    
    // Image processing and optimization
    bool OptimizeImageGeometry(ImageGeometry& geometry) const;
    bool RepairImageGeometry(ImageGeometry& geometry) const;
    bool ValidateImageDimensions(ImageGeometry& geometry) const;
    bool CalculateImageBounds(const ImageGeometry& geometry, BoundingBox3D& bounds) const;

    // Clipping processing
    bool ProcessImageClipping(const ImageGeometry& geometry) const;
    bool ProcessUnderlayClipping(const UnderlayGeometry& geometry) const;
    bool ValidateClippingBoundary(const std::vector<Point3d>& boundary) const;
    bool OptimizeClippingBoundary(std::vector<Point3d>& boundary) const;

    // Path resolution and management
    std::string ResolveImagePath(const std::string& relativePath) const;
    std::string ResolveUnderlayPath(const std::string& relativePath) const;
    bool AddSearchPath(const std::string& path);
    std::vector<std::string> GetSearchPaths() const;
    bool FindFile(const std::string& fileName, std::string& fullPath) const;

private:
    // Image processing helpers
    bool ProcessImageTransformation(const ImageGeometry& geometry) const;
    bool ValidateImageFormat(const std::string& filePath, ImageGeometry::Format expectedFormat) const;
    bool GetImageDimensions(const std::string& filePath, int& width, int& height) const;
    bool CalculateImageVectors(ImageGeometry& geometry) const;
    
    // OLE processing helpers
    bool ProcessOLEData(const OLEGeometry& geometry) const;
    bool ValidateOLEApplication(const std::string& applicationName) const;
    bool ProcessOLEMetafile(const std::vector<uint8_t>& metafileData) const;
    
    // Underlay processing helpers
    bool ProcessUnderlayTransformation(const UnderlayGeometry& geometry) const;
    bool ValidateUnderlayLayout(const std::string& filePath, const std::string& layoutName, UnderlayGeometry::Type type) const;
    std::vector<std::string> GetAvailableLayouts(const std::string& filePath, UnderlayGeometry::Type type) const;
    
    // Point cloud processing helpers
    bool ProcessPointCloudTransformation(const PointCloudGeometry& geometry) const;
    bool ValidatePointCloudFormat(const std::string& filePath) const;
    bool GetPointCloudStatistics(const std::string& filePath, size_t& pointCount, BoundingBox3D& bounds) const;
    
    // Wipeout processing helpers
    bool ProcessWipeoutBoundary(const WipeoutGeometry& geometry) const;
    bool ValidateWipeoutBoundary(const std::vector<Point3d>& boundary) const;
    bool OptimizeWipeoutBoundary(std::vector<Point3d>& boundary) const;
    
    // Geometric calculations
    Vector3d CalculateImageUVector(const ImageGeometry& geometry) const;
    Vector3d CalculateImageVVector(const ImageGeometry& geometry) const;
    Point3d CalculateImageCorner(const ImageGeometry& geometry, int corner) const;
    std::vector<Point3d> CalculateImageCorners(const ImageGeometry& geometry) const;
    
    // Clipping helpers
    bool IsPointInsideClipping(const Point3d& point, const std::vector<Point3d>& boundary, bool inverted = false) const;
    std::vector<Point3d> ClipLineToPolygon(const Point3d& start, const Point3d& end, const std::vector<Point3d>& boundary) const;
    bool ValidateClippingPolygon(const std::vector<Point3d>& boundary) const;
    
    // File system helpers
    bool FileExists(const std::string& filePath) const;
    std::string GetFileExtension(const std::string& filePath) const;
    std::string GetFileName(const std::string& filePath) const;
    std::string GetDirectoryPath(const std::string& filePath) const;
    std::string CombinePaths(const std::string& path1, const std::string& path2) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbRasterImage* CreateDWGImage(const ImageGeometry& geometry) const;
    AcDbOle2Frame* CreateDWGOLE(const OLEGeometry& geometry) const;
    AcDbWipeout* CreateDWGWipeout(const WipeoutGeometry& geometry) const;
    AcDbUnderlayReference* CreateDWGUnderlay(const UnderlayGeometry& geometry) const;
    AcDbDwfReference* CreateDWGDWFUnderlay(const UnderlayGeometry& geometry) const;
    AcDbDgnReference* CreateDWGDGNUnderlay(const UnderlayGeometry& geometry) const;
    AcDbPdfReference* CreateDWGPDFUnderlay(const UnderlayGeometry& geometry) const;
    AcDbPointCloud* CreateDWGPointCloud(const PointCloudGeometry& geometry) const;
    
    // Image definition management
    AcDbObjectId GetOrCreateImageDef(const ImageGeometry& geometry) const;
    AcDbObjectId GetOrCreateUnderlayDef(const UnderlayGeometry& geometry) const;
    
    // Property setting helpers
    bool SetImageProperties(AcDbRasterImage* image, const ImageGeometry& geometry) const;
    bool SetOLEProperties(AcDbOle2Frame* ole, const OLEGeometry& geometry) const;
    bool SetUnderlayProperties(AcDbUnderlayReference* underlay, const UnderlayGeometry& geometry) const;
    bool SetPointCloudProperties(AcDbPointCloud* pointCloud, const PointCloudGeometry& geometry) const;
    
    // Clipping helpers
    bool SetImageClipping(AcDbRasterImage* image, const std::vector<Point3d>& boundary, bool inverted) const;
    bool SetUnderlayClipping(AcDbUnderlayReference* underlay, const std::vector<Point3d>& boundary, bool inverted) const;
    
    // Error handling for RealDWG operations
    bool HandleImageCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Search paths for files
    std::vector<std::string> m_searchPaths;
    std::string m_defaultImagePath;
    std::string m_defaultUnderlayPath;
    
    // Statistics and debugging
    mutable size_t m_processedImages = 0;
    mutable size_t m_processedOLEs = 0;
    mutable size_t m_processedUnderlays = 0;
    mutable size_t m_processedPointClouds = 0;
    mutable size_t m_processedWipeouts = 0;
    mutable size_t m_repairedImages = 0;
    mutable size_t m_resolvedPaths = 0;
    
    // Configuration
    double m_imageTolerance = 1e-6;
    double m_clippingTolerance = 1e-6;
    bool m_enableImageOptimization = true;
    bool m_enablePathResolution = true;
    bool m_enableFileValidation = true;
    bool m_autoRepairImages = true;
    int m_maxImageSize = 16384;         // Maximum image dimension
    size_t m_maxFileSize = 100 * 1024 * 1024; // Maximum file size (100MB)
};

} // namespace IModelExport
