#pragma once

#include "DWGExporter.h"
#include "../../core/ConversionStrategy.h"
#include "../../core/GeometryProcessor.h"
#include "../../core/MaterialManager.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/acgi.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbdict.h>
#include <realdwg/base/dbgroup.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbxrecord.h>
#endif

#include <memory>
#include <unordered_map>
#include <queue>

namespace IModelExport {

//=======================================================================================
// DWG Conversion Engine - Advanced iModel to DWG conversion
//=======================================================================================

class DWGConverter {
public:
    DWGConverter(std::shared_ptr<ExportContext> context);
    ~DWGConverter();

    //===================================================================================
    // Main Conversion Interface
    //===================================================================================

    // Convert complete iModel to DWG
    bool ConvertIModel(const IModelDb& imodel, const DWGExportOptions& options);
    
    // Convert individual elements
    bool ConvertElement(const ElementInfo& element);
    bool ConvertElementBatch(const std::vector<ElementInfo>& elements);
    
    // Convert specific geometry types
    bool ConvertGeometry(const GeometryData& geometry, const std::string& layer = "");
    bool ConvertMaterial(const Material& material);
    
    //===================================================================================
    // Element Type Conversion
    //===================================================================================

    // Building elements
    bool ConvertWallElement(const ElementInfo& element);
    bool ConvertColumnElement(const ElementInfo& element);
    bool ConvertBeamElement(const ElementInfo& element);
    bool ConvertSlabElement(const ElementInfo& element);
    bool ConvertDoorElement(const ElementInfo& element);
    bool ConvertWindowElement(const ElementInfo& element);
    bool ConvertStairElement(const ElementInfo& element);
    bool ConvertRoofElement(const ElementInfo& element);
    
    // MEP elements
    bool ConvertPipeElement(const ElementInfo& element);
    bool ConvertDuctElement(const ElementInfo& element);
    bool ConvertCableElement(const ElementInfo& element);
    bool ConvertEquipmentElement(const ElementInfo& element);
    
    // Structural elements
    bool ConvertFoundationElement(const ElementInfo& element);
    bool ConvertBraceElement(const ElementInfo& element);
    bool ConvertConnectionElement(const ElementInfo& element);
    
    // Site elements
    bool ConvertRoadElement(const ElementInfo& element);
    bool ConvertBridgeElement(const ElementInfo& element);
    bool ConvertTunnelElement(const ElementInfo& element);
    bool ConvertTerrainElement(const ElementInfo& element);
    
    //===================================================================================
    // Geometry Conversion Strategies
    //===================================================================================

    // Curve conversion
    bool ConvertLinearGeometry(const GeometryData& geometry, const std::string& layer);
    bool ConvertArcGeometry(const GeometryData& geometry, const std::string& layer);
    bool ConvertSplineGeometry(const GeometryData& geometry, const std::string& layer);
    bool ConvertPolylineGeometry(const GeometryData& geometry, const std::string& layer);
    
    // Surface conversion
    bool ConvertPlanarSurface(const GeometryData& geometry, const std::string& layer);
    bool ConvertCurvedSurface(const GeometryData& geometry, const std::string& layer);
    bool ConvertNURBSSurface(const GeometryData& geometry, const std::string& layer);
    bool ConvertMeshSurface(const GeometryData& geometry, const std::string& layer);
    
    // Solid conversion
    bool ConvertExtrudedSolid(const GeometryData& geometry, const std::string& layer);
    bool ConvertRevolvedSolid(const GeometryData& geometry, const std::string& layer);
    bool ConvertSweptSolid(const GeometryData& geometry, const std::string& layer);
    bool ConvertBooleanSolid(const GeometryData& geometry, const std::string& layer);
    bool ConvertBRepSolid(const GeometryData& geometry, const std::string& layer);
    
    //===================================================================================
    // Advanced Conversion Features
    //===================================================================================

    // Level of Detail (LOD) conversion
    bool ConvertWithLOD(const ElementInfo& element, ExportLOD lod);
    bool GenerateLODVariants(const ElementInfo& element);
    bool SimplifyGeometryForLOD(GeometryData& geometry, ExportLOD lod);
    
    // Parametric conversion
    bool ConvertParametricElement(const ElementInfo& element);
    bool CreateParametricBlock(const ElementInfo& element);
    bool ConvertConstraints(const ElementInfo& element);
    
    // Assembly conversion
    bool ConvertAssembly(const ElementInfo& element);
    bool ConvertSubAssembly(const ElementInfo& element);
    bool CreateAssemblyHierarchy(const std::vector<ElementInfo>& elements);
    
    //===================================================================================
    // Material and Appearance Conversion
    //===================================================================================

    // Material conversion
    bool ConvertMaterialDefinition(const Material& material);
    bool ConvertMaterialMapping(const ElementInfo& element);
    bool ConvertTextureMapping(const Material& material);
    bool ConvertRenderMaterial(const Material& material);
    
    // Visual styles
    bool ConvertDisplayStyle(const ElementInfo& element);
    bool ConvertLineStyle(const ElementInfo& element);
    bool ConvertFillPattern(const ElementInfo& element);
    bool ConvertTransparency(const ElementInfo& element);
    
    //===================================================================================
    // Annotation and Documentation
    //===================================================================================

    // Text and labels
    bool ConvertTextElement(const ElementInfo& element);
    bool ConvertLabelElement(const ElementInfo& element);
    bool ConvertCalloutElement(const ElementInfo& element);
    
    // Dimensions
    bool ConvertLinearDimension(const ElementInfo& element);
    bool ConvertAngularDimension(const ElementInfo& element);
    bool ConvertRadialDimension(const ElementInfo& element);
    bool ConvertDiameterDimension(const ElementInfo& element);
    bool ConvertOrdinateDimension(const ElementInfo& element);
    
    // Symbols and details
    bool ConvertSymbolElement(const ElementInfo& element);
    bool ConvertDetailElement(const ElementInfo& element);
    bool ConvertSectionElement(const ElementInfo& element);
    bool ConvertElevationElement(const ElementInfo& element);
    
    //===================================================================================
    // Coordinate System and Units
    //===================================================================================

    // Coordinate transformation
    bool SetupCoordinateSystem(const IModelDb& imodel);
    bool TransformGeometry(GeometryData& geometry);
    bool ConvertUnits(const std::string& sourceUnits, const std::string& targetUnits);
    
    // Georeferencing
    bool ConvertGeoLocation(const IModelDb& imodel);
    bool SetupProjectionSystem(const std::string& projectionName);
    bool ConvertGeoCoordinates(std::vector<Point3d>& points);
    
    //===================================================================================
    // Quality Control and Validation
    //===================================================================================

    // Geometry validation
    bool ValidateConvertedGeometry(const GeometryData& original, const GeometryData& converted);
    bool CheckGeometryTolerance(const GeometryData& geometry);
    bool RepairInvalidGeometry(GeometryData& geometry);
    
    // Conversion quality metrics
    struct ConversionQuality {
        double geometryFidelity = 0.0;      // 0.0 to 1.0
        double materialAccuracy = 0.0;      // 0.0 to 1.0
        double propertyCompleteness = 0.0;  // 0.0 to 1.0
        size_t warningCount = 0;
        size_t errorCount = 0;
        std::vector<std::string> issues;
    };
    
    ConversionQuality AnalyzeConversionQuality(const ElementInfo& element);
    ConversionQuality GetOverallQuality() const;
    
    //===================================================================================
    // Performance Optimization
    //===================================================================================

    // Batch processing
    bool EnableBatchMode(size_t batchSize = 100);
    bool ProcessBatch();
    bool FlushBatch();
    
    // Memory management
    bool OptimizeMemoryUsage();
    bool ClearCache();
    size_t GetMemoryUsage() const;
    
    // Parallel processing
    bool EnableParallelProcessing(size_t numThreads = 0);
    bool ProcessElementsParallel(const std::vector<ElementInfo>& elements);
    
    //===================================================================================
    // Export Statistics and Reporting
    //===================================================================================

    struct ConversionStatistics {
        size_t totalElements = 0;
        size_t convertedElements = 0;
        size_t skippedElements = 0;
        size_t errorElements = 0;
        
        std::unordered_map<std::string, size_t> elementTypeCounts;
        std::unordered_map<std::string, size_t> geometryTypeCounts;
        std::unordered_map<std::string, size_t> layerCounts;
        std::unordered_map<std::string, size_t> materialCounts;
        
        double totalConversionTime = 0.0;
        double averageElementTime = 0.0;
        size_t peakMemoryUsage = 0;
        
        std::vector<std::string> warnings;
        std::vector<std::string> errors;
    };
    
    ConversionStatistics GetConversionStatistics() const;
    std::string GenerateConversionReport() const;
    bool ExportStatisticsToFile(const std::string& filePath) const;

private:
    //===================================================================================
    // Internal State and Configuration
    //===================================================================================

    std::shared_ptr<ExportContext> m_context;
    std::shared_ptr<DWGExporter> m_exporter;
    std::shared_ptr<GeometryProcessor> m_geometryProcessor;
    std::shared_ptr<MaterialManager> m_materialManager;
    
    // Conversion configuration
    DWGExportOptions m_options;
    ConversionStatistics m_statistics;
    ConversionQuality m_overallQuality;
    
    // Element processing queue
    std::queue<ElementInfo> m_processingQueue;
    std::vector<ElementInfo> m_currentBatch;
    bool m_batchMode = false;
    size_t m_batchSize = 100;
    
    // Caching and optimization
    std::unordered_map<std::string, std::string> m_layerCache;
    std::unordered_map<std::string, std::string> m_materialCache;
    std::unordered_map<std::string, std::string> m_blockCache;
    std::unordered_map<std::string, GeometryData> m_geometryCache;
    
    // Threading support
    bool m_parallelProcessing = false;
    size_t m_numThreads = 1;
    std::mutex m_conversionMutex;
    
    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Element analysis
    bool AnalyzeElement(const ElementInfo& element);
    std::string DetermineTargetLayer(const ElementInfo& element);
    std::string DetermineElementType(const ElementInfo& element);
    
    // Geometry processing
    bool PreprocessGeometry(GeometryData& geometry);
    bool PostprocessGeometry(GeometryData& geometry);
    bool OptimizeGeometry(GeometryData& geometry);
    
    // Material processing
    std::string ProcessMaterial(const Material& material);
    bool ApplyMaterialToEntity(const std::string& entityId, const std::string& materialId);
    
    // Error handling
    void LogConversionError(const std::string& elementId, const std::string& error);
    void LogConversionWarning(const std::string& elementId, const std::string& warning);
    bool HandleConversionError(const ElementInfo& element, const std::string& error);
    
    // Progress reporting
    bool UpdateProgress(double percentage, const std::string& operation);
    bool CheckCancellation();
    
    // Utility methods
    std::string GenerateUniqueId(const std::string& prefix = "");
    std::string SanitizeName(const std::string& name);
    bool IsValidGeometry(const GeometryData& geometry);
    
    // Cleanup
    void CleanupConversion();
};

//=======================================================================================
// DWG Conversion Utilities
//=======================================================================================

namespace DWGConversionUtils {
    
    // Element type mapping
    std::string MapElementTypeToDWG(ElementType elementType);
    std::string MapGeometryTypeToDWG(GeometryData::Type geometryType);
    
    // Layer naming conventions
    std::string GenerateLayerName(const ElementInfo& element);
    std::string GenerateLayerName(ElementType elementType, const std::string& category = "");
    
    // Block naming conventions
    std::string GenerateBlockName(const ElementInfo& element);
    std::string GenerateUniqueBlockName(const std::string& baseName);
    
    // Geometry conversion helpers
    bool ConvertPointsToDWG(const std::vector<Point3d>& points, std::vector<Point3d>& dwgPoints);
    bool ConvertCurveToDWG(const CurveData& curve, GeometryData& dwgGeometry);
    bool ConvertSurfaceToDWG(const SurfaceData& surface, GeometryData& dwgGeometry);
    
    // Material conversion helpers
    Color ConvertColorToDWG(const Color& color);
    std::string ConvertLineTypeToDWG(const std::string& lineType);
    double ConvertLineWeightToDWG(double lineWeight);
    
    // Validation helpers
    bool ValidateDWGGeometry(const GeometryData& geometry);
    bool ValidateDWGMaterial(const Material& material);
    bool ValidateDWGLayer(const std::string& layerName);
    
    // Performance helpers
    size_t EstimateConversionComplexity(const ElementInfo& element);
    double EstimateConversionTime(const ElementInfo& element);
    size_t EstimateMemoryUsage(const ElementInfo& element);
}

} // namespace IModelExport
