/*---------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/realDwgExported.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/

#include    "rDwgInternal.h"
#include    <Mstn\RealDwg\rDwgUtil.h>
#include    "realdwgExported.h"


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcCmColor   acCmColorFromIndexColor
(
::Int32     index
)
    {
    AcCmColor   cmColor;
    cmColor.setColorIndex((Adesk::Int16) index); //setColor() - deprecated in realdwg2021
    return cmColor;
    }


BEGIN_EXTERN_C

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED AcDbDatabase* realDwg_getDatabase
(
DgnFileP                    fileObj
)
    {
    if (DgnPlatform::DgnFileFormatType::DWG == fileObj->GetTargetFormat() || DgnPlatform::DgnFileFormatType::DXF == fileObj->GetTargetFormat())
        {
        Bentley::RealDwg::RealDwgFileIO*    dwgFileIO = dynamic_cast <Bentley::RealDwg::RealDwgFileIO*> (fileObj->GetDgnFileIO());
        if (NULL != dwgFileIO)
            return  dwgFileIO->GetDwgFileHolder()->GetDatabase ();
        }

    return  NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_getIntegerSystemVariable
(
::Int32&                    valueOut,
const AcDbDatabase*         pDatabase,
const char*                 varName
)
    {
    if (0 == _stricmp(varName, "LUNITS"))
        valueOut = (::Int32) pDatabase->lunits ();
    else if (0 == _stricmp(varName, "LUPREC"))
        valueOut = (::Int32) pDatabase->luprec ();
    else if (0 == _stricmp(varName, "AUNITS"))
        valueOut = (::Int32) pDatabase->aunits ();
    else if (0 == _stricmp(varName, "AUPREC"))
        valueOut = (::Int32) pDatabase->auprec ();
    else if (0 == _stricmp(varName, "INSUNITS"))
        valueOut = (::Int32) pDatabase->insunits ();
    else if (0 == _stricmp(varName, "TREEDEPTH"))
        valueOut = (::Int32) pDatabase->treedepth ();
    else if (0 == _stricmp(varName, "CECOLOR"))
        valueOut = (::Int32) pDatabase->cecolor().colorIndex ();
    else if (0 == _stricmp(varName, "PROXYGRAPHICS"))
        valueOut = (::Int32) pDatabase->saveproxygraphics ();
    else if (0 == _stricmp(varName, "MEASUREMENT"))
        valueOut = (::Int32) pDatabase->measurement ();
    else if (0 == _stricmp(varName, "ISOLINES"))
        valueOut = (::Int32) pDatabase->isolines ();
    else if (0 == _stricmp(varName, "DIMLUNIT"))
        valueOut = (::Int32) pDatabase->dimunit ();
    else if (0 == _stricmp(varName, "DWGCODEPAGE"))
        valueOut = (::Int32) acdbHostApplicationServices()->getSystemCodePage ();
    else if (0 == _stricmp(varName, "PDMODE"))
        valueOut = (::Int32) pDatabase->pdmode ();
    else
        return  BSIERROR;

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_getBooleanSystemVariable
(
bool&                       valueOut,
const AcDbDatabase*         pDatabase,
const char*                 varName
)
    {
    if (0 == _stricmp(varName, "TILEMODE"))
        valueOut = pDatabase->tilemode ();
    else if (0 == _stricmp(varName, "FILLMODE"))
        valueOut = pDatabase->fillmode ();
    else if (0 == _stricmp(varName, "ORTHOMODE"))
        valueOut = pDatabase->orthomode ();
    else if (0 == _stricmp(varName, "QTEXTMODE"))
        valueOut = pDatabase->qtextmode ();
    else if (0 == _stricmp(varName, "MIRRTEXT"))
        valueOut = pDatabase->mirrtext ();
    else if (0 == _stricmp(varName, "ATTMODE"))
        valueOut = pDatabase->attmode() != 0;
    else if (0 == _stricmp(varName, "DISPSILH"))
        valueOut = pDatabase->dispSilh ();
    else if (0 == _stricmp(varName, "DIMASO"))
        valueOut = pDatabase->dimaso ();
    else if (0 == _stricmp(varName, "LIMCHECK"))
        valueOut = pDatabase->limcheck ();
    else if (0 == _stricmp(varName, "PSLTSCALE"))
        valueOut = pDatabase->psltscale ();
    else if (0 == _stricmp(varName, "PSTYLEMODE"))
        valueOut = pDatabase->plotStyleMode ();
    else if (0 == _stricmp(varName, "ANNOALLVISIBLE"))
        valueOut = pDatabase->annoAllVisible ();
    else
        return  BSIERROR;

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_getDoubleSystemVariable
(
double&                     valueOut,
const AcDbDatabase*         pDatabase,
const char*                 varName
)
    {
    if (0 == _stricmp(varName, "LTSCALE"))
        valueOut = pDatabase->ltscale ();
    else if (0 == _stricmp(varName, "CELTSCALE"))
        valueOut = pDatabase->celtscale ();
    else if (0 == _stricmp(varName, "TEXTSIZE"))
        valueOut = pDatabase->textsize ();
    else if (0 == _stricmp(varName, "ELEVATION"))
        valueOut = pDatabase->elevation ();
    else if (0 == _stricmp(varName, "THICKNESS"))
        valueOut = pDatabase->thickness ();
    else if (0 == _stricmp(varName, "ANGBASE"))
        valueOut = pDatabase->angbase ();
    else if (0 == _stricmp(varName, "PSVPSCALE"))
        valueOut = pDatabase->viewportScaleDefault ();
    else if (0 == _stricmp(varName, "DIMSCALE"))
        valueOut = pDatabase->dimscale ();
    else if (0 == _stricmp(varName, "DIMLFAC"))
        valueOut = pDatabase->dimlfac ();
    else if (0 == _stricmp(varName, "FACETRES"))
        valueOut = pDatabase->facetres ();
    else if (0 == _stricmp(varName, "PDSIZE"))
        valueOut = pDatabase->pdsize ();
    else
        return  BSIERROR;

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_getPointSystemVariable
(
::DPoint3d&                 valueOut,
const AcDbDatabase*         pDatabase,
const char*                 varName
)
    {
    if (0 == _stricmp(varName, "EXTMIN"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint3d (valueOut, pDatabase->extmin());
    else if (0 == _stricmp(varName, "EXTMAX"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint3d (valueOut, pDatabase->extmax());
    else if (0 == _stricmp(varName, "LIMMIN"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint2d (valueOut, pDatabase->limmin());
    else if (0 == _stricmp(varName, "LIMMAX"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint2d (valueOut, pDatabase->limmax());
    else if (0 == _stricmp(varName, "INSBASE"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint3d (valueOut, pDatabase->insbase());
    else if (0 == _stricmp(varName, "UCSORG"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGePoint3d (valueOut, pDatabase->ucsorg());
    else if (0 == _stricmp(varName, "UCSXDIR"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGeVector3d (valueOut, pDatabase->ucsxdir());
    else if (0 == _stricmp(varName, "UCSYDIR"))
        Bentley::RealDwg::RealDwgUtil::DPoint3dFromGeVector3d (valueOut, pDatabase->ucsydir());
    else
        return  BSIERROR;

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_getStringSystemVariable
(
Bentley::WString&           valueOut,
const AcDbDatabase*         pDatabase,
const char*                 varName
)
    {
    if (0 == _stricmp(varName, "HYPERLINKBASE"))
        {
        ACHAR*  hyperlink = NULL;
        if (Acad::eOk == pDatabase->getHyperlinkBase(hyperlink))
            {
            valueOut = Bentley::WString (hyperlink);
            acutDelString (hyperlink);
            }
        }
    else
        {
        return  BSIERROR;
        }

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_setIntegerSystemVariable
(
AcDbDatabase*               pDatabase,
const char*                 varName,
::Int32                     valueIn
)
    {
    Acad::ErrorStatus   es = Acad::eOk;

    if (0 == _stricmp(varName, "LUNITS"))
        es = pDatabase->setLunits ((Adesk::Int16)(AcDb::UnitsValue) valueIn);
    else if (0 == _stricmp(varName, "LUPREC"))
        es = pDatabase->setLuprec ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "AUNITS"))
        es = pDatabase->setAunits ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "AUPREC"))
        es = pDatabase->setAuprec ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "INSUNITS"))
        es = pDatabase->setInsunits ((AcDb::UnitsValue) valueIn);
    else if (0 == _stricmp(varName, "TREEDEPTH"))
        es = pDatabase->setTreedepth ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "CECOLOR"))
        es = pDatabase->setCecolor (acCmColorFromIndexColor(valueIn));
    else if (0 == _stricmp(varName, "PROXYGRAPHICS"))
        es = pDatabase->setSaveproxygraphics ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "MEASUREMENT"))
        es = pDatabase->setMeasurement ((AcDb::MeasurementValue) valueIn);
    else if (0 == _stricmp(varName, "ISOLINES"))
        es = pDatabase->setIsolines ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "DIMLUNIT"))
        es = pDatabase->setDimunit ((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "DWGCODEPAGE"))
        return  BSIERROR;   // deprecated in R2015
    else if (0 == _stricmp(varName, "PDMODE"))
        es = pDatabase->setPdmode ((Adesk::Int16) valueIn);
    else
        return  BSIERROR;

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_setBooleanSystemVariable
(
AcDbDatabase*               pDatabase,
const char*                 varName,
bool                        valueIn
)
    {
    Acad::ErrorStatus   es = Acad::eOk;

    if (0 == _stricmp(varName, "TILEMODE"))
        es = pDatabase->setTilemode ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "FILLMODE"))
        es = pDatabase->setFillmode ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "ORTHOMODE"))
        es = pDatabase->setOrthomode ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "QTEXTMODE"))
        es = pDatabase->setQtextmode ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "MIRRTEXT"))
        es = pDatabase->setMirrtext ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "ATTMODE"))
        es = pDatabase->setAttmode((Adesk::Int16) valueIn);
    else if (0 == _stricmp(varName, "DISPSILH"))
        es = pDatabase->setDispSilh ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "DIMASO"))
        es = pDatabase->setDimaso ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "LIMCHECK"))
        es = pDatabase->setLimcheck ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "PSLTSCALE"))
        es = pDatabase->setPsltscale ((Adesk::Boolean) valueIn);
    else if (0 == _stricmp(varName, "ANNOALLVISIBLE"))
        es = pDatabase->setAnnoAllVisible (valueIn);
    else
        return  BSIERROR;

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_setDoubleSystemVariable
(
AcDbDatabase*               pDatabase,
const char*                 varName,
double                      valueIn
)
    {
    Acad::ErrorStatus   es = Acad::eOk;

    if (0 == _stricmp(varName, "LTSCALE"))
        es = pDatabase->setLtscale (valueIn);
    else if (0 == _stricmp(varName, "CELTSCALE"))
        es = pDatabase->setCeltscale (valueIn);
    else if (0 == _stricmp(varName, "TEXTSIZE"))
        es = pDatabase->setTextsize (valueIn);
    else if (0 == _stricmp(varName, "ELEVATION"))
        es = pDatabase->setElevation (valueIn);
    else if (0 == _stricmp(varName, "THICKNESS"))
        es = pDatabase->setThickness (valueIn);
    else if (0 == _stricmp(varName, "ANGBASE"))
        es = pDatabase->setAngbase (valueIn);
    else if (0 == _stricmp(varName, "PSVPSCALE"))
        es = pDatabase->setViewportScaleDefault (valueIn);
    else if (0 == _stricmp(varName, "DIMSCALE"))
        es = pDatabase->setDimscale (valueIn);
    else if (0 == _stricmp(varName, "DIMLFAC"))
        es = pDatabase->setDimlfac (valueIn);
    else if (0 == _stricmp(varName, "FACETRES"))
        es = pDatabase->setFacetres (valueIn);
    else if (0 == _stricmp(varName, "PDSIZE"))
        es = pDatabase->setPdsize (valueIn);
    else
        return  BSIERROR;

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_setPointSystemVariable
(
AcDbDatabase*               pDatabase,
const char*                 varName,
const ::DPoint3d&           valueIn
)
    {
    Acad::ErrorStatus   es = Acad::eOk;

    if (0 == _stricmp(varName, "EXTMIN"))
        es = pDatabase->setExtmin (Bentley::RealDwg::RealDwgUtil::GePoint3dFromDPoint3d(valueIn));
    else if (0 == _stricmp(varName, "EXTMAX"))
        es = pDatabase->setExtmax (Bentley::RealDwg::RealDwgUtil::GePoint3dFromDPoint3d(valueIn));
    else if (0 == _stricmp(varName, "LIMMIN"))
        es = pDatabase->setLimmin (Bentley::RealDwg::RealDwgUtil::GePoint2dFromDPoint3d(valueIn));
    else if (0 == _stricmp(varName, "LIMMAX"))
        es = pDatabase->setLimmax (Bentley::RealDwg::RealDwgUtil::GePoint2dFromDPoint3d(valueIn));
    else if (0 == _stricmp(varName, "INSBASE"))
        es = pDatabase->setInsbase (Bentley::RealDwg::RealDwgUtil::GePoint3dFromDPoint3d(valueIn));
    else
        return  BSIERROR;

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED StatusInt     realDwg_setStringSystemVariable
(
AcDbDatabase*               pDatabase,
const char*                 varName,
const Bentley::WString&     valueIn
)
    {
    Acad::ErrorStatus   es = Acad::eOk;

    if (0 == _stricmp(varName, "HYPERLINKBASE"))
        es = pDatabase->setHyperlinkBase (valueIn.GetWCharCP());
    else
        return  BSIERROR;

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

END_EXTERN_C
