#pragma once

#include "../../../include/IExportFormat.h"
#include "../../../include/ExportTypes.h"
#include "../../core/ExportContext.h"

// ODA DGN SDK includes
#ifdef ODA_DGN_AVAILABLE
#include <DgDatabase.h>
#include <DgModel.h>
#include <DgTable.h>
#include <DgElementIterator.h>
#include <DgLine.h>
#include <DgArc.h>
#include <DgEllipse.h>
#include <DgText.h>
#include <DgShape.h>
#include <DgCellHeader.h>
#include <DgLevelTable.h>
#include <DgLineStyleTable.h>
#include <DgColorTable.h>
#include <DgHostAppServices.h>
#endif

#include <memory>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class IModelDb;

//=======================================================================================
// DGN Exporter Implementation using ODA DGN SDK
//=======================================================================================

class DGNExporter : public IDGNExporter {
public:
    DGNExporter();
    virtual ~DGNExporter();

    //===================================================================================
    // IExportFormat Interface
    //===================================================================================

    ExportFormat GetFormat() const override { return ExportFormat::DGN; }
    std::string GetFormatName() const override { return "MicroStation DGN"; }
    std::string GetFileExtension() const override { return ".dgn"; }
    std::vector<std::string> GetSupportedVersions() const override;

    bool SupportsGeometry() const override { return true; }
    bool SupportsMetadata() const override { return true; }
    bool SupportsMaterials() const override { return true; }
    bool SupportsTextures() const override { return true; }
    bool SupportsAnimations() const override { return false; }
    bool SupportsHierarchy() const override { return true; }

    ExportResult Export(
        const IModelDb& imodel,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) override;

    bool ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const override;
    bool CanExportElement(const ElementInfo& element) const override;

    void SetExportContext(std::shared_ptr<ExportContext> context) override;
    std::shared_ptr<ExportContext> GetExportContext() const override;

    //===================================================================================
    // IDGNExporter Interface
    //===================================================================================

    bool SetDGNVersion(DGNExportOptions::DGNVersion version) override;
    bool LoadSeedFile(const std::string& seedPath) override;
    bool CreateLevel(const std::string& levelName, int levelNumber, const Color& color) override;
    bool CreateCellDefinition(const std::string& cellName) override;

    bool AddLineElement(const Point3d& start, const Point3d& end, int level = 0) override;
    bool AddArcElement(const Point3d& center, double radius, double startAngle, double sweepAngle, int level = 0) override;
    bool AddTextElement(const Point3d& position, const std::string& text, double height, int level = 0) override;
    bool AddShapeElement(const std::vector<Point3d>& points, int level = 0) override;
    bool AddCellElement(const std::string& cellName, const Point3d& origin, const Transform3d& transform, int level = 0) override;

protected:
    bool InitializeExport(const ExportOptions& options) override;
    bool FinalizeExport() override;
    void CleanupExport() override;

private:
    //===================================================================================
    // ODA DGN Integration
    //===================================================================================

#ifdef ODA_DGN_AVAILABLE
    // DGN objects
    OdDgDatabasePtr m_database;
    OdDgModelPtr m_model;
    OdDgHostAppServices* m_hostApp;
    
    // Symbol tables
    OdDgLevelTablePtr m_levelTable;
    OdDgLineStyleTablePtr m_lineStyleTable;
    OdDgColorTablePtr m_colorTable;
    OdDgTextStyleTablePtr m_textStyleTable;
    
    // Current drawing state
    OdDgElementId m_currentLevelId;
    OdDgElementId m_currentLineStyleId;
    OdUInt32 m_currentColorIndex;
    
    // Helper methods
    bool InitializeODADGN();
    void CleanupODADGN();
    bool CreateDefaultLevels();
    bool CreateDefaultLineStyles();
    bool CreateDefaultTextStyles();
    bool CreateDefaultColors();
    
    // Conversion helpers
    OdGePoint3d ToOdGePoint3d(const Point3d& point) const;
    OdGeVector3d ToOdGeVector3d(const Vector3d& vector) const;
    OdGeMatrix3d ToOdGeMatrix3d(const Transform3d& transform) const;
    OdCmEntityColor ToOdCmEntityColor(const Color& color) const;
    
    // Element creation helpers
    OdDgElementId AddElementToModel(OdDgElement* element);
    OdDgElementId AddElementToCell(OdDgElement* element, const std::string& cellName);
    bool SetElementProperties(OdDgElement* element, int level);
    
    // Level management
    OdDgElementId GetOrCreateLevel(const std::string& levelName, int levelNumber, const Color& color = Color());
    bool SetCurrentLevel(int levelNumber);
    
    // Cell management
    OdDgElementId GetOrCreateCellDefinition(const std::string& cellName);
    bool SetCurrentCell(const std::string& cellName);
    
    // Line style management
    OdDgElementId GetOrCreateLineStyle(const std::string& styleeName);
    bool SetCurrentLineStyle(const std::string& styleName);
#endif

    //===================================================================================
    // Export State Management
    //===================================================================================

    struct ExportState {
        std::string outputPath;
        DGNExportOptions::DGNVersion version;
        std::string seedFile;
        bool initialized;
        bool finalized;
        
        // Statistics
        size_t elementsCreated;
        size_t levelsCreated;
        size_t cellsCreated;
        size_t lineStylesCreated;
        
        // Error tracking
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };

    ExportState m_state;
    std::shared_ptr<ExportContext> m_context;
    
    // Level and cell caching
    std::unordered_map<std::string, int> m_levelCache;        // name -> level number
    std::unordered_map<std::string, std::string> m_cellCache; // name -> element ID
    std::unordered_map<std::string, std::string> m_lineStyleCache; // name -> element ID
    
    //===================================================================================
    // iModel Element Processing
    //===================================================================================

    // Main processing methods
    bool ProcessIModelElements(const IModelDb& imodel, ProgressCallback progressCallback);
    bool ProcessElement(const ElementInfo& element);
    
    // Element type processors
    bool ProcessGeometricElement(const ElementInfo& element);
    bool ProcessSpatialElement(const ElementInfo& element);
    bool ProcessPhysicalElement(const ElementInfo& element);
    bool ProcessAnnotationElement(const ElementInfo& element);
    
    // Geometry processors
    bool ProcessLineGeometry(const ElementInfo& element);
    bool ProcessArcGeometry(const ElementInfo& element);
    bool ProcessEllipseGeometry(const ElementInfo& element);
    bool ProcessPolylineGeometry(const ElementInfo& element);
    bool ProcessSplineGeometry(const ElementInfo& element);
    bool ProcessSolidGeometry(const ElementInfo& element);
    bool ProcessSurfaceGeometry(const ElementInfo& element);
    bool ProcessMeshGeometry(const ElementInfo& element);
    
    // Complex element processors
    bool ProcessCellElement(const ElementInfo& element);
    bool ProcessTextElement(const ElementInfo& element);
    bool ProcessDimensionElement(const ElementInfo& element);
    bool ProcessPatternElement(const ElementInfo& element);
    
    // Property processors
    bool ProcessElementProperties(const ElementInfo& element);
    bool ProcessLevelProperties(const ElementInfo& element);
    bool ProcessLineStyleProperties(const ElementInfo& element);
    bool ProcessColorProperties(const ElementInfo& element);
    bool ProcessMaterialProperties(const ElementInfo& element);
    
    //===================================================================================
    // DGN Hierarchy Management
    //===================================================================================

    // Model hierarchy
    bool CreateModelHierarchy(const IModelDb& imodel);
    bool CreateSheetModel(const std::string& modelName);
    bool CreateDesignModel(const std::string& modelName);
    bool SetActiveModel(const std::string& modelName);
    
    // Cell hierarchy
    bool CreateCellHierarchy(const IModelDb& imodel);
    bool ProcessCellDefinition(const ElementInfo& element);
    bool ProcessCellInstance(const ElementInfo& element);
    
    // Level organization
    bool CreateLevelHierarchy(const IModelDb& imodel);
    bool ProcessLevelStructure(const ElementInfo& element);
    
    //===================================================================================
    // Coordinate System and Units
    //===================================================================================

    // Coordinate transformation
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    double TransformLength(double length) const;
    double TransformAngle(double angle) const;
    
    // Units setup
    bool SetupDGNUnits();
    bool SetMasterUnits(const std::string& unitName, double unitsPerMeter);
    bool SetSubUnits(const std::string& unitName, double subUnitsPerMasterUnit);
    bool SetWorkingUnits(double workingUnitsPerMasterUnit);
    
    //===================================================================================
    // Symbology Management
    //===================================================================================

    // Color management
    int GetOrCreateColor(const Color& color);
    bool SetElementColor(OdDgElement* element, const Color& color);
    bool CreateColorTable();
    
    // Line style management
    std::string GetOrCreateLineStyle(const std::string& pattern, double scale = 1.0);
    bool SetElementLineStyle(OdDgElement* element, const std::string& lineStyle);
    bool CreateDefaultLineStyles();
    
    // Weight management
    bool SetElementWeight(OdDgElement* element, int weight);
    int GetWeightFromLineWidth(double width) const;
    
    // Material management
    bool ProcessMaterialDefinition(const Material& material);
    bool AssignMaterialToElement(OdDgElement* element, const std::string& materialName);
    
    //===================================================================================
    // Text and Annotation
    //===================================================================================

    // Text processing
    bool ProcessTextString(const ElementInfo& element);
    bool ProcessTextNode(const ElementInfo& element);
    bool CreateTextStyle(const std::string& styleName, const std::string& fontName, double height);
    bool SetTextProperties(OdDgElement* textElement, const std::string& styleName);
    
    // Dimension processing
    bool ProcessLinearDimension(const ElementInfo& element);
    bool ProcessAngularDimension(const ElementInfo& element);
    bool ProcessRadialDimension(const ElementInfo& element);
    bool CreateDimensionStyle(const std::string& styleName);
    
    //===================================================================================
    // Pattern and Hatching
    //===================================================================================

    // Pattern processing
    bool ProcessAreaPattern(const ElementInfo& element);
    bool ProcessLinearPattern(const ElementInfo& element);
    bool CreatePatternDefinition(const std::string& patternName);
    bool ApplyPatternToElement(OdDgElement* element, const std::string& patternName);
    
    //===================================================================================
    // Error Handling and Logging
    //===================================================================================

    void LogError(const std::string& message);
    void LogWarning(const std::string& message);
    void LogInfo(const std::string& message);
    
    bool HandleError(const std::string& elementId, const std::string& error);
    bool ShouldContinueOnError() const;
    
    //===================================================================================
    // Progress Reporting
    //===================================================================================

    bool UpdateProgress(ProgressCallback callback, double percentage, const std::string& operation);
    bool CheckCancellation(ProgressCallback callback);
    
    //===================================================================================
    // Utility Methods
    //===================================================================================

    std::string GenerateUniqueLevelName(const std::string& baseName) const;
    std::string GenerateUniqueCellName(const std::string& baseName) const;
    std::string SanitizeDGNName(const std::string& name) const;
    
    bool FileExists(const std::string& path) const;
    bool CreateDirectoryIfNeeded(const std::string& path) const;
    std::string GetTemporaryFileName() const;
    
    // DGN validation
    bool ValidateDGNDatabase() const;
    bool ValidateDGNElement(const std::string& elementId) const;
};

//=======================================================================================
// DGN Host Application Services
//=======================================================================================

#ifdef ODA_DGN_AVAILABLE
class DGNHostAppServices : public OdDgHostAppServices {
public:
    DGNHostAppServices();
    virtual ~DGNHostAppServices();

    // Required overrides
    virtual OdString findFile(
        const OdString& filename,
        OdDgDatabase* pDb = nullptr,
        FindFileHint hint = kDefault) override;

    virtual const OdString program() override;
    virtual const OdString product() override;
    virtual const OdString companyName() override;
    virtual const OdString prodcode() override;
    virtual const OdString releasemarker() override;
    virtual int versionNumber() override;

private:
    OdString m_programName;
    OdString m_productName;
    OdString m_companyName;
};
#endif

} // namespace IModelExport
