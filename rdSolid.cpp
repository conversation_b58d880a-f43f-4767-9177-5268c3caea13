/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSolid.cpp $
|
|  $Copyright: (c) 2010 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// 此文件被包含在 rDwgDgnExtension.cpp 中
// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtSolid : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,        // AutoCAD 数据库对象指针
EditElementHandleR      outElement,      // 输出的 DGN 元素句柄
ConvertToDgnContextR    context         // 转换上下文
) const override
    {
    // 将输入对象转换为 AcDbSolid 类型
    AcDbSolid*              pSolid = AcDbSolid::cast (acObject);

    DPoint3d                points[5];    // 存储实体的顶点坐标
    int                     nPoints;      // 顶点数量
    
    // 获取实体的所有顶点
    for (int iPoint = 0; iPoint < 4; iPoint++)
        {
        AcGePoint3d         acPoint;     // AutoCAD 几何点

        pSolid->getPointAt (iPoint, acPoint);
        RealDwgUtil::DPoint3dFromGePoint3d (points[iPoint], acPoint);
        }
    
    // 修复点序列，确保正确的闭合 (TR# 138302)
    if (!points[3].isEqual (&points[0]))               
        RealDwgUtil::SwapPoints (points[2], points[3]);

    // 确定是三角形还是四边形
    nPoints = points[2].isEqual (&points[3]) ? 3 : 4;

    points[nPoints++] = points[0];              // 添加闭合点

    RealDwgStatus   status = context.CreateElementFromVertices (outElement, points, nPoints, true, context.GetTransformToDGN());
    if (RealDwgSuccess == status)
        {
        context.ApplyThickness (outElement, pSolid->thickness(), pSolid->normal(), true);
        context.ElementHeaderFromEntity (outElement, pSolid);
        // add fill
        IAreaFillPropertiesEdit*    areaFill = dynamic_cast <IAreaFillPropertiesEdit*> (&outElement.GetHandler(MISSING_HANDLER_PERMISSION_ChangeAttrib));
        if (NULL != areaFill)
            {
            UInt32  fillColor = outElement.GetElementCP()->hdr.dhdr.symb.color;
            areaFill->AddSolidFill (outElement, &fillColor);
            }
        }

    return  status;
    }

}; // ToDgnExtSolid
