#include "DWGEntityTypes.h"
#include "../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbspline.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbtrace.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/db3dface.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbxref.h>
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbray.h>
#include <realdwg/base/dbxline.h>
#include <realdwg/base/dbpoint.h>
#include <realdwg/base/dbshape.h>
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbgroup.h>
#include <realdwg/base/dblight.h>
#include <realdwg/base/dbcamera.h>
#include <realdwg/base/dbhelix.h>
#include <realdwg/base/dbgeodata.h>
#include <realdwg/base/dbnamedpath.h>
#include <realdwg/base/dbmotionpath.h>
#include <realdwg/base/dbsection.h>
#include <realdwg/base/dbsurface.h>
#include <realdwg/base/dbnurbsurface.h>
#include <realdwg/base/dbplanesurface.h>
#include <realdwg/base/dbextrsurf.h>
#include <realdwg/base/dbrevsurf.h>
#include <realdwg/base/dbswepsurf.h>
#include <realdwg/base/dbloftsurf.h>
#include <realdwg/base/dbblendsurf.h>
#include <realdwg/base/dbnetsurf.h>
#include <realdwg/base/dbpatchsurf.h>
#include <realdwg/base/dboffsetsurf.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbtolerance.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbpdfref.h>
#include <realdwg/base/dbdwfref.h>
#include <realdwg/base/dbdgnref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/base/dbpointcloudex.h>
#include <realdwg/base/dbgeopositionmarker.h>
#include <realdwg/base/dbsun.h>
#include <realdwg/base/dbsky.h>
#include <realdwg/base/dbrenderenvironment.h>
#include <realdwg/base/dbrenderentry.h>
#include <realdwg/base/dbrenderglobal.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/dbmentalrayrendersettings.h>
#include <realdwg/base/dbrapidrtrendersettings.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbviewtable.h>
#include <realdwg/base/dbucstable.h>
#include <realdwg/base/dbvptable.h>
#include <realdwg/base/dbappid.h>
#include <realdwg/base/dbspatialfilter.h>
#include <realdwg/base/dblayerfilter.h>
#include <realdwg/base/dbindex.h>
#include <realdwg/base/dbspatialindex.h>
#include <realdwg/base/dblayerindex.h>
#include <realdwg/base/dbcolor.h>
#include <realdwg/base/dbtrans.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/base/dbeval.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbacis.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#include <realdwg/base/dbassoccompoundactionparam.h>
#include <realdwg/base/dbassocedgeactionparam.h>
#include <realdwg/base/dbassocfaceactionparam.h>
#include <realdwg/base/dbassocpathactionparam.h>
#include <realdwg/base/dbassocpointrefactionparam.h>
#include <realdwg/base/dbassocvertexactionparam.h>
#include <realdwg/base/dbassocasmbodyactionparam.h>
#include <realdwg/base/dbassocblendsurfaceactionbody.h>
#include <realdwg/base/dbassocextrudesurfaceactionbody.h>
#include <realdwg/base/dbassocfilletactionbody.h>
#include <realdwg/base/dbassocloftsurfaceactionbody.h>
#include <realdwg/base/dbassocnetworksurfaceactionbody.h>
#include <realdwg/base/dbassocoffsetsurfaceactionbody.h>
#include <realdwg/base/dbassocpatchsurfaceactionbody.h>
#include <realdwg/base/dbassocplanesurfaceactionbody.h>
#include <realdwg/base/dbassocrevolvesurfaceactionbody.h>
#include <realdwg/base/dbassocsweepsurfaceactionbody.h>
#include <realdwg/base/dbassoctrimsurfaceactionbody.h>
#include <realdwg/base/dbassoc3pointangulardimactionbody.h>
#include <realdwg/base/dbassocaligneddimactionbody.h>
#include <realdwg/base/dbassocordinatedimactionbody.h>
#include <realdwg/base/dbassocrotateddimactionbody.h>
#include <realdwg/base/dbassocrestoreentitystateactionbody.h>
#include <realdwg/base/dbassocmleaderactionbody.h>
#include <realdwg/base/dbassocperssubentid.h>
#include <realdwg/base/dbassocedgeperssubentid.h>
#include <realdwg/base/dbassocfaceperssubentid.h>
#include <realdwg/base/dbassocvertexperssubentid.h>
#include <realdwg/base/dbassocexternalbodyperssubentid.h>
#include <realdwg/base/dbassocindexperssubentid.h>
#include <realdwg/base/dbassocpathperssubentid.h>
#include <realdwg/base/dbassocpointperssubentid.h>
#include <realdwg/base/dbassocsingledependency.h>
#include <realdwg/base/dbassocdimdependencybody.h>
#include <realdwg/base/dbassocobjectpointer.h>
#include <realdwg/base/dbassocstatus.h>
#include <realdwg/base/dbassocglobal.h>
#include <realdwg/base/dbassocmanager.h>
#include <realdwg/base/dbassocactionparam.h>
#include <realdwg/base/dbassocactionbody.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#include <realdwg/base/dbassoccompoundactionparam.h>
#include <realdwg/base/dbassocedgeactionparam.h>
#include <realdwg/base/dbassocfaceactionparam.h>
#include <realdwg/base/dbassocpathactionparam.h>
#include <realdwg/base/dbassocpointrefactionparam.h>
#include <realdwg/base/dbassocvertexactionparam.h>
#include <realdwg/base/dbassocasmbodyactionparam.h>
#endif

#include <iostream>
#include <sstream>

namespace IModelExport {

//=======================================================================================
// DWGEntityCreator Implementation
//=======================================================================================

DWGEntityCreator::DWGEntityCreator()
#ifdef REALDWG_AVAILABLE
    : m_database(nullptr)
#endif
    , m_totalEntitiesCreated(0)
{
}

DWGEntityCreator::~DWGEntityCreator() {
}

//=======================================================================================
// Basic 2D Entity Creation Implementation
//=======================================================================================

bool DWGEntityCreator::CreateLine(const Point3d& start, const Point3d& end, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbLine* line = new AcDbLine();
        line->setStartPoint(ToAcGePoint3d(start));
        line->setEndPoint(ToAcGePoint3d(end));
        
        bool success = CreateAndAddEntity(line, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Line]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating line: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - line creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateCircle(const Point3d& center, double radius, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbCircle* circle = new AcDbCircle();
        circle->setCenter(ToAcGePoint3d(center));
        circle->setRadius(radius);
        
        bool success = CreateAndAddEntity(circle, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Circle]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating circle: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - circle creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateArc(const Point3d& center, double radius, double startAngle, double endAngle, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbArc* arc = new AcDbArc();
        arc->setCenter(ToAcGePoint3d(center));
        arc->setRadius(radius);
        arc->setStartAngle(startAngle);
        arc->setEndAngle(endAngle);
        
        bool success = CreateAndAddEntity(arc, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Arc]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating arc: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - arc creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreatePoint(const Point3d& position, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbPoint* point = new AcDbPoint();
        point->setPosition(ToAcGePoint3d(position));
        
        bool success = CreateAndAddEntity(point, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Point]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating point: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - point creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbEllipse* ellipse = new AcDbEllipse();
        ellipse->set(ToAcGePoint3d(center), AcGeVector3d::kZAxis, ToAcGeVector3d(majorAxis), radiusRatio);
        
        bool success = CreateAndAddEntity(ellipse, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Ellipse]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating ellipse: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - ellipse creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateText(const Point3d& position, const std::string& text, double height,
                                 double rotation, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbText* textEntity = new AcDbText();
        textEntity->setPosition(ToAcGePoint3d(position));
        textEntity->setTextString(text.c_str());
        textEntity->setHeight(height);
        textEntity->setRotation(rotation);

        bool success = CreateAndAddEntity(textEntity, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Text]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating text: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - text creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateMText(const Point3d& position, const std::string& text, double width, double height,
                                  const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDbMText* mtext = new AcDbMText();
        mtext->setLocation(ToAcGePoint3d(position));
        mtext->setContents(text.c_str());
        mtext->setWidth(width);
        mtext->setTextHeight(height);

        bool success = CreateAndAddEntity(mtext, layer);
        if (success) {
            m_entityCounts[DWGEntityType::MText]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating mtext: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - mtext creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreatePolyline(const std::vector<Point3d>& points, bool closed, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    if (points.size() < 2) {
        LogError("Polyline requires at least 2 points");
        return false;
    }

    try {
        AcDbPolyline* polyline = new AcDbPolyline();

        for (size_t i = 0; i < points.size(); ++i) {
            AcGePoint3d pt = ToAcGePoint3d(points[i]);
            polyline->addVertexAt(static_cast<Adesk::UInt32>(i), AcGePoint2d(pt.x, pt.y));
        }

        polyline->setClosed(closed);

        bool success = CreateAndAddEntity(polyline, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Polyline]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating polyline: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - polyline creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::Create3DPolyline(const std::vector<Point3d>& points, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    if (points.size() < 2) {
        LogError("3D Polyline requires at least 2 points");
        return false;
    }

    try {
        AcDb3dPolyline* polyline3d = new AcDb3dPolyline();

        for (const auto& point : points) {
            AcDb3dPolylineVertex* vertex = new AcDb3dPolylineVertex();
            vertex->setPosition(ToAcGePoint3d(point));
            polyline3d->appendVertex(vertex);
        }

        bool success = CreateAndAddEntity(polyline3d, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Polyline3D]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating 3D polyline: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - 3D polyline creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateSpline(const std::vector<Point3d>& controlPoints, int degree, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    if (controlPoints.size() < degree + 1) {
        LogError("Spline requires at least degree+1 control points");
        return false;
    }

    try {
        AcGePoint3dArray acPoints;
        for (const auto& point : controlPoints) {
            acPoints.append(ToAcGePoint3d(point));
        }

        AcDbSpline* spline = new AcDbSpline();
        spline->setFitData(acPoints, degree, 0.0, 0.0, AcGeVector3d::kIdentity, AcGeVector3d::kIdentity);

        bool success = CreateAndAddEntity(spline, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Spline]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating spline: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - spline creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateHatch(const std::vector<std::vector<Point3d>>& boundaries, const std::string& pattern,
                                  double scale, double angle, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    if (boundaries.empty()) {
        LogError("Hatch requires at least one boundary");
        return false;
    }

    try {
        AcDbHatch* hatch = new AcDbHatch();
        hatch->setNormal(AcGeVector3d::kZAxis);
        hatch->setElevation(0.0);
        hatch->setAssociative(false);

        // Set pattern
        hatch->setPattern(AcDbHatch::kPreDefined, pattern.c_str());
        hatch->setPatternScale(scale);
        hatch->setPatternAngle(angle);

        // Add boundaries
        for (const auto& boundary : boundaries) {
            AcGePoint2dArray boundaryPoints;
            for (const auto& point : boundary) {
                AcGePoint3d pt = ToAcGePoint3d(point);
                boundaryPoints.append(AcGePoint2d(pt.x, pt.y));
            }

            AcDbPolyline* boundaryPline = new AcDbPolyline();
            for (int i = 0; i < boundaryPoints.length(); ++i) {
                boundaryPline->addVertexAt(i, boundaryPoints[i]);
            }
            boundaryPline->setClosed(true);

            AcDbObjectIdArray boundaryIds;
            boundaryIds.append(AddEntityToModelSpace(boundaryPline));
            hatch->appendLoop(AcDbHatch::kExternal, boundaryIds);
        }

        hatch->evaluateHatch();

        bool success = CreateAndAddEntity(hatch, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Hatch]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating hatch: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - hatch creation skipped");
    return false;
#endif
}

//=======================================================================================
// 3D Solid Entity Creation Implementation
//=======================================================================================

bool DWGEntityCreator::CreateBox(const Point3d& corner, double length, double width, double height,
                                const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDb3dSolid* box = new AcDb3dSolid();
        box->createBox(length, width, height);

        // Move to corner position
        AcGeMatrix3d transform;
        transform.setToTranslation(ToAcGePoint3d(corner).asVector());
        box->transformBy(transform);

        bool success = CreateAndAddEntity(box, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Solid3D]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating box: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - box creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateCylinder(const Point3d& center, double radius, double height, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDb3dSolid* cylinder = new AcDb3dSolid();
        cylinder->createCylinder(height, radius);

        // Move to center position
        AcGeMatrix3d transform;
        transform.setToTranslation(ToAcGePoint3d(center).asVector());
        cylinder->transformBy(transform);

        bool success = CreateAndAddEntity(cylinder, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Solid3D]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating cylinder: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - cylinder creation skipped");
    return false;
#endif
}

bool DWGEntityCreator::CreateSphere(const Point3d& center, double radius, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not set");
        return false;
    }

    try {
        AcDb3dSolid* sphere = new AcDb3dSolid();
        sphere->createSphere(radius);

        // Move to center position
        AcGeMatrix3d transform;
        transform.setToTranslation(ToAcGePoint3d(center).asVector());
        sphere->transformBy(transform);

        bool success = CreateAndAddEntity(sphere, layer);
        if (success) {
            m_entityCounts[DWGEntityType::Solid3D]++;
            m_totalEntitiesCreated++;
        }
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating sphere: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("RealDWG not available - sphere creation skipped");
    return false;
#endif
}

//=======================================================================================
// Utility Methods Implementation
//=======================================================================================

std::unordered_map<DWGEntityType, size_t> DWGEntityCreator::GetEntityCounts() const {
    return m_entityCounts;
}

size_t DWGEntityCreator::GetTotalEntitiesCreated() const {
    return m_totalEntitiesCreated;
}

void DWGEntityCreator::ResetStatistics() {
    m_entityCounts.clear();
    m_totalEntitiesCreated = 0;
}

std::string DWGEntityCreator::GetEntityTypeName(DWGEntityType type) const {
    switch (type) {
        case DWGEntityType::Line: return "Line";
        case DWGEntityType::Point: return "Point";
        case DWGEntityType::Circle: return "Circle";
        case DWGEntityType::Arc: return "Arc";
        case DWGEntityType::Ellipse: return "Ellipse";
        case DWGEntityType::Polyline: return "Polyline";
        case DWGEntityType::LWPolyline: return "LWPolyline";
        case DWGEntityType::Polyline2D: return "Polyline2D";
        case DWGEntityType::Polyline3D: return "Polyline3D";
        case DWGEntityType::Spline: return "Spline";
        case DWGEntityType::Ray: return "Ray";
        case DWGEntityType::XLine: return "XLine";
        case DWGEntityType::Text: return "Text";
        case DWGEntityType::MText: return "MText";
        case DWGEntityType::AttributeDefinition: return "AttributeDefinition";
        case DWGEntityType::Attribute: return "Attribute";
        case DWGEntityType::AlignedDimension: return "AlignedDimension";
        case DWGEntityType::AngularDimension: return "AngularDimension";
        case DWGEntityType::DiametricDimension: return "DiametricDimension";
        case DWGEntityType::LinearDimension: return "LinearDimension";
        case DWGEntityType::OrdinateDimension: return "OrdinateDimension";
        case DWGEntityType::RadialDimension: return "RadialDimension";
        case DWGEntityType::RadialDimensionLarge: return "RadialDimensionLarge";
        case DWGEntityType::ArcDimension: return "ArcDimension";
        case DWGEntityType::Leader: return "Leader";
        case DWGEntityType::MLeader: return "MLeader";
        case DWGEntityType::Hatch: return "Hatch";
        case DWGEntityType::Gradient: return "Gradient";
        case DWGEntityType::Solid: return "Solid";
        case DWGEntityType::Trace: return "Trace";
        case DWGEntityType::Face3D: return "Face3D";
        case DWGEntityType::PolyFaceMesh: return "PolyFaceMesh";
        case DWGEntityType::PolygonMesh: return "PolygonMesh";
        case DWGEntityType::SubDMesh: return "SubDMesh";
        case DWGEntityType::Solid3D: return "Solid3D";
        case DWGEntityType::Region: return "Region";
        case DWGEntityType::Body: return "Body";
        case DWGEntityType::Surface: return "Surface";
        case DWGEntityType::NurbSurface: return "NurbSurface";
        case DWGEntityType::PlaneSurface: return "PlaneSurface";
        case DWGEntityType::ExtrudedSurface: return "ExtrudedSurface";
        case DWGEntityType::RevolvedSurface: return "RevolvedSurface";
        case DWGEntityType::SweptSurface: return "SweptSurface";
        case DWGEntityType::LoftedSurface: return "LoftedSurface";
        case DWGEntityType::BlendSurface: return "BlendSurface";
        case DWGEntityType::NetworkSurface: return "NetworkSurface";
        case DWGEntityType::PatchSurface: return "PatchSurface";
        case DWGEntityType::OffsetSurface: return "OffsetSurface";
        case DWGEntityType::Mesh: return "Mesh";
        case DWGEntityType::BlockReference: return "BlockReference";
        case DWGEntityType::MInsert: return "MInsert";
        case DWGEntityType::XRef: return "XRef";
        case DWGEntityType::MLine: return "MLine";
        case DWGEntityType::Shape: return "Shape";
        case DWGEntityType::RasterImage: return "RasterImage";
        case DWGEntityType::WipeOut: return "WipeOut";
        case DWGEntityType::OLE2Frame: return "OLE2Frame";
        case DWGEntityType::Viewport: return "Viewport";
        case DWGEntityType::Table: return "Table";
        case DWGEntityType::Field: return "Field";
        case DWGEntityType::Group: return "Group";
        case DWGEntityType::Light: return "Light";
        case DWGEntityType::Camera: return "Camera";
        case DWGEntityType::Helix: return "Helix";
        case DWGEntityType::GeoData: return "GeoData";
        case DWGEntityType::NamedPath: return "NamedPath";
        case DWGEntityType::MotionPath: return "MotionPath";
        case DWGEntityType::Section: return "Section";
        case DWGEntityType::Tolerance: return "Tolerance";
        case DWGEntityType::FCF: return "FCF";
        case DWGEntityType::ProxyEntity: return "ProxyEntity";
        case DWGEntityType::UnderlayReference: return "UnderlayReference";
        case DWGEntityType::DwfReference: return "DwfReference";
        case DWGEntityType::DgnReference: return "DgnReference";
        case DWGEntityType::PdfReference: return "PdfReference";
        case DWGEntityType::PointCloud: return "PointCloud";
        case DWGEntityType::PointCloudEx: return "PointCloudEx";
        case DWGEntityType::CustomEntity: return "CustomEntity";
        case DWGEntityType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

void DWGEntityCreator::SetDatabase(void* database) {
#ifdef REALDWG_AVAILABLE
    m_database = static_cast<AcDbDatabase*>(database);
#endif
}

void* DWGEntityCreator::GetDatabase() const {
#ifdef REALDWG_AVAILABLE
    return m_database;
#else
    return nullptr;
#endif
}

bool DWGEntityCreator::ValidateEntityData(DWGEntityType type, const std::vector<Point3d>& points) const {
    switch (type) {
        case DWGEntityType::Line:
            return points.size() == 2;
        case DWGEntityType::Point:
            return points.size() == 1;
        case DWGEntityType::Circle:
        case DWGEntityType::Arc:
            return points.size() >= 1; // Center point required
        case DWGEntityType::Polyline:
        case DWGEntityType::LWPolyline:
        case DWGEntityType::Polyline2D:
        case DWGEntityType::Polyline3D:
            return points.size() >= 2;
        case DWGEntityType::Spline:
            return points.size() >= 4; // Minimum for cubic spline
        case DWGEntityType::Face3D:
            return points.size() >= 3 && points.size() <= 4;
        default:
            return !points.empty();
    }
}

bool DWGEntityCreator::ValidateLayer(const std::string& layerName) const {
    if (layerName.empty()) {
        return true; // Default layer is valid
    }

    // Check for invalid characters in layer name
    const std::string invalidChars = "<>/\\\":;?*|,=`";
    for (char c : layerName) {
        if (invalidChars.find(c) != std::string::npos) {
            return false;
        }
    }

    // Check length (AutoCAD limit is 255 characters)
    return layerName.length() <= 255;
}

//=======================================================================================
// Private Helper Methods Implementation
//=======================================================================================

#ifdef REALDWG_AVAILABLE
template<typename T>
bool DWGEntityCreator::CreateAndAddEntity(T* entity, const std::string& layer) {
    if (!entity) {
        LogError("Entity is null");
        return false;
    }

    if (!SetEntityProperties(entity, layer)) {
        delete entity;
        return false;
    }

    AcDbObjectId entityId = AddEntityToModelSpace(entity);
    if (entityId.isNull()) {
        delete entity;
        return false;
    }

    return true;
}

bool DWGEntityCreator::SetEntityProperties(AcDbEntity* entity, const std::string& layer) {
    if (!entity) {
        return false;
    }

    try {
        // Set layer
        if (!layer.empty()) {
            AcDbObjectId layerId = GetOrCreateLayer(layer);
            if (!layerId.isNull()) {
                entity->setLayer(layerId);
            }
        }

        // Set default properties
        entity->setColorIndex(256); // ByLayer
        entity->setLinetype("ByLayer");
        entity->setLinetypeScale(1.0);
        entity->setLineWeight(AcDb::kLnWtByLayer);

        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception setting entity properties: " + std::string(e.what()));
        return false;
    }
}

AcDbObjectId DWGEntityCreator::AddEntityToModelSpace(AcDbEntity* entity) {
    if (!entity || !m_database) {
        return AcDbObjectId::kNull;
    }

    try {
        AcDbBlockTable* blockTable;
        if (m_database->getBlockTable(blockTable, AcDb::kForRead) != Acad::eOk) {
            return AcDbObjectId::kNull;
        }

        AcDbBlockTableRecord* modelSpace;
        if (blockTable->getAt(ACDB_MODEL_SPACE, modelSpace, AcDb::kForWrite) != Acad::eOk) {
            blockTable->close();
            return AcDbObjectId::kNull;
        }

        AcDbObjectId entityId;
        if (modelSpace->appendAcDbEntity(entityId, entity) != Acad::eOk) {
            modelSpace->close();
            blockTable->close();
            return AcDbObjectId::kNull;
        }

        entity->close();
        modelSpace->close();
        blockTable->close();

        return entityId;
    }
    catch (const std::exception& e) {
        LogError("Exception adding entity to model space: " + std::string(e.what()));
        return AcDbObjectId::kNull;
    }
}

AcDbObjectId DWGEntityCreator::GetOrCreateLayer(const std::string& layerName) {
    if (!m_database || layerName.empty()) {
        return AcDbObjectId::kNull;
    }

    try {
        AcDbLayerTable* layerTable;
        if (m_database->getLayerTable(layerTable, AcDb::kForRead) != Acad::eOk) {
            return AcDbObjectId::kNull;
        }

        AcDbObjectId layerId;
        if (layerTable->getAt(layerName.c_str(), layerId) == Acad::eOk) {
            layerTable->close();
            return layerId;
        }

        // Layer doesn't exist, create it
        layerTable->close();
        if (m_database->getLayerTable(layerTable, AcDb::kForWrite) != Acad::eOk) {
            return AcDbObjectId::kNull;
        }

        AcDbLayerTableRecord* layerRecord = new AcDbLayerTableRecord();
        layerRecord->setName(layerName.c_str());
        layerRecord->setColor(AcCmColor(7)); // White
        layerRecord->setLinetypeObjectId(m_database->continuousLinetype());

        if (layerTable->add(layerId, layerRecord) != Acad::eOk) {
            delete layerRecord;
            layerTable->close();
            return AcDbObjectId::kNull;
        }

        layerRecord->close();
        layerTable->close();

        return layerId;
    }
    catch (const std::exception& e) {
        LogError("Exception creating layer: " + std::string(e.what()));
        return AcDbObjectId::kNull;
    }
}

AcGePoint3d DWGEntityCreator::ToAcGePoint3d(const Point3d& point) {
    return AcGePoint3d(point.x, point.y, point.z);
}

AcGeVector3d DWGEntityCreator::ToAcGeVector3d(const Vector3d& vector) {
    return AcGeVector3d(vector.x, vector.y, vector.z);
}

AcCmColor DWGEntityCreator::ToAcCmColor(const Color& color) {
    AcCmColor acColor;
    acColor.setRGB(static_cast<Adesk::UInt8>(color.r * 255),
                   static_cast<Adesk::UInt8>(color.g * 255),
                   static_cast<Adesk::UInt8>(color.b * 255));
    return acColor;
}
#endif

void DWGEntityCreator::LogError(const std::string& message) {
    std::cerr << "DWGEntityCreator Error: " << message << std::endl;
}

void DWGEntityCreator::LogWarning(const std::string& message) {
    std::cout << "DWGEntityCreator Warning: " << message << std::endl;
}

} // namespace IModelExport
