#pragma once

#include "DWGEntityTypes.h"
#include "../../core/GeometryProcessor.h"
#include "../../../include/ExportTypes.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbiter.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbviewtable.h>
#include <realdwg/base/dbucstable.h>
#include <realdwg/base/dbvptable.h>
#include <realdwg/base/dbappid.h>
#include <realdwg/base/dbspatialfilter.h>
#include <realdwg/base/dblayerfilter.h>
#include <realdwg/base/dbindex.h>
#include <realdwg/base/dbspatialindex.h>
#include <realdwg/base/dblayerindex.h>
#include <realdwg/base/dbcolor.h>
#include <realdwg/base/dbtrans.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/base/dbeval.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbacis.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#endif

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>

namespace IModelExport {

//=======================================================================================
// DWG Entity Processing Statistics
//=======================================================================================

struct DWGProcessingStats {
    std::unordered_map<DWGEntityType, size_t> entityCounts;
    std::unordered_map<std::string, size_t> layerCounts;
    std::unordered_map<std::string, size_t> materialCounts;
    std::unordered_map<std::string, size_t> linetypeCounts;
    std::unordered_map<std::string, size_t> textstyleCounts;
    std::unordered_map<std::string, size_t> dimstyleCounts;
    
    size_t totalEntities = 0;
    size_t processedEntities = 0;
    size_t skippedEntities = 0;
    size_t errorEntities = 0;
    
    double processingTimeMs = 0.0;
    double averageTimePerEntity = 0.0;
    
    void Reset() {
        entityCounts.clear();
        layerCounts.clear();
        materialCounts.clear();
        linetypeCounts.clear();
        textstyleCounts.clear();
        dimstyleCounts.clear();
        
        totalEntities = 0;
        processedEntities = 0;
        skippedEntities = 0;
        errorEntities = 0;
        
        processingTimeMs = 0.0;
        averageTimePerEntity = 0.0;
    }
};

//=======================================================================================
// DWG Entity Processing Options
//=======================================================================================

struct DWGProcessingOptions {
    // Entity filtering
    std::vector<DWGEntityType> includeEntityTypes;
    std::vector<DWGEntityType> excludeEntityTypes;
    std::vector<std::string> includeLayers;
    std::vector<std::string> excludeLayers;
    
    // Processing options
    bool processGeometry = true;
    bool processAttributes = true;
    bool processMaterials = true;
    bool processLayers = true;
    bool processLinetypes = true;
    bool processTextStyles = true;
    bool processDimStyles = true;
    bool processBlocks = true;
    bool processXRefs = true;
    bool processImages = true;
    bool processAnnotations = true;
    bool process3DSolids = true;
    bool processSurfaces = true;
    bool processMeshes = true;
    bool processLights = true;
    bool processCameras = true;
    bool processRenderSettings = true;
    
    // Quality settings
    double tessellationTolerance = 0.01;
    double angularTolerance = 0.1;
    int maxTessellationPoints = 10000;
    bool useHighQualityTessellation = true;
    
    // Performance settings
    bool useMultithreading = true;
    int maxThreads = 0; // 0 = auto-detect
    bool enableProgressCallback = true;
    bool enableDetailedLogging = false;
    
    // Error handling
    bool continueOnError = true;
    bool logErrors = true;
    bool logWarnings = true;
    
    DWGProcessingOptions() = default;
};

//=======================================================================================
// DWG Entity Processor - Comprehensive DWG Entity Processing
//=======================================================================================

class DWGEntityProcessor {
public:
    // Progress callback function type
    using ProgressCallback = std::function<bool(size_t current, size_t total, const std::string& currentEntity)>;
    
    // Error callback function type
    using ErrorCallback = std::function<void(const std::string& error, DWGEntityType entityType, const std::string& entityId)>;

    DWGEntityProcessor();
    ~DWGEntityProcessor();

    //===================================================================================
    // Main Processing Interface
    //===================================================================================

    // Process entire DWG database
    bool ProcessDatabase(void* database, const DWGProcessingOptions& options = DWGProcessingOptions());
    
    // Process specific model space or paper space
    bool ProcessModelSpace(void* database, const DWGProcessingOptions& options = DWGProcessingOptions());
    bool ProcessPaperSpace(void* database, const std::string& layoutName, const DWGProcessingOptions& options = DWGProcessingOptions());
    
    // Process specific block
    bool ProcessBlock(void* database, const std::string& blockName, const DWGProcessingOptions& options = DWGProcessingOptions());
    
    // Process individual entity
    bool ProcessEntity(void* entity, const DWGProcessingOptions& options = DWGProcessingOptions());

    //===================================================================================
    // Entity Type Specific Processing
    //===================================================================================

    // Basic 2D entities
    bool ProcessLine(void* entity);
    bool ProcessPoint(void* entity);
    bool ProcessCircle(void* entity);
    bool ProcessArc(void* entity);
    bool ProcessEllipse(void* entity);
    bool ProcessPolyline(void* entity);
    bool ProcessLWPolyline(void* entity);
    bool Process2DPolyline(void* entity);
    bool Process3DPolyline(void* entity);
    bool ProcessSpline(void* entity);
    bool ProcessRay(void* entity);
    bool ProcessXLine(void* entity);
    
    // Text entities
    bool ProcessText(void* entity);
    bool ProcessMText(void* entity);
    bool ProcessAttributeDefinition(void* entity);
    bool ProcessAttribute(void* entity);
    
    // Dimension entities
    bool ProcessAlignedDimension(void* entity);
    bool ProcessAngularDimension(void* entity);
    bool ProcessDiametricDimension(void* entity);
    bool ProcessLinearDimension(void* entity);
    bool ProcessOrdinateDimension(void* entity);
    bool ProcessRadialDimension(void* entity);
    bool ProcessRadialDimensionLarge(void* entity);
    bool ProcessArcDimension(void* entity);
    
    // Leader entities
    bool ProcessLeader(void* entity);
    bool ProcessMLeader(void* entity);
    
    // Hatch and fill entities
    bool ProcessHatch(void* entity);
    bool ProcessGradient(void* entity);
    bool ProcessSolid(void* entity);
    bool ProcessTrace(void* entity);
    
    // 3D face and mesh entities
    bool Process3DFace(void* entity);
    bool ProcessPolyFaceMesh(void* entity);
    bool ProcessPolygonMesh(void* entity);
    bool ProcessSubDMesh(void* entity);
    
    // 3D solid entities
    bool Process3DSolid(void* entity);
    bool ProcessRegion(void* entity);
    bool ProcessBody(void* entity);
    
    // Surface entities
    bool ProcessSurface(void* entity);
    bool ProcessNurbSurface(void* entity);
    bool ProcessPlaneSurface(void* entity);
    bool ProcessExtrudedSurface(void* entity);
    bool ProcessRevolvedSurface(void* entity);
    bool ProcessSweptSurface(void* entity);
    bool ProcessLoftedSurface(void* entity);
    bool ProcessBlendSurface(void* entity);
    bool ProcessNetworkSurface(void* entity);
    bool ProcessPatchSurface(void* entity);
    bool ProcessOffsetSurface(void* entity);
    
    // Mesh entities
    bool ProcessMesh(void* entity);
    
    // Block entities
    bool ProcessBlockReference(void* entity);
    bool ProcessMInsert(void* entity);
    bool ProcessXRef(void* entity);
    
    // Multiline entities
    bool ProcessMLine(void* entity);
    
    // Shape and font entities
    bool ProcessShape(void* entity);
    
    // Image and OLE entities
    bool ProcessRasterImage(void* entity);
    bool ProcessWipeOut(void* entity);
    bool ProcessOLE2Frame(void* entity);
    
    // Viewport entities
    bool ProcessViewport(void* entity);
    
    // Table entities
    bool ProcessTable(void* entity);
    
    // Field entities
    bool ProcessField(void* entity);
    
    // Group entities
    bool ProcessGroup(void* entity);
    
    // Light and camera entities
    bool ProcessLight(void* entity);
    bool ProcessWebLight(void* entity);
    bool ProcessDistantLight(void* entity);
    bool ProcessPointLight(void* entity);
    bool ProcessSpotLight(void* entity);
    bool ProcessCamera(void* entity);
    
    // Helix entities
    bool ProcessHelix(void* entity);
    
    // Geographic entities
    bool ProcessGeoData(void* entity);
    bool ProcessGeoPositionMarker(void* entity);
    
    // Path and motion entities
    bool ProcessNamedPath(void* entity);
    bool ProcessMotionPath(void* entity);
    
    // Section entities
    bool ProcessSection(void* entity);
    
    // Tolerance entities
    bool ProcessTolerance(void* entity);
    bool ProcessFCF(void* entity);
    
    // Proxy entities
    bool ProcessProxyEntity(void* entity);
    
    // Underlay reference entities
    bool ProcessUnderlayReference(void* entity);
    bool ProcessDwfReference(void* entity);
    bool ProcessDgnReference(void* entity);
    bool ProcessPdfReference(void* entity);
    
    // Point cloud entities
    bool ProcessPointCloud(void* entity);
    bool ProcessPointCloudEx(void* entity);

    //===================================================================================
    // Symbol Table Processing
    //===================================================================================

    bool ProcessLayers(void* database);
    bool ProcessLinetypes(void* database);
    bool ProcessTextStyles(void* database);
    bool ProcessDimStyles(void* database);
    bool ProcessMLineStyles(void* database);
    bool ProcessBlocks(void* database);
    bool ProcessViews(void* database);
    bool ProcessUCSs(void* database);
    bool ProcessViewports(void* database);
    bool ProcessRegApps(void* database);

    //===================================================================================
    // Dictionary Processing
    //===================================================================================

    bool ProcessMaterials(void* database);
    bool ProcessVisualStyles(void* database);
    bool ProcessLayouts(void* database);
    bool ProcessPlotSettings(void* database);
    bool ProcessRenderSettings(void* database);

    //===================================================================================
    // Callback and Event Management
    //===================================================================================

    void SetProgressCallback(ProgressCallback callback);
    void SetErrorCallback(ErrorCallback callback);
    void ClearCallbacks();

    //===================================================================================
    // Statistics and Information
    //===================================================================================

    const DWGProcessingStats& GetProcessingStats() const;
    void ResetProcessingStats();
    
    std::vector<std::string> GetSupportedEntityTypes() const;
    std::vector<std::string> GetProcessedLayers() const;
    std::vector<std::string> GetProcessedMaterials() const;
    
    bool IsEntityTypeSupported(DWGEntityType type) const;
    DWGEntityType DetectEntityType(void* entity) const;

    //===================================================================================
    // Configuration
    //===================================================================================

    void SetGeometryProcessor(std::shared_ptr<GeometryProcessor> processor);
    std::shared_ptr<GeometryProcessor> GetGeometryProcessor() const;
    
    void SetOutputFormat(ExportFormat format);
    ExportFormat GetOutputFormat() const;

private:
    //===================================================================================
    // Internal State
    //===================================================================================

    std::shared_ptr<GeometryProcessor> m_geometryProcessor;
    ExportFormat m_outputFormat;
    
    DWGProcessingStats m_stats;
    DWGProcessingOptions m_currentOptions;
    
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;
    
    // Entity type handlers
    std::unordered_map<DWGEntityType, std::function<bool(void*)>> m_entityHandlers;
    
    // Processing state
    bool m_isProcessing;
    size_t m_currentEntityIndex;
    size_t m_totalEntitiesToProcess;

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    void InitializeEntityHandlers();
    bool ShouldProcessEntity(void* entity, const DWGProcessingOptions& options) const;
    bool ShouldProcessLayer(const std::string& layerName, const DWGProcessingOptions& options) const;
    bool ShouldProcessEntityType(DWGEntityType type, const DWGProcessingOptions& options) const;
    
    void UpdateProgress(const std::string& currentEntity);
    void LogError(const std::string& error, DWGEntityType entityType = DWGEntityType::Unknown, const std::string& entityId = "");
    void LogWarning(const std::string& warning);
    
    std::string GetEntityId(void* entity) const;
    std::string GetEntityLayer(void* entity) const;
    
#ifdef REALDWG_AVAILABLE
    // RealDWG specific helpers
    DWGEntityType GetEntityTypeFromAcDb(AcDbEntity* entity) const;
    std::string GetLayerName(AcDbEntity* entity) const;
    AcDbObjectId GetEntityObjectId(AcDbEntity* entity) const;
    
    // Conversion helpers
    Point3d FromAcGePoint3d(const AcGePoint3d& point) const;
    Vector3d FromAcGeVector3d(const AcGeVector3d& vector) const;
    Color FromAcCmColor(const AcCmColor& color) const;
    Transform3d FromAcGeMatrix3d(const AcGeMatrix3d& matrix) const;
    
    // Database iteration helpers
    bool IterateModelSpace(AcDbDatabase* database, const DWGProcessingOptions& options);
    bool IteratePaperSpace(AcDbDatabase* database, const std::string& layoutName, const DWGProcessingOptions& options);
    bool IterateBlockTableRecord(AcDbBlockTableRecord* blockRecord, const DWGProcessingOptions& options);
#endif
};

} // namespace IModelExport
