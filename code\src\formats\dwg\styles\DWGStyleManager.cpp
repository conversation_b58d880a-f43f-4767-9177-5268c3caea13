#include "DWGStyleManager.h"
#include <algorithm>
#include <regex>
#include <sstream>

namespace IModelExport {

//=======================================================================================
// Color Implementation
//=======================================================================================

Color::Color() : r(1.0f), g(1.0f), b(1.0f), a(1.0f) {}

Color::Color(float red, float green, float blue, float alpha)
    : r(std::max(0.0f, std::min(1.0f, red)))
    , g(std::max(0.0f, std::min(1.0f, green)))
    , b(std::max(0.0f, std::min(1.0f, blue)))
    , a(std::max(0.0f, std::min(1.0f, alpha))) {}

Color::Color(int red, int green, int blue, int alpha)
    : r(red / 255.0f)
    , g(green / 255.0f)
    , b(blue / 255.0f)
    , a(alpha / 255.0f) {
    // Clamp values
    r = std::max(0.0f, std::min(1.0f, r));
    g = std::max(0.0f, std::min(1.0f, g));
    b = std::max(0.0f, std::min(1.0f, b));
    a = std::max(0.0f, std::min(1.0f, a));
}

bool Color::IsValid() const {
    return r >= 0.0f && r <= 1.0f &&
           g >= 0.0f && g <= 1.0f &&
           b >= 0.0f && b <= 1.0f &&
           a >= 0.0f && a <= 1.0f;
}

int Color::ToRGB() const {
    int red = static_cast<int>(r * 255.0f + 0.5f);
    int green = static_cast<int>(g * 255.0f + 0.5f);
    int blue = static_cast<int>(b * 255.0f + 0.5f);
    return (red << 16) | (green << 8) | blue;
}

std::string Color::ToHex() const {
    int rgb = ToRGB();
    std::ostringstream oss;
    oss << "#" << std::hex << std::uppercase << std::setfill('0') << std::setw(6) << rgb;
    return oss.str();
}

Color Color::FromRGB(int rgb) {
    int red = (rgb >> 16) & 0xFF;
    int green = (rgb >> 8) & 0xFF;
    int blue = rgb & 0xFF;
    return Color(red, green, blue, 255);
}

Color Color::FromHex(const std::string& hex) {
    std::string cleanHex = hex;
    if (cleanHex.front() == '#') {
        cleanHex = cleanHex.substr(1);
    }
    
    if (cleanHex.length() != 6) {
        return Color(); // Return white for invalid hex
    }
    
    try {
        int rgb = std::stoi(cleanHex, nullptr, 16);
        return FromRGB(rgb);
    }
    catch (...) {
        return Color(); // Return white for invalid hex
    }
}

//=======================================================================================
// LayerStyle Implementation
//=======================================================================================

bool LayerStyle::IsValid() const {
    if (name.empty()) {
        return false;
    }
    
    if (!color.IsValid()) {
        return false;
    }
    
    if (!std::isfinite(lineWeight) || lineWeight < 0.0) {
        return false;
    }
    
    if (!std::isfinite(transparency) || transparency < 0.0 || transparency > 100.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// LineTypeStyle Implementation
//=======================================================================================

LineTypeStyle LineTypeStyle::CreateContinuous() {
    LineTypeStyle style;
    style.name = "Continuous";
    style.description = "Solid line";
    style.pattern.clear();
    style.patternLength = 0.0;
    return style;
}

LineTypeStyle LineTypeStyle::CreateDashed() {
    LineTypeStyle style;
    style.name = "Dashed";
    style.description = "Dashed line";
    style.pattern = {5.0, -2.5};
    style.patternLength = 7.5;
    return style;
}

LineTypeStyle LineTypeStyle::CreateDotted() {
    LineTypeStyle style;
    style.name = "Dotted";
    style.description = "Dotted line";
    style.pattern = {0.5, -0.5};
    style.patternLength = 1.0;
    return style;
}

LineTypeStyle LineTypeStyle::CreateDashDot() {
    LineTypeStyle style;
    style.name = "DashDot";
    style.description = "Dash-dot line";
    style.pattern = {5.0, -1.0, 0.5, -1.0};
    style.patternLength = 7.5;
    return style;
}

bool LineTypeStyle::IsValid() const {
    if (name.empty()) {
        return false;
    }
    
    if (!std::isfinite(patternLength) || patternLength < 0.0) {
        return false;
    }
    
    if (!std::isfinite(scale) || scale <= 0.0) {
        return false;
    }
    
    // Validate pattern elements
    for (double element : pattern) {
        if (!std::isfinite(element)) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// TextStyle Implementation
//=======================================================================================

bool TextStyle::IsValid() const {
    if (name.empty() || fontName.empty()) {
        return false;
    }
    
    if (!std::isfinite(height) || height <= 0.0) {
        return false;
    }
    
    if (!std::isfinite(widthFactor) || widthFactor <= 0.0) {
        return false;
    }
    
    if (!std::isfinite(obliqueAngle)) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// DimensionStyle Implementation
//=======================================================================================

bool DimensionStyle::IsValid() const {
    if (name.empty()) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(textHeight) || textHeight <= 0.0 ||
        !std::isfinite(arrowSize) || arrowSize <= 0.0 ||
        !std::isfinite(extLineOffset) || extLineOffset < 0.0 ||
        !std::isfinite(extLineExtend) || extLineExtend < 0.0 ||
        !std::isfinite(dimLineExtend) || dimLineExtend < 0.0 ||
        !std::isfinite(textGap) || textGap < 0.0 ||
        !std::isfinite(linearScale) || linearScale <= 0.0 ||
        !std::isfinite(roundValue) || roundValue < 0.0) {
        return false;
    }
    
    // Validate precision
    if (precision < 0 || precision > 8) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// MaterialStyle Implementation
//=======================================================================================

bool MaterialStyle::IsValid() const {
    if (name.empty()) {
        return false;
    }
    
    if (!diffuseColor.IsValid() || !specularColor.IsValid() || !emissiveColor.IsValid()) {
        return false;
    }
    
    if (!std::isfinite(shininess) || shininess < 0.0 || shininess > 128.0) {
        return false;
    }
    
    if (!std::isfinite(transparency) || transparency < 0.0 || transparency > 1.0) {
        return false;
    }
    
    if (!std::isfinite(reflectivity) || reflectivity < 0.0 || reflectivity > 1.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// DWGStyleManager Implementation
//=======================================================================================

DWGStyleManager::DWGStyleManager()
    : m_enableValidation(true)
    , m_enableRepair(true)
    , m_enableOptimization(true)
{
    CreateDefaultStyles();
}

//=======================================================================================
// Layer Management
//=======================================================================================

StyleConversionResult DWGStyleManager::CreateLayer(const LayerStyle& layerStyle) {
    StyleConversionResult result;
    result.success = false;
    
    // Validate layer style
    if (!layerStyle.IsValid()) {
        result.errors.push_back("Invalid layer style");
        return result;
    }
    
    // Check if layer already exists
    if (m_layers.find(layerStyle.name) != m_layers.end()) {
        result.warnings.push_back("Layer already exists: " + layerStyle.name);
        result.success = true;
        return result;
    }
    
    // Create layer
    m_layers[layerStyle.name] = layerStyle;
    result.success = true;
    result.info.push_back("Created layer: " + layerStyle.name);
    
    return result;
}

StyleConversionResult DWGStyleManager::GetLayer(const std::string& name, LayerStyle& layerStyle) const {
    StyleConversionResult result;
    result.success = false;
    
    auto it = m_layers.find(name);
    if (it != m_layers.end()) {
        layerStyle = it->second;
        result.success = true;
    } else {
        result.errors.push_back("Layer not found: " + name);
    }
    
    return result;
}

StyleConversionResult DWGStyleManager::UpdateLayer(const LayerStyle& layerStyle) {
    StyleConversionResult result;
    result.success = false;
    
    if (!layerStyle.IsValid()) {
        result.errors.push_back("Invalid layer style");
        return result;
    }
    
    auto it = m_layers.find(layerStyle.name);
    if (it != m_layers.end()) {
        it->second = layerStyle;
        result.success = true;
        result.info.push_back("Updated layer: " + layerStyle.name);
    } else {
        result.errors.push_back("Layer not found for update: " + layerStyle.name);
    }
    
    return result;
}

StyleConversionResult DWGStyleManager::DeleteLayer(const std::string& name) {
    StyleConversionResult result;
    result.success = false;
    
    // Don't allow deletion of layer "0"
    if (name == "0") {
        result.errors.push_back("Cannot delete layer 0");
        return result;
    }
    
    auto it = m_layers.find(name);
    if (it != m_layers.end()) {
        m_layers.erase(it);
        result.success = true;
        result.info.push_back("Deleted layer: " + name);
    } else {
        result.errors.push_back("Layer not found for deletion: " + name);
    }
    
    return result;
}

std::vector<std::string> DWGStyleManager::GetLayerNames() const {
    std::vector<std::string> names;
    names.reserve(m_layers.size());
    
    for (const auto& pair : m_layers) {
        names.push_back(pair.first);
    }
    
    std::sort(names.begin(), names.end());
    return names;
}

//=======================================================================================
// LineType Management
//=======================================================================================

StyleConversionResult DWGStyleManager::CreateLineType(const LineTypeStyle& lineTypeStyle) {
    StyleConversionResult result;
    result.success = false;
    
    if (!lineTypeStyle.IsValid()) {
        result.errors.push_back("Invalid line type style");
        return result;
    }
    
    if (m_lineTypes.find(lineTypeStyle.name) != m_lineTypes.end()) {
        result.warnings.push_back("Line type already exists: " + lineTypeStyle.name);
        result.success = true;
        return result;
    }
    
    m_lineTypes[lineTypeStyle.name] = lineTypeStyle;
    result.success = true;
    result.info.push_back("Created line type: " + lineTypeStyle.name);
    
    return result;
}

StyleConversionResult DWGStyleManager::GetLineType(const std::string& name, LineTypeStyle& lineTypeStyle) const {
    StyleConversionResult result;
    result.success = false;
    
    auto it = m_lineTypes.find(name);
    if (it != m_lineTypes.end()) {
        lineTypeStyle = it->second;
        result.success = true;
    } else {
        result.errors.push_back("Line type not found: " + name);
    }
    
    return result;
}

//=======================================================================================
// TextStyle Management
//=======================================================================================

StyleConversionResult DWGStyleManager::CreateTextStyle(const TextStyle& textStyle) {
    StyleConversionResult result;
    result.success = false;
    
    if (!textStyle.IsValid()) {
        result.errors.push_back("Invalid text style");
        return result;
    }
    
    if (m_textStyles.find(textStyle.name) != m_textStyles.end()) {
        result.warnings.push_back("Text style already exists: " + textStyle.name);
        result.success = true;
        return result;
    }
    
    m_textStyles[textStyle.name] = textStyle;
    result.success = true;
    result.info.push_back("Created text style: " + textStyle.name);
    
    return result;
}

StyleConversionResult DWGStyleManager::GetTextStyle(const std::string& name, TextStyle& textStyle) const {
    StyleConversionResult result;
    result.success = false;
    
    auto it = m_textStyles.find(name);
    if (it != m_textStyles.end()) {
        textStyle = it->second;
        result.success = true;
    } else {
        result.errors.push_back("Text style not found: " + name);
    }
    
    return result;
}

//=======================================================================================
// Material Management
//=======================================================================================

StyleConversionResult DWGStyleManager::CreateMaterial(const MaterialStyle& materialStyle) {
    StyleConversionResult result;
    result.success = false;
    
    if (!materialStyle.IsValid()) {
        result.errors.push_back("Invalid material style");
        return result;
    }
    
    if (m_materials.find(materialStyle.name) != m_materials.end()) {
        result.warnings.push_back("Material already exists: " + materialStyle.name);
        result.success = true;
        return result;
    }
    
    m_materials[materialStyle.name] = materialStyle;
    result.success = true;
    result.info.push_back("Created material: " + materialStyle.name);
    
    return result;
}

StyleConversionResult DWGStyleManager::GetMaterial(const std::string& name, MaterialStyle& materialStyle) const {
    StyleConversionResult result;
    result.success = false;
    
    auto it = m_materials.find(name);
    if (it != m_materials.end()) {
        materialStyle = it->second;
        result.success = true;
    } else {
        result.errors.push_back("Material not found: " + name);
    }
    
    return result;
}

//=======================================================================================
// Validation and Repair
//=======================================================================================

bool DWGStyleManager::ValidateAllStyles() const {
    bool allValid = true;
    
    // Validate layers
    for (const auto& pair : m_layers) {
        if (!pair.second.IsValid()) {
            allValid = false;
        }
    }
    
    // Validate line types
    for (const auto& pair : m_lineTypes) {
        if (!pair.second.IsValid()) {
            allValid = false;
        }
    }
    
    // Validate text styles
    for (const auto& pair : m_textStyles) {
        if (!pair.second.IsValid()) {
            allValid = false;
        }
    }
    
    // Validate materials
    for (const auto& pair : m_materials) {
        if (!pair.second.IsValid()) {
            allValid = false;
        }
    }
    
    return allValid;
}

bool DWGStyleManager::RepairLayer(LayerStyle& layerStyle) const {
    bool repaired = false;
    
    // Repair name
    if (layerStyle.name.empty()) {
        layerStyle.name = "Layer";
        repaired = true;
    }
    
    // Sanitize name
    std::string sanitizedName = SanitizeStyleName(layerStyle.name);
    if (sanitizedName != layerStyle.name) {
        layerStyle.name = sanitizedName;
        repaired = true;
    }
    
    // Repair color
    if (!layerStyle.color.IsValid()) {
        layerStyle.color = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
        repaired = true;
    }
    
    // Repair line weight
    if (!std::isfinite(layerStyle.lineWeight) || layerStyle.lineWeight < 0.0) {
        layerStyle.lineWeight = 0.25;
        repaired = true;
    }
    
    // Repair transparency
    if (!std::isfinite(layerStyle.transparency) || layerStyle.transparency < 0.0 || layerStyle.transparency > 100.0) {
        layerStyle.transparency = 0.0;
        repaired = true;
    }
    
    return repaired;
}

bool DWGStyleManager::RepairInvalidStyles() {
    bool repaired = false;
    
    // Repair layers
    for (auto& pair : m_layers) {
        if (RepairLayer(pair.second)) {
            repaired = true;
        }
    }
    
    // Repair line types
    for (auto& pair : m_lineTypes) {
        if (RepairLineType(pair.second)) {
            repaired = true;
        }
    }
    
    // Repair text styles
    for (auto& pair : m_textStyles) {
        if (RepairTextStyle(pair.second)) {
            repaired = true;
        }
    }
    
    // Repair materials
    for (auto& pair : m_materials) {
        if (RepairMaterial(pair.second)) {
            repaired = true;
        }
    }
    
    return repaired;
}

//=======================================================================================
// Helper Methods
//=======================================================================================

std::string DWGStyleManager::SanitizeStyleName(const std::string& name) const {
    std::string sanitized = name;
    
    // Replace invalid characters
    static const std::regex invalidChars(R"([<>:"/\\|?*])");
    sanitized = std::regex_replace(sanitized, invalidChars, "_");
    
    // Remove leading/trailing whitespace
    sanitized.erase(0, sanitized.find_first_not_of(" \t\n\r"));
    sanitized.erase(sanitized.find_last_not_of(" \t\n\r") + 1);
    
    // Ensure it's not empty
    if (sanitized.empty()) {
        sanitized = "Style";
    }
    
    return sanitized;
}

bool DWGStyleManager::RepairLineType(LineTypeStyle& lineTypeStyle) const {
    bool repaired = false;
    
    if (lineTypeStyle.name.empty()) {
        lineTypeStyle.name = "LineType";
        repaired = true;
    }
    
    if (!std::isfinite(lineTypeStyle.patternLength) || lineTypeStyle.patternLength < 0.0) {
        lineTypeStyle.patternLength = 1.0;
        repaired = true;
    }
    
    if (!std::isfinite(lineTypeStyle.scale) || lineTypeStyle.scale <= 0.0) {
        lineTypeStyle.scale = 1.0;
        repaired = true;
    }
    
    return repaired;
}

bool DWGStyleManager::RepairTextStyle(TextStyle& textStyle) const {
    bool repaired = false;
    
    if (textStyle.name.empty()) {
        textStyle.name = "TextStyle";
        repaired = true;
    }
    
    if (textStyle.fontName.empty()) {
        textStyle.fontName = "Arial";
        repaired = true;
    }
    
    if (!std::isfinite(textStyle.height) || textStyle.height <= 0.0) {
        textStyle.height = 2.5;
        repaired = true;
    }
    
    if (!std::isfinite(textStyle.widthFactor) || textStyle.widthFactor <= 0.0) {
        textStyle.widthFactor = 1.0;
        repaired = true;
    }
    
    return repaired;
}

bool DWGStyleManager::RepairMaterial(MaterialStyle& materialStyle) const {
    bool repaired = false;
    
    if (materialStyle.name.empty()) {
        materialStyle.name = "Material";
        repaired = true;
    }
    
    if (!materialStyle.diffuseColor.IsValid()) {
        materialStyle.diffuseColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
        repaired = true;
    }
    
    if (!std::isfinite(materialStyle.shininess) || materialStyle.shininess < 0.0 || materialStyle.shininess > 128.0) {
        materialStyle.shininess = 32.0;
        repaired = true;
    }
    
    return repaired;
}

void DWGStyleManager::CreateDefaultStyles() {
    // Create default layer "0"
    LayerStyle defaultLayer;
    defaultLayer.name = "0";
    defaultLayer.color = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    defaultLayer.lineTypeName = "Continuous";
    defaultLayer.lineWeight = 0.25;
    defaultLayer.isVisible = true;
    defaultLayer.isLocked = false;
    defaultLayer.isPlottable = true;
    m_layers["0"] = defaultLayer;
    
    // Create standard line types
    m_lineTypes["Continuous"] = LineTypeStyle::CreateContinuous();
    m_lineTypes["Dashed"] = LineTypeStyle::CreateDashed();
    m_lineTypes["Dotted"] = LineTypeStyle::CreateDotted();
    m_lineTypes["DashDot"] = LineTypeStyle::CreateDashDot();
    
    // Create default text style
    TextStyle defaultTextStyle;
    defaultTextStyle.name = "Standard";
    defaultTextStyle.fontName = "Arial";
    defaultTextStyle.height = 2.5;
    defaultTextStyle.widthFactor = 1.0;
    defaultTextStyle.obliqueAngle = 0.0;
    defaultTextStyle.isBackward = false;
    defaultTextStyle.isUpsideDown = false;
    defaultTextStyle.isVertical = false;
    m_textStyles["Standard"] = defaultTextStyle;
    
    // Create default material
    MaterialStyle defaultMaterial;
    defaultMaterial.name = "Global";
    defaultMaterial.diffuseColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
    defaultMaterial.specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    defaultMaterial.emissiveColor = Color(0.0f, 0.0f, 0.0f, 1.0f);
    defaultMaterial.shininess = 32.0;
    defaultMaterial.transparency = 0.0;
    defaultMaterial.reflectivity = 0.0;
    m_materials["Global"] = defaultMaterial;
}

} // namespace IModelExport
