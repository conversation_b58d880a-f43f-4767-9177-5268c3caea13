#include "IFCExporter.h"
#include "../../core/ExportContext.h"

#ifdef ODA_IFC_AVAILABLE
#include <Ifc4/Ifc4EntityTypes.h>
#include <IfcCore/IfcUtils.h>
#include <IfcCore/IfcFile.h>
#endif

#include <filesystem>
#include <fstream>
#include <chrono>
#include <sstream>
#include <iomanip>

namespace IModelExport {

//=======================================================================================
// IFCExporter Implementation
//=======================================================================================

IFCExporter::IFCExporter() 
#ifdef ODA_IFC_AVAILABLE
    : m_ifcModel(nullptr)
    , m_ifcBuilder(nullptr)
    , m_geomServer(nullptr)
#endif
{
    m_state.initialized = false;
    m_state.finalized = false;
    m_state.entitiesCreated = 0;
    m_state.wallsCreated = 0;
    m_state.slabsCreated = 0;
    m_state.columnsCreated = 0;
    m_state.beamsCreated = 0;
    m_state.version = IFCExportOptions::IFCVersion::IFC4;
    m_state.fileFormat = IFCExportOptions::IFCFileFormat::STEP;
}

IFCExporter::~IFCExporter() {
    CleanupExport();
}

//=======================================================================================
// IExportFormat Interface Implementation
//=======================================================================================

std::vector<std::string> IFCExporter::GetSupportedVersions() const {
    return {"IFC2x3", "IFC4", "IFC4x3"};
}

ExportResult IFCExporter::Export(
    const IModelDb& imodel,
    const ExportOptions& options,
    ProgressCallback progressCallback) {
    
    ExportResult result;
    auto startTime = std::chrono::steady_clock::now();
    
    try {
        // Validate and cast options
        const IFCExportOptions* ifcOptions = dynamic_cast<const IFCExportOptions*>(&options);
        if (!ifcOptions) {
            result.status = ExportStatus::Error;
            result.errors.push_back("Invalid options type for IFC export");
            return result;
        }

        // Initialize export
        if (!InitializeExport(*ifcOptions)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 10.0, "Initializing IFC export")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Create project structure
        if (!CreateProjectStructure()) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 20.0, "Creating project structure")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Process iModel elements
        if (!ProcessIModelElements(imodel, progressCallback)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            result.warnings = m_state.warnings;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 90.0, "Finalizing IFC file")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Finalize export
        if (!FinalizeExport()) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Complete progress
        if (progressCallback) {
            UpdateProgress(progressCallback, 100.0, "Export completed");
        }

        // Set result
        result.status = m_state.warnings.empty() ? ExportStatus::Success : ExportStatus::Warning;
        result.outputFile = m_state.outputPath;
        result.warnings = m_state.warnings;
        result.errors = m_state.errors;
        result.exportedElements = m_state.entitiesCreated;

    } catch (const std::exception& e) {
        result.status = ExportStatus::Error;
        result.errors.push_back("Exception during export: " + std::string(e.what()));
        LogError(result.errors.back());
    }

    auto endTime = std::chrono::steady_clock::now();
    result.exportTime = std::chrono::duration<double>(endTime - startTime).count();

    CleanupExport();
    return result;
}

bool IFCExporter::ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const {
    const IFCExportOptions* ifcOptions = dynamic_cast<const IFCExportOptions*>(&options);
    if (!ifcOptions) {
        errors.push_back("Options must be of type IFCExportOptions");
        return false;
    }

    // Validate output path
    if (ifcOptions->outputPath.empty()) {
        errors.push_back("Output path cannot be empty");
        return false;
    }

    // Validate project info
    if (ifcOptions->projectInfo.empty()) {
        errors.push_back("Project information should be provided for IFC export");
        // This is a warning, not an error
    }

    // Validate geometry tolerance
    if (ifcOptions->geometryTolerance <= 0.0) {
        errors.push_back("Geometry tolerance must be positive");
        return false;
    }

    return true;
}

bool IFCExporter::CanExportElement(const ElementInfo& element) const {
    // IFC supports building elements and spatial elements
    switch (element.type) {
        case ElementType::GeometricElement:
        case ElementType::SpatialElement:
        case ElementType::PhysicalElement:
            return true;
        case ElementType::FunctionalElement:
        case ElementType::InformationElement:
            return false; // These don't have physical representation
        default:
            return false;
    }
}

void IFCExporter::SetExportContext(std::shared_ptr<ExportContext> context) {
    m_context = context;
}

std::shared_ptr<ExportContext> IFCExporter::GetExportContext() const {
    return m_context;
}

//=======================================================================================
// IIFCExporter Interface Implementation
//=======================================================================================

bool IFCExporter::SetIFCVersion(IFCExportOptions::IFCVersion version) {
    m_state.version = version;
    return true;
}

bool IFCExporter::SetProjectInfo(const std::string& projectName, const std::string& description) {
    m_state.projectName = projectName;
    m_state.projectDescription = description;
    return true;
}

bool IFCExporter::CreateSite(const std::string& siteName) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcSite
        auto site = m_ifcBuilder->createIfcSite();
        site->setGlobalId(GenerateGUID());
        site->setName(ToIfcLabel(siteName));
        site->setDescription(ToIfcText("Site created by iModel Export Framework"));
        
        // Set owner history
        site->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        site->setObjectPlacement(CreateIfcLocalPlacement(Point3d(0, 0, 0)));
        
        m_site = site;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC site: " + siteName);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC site: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - site creation skipped");
    return false;
#endif
}

bool IFCExporter::CreateBuilding(const std::string& buildingName, const std::string& siteId) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcBuilding
        auto building = m_ifcBuilder->createIfcBuilding();
        building->setGlobalId(GenerateGUID());
        building->setName(ToIfcLabel(buildingName));
        building->setDescription(ToIfcText("Building created by iModel Export Framework"));
        
        // Set owner history
        building->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        building->setObjectPlacement(CreateIfcLocalPlacement(Point3d(0, 0, 0)));
        
        m_building = building;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC building: " + buildingName);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC building: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - building creation skipped");
    return false;
#endif
}

bool IFCExporter::CreateBuildingStorey(const std::string& storeyName, const std::string& buildingId, double elevation) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcBuildingStorey
        auto storey = m_ifcBuilder->createIfcBuildingStorey();
        storey->setGlobalId(GenerateGUID());
        storey->setName(ToIfcLabel(storeyName));
        storey->setDescription(ToIfcText("Building storey created by iModel Export Framework"));
        
        // Set owner history
        storey->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement with elevation
        storey->setObjectPlacement(CreateIfcLocalPlacement(Point3d(0, 0, elevation)));
        
        // Set elevation
        storey->setElevation(ToIfcLengthMeasure(elevation));
        
        m_buildingStoreys[storeyName] = storey;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC building storey: " + storeyName + " at elevation " + std::to_string(elevation));
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC building storey: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - building storey creation skipped");
    return false;
#endif
}

//=======================================================================================
// Building Element Creation
//=======================================================================================

bool IFCExporter::AddWall(const std::string& wallId, const std::vector<Point3d>& profile, double height, const std::string& storeyId) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcWall
        auto wall = m_ifcBuilder->createIfcWall();
        wall->setGlobalId(GenerateGUID());
        wall->setName(ToIfcLabel("Wall_" + wallId));
        wall->setDescription(ToIfcText("Wall created by iModel Export Framework"));
        
        // Set owner history
        wall->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        wall->setObjectPlacement(CreateIfcLocalPlacement(profile.empty() ? Point3d(0, 0, 0) : profile[0]));
        
        // Create wall geometry
        if (!profile.empty()) {
            auto extrudedSolid = CreateIfcExtrudedAreaSolid(profile, Vector3d(0, 0, 1), height);
            if (extrudedSolid) {
                // Create product definition shape
                auto shape = m_ifcBuilder->createIfcProductDefinitionShape();
                auto shapeRep = m_ifcBuilder->createIfcShapeRepresentation();
                shapeRep->getItems().push_back(extrudedSolid);
                shape->getRepresentations().push_back(shapeRep);
                wall->setRepresentation(shape);
            }
        }
        
        m_state.wallsCreated++;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC wall: " + wallId);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC wall: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - wall creation skipped");
    return false;
#endif
}

bool IFCExporter::AddSlab(const std::string& slabId, const std::vector<Point3d>& boundary, double thickness, const std::string& storeyId) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcSlab
        auto slab = m_ifcBuilder->createIfcSlab();
        slab->setGlobalId(GenerateGUID());
        slab->setName(ToIfcLabel("Slab_" + slabId));
        slab->setDescription(ToIfcText("Slab created by iModel Export Framework"));
        
        // Set owner history
        slab->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        slab->setObjectPlacement(CreateIfcLocalPlacement(boundary.empty() ? Point3d(0, 0, 0) : boundary[0]));
        
        // Create slab geometry
        if (!boundary.empty()) {
            auto extrudedSolid = CreateIfcExtrudedAreaSolid(boundary, Vector3d(0, 0, 1), thickness);
            if (extrudedSolid) {
                // Create product definition shape
                auto shape = m_ifcBuilder->createIfcProductDefinitionShape();
                auto shapeRep = m_ifcBuilder->createIfcShapeRepresentation();
                shapeRep->getItems().push_back(extrudedSolid);
                shape->getRepresentations().push_back(shapeRep);
                slab->setRepresentation(shape);
            }
        }
        
        m_state.slabsCreated++;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC slab: " + slabId);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC slab: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - slab creation skipped");
    return false;
#endif
}

bool IFCExporter::AddColumn(const std::string& columnId, const Point3d& position, double height, double width, double depth, const std::string& storeyId) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcColumn
        auto column = m_ifcBuilder->createIfcColumn();
        column->setGlobalId(GenerateGUID());
        column->setName(ToIfcLabel("Column_" + columnId));
        column->setDescription(ToIfcText("Column created by iModel Export Framework"));
        
        // Set owner history
        column->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        column->setObjectPlacement(CreateIfcLocalPlacement(position));
        
        // Create column geometry (rectangular profile)
        auto rectProfile = CreateIfcRectangleProfileDef(width, depth);
        if (rectProfile) {
            auto extrudedSolid = CreateIfcExtrudedAreaSolid({}, Vector3d(0, 0, 1), height);
            if (extrudedSolid) {
                // Create product definition shape
                auto shape = m_ifcBuilder->createIfcProductDefinitionShape();
                auto shapeRep = m_ifcBuilder->createIfcShapeRepresentation();
                shapeRep->getItems().push_back(extrudedSolid);
                shape->getRepresentations().push_back(shapeRep);
                column->setRepresentation(shape);
            }
        }
        
        m_state.columnsCreated++;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC column: " + columnId);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC column: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - column creation skipped");
    return false;
#endif
}

bool IFCExporter::AddBeam(const std::string& beamId, const Point3d& start, const Point3d& end, double width, double height, const std::string& storeyId) {
#ifdef ODA_IFC_AVAILABLE
    if (!m_ifcModel || !m_ifcBuilder) {
        LogError("IFC model not initialized");
        return false;
    }

    try {
        // Create IfcBeam
        auto beam = m_ifcBuilder->createIfcBeam();
        beam->setGlobalId(GenerateGUID());
        beam->setName(ToIfcLabel("Beam_" + beamId));
        beam->setDescription(ToIfcText("Beam created by iModel Export Framework"));
        
        // Set owner history
        beam->setOwnerHistory(CreateIfcOwnerHistory());
        
        // Set placement
        beam->setObjectPlacement(CreateIfcLocalPlacement(start));
        
        // Calculate beam length
        double length = std::sqrt(
            (end.x - start.x) * (end.x - start.x) +
            (end.y - start.y) * (end.y - start.y) +
            (end.z - start.z) * (end.z - start.z)
        );
        
        // Create beam geometry (rectangular profile extruded along length)
        auto rectProfile = CreateIfcRectangleProfileDef(width, height);
        if (rectProfile) {
            Vector3d direction = Vector3d(end.x - start.x, end.y - start.y, end.z - start.z);
            auto extrudedSolid = CreateIfcExtrudedAreaSolid({}, direction, length);
            if (extrudedSolid) {
                // Create product definition shape
                auto shape = m_ifcBuilder->createIfcProductDefinitionShape();
                auto shapeRep = m_ifcBuilder->createIfcShapeRepresentation();
                shapeRep->getItems().push_back(extrudedSolid);
                shape->getRepresentations().push_back(shapeRep);
                beam->setRepresentation(shape);
            }
        }
        
        m_state.beamsCreated++;
        m_state.entitiesCreated++;
        
        LogInfo("Created IFC beam: " + beamId);
        return true;
    }
    catch (const std::exception& e) {
        LogError("Failed to create IFC beam: " + std::string(e.what()));
        return false;
    }
#else
    LogWarning("ODA IFC not available - beam creation skipped");
    return false;
#endif
}

} // namespace IModelExport
