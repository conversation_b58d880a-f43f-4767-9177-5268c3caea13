# DWG Entity Processor Tests

# Find required packages
find_package(GTest REQUIRED)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
    ${GTEST_INCLUDE_DIRS}
)

# Common DWG sources
set(DWG_COMMON_SOURCES
    ../../src/formats/dwg/entities/DWGEntityProcessor.cpp
    ../../src/formats/dwg/entities/DWGEntityProcessorFactory.cpp
    ../../src/formats/dwg/entities/DWGSplineProcessor.cpp
    ../../src/formats/dwg/entities/DWGPolylineProcessor.cpp
    ../../src/formats/dwg/DWGExporter.cpp
)

# Basic entity processor tests
set(DWG_BASIC_TEST_SOURCES
    test_dwg_entity_processors.cpp
    ${DWG_COMMON_SOURCES}
)

# Comprehensive entity tests
set(DWG_COMPREHENSIVE_TEST_SOURCES
    test_dwg_comprehensive.cpp
    ${DWG_COMMON_SOURCES}
)

# All entities tests
set(DWG_ALL_ENTITIES_TEST_SOURCES
    test_dwg_all_entities.cpp
    ${DWG_COMMON_SOURCES}
)

# Create test executables
add_executable(test_dwg_entity_processors ${DWG_BASIC_TEST_SOURCES})
add_executable(test_dwg_comprehensive ${DWG_COMPREHENSIVE_TEST_SOURCES})
add_executable(test_dwg_all_entities ${DWG_ALL_ENTITIES_TEST_SOURCES})

# Link libraries
target_link_libraries(test_dwg_entity_processors
    ${GTEST_LIBRARIES}
    ${GTEST_MAIN_LIBRARIES}
    pthread
)

target_link_libraries(test_dwg_comprehensive
    ${GTEST_LIBRARIES}
    ${GTEST_MAIN_LIBRARIES}
    pthread
)

target_link_libraries(test_dwg_all_entities
    ${GTEST_LIBRARIES}
    ${GTEST_MAIN_LIBRARIES}
    pthread
)

# Compiler definitions
target_compile_definitions(test_dwg_entity_processors PRIVATE
    # Disable RealDWG for testing (unless available)
    # REALDWG_AVAILABLE
)

target_compile_definitions(test_dwg_comprehensive PRIVATE
    # Disable RealDWG for testing (unless available)
    # REALDWG_AVAILABLE
)

target_compile_definitions(test_dwg_all_entities PRIVATE
    # Disable RealDWG for testing (unless available)
    # REALDWG_AVAILABLE
)

# Add tests to CTest
add_test(NAME DWGEntityProcessorTests COMMAND test_dwg_entity_processors)
add_test(NAME DWGComprehensiveTests COMMAND test_dwg_comprehensive)
add_test(NAME DWGAllEntitiesTests COMMAND test_dwg_all_entities)

# Set test properties
set_tests_properties(DWGEntityProcessorTests PROPERTIES
    TIMEOUT 60
    LABELS "dwg;entity_processors;unit_tests"
)

set_tests_properties(DWGComprehensiveTests PROPERTIES
    TIMEOUT 120
    LABELS "dwg;comprehensive;integration_tests"
)

set_tests_properties(DWGAllEntitiesTests PROPERTIES
    TIMEOUT 180
    LABELS "dwg;all_entities;comprehensive_tests"
)
