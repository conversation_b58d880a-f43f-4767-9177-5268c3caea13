# DWG Entity Processor Tests

# Find required packages
find_package(GTest REQUIRED)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
    ${GTEST_INCLUDE_DIRS}
)

# Test sources
set(DWG_TEST_SOURCES
    test_dwg_entity_processors.cpp
    ../../src/formats/dwg/entities/DWGEntityProcessor.cpp
    ../../src/formats/dwg/entities/DWGSplineProcessor.cpp
    ../../src/formats/dwg/DWGExporter.cpp
)

# Create test executable
add_executable(test_dwg_entity_processors ${DWG_TEST_SOURCES})

# Link libraries
target_link_libraries(test_dwg_entity_processors
    ${GTEST_LIBRARIES}
    ${GTEST_MAIN_LIBRARIES}
    pthread
)

# Compiler definitions
target_compile_definitions(test_dwg_entity_processors PRIVATE
    # Disable RealDWG for testing (unless available)
    # REALDWG_AVAILABLE
)

# Add test to CTest
add_test(NAME DWGEntityProcessorTests COMMAND test_dwg_entity_processors)

# Set test properties
set_tests_properties(DWGEntityProcessorTests PROPERTIES
    TIMEOUT 60
    LABELS "dwg;entity_processors;unit_tests"
)
