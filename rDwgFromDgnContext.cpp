/*----------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFromDgnContext.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*----------------------------------------------------------------------+
|                                                                       |
|   Local Static Data                                                   |
|                                                                       |
+----------------------------------------------------------------------*/
static  int       s_excludedLinkageIds[] =
    {
    LINKAGEID_XData,
    LINKAGEID_Node,
    LINKAGEID_CellDef,
    LINKAGEID_ACS,
    LINKAGEID_AssociatedElements,
    LINKAGEID_AnimatorCompressionCell,
    LINKAGEID_Feature,
    LINKAGEID_EmbeddedBRep,
    LINKAGEID_Profile,
    LINKAGEID_String,
    LINKAGEID_BitMask,
    LINKAGEID_Thickness,
    LINKAGEID_DoubleArray,
    LINKAGEID_ToolTemplate,
    LINKAGEID_AssocRegion,
    LINKAGEID_SeedPoints,
    LINKAGEID_MultipleLevels,
    LINKAGEID_ClipBoundary,
    LINKAGEID_FilterMember,
    LINKAGEID_Symbology,
    // LINKAGEID_XML,                   <= ABD uses this for Groupdata info
    LINKAGEID_BoundaryAssociations,
    LINKAGEID_LoopOEDCode,
    LINKAGEID_LevelLibrary,
    LINKAGEID_InfiniteLine,
    LINKAGEID_SharedCellFlags,
    LINKAGEID_ACISModelerNode,
    LINKAGEID_Dependency,
    LINKAGEID_DimExtensionLinkage,
    DISPLAY_ATTRIBUTE_ID,
    STYLELINK_ID,
    PATTERN_ID,
    LINKAGEID_TEXTSTYLE,
    MLNOTE_USERATTR_SIGNATURE,
    TEXTATTR_ID,
    TEXTNODE_Linkage,
    TEXT_Linkage,
    TEXT_IndentationLinkage,
    LINKAGEID_TextRendering
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    CaseyMullen     01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetScanCriteriaLinkageTest
(
ScanCriteriaP               pScanCriteria, // => subject
Int16*                      searchWords,   // => array of words for extended scan. May be NULL.
Int16                       nSearchWords,  // => number of words in searchWords
UInt16                      signature      // => signature id of elements
)
    {
    Int16               numWords    = nSearchWords + 2;
    ExtendedAttrBuf     extAttrBuf;

    /*---------------------------------------------------+
    |  set extended attribute buffer and clear it        |
    +---------------------------------------------------*/
    memset (&extAttrBuf, 0, sizeof (ExtendedAttrBuf));

    extAttrBuf.numWords      = numWords;
    extAttrBuf.extAttData[0] = 0x1000; /* verify user bit is set */
    extAttrBuf.extAttData[1] = 0xffff;

    LinkageUtil::SetWords ((LinkageHeader *)extAttrBuf.extAttData, numWords);

    extAttrBuf.extAttData[numWords]     = extAttrBuf.extAttData[0];
    extAttrBuf.extAttData[numWords + 1] = signature;

    if (searchWords)
        {
        for (int iWord = 0, j = 2; iWord < nSearchWords; iWord++, j++ )
            {
            extAttrBuf.extAttData[j] = 0xffff;
            extAttrBuf.extAttData[j + numWords] = searchWords[iWord];
            }
        }

    pScanCriteria->SetAttributeTest (false, false, &extAttrBuf);
    return  BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsTFDemModel
(
UInt32                      scanBuffer[],
UInt32                      dataSize,
Int16                       searchWords[],
Int16                       numWords,
UInt16                      signature,
DgnModelP                   modelRef
)
    {
    Type66Linkage   type66Linkage;

    // search for type 66 for DEM model linkage
    for (UInt32 iElem = 0; iElem < dataSize; iElem++)
        {
        ElementRefP elemRef = modelRef->FindByFilePos (scanBuffer[iElem], true);
        if (NULL == elemRef || elemRef->IsEOF())
            continue;

        MSElementDescrP     pElmdscr = NULL;
        if (BSISUCCESS == elemRef->GetElementDescr(pElmdscr, modelRef, false) && NULL != pElmdscr)
            {
            /*---------------------------------------------------------------------------
            RSCID_DataDef_type66LinkInfo=435757001 will fail to extract DEM linkage when
            Triforma is absent, so simply pass NULL to get the type 66 linkage header.
            ---------------------------------------------------------------------------*/
            if (linkage_extractFromElement(&type66Linkage, &pElmdscr->el, signature, NULL, NULLFUNC, NULL))
                {
                if ((type66Linkage.type66LI.string == searchWords[0]) && (2 == numWords ? type66Linkage.type66LI.ver == searchWords[1] : true))
                    {
                    pElmdscr->Release ();
                    return  true;
                    }
                }

            pElmdscr->Release ();
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    CaseyMullen     11/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetAppElmBySecondaryIds
(
bool*                       pIsDemModel,
DgnModelP                   modelRef    // =>
)
    {
    ScanCriteria*   pScanCriteria   = NULL;
    Int16           searchWords[2]  = {0x4644, 1};  /* Search in word 3 for "DF" and in word 4 for 1 */
    Int16           numWords        = sizeof(searchWords)/sizeof(Int16);
    UInt16          signature       = 48742;    /* LINKAGEID_TFTYPE66 */
    bool            found           = false;
    int             scanStatus      = 0;

    if (NULL == (pScanCriteria = ScanCriteria::Create()))
        {
        BeAssert (false && L"ScanCriteria::Create() has failed!");
        return  false;
        }

    pScanCriteria->SetModelRef (modelRef);
    // Setup to return FilePositions. Set NestCells to be true
    pScanCriteria->SetReturnType (MSSCANCRIT_RETURN_FILEPOS, false, true);

    // scan for type 66 level 20 only
    pScanCriteria->AddSingleElementTypeTest (MICROSTATION_ELM);
    pScanCriteria->AddSingleLevelTest (20);
    pScanCriteria->SetModelSections (DgnModelSections::ControlElements);
    SetScanCriteriaLinkageTest (pScanCriteria, searchWords, numWords, signature);

    if (NULL != pIsDemModel)
        *pIsDemModel = false;

    do
        {
        UInt32  scanBuffer[1024];
        UInt32  scanPosition        = 0;
        int     acceptSize          = sizeof (scanBuffer) / sizeof (Int16);

        // perform the actual scan
        scanStatus = pScanCriteria->Scan ((void *)scanBuffer, &acceptSize, &scanPosition, NULL);

        if (acceptSize)
            {
            found = true;

            // check Triforma DEM model linkage
            if (NULL != pIsDemModel)
                {
                UInt32  dataSize = acceptSize * sizeof(short) / sizeof(ULong);

                *pIsDemModel = IsTFDemModel (scanBuffer, dataSize, searchWords, numWords, signature, modelRef);
                }

            break;
            }

        } while (scanStatus == BUFF_FULL);

    if (NULL != pScanCriteria)
        ScanCriteria::Delete (pScanCriteria);

    return found;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Wouter Rombouts 01/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                  IsATriformaDrawing
(
bool            *pIsDemModel,
DgnModelP       modelRef /*=> */
)
    {
    return GetAppElmBySecondaryIds (pIsDemModel, modelRef);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    GarySmith       4/05
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsTriformaDrawingElement
(
ElementHandleCR             inElement
)
    {
    return inElement.IsValid() && NULL != linkage_extractFromElement(NULL, inElement.GetElementCP(), LINKAGEID_TFSMARTSECT_DATA, NULL, NULL, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsStructuralTriformaNode (ElementHandleCR inElement)
    {
    WChar     name[MAX_CELLNAME_LENGTH];
    return inElement.IsValid() && SUCCESS == CellUtil::ExtractName(name, _countof(name), inElement) && 0 == wcscmp(L"Structural Element", name);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsTriformaNode (ElementHandleCR inElement)
    {
    return inElement.IsValid() && inElement.GetElementType() == CELL_HEADER_ELM && NULL != linkage_extractFromElement(NULL, inElement.GetElementCP(), LINKAGEID_TriFormaNode, NULL, NULL, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsTriformaForm (ElementHandleCR inElement)
    {
    return  !m_disallowBlockNameFromTriForma && !this->IsTriformaDrawing() &&
            !this->IsTriformaDrawingElement (inElement) && this->IsTriformaNode (inElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsACISModelerNode (MSElementCP pElement)
    {
    return pElement->ehdr.type == CELL_HEADER_ELM && NULL != linkage_extractFromElement(NULL, pElement, LINKAGEID_ACISModelerNode, NULL, NULL, NULL);
    }


/*=================================================================================**//**
* @bsiclass                                                     RayBentley      07/2005
+===============+===============+===============+===============+===============+======*/
struct  PrioritySorter
    {
    /*-----------------------------------------------------------------------------------
    This class sorts elements by either DGN cache file position or by a relative position
    of elements in a sequence they are created in an output block.

    In either case, it also sorts new entities created/dropped from a cache element, such
    as filled hatches, unsupported elements and linestyles etc.  Each dropped element has
    the same primary order of its original element, but has a secondary order relative to 
    the original element.
    -----------------------------------------------------------------------------------*/
public:
enum PrioritySorterType
    {
    ByFilePosition,
    ByElementSequence,
    };

private:
    AvlTree*            m_pPriorityTree;
    AvlTree*            m_pIdTree;
    PrioritySorterType  m_prioritySorterType;
    UInt32              m_currentElementPos;            // last incremental index for PrioritySorterType==ByElementSequence
    UInt32              m_basePosForDroppedElements;    // filePos or elementPos of the base element on which its dropped elements are based.

    struct PriorityNode
        {
        UInt32          m_filePos;      // primary comparison for cache elements, served either as filePos or as sequential order
        Int32           m_secondaryPos; // secondary comparison for entities added from base element (i.e. filled hatch, dropped elements, etc)
        Int32           m_priority;
        ElementId       m_elementId;

        PriorityNode (Int32 priority, UInt32 filePos, Int32 secondaryPos, ElementId elementId)
            {
            m_priority = priority;
            m_filePos = filePos;
            m_elementId = elementId;
            m_secondaryPos = secondaryPos;
            }
        };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/05
+---------------+---------------+---------------+---------------+---------------+------*/
static int              PriorityTreeCompare
(
void const   *pNode1,
void const   *pNode2
)
    {
    PriorityNode const      *pPriorityNode1 = (PriorityNode const*) pNode1;
    PriorityNode const      *pPriorityNode2 = (PriorityNode const*) pNode2;

    if (pPriorityNode1->m_priority < pPriorityNode2->m_priority)
        {
        return -1;
        }
    else if (pPriorityNode1->m_priority > pPriorityNode2->m_priority)
        {
        return 1;
        }
    else
        {
        if (pPriorityNode1->m_filePos < pPriorityNode2->m_filePos)
            return -1;
        else if (pPriorityNode1->m_filePos > pPriorityNode2->m_filePos)
            return 1;
        else if (pPriorityNode1->m_secondaryPos < pPriorityNode2->m_secondaryPos)
            return  -1;
        else if (pPriorityNode1->m_secondaryPos > pPriorityNode2->m_secondaryPos)
            return  1;
        else
            return 0;
        }
    }



public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/05
+---------------+---------------+---------------+---------------+---------------+------*/
PrioritySorter (PrioritySorterType type = ByFilePosition)
    {
    /*-------------------------------------------------------------------------------------------------------------------------------
    Since we use two AVL trees to track element priority, we have to allow dup entries for m_pPriorityTree to match up with m_pIdTree.
    This is because we allow elements to have the same priority defined in above PriorityTreeCompare, hence dup entries. TR 318520.
    Note that while dup priority entries are allowed, dup element ID entries are not.
    -------------------------------------------------------------------------------------------------------------------------------*/
    m_pPriorityTree = mdlAvlTree_initExtended (AVLKEY_USER | AVLKEY_DUPLICATE, PriorityTreeCompare, 0,  0);
    m_pIdTree = mdlAvlTree_init (AVLKEY_UINT64);
    m_prioritySorterType = type;
    m_currentElementPos = 0;
    m_basePosForDroppedElements = 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/05
+---------------+---------------+---------------+---------------+---------------+------*/
~PrioritySorter ()
    {
    mdlAvlTree_free (&m_pPriorityTree, NULLFUNC, NULL);
    mdlAvlTree_free (&m_pIdTree, NULLFUNC, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/05
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddElement (ElementHandleCR elemHandle, Int32 secondaryOrder=0, ElementId newEntityId=0)
    {
    MSElementCP             pElem = elemHandle.GetElementCP();
    ElementId               elementId = 0 == newEntityId ? pElem->ehdr.uniqueId : newEntityId;

    if ((pElem->ehdr.isGraphics || REFERENCE_ATTACH_ELM == pElem->ehdr.type) && (0 != elementId && INVALID_ELEMENTID != elementId))
        {
        Int32          priority = 0;
        if (!pElem->hdr.dhdr.props.b.is3d && REFERENCE_ATTACH_ELM != pElem->ehdr.type)
            {
            priority = ElemDisplayParams::CalcDefaultNetDisplayPriority (pElem->hdr.dhdr.priority, pElem->ehdr.level, *elemHandle.GetModelRef ());
            }

        // if the element ID is not already in the list, add it (it can be added for relative element sorting, e.g. a hacth added for a shape that is in a cell).
        this->AddElement (elementId, priority, this->GetPrimaryOrder(elemHandle), secondaryOrder);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            AddElement (ElementId elementId, Int32 priority, UInt32 primaryOrder, Int32 secondaryOrder)
    {
    if (nullptr == mdlAvlTree_search(m_pIdTree, &elementId))
        {
        PriorityNode    priorityNode (priority, primaryOrder, secondaryOrder, elementId);
        mdlAvlTree_insertNode (m_pPriorityTree, &priorityNode, sizeof(priorityNode));
        mdlAvlTree_insertNode (m_pIdTree, (void *) &elementId, sizeof (ElementId));
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/11
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      GetPrimaryOrder (ElementHandleCR elemHandle)
    {
    if (m_basePosForDroppedElements > 0)
        {
        // use base element pos for dropped chain elements, for either ByFilePosition or ByElementSequence
        return  m_basePosForDroppedElements;
        }
    else if (ByElementSequence == m_prioritySorterType)
        {
        // use relative element pos for sequential sorting:
        return m_currentElementPos++;
        }
    else
        {
        // use filePos from cache element for file position sorting:
        ElementRefP elemref = elemHandle.GetElementRef ();
        return NULL == elemref ? 0 : elemref->GetFilePos ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/11
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetBaseElementPosition (UInt32 newPos)
    {
    // set base filePos/elementPos from an element to be dropped, then reset it to 0 after done.
    m_basePosForDroppedElements = newPos;
    }


/*=================================================================================**//**
* This class compare two pairs of object handles. Returns true if handlePair1 < handlePair2
* @bsiclass                                                     Don.Fu          03/09
+===============+===============+===============+===============+===============+======*/
class HandlePairComparison : public std::binary_function<HandlePair, HandlePair, bool>
{
public:
bool HandlePairComparison::operator() (const HandlePair& handlePair1, const HandlePair& handlePair2)
        {
        AcDbHandle handle1 = handlePair1.first;
        AcDbHandle handle2 = handlePair2.first;

        if (handle1 < handle2)
          return true;
        else if (handle1 > handle2)
          return false;

        AcDbHandle  handlePair1Second = handlePair1.second.handle();
        AcDbHandle  handlePair2Second = handlePair2.second.handle();

        // unknown object ID?
        if(handlePair1Second.isNull() || handlePair2Second.isNull())
          return false;

        /*-------------------------------------------------------------------------------
        Handle entities having the same draw order: an entity with no draw order will
        draw first.
        -------------------------------------------------------------------------------*/
        if (handle1 == handlePair1Second)
          handle1 += -1;

        if (handle2 == handlePair2Second)
          handle2 += -1;

        return handle1 < handle2;
        }
};  // HandlePairsComparison

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetSortentsAbsoluteDrawOrder
(
AcDbSortentsTable*          pSortentsTable,
const HandlePairsArray&     handlePairs,
const AcDbBlockTableRecord* pBlock
)
    {
    // get entity ids in the block and sort them:
    AcDbBlockTableRecordIterator*   pBlockIter = NULL;
    if (Acad::eOk != pBlock->newIterator(pBlockIter))
        return  BlockIteratorFailure;

    ObjectIdArray           blockIds;
    for (; !pBlockIter->done(); pBlockIter->step())
        {
        AcDbObjectId        entityId;
        if (Acad::eOk == pBlockIter->getEntityId(entityId))
            blockIds.push_back (entityId);
        }

    delete pBlockIter;

    std::sort (blockIds.begin(), blockIds.end());

    // get input ids and sort them
    ObjectIdArray       sortIds;
    sortIds.reserve (handlePairs.size());
    for (HandlePairsArray::const_iterator pCurr = handlePairs.begin(); pCurr != handlePairs.end(); pCurr++)
        sortIds.push_back (pCurr->second);

    std::sort (sortIds.begin(), sortIds.end());

    // check input ids against block entities:
    ObjectIdArray::iterator     sortIdIter, blockIdIter;
    for(sortIdIter = sortIds.begin(), blockIdIter = blockIds.begin(); sortIdIter != sortIds.end(); sortIdIter++)
        {
        while (*sortIdIter > *blockIdIter && blockIdIter != blockIds.end())
            blockIdIter++;

        if (blockIdIter == blockIds.end() || *sortIdIter != *blockIdIter)
            return  UnmatchedIds;
        }

    // check input handles for dups
    HandlePairsArray        sortedHandlePairs = handlePairs;
    HandlePairComparison    handlePairComparison;
    std::sort(sortedHandlePairs.begin(), sortedHandlePairs.end(), handlePairComparison);

    for(UInt32 i = 1; i < sortedHandlePairs.size(); i ++)
        {
        if (sortedHandlePairs[i-1].first == sortedHandlePairs[i].first)
            return  DuplicatedIds;
        }

    // input handle pairs appear valid, save them to sortents table.
    RecordingFiler  filer (10+sortedHandlePairs.size());
    filer.RecordData (pSortentsTable);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("Sortents table from DWGOUT filing:");
    /*-----------------------------------------------------------------------------------
      0: kDwgSoftPointerId, ffd0a058 fe189c38 AcDbDictionary
      1: kDwgUInt32,    1 (0x1)
      2: kDwgSoftPointerId, ffd0a058 fe189c38 AcDbDictionary
      3: kDwgHardOwnershipId, NULL
      4: kDwgSoftPointerId, ffd09c38 fe18a058 AcDbBlockTableRecord
      5: kDwgUInt32,    0 (0x0)
      ...
    -----------------------------------------------------------------------------------*/
#endif

    FilerDataList&      dataList = filer.GetDataList ();
    UInt32              dataIndex = 6;

    UInt32FilerData*    intData = dynamic_cast <UInt32FilerData *> (dataList[5]);
    if (NULL == intData)
        return BadDataSequence;

    size_t              newCount = handlePairs.size ();
    size_t              oldCount = intData->GetValue ();
    intData->SetValue (newCount);

    SoftPointerIdFilerData* idData;
    HandleFilerData*        handleData;

    for (size_t i = 0; i < newCount; i++)
        {
        if (i < oldCount)
            {
            idData = dynamic_cast <SoftPointerIdFilerData *> (dataList[dataIndex++]);
            if (NULL == idData)
                return  BadDataSequence;
            handleData = dynamic_cast <HandleFilerData *> (dataList[dataIndex++]);
            if (NULL == idData)
                return  BadDataSequence;

            idData->SetValue (sortedHandlePairs[i].second);
            handleData->SetValue (sortedHandlePairs[i].first);
            }
        else
            {
            idData = new SoftPointerIdFilerData (sortedHandlePairs[i].second);
            handleData = new HandleFilerData (sortedHandlePairs[i].first);

            filer.InsertEntryAt (dataIndex++, idData);
            filer.InsertEntryAt (dataIndex++, handleData);
            }
        }

    Acad::ErrorStatus   errorStatus = filer.PlaybackData (pSortentsTable);

#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == errorStatus)
        {
        RecordingFiler  check (10+newCount);
        check.RecordData (pSortentsTable);
        check.DumpList ("Sortents table from DWGIN filing:");
        }
#endif

    return  Acad::eOk == errorStatus ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/05
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetSortEnts
(
AcDbDatabase*               pDatabase,
AcDbObjectId                blockId,
ConvertFromDgnContextR      context
)
    {
    AcDbObjectIdArray   bottomArray, topArray;
    AvlTreeIteratorP    pPriorityTreeIterator   = mdlAvlTreeIterator_create (m_pPriorityTree);
    AvlTreeIteratorP    pIdTreeIterator         = mdlAvlTreeIterator_create (m_pIdTree);
    bool                sortRequired            = false;
    HandlePairsArray    handlePairs;

    PriorityNode*       pNode;
    ElementId*          pID;
    for (pNode = (PriorityNode *) mdlAvlTreeIterator_getFirst (pPriorityTreeIterator), pID = (ElementId *) mdlAvlTreeIterator_getFirst (pIdTreeIterator);
            (NULL != pNode) && (NULL != pID) ;
                pNode = (PriorityNode*) mdlAvlTreeIterator_getNext (pPriorityTreeIterator), pID = (ElementId*) mdlAvlTreeIterator_getNext (pIdTreeIterator))
        {
        AcDbObjectId            objectId;
        if (Acad::eOk != pDatabase->getAcDbObjectId (objectId, false, context.DBHandleFromElementId (pNode->m_elementId), 0))
            continue;

        AcDbSoftPointerId       softPointerId  (objectId);
        if (softPointerId.isValid() && !objectId.isErased())
            {
            AcDbObjectPointer<AcDbObject> pObject (objectId, AcDb::kForRead);
            if ( (Acad::eOk == pObject.openStatus()) && (pObject->ownerId() == blockId) )
                {
                sortRequired = sortRequired || (*pID != pNode->m_elementId);
                handlePairs.push_back (HandlePair (context.DBHandleFromElementId (*pID), softPointerId));
                }
            }
        }

    if (sortRequired)
        {
        AcDbBlockTableRecord*   pBlock = NULL;
        RealDwgStatus           status = CantOpenObject;
        if (Acad::eOk == acdbOpenObject (pBlock, blockId, AcDb::kForWrite))
            {
            AcDbSortentsTable*  pSortentsTable;
            if (Acad::eOk == pBlock->getSortentsTable(pSortentsTable, AcDb::kForWrite, true))
                {
                status = SetSortentsAbsoluteDrawOrder (pSortentsTable, handlePairs, pBlock);

                pSortentsTable->close();
                }
            pBlock->close ();
            }

        if (RealDwgSuccess != status)
            DIAGNOSTIC_PRINTF ("Error setting sortents table!");
        }

    mdlAvlTreeIterator_free (&pPriorityTreeIterator);
    mdlAvlTreeIterator_free (&pIdTreeIterator);
    }

};  // PrioritySorter


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
DwgOpenUnitMode         ConvertFromDgnContext::GetDwgOpenUnitModeFromSaveUnitMode (DwgSaveUnitMode saveUnitMode)
    {
    if (DWGSaveUnitMode_ArchOrEngineering == saveUnitMode)
        return static_cast <DwgOpenUnitMode> (StandardUnit::EnglishInches);
    else 
        return static_cast <DwgOpenUnitMode> (saveUnitMode);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertFromDgnContext::ConvertFromDgnContext
(
DgnModelP               modelRef,
ModelId                 modelId,
FileHolder*             pFileHolder,
DgnFileFormatType       format,
DwgFileVersion          version,
DwgSaveUnitMode         saveUnitMode,
bool                    omitReferencePath,
bool                    threeD,
UInt32                  elementCount,
IDwgConversionSettings& settings
) : ConvertContext (settings)
    {
    DgnFileP            dgnFile = modelRef->GetDgnFileP ();
    BeAssert (NULL != dgnFile);
    TcbCP               pTcb = dgnFile->GetPersistentTcb ();
    BeAssert (NULL != pTcb);

    m_savingChanges                             = false;
    m_modelId                                   = modelId;
    m_modelIdForModelSpace                      = RealDwgUtil::GetDwgModelSpaceId (dgnFile);
    m_format                                    = format;
    m_targetVersion                             = (version == DwgFileVersion_Unknown) ? RealDwgUtil::MSVersionFromAcVersion (pFileHolder->GetDatabase()->originalFileVersion()): version;
    m_omitReferencePath                         = omitReferencePath;
    m_threeD                                    = threeD;
    m_pMatchingCellTree                         = mdlAvlTree_init (AVLKEY_WSTRING | AVLKEY_DUPLICATE);
    m_levelDisplayView                          = settings.GetLevelDisplayView ();
    m_useLevelSymbologyOverrides                = GetSettings().UseLevelSymbologyOverrides();        // this is changed at runtime, so don't use GetSettings().UseLevelSymbologOverrides elsewhere!
    m_elementCount                              = elementCount;
    m_msInsertLayerId.setNull();
    m_tagSetList.clear ();
    m_preSavedElementList.clear ();
    m_itemTypeTargetList.clear ();
    m_blockListForNonUniformScaling.clear ();
    m_mapOverrideCloneBlock.clear();
    m_xRefBlockPurgeRequired                    = false;
    m_ignoreTriformaNodes                       = false;
    m_targetUnitMode                            = saveUnitMode;
    m_prioritySorter                            = NULL;
    m_saveElementPurpose                        = SAVEELEMENTPURPOSE_Normal;
    m_modelScaleDefinition                      = NULL;
    m_savingApplicationData                     = this->GetSettings().SaveApplicationData ();

    // get the annotation scale collection from the host - the constructor populates the list from scales.def:
    ScaleIteratorOptionsPtr     iterationOptions = ScaleIteratorOptions::Create ();
    ScaleCollection             scalesFromFile (*iterationOptions);
    m_annotationScaleCollection                 = DgnAnnotationScaleList (scalesFromFile);

    // append active model scale if it is not already in the list (this happens when a custom scale is set as active):
    ScaleDefinition     activeScale (modelRef->GetModelInfo().GetAnnotationScaleFactor(), true);
    if (!m_annotationScaleCollection.FindByName(activeScale.GetName()))
        m_annotationScaleCollection.AddScale (activeScale);

    if (nullptr != pTcb)
        memcpy (&m_activeDgnSymbology, &pTcb->symbology, sizeof m_activeDgnSymbology);
    else
        memset (&m_activeDgnSymbology, 0, sizeof m_activeDgnSymbology);

    // save default model's annotation scale toggle status
    DgnModelP       defaultModel = dgnFile->FindLoadedModelById (dgnFile->GetDefaultModelId());
    if (NULL != dgnFile && NULL != (defaultModel = dgnFile->FindLoadedModelById(dgnFile->GetDefaultModelId())))
        m_isDefaultModelAnnotationScaleOn       = defaultModel->GetModelInfo().GetIsUseAnnotationScaleOn ();
    else
        m_isDefaultModelAnnotationScaleOn       = false;

    // m_disallowBlockNamesFromTriforma is changed during execution. Initialize it from settings.
    m_disallowBlockNameFromTriForma             = m_dwgSettings.DisallowBlockNameFromTriForma();
    // if we do not create blocks from TriForma, don't bother to check for TF part names
    if (!m_dwgSettings.CreateBlocksFromTriForma())
        m_disallowBlockNameFromTriForma = true;

    // m_dropUnsupportedLineStyles is changed during execution. Initialize it from settings.
    m_dropUnsupportedLineStyles  = m_dwgSettings.DropUnsupportedLineStyles();

    // RealDWG supports only R14 and higher.
    m_targetVersion = RealDwgUtil::ValidateSaveVersion (m_targetVersion, DgnFileFormatType::DXF == m_format);

    ConfigurationManager::GetVariable (m_sourceViewGroupName, L"MS_DWG_SOURCEVIEWGROUP");

    m_isTriformaDemModel = false;
    m_isTriformaDrawing = IsATriformaDrawing (GetSettings().DropTriformaCells() ? &m_isTriformaDemModel : NULL, modelRef);

    memset (&m_viewDisplayFlags, 0, sizeof(m_viewDisplayFlags));
    m_outputFileName.clear ();
    m_dgnUnderlayDefInfoArray.clear ();

    DwgOpenUnitMode translatedUnitMode = GetDwgOpenUnitModeFromSaveUnitMode (saveUnitMode);
    this->Initialize (modelRef, pFileHolder, pFileHolder->GetModelItemByModelId (m_modelId), translatedUnitMode);

    this->SaveActiveViewGroupToDwg();

    if (this->GetSettings().CreateDWGMaterials())
        MaterialManager::GetManagerR().LoadActiveTable (*modelRef);

    m_dropUnsupportedPatterns = !ConfigurationManager::IsVariableDefined (L"MS_DWG_IGNORE_LSTYLEONPATTERN");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 FreeMatchingCellNode
(
void*                       nodeToFreeP,
void*                       optArgP
)
    {
    MatchingCellNode*   pNode = static_cast<MatchingCellNode *>(nodeToFreeP);

    pNode->pCellDescr->Release ();
    pNode->pCellDescr = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertFromDgnContext::~ConvertFromDgnContext
(
)
    {
    mdlAvlTree_free (&m_pMatchingCellTree, FreeMatchingCellNode, NULL);
    if (NULL != m_modelScaleDefinition)
        delete m_modelScaleDefinition;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
DwgFileVersion              ConvertFromDgnContext::GetTargetVersion ()  const   { return m_targetVersion; }
DwgSaveUnitMode             ConvertFromDgnContext::GetTargetUnitMode () const   { return m_targetUnitMode; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
DgnFileFormatType           ConvertFromDgnContext::GetFormat() const   { return m_format; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
ModelId                     ConvertFromDgnContext::GetModelId () const { return m_modelId; }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        UpdatePlotSettingsObjectInDictionary
(
const ACHAR*                plotName,
const AcDbPlotSettings*     plotSettingsIn
)
    {
    RealDwgStatus           status = CantOpenObject;

    AcDbDictionaryPointer   mainDictionary (plotSettingsIn->database()->namedObjectsDictionaryId(), AcDb::kForWrite);

    if (Acad::eOk == mainDictionary.openStatus())
        {
        AcDbObject*         dictionaryEntry = NULL;
        if (Acad::eOk == mainDictionary->getAt(L"ACAD_PLOTSETTINGS", dictionaryEntry, AcDb::kForWrite))
            {
            AcDbDictionary* plotsettingsDictionary = AcDbDictionary::cast (dictionaryEntry);
            AcDbObject*     objectEntry = NULL;

            if (NULL != plotsettingsDictionary &&
                Acad::eOk == plotsettingsDictionary->getAt(plotName, objectEntry, AcDb::kForWrite))
                {
                AcDbPlotSettings*   plotSettingsObj = AcDbPlotSettings::cast (objectEntry);

                if (NULL != plotSettingsObj && Acad::eOk == plotSettingsObj->copyFrom(plotSettingsIn))
                    status = RealDwgSuccess;

                objectEntry->close ();
                }

            dictionaryEntry->close ();
            }
        }

    return  status;
    }


static StandardPlotScale    s_standardPlotScaleList[] =
    {                                           // enum StdScaleType:   Displayed in ACAD:
    {0,     1,     1,    1},                    // kScaleToFit          "Scaled to Fit"
    {1,     1,     1536, 0.0006510416666667},   // k1_128in_1ft         "1/128\" = 1'"
    {2,     1,     768,  0.0013020833333333},   // k1_64in_1ft          "1/64\" = 1'"
    {3,     1,     384,  0.0026041666666667},   // k1_32in_1ft          "1/32\" = 1'"
    {4,     1,     192,  0.0052083333333333},   // k1_16in_1ft          "1/16\" = 1'"
    {5,     1,     128,  0.0078125},            // k3_32in_1ft          "3/32\" = 1'"
    {6,     1,     96,   0.0104166666666667},   // k1_8in_1ft           "1/8\" = 1'"
    {7,     1,     64,   0.015625},             // k3_16in_1ft          "3/16\" = 1'"
    {8,     1,     48,   0.0208333333333333},   // k1_4in_1ft           "1/4\" = 1'"
    {9,     1,     32,   0.03125},              // k3_8in_1ft           "3/8\" = 1'"
    {10,    1,     24,   0.0416666666666667},   // k1_2in_1ft           "1/2\" = 1'"
    {11,    1,     16,   0.0625},               // k3_4in_1ft           "3/4\" = 1'"
    {12,    1,     12,   0.0833333333333333},   // k1in_1ft             "1\" = 1'"
    {13,    1,     4,    0.25},                 // k3in_1ft             "3\" = 1'"
    {14,    1,     2,    0.5},                  // k6in_1ft             "6\" = 1'"
    {15,    1,     1,    1},                    // k1ft_1ft             "1' = 1'"
    {16,    1,     1,    1},                    // k1_1                 "1:1"
    {17,    1,     2,    0.5},                  // k1_2                 "1:2"
    {18,    1,     4,    0.25},                 // k1_4                 "1:4"
    {19,    1,     8,    0.125},                // k1_8                 "1:8"
    {20,    1,     10,   0.1},                  // k1_10                "1:10"
    {21,    1,     16,   0.0625},               // k1_16                "1:16"
    {22,    1,     20,   0.05},                 // k1_20                "1:20"
    {23,    1,     30,   0.03333333333333},     // k1_30                "1:30"
    {24,    1,     40,   0.025},                // k1_40                "1:40"
    {25,    1,     50,   0.2},                  // k1_50                "1:50"
    {26,    1,     100,  0.01},                 // k1_100               "1:100"
    {27,    2,     1,    2},                    // k2_1                 "2:1"
    {28,    4,     1,    4},                    // k4_1                 "4:1"
    {29,    8,     1,    8},                    // k8_1                 "8:1"
    {30,    10,    1,    10},                   // k10_1                "10:1"
    {31,    100,   1,    100},                  // k100_1               "100:1"
    {32,    1000,  1,    1000}                  // k1000_1              "1000:1"
    };
static UInt32               s_numberOfPlotScales = sizeof(s_standardPlotScaleList) / sizeof(StandardPlotScale);

struct StandardPlotMedia
    {
    const ACHAR*                        m_localeName;
    const ACHAR*                        m_canonicalName;
    double                              m_plotWidth;
    double                              m_plotHeight;
    AcDbPlotSettings::PlotPaperUnits    m_units;
    };

static StandardPlotMedia    s_standardPlotMediaList[] =
    {
    {L"Letter (8.50 x 11.00 Inches)"           , L"Letter_(8.50_x_11.00_Inches)"           , 8.50     , 11.00     , AcDbPlotSettings::kInches     } ,
    {L"Legal (8.50 x 14.0 Inches)"             , L"Legal_(8.50_x_14.0_Inches)"             , 8.50     , 14.0      , AcDbPlotSettings::kInches     } ,
    {L"ANSI A (8.50 x 11.00 Inches)"           , L"ANSI_A_(8.50_x_11.00_Inches)"           , 8.50     , 11.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI A (11.00 x 8.50 Inches)"           , L"ANSI_A_(11.00_x_8.50_Inches)"           , 11.00    , 8.50      , AcDbPlotSettings::kInches     } ,
    {L"ANSI B (11.00 x 17.00 Inches)"          , L"ANSI_B_(11.00_x_17.00_Inches)"          , 11.00    , 17.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI B (17.00 x 11.00 Inches)"          , L"ANSI_B_(17.00_x_11.00_Inches)"          , 17.00    , 11.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI C (17.00 x 22.00 Inches)"          , L"ANSI_C_(17.00_x_22.00_Inches)"          , 17.00    , 22.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI C (22.00 x 17.00 Inches)"          , L"ANSI_C_(22.00_x_17.00_Inches)"          , 22.00    , 17.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI D (22.00 x 34.00 Inches)"          , L"ANSI_D_(22.00_x_34.00_Inches)"          , 22.00    , 34.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI D (34.00 x 22.00 Inches)"          , L"ANSI_D_(34.00_x_22.00_Inches)"          , 34.00    , 22.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI E (34.00 x 44.00 Inches)"          , L"ANSI_E_(34.00_x_44.00_Inches)"          , 34.00    , 44.00     , AcDbPlotSettings::kInches     } ,
    {L"ANSI E (44.00 x 34.00 Inches)"          , L"ANSI_E_(44.00_x_34.00_Inches)"          , 44.00    , 34.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH C (18.00 x 24.00 Inches)"          , L"ARCH_C_(18.00_x_24.00_Inches)"          , 18.00    , 24.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH C (24.00 x 18.00 Inches)"          , L"ARCH_C_(24.00_x_18.00_Inches)"          , 24.00    , 18.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH D (24.00 x 36.00 Inches)"          , L"ARCH_D_(24.00_x_36.00_Inches)"          , 24.00    , 36.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH D (36.00 x 24.00 Inches)"          , L"ARCH_D_(36.00_x_24.00_Inches)"          , 36.00    , 24.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH E (36.00 x 48.00 Inches)"          , L"ARCH_E_(36.00_x_48.00_Inches)"          , 36.00    , 48.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH E (48.00 x 36.00 Inches)"          , L"ARCH_E_(48.00_x_36.00_Inches)"          , 48.00    , 36.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH E1 (30.00 x 42.00 Inches)"         , L"ARCH_E1_(30.00_x_42.00_Inches)"         , 30.00    , 42.00     , AcDbPlotSettings::kInches     } ,
    {L"ARCH E1 (42.00 x 30.00 Inches)"         , L"ARCH_E1_(42.00_x_30.00_Inches)"         , 42.00    , 30.00     , AcDbPlotSettings::kInches     } ,
    {L"ISO A4 (210.00 x 297.00 MM)"            , L"ISO_A4_(210.00_x_297.00_MM)"            , 210.00   , 297.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A4 (297.00 x 210.00 MM)"            , L"ISO_A4_(297.00_x_210.00_MM)"            , 297.00   , 210.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A3 (297.00 x 420.00 MM)"            , L"ISO_A3_(297.00_x_420.00_MM)"            , 297.00   , 420.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A3 (420.00 x 297.00 MM)"            , L"ISO_A3_(420.00_x_297.00_MM)"            , 420.00   , 297.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A2 (420.00 x 594.00 MM)"            , L"ISO_A2_(420.00_x_594.00_MM)"            , 420.00   , 594.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A2 (594.00 x 420.00 MM)"            , L"ISO_A2_(594.00_x_420.00_MM)"            , 594.00   , 420.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A1 (594.00 x 841.00 MM)"            , L"ISO_A1_(594.00_x_841.00_MM)"            , 594.00   , 841.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A1 (841.00 x 594.00 MM)"            , L"ISO_A1_(841.00_x_594.00_MM)"            , 841.00   , 594.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A0 (841.00 x 1189.00 MM)"           , L"ISO_A0_(841.00_x_1189.00_MM)"           , 841.00   , 1189.00   , AcDbPlotSettings::kMillimeters} ,
    {L"ISO A0 (1189.00 x 841.00 MM)"           , L"ISO_A0_(1189.00_x_841.00_MM)"           , 1189.00  , 841.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B5 (182.00 x 237.00 MM)"            , L"ISO_B5_(182.00_x_237.00_MM)"            , 182.00   , 237.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B5 (237.00 x 182.00 MM)"            , L"ISO_B5_(237.00_x_182.00_MM)"            , 237.00   , 182.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B4 (250.00 x 354.00 MM)"            , L"ISO_B4_(250.00_x_354.00_MM)"            , 250.00   , 354.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B4 (354.00 x 250.00 MM)"            , L"ISO_B4_(354.00_x_250.00_MM)"            , 354.00   , 250.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B2 (500.00 x 707.00 MM)"            , L"ISO_B2_(500.00_x_707.00_MM)"            , 500.00   , 707.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B2 (707.00 x 500.00 MM)"            , L"ISO_B2_(707.00_x_500.00_MM)"            , 707.00   , 500.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B1 (707.00 x 1000.00 MM)"           , L"ISO_B1_(707.00_x_1000.00_MM)"           , 707.00   , 1000.00   , AcDbPlotSettings::kMillimeters} ,
    {L"ISO B1 (1000.00 x 707.00 MM)"           , L"ISO_B1_(1000.00_x_707.00_MM)"           , 1000.00  , 707.00    , AcDbPlotSettings::kMillimeters} ,
    {L"ISO C5 (229.00 x 162.00 MM)"            , L"ISO_C5_(229.00_x_162.00_MM)"            , 229.00   , 162.00    , AcDbPlotSettings::kMillimeters} ,
    {L"700mm (700.00 x 1000.00 MM)"            , L"700mm_(700.00_x_1000.00_MM)"            , 700.00   , 1000.00   , AcDbPlotSettings::kMillimeters} ,
    {L"VGA (480.00 x 640.00 Pixels)"           , L"VGA_(480.00_x_640.00_Pixels)"           , 480.00   , 640.00    , AcDbPlotSettings::kPixels     } ,
    {L"XGA (768.00 x 1024.00 Pixels)"          , L"XGA_(768.00_x_1024.00_Pixels)"          , 768.00   , 1024.00   , AcDbPlotSettings::kPixels     } ,
    {L"Super VGA (600.00 x 800.00 Pixels)"     , L"Super_VGA_(600.00_x_800.00_Pixels)"     , 600.00   , 800.00    , AcDbPlotSettings::kPixels     } ,
    {L"XGA Hi-Res (1200.00 x 1600.00 Pixels)"  , L"XGA_Hi-Res_(1200.00_x_1600.00_Pixels)"  , 1200.00  , 1600.00   , AcDbPlotSettings::kPixels     } ,
    {L"Sun Hi-Res (1280.00 x 1600.00 Pixels)"  , L"Sun_Hi-Res_(1280.00_x_1600.00_Pixels)"  , 1280.00  , 1600.00   , AcDbPlotSettings::kPixels     } ,
    {L"Sun Standard (900.00 x 1152.00 Pixels)" , L"Sun_Standard_(900.00_x_1152.00_Pixels)" , 900.00   , 1152.00   , AcDbPlotSettings::kPixels     }
    };
static UInt32               s_numberOfPlotMedias = sizeof(s_standardPlotMediaList) / sizeof(StandardPlotMedia);

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StandardPlotScale        GetStandardPlotScale
(
const double&                           scale,
const AcDbPlotSettings::PlotPaperUnits& plotPaperUnits
)
    {
    int     startScale = AcDbPlotSettings::kInches == plotPaperUnits ? 15 : 32;
    int     endScale   = AcDbPlotSettings::kInches == plotPaperUnits ? 1  : 16;

    for (; startScale > endScale; startScale--)
        {
        if (fabs(scale - s_standardPlotScaleList[startScale].m_scaleFactor) <= TOLERANCE_ZeroScale)
            return  s_standardPlotScaleList[startScale];
        }

    DIAGNOSTIC_PRINTF ("No ACAD standard scale type is found for scale %g - setting it as a custom scale\n", scale);

    StandardPlotScale   customScale;
    customScale.m_scaleType         = 0xFF;
    customScale.m_scaleNumerator    = scale;
    customScale.m_scaleDenominator  = 1.0;
    customScale.m_scaleFactor       = scale;

    return  customScale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/07
+---------------+---------------+---------------+---------------+---------------+------*/
static const ACHAR*             GetCanonicalPlotMediaName
(
WStringCR                               formName,
double                                  widthInMM,
double                                  heightInMM,
const AcDbPlotSettings::PlotPaperUnits& units,
const AcDbPlotSettings*                 pPlotSet
)
    {
    double      width = widthInMM, height = heightInMM;

    if (AcDbPlotSettings::kInches == units)
        {
        width  /= 25.4;
        height /= 25.4;
        }

    UInt32      firstMatch = s_numberOfPlotMedias + 1;
    for (UInt32 i = 0; i < s_numberOfPlotMedias; i++)
        {
        // if the form name is a saved media name, no further check.
        if (0 == BeStringUtilities::Wcsicmp(formName.c_str(), s_standardPlotMediaList[i].m_localeName) ||
            0 == BeStringUtilities::Wcsicmp(formName.c_str(), s_standardPlotMediaList[i].m_canonicalName))
            return  s_standardPlotMediaList[i].m_canonicalName;

        // save the first size matching entry
        if (firstMatch > s_numberOfPlotMedias &&
            fabs(width  - s_standardPlotMediaList[i].m_plotWidth)  < TOLERANCE_MinimumLimits &&
            fabs(height - s_standardPlotMediaList[i].m_plotHeight) < TOLERANCE_MinimumLimits)
            firstMatch = i;
        }

    // no matching media name found, return the first size matching entry:
    if (firstMatch < s_numberOfPlotMedias)
        return  s_standardPlotMediaList[firstMatch].m_canonicalName;

    DIAGNOSTIC_PRINTF ("Did not find a matching canonical media name for %g X %g size.\n", width, height);
    return  s_standardPlotMediaList[0].m_canonicalName;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetPlotSettings
(
AcDbLayout*                             pLayout,
const ACHAR*                            plotName,
const ACHAR*                            paperSize,
const AcDbPlotSettings::PlotPaperUnits& plotPaperUnits,
const AcDbPlotSettings::PlotRotation&   plotRotation,
const AcGePoint2d&                      plotOrigin,
const double&                           plotScale,
const double&                           fullWidth,
const double&                           fullHeight,
const DPoint2d&                         minMargin,
const DPoint2d&                         maxMargin
)
    {
    AcDbPlotSettings*           plotSettings = AcDbPlotSettings::cast (pLayout);
    AcDbPlotSettings::PlotType  plotType = plotSettings->plotType ();
    Adesk::Boolean              isCentered = plotSettings->plotCentered ();

    RecordingFiler          filer (55);
    filer.RecordData (plotSettings);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("AcDbPlotSettings from DWGOUT filing:");
    // Upgrade RealDWG 2021
    // AcValue::DataType for index 4, 13, 35 changed from ACHAR to AcString
    /*-----------------------------------------------------------------------------------
    AcDbPlotSettings from DWGOUT filing:
     0: kDwgSoftPointerId, ffb04cc0 fe7becaa AcDbDictionary
     1: kDwgUInt32,    1 (0x1)
     2: kDwgSoftPointerId, ffb04cc0 fe7becaa AcDbDictionary
     3: kDwgHardOwnershipId, ffb04ce8 fe7bec82 AcDbDictionary
     4: kDwgString(AcString)                                    Name of active page setup
     5: kDwgString(ACHAR) none_device                           Printer/cfg name (2)
     6: kDwgUInt16,  688 (0x2b0)                                Plot layout flag (70)
     7: kDwgReal 6.350000                                       Left margin (40)
     8: kDwgReal 6.350000                                       Bottom margin (41)
     9: kDwgReal 6.350006                                       Right margin (42)
    10: kDwgReal 6.350006                                       Top margin (43)
    11: kDwgReal 215.899994                                     Paper width (44)
    12: kDwgReal 279.399994                                     Paper height (45)
    13: kDwgString(AcString) Letter_(8.50_x_11.00_Inches)       Paper size (4)
    14: kDwgReal 0.000000                                       Plot origin.x (46)
    15: kDwgReal 0.000000                                       Plot origin.y (47)
    16: kDwgInt16,    0 (0x0)                                   Plot paper units (72)
    17: kDwgInt16,    1 (0x1)                                   Plot rotation (73)
    18: kDwgInt16,    5 (0x5)                                   Plot type (74)
    19: kDwgReal 0.000000                                       Window area min.x (48)
    20: kDwgReal 0.000000                                       Window area min.y (49)
    21: kDwgReal 0.000000                                       Window area max.x (140)
    22: kDwgReal 0.000000                                       Window area max.y (141)
    23: kDwgHardPointerId, NULL                                 Plot view ID (6)
    24: kDwgReal 1.000000                                       Custom scale numerator: real world/paper units (142)
    25: kDwgReal 1.000000                                       Custom scale denominator: drawing units (143)
    26: kDwgString(ACHAR)                                       Current style sheet (7)
    27: kDwgInt16,   16 (0x10)                                  Standard scale type (75)
    28: kDwgReal 1.000000                                       Standard scale factor (147)
    29: kDwgReal 0.000000                                       Image origin.x (148)
    30: kDwgReal 0.000000                                       Image origin.y (149)
    31: kDwgInt16,    0 (0x0)                                   Shade plot mode (76)
    32: kDwgInt16,    2 (0x2)                                   Shade plot resolution (77)
    33: kDwgInt16,  300 (0x12c)                                 Shade plot DPI (78)
    34: kDwgSoftPointerId, NULL                                 Visual style ID
    35: kDwgString(AcString) Untitled Sheet                     Layout name
    36: kDwgSoftPointerId, ffb04cc8 fe7beca2 AcDbBlockTableRecord
    37: kDwgInt32,    1 (0x1)
    38: kDwgUInt16,    1 (0x1)
    39: kDwgSoftPointerId, NULL
    40: AcGePoint3d, 0.000000, 0.000000, 0.000000
    41: AcGePoint2d, -0.250000, -0.250000, 0.000000
    42: AcGePoint2d, 10.750000, 8.250000, 0.000000
    43: AcGePoint3d, 0.000000, 0.000000, 0.000000
    44: AcGeVector3d, 1.000000, 0.000000, 0.000000
    45: AcGeVector3d, 0.000000, 1.000000, 0.000000
    46: kDwgReal 0.000000
    47: kDwgHardPointerId, NULL
    48: kDwgHardPointerId, NULL
    49: kDwgInt16,    0 (0x0)
    50: AcGePoint3d, 1.049744, 0.799667, -0.001526
    51: AcGePoint3d, 9.450623, 7.200577, 0.000000
    52: kDwgInt32,    0 (0x0)
    -----------------------------------------------------------------------------------*/
#endif
    FilerDataList&      dataList = filer.GetDataList ();

    // Set active page setup name (DXF group 1).  This is the name under parenthesis of active model name.
    StringFilerData*     stringData = dynamic_cast <StringFilerData*> (dataList[4]);
    if (NULL == stringData)
        return BadDataSequence;
    stringData->SetValue (plotName);

    // Sheetdef currently does not have an auto-center option. Turn off the centered bit on flag 70.
    if (isCentered)
        {
        UInt16FilerData*    uint16Data = dynamic_cast <UInt16FilerData*> (dataList[6]);
        if (NULL == uint16Data)
            return BadDataSequence;

        Adesk::UInt16   flag70 = uint16Data->GetValue ();

        flag70 &= ~(1<<3);

        uint16Data->SetValue (flag70);
        }

    // Left margin (DXF group 40):
    DoubleFilerData*    doubleData = dynamic_cast <DoubleFilerData*> (dataList[7]);
    if (NULL == doubleData)
        return BadDataSequence;
    doubleData->SetValue (minMargin.x);

    // Bottom margin (DXF group 41):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[8])))
        return BadDataSequence;
    doubleData->SetValue (minMargin.y);

    // Right margin (DXF group 42):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[9])))
        return BadDataSequence;
    doubleData->SetValue (maxMargin.x);

    // Top margin (DXF group 43):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[10])))
        return BadDataSequence;
    doubleData->SetValue (maxMargin.y);

    // Paper width (DXF group 44):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[11])))
        return BadDataSequence;
    doubleData->SetValue (fullWidth);

    // Paper height (DXF group 45):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[12])))
        return BadDataSequence;
    doubleData->SetValue (fullHeight);

    // Paper size (DXF group 4):
    if (NULL == (stringData = dynamic_cast <StringFilerData*> (dataList[13])))
        return BadDataSequence;
    stringData->SetValue (paperSize);

    // Plot origin (DXF group 46):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[14])))
        return BadDataSequence;
    doubleData->SetValue (plotOrigin.x);

    // Plot origin (DXF group 47):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[15])))
        return BadDataSequence;
    doubleData->SetValue (plotOrigin.y);

    // Plot paper units (DXF group 72):
    Int16FilerData*     intData = dynamic_cast <Int16FilerData*> (dataList[16]);
    if (NULL == intData)
        return BadDataSequence;
    intData->SetValue (plotPaperUnits);

    // Plot rotation (DXF group 73):
    if (NULL == (intData = dynamic_cast <Int16FilerData*> (dataList[17])))
        return BadDataSequence;
    intData->SetValue (plotRotation);

    if (plotScale > TOLERANCE_ZeroScale)
        {
        StandardPlotScale   standardScale = GetStandardPlotScale (plotScale, plotPaperUnits);

        // Custom scale numerator/real world units (DXF group 142):
        if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[24])))
            return BadDataSequence;
        doubleData->SetValue (standardScale.m_scaleNumerator);

        // Custom scale denominator/drawing units (DXF group 143):
        if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[25])))
            return BadDataSequence;
        doubleData->SetValue (standardScale.m_scaleDenominator);

        // Standard scale type (DXF group 75):
        if (NULL == (intData = dynamic_cast <Int16FilerData*> (dataList[27])))
            return BadDataSequence;
        intData->SetValue (standardScale.m_scaleType);

        // Standard scale factor (DXF group 147):
        if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[28])))
            return BadDataSequence;
        doubleData->SetValue (standardScale.m_scaleFactor);
        }

    // Image origin.x (DXF group 148):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[29])))
        return BadDataSequence;
    doubleData->SetValue (0.0);

    // Image origin.y (DXF group 149):
    if (NULL == (doubleData = dynamic_cast <DoubleFilerData*> (dataList[30])))
        return BadDataSequence;
    doubleData->SetValue (0.0);

    Acad::ErrorStatus   es = filer.PlaybackData (plotSettings);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (40);
        check.RecordData (plotSettings);
        check.DumpList ("AcDbPlotSettings after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetLayoutFromSheetInfo (AcDbLayout* pLayout, DgnModelCP modelIn)
    {
    DPoint2d        currentOrigin, currentSize, currentMinMargin, currentMaxMargin;
    double          currentScale, currentRotation;
    UnitDefinition  currentUnits, units;
    RealDwgStatus   status;

    if (RealDwgSuccess == (status = this->CalculateSheetPropertiesFromLayout(currentOrigin, currentSize, currentScale, currentRotation, currentUnits, currentMinMargin, currentMaxMargin, pLayout)))
        {
        DgnModelCP      model = NULL == modelIn ? this->GetModel() : modelIn;
        ModelInfoCR     modelInfo = model->GetModelInfo ();
        SheetDefCP      pSheetDef = modelInfo.GetSheetDefCP ();

        if (NULL != pSheetDef)
            {
            DPoint2d    sheetSize, sheetOrigin, minMargin, maxMargin;

            pSheetDef->GetUnits (units);
            pSheetDef->GetSize (sheetSize.x, sheetSize.y);
            pSheetDef->GetOrigin (sheetOrigin);
            pSheetDef->GetMargins (minMargin.y, minMargin.x, maxMargin.y, maxMargin.x);

            if (sheetSize.IsEqual(DPoint2d::FromZero()))
                return  BadData;

            /*-------------------------------------------------------------------------------------------------------
            Below we will need to handle two scienarios for sheet scale and rotation:

                1) a typical DGN workflow which sets model annotation and sheet rotation.
                2) round trip DWG plot scale and drawing orientation(landscape/portrait modes).
            
            DWG plot scale and drawing orientation are saved as plot scale and paper rotation in SheetDef respectively.
            Both parameters are only used by print defintion and ignored by SheetDef::Draw.  A user can only edit model
            annoation scale and sheet rotation, but cannot change plot scale and paper rotation.  This allows us to
            swap sheet width and height back to DWG, i.e. a reverse process of CalculateSheetPropertiesFromLayout.
            The same reversal calculation applies to plot scale as well.
            -------------------------------------------------------------------------------------------------------*/
            double  sheetRotation = pSheetDef->GetRotation ();
            double  paperRotation = pSheetDef->GetDWGPaperOrientation ();
            double  rotation = sheetRotation + paperRotation;

            double  annotationScale = modelInfo.GetAnnotationScaleFactor ();
            double  plotScale = pSheetDef->GetPlotScaleFactor ();
            double  scale = plotScale * annotationScale;

            if (rotation != currentRotation || scale != currentScale ||
                !sheetSize.IsEqual(currentSize) ||
                !sheetOrigin.IsEqual(currentOrigin) ||
                !minMargin.IsEqual(currentMinMargin) ||
                !maxMargin.IsEqual(currentMaxMargin) ||
                !currentUnits.IsEqual(units))
                {
                double              dgnToMM = 1.0;
                UnitDefinition      mmUnits = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);
                mmUnits.ConvertDistanceFrom (dgnToMM, this->GetScaleFromDGN(), m_targetUnit);

                double              dgnToLayout = dgnToMM / plotScale;

                // DWG plot paper units only use MM or Inches.
                AcDbPlotSettings::PlotPaperUnits  plotPaperUnits;

                switch (units.IsStandardUnit())
                    {
                    case StandardUnit::EnglishMicroInches:
                    case StandardUnit::EnglishInches:
                    case StandardUnit::EnglishFeet:
                    case StandardUnit::EnglishSurveyFeet:
                    case StandardUnit::EnglishYards:
                    case StandardUnit::EnglishMiles:
                        plotPaperUnits = AcDbPlotSettings::kInches;
                        units = UnitDefinition::GetStandardUnit (StandardUnit::EnglishInches);
                        break;

                    default:
                        plotPaperUnits = AcDbPlotSettings::kMillimeters;
                        units = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);
                        break;
                    }

                /*-----------------------------------------------------------------------
                Get width & height now as they are needed to set media name, which is a
                prerequisite by plot settings validator to validate rest of the settings.
                -----------------------------------------------------------------------*/
                double      fullWidth  = (sheetSize.x + minMargin.x + maxMargin.x) * dgnToLayout;
                double      fullHeight = (sheetSize.y + minMargin.y + maxMargin.y) * dgnToLayout;

                const ACHAR*    canonicalName = L"Letter_(8.50_x_11.00_Inches)";
                WString         formName;
                pSheetDef->GetFormName (formName);
                if (!formName.empty())
                    canonicalName = GetCanonicalPlotMediaName (formName, fullWidth, fullHeight, plotPaperUnits, pLayout);
                else
                    pLayout->getCanonicalMediaName (canonicalName);

                DPoint2d    paperOffset;
                AcGePoint2d plotOrigin = AcGePoint2d::kOrigin;

                // Account for global origin.
                this->GetTransformFromDGN().Multiply (&sheetOrigin, &sheetOrigin, 1);
                sheetOrigin.Scale (this->GetScaleToDGN());

                paperOffset.Scale (sheetOrigin, -1.0 / scale);
                // EDL What direction is this rotation?
                paperOffset.RotateCCW (paperOffset, -sheetRotation);

                AcDbPlotSettings::PlotRotation  plotRotation = AcDbPlotSettings::k0degrees;

                // set plot orientation based on composite rotation(round tripping DWG rotation + DGN sheet rotation):
                double      angleQuadrant = bsiTrig_getPositiveNormalizedAngle(rotation) / msGeomConst_piOver2;
                switch (angleQuadrant > 3.5 ? 0 : DataConvert::RoundDoubleToLong(angleQuadrant))
                    {
                    case    0:
                        plotRotation = AcDbPlotSettings::k0degrees;
                        break;
                    case    1:
                        plotRotation = AcDbPlotSettings::k90degrees;
                        break;
                    case    2:
                        plotRotation = AcDbPlotSettings::k180degrees;
                        break;
                    case    3:
                        plotRotation = AcDbPlotSettings::k270degrees;
                        break;
                    }

                // set DWG plot origin based on DGN sheet rotation:
                sheetSize.Scale (1.0 / plotScale);
                angleQuadrant = bsiTrig_getPositiveNormalizedAngle(sheetRotation) / msGeomConst_piOver2;
                switch (angleQuadrant > 3.5 ? 0 : DataConvert::RoundDoubleToLong(angleQuadrant))
                    {
                    case    0:
                    default:
                        plotOrigin = RealDwgUtil::GePoint2dFromDPoint2d(paperOffset);
                        break;
                    case    1:
                        plotOrigin.x = paperOffset.x;
                        plotOrigin.y = sheetSize.y - paperOffset.y;
                        break;
                    case    2:
                        plotOrigin.x = sheetSize.x - paperOffset.x;
                        plotOrigin.y = sheetSize.y - paperOffset.y;
                        break;
                    case    3:
                        plotOrigin.x = sheetSize.x - paperOffset.x;
                        plotOrigin.y = paperOffset.y;
                        break;
                    }

                minMargin.Scale (dgnToLayout);
                maxMargin.Scale (dgnToLayout);
                plotOrigin.scaleBy (dgnToMM);

                // Get current scale - modify numerator/denominator only if it has changed.
                double          sheetToPaper, newScale;
                units.GetConversionFactorFrom (sheetToPaper, m_targetUnit);

                if (pLayout->useStandardScale())
                    {
                    pLayout->getStdScale (currentScale);
                    }
                else
                    {
                    double          drawingUnitsNumerator, drawingUnitsDenominator;

                    pLayout->getCustomPrintScale (drawingUnitsNumerator, drawingUnitsDenominator);
                    currentScale = drawingUnitsNumerator / drawingUnitsDenominator;
                    }

                newScale = sheetToPaper / scale;
                if (fabs (currentScale - newScale) <= 1.0E-6)
                    newScale = 0.0;

                // Use sheet name as page setup name:
                WString         sheetName;
                pSheetDef->GetSheetName (sheetName);

                // swap width and height to round trip plot orientation
                angleQuadrant = bsiTrig_getPositiveNormalizedAngle(paperRotation) / msGeomConst_piOver2;
                switch (angleQuadrant > 3.5 ? 0 : DataConvert::RoundDoubleToLong(angleQuadrant))
                    {
                    case 1:     // 90-degree
                    case 3:     // 270-degrees
                        {
                        double      w = fullWidth;
                        fullWidth = fullHeight;
                        fullHeight = w;
                        plotOrigin.set (plotOrigin.y, plotOrigin.x);
                        minMargin.Init (minMargin.y, minMargin.x);
                        maxMargin.Init (maxMargin.y, maxMargin.x);
                        break;
                        }
                    }

                status = SetPlotSettings (pLayout, sheetName.c_str(), canonicalName, plotPaperUnits, plotRotation, plotOrigin, newScale, fullWidth, fullHeight, minMargin, maxMargin);
                if (RealDwgSuccess != status)
                    {
                    DIAGNOSTIC_PRINTF ("Error settings plot settings for sheet <%ls>\n", sheetName.c_str());
                    return  status;
                    }

                UpdatePlotSettingsObjectInDictionary (sheetName.c_str(), pLayout);
                }
            }
        }

    // handle minimum paper limits:
    AcGePoint2d     limmin, limmax;
    pLayout->getLimits (limmin, limmax);

    bool        resetLimmax = false;

    if (fabs(limmax.x - limmin.x) < TOLERANCE_MinimumLimits)
        {
        limmax.x += TOLERANCE_MinimumLimits;
        resetLimmax = true;
        }

    if (fabs(limmax.y - limmin.y) < TOLERANCE_MinimumLimits)
        {
        limmax.y += TOLERANCE_MinimumLimits;
        resetLimmax = true;
        }

    if (resetLimmax)
        {
        // reset LIMMAX for layout
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetCannoscaleFromModelInfo (ModelInfoCR modelInfo, AcDbDatabase* dwg)
    {
    RealDwgStatus           status = CantSetAnnotationScale;
    AcDbDatabase*           database = nullptr == dwg ? this->GetDatabase() : dwg;
    double                  scaleFactor = modelInfo.GetAnnotationScaleFactor ();
    if (NULL == database || fabs(scaleFactor) < TOLERANCE_AnnotationScale)
        return  status;

    ScaleDefinitionCP       scaleDef = m_annotationScaleCollection.FindByFactor (scaleFactor);
    AcDbAnnotationScale*    cannoscale = database->cannoscale ();

    // chances that current CANNOSCALE matches input model scale
    if (NULL != cannoscale)
        {
        double              dwgScaleFactor = 0.0;
        if (Acad::eOk == cannoscale->getScale(dwgScaleFactor) && fabs(dwgScaleFactor - 1.0 / scaleFactor) < TOLERANCE_AnnotationScale)
            {
            // CANNOSCALE matches model scale - will not reset it
            status = RealDwgSuccess;
            }
        else
            {
            // CANNOSCALE does not match model scale - will try resetting it at next step
            delete cannoscale;
            cannoscale = NULL;
            }
        }

    if (NULL == cannoscale && RealDwgSuccess == this->GetOrAddAnnotationScale(cannoscale, scaleDef, m_savingChanges, dwg))
        {
        if (Acad::eOk == database->setCannoscale(cannoscale))
            status = RealDwgSuccess;
        }

    // now we can delete cannoscale
    if (NULL != cannoscale)
        delete cannoscale;

    // save model scale to be used for annotative object creation
    if (NULL != scaleDef && NULL == m_modelScaleDefinition)
        m_modelScaleDefinition = new ScaleDefinition (scaleDef->GetName(), scaleDef->GetPreScale(), scaleDef->GetPostScale(),scaleDef->GetUnitSystemMask());

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveModelInfoToDatabase (bool changesOnly)
    {
    ModelInfoCR modelInfo = m_model->GetModelInfo();

    // Set the layout name for sheet models.
    double                  scaleToDGN;
    AcDbObjectId            layoutId;
    Transform               transformToDGN, transformFromDGN;

    DwgOpenUnitMode         translatedUnitMode = GetDwgOpenUnitModeFromSaveUnitMode (m_targetUnitMode);
    this->GetTransformToDGNFromModelInfo (&transformToDGN, &scaleToDGN, NULL, modelInfo, m_model, translatedUnitMode);
    transformFromDGN.inverseOf (&transformToDGN);

    switch (m_pModelIndexItem->GetRealDwgModelType())
        {
        case RDWGMODEL_TYPE_Sheet:
            {
            AcDbBlockTableRecordPointer pBlock (m_currentBlockId, AcDb::kForRead);
            if (! (layoutId = pBlock->getLayoutId()).isNull())
                {
                WChar     modelName[MAX_MODEL_DISPLAY_LENGTH];
                WCharCP   nameP = modelInfo.GetName();
                if ( (NULL == nameP) || (0 == *nameP) )
                    wcscpy (modelName, L"Untitled");
                else
                    wcscpy (modelName, nameP);
                ConvertFromDgnContext::ValidateName (modelName, DwgFileVersion_13);

                AcDbLayoutPointer   pLayout (layoutId, AcDb::kForWrite);
                bool                duplicateNameExists = false;
                int                 duplicateNameIndex = 0;

                AcString        name = modelName;
                do
                    {
                    Acad::ErrorStatus status;
                    if (Acad::eOk == (status = pLayout->setLayoutName (name)))
                        break;

                    if (false != (duplicateNameExists = (Acad::eRenameLayoutAlreadyExists == status)) )
                        {
                        char    suffix[1024];
                        sprintf (suffix, "_%d", ++duplicateNameIndex);
                        DIAGNOSTIC_PRINTF ("Duplicate Model Name: %ls -- adding suffix: %hs\n", name.kwszPtr(), suffix);
                        name += AcString(suffix);
                        }
                    } while (duplicateNameExists);


                this->SetLayoutFromSheetInfo (pLayout);
                }

            break;
            }

        case RDWGMODEL_TYPE_DefaultModel:
            DPoint3d        insertBase;

            if (mdlModelRef_isTargetFormatDwgOrDxf (m_model))
                transformFromDGN.multiply (&insertBase, &modelInfo.m_insertBase, 1);
            else
                {
                RotMatrix rotMatrix;
                transformFromDGN.getMatrix (&rotMatrix);
                rotMatrix.multiply (&insertBase, &modelInfo.m_insertBase, 1);
                }

            AcDbDatabase*   database = m_pFileHolder->GetDatabase ();
            if (NULL != database)
                {
                database->setInsbase (RealDwgUtil::GePoint3dFromDPoint3d(insertBase));
                database->setAnnotativeDwg (modelInfo.GetIsAnnotationCell());

                // set CANNOSCALE
                if (m_savingChanges)
                    this->SetCannoscaleFromModelInfo (modelInfo);

                // set MSLTSCALE: 0 = use LTSCALE, 1 = compound annotation scale.  New in R2008.
                database->setMsltscale (ModelInfo::LSSCALEMODE_CompoundScale == modelInfo.GetLineStyleScaleMode());
                }
            break;
        }

    // Each AutoCAD view contains information that we store on a per-model basis such as grid and UCS definitions.
    // We don't want to set these unless they have been changed on the MicroStation side to avoid destroying settings
    // that vary per view - so when we are saving changes we impose the model settings only when the values have
    // changes in MicroStation. - therefore the view groups must be checked in before the model information so we can
    // detect changed values correctly.
    if (RDWGMODEL_TYPE_NonDefaultModel != m_pModelIndexItem->GetRealDwgModelType())
        {
        try
            {
            this->SaveViewGroupToDwg (m_pFileHolder->GetDatabase(), modelInfo, false, changesOnly);
            }
        catch (...)
            {
            DIAGNOSTIC_PRINTF ("Exception caught checking in view group\n");
            }
        }

    m_pModelIndexItem->SetModelInfo (&modelInfo);

    MSElement   element;
    if (SUCCESS == dgnModel_getLinkageHolderElement (m_model, &element))
        {
        int                     nFrozenLayers;
        ElementId               frozenLayers[MAX_FrozenLayers];
        ViewportFreezeMode      vpFreeze = this->GetSettings().GetViewportFreezeMode ();

        if (!SavingChanges() &&
            m_pModelIndexItem->GetRealDwgModelType() == RDWGMODEL_TYPE_Sheet &&
            (VPFreeze_ViewportsOnly == vpFreeze || VPFreeze_ThawGlobalLayers == vpFreeze))
            {
            // TR# 197439 -  If we are saving from DGN to DWG AND setting the level mask for the viewports
            // Then get the Frozen Layers from the viewport.
            nFrozenLayers = this->GetFrozenLayersFromViewportModelRef (frozenLayers, m_model);
            }
        else
            {
            nFrozenLayers = mdlLinkage_getElementIds (frozenLayers, MAX_FrozenLayers, &element, DEPENDENCYAPPID_MicroStation, DEPENDENCYAPPVALUE_ReferenceFrozenLevel);
            }
        m_pModelIndexItem->SetFrozenLayers (frozenLayers, nFrozenLayers, *this);

        // Extract XData from linkage holder element.
        AcDbBlockTableRecordPointer         pBlock (m_pModelIndexItem->GetBlockTableRecordId(), AcDb::kForWrite);
        if (Acad::eOk == pBlock.openStatus())
            this->UpdateEntityXDataFromLinkagesAndGraphicGroup (pBlock, &element);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SetModelLimits ()
    {
#ifndef BUILD_DWGPLATFORM
    DVector3d           range;
    AcDbDatabase*       pDatabase = m_pFileHolder->GetDatabase();

    if (SUCCESS == mdlModelRef_getRange (m_model, (DRange3dP) &range, -1, NULL, true))
        {
        this->GetTransformFromDGN().Multiply (&range.org, &range.org, 2);
        double          diagonal = range.org.distance (&range.end);
        DPoint3d        center;
        AcGePoint2d     limMin, limMax;

        center.sumOf (NULL, &range.org, .5, &range.end, .5);
        limMin.x = center.x - LIMIT_Ratio * diagonal;
        limMin.y = center.y - LIMIT_Ratio * diagonal;
        limMax.x = center.x + LIMIT_Ratio * diagonal;
        limMax.y = center.y + LIMIT_Ratio * diagonal;

        switch (this->GetModelIndexItem()->GetRealDwgModelType())
            {
            case RDWGMODEL_TYPE_DefaultModel:
                pDatabase->setExtmin (RealDwgUtil::GePoint3dFromDPoint3d (range.org));
                pDatabase->setExtmax (RealDwgUtil::GePoint3dFromDPoint3d (range.end));
                pDatabase->setLimmin (limMin);
                pDatabase->setLimmax (limMax);
                break;

            case RDWGMODEL_TYPE_Sheet:
                pDatabase->setPextmin (RealDwgUtil::GePoint3dFromDPoint3d (range.org));
                pDatabase->setPextmax (RealDwgUtil::GePoint3dFromDPoint3d (range.end));
                pDatabase->setPlimmin (limMin);
                pDatabase->setPlimmax (limMax);
                break;
            }
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::DeleteSymbolTableRecord (AcDbObjectId objectId)
    {
    // If we're going to delete a table record change its name so that a Delete/Add edit will work (TR# 171850).
    AcDbSymbolTableRecordPointer<AcDbSymbolTableRecord> tableRecord (objectId, AcDb::kForWrite);
    if (Acad::eOk == tableRecord.openStatus())
        {
        const ACHAR*    name;
        if (Acad::eOk != tableRecord->getName (name))
            name = L"";
        tableRecord->setName (AcString(NAME_TableRecordErasePrefix) + name);
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Failed deleting symbol table record ID=%I64d! [%ls]\n", this->ElementIdFromObjectId(objectId), acadErrorStatusText(tableRecord.openStatus()));
        }

    m_pFileHolder->ScheduleTableRecordErase (objectId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::DeleteSymbolTableRecords (SignedTableIndex* tableIndex, AvlTreeP currentIdTree)
    {
    /*--------------------------------------------------------------------------------------
    Delete DWG table records if their DGN counterparts are deleted.  The input currentIdTree
    contains the list of DGN table entry element ID's.  If an ID originated in DWG no longer
    exists in this list, delete it from DWG and also update input tableIndex to track the
    table records.
    --------------------------------------------------------------------------------------*/
    if (NULL == tableIndex || NULL == currentIdTree)
        return;

    ElementIdArrayR     originatedInDwgList = tableIndex->GetOriginatedInDwgList ();

    for each (ElementId elementId in originatedInDwgList)
        {
        if (NULL == mdlAvlTree_search(currentIdTree, &elementId))
            {
            // the originated DWG ID does not exist in currentIdTree, delete it from DWG.
            AcDbObjectId    objectId = this->ExistingObjectIdFromElementId (elementId);
            if (!objectId.isNull())
                {
                this->DeleteSymbolTableRecord (objectId);
                tableIndex->RemoveEntry (objectId.handle());
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectP                 ConvertFromDgnContext::ReuseStandardStyle
(
const AcString&             styleName,
const AcDbObjectId&         tableId
)
    {
    /*-----------------------------------------------------------------------------------
    Since RealDWG does not allow us to delete "Standard" text or dimension style from
    seed file, we will reuse existing styles and override them with our own data.
    -----------------------------------------------------------------------------------*/
    AcDbSymbolTableRecord*  standardStyle = NULL;

    if (0 == styleName.compareNoCase(NAME_Standard))
        {
        AcDbSymbolTablePointer<AcDbSymbolTable>     pTable (tableId, AcDb::kForRead);
        if (Acad::eOk == pTable.openStatus())
            {
            if (Acad::eOk != pTable->getAt(styleName, standardStyle, AcDb::kForWrite))
                DIAGNOSTIC_PRINTF ("Failed to override existing Standard style.\n");
            }
        }

    return  standardStyle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveDictionaryModel()
    {
    DgnFileP    dgnFile = m_model->GetDgnFileP ();
    DgnModelP   defaultModel = dgnFile->FindLoadedModelById (dgnFile->GetDefaultModelId());
    UInt32      runningCount = 0;

    m_pFileHolder->GetDgnSymbologyData()->ReadColorTable (m_model);

    // Text styles have to precede everything else, they are required for dimension styles.
    this->SaveDgnTextStylesToDatabase ();

    // Linetypes must procede to layers, but after text styles.
    this->SaveDgnLineStylesToDatabase ();

    // Save the References and Layers .
    this->SaveXRefsAndLayersToDatabase (dgnFile);
    this->SaveLayerFiltersToDatabase ();

    // Save the dimension styles.
    this->SaveDgnDimensionStylesToDatabase ();
    // Save mutiline styles.
    this->SaveDgnMultilineStylesToDatabase ();

    // Save the named views to.
    this->SaveDgnNamedViewsToDatabase ();
    this->SaveRegisteredApplicationsToDatabase ();
    this->SaveDgnMaterialsToDatabase ();

    // we have to save which layers are locked and unlock them. We'll relock them when we're done.
    m_pFileHolder->RecordLockedLayers ();

    // delete unused underlay definitions
    this->DeleteUnusedUnderlayDefinitions ();

    // save other nonGraphic elemements in dictionary model such as named groups, dgn stores, etc
    this->SaveDictionaryElementsToDatabase (runningCount);
    // before saving shared cell def's, set CANNOSCALE to get it added as a supported scale in the blocks
    if (NULL != defaultModel)
        this->SetCannoscaleFromModelInfo (defaultModel->GetModelInfo());
    // Shared Cells.
    this->SaveElementListToDatabase (m_model, false, CHECKIN_MASK_Graphic, runningCount);

    this->_OnPostSavePsdEntitiesToDatabase (m_model, false,  CHECKIN_MASK_Graphic, runningCount);

    this->PostSaveToDatabase (false);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveModelCache ()
    {
    bool                isSheet         = (DgnModelType::Sheet == m_model->GetModelType());
    bool                isDefaultModel  = !isSheet && m_modelIdForModelSpace == m_model->GetModelId();

    if ( (NonDefaultModels_Ignore == GetSettings().GetNonDefaultModelMode ()) && (m_modelId >= 0) && !isDefaultModel && !isSheet)
        return SUCCESS;

    m_pFileHolder->GetDgnSymbologyData()->ReadColorTable (m_model);

    UInt32  runningCount = 0;

    this->SaveModelInfoToDatabase (false);
    this->SetModelLimits();

    // get the modelRef list of current model, including ref attatachments and this model, in correct display order:
    ModelRefIteratorP           mrIterator = new ModelRefIterator (m_model, MRITERATE_DisplayOrder, 0);
    if (nullptr != mrIterator)
        {
        /*--------------------------------------------------------------------------------------------------------
        We want to sort all graphical elements in the model, including reference attachments and viewports in the
        paperspace.  Otherwise if DWG xRef's and viewports are not in the SortEnts list, they are placed after the 
        end of SortEnts, effectively always displayed at last, as in TFS 7609 and 115006.

        Since reference file update sequence is independent to element priority, to reassemble this MicroStation's
        display effect in DWG's SortEnts requires us to handle 3 possible scenarios of the update sequence:

            1) All ref's are all in front of the active model.
            2) All ref's are placed after the active model.
            3) The active model is in between any two ref files.

        To achieve that gaol we create an artificial display priority for each and every one of the attachment 
        elements: those displayed before the active model are assigned with the lowest priorities and those after 
        the active model get the highest.  Using the lowest & highest possible priority values for ref attachment 
        elements separates separates their priority range from that of the active model, regardless to how model
        elements are sorted (by priority or file position etc). As a result, the final SortEnts will have ref 
        attachments, viewports and graphical elements sorted correctly in accordance to aforementioned 3 scenarios.
        --------------------------------------------------------------------------------------------------------*/
        PrioritySorter*         pPrioritySorter = m_currentBlockId.isNull() ? nullptr : new PrioritySorter();
        // will assign the lowest priorities to ref attachments before the active model:
        Int32                   refPriority = INT_MIN;

        // count number of modelRef's to be saved to DWG:
        DgnModelRefP            pIterateRef;
        UInt32                  count = 0;
        while (nullptr != (pIterateRef = mrIterator->GetNext()))
            count++;

        if (count < 1)
            {
            delete pPrioritySorter;
            delete mrIterator;
            return  BSIERROR;
            }

        // reset the modelRef iterator and iterate them over again - this time actually processing them:
        pIterateRef = mrIterator->GetFirst ();

        do
            {
            DgnModelP       dgnCache = m_model->GetDgnModelP();
            if (pIterateRef == m_model)
                {
                this->SaveElementListToDatabase (dgnCache, false, CHECKIN_MASK_Graphic | CHECKIN_MASK_NonGraphic, runningCount);
                this->SaveElementListToDatabase (dgnCache, true,  CHECKIN_MASK_Graphic | CHECKIN_MASK_NonGraphic, runningCount, pPrioritySorter);

                // will assign the highest priorities to all ref attachments after the active model
                refPriority = INT_MAX - count - 2;
                }
            else
                {
                DgnAttachmentP  refP = pIterateRef->AsDgnAttachmentP ();
                ElementRefP     elemRef;

                if ( NULL != refP && NULL != (elemRef = dgnCache->FindByElementId (refP->GetElementId())))
                    {
                    ElementHandle elemHandle (elemRef, m_model);
                    this->SaveElementToDatabase (elemHandle);

                    // assign and increase priority for each and everyone of the ref attachments:
                    if (nullptr != pPrioritySorter)
                        pPrioritySorter->AddElement (elemHandle.GetElementId(), refPriority++, 0, 0);
                    }
                }
            } while (nullptr != (pIterateRef = mrIterator->GetNext()));

        this->_OnPostSavePsdEntitiesToDatabase (m_model->GetDgnModelP(), true,  CHECKIN_MASK_Graphic | CHECKIN_MASK_NonGraphic, runningCount);

        // sort all elements including refs & viewports and save the list to SortEnts:
        if (nullptr != pPrioritySorter)
            {
            pPrioritySorter->SetSortEnts (m_pFileHolder->GetDatabase(), m_currentBlockId, *this);
            delete pPrioritySorter;
            }

        delete mrIterator;
        }
    try
        {
        this->SortRasterReferences (m_model->IsRasterRefsLast());
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught sorting raster references\n");
        }

    this->PostSaveToDatabase (false);
    this->ReportProgress (1.1);       // Close completion bar.

    /*-----------------------------------------------------------------------------------
    By now DWG database is complete but we might have created more entities than DGN cache
    elements.  Feature Modeler may dirty the cache by creating new elements after this
    step. These new element ID's may not be unique to the extra entities we have now saved.
    As a result, existingObjectIdFromElementId will pick up a wrong entity and reset it
    with wrong data, hence to corrupt DWG. One way around that is to synch DgnFileObj's
    highest ID with DWG's handseed to prevent future dirty elements using dup ID's.
    -----------------------------------------------------------------------------------*/
    m_pFileHolder->SyncHighestDgnElementIdFromHandseed ();

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/09
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      ConvertFromDgnContext::PurgeTableObjects
(
AcDbObjectIdArray&          idsToBePurged
)
    {
    /*-----------------------------------------------------------------------------------
    Often times RealDWG cannot purge all table entries in a single round.  It may require
    multiple times of purging each of which clears up hard references for the next purge.
    This is a same behavior in AutoCAD, except it requires manual operations instead of
    automatic purge.
    -----------------------------------------------------------------------------------*/
    UInt32                  numPurged = 0;

    do
        {
        AcDbObjectIdArray   idsCanBePurged = idsToBePurged;

        // Get object ID's that RealDWG determines can be purged, then delete each of them individually.
        if (Acad::eOk != m_pFileHolder->GetDatabase()->purge(idsCanBePurged) || idsCanBePurged.isEmpty())
            break;

        for (int index = 0; index < idsCanBePurged.length(); index++)
            {
            AcDbObjectPointer<AcDbObject>   pObject (idsCanBePurged[index], AcDb::kForWrite);

            if (Acad::eOk == pObject.openStatus())
                {
                pObject->erase ();
                numPurged++;
                }

            // Take out this object from the source array for next purge trial.
            idsToBePurged.remove (idsCanBePurged[index]);
            }
        } while (!idsToBePurged.isEmpty());

    return  numPurged;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveModelChanges (DgnModel::ElementRefIterator* iterator)
    {
    if (m_modelId < 0)
        this->SaveModelChanges (iterator, true);

    return  this->SaveModelChanges (iterator, false);
    }

/*---------------------------------------------------------------------------------**//**
* Save Changes from cache
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveModelChanges
(
DgnModel::ElementRefIterator* iterator,
bool                        textStyles
)
    {
    bool                    changeFound             = false;
    bool                    updateSequenceChanged   = false;
    AvlTree*                pDeletedIdTree          = mdlAvlTree_init (AVLKEY_UINT64);
    bool                    sequenceChangeRequired  = false;
    AcDbObjectId            currentBlockId;
    AcDbObjectIdArray       topIds;

    m_savingChanges = true;
    m_useLevelSymbologyOverrides = false;       // Dont use level symbology if saving changes from DWG.
    if (m_modelId >= 0)
        {
        this->SaveModelInfoToDatabase (true);
        currentBlockId = m_currentBlockId;
        }

    AcDbObjectIdArray       idsToPurge;
    PersistentElementRefP   elemRef;
    for (elemRef = iterator->GetFirstDirtyElementRef(); NULL != elemRef; elemRef = iterator->GetNextDirtyElementRef())
        {
        try
            {
            changeFound = true;

            if (elemRef->IsDeleted())
                {
                /*-----------------------------------------------------------------------
                Don't try to delete the same element twice. For the same Dictionary Model,
                we get called twice, once for m_modelId==-1 with textstyle==true,
                another for modelId>=0 with textstyle==false (see above calling method).
                We should delete dictionary model elements only once.  While table entry
                deletion occurs in later process, named groups get deleted here.
                -----------------------------------------------------------------------*/
                if (m_modelId < 0 && !textStyles)
                    continue;

                // Dont bother trying to delete elements that are new in this session
                // if (elemRef->isNew()) continue; not an exported method!

                // Also test that the element still exists to avoid deleting elements
                // that were later re-added.
                ElementId   elementId    = elemRef->GetElementId ();
                this->DeleteElementObject (elementId, idsToPurge);
                }
            else
                {
                Elm_hdr const*          ehdr;

                // If the element was either deleted or made part of a complex, delete it.
                if (NULL == (ehdr = elemRef->GetElementHeaderCP()))
                    continue;

                if (textStyles == ((MS_TEXTSTYLE_TABLE_LEVEL == ehdr->level) && (TABLE_ELM == ehdr->type)) )
                    {
                    AcDbObjectId            objectId;
                    ElementHandle           elemHandle (elemRef, m_model);
                    this->SaveElementToDatabase (elemHandle, &objectId);
                    if ( (m_modelId >= 0) && (elemRef->IsNew() && ehdr->isGraphics && !objectId.isNull() && !objectId.isErased()) )
                        {
                        ElementId   elementId = ehdr->uniqueId;
                        topIds.append (objectId);
                        if (!sequenceChangeRequired && NULL != mdlAvlTree_search (pDeletedIdTree, &elementId))
                            sequenceChangeRequired = true;
                        }
                    }
                updateSequenceChanged |= (!textStyles && (MICROSTATION_ELM == ehdr->type) && (MSUPDATESEQUENCE_LEVEL == ehdr->level) );
                }
            }
        catch (...)
            {
            DIAGNOSTIC_PRINTF ("Exception caught checking in cache change, ElementId: %I64d \n");
            }
        }

    if (!idsToPurge.isEmpty())
        this->PurgeTableObjects (idsToPurge);

    if (sequenceChangeRequired)
        {
        AcDbSortentsTable*              pSortentsTable;
        AcDbBlockTableRecordPointer     pCurrentBlock (m_currentBlockId, AcDb::kForRead);
        if (Acad::eOk == pCurrentBlock->getSortentsTable (pSortentsTable, AcDb::kForWrite, true))
            {
            pSortentsTable->moveToTop (topIds);
            pSortentsTable->close();
            }
        }

    mdlAvlTree_free (&pDeletedIdTree, NULLFUNC, NULL);

    // do not post process twice as not only it is a waste but also causes problems(e.g. INUNITS incorrectly overridden at the 2nd run).
    if (!textStyles)
        {
        this->PostSaveToDatabase (true);
        if (m_modelId >= 0)
            {
            this->PurgeMicroStationDictionary ();
            this->SaveItemTypeTargets ();
            }
        }

    if (m_modelId >= 0)
        {
        if (updateSequenceChanged)
            this->SaveUpdateSequenceToDatabase ();

        this->SaveRasterReferenceSequenceToDatabase();
        }

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveUpdateSequenceToDatabase ()
    {
    AcDbBlockTableRecordPointer pCurrentBlock (m_currentBlockId, AcDb::kForRead);
    if (Acad::eOk != pCurrentBlock.openStatus())
        {
        BeAssert (false && L"Cannot open current block record for update sequence!");
        return;
        }

    AcDbSortentsTable*          pSortEntsTable;
    if (Acad::eOk != pCurrentBlock->getSortentsTable (pSortEntsTable, AcDb::kForWrite, true))
        {
        DIAGNOSTIC_PRINTF ("UpdateSequence: Failed getSortentsTable\n");
        return;
        }

    // We got here because we encountered a MicroStation Update Sequence element among the changed elements.
    // All we want to do is to move the position of the AcDbEntities that correspond to references (AcDbBlockReference or AcDbViewport)
    //   to be right relative to each other, and right relative to all the other entities in the SortEnts.
    DgnModelRefList     updateSequence;
    m_model->FillUpdateList (updateSequence);

    bool                beforeMasterFile    = true;
    AcDbObjectId        previousRefObjectId;
    for (size_t iModel=0, modelCount = updateSequence.size(); iModel < modelCount; iModel++)
        {
        DgnModelRefP    modelRef = updateSequence.at (iModel);
        DgnAttachmentP  refP;

        // all we use the masterModelRef for is to switch from putting the references before to putting them after.
        if (modelRef == m_model)
            beforeMasterFile = false;
        else if ( NULL != ( refP = modelRef->AsDgnAttachmentP() ) )
            {
            AcDbObjectId    referenceObjectId;
            if ((referenceObjectId = this->ExistingObjectIdFromElementId (refP->GetElementId())).isNull())
                continue;

            AcDbObjectIdArray tmpArray;
            tmpArray.append (referenceObjectId);
            if (beforeMasterFile)
                {
                if (previousRefObjectId.isNull())
                    pSortEntsTable->moveToBottom (tmpArray);
                else
                    pSortEntsTable->moveAbove (tmpArray, previousRefObjectId);
                }
            else
                {
                pSortEntsTable->moveToTop (tmpArray);
                }
            previousRefObjectId = referenceObjectId;
            }
        }

    pSortEntsTable->close();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveRasterReferenceSequenceToDatabase ()
    {
    //Fix TR #190990: We want to sort raster reference only if the MS_RASTER_DWG_UPDATESEQUENCE_MODE env. var. has been set.
    //Otherwise, keep order from file
    WString buffer;
    int     value;
    if ( (SUCCESS == ConfigurationManager::GetVariable (buffer, L"MS_RASTER_DWG_UPDATESEQUENCE_MODE")) && (0 != (value = BeStringUtilities::Wtoi (buffer.c_str()))) )
        {
        // 0 Use default
        // 1 Rasters should always be displayed under vectors
        // 2 Rasters should always be displayed over vectors
        bool    rasterRefsLast = (value == 2);
        this->SortRasterReferences (rasterRefsLast);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SortRasterReferences (bool rasterRefsLast)
    {
    DgnPlatform::Raster::DgnRasterVector     nRasterMap;
    DgnPlatform::Raster::DgnRasterCollection::QueryRastersOrderedList (nRasterMap, m_model, MRITERATE_Root, -1);

    if (0 != nRasterMap.size())
        {
        ElementId                   elementId;
        AcDbObjectId                objectId;
        AcDbSortentsTable*          pSortEntsTable;
        AcDbBlockTableRecordPointer pCurrentBlock (m_currentBlockId, AcDb::kForRead);
        if (Acad::eOk != pCurrentBlock->getSortentsTable (pSortEntsTable, AcDb::kForWrite, true))
            {
            DIAGNOSTIC_PRINTF ("SortRasterReferences: Failed getSortentsTable\n");
            return;
            }

        if (rasterRefsLast)
            {
            AcDbObjectIdArray   moveArray;
            for (bvector<DgnRasterP>::iterator nRasterMapItor = nRasterMap.begin(); nRasterMapItor!=nRasterMap.end(); nRasterMapItor++)
                {
                DgnRasterP  rasterP = *nRasterMapItor;
                elementId = rasterP->GetElementRef() ?  rasterP->GetElementRef()->GetElementId() : 0;
                if (! (objectId = this->ExistingObjectIdFromElementId (elementId)).isNull())
                    {
                    moveArray.append (objectId);
                    }
                }
            if (moveArray.length() > 0)
                {
                if (Acad::eOk != pSortEntsTable->moveToTop (moveArray))
                    DIAGNOSTIC_PRINTF ("SortRasterReferences:: moveToTop failed\n");
                }
            }
        else
            {
            AcDbObjectIdArray   moveArray;
            for (bvector<DgnRasterP>::reverse_iterator nRasterMapItor = nRasterMap.rbegin(); nRasterMapItor!=nRasterMap.rend(); nRasterMapItor++)
                {
                DgnRasterP  rasterP = *nRasterMapItor;
                elementId = rasterP->GetElementRef() ?  rasterP->GetElementRef()->GetElementId() : 0;
                if (! (objectId = this->ExistingObjectIdFromElementId (elementId)).isNull())
                    {
                    moveArray.append (objectId);
                    }
                }
            if (moveArray.length() > 0)
                {
                pCurrentBlock.close ();
                if (Acad::eOk != pSortEntsTable->moveToBottom(moveArray))
                    DIAGNOSTIC_PRINTF ("SortRasterReferences:: moveToTop failed\n");
                }
            }

        pSortEntsTable->close();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/09
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId     FindFirstRasterImageId
(
AcDbRasterImageDef*     imageDef
)
    {
    const AcDbVoidPtrArray* reactors = imageDef->reactors ();
    if (NULL == reactors)
        return  AcDbObjectId::kNull;

    int                 numberReactors = reactors->length ();
    for (int i = 0; i < numberReactors; i++)
        {
        void *pSomething = reactors->at (i);
        if (acdbIsPersistentReactor (pSomething))
            {
            AcDbObjectId    persistentReactorId = acdbPersistentReactorObjectId (pSomething);
            AcDbObjectPointer<AcDbObject>   pPersistentObject (persistentReactorId, AcDb::kForRead);
            if (Acad::eOk == pPersistentObject.openStatus() && pPersistentObject->isKindOf(AcDbRasterImageDefReactor::desc()))
                {
                return  pPersistentObject->ownerId ();
                }
            }
        }

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                   Marc.Bedard  10/2009
+---------------+---------------+---------------+---------------+---------------+------*/
bool  ConvertFromDgnContext::ShouldLoadImageDef(AcDbRasterImageDef* pImageDef, DgnRasterP& rasterP)
    {
    bool shouldLoad(true);

    rasterP=NULL;
    ElementId  rasterFrameID = this->ElementIdFromObjectId (FindFirstRasterImageId(pImageDef));
    ElementRefP rasterFrameElmRef(m_model->FindByElementId(rasterFrameID));
    rasterP = Raster::DgnRasterCollection::GetRastersR(m_model).FindP(rasterFrameElmRef);

    ElementHandle frameHandle(rasterFrameElmRef,m_model);
    IRasterAttachmentQuery* pQuery = dynamic_cast<IRasterAttachmentQuery*>(&frameHandle.GetHandler());

    if (NULL!=pQuery)
        {
        // if a view is selected by a user, apply that view.
        int levelDisplayView(this->GetLevelDisplayView());
        if (levelDisplayView>=0 && levelDisplayView<8)
            {
            shouldLoad = pQuery->GetViewState(frameHandle,levelDisplayView);
            }
        else
            {
            // "Global" is selected by the user, turn the image on if it is on in any of the 8 views.
            if (pQuery->GetViewState(frameHandle,0) || 
                pQuery->GetViewState(frameHandle,1) || 
                pQuery->GetViewState(frameHandle,2) || 
                pQuery->GetViewState(frameHandle,3) || 
                pQuery->GetViewState(frameHandle,4) || 
                pQuery->GetViewState(frameHandle,5) || 
                pQuery->GetViewState(frameHandle,6) || 
                pQuery->GetViewState(frameHandle,7)   )
                shouldLoad = true;
            else
                shouldLoad = false;
            }
        }
    return shouldLoad;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                   Marc.Bedard  04/2009
+---------------+---------------+---------------+---------------+---------------+------*/
class RasterSourceUnloadGuard2
    {
    public:
    RasterSourceUnloadGuard2():m_rasterP(NULL),m_IsSourceClose(false)
        {
        }
    ~RasterSourceUnloadGuard2()
        {
        if (m_rasterP!=NULL && m_IsSourceClose)
            {
            DgnPlatform::Raster::DgnRasterUtilities::ReloadRasterSource(*m_rasterP, false);
            }
        }    
    void UnloadNow(DgnRasterP rasterP)
        {
        BeAssert (m_rasterP==nullptr && L"This class is not design to be used with several raster");
        m_rasterP=rasterP;
        if (m_rasterP!=NULL && !m_IsSourceClose)
            {
            m_IsSourceClose=true;
            if(m_rasterP->GetRasterSourceCP() == NULL || m_rasterP->GetRasterSourceCP()->GetRasterFile().GetStatus() == DgnPlatform::Raster::RASTERFILE_STATUS_Closed)
                return;

            // Close source file on disk and free corresponding loader
            DgnPlatform::Raster::DgnRasterUtilities::CloseRasterSource(*m_rasterP, false);
            }
        }

    private:
        DgnRasterP m_rasterP;
        bool m_IsSourceClose;
    };


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveNonCacheChanges (bool doFullSave, DgnFileP dgnFile)
    {
    m_savingChanges = true;
    if (NULL != m_model)
        {
        DgnFileFormatType   format = dgnFile->GetOriginalFormat ();

        if (DgnFileFormatType::DWG == format || DgnFileFormatType::DXF == format)
             m_useLevelSymbologyOverrides = false;
        }

    // full save has already saved table elements in SaveDictionaryModel - don't dup the save
    if (!doFullSave)
        this->SaveXRefsAndLayersToDatabase (dgnFile);

    // we have to save which layers are locked and unlock them. We'll relock them when we're done.
    m_pFileHolder->RecordLockedLayers ();

    // We have unloaded imageDefs at file open.  We have to re-load them before saving.
    AcDbDictionaryPointer   imageDictionary (AcDbRasterImageDef::imageDictionary(m_pFileHolder->GetDatabase()), AcDb::kForWrite);
    if (Acad::eOk == imageDictionary.openStatus())
        {
        AcDbDictionaryIterator* pIterator = imageDictionary->newIterator ();
        for (; !pIterator->done(); pIterator->next())
            {
            AcDbObject*     object = NULL;
            RasterSourceUnloadGuard2 __rasterSourceUnloadGuard;
            if (Acad::eOk == pIterator->getObject(object, AcDb::kForWrite))
                {
                bool shouldLoad(false);
                DgnRasterP pRaster;
                AcDbRasterImageDef* imageDef = AcDbRasterImageDef::cast (object);
                if (NULL != imageDef)
                    {
                    shouldLoad = ShouldLoadImageDef(imageDef,pRaster);

                    Acad::ErrorStatus   errorStatus;
                    if (shouldLoad)
                        {
                        __rasterSourceUnloadGuard.UnloadNow(pRaster); //Will be restore when become out of scope
                        errorStatus = imageDef->load ();
                        }
                    else
                        {
                        errorStatus = imageDef->unload ();
                        }

                    if (Acad::eOk != errorStatus)
                        DIAGNOSTIC_PRINTF ("Raster image %ls failed loading/unloading. %ls\n", imageDef->sourceFileName(), acadErrorStatusText(errorStatus));
                    }
                object->close ();
                }
            }
        }

    return SUCCESS;
    }

struct PostProcessingTreeNode
    {
    UInt64                  m_objectInt64Id;
    MSElementDescr*         m_pDescr;
    PostProcessingTreeNode (MSElementDescrCP pDescr, ElementId objectInt64Id)
        {
        m_objectInt64Id = objectInt64Id;
        if (NULL == pDescr)
            m_pDescr = NULL;
        else
            pDescr->Duplicate (&m_pDescr);
        }
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::PostProcessingRequired
(
ElementHandleCR             inElement,
AcDbObjectId                objectId
)
    {
    // should never happen.
    if (objectId.isNull())
        return;

    // The default AVL tree compare function uses uint64 key:
    UInt64      objectInt64Id = this->ElementIdFromObjectId (objectId);

    // If this is a dictionary element, then store it in the file holder tree
    // and postProcess only after then entire file has been saved.  Else
    // store with the context and it will be process after this cache is processed.
    AvlTree*    idTree = m_modelId < 0 ? m_pFileHolder->GetPostProcessTree() : m_pPostProcessIdTree;

    // Don't try to insert a node for an existing objectID, as that would duplicate element descriptor and lead the memory.
    if (NULL != mdlAvlTree_search(idTree, &objectInt64Id))
        return;

    // optional input element, via an invalid element handle:
    MSElementDescrCP        oldElmdscr = inElement.GetElementDescrCP ();
    MSElementDescrP         newElmdscr = NULL;
    if (!inElement.IsValid() || NULL == oldElmdscr || BSISUCCESS != oldElmdscr->Duplicate(&newElmdscr))
        {
        // create an element from the ID if not provided - required for ToObject extension of an element handler:
        EditElementHandle   oldEeh(this->ElementIdFromObjectId(objectId), m_model);
        oldElmdscr = oldEeh.GetElementDescrCP ();
        if (!oldEeh.IsValid() || NULL == oldElmdscr || BSISUCCESS != oldElmdscr->Duplicate(&newElmdscr))
            {
            DIAGNOSTIC_PRINTF ("Error constructing element(ID=%I64d) for post process!\n", this->ElementIdFromObjectId(objectId));
            return;
            }
        }

    PostProcessingTreeNode  node (newElmdscr, objectInt64Id);

    mdlAvlTree_insertNode (idTree, &node, sizeof(node));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 FromDgnPostProcessIdFreeFunction
(
void*                       nodeToFreeP,
void*                       optArgP
)
    {
    PostProcessingTreeNode*     pNode       = static_cast<PostProcessingTreeNode *>(nodeToFreeP);
    ConvertFromDgnContext*      pDgnContext = static_cast<ConvertFromDgnContext *>(optArgP);
    // post process has to change
    try
        {
        if (NULL == pNode->m_pDescr)
            {
            DIAGNOSTIC_PRINTF ("Null element descriptor of objectID=%I64d for post process.\n", pNode->m_objectInt64Id);
            return;
            }

        AcDbObjectId        objectId;
        if (Acad::eOk != pDgnContext->GetFileHolder().GetDatabase()->getAcDbObjectId(objectId, false, pDgnContext->DBHandleFromElementId(pNode->m_objectInt64Id), 0))
            {
            DIAGNOSTIC_PRINTF ("Error getting objectID %I64d for post process.\n", pNode->m_objectInt64Id);
            return;
            }

        AcDbObjectPointer<AcDbObject> acObject (objectId, AcDb::kForWrite);
        if (Acad::eOk == acObject.openStatus())
            {
            // Need a persistent element as some elements may require elementRef - e.g. NamedGroup constructor in TFS#13759.
            ElementHandle       elemHandle(pNode->m_pDescr, true, true);
            ToDwgExtension* toDwg = ToDwgExtension::Cast (elemHandle.GetHandler());
            if (NULL != toDwg)
                {
                AcDbObjectP     resultObject = NULL;
                RealDwgStatus   status = toDwg->ToObjectPostProcess (elemHandle, acObject, *pDgnContext);
                if (RealDwgSuccess != status)
                    DIAGNOSTIC_PRINTF ("Error post processing object with AcDbHandle %I64d.\n", pNode->m_objectInt64Id);
                }
            acObject->close();
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Error opening object with AcDbHandle %I64d for post process. [%ls]\n", pNode->m_objectInt64Id, acadErrorStatusText(acObject.openStatus()));
            }
        }

    catch (RealDwgException& exception)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown during postProcessing of AcDbHandle: %I64d, [%ls]\n", pNode->m_objectInt64Id, exception.ErrorMessage());
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown during postProcessing of AcDbHandle: %I64d\n", pNode->m_objectInt64Id);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::PostProcessTree (AvlTree** ppTree)
    {
    mdlAvlTree_free (ppTree, FromDgnPostProcessIdFreeFunction, this);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::PostSaveToDatabase (bool changesOnly)
    {
    try
        {
        if (m_modelId < 0)
            {
            // Need to extract the database variables from the database after other objects
            this->ExtractDatabaseVariablesFromDgnHeader (changesOnly);
            if (!this->GetSettings().DisallowSaveDimensionSettings())
                this->ExtractDatabaseVariablesFromUnnamedDimensionStyle();
            }

        this->PostProcessTree (&m_pPostProcessIdTree);

        if (m_xRefBlockPurgeRequired)
            RealDwgUtil::PurgeXRefBlocks (m_pFileHolder->GetDatabase());

        m_pPostProcessIdTree = mdlAvlTree_init (AVLKEY_ELEMENTID);

        AcDbObjectId        layerId;

        // If constructions are off - light cells are not displayed so turn off ASHADE layer - this
        // is layer created to contain lights.
        if (!m_savingChanges &&
            !m_viewDisplayFlags.constructs &&
            ! (layerId = m_pFileHolder->GetLayerByName (StringConstants::AShadeLayerName)).isNull())
            {
            AcDbLayerTableRecordPointer layer (layerId, AcDb::kForWrite);
            layer->setIsOff (true);
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown during Post-Checkin\n");
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::PreSaveElementToDatabase (AcDbObjectId* outId, ElementId elementId)
    {
    ElementHandle   eh (elementId, m_model);
    if (!eh.IsValid())
        return  CantAccessMstnElement;

    AcDbObjectId    objectId;
    this->SaveElementToDatabase (eh, &objectId);

    if (objectId.isValid())
        {
        bpair<ElementId, AcDbObjectId>  idPair(elementId, objectId);
        m_preSavedElementList.insert (idPair);
        }

    if (NULL != outId)
        *outId = objectId;

    return  objectId.isValid() ? RealDwgSuccess : NullObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::IsElementPreSaved (AcDbObjectId* outId, ElementId elementId)
    {
    T_PreSavedElements::const_iterator  iter = m_preSavedElementList.find (elementId);
    if (iter != m_preSavedElementList.end())
        {
        if (NULL != outId)
            *outId = iter->second;

        return  true;
        }

    if (m_pFileHolder->IsElementExcludedFromSaving(elementId))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveElementListToDatabase
(
DgnModelP                   pCache,
bool                        graphicElements,
int                         checkInMask,
UInt32&                     runningCount,
PrioritySorter*             pPrioritySorter
)
    {
    bool                changeFound     = false;

    PersistentElementRefP               elemRef;
    PersistentElementRefListIterator    iter;

    double progress = 0;;
    double totalPercentage = 0;;
    UInt32 elemCount = 0;
    if (graphicElements)
        {
        elemCount = pCache->GetElementCount(DgnModelSections::GraphicElements);
        progress = 10;
        totalPercentage = 90; //Allocating 90% for processing for graphical elements.
        }
    else
        {
        elemCount = pCache->GetElementCount(DgnModelSections::ControlElements);
        totalPercentage = 10; //Allocating 10% for processing for control elements.
        }

    IDgnProgressMeterExt* progressMeter = dynamic_cast<IDgnProgressMeterExt*>(DgnPlatformLib::GetHost().GetProgressMeter());

    for (elemRef = graphicElements ? iter.GetFirstElementRef (*pCache->GetGraphicElementsP()) : iter.GetFirstElementRef (*pCache->GetControlElementsP()); NULL != elemRef; elemRef = iter.GetNextElementRef())
        {
        Elm_hdr const*          ehdr;

        // If the element was either deleted or made part of a complex, delete it.
        if (NULL == (ehdr = elemRef->GetElementHeaderCP()))
            continue;

        int         maskValue;
        switch (ehdr->type)
            {
            case REFERENCE_ATTACH_ELM:
                maskValue = CHECKIN_MASK_References;
                break;

            case RASTER_FRAME_ELM:
                maskValue = CHECKIN_MASK_Graphic;
                break;

            // these elements are handled separately
            case DGNFIL_HEADER_ELM:
            case DGNSTORE_HDR:
            case DGNSTORE_COMP:
            case VIEW_GROUP_ELM:
            case TABLE_ELM:
            case TABLE_ENTRY_ELM:
                continue;

            default:
                maskValue = MSElement::IsDisplayable(ehdr->type) ? CHECKIN_MASK_Graphic : CHECKIN_MASK_NonGraphic;
                break;
            }

        if (0 != (checkInMask & maskValue))
            {
            runningCount += (1 + elemRef->GetComponentCount());
            this->ReportProgress ((double) runningCount / (double) m_elementCount);

            ElementHandle  elemHandle (elemRef, m_model);
            this->SaveElementToDatabase (elemHandle, NULL, pPrioritySorter);
            if (NULL != pPrioritySorter)
                pPrioritySorter->AddElement (elemHandle);

            if (progressMeter)
                {
                progress += totalPercentage / (double)elemCount;
                progressMeter->ReportProgress(progress);
                }
            }
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ReportProgress (double progress)
    {
    IDgnProgressMeterP progressMeter = DgnPlatformLib::GetHost ().GetProgressMeter ();
    if (progressMeter)
        {
        progressMeter->UpdateTaskProgress ();
        //progressMeter->ReportProgress(progress);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     WillChildrenBeAdded (AcDbEntityP acEntity)
    {
    /*----------------------------------------------------------------------------------------------------
    Since Vancouver, we try post adding objects to database, as opposed to pre-adding them.  Some entities,
    such as 2D and 3D polylines, when added to the database, their children get automatically added as well.
    These children entities will take the new object ID's and thus to step over our handseed.
    To workaround this problem, we create a temporary place holder entity, add it to database, reset the
    handseed to original value, and hand the desired ID over to the header entity.  Apparently handOverTo
    call also automatically post children to database so we do not have to post them separately.

    For MLeader, AcDbMLeaderObjectContextData extension dictionaries get appended while saving to database.
    ----------------------------------------------------------------------------------------------------*/
    if (acEntity->isKindOf(AcDb3dPolyline::desc()) || acEntity->isKindOf(AcDb2dPolyline::desc()) || acEntity->isKindOf(AcDbMLeader::desc()))
        return  true;

    /*----------------------------------------------------------------------------------------------------
    Most annotative objects should be pre-added and therefore should not have to go through this workaround.
    There can be cases that still require this step, TFS# 89079 for instance.  A failed tag is dropped to
    text element and the annotative text gets posted added here.
    ----------------------------------------------------------------------------------------------------*/
    if (RealDwgUtil::IsObjectAnnotative(acEntity) && !acEntity->extensionDictionary().isValid())
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddBlockToBlockTable
(
AcDbBlockTable*             pBlockTable,
AcDbBlockTableRecord*       pBlock,
ElementId                   uniqueId
)
    {
    // we'd like to our new Block to have a handle value equal to sourceElementId, but there doesn't seem to be a way to do that in ObjectARX.
    AcDbObjectId        newObjectId;
    Acad::ErrorStatus   status;
    if (0 != uniqueId)
        {
        BeAssert (m_pFileHolder->NoExistingElementMatchesId(uniqueId) && L"Block ID already used in DWG!");
        /*-------------------------------------------------------------------------------
        Although a BLOCK contains BLOCKBEGIN and BLOCKEND, they appear to be created at
        the time of construction, so there is no need to force adding them prior to setting
        handseed like what must do for adding attributes to an INSERT entity.
        -------------------------------------------------------------------------------*/

        UInt64          oldId       = RealDwgUtil::CastDBHandle (m_pFileHolder->SetHandseed (uniqueId));
        BeAssert (oldId > uniqueId && L"Block ID greater than seed ID!");

        status = pBlockTable->add (newObjectId, pBlock);
        BeAssert (m_pFileHolder->ExactlyOneAppended(uniqueId) && L"More objects added to DWG when adding a block!");
        BeAssert (m_pFileHolder->VerifyDesiredHandleValue(uniqueId, newObjectId.handle()) && L"Added block ID not the same as requested!");

        m_pFileHolder->RestoreHandseed (oldId);
        }
    else
        {
        status = pBlockTable->add (newObjectId, pBlock);
        }

    if (Acad::eOk != status)
        DIAGNOSTIC_PRINTF ("Attempt to add a new block %I64d to block table failed. [%ls]\n", uniqueId, acadErrorStatusText(status));

    NOISYDEBUG_PRINTF ("Block(ID=%I64d) has been added to block table(%I64d)\n", this->ElementIdFromObjectId(newObjectId), this->ElementIdFromObject(pBlockTable));

    return newObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddEntityToBlock
(
AcDbBlockTableRecord*       pBlock,
AcDbEntity*                 pEntity,
ElementId                   uniqueId
)
    {
    if (NULL == pBlock)
        {
        DIAGNOSTIC_PRINTF ("Attempt to add to null block ignored\n");
        return  AcDbObjectId::kNull;
        }
    else if (NULL == pEntity)
        {
        DIAGNOSTIC_PRINTF ("Attempt to add a null entity to block ignored\n");
        return  AcDbObjectId::kNull;
        }
    else if (pEntity->objectId().isValid())
        {
        DIAGNOSTIC_PRINTF ("Attempt to add an already database resident entity(%I64d:%I64d) to block ignored\n", uniqueId, this->ElementIdFromObject(pEntity));
        return  AcDbObjectId::kNull;
        }
    else if (pEntity->isKindOf(AcDbAttribute::desc()))
        {
        DIAGNOSTIC_PRINTF ("Attempting to add an attribute (%I64d) to block ignored\n", uniqueId);
        return  AcDbObjectId::kNull;
        }

    AcDbObjectId        newEntityId;
    Acad::ErrorStatus   status;
    if (0 != uniqueId && INVALID_ELEMENTID != uniqueId)
        {
#ifdef REALDWG_NOISY_DEBUG        
        BeAssert (m_pFileHolder->NoExistingElementMatchesId(uniqueId) && L"Entity ID already used in DWG!");
#endif
        UInt64          oldId = m_pFileHolder->SetHandseed (uniqueId);

        AcDbPoint*      placeHolderEntity = NULL;
        if (WillChildrenBeAdded(pEntity))
            placeHolderEntity = new AcDbPoint ();

        status = pBlock->appendAcDbEntity (newEntityId, NULL == placeHolderEntity ? pEntity : placeHolderEntity);

#ifdef REALDWG_NOISY_DEBUG        
        BeAssert (oldId > uniqueId && L"Entity ID is larger than handseed!");
        BeAssert (m_pFileHolder->ExactlyOneAppended(uniqueId) && L"More objects added to DWG with a newly saved entity!");
        BeAssert (m_pFileHolder->VerifyDesiredHandleValue(uniqueId, newEntityId.handle()) && L"New entity added with a different ID than requested!");
#endif

        m_pFileHolder->RestoreHandseed (oldId);

        if (Acad::eOk == status && NULL != placeHolderEntity && placeHolderEntity->handOverTo(pEntity, false, false))
            delete placeHolderEntity;

        // if the desired handle is in use, which should rarely happen and needs to be looked at, add the entity with a new handle:
        if (Acad::eHandleInUse == status)
            status = pBlock->appendAcDbEntity (newEntityId, pEntity);
        }
    else
        {
        status = pBlock->appendAcDbEntity (newEntityId, pEntity);
        }

#ifdef REALDWG_DIAGNOSTICS
    if (Acad::eOk != status)
        printf ("Attempt to add a new entity %I64d to block failed. [%ls]\n", uniqueId, acadErrorStatusText(status));

    ElementId       actualId = this->ElementIdFromObjectId (newEntityId);
    NOISYDEBUG_PRINTF ("Entity(ID=%I64d) has been added to block(%I64d)\n", actualId, this->ElementIdFromObject(pBlock));
#endif

    return newEntityId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddAttributeToBlockReference
(
AcDbBlockReference*         pBlockReference,
AcDbAttribute*              pAttribute,
ElementId                   uniqueId
)
    {
    AcDbObjectId        newObjectId;
    Acad::ErrorStatus   status;
    if (0 != uniqueId)
        {
        BeAssert (m_pFileHolder->NoExistingElementMatchesId(uniqueId) && L"Requested attribute ID already used in DWG!");

        // turn annotative off prior to adding the attribute to DWG as otherwise annotation dictionaries will also be added.
        bool                isAnnotative = RealDwgUtil::IsObjectAnnotative (pAttribute);
        if (isAnnotative)
            RealDwgUtil::SetObjectAnnotative (pAttribute, false);

        AcDbSequenceEnd*    seqEnd = NULL;
        if (Acad::eOk == pBlockReference->openSequenceEnd(seqEnd, AcDb::kForRead))
            {
            // already has seqbegin & seqend entities in database.
            seqEnd->close ();
            }
        else
            {
            /*---------------------------------------------------------------------------
            When appending the first attribute for a newly created AcDbBlockReference
            object, RealDWG will also add SEQBEGIN and SEQEND in the block reference,
            with two new object ID's whose values are incrementals from the handseed we
            give to it.  This would cause duplicated and conflicting object ID's in
            database.  To avoid this from happening, we force RealDWG to add seqbegin &
            seqend entities by appending and then erasing a temporary attribute.  A trade
            off of this workaround is a waste of an object ID for every new block
            reference we create in this way.  This is because an erased object ID is still
            valid in database until the file is saved and re-loaded.
            ---------------------------------------------------------------------------*/
            AcDbAttribute*  tempAttribute = new AcDbAttribute ();
            status = pBlockReference->appendAttribute (newObjectId, tempAttribute);
            if (Acad::eOk == status)
                {
                tempAttribute->erase ();
                tempAttribute->close ();
                newObjectId.setNull ();
                }
            else
                {
                delete tempAttribute;
                }
            }

        UInt64          oldId       = m_pFileHolder->SetHandseed (uniqueId);
        BeAssert (oldId > uniqueId && L"Requested attribute ID bigger than handseed in DWG!");

        status = pBlockReference->appendAttribute (newObjectId, pAttribute);
        BeAssert (m_pFileHolder->ExactlyOneAppended(uniqueId) && L"Other objects added to DWG with adding an attribute");
        BeAssert (m_pFileHolder->VerifyDesiredHandleValue(uniqueId, newObjectId.handle()) && L"Attribute saved with a different ID than requested!");

        m_pFileHolder->RestoreHandseed (oldId);

        // reset annotative back
        if (isAnnotative)
            RealDwgUtil::SetObjectAnnotative (pAttribute, true);
        }
    else
        {
        status = pBlockReference->appendAttribute (newObjectId, pAttribute);
        }

    if (Acad::eOk != status)
        DIAGNOSTIC_PRINTF ("Attempt to add a new attribute %I64d failed. [%ls]\n", uniqueId, acadErrorStatusText(status));

    NOISYDEBUG_PRINTF ("Attribute with ID=%I64d has been added to block reference\n", this->ElementIdFromObject(pBlockReference));

    return newObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddEntityToBlockId
(
AcDbObjectId        blockId,                // objectId of block.
AcDbEntity*         pEntity,                // New entity
ElementId           uniqueId                // id.
)
    {
    if (!blockId.isNull())
        {
        AcDbBlockTableRecordPointer     pBlock (blockId, AcDb::kForWrite);
        if (Acad::eOk != pBlock.openStatus())
            return 0;

        return this->AddEntityToBlock (pBlock, pEntity, uniqueId);
        }
    return AcDbObjectId();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddEntityToCurrentBlock
(
AcDbEntity*                 pEntity,
ElementId                   uniqueId
)
    {
    return this->AddEntityToBlockId (m_currentBlockId, pEntity, uniqueId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddRecordToSymbolTable
(
AcDbSymbolTable*            pSymbolTable,
AcDbSymbolTableRecord*      pSymbolTableRecord,
ElementId                   uniqueId
)
    {
    AcDbObjectId        newObjectId;
    Acad::ErrorStatus   status;
    if (0 != uniqueId)
        {
#ifdef REALDWG_NOISY_DEBUG        
        BeAssert (m_pFileHolder->NoExistingElementMatchesId(uniqueId) && L"Requested ID for a new symbol table record has already be taken!");
#endif
        UInt64          oldId       = m_pFileHolder->SetHandseed (uniqueId);

        status = pSymbolTable->add (newObjectId, pSymbolTableRecord);
#ifdef REALDWG_NOISY_DEBUG        
        BeAssert (oldId > uniqueId && L"New symble table record ID greater than handseed!");
        if (Acad::eOk == status)
            {
            BeAssert (m_pFileHolder->ExactlyOneAppended(uniqueId) && L"More objects added with a symble table record!");
            BeAssert (m_pFileHolder->VerifyDesiredHandleValue(uniqueId, newObjectId.handle()) && L"New symble table record is added with a different ID!");
            }
#endif
        m_pFileHolder->RestoreHandseed (oldId);
        }
    else
        {
        status = pSymbolTable->add (newObjectId, pSymbolTableRecord);
        }

#ifdef REALDWG_DIAGNOSTICS
    if (Acad::eOk != status)
        printf ("Attempt to add a new symbol table record %I64d failed. [%ls]\n", uniqueId, acadErrorStatusText(status));

    ElementId       actualId = this->ElementIdFromObjectId (newObjectId);
    NOISYDEBUG_PRINTF ("Symbol table record(ID=%I64d) has been added to table(%I64d)\n", actualId, this->ElementIdFromObject(pSymbolTable));
#endif  // REALDWG_DIAGNOSTICS

    return newObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddObjectToDictionary
(
AcDbDictionary*             pDictionary,
AcDbObject*                 pObject,
const ACHAR*                pKey,
ElementId                   uniqueId
)
    {
    AcDbObjectId        newObjectId;
    Acad::ErrorStatus   status;
    if (0 != uniqueId)
        {
        BeAssert (m_pFileHolder->NoExistingElementMatchesId(uniqueId) && L"New dictionary ID has been taken in DWG!");
        UInt64          oldId   = m_pFileHolder->SetHandseed (uniqueId);
        BeAssert (oldId > uniqueId && L"New dictionary ID greater than handseed!");

        status = pDictionary->setAt (pKey, pObject, newObjectId);
        BeAssert (m_pFileHolder->ExactlyOneAppended(uniqueId) && L"More objects added with a new dictionary saved!");
        BeAssert (m_pFileHolder->VerifyDesiredHandleValue(uniqueId, newObjectId.handle()) && L"New dictionary saved with a different ID!");

        m_pFileHolder->RestoreHandseed (oldId);

        // if the desired handle is in use, which should rarely happen and needs to be looked at, add the entity with a new handle:
        if (Acad::eHandleInUse == status)
            status = pDictionary->setAt (pKey, pObject, newObjectId);
        }
    else
        {
        status = pDictionary->setAt (pKey, pObject, newObjectId);
        }

    if (Acad::eOk != status)
        DIAGNOSTIC_PRINTF ("Attempt to add a new dictionary %I64d failed. [%ls]\n", uniqueId, acadErrorStatusText(status));

    NOISYDEBUG_PRINTF ("Dictionary entry(ID=%I64d) has been added to parent dictionary(%I64d)\n", this->ElementIdFromObjectId(newObjectId), this->ElementIdFromObject(pDictionary));

    return newObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddUnknownTypeToDatabase
(
AcDbObject*                 pOwner,
AcDbObject*                 pObject,
ElementId                   uniqueId
)
    {
    // this method is called when we get an AcDbObject from an application. We don't know whether
    // it's an entity or a symbol table entry or a dictionary entry or what. ObjectARX does not allow
    // you to use AcDbDatabase::addAcDbObject for entities - you have to use AcDbBlockTableRecord::appendAcDbEntity.
    AcDbEntity*             pEntity;
    AcDbSymbolTableRecord*  pSymbolTableRecord;
    AcDbObjectId            newObjectId;

    if (NULL != (pEntity = AcDbEntity::cast (pObject)))
        {
        // owner has to be block.
        AcDbBlockTableRecord* pBlockTableRecord = AcDbBlockTableRecord::cast (pOwner);
        BeAssert (nullptr != pBlockTableRecord && L"Cannot open block table record to add an unknown entity!");
        if (NULL != pBlockTableRecord)
            newObjectId = AddEntityToBlock (pBlockTableRecord, pEntity, uniqueId);
        }
    else if (NULL != (pSymbolTableRecord = AcDbSymbolTableRecord::cast (pObject)))
        {
        AcDbSymbolTable* pSymbolTable = AcDbSymbolTable::cast (pOwner);
        BeAssert (nullptr != pSymbolTable && L"Cannot open symbol table to add an unknown record!");
        if (NULL != pSymbolTable)
            newObjectId = AddRecordToSymbolTable (pSymbolTable, pSymbolTableRecord, uniqueId);
        }
    else
        {
        const ACHAR* objectName = pObject->isA()->name();
        const ACHAR* ownerName  = pOwner->isA()->name();
        DIAGNOSTIC_PRINTF ("Error trying to add unknown type %ls to database with owner type %ls.\n", objectName, ownerName);
        BeAssert (false && L"Unsupported unknown type of object!");
        }

    return newObjectId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsUnicodeOrMultibyteCharacterInducer (WCharCP pStringChar, size_t stringLength)
    {
    if (stringLength <= 7)
        return  false;

    return (0x5c == pStringChar[0] && '+' == pStringChar[2]) && (pStringChar[1] == 'U' || pStringChar[1] == 'M');
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ReplaceStringChars (WCharP pString, WCharCP pTestChars, WChar replaceChar)
    {
    bool        replaced = false;
    size_t      len = wcslen (pString);
    for (; '\0' != *pTestChars; pTestChars++)
        {
        for (WChar *pStringChar = pString; '\0' != *pStringChar; pStringChar++)
            {
            if (*pTestChars == *pStringChar && !IsUnicodeOrMultibyteCharacterInducer (pStringChar, len - (pStringChar - pString)))
                {
                *pStringChar = replaceChar;
                replaced = true;
                }
            }
        }
    return replaced;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ValidateFileName (WStringR fileName)
    {
    static WChar*   s_invalidFileNameChars = L"\\/:*?\"<>|\t";

    if (fileName.empty())
        return  false;

    WChar*          outName = (WCharP) calloc (fileName.length() + 50, sizeof(WChar));
    if (NULL == outName)
        return  false;

    wcscpy (outName, fileName.c_str());

    bool            replaced = ReplaceStringChars (outName, s_invalidFileNameChars, CHAR_ReplaceInvalidPost2000);
    if (replaced)
        fileName.assign (outName);

    free (outName);

    return  replaced;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ValidateName (WCharP pName, int versionNumber)
    {
    static WChar  *s_post2000InvalidNameChars = L"<>/\\:;?*,=`\"\t",
                    *s_pre2000InvalidNameChars  = L"#!@%^&()+.{} ";
    bool            replacedChars = ReplaceStringChars (pName, s_post2000InvalidNameChars, CHAR_ReplaceInvalidPost2000);

    if (versionNumber < DwgFileVersion_13)
        replacedChars |= ReplaceStringChars (pName, s_pre2000InvalidNameChars, CHAR_ReplaceInvalidPre2000);

    // remove ending space chars:
    WCharP        pLastChar = pName + wcslen (pName) - 1;
    WCharP        pEnd = pLastChar;
    for (; pEnd > pName && *pEnd == ' '; *pEnd-- = '\0');

    if (pEnd < pLastChar)
        replacedChars = true;

    // replace starting space char:
    if (pName[0] == ' ')
        {
        pName[0] = '_';            // Names are not allowed to begin with space (TR# 126431).
        replacedChars = true;
        }

    // control characters cause problems in both ACAD and MS - TR 327470
    for (WCharP currChar = pName; 0 != *currChar; currChar++)
        {
        if (iswcntrl(*currChar))
            {
            *currChar = L'_';
            replacedChars = true;
            }
        }

    if ('\0' == pName[0])
        {
        wcscpy (pName, L"null");
        replacedChars = true;
        }

    return replacedChars;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ValidateName (WCharP pName)
    {
    return this->ValidateName (pName, m_targetVersion);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ValidateName (AcString& inputString)
    {
    WChar     tmpChars[2048];
    wcscpy (tmpChars, inputString);
    if (this->ValidateName (tmpChars))
        {
        inputString = AcString (tmpChars);
        return true;
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ValidateName (WString& name)
    {
    size_t      nChars = name.size ();
    if (nChars < 1)
        return  false;

    WCharP    nameChars = (WCharP) _alloca ((nChars + 1) * sizeof(WChar));
    wcscpy (nameChars, name.GetWCharCP());
    if (this->ValidateName(nameChars))
        {
        name = WString (nameChars);
        return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
DeduplicateTableCache::DeduplicateTableCache()
    {
    m_pNameTree = NULL;
    m_lastName[0] = '\0';
    m_lastIndex = -1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
DeduplicateTableCache::~DeduplicateTableCache()
    {
    mdlAvlTree_free (&m_pNameTree, NULLFUNC, NULL);
    }

/*---------------------------------------------------------------------------------**//**
*  This code will append a suffix to any name that already exists in the table being
*  exported.  This is to safeguard against duplicate names in a table.  Our tables
*  should not contain duplicates, but if they do then AutoCAD will reject file.
*
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::DeduplicateBlockName (AcString& name)
    {
    AcDbBlockTablePointer pBlockTable (m_pFileHolder->GetDatabase()->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != pBlockTable.openStatus())
        {
        BeAssert (false && L"Cannot open block table to check dup block name!");
        return;
        }

    this->DeduplicateTableName (pBlockTable, &m_blockNameCache, name);
    }

/*---------------------------------------------------------------------------------**//**
*  This code will append a suffix to any name that already exists in the table being
*  exported.  This is to safeguard against duplicate names in a table.  Our tables
*  should not contain duplicates, but if they do then AutoCAD will reject file.
*
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::DeduplicateTableName (AcDbObjectId tableId, AcString& name)
    {
    AcDbSymbolTablePointer<AcDbSymbolTable> pTable (tableId, AcDb::kForRead);
    if (Acad::eOk != pTable.openStatus())
        {
        BeAssert (false && L"Cannot open symbol table to check dup record name!");
        return;
        }

    this->ValidateName (name);

    ConvertFromDgnContext::DeduplicateTableName (pTable, name);
    }

/*---------------------------------------------------------------------------------**//**
*  This code will append a suffix to any name that already exists in the table being
*  exported.  This is to safeguard against duplicate names in a table.  Our tables
*  should not contain duplicates, but if they do then AutoCAD will reject file.
*
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::DeduplicateTableName
(
AcDbSymbolTable*            pTable,
AcString&                   name
)
    {
    // Use pTable->getAt to exclude erased table entries (pTable->has includes erased entries)
    int             suffixIndex;
    WChar         suffixedName[512];
    const ACHAR*    nameStr = name.kwszPtr();
    for (suffixIndex = 1; suffixIndex < 2000; suffixIndex++)
        {
        AcDbObjectId    objectId;
        if (Acad::eOk != pTable->getAt (nameStr, objectId, false))
            break;

        swprintf (suffixedName, L"%s_%d", name.kwszPtr(), suffixIndex);
        nameStr = suffixedName;
        }

    // this is a safety valve to prevent an infinite loop.
    BeAssert (suffixIndex < 2000 && L"New name ran out of buffer");

    // if it was necessary to change the name, assign it.
    if (nameStr == suffixedName)
        name.assign (suffixedName);
    }

/*---------------------------------------------------------------------------------**//**
*  This code will append a suffix to any name that already exists in the table being
*  exported.  This is to safeguard against duplicate names in a table.  Our tables
*  should not contain duplicates, but if they do then AutoCAD will reject file.
*
* @bsimethod                                                    RayBentley      08/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::DeduplicateTableName
(
AcDbSymbolTable*            pTable,
DeduplicateTableCache*      pCache,
AcString&                   name
)
    {
    if (NULL == pCache->m_pNameTree)
        {
        pCache->m_pNameTree= mdlAvlTree_init (AVLKEY_WSTRINGI);

        AcDbSymbolTableIterator*        pIter;
        pTable->newIterator (pIter);

        for (pIter->start(); !pIter->done(); pIter->step())
            {
            AcDbObjectId    recordId;
            if (Acad::eOk != pIter->getRecordId (recordId))
                continue;

            AcDbSymbolTableRecord* pRecord;
            if (Acad::eOk != acdbOpenObject (pRecord, recordId, AcDb::kForRead, false))
                continue;

            const ACHAR*    nameNew;
            pRecord->getName (nameNew);
            pRecord->close();
            mdlAvlTree_insertNode (pCache->m_pNameTree, (void *) nameNew, 2 * (1 + wcslen (nameNew)));
            }

        delete pIter;
        }

    WChar    nameChars[512], originalName[512];

    wcscpy (nameChars, name.kwszPtr());
    wcscpy (originalName, nameChars);

    this->ValidateName (nameChars);

    int         prefixIndex = 0;
    if (pCache->m_lastIndex > 0 && 0 == wcscmp (nameChars, pCache->m_lastName))
        prefixIndex = pCache->m_lastIndex;

    while (NULL !=  mdlAvlTree_search (pCache->m_pNameTree, nameChars))
        swprintf (nameChars, L"%s_%d", originalName, ++prefixIndex);

    mdlAvlTree_insertNode (pCache->m_pNameTree, nameChars, 2 * wcslen (nameChars) + 2);

    if (prefixIndex > 0)
        {
        pCache->m_lastIndex = prefixIndex;
        wcscpy (pCache->m_lastName, originalName);
        }

    name.assign(nameChars);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    ConvertFromDgnContext::CreateAndAddBlockHeader (AcString& name, ElementHandleCR cell)
    {
    AcDbBlockTablePointer           pBlockTable (m_pFileHolder->GetDatabase()->blockTableId(), AcDb::kForWrite);
    if (Acad::eOk != pBlockTable.openStatus())
        {
        BeAssert (false && L"Cannot open block table to add a new record!");
        AcDbObjectId    nullId;
        return nullId;
        }

    AcDbBlockTableRecordPointer     pBlock (new AcDbBlockTableRecord());
    WChar                           nameChars[MAX_CELLNAME_LENGTH];

    wcscpy (nameChars, name.kwszPtr());
    if ( (0 != nameChars[0]) && this->ValidateName (nameChars))
        name.assign (nameChars);

    // Can't add to table without name.
    if (name.isEmpty())
        name.assign (L"*U");
    else
        DeduplicateTableName (pBlockTable, &m_blockNameCache, name);

    // If RealDWG chokes on setting the name, try setting it to *U again - TR 291008
    Acad::ErrorStatus   errorStatus = pBlock->setName (name);
    if (Acad::eOk != errorStatus)
        errorStatus = pBlock->setName (AcString(L"*U"));
    if (Acad::eOk != errorStatus)
        DIAGNOSTIC_PRINTF ("Failed setting block name %ls. [%ls]\n", nameChars, acadErrorStatusText(errorStatus));

    // set block comments from cell description
    MSElementCP elem = cell.GetElementCP ();
    WChar       comments[MAX_CELLDSCR_LENGTH] = { 0 };
    if (nullptr != elem && BSISUCCESS == CellUtil::GetCellDescription(comments, sizeof(comments) - 1, *elem))
        pBlock->setComments (comments);

    // set block units per options
    if (this->GetSettings().SaveBlockUnitsFromFileUnits())
        pBlock->setBlockInsertUnits (RealDwgUtil::AcDbUnitsValueFromDgnUnits(this->GetStandardTargetUnits()));

    // check input cell and set block annotative as appropriate:
    bool        annotative = RealDwgUtil::IsElementAnnotative (nullptr, cell);
    if (annotative)
        {
        /*-----------------------------------------------------------------------------------------------------------
        This cell is made for annotation purpose: if in a non-default model, remove annotation scale; otherwise check
        its annotation status and set the block accordingly.  
        
        Obviously creating a non-annotative block definition from a cell with annotation purpose will likely result 
        in two otherwise identical block definitions created in the file, one annotative and the other not.  This is 
        because ACAD requires block references to be consistent with their definitions.  If a definition is annotative
        all references are annotative and if a definition is not annotative none of its references can be annotative.
        -----------------------------------------------------------------------------------------------------------*/
        DgnModelP       model = cell.GetDgnModelP ();
        if (nullptr != model && model->GetModelId() != this->GetFile()->GetDefaultModelId())
            annotative = false;
        else
            annotative = RealDwgUtil::IsElementAnnotative (nullptr, cell);
        }

    RealDwgUtil::SetObjectAnnotative (pBlock, annotative);

    // add the block to the block table as a NEW record:
    this->AddBlockToBlockTable (pBlockTable, pBlock, 0);
    pBlockTable.close();

    BeAssert (m_pFileHolder->AllHandlesBelowSeed(pBlock->objectId()) && L"New block has entities greater than handseed!");

    return pBlock->objectId();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::IsFlatSurfaceOrSolid (ElementHandleCR elementIn)
    {
    if (!elementIn.IsValid())
        return false;

    switch (elementIn.GetElementType())
        {
        case CURVE_ELM:
        case ARC_ELM:
        case ELLIPSE_ELM:
        case BSPLINE_CURVE_ELM:
        case BSPLINE_SURFACE_ELM:
        case CONE_ELM:
            return false;
        }

    for (ChildElemIter child(elementIn, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        if (!IsFlatSurfaceOrSolid (child))
            return false;
        }

    return true;
    }

/*---------------------------------------------------------------------------------**//**
* Return true if this is a SmartSolid/FeatureSolid (i.e. An embedded B-Rep is present).
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsBRepSolid (ElementHandleCR elementIn)
    {
    MSElementCP     pElem = elementIn.GetElementCP ();
    return (NULL != pElem && CELL_HEADER_ELM == pElem->ehdr.type &&
            NULL != linkage_extractFromElement (NULL, pElem, LINKAGEID_EmbeddedBRep, NULL, NULL, NULL) ||
           NULL != linkage_extractFromElement (NULL, pElem, LINKAGEID_Feature, NULL, NULL, NULL));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsLineStyleExportableToDwg (Int32 style, DgnModelP model)
    {
    if (style >= MIN_LINECODE && style <= MAX_LINECODE)
        return  true;

    LsDefinitionP   lsdef = LsMap::FindInDgnOrRsc (model->GetDgnFileP(), style);
    if (NULL == lsdef)
        return  true;

#if RealDwgVersion <= 2014
    if (NULL != lsdef->GetLocation() && LsLocationType_LinFile == lsdef->GetLocation()->GetSourceType())
#else
    if (NULL != lsdef->GetLocation() && LsLocationType::LinFile == lsdef->GetLocation()->GetSourceType())
#endif
        return true;

    LsCacheComponentCP  lscomp = lsdef->GetComponentCP (model);
    if (NULL == lscomp)
        return  true;

    return  lscomp->_ExportableToDwg (true, true);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::UnsupportableLineStyle (Int32 style, LevelId level)
    {
    if (STYLE_BYLEVEL == style)
        {
        LevelHandle     levelHandle = m_dgnFile->GetLevelCacheR().GetLevel (level);
        if (!levelHandle.IsValid())
            return  false;

        style = levelHandle.GetByLevelLineStyle().GetStyle ();
        }

    WString     name;

    return style != INVALID_STYLE &&
           style != STYLE_BYLEVEL &&
           style != STYLE_BYCELL &&
           (style < 0 || style > 7) &&
           !(name = LineStyleManager::GetNameFromNumber(style, m_dgnFile)).empty() &&
           0 != wcscmp (name.c_str(), StringConstants::ContinousLinetypeName) &&
           !IsLineStyleExportableToDwg(style, m_model);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::PreprocessUnsupportedLineStyles (EditElementHandleR outElemHandle, ElementHandleR inElemHandle)
    {
    if (this->m_dropUnsupportedLineStyles && TEXT_ELM != inElemHandle.GetElementType () && TEXT_NODE_ELM != inElemHandle.GetElementType ())
        {
        EditElementHandle   dupEeh (inElemHandle, true);
        MSElementCP         element = dupEeh.GetElementCP ();
        if (NULL == element)
            return  false;

        LevelHandle         level = m_dgnFile->GetLevelCacheR().GetLevel (element->ehdr.level);

        // get the linestyle overridden by the level and apply it on the element
        if (m_useLevelSymbologyOverrides && level.IsValid() && level.GetOverrideLineStyleOn())
            {
            Int32       overrideStyle = level.GetOverrideLineStyle().GetStyle ();

            if (this->UnsupportableLineStyle(overrideStyle, element->ehdr.level))
                {
                ElementPropertiesSetter remapper;

                remapper.SetLinestyle (overrideStyle, NULL);
                remapper.Apply (dupEeh);
                }
            }

        element = dupEeh.GetElementCP ();
        if (NULL == element)
            return  false;

        ElementAgenda   droppedAgenda;
        
        // get linestyle of the element or by level and drop it to components
        if (this->UnsupportableLineStyle (element->hdr.dhdr.symb.style, element->ehdr.level) &&
            BSISUCCESS == DropToElementDrawGeom::DoDrop(dupEeh, droppedAgenda, DropGeometry(), DropGraphics(DropGraphics::OPTION_LineStyles)))
            {
            MSElementDescrP outputDescr = nullptr, tailDescr = nullptr;

            for each (ElemAgendaEntry elem in droppedAgenda)
                {
                MSElementDescrP elmdscr = elem.ExtractElementDescr ();

                if (nullptr != elmdscr)
                    MSElementDescr::InitOrAddToChainWithTail (&outputDescr, &tailDescr, elmdscr);
                }

            // FUTUREWORK: This isn't valid...an EditElementHandle should never be created from an edP chain, this method should return an ElementAgenda!!!
            outElemHandle.SetElementDescr (outputDescr, true, false);

            return true;
            }
        }

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsBmfCadElement (ElementHandleR elemHandle)
    {
    if (CELL_HEADER_ELM != elemHandle.GetElementType())
        return  false;

    // PowerPID elements can have empty cells.
    static const int    BmfMsCadElementMajorID = 22311;
    ElementHandle::XAttributeIter      xAttrIter (elemHandle);

    while (xAttrIter.IsValid())
        {
        if (BmfMsCadElementMajorID == xAttrIter.GetHandlerId().GetMajorId())
            return  true;

        xAttrIter.ToNext();
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 DescriptorContainsGraphics (ElementHandleR elemHandle, ConvertFromDgnContext& context)
    {
    if (MESH_HEADER_ELM == elemHandle.GetElementType())
        return true;

    MSElementDescrCP        pDescr = elemHandle.GetElementDescrCP ();
    if (pDescr->h.isHeader)
        {
        // allow empty PowerPID cells
        if (context.IsBmfCadElement (elemHandle))
            return  true;

        for (ChildElemIter child (elemHandle, ExposeChildrenReason::Count); (child.PeekElementDescrCP() && child.IsValid()); child=child.ToNext())
            {
            if (DescriptorContainsGraphics (child, context))
                return true;
            }

        return false;
        }
    else
        {
        if (pDescr->el.ehdr.isGraphics)
            {
            switch (pDescr->el.hdr.dhdr.props.b.elementClass)
                {
                case DgnElementClass::Construction:
                case DgnElementClass::ConstructionRule:
                    return Construction_Omit != context.GetSettings().GetConstructionClassMapping();

                default:
                    return true;
                }
            }
        return false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsLightCell (ElementHandleCR cellElem)
    {
    WChar       cellName[MAX_CELLNAME_LENGTH];
    if (BSISUCCESS == CellUtil::ExtractName(cellName, _countof(cellName), cellElem))
        {
        return  0 == wcscmp (cellName, NAME_DistanceLight) ||
                0 == wcscmp (cellName, NAME_PointLight) ||
                0 == wcscmp (cellName, NAME_SpotLight) ||
                0 == wcscmp (cellName, NAME_AreaLight) ||
                0 == wcscmp (cellName, NAME_SkyLight);
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IgnoreElement (ElementHandleR elemHandle)
    {
    MSElementCP     pElem = elemHandle.GetElementCP();

    switch (pElem->ehdr.type)
        {
        case CELL_HEADER_ELM:
            if (!DescriptorContainsGraphics (elemHandle, *this))
                {
                DIAGNOSTIC_PRINTF ("Ignoring Cell with no graphics\n");
                return true;
                }

            if (!this->GetSettings().CreateDWGLights() && IsLightCell(elemHandle))
                {
                DIAGNOSTIC_PRINTF ("Ignoring Light Cell\n");
                return true;
                }
            break;

        case GROUP_DATA_ELM:
            if (1 == pElem->ehdr.level)
                return true;            // Color Table.
            break;

        case MICROSTATION_ELM:
            {
            if (MSAPPINFO_LEVEL == pElem->ehdr.level)
                {
                switch (pElem->applicationElm.signatureWord)
                    {
                    case 182:                                           // Always save Geographic coordinate data.
                        return false;

                    default:
                        return !this->GetIsSavingApplicationData();     // For arbitrary application elements, save only if user has chosen this setting.
                    }
                }
            break;
            }
        case RASTER_REFERENCE_ELM:
            {
            //Only process type 94 elements
            return true;
            }

        case RASTER_HIERARCHY_ELM:
            {
            //Only process type 94 elements
            return true;
            }

        case TABLE_ENTRY_ELM:
            switch (pElem->ehdr.level)
                {
                case MS_DIMSTYLE_TABLE_LEVEL:       // Active Dimension style handled seperately (extracted to database variables).
                    if (pElem->ehdr.level  && DIMSTYLE_ACTIVE_STYLE_ID == ((TableEntryElm *) pElem)->entryHdr.entryId)
                        return true;
                    break;

                case MS_TEXTSTYLE_TABLE_LEVEL:      // Unnamed text style??
                    TextStyleTableElm *pStyleElem = (TextStyleTableElm*) pElem;

                    // ignore unnamed styles except for those that are used by linestyle
                    if (0 == pStyleElem->styleName[0] && !pStyleElem->textStyle.flags.acadShapeFile)
                        return true;

                    break;
                }
            break;

       case DGNSTORE_HDR:
            {
            // tagsets are processed via tags
            if (DgnStoreHdrHandler::IsDgnStoreElement(elemHandle, TAGSET_ID, TAGID_BSI))
                return  true;
            break;
            }

        case EXTENDED_ELM:
            {
            // NamedViewDisplayHandler is not supported
            NamedViewDisplayHandler*    handler = dynamic_cast <NamedViewDisplayHandler*> (&elemHandle.GetHandler());
            if (NULL != handler)
                return  true;
            break;
            }

        case VIEW_GROUP_ELM:
        case TABLE_ELM:
            {
            // these elements are handled separately
            return  true;
            }
        }

    // Ignore graphic elements off design plane.  - TR# 130446
    if (pElem->ehdr.isGraphics)
        {
        DRange3d       dRange;
        DataConvert::ScanRangeToDRange3d (dRange, pElem->hdr.dhdr.range);
        if (RealDwgUtil::IsPointOffDesignPlane (&dRange.low, pElem->hdr.dhdr.props.b.is3d) || 
            RealDwgUtil::IsPointOffDesignPlane (&dRange.high, pElem->hdr.dhdr.props.b.is3d))
            {
            DIAGNOSTIC_PRINTF ("Ignoring Element: %d ID: %d - Off Design Plane\n", pElem->ehdr.type, pElem->ehdr.uniqueId);
            return true;
            }

        // ignore control elements from dropped standard surface cell
        if (pElem->hdr.dhdr.props.b.invisible && StandardSurfaceBaseHandler::IsStandardSurface(elemHandle))
            return  true;
        }

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* Return true if this is a hatch region
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsHatchRegion (MSElementCP pElem)
    {
    return CELL_HEADER_ELM == pElem->ehdr.type &&
           NULL != linkage_extractFromElement (NULL, pElem, LINKAGEID_AssocRegion, NULL, NULL, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ShouldDropFailedElement (ElementHandleR elemIn)
    {
    MSElementCP     element = elemIn.GetElementCP ();
    if (!element->ehdr.isGraphics)
        return false;
    if (element->ehdr.type == BSPLINE_CURVE_ELM)
        return false;
    if (element->ehdr.type == BSPLINE_SURFACE_ELM)
        return false;
    if (element->ehdr.type == MESH_HEADER_ELM)
        return false;
    if (element->ehdr.type == SHAREDCELL_DEF_ELM)
        return false;

    /*-------------------------------------------------------------------------------------------------------------------------------
    A workaround for a current problem where a shared cell containing an assoc region when merged & clipped from a reference file, 
    the assoc region's child elements do not have correct coordinates.  After the shared cell gets merged & clipped, it is replaced
    with a new cell (type 2 or type 34 depending on merge option) containing unclipped elements.  All elements of the cell are indeed
    transformed to target model, but the assoc region's dependency ID's remain unchanged: they still point to the original roots that 
    are in the shared cell definition, instead of pointing to the new elements that are not part of the cell.  The original shared 
    cell definition is not transformed.  Somehow, dependency manager/assoc region handler resets the assoc region's child elements in 
    the clipped cell to the original untransformed state as they are in the shared cell definition.
    
    A correct fix ought to be in dependency manager and/or assoc region code which may need a much involved change.  For Imtech's 
    immediate request, documented as the 2nd issue in TR 318341, we check for assoc region's root elements. If any root is a child 
    whose outermost parent is a shared cell def, but not the same as that of the assoc region's, we do not attempt to drop assoc 
    region.  We simply ignore it, avoiding creating bad dropped entities in DWG.
    
    Of course, this workaround should be removed after a fix in dependency manager and/or assoc region has been made in the future.
    -------------------------------------------------------------------------------------------------------------------------------*/
    if (IsHatchRegion(element))
        {
        ElementRefP elementRef = elemIn.GetElementRef ();
        if (NULL == elementRef)
            return false;

        ElementRefP hatchParent = elementRef->GetOutermostParent ();
        if (NULL == hatchParent)
            return  true;

        int         parentType = hatchParent->GetElementType ();
        if (CELL_HEADER_ELM == parentType || SHAREDCELL_DEF_ELM == parentType)
            {
            // we only care about model sections.
            DgnModelP   dgnModel = elemIn.GetDgnModelP ();
            if (NULL == dgnModel)
                return  true;

            // extract root element ID's
            ElementIdArray  depIds;
            DependencyManagerLinkage::GetRootElementIdsInMSElement (depIds, element, DEPENDENCYAPPID_AssocRegion, 0/*TYPE_REGIONBOUNDARY*/);
            if (depIds.size() <= 0)
                DependencyManagerLinkage::GetRootElementIdsInMSElement (depIds, element, DEPENDENCYAPPID_AssocRegion, 1/*TYPE_ROOTBOUNDARY*/);

            // do not drop the assoc region if a root's outermost parent does not match its own:
            for each (ElementId depId in depIds)
                {
                if (NULL != (elementRef = dgnModel->FindElementByID(depId)) && hatchParent != elementRef->GetOutermostParent())
                    return  false;
                }
            }
        }

    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 DontSaveBasedOnClass (MSElementCP pEl, IDwgConversionSettings& settings)
    {
    if (!pEl->ehdr.isGraphics)
        return false;

    switch (pEl->hdr.dhdr.props.b.elementClass)
        {
        case DgnElementClass::Construction:
        case DgnElementClass::ConstructionRule:
            if (Construction_Omit == settings.GetConstructionClassMapping())
                return true;
            break;

        case DgnElementClass::PatternComponent:
            if (Pattern_Omit == settings.GetPatternClassMapping())
                return true;
            break;

        case DgnElementClass::LinearPatterned:
            if (LinearPatterned_Omit== settings.GetLinearPatternedClassMapping())
                return true;
            break;
        }
    return false;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveDefaultModelViewGroupToDwg (ElementHandleCR inElement)
    {
    DgnModelP       defaultModel = NULL;
    DgnFileP        dgnFile = inElement.GetDgnFileP ();
    if (NULL == dgnFile || NULL == (defaultModel = dgnFile->FindLoadedModelById(dgnFile->GetDefaultModelId())))
        return  BadModel;

    ViewGroupPtr    activeViewgroup = dgnFile->GetViewGroups().FindByElementId (inElement.GetElementId());
    if (!activeViewgroup.IsValid())
        return  MstnElementUnacceptable;

    return this->SaveViewportTableToDwg (m_pFileHolder->GetDatabase(), *activeViewgroup.get(), defaultModel->GetModelInfo(), true);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveElementToDatabase (ElementHandleR elemHandle, AcDbObjectId* acObjectId, PrioritySorter* pPrioritySorter)
    {
    AcDbObjectId            objectId;

    if (NULL == acObjectId)
        acObjectId = &objectId;

    acObjectId->setNull();

    MSElementCP             element;
    if (NULL == (element = elemHandle.GetElementCP()))
        return BSIERROR;

    // some elements (a small amount) might have been pre-saved to DWG.
    if (this->IsElementPreSaved(acObjectId, element->ehdr.uniqueId))
        return  BSISUCCESS;

    /*---------------------------------------------------------------------------------------------------------
    Currently a File-SaveSettings alone does not set the Default model dirty when editing a DWG file, such that 
    SaveModelInfoToDatabase would not get called at all and the active viewport would not get updated.  We try
    to overcome that problem by saving the active view group of the Default model if the only change made in
    the file is File->SaveSettings.
    ---------------------------------------------------------------------------------------------------------*/
    if (m_savingChanges)
        {
        if (VIEW_GROUP_ELM == element->ehdr.type && element->ehdr.uniqueId == this->GetActiveViewGroupId() && m_model->IsDictionaryModel())
            return  this->SaveDefaultModelViewGroupToDwg (elemHandle);
        else if (TABLE_ELM == element->ehdr.type)
            return  this->SaveTableChangesToDwg (elemHandle);
        }

    if (this->IgnoreElement(elemHandle))
        {
        DIAGNOSTIC_PRINTF ("Ignoring Element Type: %d, Level: %d\n", element->ehdr.type, element->ehdr.level);
        return SUCCESS;
        }

    NOISYDEBUG_PRINTF ("Checking In Element: %I64d, Type: %d\n", element->ehdr.uniqueId, element->ehdr.type);
    this->ReportProgress (0.0);

    _OnPreSaveElementIntoCache (elemHandle);

    try
        {
        AcDbObjectId    existingObjectId;
        bool            existingObjectErased = false;
        if (m_savingChanges && !(existingObjectId = this->ExistingObjectIdFromElementId (element->ehdr.uniqueId)).isNull() && !(existingObjectErased = existingObjectId.isErased()))
            {
            // This is the case where we started with a DWG Entity and we're attempting to save the corresponding DGN Element back to DWG.
            AcDbSmartObjectPointer<AcDbObject> acObject (existingObjectId, AcDb::kForWrite);
            if (Acad::eOnLockedLayer == acObject.openStatus())
                {
                // we unlock all the layers before we start saving objects, and then relock them at the end, so this should never happen.
                BeAssert (false && L"Opening entity on locked layer!");
                DIAGNOSTIC_PRINTF ("Cannot update object for element ID=%I64d, type=%d, because its layer is locked!\n", element->ehdr.uniqueId, element->ehdr.type);
                return  SUCCESS;
                }

            BeAssert (Acad::eOk == acObject.openStatus() && L"Cannot open existing entity for update!");
            if (Acad::eOk != acObject.openStatus())
                return BSIERROR;

            if (RealDwgSuccess == this->UpdateObjectFromElement (acObject, elemHandle))
                {
                if (Acad::eOk == acObject.openStatus() && acObject->objectId().isValid())
                    *acObjectId = acObject->objectId();
                else
                    *acObjectId = existingObjectId;

                return BSISUCCESS;
                }
            else
                {
                // no default failure handling for object update
                return  BSIERROR;
                }
            }

        // we get here if:
        //  1. It's an element descriptor for which there is no corresponding AcDbObject in the AcDbDatabase we're saving to.
        //  2. The existing object was deleted because it is now incorporated into a complex. This happens, for example, when the "group" command is used.
        if (DontSaveBasedOnClass (element, GetSettings()))
            return SUCCESS;

        // handle some special cases that apply to multiple element types.
        EditElementHandle              preprocessed;
        if (this->PreprocessUnsupportedLineStyles (preprocessed, elemHandle))
            {
            this->m_dropUnsupportedLineStyles = false;          // TR# 176294 - Infinite loop if unsupported style by level.
            this->SaveElementChainToDatabase (preprocessed, &elemHandle, pPrioritySorter);
            // note: no need to save and restore m_dropUnsupportedLineStyles, we know that PreprocessUnsupporteLineStyles can only return true if m_dropUnsupportedLineStyles is true.
            this->m_dropUnsupportedLineStyles = true;
            return BSISUCCESS;
            }
        else if (this->ShouldDropPattern(elemHandle))
            {
            // drop patterned element at this early stage avoids having to handle all complicated cases of entity sorting!
            if (RealDwgSuccess == this->DropGraphicsToBlock(nullptr, elemHandle, DropGraphics::OPTION_Patterns))
                return  BSISUCCESS;
            else
                return  BSIERROR;
            }

        // if we got here, we have an element that needs to be saved, but there is no existing object in the DWG database.
        PrioritySorter*     currentSorter = this->GetPrioritySorter ();
        this->SetPrioritySorter (pPrioritySorter);
        AcDbObjectP         createdObject = nullptr;
        RealDwgStatus       createStatus = this->CreateObjectFromElement (createdObject, elemHandle);
        this->SetPrioritySorter (currentSorter);

        // save the new object into database if not already saved:
        if (createStatus == RealDwgSuccess)
            {
            // if we choose to ignore the element, we don't need to go any further.
            if (NULL == createdObject)
                return  RealDwgSuccess;

            AcDbObjectId    createdObjectId = createdObject->objectId();

            // does the new object have to be added to the database?
            if (createdObjectId.isNull())
                {
                // the created object should always be an entity, and the owner should always the current block. Any other case must be handled by the ToDwgExtension::ToObject method.
                AcDbEntityP         createdEntity = AcDbEntity::cast (createdObject);
                if (NULL == createdEntity)
                    {
                    DIAGNOSTIC_PRINTF ("Attempting to add entity type=%ls has failed for element ID=%I64d, type=%d\n",  createdObject->isA()->name(), element->ehdr.uniqueId, element->ehdr.type);
                    return BSIERROR;
                    }

                AcDbObjectId        ownerId       = this->GetCurrentBlockId();
                AcDbBlockTableRecordPointer blockTableRecord (ownerId, AcDb::kForWrite);
                if (Acad::eOk == blockTableRecord.openStatus())
                    *acObjectId = this->AddEntityToBlock (blockTableRecord, createdEntity, existingObjectErased ? 0 : element->ehdr.uniqueId);
                else
                    DIAGNOSTIC_PRINTF ("Failed to open current block to add new entity ID=%I64d, type=%d\n", element->ehdr.uniqueId, element->ehdr.type);
                }
            else
                {
                *acObjectId = createdObjectId;
                }

            if (NULL != createdObject && createdObject->objectId().isValid())
                createdObject->close();

            return  createStatus;
            }

        /*------------------------------------------------------------------------------------------------------------
        If we get here, we have failed to create a new object from the input element.  While we expect each element's 
        ToDwg to handle it's own object creation failure, we have this default handling here for two reasons:
            1)an object enabler that did not handle its own element's failure
            2)any element that needs to be dropped so we don't have to duplicate the drop code everywhere
        ------------------------------------------------------------------------------------------------------------*/
        if (DropFailed != createStatus && ReplacedObjectType != createStatus && ConversionInChildrenProc != createStatus && ShouldDropFailedElement(elemHandle))
            {
            DropGeometry    dropAll ((DropGeometry::Options)(0xFFFF ^ DropGeometry::OPTION_Text));
            return  this->DropElementToDwg (createdObject, NULL, elemHandle, dropAll);
            }
        }


    catch (RealDwgException& exception)
        {
        DIAGNOSTIC_PRINTF ("Exception Thrown on Element: %I64d, Type: %d [%ls]\n", element->ehdr.uniqueId, element->ehdr.type, exception.ErrorMessage());
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception Thrown on Element: %I64d, Type: %d\n", element->ehdr.uniqueId, element->ehdr.type);
        }


    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveElementChainToDatabase (ElementHandleR elemHandle, ElementHandleCP originalElem, PrioritySorter* pPrioritySorter)
    {
    // Try to check in the elements in the chain as individual entities.
    // NOTE: This is a misuse ElemHandles. They are not designed to store an element descriptor chain.

    /*-----------------------------------------------------------------------------------
    Set priority sorter:
        1. Use the same base primary sorting position as the original element.
        2. Use relative pos as a secondary sort order for dropped elements.
        3. Reset base primary sorting position to 0 at the end of addding dropped elements.
    -----------------------------------------------------------------------------------*/
    if (NULL != pPrioritySorter && NULL != originalElem)
        {
        ElementRefP     originalElemref = originalElem->GetElementRef ();
        pPrioritySorter->SetBaseElementPosition (NULL == originalElemref ? 0 : originalElemref->GetFilePos());
        }
    Int32               secondaryPos = 0;
    MSElementDescrCP    pElmDscr   = elemHandle.GetElementDescrCP();
    for (MSElementDescrP pTmpDscr = const_cast <MSElementDescrP> (pElmDscr); NULL != pTmpDscr; pTmpDscr = pTmpDscr->h.next)
        {
        // isolate the element descriptor
        MSElementDescrP     pNext   = pTmpDscr->h.next;
        MSElementDescrP     pHeader = pTmpDscr->h.myHeader;
        pTmpDscr->h.next = pTmpDscr->h.myHeader = NULL;

        ElementHandle   tmpHandle (pTmpDscr, false, false, NULL == pTmpDscr->h.dgnModelRef ? m_model : pTmpDscr->h.dgnModelRef);
        AcDbObjectId    objectId;
        SaveElementToDatabase (tmpHandle, &objectId, pPrioritySorter);

        // dropped elements should be sorted relative to root element's position:
        if (NULL != pPrioritySorter && objectId.isValid())
            pPrioritySorter->AddElement (elemHandle, secondaryPos++, this->ElementIdFromObjectId(objectId));

        pTmpDscr->h.myHeader    = pHeader;
        pTmpDscr->h.next        = pNext;
        }

    if (NULL != pPrioritySorter && NULL != originalElem)
        pPrioritySorter->SetBaseElementPosition (0);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveTableChangesToDwg (ElementHandleCR tableElement)
    {
    // this method is only called to save changes made in a table element, which does not have an element handler.
    switch (tableElement.GetElementCP()->ehdr.level)
        {
        case MS_LSTYLE_DEF_LEVEL:
            return  this->SaveDgnLineStylesToDatabase ();
        case MS_TEXTSTYLE_TABLE_LEVEL:
            return  this->SaveDgnTextStylesToDatabase ();
        case MS_DIMSTYLE_TABLE_LEVEL:
            return  this->SaveDgnDimensionStylesToDatabase ();
        case MS_MATERIAL_PALETTE_LEVEL:
            return  this->SaveDgnMaterialsToDatabase ();
        case MS_MLINESTYLE_TABLE_LEVEL:
            return  this->SaveDgnMultilineStylesToDatabase ();
        case MS_FILTER_TABLE_LEVEL:
            return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (this->SaveLayerFiltersToDatabase());
        case MS_REGAPP_TABLE_LEVEL:
            this->SaveRegisteredApplicationsToDatabase ();
            break;
        }
    
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::ExistingObjectIdFromDBHandle (AcDbHandle dbHandle) const
    {
    AcDbObjectId    objectId;
    if (Acad::eOk == m_pFileHolder->GetDatabase()->getAcDbObjectId (objectId, false, dbHandle, 0))
        return objectId;

    AcDbObjectId    nullId;
    return nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    ConvertFromDgnContext::ExistingObjectIdFromElementId (ElementId elementId, const AcRxClass* requestedClass) const
    {
    AcDbObjectId    objectId;
    if (Acad::eOk == m_pFileHolder->GetDatabase()->getAcDbObjectId (objectId, false, this->DBHandleFromElementId (elementId), 0))
        {
#ifdef DEBUG_EXISTING_OBJECTTYPE
        printf ("Existing object type for elementId=%I64d found: %ls\n", elementId, objectId.objectClass()->name());
#endif
        if (nullptr == requestedClass || objectId.objectClass()->isDerivedFrom(requestedClass))
            return  objectId;
        }

    return AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::ExistingObjectIdFromElement (MSElementCP pElement) const
    {
    return this->ExistingObjectIdFromElementId (pElement->ehdr.uniqueId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CheckObjectError (RealDwgStatus status, AcDbObjectP acObject, ElementHandleCR elemHandle)
    {
#if defined (REALDWG_DIAGNOSTICS)
    WString     typeName;
    elemHandle.GetHandler().GetTypeName (typeName, 100);
    ElementId   id = elemHandle.GetElementCP()->ehdr.uniqueId;
#endif

    switch (status)
        {
        case DwgObjectChangesIgnored:
#ifdef REALDWG_NOISY_DEBUG
            if (NULL != acObject)
                printf ("Changes to Object: %ls intentionally ignored\n", acObject->isA()->name());
            else
                printf ("Intentionally ignored changes to element type %ls, ID: %I64d\n", typeName.c_str(), id);
#endif
            status = RealDwgSuccess;
            break;

        case DwgObjectUnsupported:
            // For an object that doesn't support import, just leave it unchanged.
#ifdef REALDWG_DIAGNOSTICS
            if (NULL != acObject)
                printf ("Tobject not supported for Object: %ls, Object not updated to reflect element change\n", acObject->isA()->name());
            else
                printf ("ToObject ignored unsupported element type %ls, ID=%I64d\n", typeName.c_str(), id);
#endif
            status = RealDwgSuccess;
            break;

        case ReplacedObjectType:
#ifdef REALDWG_DIAGNOSTICS
            if (NULL != acObject)
                printf ("Object type replaced. Deleting old type %ls.\n", acObject->isA()->name());
            else
                printf ("Object type replaced when converting %ls, ID=%I64d\n", typeName.c_str(), id);
#endif
            status = RealDwgSuccess;
            break;

        case RealDwgIgnoreElement:
#ifdef REALDWG_DIAGNOSTICS
            if (NULL != acObject)
                printf ("Intentionally ignored object ID=%I64d, type %ls.\n", id, acObject->isA()->name());
            else
                printf ("Intentionally ignored element ID=%I64d, type = %ls\n", id, typeName.c_str());
#endif
            status = RealDwgSuccess;
            break;
        
        default:
#if defined (REALDWG_DIAGNOSTICS)
            // Unexpected error.
            printf ("Error saving element type %ls, ID=%I64d\n", typeName.c_str(), id);
#endif
            break;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::UpdateObjectFromElement (AcDbObjectP existingObject, ElementHandleR elemHandle)
    {
    try
        {
        // the element is responsible for saving itself to the DWG file.
        ToDwgExtension* toDwg = ToDwgExtension::Cast (elemHandle.GetHandler());
        if (NULL == toDwg)
            {
            BeAssert (false && "No ToDwgExtension found for this element");
            return EntityError;
            }

        RealDwgStatus   status;
        AcDbObjectP     resultObject = NULL;
        if (RealDwgSuccess == (status = toDwg->ToObject (elemHandle, resultObject, existingObject, *this)))
            {
            AcDbEntityP resultEntity = AcDbEntity::cast (resultObject);
            if (RealDwgUtil::CanPostSetEntityProperties(resultEntity))
                this->UpdateEntityPropertiesFromElement (resultEntity, elemHandle);

            // if the result is a new object, even it was handed over from the existing object, we have to close it:
            if (existingObject != resultObject && NULL != resultObject)
                resultObject->close ();
            }
        else
            {
            status = CheckObjectError(status, existingObject, elemHandle);
            // ToObject might have handed the existing object over to the new object but failed later.  Hand the ID back as the caller might attempt to use it:
            if (RealDwgSuccess != status && NULL != resultObject && resultObject->objectId().isValid() && NULL != existingObject && !existingObject->objectId().isValid())
                resultObject->handOverTo (existingObject);
            }
        return status;
        }

    catch (RealDwgException& exception)
        {
        DIAGNOSTIC_PRINTF ("Exception caught Extracting Object: %ls From Element: %d, ID: %I64d [%ls]\n", existingObject->isA()->name(), elemHandle.GetElementType(), elemHandle.GetElementCP()->ehdr.uniqueId, exception.ErrorMessage());
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught Extracting Object: %ls From Element: %d, ID: %I64d\n", existingObject->isA()->name(), elemHandle.GetElementType(), elemHandle.GetElementCP()->ehdr.uniqueId);
        }

    return ExceptionCaught;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::CreateObjectFromElement (AcDbObjectP& newObject, ElementHandleR elemHandle)
    {
    try
        {
        // the element is responsible for saving itself to the DWG file.
        ToDwgExtension* toDwg            = ToDwgExtension::Cast (elemHandle.GetHandler());

        if (NULL == toDwg)
            {
            BeAssert (false && L"Missing ToDwgExtension from the element handler!");
            return EntityError;
            }

        RealDwgStatus   status;
        if (RealDwgSuccess == (status = toDwg->ToObject (elemHandle, newObject, NULL, *this)))
            {
            AcDbEntity* newEntity = AcDbEntity::cast (newObject);
            if (RealDwgUtil::CanPostSetEntityProperties(newEntity))
                this->UpdateEntityPropertiesFromElement (newEntity, elemHandle);
            }
        else
            {
            if (NULL != newObject)
                {
                if (newObject->objectId().isValid())
                    newObject->erase ();
                else
                    delete newObject;
                newObject = NULL;
                }
            status = CheckObjectError(status, NULL, elemHandle);
            }
        return status;
        }

    catch (RealDwgException& exception)
        {
        DIAGNOSTIC_PRINTF ("Exception caught Creating Object from Element: %d, ID: %I64d [%ls]\n", elemHandle.GetElementType(), elemHandle.GetElementCP()->ehdr.uniqueId, exception.ErrorMessage());
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught Creating Object from Element: %d, ID: %I64d\n", elemHandle.GetElementType(), elemHandle.GetElementCP()->ehdr.uniqueId);
        }

    return ExceptionCaught;
    }

/*---------------------------------------------------------------------------------**//**
* Get a symbology template for this element (Cell Headers are not valid templates).
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
MSElementDescrCP            ConvertFromDgnContext::GetSymbologyTemplate (MSElementDescrCP pDescr)
    {
    int     type = pDescr->el.ehdr.type;

    // If this is a compound type, attempt to use a child as a symbology template.
    // The header is not reliable.
    if (CELL_HEADER_ELM == type ||
        SOLID_ELM == type ||
        SURFACE_ELM == type||
        CMPLX_SHAPE_ELM == type ||
        CMPLX_STRING_ELM == type ||
        SHAREDCELL_DEF_ELM == type)
        {
        MSElementDescrCP       pChildDescr;

        for (pChildDescr = pDescr->h.firstElem; NULL != pChildDescr; pChildDescr = pChildDescr->h.next)
            {
            MSElementDescrCP        pTemplate;

            if (NULL != (pTemplate = this->GetSymbologyTemplate (pChildDescr)))
                return pTemplate;
            }
        return NULL;
        }
    else
        {
        return pDescr->el.ehdr.isGraphics ? pDescr : NULL;
        }
    }

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
+===============+===============+===============+===============+===============+======*/
struct QueryElementThickness : public IQueryProperties
{
private:
    bool            m_hasThickness;
    bool            m_hasCaps;
    bool            m_alwaysUseDirection;
    double          m_thickness;
    DVec3d          m_extrusionDirection;

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
QueryElementThickness::QueryElementThickness ()
    {
    m_hasThickness = false;
    m_hasCaps = false;
    m_alwaysUseDirection = false;
    m_thickness = 0.0;
    m_extrusionDirection.zero ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
void            _EachThicknessCallback (EachThicknessArg& args) override
    {
    if (0 != (args.GetPropertyFlags () & PROPSCALLBACK_FLAGS_ElementIgnoresID))
        return;

    m_thickness = args.GetStoredValue ();
    m_hasCaps = args.GetCapped ();
    m_alwaysUseDirection = args.GetAlwaysUseDirection ();
    args.GetDirection (m_extrusionDirection);

    m_hasThickness = (0.0 != m_thickness);
    }

virtual ElementProperties   _GetQueryPropertiesMask () override {return ELEMENT_PROPERTY_Thickness; }
bool            GetHasThickness () { return m_hasThickness; }
bool            GetHasCaps () { return m_hasCaps; }
bool            GetAlwaysUseDirection () { return m_alwaysUseDirection; }
double          GetThickness () { return m_thickness; }
DVec3dCR        GetExtrusionDirection () { return m_extrusionDirection; }

}; // QueryElementThickness

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::GetEntityThicknessFromElementLinkage
(
double&                     newThickness,
DPoint3dR                   newNormal,
DPoint3dCP                  pCurrentNormal,
ElementHandleCR             elemHandle
)
    {
    QueryElementThickness   thicknessProp;
    PropertyContext         propContext (&thicknessProp);

    elemHandle.GetHandler().QueryProperties (elemHandle, propContext);

    if (thicknessProp.GetHasThickness())
        {
        // get thickness
        newThickness = thicknessProp.GetThickness() * this->GetScaleFromDGN ();
        // get extrusion direction
        newNormal = thicknessProp.GetExtrusionDirection ();

        // transform thickness direction if needed
        DPoint3d            thicknessDirection = newNormal;
        this->GetLocalTransform().MultiplyMatrixOnly (thicknessDirection);
        if (NULL != pCurrentNormal)
            {
            double  dotProduct = thicknessDirection.DotProduct (*pCurrentNormal), nearOne = .999999;
            if (dotProduct < -nearOne)
                {
                newThickness = - newThickness;
                newNormal    = *pCurrentNormal;
                }
            else if (dotProduct > nearOne)
                {
                newNormal = *pCurrentNormal;
                }
            }
        return true;
        }

    newThickness = 0.0;
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/01
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsElementFilled
(
ElementHandleCR             elemHandle,
UInt32*                     pFillColor,
UInt32*                     pOutlineColor,
GradientSymb*               pGradientSymb
)
    {
    IAreaFillPropertiesQuery*   areaFill = dynamic_cast <IAreaFillPropertiesQuery*> (&elemHandle.GetHandler());
    if (NULL == areaFill)
        return  false;

    if (NULL != pOutlineColor)
        *pOutlineColor = elemHandle.GetElementCP()->hdr.dhdr.symb.color;

    // first check for gradient fill
    GradientSymbPtr gradientSymb;

    if (areaFill->GetGradientFill (elemHandle, gradientSymb))
        {
        if (NULL != pGradientSymb)
            pGradientSymb->CopyFrom (*gradientSymb);

        return true;
        }

    // then check for solid fill
    UInt32                  fillColor = 0;
    bool                    alwaysFilled = false;
    if (areaFill->GetSolidFill(elemHandle, &fillColor, &alwaysFilled))
        {
        if (NULL != pFillColor)
            *pFillColor = fillColor;
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::IsFilledOrPatterned
(
ElementHandleCR     inElement,          // => input element to check
bool*               isSolidFilled,      // <= has solid fill
bool*               isGradientFilled,   // <= has gradient fill
bool*               isOutlined,         // <= display outline if solid or gradient filled
bool*               isPatterned,        // <= has pattern
UInt32*             solidFillColor,     // <= fill color if solid filled
UInt32*             outlineColor,       // <= element color if outline filled
GradientSymb*       gradientSymb        // <= gradient data if gradient filled
)
    {
    bool        solidFill, outlineFill, gradientFill, patterned;
    UInt32      fillColor, outlineElemColor;

    if (NULL == isSolidFilled)
        isSolidFilled = &solidFill;
    if (NULL == isOutlined)
        isOutlined = &outlineFill;
    if (NULL == isGradientFilled)
        isGradientFilled = &gradientFill;
    if (NULL == isPatterned)
        isPatterned = &patterned;
    if (NULL == solidFillColor)
        solidFillColor = &fillColor;
    if (NULL == outlineColor)
        outlineColor = &outlineElemColor;

    *isSolidFilled = *isOutlined = *isGradientFilled = *isPatterned = false;
    *solidFillColor = *outlineColor = 0;

    IAreaFillPropertiesQuery*   areaQuery = dynamic_cast<IAreaFillPropertiesQuery*>(&inElement.GetHandler());
    if (NULL != areaQuery)
        {
        outlineElemColor = inElement.GetElementCP()->hdr.dhdr.symb.color;

        GradientSymbPtr     gradientParams;
        *isGradientFilled = areaQuery->GetGradientFill (inElement, gradientParams);
        if (*isGradientFilled && NULL != gradientSymb)
            gradientSymb->CopyFrom (*gradientParams.get());

        bool                alwaysFill = false;
        *isSolidFilled = areaQuery->GetSolidFill (inElement, solidFillColor, &alwaysFill);

        // find if the element boundary is displayed or not
        if (*isSolidFilled)
            *isOutlined = *solidFillColor != *outlineColor;
        else if (*isGradientFilled)
            *isOutlined = gradientParams->GetFlags() != (UInt32)FillDisplay::Never;

        PatternParamsPtr    patternParams;
        *isPatterned = areaQuery->GetPattern (inElement, patternParams, NULL, NULL, 0);
        }

    return  *isSolidFilled || *isGradientFilled || *isPatterned;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::ShouldSeperateFillAndPattern (bool isSolidFilled, bool isGradientFilled, bool isPatterned)
    {
    /*--------------------------------------------------------------------------------------------------------
    Checks to see if we should seperate a fill from a patterned element.

    A composite hatch that is both filled & patterned has been supported since ACAD R2011.  But only a solid
    fill in a non-background color is allowed.  A gradient fill cannot be used as a SOLID hatch type, hence
    still has to be created as a seperate hatch entity.
    --------------------------------------------------------------------------------------------------------*/
    bool        isCompositePattern = (isSolidFilled || isGradientFilled) && isPatterned;

    if (isCompositePattern && this->GetTargetVersion() > DwgFileVersion_2007)
        return  isGradientFilled;

    return  isCompositePattern;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsInvisibleElement (ElementHandleCR elemHandle)
    {
    MSElementCP    pElem = elemHandle.GetElementCP();

    if (NULL != pElem && pElem->ehdr.isGraphics)
        {
        if (pElem->hdr.dhdr.props.b.invisible)
            return true;

        switch (pElem->ehdr.type)
            {
            case CMPLX_STRING_ELM:
            case CMPLX_SHAPE_ELM:               // Invisible if all components are (TR# 126904)
                {
                for (ChildElemIter child (elemHandle, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
                    {
                    if (!child.GetElementCP()->hdr.dhdr.props.b.invisible)
                        return false;
                    }
                return true;
                }
            }
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
MaterialCP      FindAttachedMaterail (ElementHandleCR inElement, MSElementDescrCP inSymbTemplate, DgnModelP inModel)
    {
    WString             materialName;
    MaterialManagerR    materialManager = MaterialManager::GetManagerR ();

    if (!materialManager.HasExternalAttachment(inElement, materialName))
        {
        // for a simple element, we done
        if (nullptr == inSymbTemplate || (inElement.IsPersistent() && inElement.GetElementId() == inSymbTemplate->el.ehdr.uniqueId))
            return  nullptr;

        ElementHandleCR     firstChild = ChildElemIter (inElement, ExposeChildrenReason::Count);
        if (!firstChild.IsValid())
            return  nullptr;

        // for a complex element, check its symbology template
        if (!materialManager.HasExternalAttachment(ElementHandle(inSymbTemplate, false, false, inModel), materialName))
            {
            // for a cell, also check its first child
            MSElementTypes  type = (MSElementTypes) inElement.GetElementType ();
            if ((CELL_HEADER_ELM != type && SHAREDCELL_DEF_ELM != type) || firstChild.GetElementType() == inSymbTemplate->el.ehdr.type)
                return  nullptr;

            if (!materialManager.HasExternalAttachment(firstChild, materialName))
                return  nullptr;
            }
        }

    if (materialName.empty() || nullptr == inModel)
        return  nullptr;

    // search for material by name
    MaterialList    list;
    if (BSISUCCESS == materialManager.FindMaterialByNameFromAnySource(nullptr, list, materialName.GetWCharCP(), *inModel, false) && list.size() > 0)
        return  list.front();

    return  nullptr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntityMaterial
(
AcDbEntity*                 pEntity,
ElementHandleCR             elemHandle,
MSElementDescrCP            pSymbologyTemplate
)
    {
    MaterialManagerR    materialManager = MaterialManager::GetManagerR ();
    ElementId           materialElementId = materialManager.FindMaterialAttachmentId (elemHandle);

    /*-----------------------------------------------------------------------------------
    For cells, materials may be set on the first child elements.  Since a type 2 cell can
    represent many object types, our strategy is to use the material on the cell header
    if it has one.  Otherwise we use the one from its first child.
    -----------------------------------------------------------------------------------*/
    if ((0 == materialElementId || INVALID_ELEMENTID == materialElementId) &&
        (CELL_HEADER_ELM == elemHandle.GetElementType() || SHAREDCELL_DEF_ELM == elemHandle.GetElementType()) && 
        NULL != pSymbologyTemplate)
        {
        DgnModelRefP    modelRef = NULL == pSymbologyTemplate->h.dgnModelRef ? m_model : pSymbologyTemplate->h.dgnModelRef;
        materialElementId = materialManager.FindMaterialAttachmentId (ElementHandle(pSymbologyTemplate, false, false, modelRef));
        }

    AcDbObjectId        materialObjectId;
    Acad::ErrorStatus   errorStatus = Acad::eNotApplicable;
    MaterialCP          material = nullptr;

    if (0 != materialElementId && INVALID_ELEMENTID != materialElementId)
        {
        // This element is attached with a material: get existing DWG meterial or create a new one from material ID:
        materialObjectId = this->GetOrCreateDwgMaterial (materialElementId);
        if (materialObjectId.isValid())
            errorStatus = pEntity->setMaterial (materialObjectId);
        }
    else if (NULL != pSymbologyTemplate)
        {
        // This element inherits material from level: set bylayer on entity and attach material to the layer as needed:
        LevelHandle     level = m_model->GetLevelCache().GetLevel (pSymbologyTemplate->el.ehdr.level);
        if (level.IsValid() && 0 != (materialElementId = this->GetMaterialIdFromLevel(level)) && INVALID_ELEMENTID != materialElementId)
            {
            // The level of the element has a material attached.  Just set bylayer for the entity - let layer create material:
            materialObjectId = m_pFileHolder->GetDatabase()->byLayerMaterial ();
            }
        else if (nullptr != (material = FindAttachedMaterail(elemHandle, pSymbologyTemplate, m_model)))
            {
            // This element has a material linkage: get or create a material - TFS#19536, TFS#103285
            materialObjectId = this->GetOrCreateDwgMaterial (material);
            }
        else
            {
            // If a material is assigned through a single level with wildcard color, make it bylayer; otherwise make it a direct attachment:
            UInt32          effectiveColor = pSymbologyTemplate->el.hdr.dhdr.symb.color;
            if (COLOR_BYLEVEL == effectiveColor)
                effectiveColor = level.GetByLevelColor().GetColor ();

            material = materialManager.FindMaterialBySymbology (NULL, pSymbologyTemplate->el.ehdr.level, effectiveColor, *m_model, this->UseLevelSymbologyOverrides(), true, NULL);
            if (NULL != material)
                {
                if ((materialObjectId = m_pFileHolder->GetMaterialFromTree(material->GetName().c_str())).isValid() ||
                    (materialObjectId = this->SaveDgnMaterialToDwg(material)).isValid())
                    {
                    AcDbObjectId    layerId = (this->SavingChanges() && level.IsValid()) ? m_pFileHolder->GetLayerByLevelHandle(level) : m_pFileHolder->GetLayerByLevelId(pSymbologyTemplate->el.ehdr.level);
                    if (!layerId.isNull())
                        {
                        // try attaching this material to the layer:
                        AcDbLayerTableRecordPointer     layer(layerId, AcDb::kForWrite);
                        if (Acad::eOk == layer.openStatus())
                            {
                            errorStatus = layer->setMaterialId (materialObjectId);
                            materialObjectId = m_pFileHolder->GetDatabase()->byLayerMaterial ();
                            }
                        }
                    }
                }
            }
        // set entity material (RealDWG does not allow removing material with a null object ID):
        if (materialObjectId.isValid())
            errorStatus = pEntity->setMaterial (materialObjectId);
        }

    if (Acad::eOk != errorStatus && Acad::eNotApplicable != errorStatus)
        DIAGNOSTIC_PRINTF ("Error updating entity material, ID=%I64d. [%ls]\n", elemHandle.GetElementId(), acadErrorStatusText(errorStatus));

    return Acad::eOk == errorStatus ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntityPropertiesFromElement
(
AcDbEntity*                 pEntity,
ElementHandleCR             elemHandle,
int                         headerMask
)
    {
    MSElementCP     pElem = elemHandle.GetElementCP();
    if (NULL != pElem && pElem->ehdr.isGraphics && SAVEELEMENTPURPOSE_ForExtrusion != this->GetSaveElementPurpose())
        {
        LayerClassOverride  layerClassOverride = CLASS_LAYER_None;

#if defined (IGNORE_HATCH_REGION_VISIBILITY)
        //  TR# 157846 - I believe this test was originally added so that we would not misinterpret visibility of regions
        // which had their boundary elements set invisible (as in regular hatches) - but we don't set the header
        // cel invisilble now, so that shouldn't be a problem and with the test we don't test really invisible hatches correctly.
        if (!isHatchRegion (pElem))
#endif
            {
            AcDb::Visibility  visibility = IsInvisibleElement (elemHandle) ? AcDb::kInvisible : AcDb::kVisible;

            switch (pElem->hdr.dhdr.props.b.elementClass)
                {
                case DgnElementClass::Construction:
                    switch (GetSettings().GetConstructionClassMapping())
                        {
                        case Construction_Invisible:
                            visibility = AcDb::kInvisible;
                            break;
                        case Construction_Layer:
                            layerClassOverride = CLASS_LAYER_Construction;
                            break;
                        case Construction_DefPoints:
                            layerClassOverride = CLASS_LAYER_DefPoints;
                            break;
                        }

                    break;

                case DgnElementClass::PatternComponent:
                    switch (GetSettings().GetPatternClassMapping())
                        {
                        case Pattern_Invisible:
                            visibility = AcDb::kInvisible;
                            break;
                        case Pattern_Layer:
                            layerClassOverride = CLASS_LAYER_Pattern;
                            break;
                        case Pattern_DefPoints:
                            layerClassOverride = CLASS_LAYER_DefPoints;
                            break;
                        }
                    break;

                case DgnElementClass::LinearPatterned:
                    switch (GetSettings().GetLinearPatternedClassMapping())
                        {
                        case LinearPatterned_Invisible:
                            visibility = AcDb::kInvisible;
                            break;
                        case LinearPatterned_Layer:
                            layerClassOverride = CLASS_LAYER_LinearPatterned;
                            break;
                        case LinearPatterned_DefPoints:
                            layerClassOverride = CLASS_LAYER_DefPoints;
                            break;
                        }
                    break;
                }

            // Invisible...
            if (visibility != pEntity->visibility())
                pEntity->setVisibility (visibility);
            }

        // if a user has reampped type-2 cell's insert layer, honor his mapping:
        bool isLayerMapped = pElem->ehdr.type == CELL_HEADER_ELM && m_msInsertLayerId.isValid() && m_msInsertLayerId == pEntity->layerId();

        MSElementDescrCP        pSymbologyTemplate = this->GetSymbologyTemplate (elemHandle.GetElementDescrCP());
        this->UpdateEntitySymbologyAndLevelFromElement (pEntity, pSymbologyTemplate, layerClassOverride, isLayerMapped);
        this->UpdateEntityXDataFromLinkagesAndGraphicGroup (pEntity, pElem);
        this->UpdateEntityHyperlink (pEntity, elemHandle);
        this->UpdateEntityMaterial (pEntity, elemHandle, pSymbologyTemplate);

        if (0 != (headerMask & HEADERRESTOREMASK_XAttributes))
            this->UpdateExtensionDictionaryFromXAttributes (pEntity, elemHandle);
        }
    return SUCCESS;
    }

#if defined (FUTUREWORK_LINKPATH_SAVEAS_FILENAME)
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::getOutputFileNameFromModelName
(
WStringR        pOutputName,
WCharCP         pModelName,
WCharCP         pMasterFileName,
OdCodePageId    dwgCodePage,
int             format
)
    {
    ConvertFromDgnContext::ValidateName (pModelName, DwgFileVersion_13, dwgCodePage);

    WString device, directory, name;
    BeFileName::ParseName (device, directory, name, NULL, pMasterFileName);

    size_t dotIndex = name.find (L'.');
    if (dotIndex != name.npos)
        name.replace (dotIndex, 1, L'\0', 1);

    name.append(L"_");
    name.AppendA(modelNameChars);
    BeFileName::BuildName (pOutputName, device.c_str(), directory.c_str(), name.c_str(), DgnFileFormatType::DWG == format ? L"dwg" : L"dxf");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ConvertFromDgnContext::changeLinkPathForModelSavedAsFile
(
AcString        *pPath,
WCharCP         pTargetModelName,
DgnModelP       targetModelRef
)
    {
    DgnModelP       defaultModelRef = INVALID_MODELREF;

    if (!pPath->isEmpty() || INVALID_MODELREF == targetModelRef ||
        SUCCESS != mdlModelRef_getDefaultModelRef(&defaultModelRef, this->GetModel(), false, false, false))
        return  BSIERROR;

    ModelId     defaultModelId  = mdlModelRef_getModelID (defaultModelRef);
    ModelId     targetModelId   = mdlModelRef_getModelID (targetModelRef);
    int         targetModelType = mdlModelRef_getModelType (targetModelRef);

    /*-----------------------------------------------------------------------------------
    If a model gets saved as a separate output file, we must change the link path to the
    new file name for the link to work.
    -----------------------------------------------------------------------------------*/
    if ((DgnModelType::Sheet == targetModelType && this->GetSettings().SaveSheetsToSeparateFiles()) ||
        (DgnModelType::Normal == targetModelType && NONDEFAULT_MODELS_SeperateFiles == dwgSaveSettings_getNonDefaultModelMode() && targetModelId != defaultModelId))
        {
        WString    fileName;

        ConvertFromDgnContext::getOutputFileNameFromModelName (fileName, pTargetModelName, pRootName, this->GetDatabase()->getDWGCODEPAGE(), this->GetFormat());

        *pPath = AcString (fileName);

        return  SUCCESS;
        }

    return  BSIERROR;
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::GetHyperlinkDataFromDesignLink
(
AcString&                   outName,
AcString&                   outTarget,
AcString&                   outPath,
ElementHandleCR             elemHandle
)
    {
    XAttributeHandlerId             handlerID (XATTRIBUTEID_DesignLinks, 0x00);
    ElementHandle::XAttributeIter   xattrIter (elemHandle, handlerID);

    if ( ! xattrIter.IsValid())
        return BSIERROR;

    DgnLinkTreeSpecPtr      treeSpec = DgnLinkManager::CreateTreeSpec (elemHandle);
    if (!treeSpec.IsValid())
        return  BSIERROR;

    // get link tree
    DgnLinkTreePtr          linkTree = DgnLinkManager::ReadLinkTree(*treeSpec.get(), false);
    if (!linkTree.IsValid())
        return  BSIERROR;

    DgnLinkTreeBranchCR     treeRoot = linkTree->GetRoot ();
    if (treeRoot.GetChildCount() < 1)
        return BSIERROR;

    // DWG only allows one hyperlink per object
    DgnLinkTreeNodeCP       treeNode = treeRoot.GetChildCP (0);
    if (NULL == treeNode)
        return  BSIERROR;

    DgnLinkTreeLeafCP       treeLeaf = dynamic_cast <DgnLinkTreeLeafCP> (treeNode);
    if (NULL == treeLeaf)
        return  BSIERROR;

    DgnLinkCP               dgnLink = treeLeaf->GetLinkCP ();
    if (NULL == dgnLink)
        return  BSIERROR;

    // get link name
    WString                 linkName (treeNode->GetName());
    if (!linkName.empty())
        {
        RealDwgUtil::ReplaceBackSlashes (linkName, false);

        // round-trip unknown links
        if (linkName.Equals(NAME_UnknownHyperlink))
            outName.setEmpty();
        else
            outName.assign (linkName.c_str());
        }

    // try to get IP links (url, email, ftp, etc)
    DgnURLLinkCP            urlLink = dynamic_cast <DgnURLLinkCP> (dgnLink);
    if (NULL != urlLink)
        {
        WCharCP             urlAddress = urlLink->GetAddress ();
        if (NULL == urlAddress || 0 == urlAddress[0])
            outPath.setEmpty ();
        else
            outPath.assign (urlAddress);
        return  BSISUCCESS;
        }

    // try to get a file links from all file types: DGN, Word, Excel, PDF, etc
    bool                    isDgnFileTarget = true;
    DgnRegionLinkCP         regionLink = dynamic_cast <DgnRegionLinkCP> (dgnLink);
    DgnFileLinkCP           fileLink = dynamic_cast <DgnFileLinkCP> (dgnLink);
    if (NULL == fileLink)
        {
        if (NULL == regionLink)
            {
            WordHeadingLinkCP   wordHeading = NULL;
            WordBookmarkLinkCP  wordBookmark = NULL;
            ExcelSheetLinkCP    excelSheet = NULL;

            if (NULL != (wordHeading = dynamic_cast <WordHeadingLinkCP> (dgnLink)))
                fileLink = wordHeading->GetFileLinkCP ();
            else if (NULL != (wordBookmark = dynamic_cast <WordBookmarkLinkCP> (dgnLink)))
                fileLink = wordBookmark->GetFileLinkCP ();
            else if (NULL != (excelSheet = dynamic_cast <ExcelSheetLinkCP> (dgnLink)))
                fileLink = excelSheet->GetFileLinkCP ();

            isDgnFileTarget = false;
            }
        else
            {
            fileLink = regionLink->GetFileLinkCP ();

            isDgnFileTarget = true;
            }
        }

    WString                 filePath;
    if (NULL == fileLink)
        {
        DgnGenericLinkCP    genericLink = dynamic_cast <DgnGenericLinkCP> (dgnLink);
        if (NULL != genericLink && genericLink->IsFileBasedLink())
            filePath = genericLink->GetMonikerPtr()->GetSavedFileName ();
        }
    else
        {
        filePath = fileLink->GetMoniker().GetSavedFileName ();
        }

    if (!filePath.empty())
        {
        // since hyperlink does not support region targets of non-DWG types, collapse them to file links.
        if (!isDgnFileTarget)
            {
            // for a none DGN region link, always set the file path
            outPath.assign (filePath.c_str());

            // DWG does not support regions in these files, so change them to file type if not already is
            return  BSISUCCESS;
            }
        else
            {
            // get active DGN file name
            WString         activeFile = this->GetFile()->GetFileName ();

            // leave path link empty if it points to the active file
            if (!DgnFile::IsSameFile(filePath.GetWCharCP(), activeFile.GetWCharCP(), FileCompareMask::All))
                outPath.assign (filePath.c_str());
            }
        }

    // try to get model and view targets
    DgnModelLinkCP      modelLink = NULL;
    bool                isViewTarget = false;
    if (NULL != regionLink)
        {
        WString         targetType = regionLink->GetTargetType ();

        // get view name
        if (targetType.Equals(DGNLINK_REGIONTYPE_View))
            {
            WString     targetName = regionLink->GetTargetName ();
            if (!targetName.empty())
                {
                outTarget.assign (targetName.c_str());
                isViewTarget = true;
                }
            }
        modelLink = regionLink->GetModelLinkCP ();
        }
    else
        {
        modelLink = dynamic_cast <DgnModelLinkCP> (dgnLink);
        }

    // get model name
    if (NULL != modelLink)
        { 
        if (!modelLink->IsTargetValid(NULL))
            return BSIERROR;
        WString             modelName = modelLink->GetModelName ();
        if (!modelName.empty())
            {
            DgnModelRefP    targetModelRef = INVALID_MODELREF;
            ModelId         targetModelId = INVALID_MODELID, linkModelId = this->GetModel()->GetModelId();

            /*---------------------------------------------------------------------------
            Append a model name to target name only under following condition:

            1) target is a sheet model, or
            2) target is a model but it is not of a view type, and
            3) target and link are not in the same model.

            Above rule applies to both target models within the same file as well as those
            across files.  However, when a target model is across to a different file we
            opt to ignore this rule and go ahead to add the extra model name because it is
            not worth of opening the target file just to avoid such an otherwise seemingly
            harmless extra model name.  Here we only apply above rule to models within
            the same file.
            ---------------------------------------------------------------------------*/
            if (!outPath.isEmpty() ||
                (INVALID_MODELID != (targetModelId = m_dgnFile->FindModelIdByName(modelName.GetWCharCP())) &&
#ifndef BUILD_DWGPLATFORM
                 BSISUCCESS == mdlModelRef_createWorking(&targetModelRef, m_dgnFile, targetModelId, false, false) &&
                 (DgnModelType::Sheet == targetModelRef->GetModelType() || !isViewTarget) &&
#endif
                 linkModelId != targetModelId))
                {
                // make sure DGN's default model name to be DWG's required name "Model":
                if (outPath.isEmpty() && targetModelRef->IsDefault())
                    modelName.assign (L"Model");

                outTarget += AcString(L",") + AcString (modelName.c_str());
                }

            /*---------------------------------------------------------------------------
            FUTUREWORK: need to find a way to get user output file name so we can update
            the link target for models saved as separate files.

            this->ChangeLinkPathForModelSavedAsFile (pPath, wBuffer, targetModelRef);
            ---------------------------------------------------------------------------*/

#ifndef BUILD_DWGPLATFORM
            if (INVALID_MODELREF != targetModelRef)
                mdlModelRef_freeWorking (targetModelRef);
#endif
            }
        }

    if (outTarget.isEmpty() && outPath.isEmpty())
        {
        // don't bother to try creating a hyperlink with all empty titles!
        if (outName.isEmpty())
            BSIERROR;

        /*-------------------------------------------------------------------------------
        Handle a special case: if we have gone this far but still have neither a target
        nor a path, it may be a file link without a path:
        TR#239379, TFS#23435
        -------------------------------------------------------------------------------*/
        outPath = outName;
        }

    return  BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::GetHyperlinkDataFromEngineeringLink
(
AcString&                   acTitle,
AcString&                   acUrl,
ElementHandleCR             elemHandle
)
    {
    DgnModelP       model = elemHandle.GetDgnModelP ();
    if (NULL == model)
        return  DGNMODEL_STATUS_BadModelPtr;

    // get tags
    ElementAgenda   tagArray;
    if (TagElementHandler::GetTagsFromSourceElement(&tagArray, elemHandle) < 1)
        return  BSIERROR;

    FOR_EACH (ElementHandleCR tag, tagArray)
        {
        DgnTagSpec  tagSpec;

        // we only need tag name "Internet"?
        if (BSISUCCESS == TagElementHandler::Extract(tagSpec, tag, *model) && 0 == BeStringUtilities::Wcsicmp(tagSpec.set.setName, NAME_TAGSET_INTERNET))
            {
            WString     title, url;

            // get title
            if (0 == BeStringUtilities::Wcsicmp(tagSpec.tagName, NAME_TAG_TITLE))
                TagElementHandler::GetDisplayText (tag, title);

            // get URL
            if (0 == BeStringUtilities::Wcsicmp(tagSpec.tagName, NAME_TAG_URL))
                {
                if (BSISUCCESS == TagElementHandler::GetDisplayText(tag, url) && !url.empty())
                    {
                    acTitle.assign (title.c_str());
                    acUrl.assign (url.c_str());

                    return  BSISUCCESS;
                    }
                }
            }
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           06/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntityHyperlink
(
AcDbObject*                 acObject,
ElementHandleCR             elemHandle
)
    {
    try
        {
        AcString        title1, title2, url;
        if (SUCCESS == this->GetHyperlinkDataFromDesignLink (title1, title2, url, elemHandle) ||
            SUCCESS == this->GetHyperlinkDataFromEngineeringLink (title1, url, elemHandle))
            {
            // try to create a new PE_URL regapp if not already existed:
            RealDwgXDataUtil::AddRegApp (StringConstants::RegAppName_PeUrl, this->GetDatabase());

            // create actual hyperlink
            RealDwgResBuf*  pHyperlinkXData;

            if (SUCCESS != RealDwgXDataUtil::CreateHyperlinkXData (&pHyperlinkXData, title1, title2, url))
                {
                // if it didn't work, create a blank one to delete existing hyperlink.
                pHyperlinkXData = RealDwgResBuf::CreateRegAppXData (StringConstants::RegAppName_PeUrl);
                }

            acObject->setXData (pHyperlinkXData);

            // the data is now copied - free it.
            RealDwgResBuf::Free (pHyperlinkXData);
            }
        }


    catch (...)
        {
        return  BSIERROR;
        }

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::UpdateExtensionDictionaryFromXAttributes
(
AcDbObject*                 acObject,
ElementHandleCR             elemHandle
)
    {
    if (!this->GetIsSavingApplicationData())
        return;

    /*-----------------------------------------------------------------------------------
    This code does two things:

    1) Remove legacy XAttributes from XDATA we added on the object prior to Vancouver.
    2) Save AXtributes as an xRecord contained in an extended dictionary to the object.
    
    We remove entire MicroStation regapp xdata, leaving app linkages to be updated by the caller.
    -----------------------------------------------------------------------------------*/
    RealDwgResBuf*  msXData = static_cast<RealDwgResBuf*> (acObject->xData(StringConstants::RegAppName_MicroStation));
    if (nullptr != msXData && RealDwgXDataUtil::RemoveXDataByKey(&msXData, StringConstants::XDataKey_XAttribute))
        acObject->setXData (msXData);
    RealDwgResBuf::Free (msXData);
    msXData = NULL;

    // Create a new resbuf from XAttributes, without the 16KB size limitation
    if (RealDwgXDataUtil::GetXDataFromXAttributes (&msXData, elemHandle.GetElementRef(), this->GetDatabase(), false))
        {
        // get existing extended dictionary or create a new one
        AcDbDictionaryPointer   acDictionary (acObject->extensionDictionary(), AcDb::kForWrite);
        if (Acad::eOk != acDictionary.openStatus())
            {
            // ensure the object's database residency prior to add a new extended dictionary to it
            if (!acObject->objectId().isValid() && acObject->isKindOf(AcDbEntity::desc()))
                this->AddEntityToCurrentBlock (AcDbEntity::cast(acObject), elemHandle.GetElementCP()->ehdr.uniqueId);
                
            if (Acad::eOk != acObject->createExtensionDictionary())
                {
                DIAGNOSTIC_PRINTF ("Failed creating a new extended dictionary for element %I64d\n", elemHandle.GetElementCP()->ehdr.uniqueId);
                RealDwgResBuf::Free (msXData);
                return;
                }

            acDictionary.open (acObject->extensionDictionary(), AcDb::kForWrite);
            if (Acad::eOk != acDictionary.openStatus())
                {
                DIAGNOSTIC_PRINTF ("Failed opening a new extended dictionary created for element %I64d\n", elemHandle.GetElementCP()->ehdr.uniqueId);
                RealDwgResBuf::Free (msXData);
                return;
                }
            }

        // get or create the xRecord for our XAttribute:
        AcDbObjectId        objectId;
        AcDbXrecordPointer  acXrecord;
        if (Acad::eOk == acDictionary->getAt(StringConstants::XDataKey_XAttribute, objectId))
            {
            // update existing xRecord with new xattribute data
            if (Acad::eOk != acXrecord.open(objectId, AcDb::kForWrite))
                {
                DIAGNOSTIC_PRINTF ("Failed opening existing xrecord on the extended dictionary for element %I64d\n", elemHandle.GetElementCP()->ehdr.uniqueId);
                RealDwgResBuf::Free (msXData);
                return;
                }
            }
        else
            {
            // create a new xRecord and add to the extended dictionary:
            if (Acad::eOk != acXrecord.create() ||
                Acad::eOk != acDictionary->setAt(StringConstants::XDataKey_XAttribute, acXrecord, objectId))
                {
                DIAGNOSTIC_PRINTF ("Failed adding a new xrecord to the extended dictionary for element %I64d\n", elemHandle.GetElementCP()->ehdr.uniqueId);
                RealDwgResBuf::Free (msXData);
                return;
                }
            }

        // set the xRecord with xattributes
        acXrecord->setFromRbChain (*msXData);
        acXrecord->close ();
        
        RealDwgResBuf::Free (msXData);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntityXDataFromLinkagesAndGraphicGroup
(
AcDbObject*                 acObject,
MSElementCP                 pElement
)
    {
    try
        {
        // When we create a DGN element from a DWG entity, we put the XData from the DWG entity onto the DGN element as an XData (binary data) linkage.
        // Conversely, when we create a DWG entity from a DGN element, we put the MicroStation-specific data onto the DWG entity so if we open the DWG
        //  again in MicroStation, we remember all of that stuff.

        // This method must retrieve the XData linkage from the DGN element (the stuff that originated from the DWG entity) so it can be stored on the DWG entity,
        //  and create the DGN-oriented XData. It tries to replac the DGN xdata on the element only if has been changed.


        bool                    xDataChanged = false;
        AcDbDatabase*           pDatabase = m_pFileHolder->GetDatabase();

        // Note:: This is required if MicroStation ever modifies any of the "other" XData

        // Extract current XData chain from ARX object..
        RealDwgResBuf*  pAllXData = static_cast<RealDwgResBuf*> (acObject->xData ());

        // separate the XData chain into MicroStation XData and all the rest.
        RealDwgResBuf*  pNonMicroStationXData;
        RealDwgResBuf*  pMicroStationXData = RealDwgXDataUtil::SeparateXData (pNonMicroStationXData, pAllXData, StringConstants::RegAppName_MicroStation);

        // ----------------------
        // Get the XData that corresponds to the nonMicroStation XData from the DGN Element (getting it from the linkage), and update pNonMicroStationXData
        // ----------------------
        if (!GetSettings().IgnoreXData())
            {
            ElementHandle   eh (pElement, m_model);
            byte const* pXDataLinkage = nullptr;
            UInt32      xDataLinkageSize = RealDwgXDataUtil::ExtractXDataLinkage (&pXDataLinkage, eh);

            xDataChanged = RealDwgXDataUtil::XDataFromBinaryData (pNonMicroStationXData, pXDataLinkage, xDataLinkageSize, pDatabase, *this);

            /*----------------------------------------------------------------------------------------------------
            Remove conflicting xdata from DGN element, including annotation scale and dimension's style overrides.
            ----------------------------------------------------------------------------------------------------*/
            if (xDataChanged)
                {
                RealDwgXDataUtil::RemoveXDataByAppName (pNonMicroStationXData, L"AcadAnnotative");
                if (DIMENSION_ELM == pElement->ehdr.type)
                    RealDwgXDataUtil::RemoveXDataByAppName (pNonMicroStationXData, L"ACAD");
                }
            }

        // ----------------------
        // Make sure the current Application linkages are reflected in the MicroStation XData sequence.
        // ----------------------
        int                     appLinkageSize = 0;
        UShort                  appLinkageBuffer[MAX_ELEMENT_SIZE];
        this->ExtractApplicationLinkageFromElement (appLinkageBuffer, &appLinkageSize, pElement);
        xDataChanged |= RealDwgXDataUtil::SetAppLinkage (&pMicroStationXData, appLinkageBuffer, appLinkageSize, pDatabase);

        // Make sure the current Dependency linkages are reflected in the MicroStation XData sequence..
        int                     nAppDependencies;
        DependencyLinkage       *appDependencies[MAX_APP_DEPENDENCIES];
        this->ExtractApplicationDependenciesFromElement (appDependencies, &nAppDependencies, pElement);
        xDataChanged |= RealDwgXDataUtil::SetAppDependencies (&pMicroStationXData, appDependencies, nAppDependencies, pDatabase, *this);
        // free the dependencies
        for (int iDepend = 0; iDepend < nAppDependencies; iDepend++)
            free (appDependencies[iDepend]);

        // // make sure the current Graphic Group and transparency are in the MicroStation XData sequence.
        if (pElement->ehdr.isGraphics && this->GetSettings().SaveMicroStationSettings())
            {
            if (!this->SavingChanges() ||                                   // Try to avoid saving attributes that have been ggrouped by export.
                !this->GetSettings().GraphicGroupAttributes() ||
                 pElement->ehdr.type != SHARED_CELL_ELM || pElement->ehdr.type != ATTRIBUTE_ELM)
                xDataChanged |= RealDwgXDataUtil::SetGraphicGroup (&pMicroStationXData, pElement->hdr.dhdr.grphgrp, pDatabase);

            // ACAD R2011 supports transparency
            if (this->GetTargetVersion() < DwgFileVersion_2010)
                {
                ElementHandle   eh(pElement, m_model);
                if (eh.IsValid())
                    {
                    ElementPropertiesGetter     elemProps(eh);
                    xDataChanged |= RealDwgXDataUtil::SetTransparency (&pMicroStationXData, elemProps.GetTransparency(), pDatabase);
                    }
                }
            }

        // ----------------------
        // Consolidate the XData and put it back on the AcDbObject
        // ----------------------
        pAllXData = RealDwgXDataUtil::ConsolidateXData (pMicroStationXData, pNonMicroStationXData);
        if (xDataChanged)
            acObject->setXData (pAllXData);

        // free the XData buffers.
        RealDwgResBuf::Free (pAllXData);
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught extracting XData from Element: %I64d\n", pElement->ehdr.uniqueId);
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntityLevelFromElement
(
AcDbEntity*                 pEntity,
MSElementDescrCP            pDescr,
LayerClassOverride          layerClassOverride
)
    {
    AcDbObjectId        layerId;
    MSElementCP         pElement = &pDescr->el;

    switch (layerClassOverride)
        {
        case CLASS_LAYER_DefPoints:
            layerId = this->GetDefPointsLayer();
            break;

        case CLASS_LAYER_Construction:
            layerId = this->GetConstructionClassLayer (pElement->ehdr.level);
            break;

        case CLASS_LAYER_Pattern:
            layerId = this->GetPatternClassLayer (pElement->ehdr.level);
            break;

        case CLASS_LAYER_LinearPatterned:
            layerId = this->GetLinearPatternedClassLayer (pElement->ehdr.level);
            break;

        default:
            if (this->SavingChanges())
                {
                LevelHandle     level = m_model->GetLevelCache().GetLevel (pElement->ehdr.level);
                layerId = m_pFileHolder->GetLayerByLevelHandle (level);
                }
            else
                {
                layerId = m_pFileHolder->GetLayerByLevelId (pElement->ehdr.level);
                }
            break;
        }

    if (!layerId.isNull())
        pEntity->setLayer (layerId);

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/04
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntitySymbologyFromDgn
(
AcDbEntity*                 pEntity,
UInt32                      dgnColor,
UInt32                      dgnWeight,
Int32                       dgnStyle,
UInt32                      dgnLevel,
MSElementCP                 pElement
)
    {
    bool                colorOverride = false, styleOverride = false, weightOverride = false;

    if (m_useLevelSymbologyOverrides)
        m_pFileHolder->GetLayerIndex()->GetSymbologyOverrides (&colorOverride, &styleOverride, &weightOverride, dgnLevel);

    if (colorOverride)
        {
        AcCmColor       color;
        color.setByLayer(); //setColorMethod - deprecated in realdwg2021
        pEntity->setColor (color);
        }
    else
        {
        pEntity->setColor (this->GetColorFromDgn (dgnColor, pEntity->colorIndex()), true);
        }

    pEntity->setLineWeight (weightOverride ? AcDb::kLnWtByLayer : this->GetLineWeightFromDgn (dgnWeight,  pEntity->lineWeight()), true);
    pEntity->setLinetype   (styleOverride  ? m_pFileHolder->GetDatabase()->byLayerLinetype() : this->GetLineTypeFromDgn (dgnStyle), true);

    if (NULL != pElement)
        {
        Int32                       lineStyle;
        LineStyleParams             styleParams;
        ElementHandle               eh(pElement, m_model);
        if (styleOverride)
            {
            LevelHandle     level = this->GetModel()->GetLevelCache().GetLevel (dgnLevel);
            if (level.IsValid())
                {
                LevelDefinitionLineStyle    lstyleDef = level.GetOverrideLineStyle ();
                lineStyle = lstyleDef.GetStyle ();
                if (!lstyleDef.GetStyleParams(styleParams))
                    LineStyleUtil::InitializeParams (&styleParams);
                }
            }
        else
            {
            // Get effective line style: mdlElement_getEffectiveSymbology (NULL, NULL, &lineStyle, &styleParams, NULL, pElement, this->GetModel(), NULL, -1);
            if (eh.IsValid())
                {
                ElementPropertiesGetter elemProps(eh);
                lineStyle = elemProps.GetLineStyle (&styleParams);
                }
            }

        // TR: 102331 - make sure that we don't pay attention to linestyle scale on elements with standard (cosmetic) linecodes
        if ( ((lineStyle < 0) || (lineStyle > 7)) && (0 != (styleParams.modifiers  & STYLEMOD_SCALE)) && (0.0 != styleParams.scale) )
            pEntity->setLinetypeScale (styleParams.scale);
        else
            pEntity->setLinetypeScale (1.0);

        if (eh.IsValid())
            {
            ElementPropertiesGetter elemProps(eh);
            pEntity->setTransparency (this->GetTransparencyFromDgn(elemProps.GetTransparency(), dgnLevel));
            }
        }
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateEntitySymbologyAndLevelFromElement            // Java Method: DwgEntityHeader.extractHeader...
(
AcDbEntity*                 pEntity,
MSElementDescrCP            pDescr,
LayerClassOverride          layerClassOverride,
bool                        isLayerMapped
)
    {
    if (NULL == pDescr || !pDescr->el.hdr.ehdr.isGraphics)
        return BSIERROR;

    // a SOLID entity should get color from fill color
    MSElementCP         pElement = &pDescr->el;
    UInt32              color = pElement->hdr.dhdr.symb.color;
    if (pEntity->isKindOf(AcDbSolid::desc()))
        {
        ElementHandle   eh (pDescr, false);
        UInt32          fillColor = 0;
        if (this->IsElementFilled(eh, &fillColor, NULL, NULL))
            color = fillColor;
        }

    this->UpdateEntitySymbologyFromDgn (pEntity, color, pElement->hdr.dhdr.symb.weight, pElement->hdr.dhdr.symb.style, pElement->hdr.ehdr.level, pElement);
    if (!isLayerMapped || layerClassOverride != LayerClassOverride::CLASS_LAYER_None)
        this->UpdateEntityLevelFromElement (pEntity, pDescr, layerClassOverride);
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley     12/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::DeleteElementObject
(
ElementId                   elementId,
AcDbObjectIdArray&          idsToPurge
)
    {
    AcDbObjectId    objectId;
    if ((objectId = this->ExistingObjectIdFromElementId (elementId)).isNull())
        return BSIERROR;
    if (objectId.isErased())
        return SUCCESS;

    try
        {
        AcDbObjectPointer<AcDbObject> acObject (objectId, AcDb::kForWrite);

        if (Acad::eOk != acObject.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Cannot delete object w/ID=%I64d due to ACAD error: %ls\n", elementId, acadErrorStatusText(acObject.openStatus()));
            return  CantEraseObject;
            }

        // layout deletion is handled when deleting model - if it wasn't deleted then (due to an exception) then i shouldn't be deleted here.
        if (NULL != AcDbLayout::cast (acObject))
            return RealDwgSuccess;

        // symbol tables are added to the purge list.
        if (NULL != AcDbSymbolTableRecord::cast (acObject))
            {
            idsToPurge.append (acObject->objectId());
            return RealDwgSuccess;
            }

        return ACRX_X_CALL (acObject, ToDgnExtension)->FromElementDeleteObject (acObject, idsToPurge, *this);
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown deleting element ID: %I64d\n", elementId);
        return  BSIERROR;
        }

    return SUCCESS;
    }

/*----------------------------------------------------------------------------------*//**
* @bsimethod                                                    VenkatKalyan    07/02
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsConeHeightZero
(
ElementHandleCR             coneElement
)
    {
    ConeHandler*    coneHandler;
    if (NULL == (coneHandler = dynamic_cast <ConeHandler*> (&coneElement.GetHandler())))
        {
        BeAssert (false && L"Not a cone handler!");
        return false;
        }

    DPoint3d        top     = *coneHandler->ExtractTopCenter (coneElement);
    DPoint3d        bottom  = *coneHandler->ExtractBottomCenter (coneElement);
    return bottom.distance(&top) < TOLERANCE_UORPointEqual;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::CanElementBeRepresentedByLWPolyline (ElementHandleCR elementIn, bool wantFullCircularArcs)
    {
    switch (elementIn.GetElementType())
        {
        case LINE_ELM:
        case LINE_STRING_ELM:
        case SHAPE_ELM:
        case POINT_STRING_ELM:
            return  true;

        case CMPLX_SHAPE_ELM:
        case CMPLX_STRING_ELM:
            {
            if (!RealDwgUtil::GetElementNormal(NULL, NULL, elementIn))
                return  false;

            for (ChildElemIter child(elementIn, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
                {
                switch (child.GetElementType())
                    {
                    case LINE_ELM:
                    case LINE_STRING_ELM:
                    case SHAPE_ELM:
                        break;

                    case ARC_ELM:
                        bool isFullSweepCircularArc;
                        if (!this->IsElementCircular (NULL, &isFullSweepCircularArc, child))
                            return false;

                        if (!wantFullCircularArcs && isFullSweepCircularArc)
                            return false;

                        break;

                    default:
                        return false;
                    }
                }
            return true;
            }

        default:
            return false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    06/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsEnterDataField (MSElementCP pElem)
    {
    if (NULL != pElem && TEXT_ELM == pElem->ehdr.type)
        {
        int     numEDFields = pElem->hdr.dhdr.props.b.is3d ? pElem->text_3d.edflds : pElem->text_2d.edflds;
        if (1 == numEDFields)
            {
            int             numBytes = pElem->hdr.dhdr.props.b.is3d ? pElem->text_3d.numchars : pElem->text_2d.numchars;
            char const*     textStart = pElem->hdr.dhdr.props.b.is3d ? pElem->text_3d.string : pElem->text_2d.string;
            TextEDField     edField1;
            memcpy (&edField1.start, (byte const*) (textStart + numBytes), sizeof edField1);
            if (1 == edField1.start)
                return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::IsTextShapeEntity (TextBlockCR textBlock)
    {
    /*-----------------------------------------------------------------------------------
    Check to see if this text element should be created as a shape entity:
    1) A shape entity only uses a shape textstyle.
    2) A shape entity contains only one character.
    -----------------------------------------------------------------------------------*/
    DgnTextStylePtr         textStyle = textBlock.GetTextStyle();
    bool                    isShapeFile = false;
    bool                    isShapeEntity = false;

    if (!textStyle.IsNull() && BSISUCCESS == textStyle->GetProperty(TextStyle_AcadShapeFile, isShapeFile))
        isShapeEntity = isShapeFile;
    else
        isShapeEntity = false;

    if (isShapeEntity)
        {
        Bentley::WString string;
        textBlock.ToDText (string, this->GetModel(), !this->GetSettings().ConvertEmptyEDFToSpace());
        if (string.size() > 1)
            isShapeEntity = false;
        }

    return  isShapeEntity;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveXRefsAndLayersToDatabase (DgnFileP dgnFile)
    {
    bool    first = true;

    for each (ModelIndexItemCR modelItem in dgnFile->GetModelIndex())
        {
        dgnFile->AddRef();
        DgnModelP       model = NULL;

        // Note: For 08.09, I changed it to separate the LoadRootModel and loading the references to avoid loading the graphic elements in the references.
        // Load the cache. We need the control elements of the references, but not the graphics elements, so first we tell it not to load the references.
        if (NULL != (model = dgnFile->LoadRootModelById (NULL, modelItem.GetModelId(), true, false)))
            {
            // now load the reference files, but only the control elements.
            DgnAttachmentLoadOptions loadOptions (true, false, false);
            loadOptions.SetSectionsToFill ((DgnModelSections)(DgnModelSections::Dictionary | DgnModelSections::ControlElements));
            model->LoadDgnAttachments (loadOptions);

            if (first)
                {
                first = false;
                CreateLayersForModelRef (NULL, model, model);
                }

            this->SaveModelXRefsToDatabase (model, model);
            }

        dgnFile->Release();
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ExtractPolylineWidth
(
bool&                       hasConstantWidth,
double&                     constantWidth,
bool&                       hasNonZeroWidth,
ElementHandleCR             elemHandle
)
    {
    hasConstantWidth    = hasNonZeroWidth = false;
    constantWidth       = 0.0;

    MSElementCP     pElem;
    if (NULL == (pElem = elemHandle.GetElementCP()))
        return;
    int             type    =pElem->ehdr.type;


    if (CMPLX_SHAPE_ELM == type || CMPLX_STRING_ELM == type)
        {
        bool                    first = true;
        double                  childWidth = 0.0;

        for (ChildElemIter child(elemHandle, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext(), first = false)
            {
            bool                childHasConstantWidth = false, childHasNonZeroWidth = false;
            double              childConstantWidth = 0.0;

            ExtractPolylineWidth (childHasConstantWidth, childConstantWidth, childHasNonZeroWidth, child);

            if (first)
                {
                hasConstantWidth = childHasConstantWidth;
                constantWidth    = childConstantWidth;
                hasNonZeroWidth  = childHasNonZeroWidth;
                }
            else
                {
                if (!childHasConstantWidth || constantWidth != childConstantWidth)
                    hasConstantWidth = false;

                hasNonZeroWidth |= childHasNonZeroWidth;
                }
            }
        }
    else
        {
        double          startWidth, endWidth;

        this->ExtractWidthFromElement (startWidth, endWidth, elemHandle);

        if (false != (hasNonZeroWidth = (0.0 != startWidth || 0.0 != endWidth)))
            {
            if (false != (hasConstantWidth = (startWidth == endWidth)))
                constantWidth = startWidth;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ExtractWidthFromElement
(
double&                     startWidth,
double&                     endWidth,
ElementHandleCR             elemHandle
)
    {
    startWidth  = endWidth = 0.0;

    MSElementCP element = elemHandle.GetElementCP();
    LineStyle_getWidthInUORs (&startWidth, &endWidth, elemHandle);

    double  scale = this->GetScaleFromDGN();
    startWidth *= scale;
    endWidth   *= scale;

    if ((0.0 != startWidth || 0.0 != endWidth) && this->IsCurrentBlockLayout())
        {
        // get effective linestyle:
        ElementPropertiesGetter elemProps (elemHandle);
        LsDefinitionP           lsDefinition = LsMap::FindInRef (*this->GetFile(), elemProps.GetLineStyle());
        if (nullptr != lsDefinition)
            {
            LsCacheComponentCP  lsComponent = lsDefinition->GetComponentCP (this->GetModel());
            if (nullptr == lsComponent || !lsComponent->IsAffectedByWidth(true))
                {
                // widths are not displayed
                startWidth = endWidth = 0.0;
                }
            }
        }

    return 0.0 != startWidth || 0.0 != endWidth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::GetLinestyleFromElement (Int32& lsNo, bool& acrossSegments, ElementHandleCR eh)
    {
    MSElementCP     elem = eh.GetElementCP ();
    lsNo = elem->hdr.dhdr.symb.style;

    if (STYLE_BYLEVEL == lsNo)     // TR# 173247
        {
        LevelHandle     level = this->GetModel()->GetLevelCache().GetLevel (elem->ehdr.level);
        if (level.IsValid())
            lsNo = level.GetByLevelLineStyle().GetStyle ();
        }

    // default linestyle to acrossSegment, not BySegment - TFS 526805
    acrossSegments = true;

    LsDefinitionP       lsDefinition = LsMap::FindInRef (*this->GetFile(), lsNo);
    if (nullptr != lsDefinition)
        {
        LsCacheComponentCP  lsComponent = lsDefinition->GetComponentCP (this->GetModel());
        if (nullptr != lsComponent)
            {
            acrossSegments = !lsComponent->IsBySegment ();
            return  RealDwgSuccess;
            }
        }

    return  LineStyleTableNotFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
Int16                       ConvertFromDgnContext::GetColorIndexFromRGB
(
const RgbColorDef           *pRGB
)
    {
    DwgSymbologyData*               dwgSymbologyData;

    if (NULL != (dwgSymbologyData = m_pFileHolder->GetDwgSymbologyData ()))
        return (Int16) dwgSymbologyData->GetColorIndex (pRGB, *this);

    return DWG_COLOR_ByLayer;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
Int16                       ConvertFromDgnContext::GetColorIndexFromDgn
(
UInt32                      dgnColor,
Int16                       currentColor
)
    {
    switch (dgnColor)
        {
        case    COLOR_BYLEVEL:
            return DWG_COLOR_ByLayer;

        case    COLOR_BYCELL:
            return DWG_COLOR_ByBlock;
        }

    if (DWG_OVERRIDE_SYMBOLOGY_MAP == (0xffffff00 & dgnColor))
        {
        return (Int16) (dgnColor & 0xff);
        }
    else
        {
        DwgSymbologyData*                   dwgSymbologyData;
        DgnSymbologyData*                   dgnSymbologyData;

        if (NULL != (dgnSymbologyData = m_pFileHolder->GetDgnSymbologyData ()) &&
            NULL != (dwgSymbologyData = m_pFileHolder->GetDwgSymbologyData ()))
            {
            UInt32  colorIndex = currentColor < 0 ?  dwgSymbologyData->RemapColorIndex (dgnColor, dgnSymbologyData, *this) :
                                 dwgSymbologyData->RemapColorIndexIfChanged ((UInt32) currentColor, dgnColor, dgnSymbologyData, *this);
             
            return (Int16) colorIndex;
            }
        else
            return DWG_COLOR_Default;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcCmColor                   ConvertFromDgnContext::GetColorFromDgn
(
UInt32                      dgnColor,
Int16                       currentColor,
DgnModelRefP                modelRef
)
    {
    AcCmColor       color;
    IntColorDef     colorDef;
    WString         bookName, entryName;
    bool            isTrueColor = false;

    if (DWG_OVERRIDE_SYMBOLOGY_MAP != (INVALID_COLOR & dgnColor) &&
        0 != ColorUtil::GetExtendedIndexFromRawColor(dgnColor) &&
        BSISUCCESS == DgnColorMap::ExtractElementColorInfo(&colorDef, NULL, &isTrueColor, &bookName, &entryName, dgnColor, NULL == modelRef ? *m_dgnFile : *modelRef->GetDgnFileP()))
        {
        color.setRGB(colorDef.m_rgb.red, colorDef.m_rgb.green, colorDef.m_rgb.blue); ////setRed(), setGreen(), setBlue() - deprecated in realdwg2021

        if (isTrueColor && !bookName.empty() && !entryName.empty())
            {
            // round-trip DWG's frame foreground color 255:
            if (bookName.Equals (this->GetSpecialDwgColorBookName()) && entryName.Equals (this->GetDwgForegroundColor255Name()))
                color.setColorIndex (DWG_COLOR_Foreground255);
            else
                color.setNames (entryName.c_str (), bookName.c_str ());
            }
        }
    else
        {
        Int16       colorIndex;

        switch  (colorIndex = this->GetColorIndexFromDgn(dgnColor, currentColor))
            {
            case    DWG_COLOR_ByLayer:
                color.setByLayer(); //setColorMethod- deprecated in realdwg2021
                break;

            case    COLOR_BYCELL:
                color.setByBlock(); //setColorMethod- deprecated in realdwg2021
                break;

            default:
                if (this->GetSettings().CreateTrueColorFromDgnIndices() && this->GetTargetVersion() >= DwgFileVersion_2004)

                    {
                    DwgSymbologyData*           dwgSymbologyData;
                    DgnSymbologyData*           dgnSymbologyData;
                    RgbColorDef const           *pDwgColor, *pDgnColor;

                    if (NULL == (dgnSymbologyData = m_pFileHolder->GetDgnSymbologyData ()) ||
                        NULL == (dwgSymbologyData = m_pFileHolder->GetDwgSymbologyData ()) ||
                        NULL == (pDgnColor = dgnSymbologyData->GetColor (dgnColor)) ||
                        NULL == (pDwgColor = dwgSymbologyData->GetColor (colorIndex)) ||
                        (pDwgColor->red == pDgnColor->red && pDwgColor->blue == pDgnColor->blue && pDwgColor->green == pDgnColor->green))
                        {
                        color.setColorIndex (colorIndex);
                        }
                    else
                        {
                        color.setRGB(pDgnColor->red, pDgnColor->green, pDgnColor->blue); //setColorMethod, setRed, setGreen, setBlue - deprecated in realdwg2021
                        }
                    }
                else
                    {
                    color.setColorIndex (colorIndex);
                    }
                break;
            }
        }

    return color;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDb::LineWeight            ConvertFromDgnContext::GetLineWeightFromDgn
(
UInt32                      dgnWeight,
AcDb::LineWeight            currentWeight
)
    {
    switch (dgnWeight)
        {
        case    WEIGHT_BYLEVEL:
            return AcDb::kLnWtByLayer;

        case    WEIGHT_BYCELL:
            return AcDb::kLnWtByBlock;
        }
    if (DWG_OVERRIDE_SYMBOLOGY_MAP == (0xffff0000 & dgnWeight))
        {
        // The value from the remap application is an AutoCAD weight index (50 * mm), but AutoCAD behaves badly if you don't set
        // the weight index to one of the standard weights, so cycle through widthFromWeight and weightFromWidth to get the rounding to happen.
        // this should do nothing if one of the standard weights is passed in.
        return (AcDb::LineWeight) GetSettings().GetDwgWeightFromWidthInMM ((double) (dgnWeight & 0x0000ffff) / 50.0);
        }
    else
        {
        if (this->GetDgnWeight (currentWeight) == (int) dgnWeight)
            return currentWeight;
        else
            return (AcDb::LineWeight) GetSettings().GetDwgWeightFromDgnWeight (dgnWeight);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetLineTypeFromDgn (Int32 dgnStyle)
    {
    switch (dgnStyle)
        {
        case STYLE_BYLEVEL:
            return m_pFileHolder->GetDatabase()->byLayerLinetype();

        case STYLE_BYCELL:
            return m_pFileHolder->GetDatabase()->byBlockLinetype();
        }


    AcDbObjectId        linetype;

    try
        {
        if ((linetype = m_pFileHolder->GetLinetypeByStyleId (dgnStyle)).isNull() &&
            (linetype = this->CreateLinetypeFromStyleId (m_model, NULL, NULL, true, dgnStyle)).isNull())
            return m_pFileHolder->GetContinuousLinetype();
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught creating linetype from StyleId\n");
        return m_pFileHolder->GetContinuousLinetype();
        }

    return linetype;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcCmTransparency            ConvertFromDgnContext::GetTransparencyFromDgn(double dgnTransparency, UInt32 dgnLevel)
    {
    AcCmTransparency        cmTransparency;
    /*-----------------------------------------------------------------------------------
    FUTUREWORK_TRANSPARENCY: MicroStation needs to support BYLAYER/BYCELL transparencies.

    Currently, level transparency always has an effect on element transparency:

    NetTransparency = 1 - (1 - elememntTransparency) * (1 - levelTransparency) * (1 - modelTransparency)
    
    Workaround to minimize losing DWG BYLAYER transparency: if element transparency is 0, 
    it's net transparency is from level.  DWG does not have model transparency so there
    isn't much we can do about that.

    Also round trip the kludge BYBLOCK value=1%.
    -----------------------------------------------------------------------------------*/
    if (INVALID_LEVEL != dgnLevel)
        {
        if (TRANSPARENCY_IsSame(dgnTransparency, TRANSPARENCY_ByLayer))
            {
            cmTransparency.setMethod (AcCmTransparency::kByLayer);
            return  cmTransparency;
            }
        else if (TRANSPARENCY_IsSame(dgnTransparency, TRANSPARENCY_ByBlock))
            {
            cmTransparency.setMethod (AcCmTransparency::kByBlock);
            return  cmTransparency;
            }

        LevelHandle         level = this->GetModel()->GetLevelCache().GetLevel (dgnLevel);
        if (level.IsValid())
            {
            double          levelTransparency = level.GetTransparency ();
            dgnTransparency = 1.0 - (1.0 - dgnTransparency) * (1.0 - levelTransparency);
            }
        }

#if RealDwgVersion >= 2011
    cmTransparency.setAlphaPercent (1.0 - dgnTransparency);
#else
    cmTransparency.setAlpha ((Adesk::UInt8)(255 * (1.0 - dgnTransparency)));
#endif

    return  cmTransparency;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsCircularEllipse (double primary, double secondary)
    {
    // don't create a degenerated ellipse, which when in a block corrupts DWG file - TFS 935466:
    if (fabs(primary) < TOLERANCE_UORPointEqual && fabs(secondary) < TOLERANCE_UORPointEqual)
        return  true;
    return secondary > 0.0 && fabs ((primary - secondary) / (primary + secondary)) < TOLERANCE_CircularRatio;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsElementCircular
(
bool                        *pClosed,
bool                        *pFullSweepCircularArc,
ElementHandleCR             element
)
    {
    if (NULL != pClosed)
        *pClosed = (ELLIPSE_ELM == element.GetElementType());

    if (NULL != pFullSweepCircularArc)
        *pFullSweepCircularArc = false;

    double          primary = 0.0, secondary = 0.0, sweepAngle = 0.0;
    if (SUCCESS != ArcHandler::Extract(NULL, NULL, NULL, &sweepAngle, &primary, &secondary, NULL, NULL, NULL, element))
        return false;

    if (!IsCircularEllipse (primary, secondary))
        return false;

    if (NULL != pFullSweepCircularArc)
        *pFullSweepCircularArc = Angle::IsFullCircle(sweepAngle);

    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsParallelToNormal
(
const DPoint3d&             fromPoint,
const DPoint3d&             toPoint,
const RotMatrix&            matrix
)
    {
    DVec3d      vector, normal;
    vector.differenceOf (&toPoint, &fromPoint);
    vector.normalize ();

    matrix.getColumn (&normal, 2);
    normal.normalize ();

    return  fabs(fabs(vector.dotProduct(&normal)) - 1.0) < TOLERANCE_VectorEqual;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsElementRightCylinder
(
bool*                       pSurface,
DPoint3dP                   pTop,
DPoint3dP                   pBottom,
double*                     pRadius,
RotMatrixP                  pRMatrix,
ElementHandleCR             coneElement
)
    {
    ConeHandler*    coneHandler;
    if (NULL == (coneHandler = dynamic_cast <ConeHandler*> (&coneElement.GetHandler())))
        return  false;

    double          topRadius = coneHandler->ExtractTopRadius (coneElement);
    double          baseRadius = coneHandler->ExtractBottomRadius (coneElement);
    DPoint3d        top = *coneHandler->ExtractTopCenter (coneElement);
    DPoint3d        bottom = *coneHandler->ExtractBottomCenter (coneElement);
    RotMatrix       rMatrix;
    coneHandler->ExtractRotation (rMatrix, coneElement);

    if (fabs(topRadius - baseRadius) < TOLERANCE_UORPointEqual && IsParallelToNormal(bottom, top, rMatrix))
        {
        if (NULL != pSurface)
            *pSurface = !coneHandler->ExtractCapFlag (coneElement);

        if (NULL != pTop)
            *pTop = top;

        if (NULL != pBottom)
            *pBottom = bottom;

        if (NULL != pRadius)
            *pRadius = baseRadius;

        if (NULL != pRMatrix)
            *pRMatrix = rMatrix;

        return true;
        }

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsNonPlanarPolyline
(
ElementHandleCR             inElement,
bool                        allowPlanarDeviation,
DPoint3dP                   pNormal,
DPoint3dP                   pOrigin,
DPoint3dP                   pDefaultNormal
)
    {
    double          maxVariance = 0.0, planarTolerance = TOLERANCE_UORPlanarLineString;

    DRange3d        range;
    MSElementCP     elem = inElement.GetElementCP ();
    DataConvert::ScanRangeToDRange3d (range, elem->hdr.dhdr.range);

    // If they have specified that nonplanar linestrings should be force to planar, relax the planar tolerance.
    planarTolerance = (allowPlanarDeviation ? NON_PLANAR_LINESTRING_ToleranceRatio : NEARLY_PLANAR_LINESTRING_ToleranceRatio) * range.low.Distance(range.high);

    GPArrayP        pGPA = GPArray::Grab();
    if (SUCCESS == pGPA->Add(inElement))
        {
        Transform   transform;
        if (jmdlGraphicsPointArray_getPlaneAsTransformExt2 (pGPA, &transform, &maxVariance, pDefaultNormal, NULL))
            {
            if (pNormal)
                transform.getMatrixColumn (pNormal, 2);

            if (pOrigin)
                transform.getTranslation (pOrigin);
            }
        else
            {
            int         nGot = 0;

            if (pNormal)
                *pNormal = *pDefaultNormal;

            if (pOrigin)
                jmdlGraphicsPointArray_getDPoint3dArray (pGPA, pOrigin, &nGot, 0, 1);
            }
        pGPA->Drop ();
        }

    if (allowPlanarDeviation && NULL != pNormal)
        {
        if (maxVariance > TOLERANCE_UORPlanarLineString)
            {
            if (pNormal->z > .999)
                {
                pNormal->x = pNormal->y = 0.0;
                pNormal->z = 1.0;
                }
            else if (pNormal->z < -.999)
                {
                pNormal->x = pNormal->y = 0.0;
                pNormal->z = -1.0;
                }
            }
        }

    return maxVariance > planarTolerance;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsDegenerateArc (DPoint3dP pPoints, ElementHandleCR elemHandle)
    {
    double      startAngle = 0.0, sweepAngle = 0.0, primary = 0.0, secondary = 0.0, rotAngle = 0.0;
    RotMatrix   rotMatrix;
    DPoint3d    center;

    if (BSISUCCESS != ArcHandler::Extract(NULL, NULL, &startAngle, &sweepAngle, &primary, &secondary, &rotMatrix, NULL, &center, elemHandle))
        return false;

    if ((fabs (primary) < MINIMUM_AxisUORs || fabs (secondary) < MINIMUM_AxisUORs) ||
        (fabs (primary) > fabs (secondary) * MAXIMUM_EllipseAxisRatio || fabs (secondary) > fabs (primary) * MAXIMUM_EllipseAxisRatio) ||
        (fabs (sweepAngle) < MINIMUM_SweepAngle))
        {
#if defined (DIAGNOSTIC)
        if (com.bentley.sys.Diagnostic.INSTRUMENTED)
            System.out.println ("Replacing Degenerate Arc with line");
#endif

        if (NULL != pPoints)
            {
            DEllipse3d          dEllipse;
            double              minAngle = 0.0, maxAngle = 0.0;

            dEllipse.initFromScaledRotMatrix (&center, &rotMatrix, primary, secondary, startAngle, sweepAngle);

            if (fabs (sweepAngle) < MINIMUM_SweepAngle)
                {
                minAngle = startAngle;
                maxAngle = startAngle + sweepAngle;
                }
            else
                {
                int             nPotentialExtrema = 0;
                double          potentialExtrema[6];

                potentialExtrema[nPotentialExtrema++] = startAngle;
                potentialExtrema[nPotentialExtrema++] = startAngle + sweepAngle;

                int     i;
                double      angle;
                for (i = 0, angle = 0.0; i<4; i++, angle += msGeomConst_piOver2)
                    if (dEllipse.isAngleInSweep (angle))
                        potentialExtrema[nPotentialExtrema++] = angle;


                bool        testCos = primary > secondary;
                double      minValue = 0.0, maxValue = 0.0;

                for (i = 0; i<nPotentialExtrema; i++)
                    {
                    double  testValue = testCos ? cos (potentialExtrema[i]) : sin (potentialExtrema[i]);

                    if (i != 0)
                        {

                        if (testValue < minValue)
                            {
                            minValue = testValue;
                            minAngle = potentialExtrema[i];
                            }
                        else if (testValue > maxValue)
                            {
                            maxValue = testValue;
                            maxAngle = potentialExtrema[i];
                            }
                        }
                    else
                        {
                        minValue = maxValue = testValue;
                        minAngle = maxAngle = potentialExtrema[i];
                        }
                    }
                }
            dEllipse.evaluate (&pPoints[0], minAngle);
            dEllipse.evaluate (&pPoints[1], maxAngle);
            }

        return true;
        }

    return false;
    }

/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       08/98
+---------------+---------------+---------------+---------------+---------------+------*/
Public bool                 ConvertFromDgnContext::IsModelRefViewportAttachment (DgnModelRefP attachmentModelRef)
    {
    DgnAttachmentP      ref;
    DgnModelRefP        parentModelRef;
    DgnFileP            parentDgnFile;

    // do not allow separating sheet models from existing DWG file - TFS 8414.
    bool                savingSheetsToSeparateFiles = this->GetSettings().SaveSheetsToSeparateFiles() && !m_savingChanges;

    if ( !savingSheetsToSeparateFiles &&
         (NULL != (ref = attachmentModelRef->AsDgnAttachmentP())) &&
         (NULL != (parentModelRef = ref->GetParentModelRefP())) &&
         (NULL != (parentDgnFile = parentModelRef->GetDgnFileP())) &&
         (attachmentModelRef->GetDgnModelP() != parentModelRef->GetDgnModelP()) &&
         (DgnModelType::Sheet == parentModelRef->GetModelType()) )
        {
        WString     parentFileName         = parentDgnFile->GetFileName();
        WString     fullAttachSpec         = ref->GetAttachFullFileSpec();
        WString     originalParentFileName;
        
        parentDgnFile->GetOriginalName (originalParentFileName);

        // TR#131671 - The test for matching parent can not be as simple as testing
        // the file objects being equal as during a saveas the file objects may not match
        // and also if viewport is turned off then the fileObject may also be null.
        if (0 == ref->GetAttachFileName()[0] ||
            0 == parentFileName.CompareTo(fullAttachSpec) ||
            0 == originalParentFileName.CompareTo(fullAttachSpec))
            {
            return ref->GetSourceModelId() == RealDwgUtil::GetDwgModelSpaceId(parentDgnFile);
            }
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
*  The cell transforms are defined as from the world to the cell.  For creating
*   block definitions and inserts we need the transform relative to the parent.
*
* @bsimethod                                                    RayBentley      05/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractTransformFromCell
(
DPoint3dR                   origin,
RotMatrixR                  rMatrix,
ElementHandleCR             elemHandle
)
    {
    BentleyStatus           mdlStatus;

    if ( (BSISUCCESS != (mdlStatus = CellUtil::ExtractRotation (rMatrix, elemHandle))) || (BSISUCCESS != (mdlStatus = CellUtil::ExtractOrigin (origin, elemHandle))) )
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (mdlStatus);

    /*-----------------------------------------------------------------------------------
    For a 2D element, compound its flatten matrix to output, excluding TriForma cell which does not seem
    using the matrix - TR 351210.
    -----------------------------------------------------------------------------------*/
    MSElementCP     pElem = elemHandle.GetElementCP();
    if (! pElem->hdr.dhdr.props.b.is3d && !this->IsTriformaNode(elemHandle))
        {
        DVec3d          xColumn, yColumn, zColumn;
        rMatrix.GetColumns (xColumn, yColumn, zColumn);
        zColumn.z = xColumn.magnitude ();
        rMatrix.InitFromColumnVectors (xColumn, yColumn, zColumn);

        for (ConstElementLinkageIterator iter = elemHandle.BeginElementLinkages(); iter != elemHandle.EndElementLinkages(); ++iter)
            {
            UInt16          linkageKey;
            UInt32          numEntries;
            double const*   doubleData = ElementLinkageUtil::GetDoubleArrayDataCP (iter, linkageKey, numEntries);
            if (DOUBLEARRAY_LINKAGE_KEY_FlattenTransform != linkageKey && NULL != doubleData)
                {
                RotMatrix   flattenMatrix;
                memcpy (&flattenMatrix.form3d, doubleData, sizeof(flattenMatrix.form3d));

                rMatrix.InitProduct (rMatrix, flattenMatrix);
                break;
                }
            }
        }

    double determinant = rMatrix.Determinant();
    if (fabs(determinant) <= TOLERANCE_Determinant)
        {
        rMatrix.InitIdentity ();
        }
    else if (!this->GetSettings().AllowScaledBlocksFromCells ())
        {
        rMatrix.SquareAndNormalizeColumns (rMatrix, 0, 1);
        }
    else if (!GetSettings().UniformlyScaleBlocks())
        {
        // allow non-uniformly scale blocks - ACAD supports them.
        // but do not allow clockwise matrix - ACAD does not support that.
        DVec3d          scales;
        if (!rMatrix.isOrthonormal(NULL, &scales, NULL) || (determinant <= 0.0) )
            {
            rMatrix.SquareAndNormalizeColumns (rMatrix, 0, 1);
            rMatrix.ScaleColumns (rMatrix, scales.x, scales.y, scales.z);
            }
        }
    else
        {
        // Trying to transform and untransform by a non-rigid matrix is problematic.  Discard shear and nonuniform scaling if it exists.
        double          scale = 1.0;
        RotMatrix       columns;
        if (! rMatrix.IsRigidScale (columns, scale))
            {
            rMatrix.SquareAndNormalizeColumns (rMatrix, 0, 1);
            rMatrix.ScaleColumns (rMatrix, scale, scale, scale);
            }
        }

    // fix invalid cell origin - TR 294353
    RealDwgUtil::ValidateDoubleValue (&origin.x, -DBL_MAX, DBL_MAX, 0.0);
    RealDwgUtil::ValidateDoubleValue (&origin.y, -DBL_MAX, DBL_MAX, 0.0);
    RealDwgUtil::ValidateDoubleValue (&origin.z, -DBL_MAX, DBL_MAX, 0.0);

    return RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractThicknessFromElement
(
EditElementHandleR          outElement,
DPoint3dR                   extrusionDirection,
double&                     extrusionDistance,
ElementHandleCR             inElement
)
    {
    DPoint3d                top, bottom;
    double                  radius = 0.0;
    RotMatrix               rMatrix;
    bool                    surface = false;
    StatusInt               status = BSIERROR;

    extrusionDistance = 0.0;

    // Uncapped right cylinders can be represented as extruded circles
    if (IsElementRightCylinder (&surface, &top, &bottom, &radius, &rMatrix, inElement) &&
        (surface || IsConeHeightZero (inElement)))
        {
        extrusionDistance = extrusionDirection.NormalizedDifference (top, bottom);

        status = EllipseHandler::CreateEllipseElement (outElement, &inElement, bottom, radius, radius, rMatrix, m_threeD, *m_model);
        }
    else
        {
        DVec3d  extrusionVec = DVec3d::From (extrusionDirection);
        status = SurfaceOrSolidHandler::ExtractProjectionParameters (inElement, outElement, extrusionVec, extrusionDistance, false);

        extrusionDirection.Init (extrusionVec);
        }

    // compose parent transformation - TFS 613817
    this->GetTransformFromDGN().MultiplyMatrixOnly (extrusionDirection);
    extrusionDirection.Normalize ();
        
    extrusionDistance *= this->GetScaleFromDGN();

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::CanCreateCircleFromElement (ElementHandleR elemHandle)
    {
    DPoint3d            extrusionDirection;
    double              extrusionDistance;
    EditElementHandle   checkElemHandle (elemHandle, false);

    this->ExtractThicknessFromElement (checkElemHandle, extrusionDirection, extrusionDistance, elemHandle);

    DEllipse3d          dEllipse;
    bool                isCircle = false;
    if (ELLIPSE_ELM == checkElemHandle.GetElementType() && SUCCESS == this->ExtractDEllipse3dFromElement(dEllipse, checkElemHandle))
        isCircle = dEllipse.IsCircular ();

    return  isCircle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt               ConvertFromDgnContext::ExtractArcFromElement
(
bool&                   closed,
bool&                   circular,
DPoint3dR               center,
RotMatrixR              rotMatrix,
double&                 r0,
double&                 r1,
double&                 theta0,
double&                 sweep,
double&                 extrusionDistance,
ElementHandleR          elemHandle
)
    {
    StatusInt           status;
    DPoint3d            extrusionDirection;
    MSElementDescr      *pExtrudeDescr = NULL;
    DEllipse3d          dEllipse;
    EditElementHandle   checkElemHandle (elemHandle, false);

    if (RealDwgSuccess != this->ExtractThicknessFromElement(checkElemHandle, extrusionDirection, extrusionDistance, elemHandle) &&
        !this->GetEntityThicknessFromElementLinkage(extrusionDistance, extrusionDirection, NULL, elemHandle))
        extrusionDirection.x = extrusionDirection.y = extrusionDirection.z = 0.0;

    if (SUCCESS == (status = this->ExtractDEllipse3dFromElement (dEllipse, checkElemHandle)))
        {
        this->GetTransformFromDGN().Multiply (dEllipse);
        dEllipse.GetScaledRotMatrix (center, rotMatrix, r0, r1, theta0, sweep);

        RotMatrix   localMatrix;
        this->GetTransformFromDGN().GetMatrix (localMatrix);
        localMatrix.Multiply (extrusionDirection);

        DVec3d column2;
        rotMatrix.GetColumn (column2, 2);
        if (extrusionDirection.dotProduct (&column2) < 0.0)
            extrusionDistance = - extrusionDistance;

        if (localMatrix.Determinant() < 0.0)
            extrusionDistance = - extrusionDistance;

        rotMatrix.GetColumn (column2, 2);
        if (this->GetSettings().ForcePositiveExtrusionForClockwiseArcs() && (column2.z < .999999) )
            {
            extrusionDistance = - extrusionDistance;
            rotMatrix.ScaleColumns (rotMatrix, 1.0, -1.0, -1.0);
            theta0 = -(theta0 + sweep);
            }

        /*-------------------------------------------------------------------------------
        Instead of calling a likely better dEllipse.IsCircular() we choose to call IsElementCircular which is the same method 
        we have previously used prior to creating AcDbCircle object from an ellipse element. Calling the same function will ensure
        us to get the same result for the same input element.  TR 294881 shows just such an example in which case the two calls 
        became out of synch due to tolerance check failure here using transformed radii.
        -------------------------------------------------------------------------------*/
        circular = IsElementCircular (NULL, NULL, checkElemHandle);
        closed   = (checkElemHandle.GetElementType() == ELLIPSE_ELM);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::ExtractDEllipse3dFromElement
(
DEllipse3dR                 dEllipse,
ElementHandleCR             elemHandle
)
    {
    HandlerR            handler = elemHandle.GetHandler();

    CurveVectorPtr      curveVector = ICurvePathQuery::ElementToCurveVector (elemHandle);
    if (curveVector.IsNull() || (ICurvePrimitive::CURVE_PRIMITIVE_TYPE_Arc != curveVector->HasSingleCurvePrimitive()))
        return BSIERROR;

    ICurvePrimitivePtr  curvePrimitive = curveVector->front();
    DEllipse3dCP        arcEllipse;
    if (NULL == (arcEllipse = curvePrimitive->GetArcCP()))
        return BSIERROR;

    if (this->IsDegenerateArc (NULL, elemHandle))
        return BSIERROR;

    double              startAngle = arcEllipse->start;
    double              sweepAngle = arcEllipse->sweep;
    double              primary = arcEllipse->vector0.Magnitude ();
    if (primary < 0.0)
        {
        startAngle = msGeomConst_pi - startAngle;
        sweepAngle = -sweepAngle;
        primary = -primary;
        }
    double              secondary = arcEllipse->vector90.Magnitude ();
    if (secondary < 0.0)
        {
        startAngle = -startAngle;
        sweepAngle = -sweepAngle;
        secondary  = -secondary;
        }

    RotMatrix           matrix;
    DPoint3d            center;
    arcEllipse->getScaledRotMatrix (&center, &matrix, NULL, NULL, NULL, NULL);

    // We have two choices here - we can reverse the normal and
    // maintain the direction of the arc, or we can reverse the
    // direction of the arc and maintain the normal.  This
    // is now under control of the "Force Postive Extrusion For Clockwise Arcs
    // setting.
    if (ARC_ELM == elemHandle.GetElementType() && sweepAngle < 0.0)
        {
        matrix.ScaleColumns (matrix, 1.0, -1.0, -1.0);
        startAngle = -startAngle;
        sweepAngle = -sweepAngle;
        }

    static double       NEAR_FullSweep = msGeomConst_2pi - 1.0E-7;

    if (sweepAngle > NEAR_FullSweep && sweepAngle != msGeomConst_2pi)
        {
#if defined (DIAGNOSTIC)
        if (com.bentley.sys.Diagnostic.INSTRUMENTED)
            System.out.println ("Rounding Arc with near complete Sweep to 2 Pi - Delta: " + (msGeomConst_2pi - sweepAngle));
#endif

        sweepAngle = msGeomConst_2pi;
        }

    dEllipse.InitFromScaledRotMatrix (center, matrix, primary, secondary, startAngle, sweepAngle);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt ExtractTextClipShape (DPoint3dP pPoints, MSElementDescrCP pDescr, double marginRatio)
    {
    TextBlockPtr textBlock = TextHandlerBase::GetFirstTextPartValue (ElementHandle (pDescr, false));
    if (!textBlock.IsValid ())
        return ERROR;
    
    DRange3d range;
    textBlock->ComputeElementRange (range);

    if (range.IsNull ())
        return BSIERROR;

    range.low.Add (textBlock->GetUserOrigin ());
    range.high.Add (textBlock->GetUserOrigin ());
            
    double      margin = textBlock->GetRunPropertiesForAdd ().GetFontSize ().y / marginRatio;

    pPoints[0].x = pPoints[3].x = pPoints[4].x = range.low.x  - margin;
    pPoints[1].x = pPoints[2].x                = range.high.x + margin;
    pPoints[0].y = pPoints[1].y = pPoints[4].y = range.low.y  - margin;
    pPoints[2].y = pPoints[3].y                = range.high.y + margin;

    for (int i=0; i<5; i++)
        pPoints[i].z = 0.0;

    RotMatrix   matrix = textBlock->GetOrientation ();
    matrix.Multiply (pPoints, pPoints, 5);

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @param    pLength     OUT     if zero, the poles are coincident
* @bsimethod                                                    DavidAssaf       01/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ArePolesColinear
(
double*                     pLength,
DPoint3dP                   pPoles,
double*                     pWeights,
int                         numPoles
)
    {
    // find max deviation of curve from being linear
    double  deviation = 0.0, length = 0.0;
    double  relTol = 1.0e-4;

    if (NULL != pWeights)
        bsputil_unWeightPoles (pPoles, pPoles, pWeights, numPoles);

    // need to test unweighted poles...
   bsiGeom_approximateLineThroughPoints (NULL, NULL, &length, &deviation, pPoles, numPoles);

    if (NULL != pWeights)
        bsputil_weightPoles (pPoles, pPoles, pWeights, numPoles);

    if (NULL != pLength)
        *pLength = length;

    return deviation <= relTol * length;
    }


/*---------------------------------------------------------------------------------**//**
* @return true if B-spline curve has at least one linear Bezier segment.
* @param pSegmentParams     IN OUT  on input, an empty array; on true return, each point
*                                   is the extent of maximal linear segments (negative
*                                   parameters) and maximal nonlinear segments (positive
*                                   parameters).
* @bsimethod                                                    DavidAssaf       01/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsSplineCurveSegmentLinear
(
MSBsplineCurve*             pCurve,
bvector<DPoint2d>*      pSegmentParams
)
    {
    DPoint4d*               pBsplinePoles = NULL, *pBezierPoles = NULL;
    DPoint2d                span;
    double                  length = 0.0;
    double*                 pBezierSpans = NULL;
    int                     order = pCurve->params.order;
    int                     numPoles = pCurve->params.numPoles;
    bool                    bRational = 0 != pCurve->rational;
    bool                    bHasLinearSegment = false;
    int                     numKnots = bspknot_numberKnots (numPoles, order, pCurve->params.closed);
    int                     numBeziers = 0;

    // if pCurve is Bezier, no need to explode it
    if (numPoles == order)
        {
        bHasLinearSegment = ArePolesColinear (&length, pCurve->poles, bRational ? pCurve->weights : NULL, numPoles);
        if (bHasLinearSegment && length > 0.0 && NULL != pSegmentParams)
            {
            span.x =  0.0;
            span.y = -1.0;
            pSegmentParams->push_back (span);
            }
        return bHasLinearSegment;
        }

    // fill bsplinePoles with (weighted) curve poles
    pBsplinePoles = (DPoint4d *) _alloca (numPoles * sizeof(DPoint4d));
    for (int iPole = 0; iPole < numPoles; iPole++)
        {
        pBsplinePoles[iPole].x = pCurve->poles[iPole].x;
        pBsplinePoles[iPole].y = pCurve->poles[iPole].y;
        pBsplinePoles[iPole].z = pCurve->poles[iPole].z;
        pBsplinePoles[iPole].w = bRational ? pCurve->weights[iPole] : 1.0;
        }

    // ensure space for shared bezier Poles
    int         nBezierPoles = (1 + (order - 1) * (numKnots - 2 * order + 1));
    pBezierPoles =  (DPoint4d *)_alloca (nBezierPoles * sizeof(DPoint4d));

    // ensure space for shared bezier spans
    int         nBezierSpans = (numKnots - 2 * order + 2);
    pBezierSpans =  (double *)_alloca (nBezierSpans * sizeof(DPoint2d));

    numBeziers = bsiBezierDPoint4d_convertBsplineToBeziersExt (pBezierPoles, pBezierSpans, pBsplinePoles, numPoles, pCurve->knots, numKnots,
                                                                1.0e-12, order, 0 != pCurve->params.closed, true);
    if (numBeziers > 0)
        {
        // 1st entry of arrays is reserved for 1st pole/weight of previous linear segment
        DPoint3d    segmentWeightedPoles[MAX_ORDER + 1];
        double      segmentWeights[MAX_ORDER + 1];
        bool        bThisLinear, bLastLinear = false;
        bool        bThisRational;

        for (int iBezier = 0; iBezier < numBeziers; iBezier++, pBezierPoles += order - 1, pBezierSpans++)
            {
            bThisRational = false;

            for (int jPole = 0; jPole < order; jPole++)
                {
                segmentWeightedPoles[jPole + 1].xyzOf (&pBezierPoles[jPole]);
                if (1.0 != (segmentWeights[jPole + 1] = pBezierPoles[jPole].w))
                    bThisRational = true;
                }

            bThisLinear = ArePolesColinear (&length, &segmentWeightedPoles[1], bThisRational ? &segmentWeights[1] : NULL, order);

            // ignore trivial Beziers (can happen if order or more poles are coincident)
            if (0.0 == length)
                continue;

            if (bThisLinear && !bHasLinearSegment)
                bHasLinearSegment = true;

            if (NULL != pSegmentParams)
                {
                if (bThisLinear)
                    {
                    if (   !bLastLinear
                        || !ArePolesColinear (NULL, &segmentWeightedPoles[0], &segmentWeights[0], order + 1)
                        || pSegmentParams->empty ())
                        {
                        // add new linear segment
                        span.x = - pBezierSpans[0];
                        span.y = - pBezierSpans[1];

                        // preserve first pole of new linear segment to test colinearity of maximal linear segment
                        segmentWeightedPoles[0] = segmentWeightedPoles[1];
                        segmentWeights[0] = segmentWeights[1];
                        }
                    else
                        {
                        // expand last linear segment if both it and this segment are colinear
                        span = pSegmentParams->back ();
                        pSegmentParams->pop_back ();
                        span.y = - pBezierSpans[1];
                        }
                    }
                else
                    {
                    if (bLastLinear || pSegmentParams->empty())
                        {
                        // add new nonlinear segment
                        span.x = pBezierSpans[0];
                        span.y = pBezierSpans[1];
                        }
                    else
                        {
                        // expand last nonlinear segment
                        span = pSegmentParams->back ();
                        pSegmentParams->pop_back ();
                        span.y = pBezierSpans[1];
                        }
                    }

                pSegmentParams->push_back (span);
                }

            bLastLinear = bThisLinear;
            }
        }

    return bHasLinearSegment;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendSplineToEdgeArray
(
AcGeVoidPointerArray&       edgeArray,
AcGeIntArray&               edgeTypes,
MSBsplineCurve*             pCurve
)
    {
    AcGePoint2dArray            poles;
    AcGeDoubleArray             weights;
    AcGeKnotVector              knots;
    bool                        isPeriodic = false;         //???


    if (pCurve->rational)
        bsputil_unWeightPoles (pCurve->poles, pCurve->poles, pCurve->weights, pCurve->params.numPoles);

    for (int iPole = 0; iPole < pCurve->params.numPoles; iPole++)
        {
        poles.append (RealDwgUtil::GePoint2dFromDPoint3d (pCurve->poles[iPole]));
        if (pCurve->rational)
            weights.append (pCurve->weights[iPole]);
        }

    int             nKnots = bspknot_numberKnots (pCurve->params.numPoles, pCurve->params.order, false); // open

    for (int iKnot = 0; iKnot < nKnots; iKnot++)
        knots.append (pCurve->knots[iKnot]);

    edgeArray.append (pCurve->rational ? new AcGeNurbCurve2d (pCurve->params.order-1, knots, poles, weights, isPeriodic) :
                                         new AcGeNurbCurve2d (pCurve->params.order-1, knots, poles, isPeriodic));
    edgeTypes.append (AcDbHatch::kSpline);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ExtractGeCurve2dFromElement
(
AcGeVoidPointerArray&       edgeArray,
AcGeIntArray&               edgeTypes,
ElementHandleCR             elemHandle,
TransformP                  pTransform
)
    {
    MSElementCP         pElem   = elemHandle.GetElementCP();

    switch (pElem->ehdr.type)
        {
        case    LINE_ELM:
        case    LINE_STRING_ELM:
        case    SHAPE_ELM:
        case    CMPLX_STRING_ELM:
            {
            DPoint3dArray   tmpPoints;
            if (RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, elemHandle))
                {
                size_t      nPoints = tmpPoints.size ();
                pTransform->Multiply (&tmpPoints.front(), (int)nPoints);
                for (size_t iPoint = 0; iPoint < nPoints-1; iPoint++)
                    {
                    edgeArray.append (new AcGeLineSeg2d (RealDwgUtil::GePoint2dFromDPoint3d (tmpPoints[iPoint]), RealDwgUtil::GePoint2dFromDPoint3d (tmpPoints[iPoint+1])));
                    edgeTypes.append (AcDbHatch::kLine);
                    }

                }
            break;
            }

        case    TEXT_ELM:
        case    TEXT_NODE_ELM:
            {
            DPoint3d        points[5];

            if (SUCCESS == ExtractTextClipShape (points, elemHandle.GetElementDescrCP(), TEXT_MARGIN_RATIO))
                {
                pTransform->multiply (points, points, 5);

                for (int iPoint = 0; iPoint < 4; iPoint++)
                    {
                    edgeArray.append (new AcGeLineSeg2d (RealDwgUtil::GePoint2dFromDPoint3d (points[iPoint]), RealDwgUtil::GePoint2dFromDPoint3d (points[iPoint+1])));
                    edgeTypes.append (AcDbHatch::kLine);
                    }
                }
            break;
            }


        case    CURVE_ELM:
        case    BSPLINE_CURVE_ELM:
            {
            MSBsplineCurve      curve;

            memset (&curve, 0, sizeof(&curve));
            if (SUCCESS == BSplineCurveHandler::CurveFromElement (curve, elemHandle))
                {
                bvector<DPoint2d>          segmentParams;

                bspcurv_openCurve (&curve, &curve, 0.0);
                bspcurv_transformCurve (&curve, &curve, pTransform);

                if (IsSplineCurveSegmentLinear (&curve, &segmentParams))
                    {
                    MSBsplineCurve   segment;

                    for (bvector<DPoint2d>::const_iterator pParams = segmentParams.begin(); pParams != segmentParams.end(); ++pParams)
                        {
                        // Bezier segment(s) are linear; add maximal line segment
                        if (pParams->x < 0.0 || pParams->y < 0.0)
                            {
                            DPoint3d        startPt, endPt;
                            double          v0 = mdlBspline_naturalParameterToFractionParameter (&curve, fabs (pParams->x));
                            double          v1 = mdlBspline_naturalParameterToFractionParameter (&curve, fabs (pParams->y));
                            bspcurv_evaluateCurvePoint (&startPt, NULL, &curve, v0);
                            bspcurv_evaluateCurvePoint (&endPt, NULL, &curve, v1);

                            if (!startPt.isEqual (&endPt))
                                {
                                edgeArray.append (new AcGeLineSeg2d (RealDwgUtil::GePoint2dFromDPoint3d (startPt), RealDwgUtil::GePoint2dFromDPoint3d (endPt)));
                                edgeTypes.append (AcDbHatch::kLine);
                                }
                            }
                        // Bezier segment(s) are nonlinear; add maximal nonlinear B-spline segment
                        else
                            {
                            double          u0 = mdlBspline_naturalParameterToFractionParameter (&curve, pParams->x);
                            double          u1 = mdlBspline_naturalParameterToFractionParameter (&curve, pParams->y);
                            if (SUCCESS == bspcurv_segmentCurve(&segment, &curve, u0, u1))
                                AppendSplineToEdgeArray (edgeArray, edgeTypes, &segment);
                            else
                                DIAGNOSTIC_PRINTF ("Error on BSpline curve segmentation from hatch boundary element %I64d!\n", pElem->ehdr.uniqueId);

                            bspcurv_freeCurve (&segment);
                            }
                        }
                    }
                else
                    {
                    AppendSplineToEdgeArray (edgeArray, edgeTypes, &curve);
                    }
                bspcurv_freeCurve (&curve);
                }
            break;
            }

        case    ARC_ELM:
        case    ELLIPSE_ELM:
            {
            DEllipse3d      dEllipse;
            if (SUCCESS == this->ExtractDEllipse3dFromElement (dEllipse, elemHandle))
                {
                double          r0, r1, start, sweep, end;
                DPoint3d        center;
                RotMatrix       rotMatrix;
                bool            isClockwise = false;

                dEllipse.productOf (pTransform, &dEllipse);
                dEllipse.getScaledRotMatrix (&center, &rotMatrix, &r0, &r1, &start, &sweep);

                // TR#  147232 - Avoid small ellipses that AutoCAD will misinterpet.
                if (fabs (sweep) < MINIMUM_GeCurve2DSweep)
                    {
                    DPoint3d        linePoints[2];

                    dEllipse.evaluateEndPoints (&linePoints[0], &linePoints[1]);
                    edgeArray.append (new AcGeLineSeg2d (RealDwgUtil::GePoint2dFromDPoint3d (linePoints[0]), RealDwgUtil::GePoint2dFromDPoint3d (linePoints[1])));
                    edgeTypes.append (AcDbHatch::kLine);

                    DIAGNOSTIC_PRINTF ( "Degenerate Ellipse Segment (sweep: %lf) replaced with line\n", sweep);
                    break;
                    }

                DVec3d column2;
                rotMatrix.GetColumn (column2, 2);
                if (column2.z < 0.0)
                    {
                    start = -start;
                    sweep = -sweep;
                    DVec3d column1;
                    rotMatrix.GetColumn (column1, 1);
                    column1.Negate();
                    rotMatrix.SetColumn (column1, 1);
                    }

                if (dEllipse.IsCircular ())
                    {
                    DVec3d          column0;
                    rotMatrix.getColumn (&column0, 0);

                    if (Angle::IsFullCircle (sweep))
                        {
                        start = 0.0;
                        end   = msGeomConst_2pi;
                        }
                    else
                        {
                        /*--------------------------------------------------------------------------------------------
                        We intended to use a vector reference(column0) for start & end angles to create an arc edge via
                        AcGeCircArc2d, but this vector gets lost as a hatch boundary in DWG.  That is, the DWG file
                        format does not have a place to hold arc vector as a hatch boundary.  The end result is an arc
                        edge with wrong start & end angles.  A hatch boundary assumes the arc axes to align with the
                        those of the hatch's.  Hence, we want to unrotate the start angle for a rotated arc boundary.
                        --------------------------------------------------------------------------------------------*/
                        if (!rotMatrix.isIdentity())
                            {
                            double      arcRotation = atan2 (column0.y, column0.x);
                            if (fabs(arcRotation) > TOLERANCE_ArcAngle)
                                start = bsiTrig_getPositiveNormalizedAngle (start + arcRotation);
                            }
                        
                        if (sweep < 0.0)
                            {
                            isClockwise = true;
                            start = -start;
                            sweep = -sweep;
                            }

                        start = bsiTrig_getPositiveNormalizedAngle (start);
                        end   = bsiTrig_getPositiveNormalizedAngle (start + sweep);
                        }
                    edgeArray.append (new AcGeCircArc2d (RealDwgUtil::GePoint2dFromDPoint3d (center),  r0, start, end, RealDwgUtil::GeVector2dFromDPoint3d (column0), isClockwise));
                    edgeTypes.append (AcDbHatch::kCirArc);
                    }
                else
                    {
                    DVec3d            majorAxis, minorAxis;
                    if (r0 < r1)
                        {
                        double          tmp = r0;

                        r0 = r1;
                        r1 = tmp;

                        rotMatrix.GetColumn (majorAxis, 1);
                        rotMatrix.GetColumn (minorAxis, 0);
                        minorAxis.Negate ();
                        start -= msGeomConst_piOver2;
                        }
                    else
                        {
                        rotMatrix.GetColumn (majorAxis, 0);
                        rotMatrix.GetColumn (minorAxis, 1);
                        }

                    if (Angle::IsFullCircle (sweep))
                        {
                        start = 0.0;
                        end = msGeomConst_2pi;
                        }
                    else
                        {
                        if (sweep < 0.0)
                            {
                            start = -start;
                            sweep = -sweep;
                            minorAxis.scale (-1.0);
                            }
                        start = bsiTrig_getPositiveNormalizedAngle (start);
                        end   = bsiTrig_getPositiveNormalizedAngle (start + sweep);
                        }

                    edgeArray.append (new AcGeEllipArc2d (RealDwgUtil::GePoint2dFromDPoint3d (center),
                                                          RealDwgUtil::GeVector2dFromDPoint3d (majorAxis),
                                                          RealDwgUtil::GeVector2dFromDPoint3d (minorAxis),
                                                          r0, r1, start, end));
                    edgeTypes.append (AcDbHatch::kEllArc);
                    }
                }
            break;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::ExtractPolylineTransformFromElement
(
TransformR                  compositeTransform,
DPoint3dR                   headerNormal,
DPoint3dR                   normal,
double&                     elevation,
ElementHandleCR             inElement,
DPoint3dR                   defaultNormal
)
    {
    DPoint3d                normalPoint = DPoint3d::FromZero ();

    compositeTransform.InitIdentity ();
    headerNormal.Zero ();
    normal.Zero ();
    elevation = 0.0;

    // Extract normal and check planarity
    if (this->IsNonPlanarPolyline (inElement, LineString_LWPolyline == this->GetSettings().GetLineStringMapping (false), &normal, &normalPoint, &defaultNormal))
        return  NotPlanarElement;

    if (normal.DotProduct (defaultNormal) < 0.0 || normal.z < MINIMUM_NormalZ)  // AutoCAD won't PEDIT a  polyline with normal (0, 0, -1.0).
        normal.Scale (normal, -1.0);

    this->GetTransformFromDGN().Multiply (normalPoint);
    RotMatrix   rotMatrix;
    this->GetLocalTransform().GetMatrix (rotMatrix);
    rotMatrix.Multiply (headerNormal, normal);
    headerNormal.Normalize();

    elevation = headerNormal.DotProduct (normalPoint);
    if (fabs (elevation) < MIN_ElevationMagnitude)
        elevation = 0.0;

    Transform               displayTransform, inverseDisplayTransform;
    compositeTransform = this->GetTransformFromDGN();

    // if we are enforcing 0-z coordinate, do not transform the polyline to its normal plane (reinstated the logic lost during the transit from OpenDWG to RealDWG years ago). TFS 7574.
    if (this->GetSettings().IsZeroZCoordinateEnforced())
        return  BSISUCCESS;

    if (RealDwgUtil::GetExtrusionTransform (displayTransform, RealDwgUtil::GeVector3dFromDPoint3d(headerNormal), elevation))
        {
        inverseDisplayTransform.InverseOf (displayTransform);
        compositeTransform.InitProduct (inverseDisplayTransform, compositeTransform);
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractPolylineData (double& extrusionDistance, TransformR compositeTransform, DPoint3dR normal, DPoint3dR headerNormal, double& elevation, ElementHandleCR elemHandle, DPoint3dR defaultNormal)
    {
    MSElementDescrCP        inElement = elemHandle.GetElementDescrCP();

    /*----------------------------------------------------------------------------------------------------------------------
    This doesn't work right on cone, surface, or solid.  There is probably never a need for these elements.

    But if we will ever have to review the old logic, this is how we used to extract extrusion in 8.11.9:

    if (RealDwgSuccess == this->ExtractThicknessFromElement(newElement, extrusionDirection, extrusionDistance, inElement))
        defaultNormal = extrusionDirection;
    else
        this->GetEntityThicknessFromElementLinkage (extrusionDistance, defaultNormal, &defaultNormal, inElement);
    ----------------------------------------------------------------------------------------------------------------------*/
    BeAssert ( (CONE_ELM != elemHandle.GetElementType()) && (SURFACE_ELM != elemHandle.GetElementType()) && (SOLID_ELM != elemHandle.GetElementType()) );

    // Note: ExtractThicknessFromElement will only return non-NULL if the input element is either a cone or a surface.
    this->GetEntityThicknessFromElementLinkage (extrusionDistance, defaultNormal, &defaultNormal, elemHandle);

    StatusInt               status;
    if (SUCCESS != (status = this->ExtractPolylineTransformFromElement (compositeTransform, headerNormal, normal, elevation, elemHandle, defaultNormal)))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    if ( (extrusionDistance > 0.0) && (defaultNormal.dotProduct (&normal) < 0.0) )
        extrusionDistance = -extrusionDistance;

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (this->GetSettings().IsZeroZCoordinateEnforced())
        {
        elevation = 0.0;

        headerNormal.x = 0.0;
        headerNormal.y = 0.0;
        headerNormal.z = 1.0;
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractLWPolylineFromElement
(
DPoint3dArrayR              points,
DoubleArrayR                bulges,
DPoint2dArrayR              widths,
bool&                       hasConstantWidth,
double&                     constantWidth,
bool&                       closed,
bool&                       continueLt,
ElementHandleCR             elemHandle,
DPoint3dCR                  normal,
TransformCP                 transform,
double                      connectionTolerance,
bool                        ignoreDisconnects
)
    {
    bool                    nonZeroWidth    = false;
    DPoint2d                width;          // X == startWidth, Y == endWidth;
    StatusInt               status;

    this->ExtractPolylineWidth (hasConstantWidth, constantWidth, nonZeroWidth, elemHandle);

    Int32                   style = -1;
    this->GetLinestyleFromElement (style, continueLt, elemHandle);

    LineStyleParams         styleParams;
    MSElementTypes          type = static_cast <MSElementTypes> (elemHandle.GetElementType());
    switch (type)
        {
        case ARC_ELM:
        case ELLIPSE_ELM:
            {
            double      startAngle      = 0.0;
            double      sweepAngle      = 0.0;
            double      primary         = 0.0;
            double      secondary       = 0.0;
            DPoint3d    center, start, end;
            RotMatrix   rMatrix;

            status = ArcHandler::Extract (&start, &end, &startAngle, &sweepAngle, &primary, &secondary, &rMatrix, NULL, &center, elemHandle);
            if (SUCCESS == status)
                {
                if (IsCircularEllipse (primary, secondary))
                    {
                    double  startWidth, endWidth;

                    this->ExtractWidthFromElement (startWidth, endWidth, elemHandle);

                    closed = (type == ELLIPSE_ELM);
                    if (Angle::IsFullCircle (sweepAngle))
                        {
                        //  Donut...
                        double          halfAngle = startAngle + sweepAngle / 2.0;
                        DPoint3d        halfPoint;

                        halfPoint.x = primary * cos (halfAngle);
                        halfPoint.y = secondary * sin (halfAngle);
                        halfPoint.z = 0.0;
                        rMatrix.Multiply (halfPoint);
                        halfPoint.SumOf (halfPoint, center);

                        points.push_back (start);
                        points.push_back (halfPoint);
                        points.push_back (end);

                        bulges.push_back (1.0);
                        bulges.push_back (1.0);
                        bulges.push_back (0.0);
                        }
                    else
                        {
                        points.push_back (start);
                        points.push_back (end);
                        double      bulgeFactor = tan (sweepAngle/4.0);
                        DVec3d      arcZ;
                        rMatrix.GetColumn (arcZ, 2);
                        if (arcZ.DotProduct (normal) < 0.0)
                            bulgeFactor = -bulgeFactor;

                        bulges.push_back (bulgeFactor);
                        bulges.push_back (0.0);

                        this->ExtractWidthFromElement (startWidth, endWidth, elemHandle);
                        if (nonZeroWidth)
                            {
                            width.x = startWidth;
                            width.y = endWidth;
                            widths.push_back (width);

                            width.zero ();
                            widths.push_back (width);
                            }
                        }
                    }
                else
                    {
                    // This must be from saving to pre R13 (when the AutoDimwits didn't support ellipses.
                    int         nArcs = (int) (.50 + fabs (sweepAngle) * 16.0/msGeomConst_pi);
                    double      angle, angleDelta;
                    DPoint2d    m1, m2;
                    DPoint3d    p1, p2;

                    if (nArcs < 2)
                        nArcs = 2;

                    angleDelta = sweepAngle / (double) nArcs;
                    int iArc;
                    for (iArc=0, angle=startAngle; iArc<=nArcs; iArc++, angle+=angleDelta)
                        {
                        p2.init (primary  * cos (angle), secondary * sin (angle), 0.0);
                        rMatrix.multiply (&p2, &p2, 1);
                        p2.sumOf (&p2, &center);

                        m2.x = - primary * sin(angle);
                        m2.y = secondary * cos (angle);
                        double  magnitude = sqrt (m2.x * m2.x +  m2.y * m2.y);
                        if (fabs (magnitude) > 1.0E-12)
                            m2.scale (&m2, 1.0/magnitude);

                        if (0 != iArc)
                            {
                            double arcSweep = acos (m1.x * m2.x + m1.y * m2.y);
                            double bulge = tan (arcSweep/4.0);
                            if (sweepAngle < 0.0)
                                bulge = -bulge;

                            points.push_back (p1);
                            bulges.push_back (bulge);
                            }
                        p1 = p2;
                        m1 = m2;
                        }
                    if (Angle::IsFullCircle(sweepAngle))
                        {
                        bulges.push_back (0.0);
                        points.push_back (p2);
                        }
                    }
                }
            break;
            }

        case LINE_ELM:
        case LINE_STRING_ELM:
        case SHAPE_ELM:
        case POINT_STRING_ELM:
            {
            CurveVectorPtr      curveVector = ICurvePathQuery::ElementToCurveVector (elemHandle);
            if (curveVector.IsValid())
                {
                DPoint3dArray   tmpPoints;
                if (CantExtractPoints == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, *curveVector))
                    break;

                size_t          nTmpPoints = tmpPoints.size ();
                if (ignoreDisconnects)
                    {
                    RealDwgUtil::RemoveDisconnects (tmpPoints.data(), nTmpPoints);
                    }
                else
                    {
                    for each (DPoint3d point in tmpPoints)
                        if (point.x == DISCONNECT)
                            return CantExtractPoints;
                    }

                for each (DPoint3d point in tmpPoints)
                    points.push_back (point);

                double                  startWidth, endWidth;

                this->ExtractWidthFromElement (startWidth, endWidth, elemHandle);
                if (nonZeroWidth)
                    {
                    double          delta = (endWidth - startWidth) / (double) (nTmpPoints - 1);
                    size_t          iPoint;
                    for (iPoint = 0, width.x = startWidth, width.y = width.x + delta; iPoint < nTmpPoints; iPoint++, width.x = width.y, width.y += delta)
                        widths.push_back (width);
                    }
                ElementHandle       eh(elemHandle.GetElementCP(), m_model);
                if (BSISUCCESS != LineStyleUtil::GetParamsFromElement(&styleParams, eh))
                    styleParams.Init ();
                if (0 != (styleParams.modifiers & STYLEMOD_NOSEGMODE))
                    continueLt = true;
                }
            break;
            }

        case CMPLX_SHAPE_ELM:
        case CMPLX_STRING_ELM:
            {
            // Count points and check that all components are linear
            bool                bulgesRequired = false;

            closed = (CMPLX_SHAPE_ELM == type);
            // we can probably iterate over CurveVector but we also need the linestyle from each element, so iterate over element handle instead.
            for (ChildElemIter child(elemHandle, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
                {
                CurveVectorPtr      curveVector = ICurvePathQuery::ElementToCurveVector (child);
                if (!curveVector.IsValid())
                    continue;

                size_t              nTmpPoints      = 0, lastPointIndex = points.size() == 0 ? 0 : points.size() - 1;
                double              startWidth, endWidth;
                double              startAngle      = 0.0;
                double              sweepAngle      = 0.0;
                double              primary         = 0.0;
                double              secondary       = 0.0;
                DPoint3d            center, start, end;
                RotMatrix           rMatrix;

                this->ExtractWidthFromElement (startWidth, endWidth, child);
                if (SUCCESS != LineStyleUtil::GetParamsFromElement(&styleParams, child))
                    styleParams.Init ();
                if (0 != (styleParams.modifiers & STYLEMOD_NOSEGMODE))
                    continueLt = true;

                DPoint3dArray       tmpPoints;
                if (CantExtractPoints == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, *curveVector))
                    continue;

                if ((nTmpPoints = tmpPoints.size()) > 0)
                    {
                    if (lastPointIndex > 0 && tmpPoints.front().IsEqual(points.back(), connectionTolerance))
                        {
                        points.erase (points.end()-1);
                        bulges.erase (bulges.end()-1);
                        if (nonZeroWidth)
                            widths.erase (widths.end()-1);
                        }

                    for each (DPoint3d point in tmpPoints)
                        {
                        points.push_back (point);
                        bulges.push_back (0.0);
                        }

                    if (nonZeroWidth)
                        {
                        double      delta = (endWidth - startWidth) / (double) (nTmpPoints - 1);
                        size_t      iPoint;
                        for (iPoint = 0, width.x = startWidth, width.y = width.x + delta; iPoint < nTmpPoints; iPoint++, width.x = width.y, width.y += delta)
                            widths.push_back (width);
                        }
                    }
                else if (SUCCESS == ArcHandler::Extract(&start, &end, &startAngle, &sweepAngle, &primary, &secondary, &rMatrix, NULL, &center, child))
                    {
                    if (! IsCircularEllipse (primary, secondary))
                        {
                        points.clear ();
                        bulges.clear ();
                        widths.clear ();

                        return CantExtractPoints;
                        }
                    if (lastPointIndex > 0 && start.IsEqual(points.back(), connectionTolerance))
                        {
                        if (!points.empty())
                            points.pop_back ();
                        if (!bulges.empty())
                            bulges.pop_back ();
                        if (!widths.empty())
                            widths.pop_back ();
                        }

                    points.push_back (start);

                    DVec3d      arcZ;
                    rMatrix.GetColumn (arcZ, 2);
                    double      bulgeFactor;
                    if (Angle::IsFullCircle(sweepAngle))
                        {
                        double          halfAngle = startAngle + sweepAngle / 2.0;
                        DPoint3d        halfPoint;

                        halfPoint.x = primary * cos (halfAngle);
                        halfPoint.y = secondary * sin (halfAngle);
                        halfPoint.z = 0.0;
                        rMatrix.Multiply (halfPoint);
                        halfPoint.SumOf (halfPoint, center);

                        bulgeFactor = 1.0;
                        if (arcZ.DotProduct (normal) < 0.0)
                            bulgeFactor = -bulgeFactor;

                        points.push_back (halfPoint);
                        bulges.push_back (bulgeFactor);
                        }
                    else
                        {
                        bulgeFactor = tan (sweepAngle/4.0);
                        rMatrix.GetColumn (arcZ, 2);
                        if (arcZ.DotProduct (normal) < 0.0)
                            bulgeFactor = -bulgeFactor;
                        }

                    points.push_back (end);
                    bulges.push_back (bulgeFactor);
                    bulges.push_back (0.0);
                    bulgesRequired = true;

                    if (nonZeroWidth)
                        {
                        width.x = startWidth;
                        width.y = endWidth;
                        widths.push_back (width);

                        width.zero ();
                        widths.push_back (width);
                        }
                    }
                else if (curveVector->front()->GetCurvePrimitiveType() == ICurvePrimitive::CURVE_PRIMITIVE_TYPE_BsplineCurve)
                    {
                    return CantExtractPoints;
                    }
                else
                    {
                    break;      // Bad element, break out and return error
                    }
                }

            // If it's closed, and the last point matches the start point, it should be omitted then omit it.
            // it would be a zero-length segment anyway, but MicroStation's CLS system handles these poorly.  TR: 102374
            DPoint3dCR  firstPoint = points.front ();
            DPoint3dR   lastPoint  = points.back ();
            if (closed && points.size() > 2 && firstPoint.IsEqual(lastPoint, connectionTolerance))
                {
                // make sure the point is exactly equal.
                lastPoint = firstPoint;
                }

            if (!bulgesRequired)
                bulges.clear ();

            break;
            }
        }

    // if requested for 3D polyline points, return them as they are.
    if (NULL == transform)
        return  RealDwgSuccess;

    size_t      nPoints = points.size ();
    if (nPoints > 0)
        {
        transform->Multiply (points.data(), points.data(), (int)nPoints);
        return RealDwgSuccess;
        }

    return  CantExtractPoints;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractPolylineFromElement
(
DPoint3dArrayR              points,
DoubleArrayR                bulges,
DPoint2dArrayR              widths,
bool&                       hasConstantWidth,
double&                     constantWidth,
bool&                       closed,
bool&                       continueLt,
ElementHandleCR             elemHandle,
AcDbObjectP                 existingObject,
bool                        only2D
)
    {
    DPoint3d                defaultNormal = DPoint3d::From(0, 0, 1);
    if (NULL != existingObject)
        {
        AcDbPolyline*       pline = AcDbPolyline::cast(existingObject);
        if (NULL != pline)
            RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, pline->normal());
        }

    DPoint3d                normal, headerNormal;
    Transform               transform;
    double                  extrusionDistance = 0.0, elevation = 0.0;

    RealDwgStatus status = this->ExtractPolylineData (extrusionDistance, transform, normal, headerNormal, elevation, elemHandle, defaultNormal);
    if (RealDwgSuccess != status && (only2D || NotPlanarElement != status))
        return  status;

    // extract polyline data with untransformed points
    status = this->ExtractLWPolylineFromElement (points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt, 
                                                 elemHandle, normal, NULL, TOLERANCE_ComplexChainGap, true);
    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2006
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ZeroElementIds (MSElementDescrP  pDescr)
    {
    for (; NULL != pDescr; pDescr = pDescr->h.next)
        {
        pDescr->el.ehdr.uniqueId = 0;

        if (pDescr->h.isHeader)
            ZeroElementIds (pDescr->h.firstElem);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2006
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveRemainingSolidDescrToDatabase (MSElementDescrP pDescr)
    {
    if (NULL == pDescr)
        return;
    if (this->GetCurrentBlockId().isNull())
        return;

    ZeroElementIds (pDescr);

    ElementHandle   eeh(pDescr, false, true);
    this->SaveBlockChildrenToDatabase (this->GetCurrentBlockId(), NULL, eeh, false, false);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/07
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 CookViewFlags
(
ViewFlagsR                  viewFlags,
DgnFileP                    dgnFile,
ModelId                     modelId
)
    {
    /*-----------------------------------------------------------------------------------
    When in a 2D model, there should be no front/back clipping, nor camera.  This used to
    be handled in view_extractElmdscr which called mdlViewInfo_coerceForDimensionOfModel.
    Now that call has been removed so we have a true raw viewinfo that needs cooked here.
    -----------------------------------------------------------------------------------*/
    ModelIndexItemCP    indexItem = dgnFileObj_getModelItemByID (dgnFile, modelId, false);

    if (NULL != indexItem && !indexItem->Is3D())
        {
        viewFlags.noFrontClip = true;
        viewFlags.noBackClip = true;
        viewFlags.camera = false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::CalculateDwgViewParams
(
double&                     backClipDistance,               // <=
bool&                       backClipEnabled,                // <=
double&                     frontClipDistance,              // <=
bool&                       frontClipEnabled,               // <=
bool&                       frontClipAtEye,                 // <=
AcGePoint2d&                geCenterPoint,                  // <=
double&                     height,                         // <=
double&                     width,                          // <=
double&                     lensLength,                     // <=
bool&                       perspectiveEnabled,             // <=
AcGePoint3d&                geTarget,                       // <=
AcGeVector3d&               geViewDirection,                // <=
double&                     viewTwist,                      // <=
DPoint3dCR                  inOrigin,                       // =>
DPoint3dCR                  inDelta,                        // =>
RotMatrixCR                 inRMatrix,                      // =>
bool                        inCameraOn,                     // =>
DPoint3dCR                  inCameraPosition,               // =>
double                      inCameraFocalLength,            // =>
bool                        inNoFrontClip,                  // =>
bool                        inNoBackClip,                   // =>
double                      inScale,                        // =>
TransformCR                 inTransform                     // =>
)
    {
    DPoint3d            origin, delta, cameraPosition;

    // Apply Transformation From DGN.
    inTransform.Multiply (origin, inOrigin);
    RotMatrix   rotMatrix;
    inTransform.GetMatrix (rotMatrix);
    rotMatrix.Multiply (delta, inDelta);
    inTransform.Multiply (cameraPosition, inCameraPosition);

    double              cameraFocalLength = inCameraFocalLength * inScale;

    if (delta.x < MINIMUM_VIEW_DELTA)
        delta.x = 1.0;

    if (delta.y < MINIMUM_VIEW_DELTA)
        delta.y = 1.0;

    DVec3d          xVector, viewDirection;
    DPoint3d        target;

    inRMatrix.GetRow (xVector, 0);
    inRMatrix.GetRow (viewDirection, 2);

    RotMatrix       arbitraryMatrix;
    RealDwgUtil::RotMatrixFromArbitraryAxis (arbitraryMatrix, viewDirection);
    arbitraryMatrix.MultiplyTranspose (xVector);
    viewTwist = bsiTrig_getPositiveNormalizedAngle (- atan2 (xVector.y, xVector.x));

    frontClipEnabled = !inNoFrontClip;
    frontClipAtEye   =  inNoFrontClip;
    backClipEnabled  = !inNoBackClip;

    if (inCameraOn)
        {
        perspectiveEnabled = true;
        target.SumOf (cameraPosition, viewDirection, -cameraFocalLength);
        viewDirection.Scale (cameraFocalLength);
        lensLength = 35.0 * cameraFocalLength / ((delta.x > delta.y) ? delta.x : delta.y);
        }
    else
        {
        perspectiveEnabled = false;
        target.zero ();
        lensLength = 50.0;
        }

    DPoint3d        viewTarget, viewOrigin;
    inRMatrix.Multiply (viewTarget, target);
    inRMatrix.Multiply (viewOrigin, origin);

    geCenterPoint.x = viewOrigin.x - viewTarget.x + delta.x/2.0;
    geCenterPoint.y = viewOrigin.y - viewTarget.y + delta.y/2.0;
    height = delta.y;
    width  = height * delta.x / delta.y;

    backClipDistance  = backClipEnabled  ? (viewOrigin.z - viewTarget.z) : 0.0;
    frontClipDistance = frontClipEnabled ? (viewOrigin.z - viewTarget.z + delta.z) : 0.0;

    geViewDirection = RealDwgUtil::GeVector3dFromDPoint3d (viewDirection);
    geTarget = RealDwgUtil::GePoint3dFromDPoint3d (target);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsTextNodeWithEnterDataFields (MSElementDescrCP  pDescr)
    {
    if (TEXT_NODE_ELM == pDescr->el.ehdr.type)
        {
        for (MSElementDescrP pChild = pDescr->h.firstElem; NULL != pChild; pChild = pChild->h.next)
            if (IsEnterDataField (&pChild->el))
                return true;
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::EraseDeletedBlockChildren
(
AcDbObjectId                blockId,
DgnModelP                   modelRef
)
    {
    AcDbBlockTableRecordPointer pBlock (blockId, AcDb::kForWrite);
    if (Acad::eOk == pBlock.openStatus())
        {
        AcDbBlockTableRecordIterator*   pEntIter;
        pBlock->newIterator (pEntIter, true, true);

        // Remove any children that were deleted during the session.
        for (; !pEntIter->done(); pEntIter->step())
            {
            ElementRefP     elementRef = NULL;
            AcDbObjectId    childId;
            pEntIter->getEntityId (childId);

            if (!childId.isNull() && NULL != (elementRef = modelRef->FindByElementId(this->ElementIdFromObjectId(childId))) && elementRef->IsDeleted())
                {
                AcDbObjectPointer<AcDbObject>   pObject (childId, AcDb::kForWrite);
                if (Acad::eOk == pObject.openStatus())
                    pObject->erase();
                }
            }

        delete pEntIter;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveBlockChildrenToDatabase
(
AcDbObjectId                blockId,
TransformCP                 pTransform,
ElementHandleR              cellElement,
bool                        ignoreAttributes,
bool                        setSortEntities
)
    {
    if (!cellElement.IsValid())
        return  MstnElementUnacceptable;

    // Each block may have its own sortents dictionary:
    PrioritySorter          prioritySorter(PrioritySorter::ByElementSequence);
    PrioritySorter*         currentPrioritySorter = this->GetPrioritySorter ();
    this->SetPrioritySorter (&prioritySorter);

    AcDbObjectId            currentBlockId = this->GetCurrentBlockId();
    this->SetCurrentBlockId (blockId);

    Transform               currentTo, currentFrom, currentLocal;
    if (NULL != pTransform)
        this->PushTransform (&currentTo, &currentFrom, &currentLocal, pTransform);

    MSElementTypes          parentType = (MSElementTypes) cellElement.GetElementType ();

    try
        {
        if (this->SavingChanges())                              // Fix for TR# 145739
            EraseDeletedBlockChildren (blockId, m_model);

        for (ChildElemIter child(cellElement, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
            {
            // Modeler and interior nodes from TriForma are stored as type 2 cells - in general, this hierarchy is not
            // useful in AutoCAD and we just collapse it.
            // Also - a shared cell definition should never appear nested within a complex - if anomaly occurs
            // as it did in a test file from Linda Newcombe - just ignore the inner nesting (as MicroStation's display does)
            // and import the children.
            MSElementDescrCP    elmdscr = child.GetElementDescrCP ();
            if (NULL == elmdscr)
                continue;
            if ( (SHAREDCELL_DEF_ELM == elmdscr->el.ehdr.type) || IsACISModelerNode (&elmdscr->el) ||
                 (CELL_HEADER_ELM == parentType && IsTextNodeWithEnterDataFields(elmdscr)) ||
                 (m_ignoreTriformaNodes && (CELL_HEADER_ELM == elmdscr->el.ehdr.type && !GroupedHoleHandler::IsGroupedHole(child) && !IsBRepSolid(child))) )
                {
                this->SaveBlockChildrenToDatabase (blockId, NULL, child);
                }
            else
                {
                if (!ignoreAttributes || ATTRIBUTE_ELM != elmdscr->el.ehdr.type)
                    {
                    this->SaveElementToDatabase (child, NULL, &prioritySorter);
                    if (setSortEntities)
                        prioritySorter.AddElement (child);
                    }
                }
            }
        }
    catch (...)
        {
        }
    if (setSortEntities)
        prioritySorter.SetSortEnts (m_pFileHolder->GetDatabase(), blockId, *this);

    this->SetCurrentBlockId (currentBlockId);
    this->SetPrioritySorter (currentPrioritySorter);

    if (NULL != pTransform)
        this->PopTransform (&currentTo, &currentFrom, &currentLocal);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 TestLevelOrSymbologyMatch
(
UInt32*                     pLevel,
UInt32*                     pColor,
Int32*                      pStyle,
UInt32*                     pWeight,
MSElementDescrCP            pDescr,
bool*                       pFirst,
ConvertFromDgnContext&      context
)
    {
    for (; NULL != pDescr; pDescr = pDescr->h.next)
        {
        if (pDescr->el.ehdr.isGraphics)
            {
            if (pDescr->h.isHeader)
                {
                if (!TestLevelOrSymbologyMatch (pLevel, pColor, pStyle, pWeight, pDescr->h.firstElem, pFirst, context))
                    return false;
                }
            else
                {
                // If we're ignoring ignoring construction class geometry.
                DgnElementClass     elementClass = (DgnElementClass)pDescr->el.hdr.dhdr.props.b.elementClass;
                if ( ( (DgnElementClass::Construction == elementClass) || (DgnElementClass::ConstructionRule == elementClass) ) && (Construction_Omit == context.GetSettings().GetConstructionClassMapping()) )
                    continue;

                if (*pFirst)
                    {
                    *pFirst = false;

                    if (NULL != pColor)
                        *pColor = pDescr->el.hdr.ehdr.isGraphics ? pDescr->el.hdr.dhdr.symb.color : 0;
                    if (NULL != pStyle)
                        *pStyle = pDescr->el.hdr.ehdr.isGraphics ? pDescr->el.hdr.dhdr.symb.style : 0;
                    if (NULL != pWeight)
                        *pWeight = pDescr->el.hdr.ehdr.isGraphics ? pDescr->el.hdr.dhdr.symb.weight : 0;
                    if (NULL != pLevel)
                        *pLevel = pDescr->el.ehdr.level;
                    }
                else
                    {
                    if ((NULL != pLevel  && *pLevel  != pDescr->el.ehdr.level) ||
                        (NULL != pColor  && *pColor  != pDescr->el.hdr.dhdr.symb.color) ||
                        (NULL != pWeight && *pWeight != pDescr->el.hdr.dhdr.symb.weight) ||
                        (NULL != pStyle  && *pStyle  != pDescr->el.hdr.dhdr.symb.style))
                        return false;
                    }
                }
            }
        }
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void SetByBlockLevelAndSymbology (EditElementHandleR eeh, ConvertFromDgnContext& context)
    {
    IDwgConversionSettings& settings = context.GetSettings();
    ElementPropertiesSetter remapper;

    if (settings.CreateBlockDefinitionsOnLayer0())
        {
        bool        first = true;
        UInt32      level;

        if (TestLevelOrSymbologyMatch (&level, NULL, NULL, NULL, eeh.GetElementDescrCP (), &first, context))
            remapper.SetLevel (LEVEL_BYCELL);
        }

    if (settings.CreateBlockDefinitionsWithByBlockColor())
        {
        bool        first = true;
        UInt32      color;

        if (TestLevelOrSymbologyMatch (NULL, &color, NULL, NULL, eeh.GetElementDescrCP (), &first, context))
            remapper.SetColor (COLOR_BYCELL);
        }

    if (settings.CreateBlockDefinitionsWithByBlockStyle())
        {
        bool        first = true;
        Int32       style;

        if (TestLevelOrSymbologyMatch (NULL, NULL, &style, NULL, eeh.GetElementDescrCP (), &first, context))
            remapper.SetLinestyle (STYLE_BYCELL, NULL);
        }
    
    if (settings.CreateBlockDefinitionsWithByBlockWeight())
        {
        bool        first = true;
        UInt32      weight;

        if (TestLevelOrSymbologyMatch (NULL, NULL, NULL, &weight, eeh.GetElementDescrCP (), &first, context))
            remapper.SetWeight (WEIGHT_BYCELL);
        }

    remapper.Apply (eeh);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
TransformR                  ConvertFromDgnContext::ComputeInverseCellTransform
(
TransformR                  inverseCellTransform,
DPoint3dCR                  origin,
RotMatrixCR                 rMatrix
)
    {
    DVec3d              column0;
    this->GetTransformFromModelToDGN().GetMatrixColumn (column0, 0);
    double              scaleToDGN = column0.Magnitude();

    Transform           cellTransform;
    cellTransform.InitIdentity ();
    cellTransform.SetMatrix (rMatrix);
    cellTransform.SetTranslation (origin);

    cellTransform.InitProduct (this->GetTransformFromDGN(), cellTransform);
    cellTransform.ScaleMatrixRows (cellTransform, scaleToDGN, scaleToDGN, scaleToDGN);

    inverseCellTransform.InverseOf (cellTransform);
    return inverseCellTransform;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::BlockFromElements
(
AcDbObjectId&               blockId,
ElementHandleR              elementIn,
TransformCR                 inverseTransform
)
    {
    RealDwgStatus           status = MstnElementUnacceptable;
    if (!elementIn.IsValid())
        return  status;

    IDwgConversionSettings&         settings = this->GetSettings();
    if (settings.CreateBlockDefinitionsOnLayer0() || settings.CreateBlockDefinitionsWithByBlockColor() ||
        settings.CreateBlockDefinitionsWithByBlockWeight() || settings.CreateBlockDefinitionsWithByBlockStyle())
        {
        EditElementHandle   duplicateChildren (elementIn, true);

        SetByBlockLevelAndSymbology (duplicateChildren, *this);

        status = this->SaveBlockChildrenToDatabase (blockId, &inverseTransform, duplicateChildren);
        }
    else
        {
        status = this->SaveBlockChildrenToDatabase (blockId, &inverseTransform, elementIn);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::BlockFromTriformaElements
(
AcDbObjectId&               blockId,
ElementHandleR              elementIn,
DPoint3dCR                  origin,
RotMatrixCR                 rotMatrix
)
    {
    bool                    savedValue = m_disallowBlockNameFromTriForma;
    Transform               inverseCellTransform;

    this->ComputeInverseCellTransform (inverseCellTransform, origin, rotMatrix);
    m_disallowBlockNameFromTriForma = true;   // Avoid recursion.

    EditElementHandle       elementCopy (elementIn, true);
    MSElementP              element = elementCopy.GetElementP ();
    element->ehdr.uniqueId = 0;

    RealDwgStatus   status = this->BlockFromElements (blockId, elementCopy, inverseCellTransform);

    // if this is a top-level TF cell def, save its attached tags as attrdef's
    ElementRefP             elementRef = elementIn.GetElementRef ();
    if (nullptr != elementRef && nullptr == elementRef->GetParentElementRef())
        this->SaveTagsAsAttributeDefinitions (blockId, inverseCellTransform, elementIn);

    m_disallowBlockNameFromTriForma = savedValue;

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::BlockFromCellElement
(
AcDbObjectId&               blockId,
ElementHandleR              elemHandle
)
    {
    DPoint3d                origin;
    RotMatrix               rMatrix;
    Transform               inverseCellTransform;
    RealDwgStatus           status;

    // The cell transforms are defined as from the world to the cell.  For creating
    // block definitions and inserts we need the transform relative to the parent.
    if (RealDwgSuccess != (status = this->ExtractTransformFromCell (origin, rMatrix, elemHandle)))
        return status;

    this->ComputeInverseCellTransform (inverseCellTransform, origin, rMatrix);

    status = this->BlockFromElements (blockId, elemHandle, inverseCellTransform);

    this->SaveTagsAsAttributeDefinitions (blockId, inverseCellTransform, elemHandle);

    return RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       12/03
+---------------+---------------+---------------+---------------+---------------+-----*/
static bool                 ExtractAppDependencies
(
LinkageHeaderP              pHeader,
void*                       pVoid
)
    {
    AppDependencyParams  *pParams = (AppDependencyParams*) pVoid;
    int                  *pNDependencies = pParams->pNDependencies;
    DependencyLinkage    *pDependency = (DependencyLinkage *) (pHeader + 1);

    size_t               dependencySize = DependencyManagerLinkage::GetSizeofLinkage(*pDependency, 0);

    pParams->ppDependencies[*pNDependencies] = (DependencyLinkage *) malloc (dependencySize);

    memcpy (pParams->ppDependencies[(*pNDependencies)++], pDependency, dependencySize);

    return (*pNDependencies < MAX_APP_DEPENDENCIES) ? false : true;
    }

/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       12/03
+---------------+---------------+---------------+---------------+---------------+-----*/
StatusInt                   ConvertFromDgnContext::ExtractApplicationDependenciesFromElement
(
DependencyLinkage**         ppDependencies,
int*                        pNDependencies,
MSElementCP                 pElement
)
    {
    *pNDependencies = 0;

    if (this->GetIsSavingApplicationData())
        {
        AppDependencyParams         params;
        params.pNDependencies = pNDependencies;
        params.ppDependencies = ppDependencies;
        linkage_extractFromElement (NULL, pElement, LINKAGEID_Dependency, NULL, ExtractAppDependencies, (void*)&params);
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       05/02
+---------------+---------------+---------------+---------------+---------------+-----*/
StatusInt                   ConvertFromDgnContext::ExtractApplicationLinkageFromElement
(
UShort*                     pData,
int*                        pDataSize,
MSElementCP                 pElement
)
    {
    int             tmpDataSize = 0;
    UShort          tmpData[MAX_ELEMENT_SIZE];

    if (NULL == pData)
        pData = tmpData;

    if (NULL == pDataSize)
        pDataSize = &tmpDataSize;

    if (this->GetIsSavingApplicationData())
        {
#ifndef BUILD_DWGPLATFORM

        ElementHandle   eh(pElement, m_model);
        DwgConvertEvents::GetInstance().FireBeforeElementToObject(eh, *this);

        MSElement   *pElem = const_cast <MSElement*> (eh.GetElementCP());
        return mdlElement_extractRawLinkage (pData, pDataSize, pElem, sizeof(s_excludedLinkageIds) / sizeof(int), &s_excludedLinkageIds[0]);
#endif
        }
    // we used to save linkage of LINKAGEID_Material, but that is not right as we support material since 8.11.9.

    return BSIERROR;
    }

static double                       s_cellMatchDistanceTolerance  = 1.5;     // UORs.
static double                       s_cellMatchDirectionTolerance = 1.0E-8;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   04/02
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ElementsAreIdentical
(
MSElementDescrCP            edP1,
MSElementDescrCP            edP2,
double                      tolerance
)
    {
#ifndef BUILD_DWGPLATFORM
    for ( ;NULL != edP1 && NULL != edP2; edP1 = edP1->h.next, edP2 = edP2->h.next)
        {
        if (!mdlElmdscr_areIdenticalToTolerance (edP1, edP2,
                                                 COMPAREOPT_IGNORE_IDS | COMPAREOPT_IGNORE_DGNSTORE |
                                                 COMPAREOPT_IGNORE_LINKAGES | COMPAREOPT_IGNORE_MODIFIED |
                                                 COMPAREOPT_IGNORE_ATTRIBUTE_VALUE |
                                                 COMPAREOPT_IGNORE_2D_RANGEZ |
                                                 COMPAREOPT_IGNORE_CELL_RANGEDIAG |
                                                 COMPAREOPT_IGNORE_CELL_TRANSFORM |
                                                 COMPAREOPT_IGNORE_LINESTYLEDIRECTION |
                                                 COMPAREOPT_IGNORE_2D_ZRANGE,
                                                 tolerance, s_cellMatchDirectionTolerance))
            return false;
        }
#endif

    // Got to the end of both chains...they are the same
    return (NULL == edP1 && NULL == edP2);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/01
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  MatchingCellSearchFunction
(
void*                       avlNodeP,
void*                       userDataP,
void*                       avStatsP
)
    {
    MatchingCellNode*           pNode = static_cast<MatchingCellNode *>(avlNodeP);
    MatchingCellSearchParams* pParams = static_cast<MatchingCellSearchParams *>(userDataP);

    if (ElementsAreIdentical (pNode->pCellDescr, pParams->pSearchDescr, pNode->compareTolerance))
        {
        pParams->pFoundNode = pNode;
        return BSIERROR;       // Stop the search once we have found node.
        }
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsTextMirrored (MSElementP element, TransformP transform)
    {
    /*-----------------------------------------------------------------------------------
    When an enter-data-field is mirrored, yet it is nested in a cell, we want to keep the
    mirrored grandparent cell separated from the original cell such that the tags will
    look correct.  Cell rotation does not have this problem because the rotation angle is
    correctly compounded onto the text element, thus our cell transformation can correctly
    apply to the text element.  Mirroring, on the other hand, changes the compounding 
    effect and makes the text appear unrotated in the view.  If we remove text elements 
    from the cell, we'd lose this transformation difference on text element, resulting
    both mirrored and original cells indistinguishable, like the case in TR 291326.
    See transform_getTextScale on how a mirrored text element is determined when a
    transformation is applied.
    -----------------------------------------------------------------------------------*/
    if (!element->hdr.dhdr.props.b.is3d)
        {
        DVec3d  xAxis, yAxis;
        transform->getMatrixColumn (&xAxis, 0);
        transform->getMatrixColumn (&yAxis, 1);
        if (bsiDVec3d_crossProductXY(&xAxis, &yAxis) < 0.0)
            return  xAxis.x < 0.0 || yAxis.y < 0.0;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RemoveEnterDataFieldsConvertedToAttributes
(
MSElementDescrP             pDescr,
TransformP                  transform,
ConvertFromDgnContext&      context
)
    {
    switch (pDescr->el.ehdr.type)
        {
        case TEXT_ELM:
            {
            ElementHandle elemHandle (pDescr, false, false);
            if (context.IsEnterDataField (&pDescr->el) && !IsTextMirrored(&pDescr->el, transform))
                {
                TextBlock   textBlock (elemHandle);
                if (textBlock.IsDTextCompatible())
                    pDescr->RemoveElement ();
                }
            break;
            }

        case CELL_HEADER_ELM:
        case TEXT_NODE_ELM:
            {
            MSElementDescr  *pChild, *pNext;
            for (pChild = pDescr->h.firstElem; NULL != pChild; pChild = pNext)
                {
                pNext = pChild->h.next;
                RemoveEnterDataFieldsConvertedToAttributes (pChild, transform, context);
                }
            break;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     UnsetPropertiesForComparison (MSElementDescrP elmdscrChain)
    {
    for (MSElementDescrP elmdscr = elmdscrChain; NULL != elmdscr; elmdscr = elmdscr->h.next)
        {
        // Unset those properties that should have no impact on element comparisons.  Keep is3d which is used to unset geometrical props.
        elmdscr->el.ehdr.locked = false;
        // safeguard from accessing non-graphical elements - TFS 566380
        if (elmdscr->el.ehdr.isGraphics)
            {
            elmdscr->el.hdr.dhdr.grphgrp = 0;
            elmdscr->el.hdr.dhdr.props.b.reserved = 0;
            elmdscr->el.hdr.dhdr.props.b.unused2 = 0;
            elmdscr->el.hdr.dhdr.props.b.unused3 = 0;
            elmdscr->el.hdr.dhdr.props.b.dynamicRange = 0;
            elmdscr->el.hdr.dhdr.props.b.n = 0;
            elmdscr->el.hdr.dhdr.props.b.m = 0;
            elmdscr->el.hdr.dhdr.props.b.r = 0;
            elmdscr->el.hdr.dhdr.props.b.p = 0;
            elmdscr->el.hdr.dhdr.props.b.s = 0;
            elmdscr->el.hdr.dhdr.props.b.h = 0;
            }
        if (elmdscr->h.isHeader)
            UnsetPropertiesForComparison (elmdscr->h.firstElem);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/01
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::FindMatchingCellBlock
(
AcDbObjectId*               pBlockId,
ElementHandleR              elemHandle,
WCharCP                     nameChars
)
    {
    if (!this->GetSettings().CreateSingleBlockFromDuplicateCells ())
        return false;

    if (this->GetIsSavingApplicationData() && SUCCESS == this->ExtractApplicationLinkageFromElement (NULL, NULL, elemHandle.GetElementCP()))
        return false;

    DPoint3d                origin;
    RotMatrix               rMatrix;
    Transform               transform, inverseTransform;

    this->ExtractTransformFromCell (origin, rMatrix, elemHandle);
    transform.initFrom (&rMatrix, &origin);
    inverseTransform.inverseOf (&transform);

    EditElementHandle       untransformedEeh(elemHandle, true);
    if (elemHandle.IsValid() && untransformedEeh.IsValid())
        {
        TransformInfo       transInfo (inverseTransform);
        untransformedEeh.GetHandler(MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (untransformedEeh, transInfo);

        MSElementDescrP     pUntransformedDescr = untransformedEeh.ExtractElementDescr ();
        if (NULL == pUntransformedDescr)
            return  false;

        // Remove any text elements that would convert to Enter-DataFields.
        // Note - changed to removing the attributes before comparision rather than
        // just turning on the flag to ignore single enter-datafields so that we
        // also check for dTextable entities (TR# 135672).
        RemoveEnterDataFieldsConvertedToAttributes (pUntransformedDescr, &inverseTransform, *this);

        ElementAgenda       tags;
        if (TagElementHandler::GetTagsFromSourceElement(&tags, elemHandle, false, true) > 0)
            {
            // untransform the tags
            transInfo = TransformInfo (inverseTransform);

            for each (ElemAgendaEntry tag in tags)
                {
                AssociativePoint::RemoveAllAssociations (tag);
                tag.GetHandler (MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (tag, transInfo);
                pUntransformedDescr->AddToChain (tag.ExtractElementDescr());
                }
            }

        UnsetPropertiesForComparison (pUntransformedDescr);

        MatchingCellSearchParams      searchParams;
        searchParams.pFoundNode       = NULL;
        searchParams.pSearchDescr     = pUntransformedDescr;

        mdlAvlTree_searchMulti (m_pMatchingCellTree, (void*) nameChars, MatchingCellSearchFunction, &searchParams);
        if (NULL == searchParams.pFoundNode)
            {
            // Create new node and add it.
            MatchingCellNode        newNode;
            double                  transformScale;
            AcString                blockName = AcString (nameChars);

            newNode.pCellDescr = pUntransformedDescr;
            *pBlockId = newNode.blockId = this->CreateAndAddBlockHeader (blockName, elemHandle);
            wcscpy (newNode.cellName, nameChars);

            bsiTransform_isUniformScale (&inverseTransform, NULL, &transformScale);
            newNode.compareTolerance = transformScale * s_cellMatchDistanceTolerance;
            mdlAvlTree_insertNode (m_pMatchingCellTree, &newNode, sizeof(newNode));
            return false;
            }
        else
            {
            *pBlockId = searchParams.pFoundNode->blockId;
            pUntransformedDescr->Release ();
            return true;
            }
        }

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/06
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsAngleModeChanged
(
bool                        dwgAngleDir,
UInt16                      dgnAngleMode
)
    {
    /*-----------------------------------------------------------------------------------
    When saving changes to DWG, check original ANGDIR's value from which DGN's angle mode
    was determined to be either Convetional or Azimuth.  We now do a reverse check to see
    if user has explicitly made a change of the angle mode.  If he did, he should expect
    his change to be saved as opposed to bing ignored.
    -----------------------------------------------------------------------------------*/
    return  (ANGLE_MODE_Azimuth == dgnAngleMode && !dwgAngleDir) ||
            (ANGLE_MODE_Standard == dgnAngleMode && dwgAngleDir);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void     ExtractDwgAngularUnitInfoFromModel (DwgAUnits& aunits, bool& angdir, double& angbase, int& auprec, ModelInfoCR modelInfo)
    {
    switch (modelInfo.GetAngularMode())
        {
        default:
        case AngleMode::Degrees:
            aunits = DwgAUnits::DecimalDegrees;
            break;

        case AngleMode::DegMin:
        case AngleMode::DegMinSec:
            aunits = DwgAUnits::DegreesMinutesSeconds;
            // DWG DDMMSS format can display DDdMM' when auprec = 2 or 3
            if (0 == auprec && AngleMode::DegMin == modelInfo.GetAngularMode())
                auprec = 2;
            else
                auprec += 4;
            break;

        case AngleMode::Centesimal:
            aunits = DwgAUnits::Gradians;
            break;

        case AngleMode::Radians:
            aunits = DwgAUnits::Radians;
            break;
        }

    if (DirectionMode::Bearing != modelInfo.GetDirectionMode ())
        {
        angbase = modelInfo.GetDirectionBaseDir() * msGeomConst_radiansPerDegree;
        angdir  = modelInfo.GetDirectionClockwise ();
        }
    else
        {
        angbase = 0.0;
        angdir = false;
        }

    // although ACAD R2011 Audit does no longer complain AUPREC>8, it still does not display decimal places after the 8th, so this check is still valid:
    auprec = static_cast <int> (modelInfo.GetAngularPrecision());
    if (auprec > 8)           // TR# 102558
        auprec = 8;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::ExtractDatabaseUnitsFromDgn
(
const Tcb*                  pTcb,
bool                        changesOnly
)
    {
    if (!m_model->IsDictionaryModel())
        return NotApplicable;

    ModelInfoCR         modelInfo = m_model->GetModelInfo();
    AcDbDatabase*       pDatabase = m_pFileHolder->GetDatabase();
    // LUNITS
    DgnUnitFormat       unitFormat      = modelInfo.GetLinearUnitMode();
    PrecisionFormat     precisionEnum   = modelInfo.GetLinearPrecision();
    byte                linearPrecision = DoubleFormatter::GetByteFromPrecision (precisionEnum);
    PrecisionType       precisionType   = DoubleFormatter::GetTypeFromPrecision (precisionEnum);

    bool                useArchitecturalOrEngineering;

    if (DWGSaveUnitMode_ArchOrEngineering == this->GetTargetUnitMode())
        {
        useArchitecturalOrEngineering = true;
        }
    else
        {
        useArchitecturalOrEngineering = (StandardUnit::EnglishInches == m_targetUnit.IsStandardUnit() &&
                                         StandardUnit::EnglishInches == modelInfo.m_subUnit.IsStandardUnit() &&
                                         StandardUnit::EnglishFeet   == modelInfo.m_masterUnit.IsStandardUnit() &&
                                         DgnUnitFormat::MUSU == unitFormat);
        }

    if (PrecisionType::Fractional == precisionType)
        {                               // Fractional.
        if (useArchitecturalOrEngineering)
            pDatabase->setLunits (DWGLinearUnit_Architectural);
        else
            pDatabase->setLunits (DWGLinearUnit_Fractional);

        // convert the bit coded value to a sequential number of digits:
        byte    numDigits = 0;
        while (linearPrecision > 0)
            {
            linearPrecision = linearPrecision >> 1;
            numDigits++;
            }
        linearPrecision = numDigits;
        }
    else if (PrecisionType::Scientific == precisionType)
        {                                  // Scientific
        pDatabase->setLunits (DWGLinearUnit_Scientific);
        }
    else
        {                                  // Decimal.
        if (useArchitecturalOrEngineering)
            pDatabase->setLunits (DWGLinearUnit_Engineering);
        else
            pDatabase->setLunits (DWGLinearUnit_Decimal);
        }

    // INSUNITS
    if (changesOnly)
        {
        // This is the case where a DWG file has been editted.
        // We want to be careful about setting INSUNITs and only change it in response to user making change.

        if (0 != pTcb->designCenterUnits &&
            pTcb->designCenterUnits != pDatabase->insunits ())   // The user explicitly set the INSUNITs value.
            {
            pDatabase->setInsunits ((AcDb::UnitsValue) pTcb->designCenterUnits);
            }
        else if (m_dgnFile->AreDesignCenterUnitsOk()) // The DesignCenter units were in synch when the file was opened.
            {
            if (useArchitecturalOrEngineering)
                pDatabase->setInsunits (AcDb::kUnitsInches);
            else
                pDatabase->setInsunits (RealDwgUtil::AcDbUnitsValueFromDgnUnits (modelInfo.m_masterUnit.IsStandardUnit()));
            }
        }
    else
        {
        pDatabase->setInsunits (RealDwgUtil::AcDbUnitsValueFromDgnUnits (m_standardTargetUnits));
        }

    pDatabase->setLuprec (linearPrecision > MAX_ReadoutPrecision ? MAX_ReadoutPrecision : linearPrecision);

    // angular units:
    DwgAUnits       angularUnits = DwgAUnits::DecimalDegrees;
    int             angularPrecision = 0;
    bool            angleClockwise = false;
    double          angleBase = 0.0;
    ExtractDwgAngularUnitInfoFromModel (angularUnits, angleClockwise, angleBase, angularPrecision, GetModel()->GetModelInfo());

    pDatabase->setAunits (static_cast<Adesk::Int16>(angularUnits));

    if (angularPrecision >= 0)
        pDatabase->setAuprec (angularPrecision > MAX_ReadoutPrecision ? MAX_ReadoutPrecision : angularPrecision);

    pDatabase->setAngdir (true==angleClockwise);
    pDatabase->setAngbase (angleBase);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::ExtractDatabaseVariablesFromDgnHeader (bool changesOnly)
    {
    try
        {
        AcDbDatabase*       pDatabase = m_pFileHolder->GetDatabase();
        ElementRefP         headerElemref = m_model->FindByFilePos (0, true);
        MSElementDescrP     headerElmdscr = NULL;

        if (NULL == headerElemref || BSISUCCESS != headerElemref->GetElementDescr(headerElmdscr, m_model, false) || NULL == headerElmdscr)
            {
            DIAGNOSTIC_PRINTF ("Error reading DGN file header!\n");
            return BSIERROR;
            }

        if (DGNFIL_HEADER_ELM != headerElmdscr->el.ehdr.type)
            {
            DIAGNOSTIC_PRINTF ("Wrong DGN file header!\n");
            headerElmdscr->Release ();
            return BSIERROR;
            }

        Tcb*                pTcb = (Tcb *) ((Dgn_header *) &headerElmdscr->el)->tcbinfo;
        LevelHandle         activeLevel = m_dgnFile->GetLevelCacheR().GetLevel (pTcb->activeLevel);
        AcDbObjectId        activeLevelObjectId = m_pFileHolder->GetLayerByLevelHandle (activeLevel);
        if (activeLevelObjectId.isValid())
            pDatabase->setClayer (activeLevelObjectId);

        if (pDatabase->clayer().isEffectivelyErased())
            {
            DIAGNOSTIC_PRINTF ("Active Layer Erased - Resetting to layer 0\n");
            pDatabase->setClayer (pDatabase->layerZero());
            }

        m_activeDgnSymbology = pTcb->symbology;

        // So we store the RGB seperately on file-design and bring it back masterModelRef.
        if (0 != pTcb->activeColorRGB)
            {
            IntColorDef         colorDef(pTcb->activeColorRGB);
            DgnColorMapP        colorMap = DgnColorMap::GetForFile (m_dgnFile);
            if (NULL != colorMap && colorMap->FindClosestMatch(colorDef) == m_activeDgnSymbology.color)
                {
                UInt32          elementColor = DgnColorMap::CreateElementColor (colorDef, NULL, NULL, *m_dgnFile);
                if (INVALID_COLOR != elementColor)
                    m_activeDgnSymbology.color |= (ColorUtil::GetExtendedIndexFromRawColor(elementColor) << 8);
                }
            }

        pDatabase->setCecolor (this->GetColorFromDgn (m_activeDgnSymbology.color, pDatabase->cecolor().colorIndex()));
        pDatabase->setCelweight (this->GetLineWeightFromDgn (m_activeDgnSymbology.weight, pDatabase->celweight()));
        pDatabase->setCeltype   (this->GetLineTypeFromDgn (m_activeDgnSymbology.style));
        pDatabase->setCeltscale ((0 == (pTcb->lineStyle.modifiers & STYLEMOD_SCALE) || 0.0 == pTcb->lineStyle.scale) ? 1.0 : pTcb->lineStyle.scale);

        if (this->GetSettings().SetAxisLockFromOrthoMode())
            pDatabase->setOrthomode (pTcb->ext_locks.axis_lock);

        DgnTextStylePtr     activeTextStyle = DgnTextStyle::GetSettings (*m_dgnFile);

        double              activeTextHeight = 0;
        if (activeTextStyle.IsValid() && BSISUCCESS == activeTextStyle->GetProperty(TextStyle_Height, activeTextHeight) && 0.0 != activeTextHeight)
            pDatabase->setTextsize (activeTextHeight * this->GetScaleFromDGN());
        else
            pDatabase->setTextsize (this->GetScaleFromDGN());

        AcDbObjectId        activeTextStyleId;
        if (!(activeTextStyleId = m_pFileHolder->GetTextStyleIndex()->GetObjectId (activeTextStyle->GetID(), pDatabase)).isNull() ||
            !(activeTextStyleId = acdbSymUtil()->textStyleStandardId(pDatabase)).isNull() )
            {
            pDatabase->setTextstyle (activeTextStyleId);

            if (pDatabase->dimtxsty().isNull() || pDatabase->dimtxsty().isErased())
                pDatabase->setDimtxsty (activeTextStyleId);

            /*---------------------------------------------------------------------------
            AutoCAD always replaces header variable TEXTSIZE with the last used text
            height in the active text style.  The only time the header variable is ever
            used is in R14 when the last used text height is NOT present in a DXF file.
            ---------------------------------------------------------------------------*/
            AcDbTextStyleTableRecordPointer pActiveTextStyle (activeTextStyleId, AcDb::kForWrite);
            if (Acad::eOk == pActiveTextStyle.openStatus())
                {
                if (0.0 != activeTextHeight)
                    pActiveTextStyle->setPriorSize (activeTextHeight * this->GetScaleFromDGN());
                else
                    pActiveTextStyle->setPriorSize (this->GetScaleFromDGN());
                }
            }

        pDatabase->setIsolines  ((Int16) pTcb->smartGeomSettings.nIsoparametrics);
        pDatabase->setSplframe  (pTcb->invisGeomDisplayMode == INVISGEOM_ALWAYS);
        pDatabase->setPlinegen (pTcb->ext_locks2.continousLineStringStyles);
        pDatabase->setPlinewid  (0 == (pTcb->lineStyle.modifiers & STYLEMOD_SWIDTH)  ? 0.0 : (pTcb->lineStyle.startWidth * this->GetScaleFromDGN()));
        pDatabase->setLtscale (m_model->GetModelInfo().m_linestyleScale);
        pDatabase->setPsltscale (pTcb->ext_locks.scaleViewportLineStyles);
        pDatabase->setDimAssoc (pTcb->ext_locks.association ? 2 : 1);
        pDatabase->setVisretain (!pTcb->ext_locks.ignoreReferenceLevelOverrides);

        // set active layout for the active file, i.e. excluding output files separated from models:
        RealDwgModelIndexItem*          pActiveModelIndexItem;
        if (NULL != (pActiveModelIndexItem = m_pFileHolder->GetModelItemByModelId (pTcb->activeModel)) &&
            pActiveModelIndexItem->GetRealDwgModelType() != RDWGMODEL_TYPE_NonDefaultModel)
            {
            AcDbBlockTableRecordPointer     pBlockTableRecord (pActiveModelIndexItem->GetBlockTableRecordId(), AcDb::kForRead);
            if (Acad::eOk == pBlockTableRecord.openStatus())
                {
                AcDbObjectId            layoutId = pBlockTableRecord->getLayoutId ();
                pBlockTableRecord.close ();
                RealDwgHostApp::Instance().layoutManager()->setCurrentLayoutId (layoutId);
                }
            }

        this->ExtractDatabaseUnitsFromDgn (pTcb, changesOnly);

        headerElmdscr->Release ();

        // Fix for TR# 118929
        // If a textstyle "STANDARD" exists in the DGN file then the seed file's STANDARD entry
        // will be removed in FileHolder::removeRedundantSeedEntries.  IF a STANDARD
        // dimension style points to this text style then it needs to be fixed here.
        // Can't be done beforehand as the text style does not exist until after import.
        // TODO - FIgure out a better way of doing this whole redundant seed entry crap.
        AcDbObjectId    dimStyleStandardId;
        if (Acad::eOk == AcDbSymbolUtilities::getDimStyleId (dimStyleStandardId, DIMSTYLE_StandardName, pDatabase))
            {
            AcDbDimStyleTableRecordPointer      pStandardDimStyle (dimStyleStandardId, AcDb::kForWrite);
            if (Acad::eOk == pStandardDimStyle.openStatus())
                {
                if (pStandardDimStyle->dimtxsty().isNull() ||
                    pStandardDimStyle->dimtxsty().isErased())
                    {
                    pStandardDimStyle->setDimtxsty (acdbSymUtil()->textStyleStandardId(pDatabase));
                    }
                }
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught setting Database from DGN Header\n");
        }


    return SUCCESS;
    }

/*--------------------------------------------------------------------------------------
* @bsimethod                                                RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetCurrentLayoutId ()
    {
    if (m_currentBlockId.isNull())
        {
        AcDbObjectId    nullId;
        return nullId;
        }

    AcDbBlockTableRecordPointer pCurrentBlock (m_currentBlockId, AcDb::kForRead);
    return pCurrentBlock->getLayoutId();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                Barry.Bentley   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsCurrentBlockLayout ()
    {
    AcDbBlockTableRecordPointer     pCurrentBlock (this->GetCurrentBlockId(), AcDb::kForRead);
    if (Acad::eOk != pCurrentBlock.openStatus())
        return false;

    return pCurrentBlock->isLayout();
    }

/*--------------------------------------------------------------------------------------
* @bsimethod                                                    BrienBastings   02/04
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::GetTagSetNameAndDefinition
(
WStringP                    tagSetNameP,
DgnTagDefinitionP           tagDefP,
ElementHandleCR             tagElm
)
    {
    StatusInt   status = BSIERROR;
    WChar       tagsetChars[1024] = { 0 };

    if (NULL != tagDefP)
        memset (tagDefP, 0, sizeof (*tagDefP));

    if (NULL != tagSetNameP)
        tagSetNameP->clear ();

    ElementId           tagsetId = TagElementHandler::GetSetDefinitionID (tagElm);
    EditElementHandle   tagsetElm;
    if (BSISUCCESS == (status = TagSetHandler::GetByID(tagsetElm, tagsetId, *m_dgnFile)) &&
        BSISUCCESS == (status = TagSetHandler::GetSetName(tagsetChars, _countof(tagsetChars), tagsetElm)))
        {
        T_TagDefList            tagdefList;
        WString                 tagsetName (tagsetChars);
        T_TagSetList::iterator  foundAt = m_tagSetList.find (tagsetName);
        if (foundAt == m_tagSetList.end())
            {
            // the tag set is not found in our list, extract tags from the element and add them to the list:
            if (BSISUCCESS == TagSetHandler::ExtractTagDefs(tagsetElm, tagdefList))
                {
                T_TagSetEntry   newEntry (tagsetName, tagdefList);
                m_tagSetList.insert (newEntry);
                }
            }
        else
            {
            // the tag set is found in the previously processed list, retrieve tags from the list:
            tagdefList = foundAt->second;
            }

        if (!tagdefList.empty())
            {
            AttributeElm const* attrElem = (AttributeElm const*) tagElm.GetElementCP ();
            for each (DgnTagDefinition const& tagDef in tagdefList)
                {
                if (tagDef.id == attrElem->attrDefID)
                    {
                    if (NULL != tagSetNameP)
                        tagSetNameP->assign (tagsetName);

                    if (NULL != tagDefP)
                        *tagDefP = tagDef;

                    status = SUCCESS;
                    break;
                    }
                }
            }
        }

    return status;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::CreatePolylinesFromSplines ()
    {
    return this->GetSettings().CreatePolylinesFromSplines();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsDwgForegroundColor255 (UInt32 dgnColor)
    {
    WString   bookName, entryName;

    // checl color book "DWG Special Colors" for "DWG Color 255".
    return  SUCCESS == DgnColorMap::ExtractElementColorInfo(NULL, NULL, NULL, &bookName, &entryName, dgnColor, *m_dgnFile) &&
            bookName.Equals (m_specialDwgColorBookName) &&
            entryName.Equals (m_dwgForegroundColor255Name);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetDwgTextStyleId (Int32 dgnTextStyle, AcDbDatabase* database)
    {
    return this->GetFileHolder().GetTextStyleIndex()->GetObjectId (dgnTextStyle, database);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectP                 ConvertFromDgnContext::InstantiateOrUseExistingObject (AcDbObjectP existingObject, AcRxClass* typeNeeded)
    {
    // either instantiate a new object, use the existing one, or morph the existing one to the new type..
    AcDbObjectP     acObject;

    if (existingObject == NULL)
        acObject = AcDbObject::cast (typeNeeded->create());
    else if (existingObject->isKindOf(typeNeeded))
        acObject = existingObject;
    else
        {
        acObject = AcDbObject::cast (typeNeeded->create());
        existingObject->handOverTo (acObject, true, false);
        }

    AcDbEntityP     newEntity;
    if (NULL != acObject && acObject->isNewObject() && NULL != (newEntity = AcDbEntity::cast(acObject)))
        newEntity->setDatabaseDefaults (this->GetDatabase());

    return acObject;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsSameAnnotationScaleName (ACHAR const* acName, WStringCR dgnName)
    {
    // remove spaces from DWG names
    WString     nameNoSpace;
    while (NULL != acName && 0 != *acName)
        {
        if (L' ' != *acName)
            nameNoSpace.append (1, *acName);
        acName++;
        }

    return  dgnName.EqualsI(nameNoSpace);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsSameAnnotationScaleName (AcDbAnnotationScale* acAnnoScale, WStringCR dgnScaleName)
    {
    if (NULL == acAnnoScale)
        return  false;

    AcString    acName;
    return  Acad::eOk == acAnnoScale->getName(acName) && IsSameAnnotationScaleName(acName.kwszPtr(), dgnScaleName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsSameAnnotationScaleFactor (AcDbAnnotationScale* acAnnoScale, double dgnScaleFactor)
    {
    if (NULL == acAnnoScale)
        return  false;

    double      acScale = 1.0;
    return  Acad::eOk == acAnnoScale->getScale(acScale) && fabs(acScale - dgnScaleFactor) < TOLERANCE_AnnotationScale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::GetOrAddAnnotationScale (AcDbAnnotationScale*& acAnnoscale, ScaleDefinitionCP dgnScaledef, bool matchScaleOnly, AcDbDatabase* dwg)
    {
    if (NULL != acAnnoscale || NULL == dgnScaledef)
        return  NotApplicable;

    AcDbDatabase*           database = nullptr == dwg ? this->GetDatabase() : dwg;
    if (nullptr == database)
        return  NullObject;

    double                  dwgScaleFactor = dgnScaledef->GetPostScale() / dgnScaledef->GetPreScale();
    WCharCP                 scaleName = dgnScaledef->GetName ();
    if (NULL == scaleName || 0 == scaleName[0] || dwgScaleFactor < TOLERANCE_AnnotationScale)
        return  InvalidName;

    // does the annotation scale already exist in the database?
    AcDbObjectContextCollection*    contextCollection = NULL;
    AcDbObjectContextManager*       contextManager = database->objectContextManager ();
    if (NULL == contextManager || NULL == (contextCollection = contextManager->contextCollection(ACDB_ANNOTATIONSCALES_COLLECTION)))
        return  NullObject;

    // remove space chars in scale name prior to be compared with ACAD's scale names
    WString                 dgnScaleName;
    while (NULL != scaleName && 0 != *scaleName)
        {
        if (L' ' != *scaleName)
            dgnScaleName.append (1, *scaleName);
        scaleName++;
        }

    AcDbObjectContextCollectionIterator*    iter = contextCollection->newIterator ();
    for (iter->start(); !iter->done(); iter->next())
        {
        AcDbObjectContext*  existingContext = NULL;
        if (Acad::eOk == iter->getContext(existingContext))
            {
            AcDbAnnotationScale*    existingAnnoscale = AcDbAnnotationScale::cast (existingContext);

            /*---------------------------------------------------------------------------------------------------
            Ideally we'd like to find scale by name only, but currently DGN ModelInfo only allows unique scales.
            For instance, scale factor of 0.5 is forced as 6"=1'-0".  We cannot round trip a scale like 1:2 back
            to DWG if we have to match both scale factor and name.  To round trip scale 1:2 back to DWG, we only 
            want to match scale factor and ignore the name.  We need to keep this workaround, i.e to allow 
            matching scale only, until MicroStation will support multiple names shared by an annotation scale factor.
            ---------------------------------------------------------------------------------------------------*/
            if (IsSameAnnotationScaleFactor(existingAnnoscale, dwgScaleFactor) && (matchScaleOnly || IsSameAnnotationScaleName(existingAnnoscale, dgnScaleName)))
                {
                // found a matching scale in the collection - copy it to output:
                acAnnoscale = existingAnnoscale;
                return  RealDwgSuccess;
                }

            if (NULL != existingContext)
                delete existingContext;
            }
        }

    // no matching scale found in the collection, try adding a new scale to the file
    Acad::ErrorStatus       es;
    AcString                originalName (dgnScaledef->GetName());
    AcDbAnnotationScale     newAnnoscale;
    if (Acad::eOk != (es = newAnnoscale.setDrawingUnits(dgnScaledef->GetPreScale())) ||
        Acad::eOk != (es = newAnnoscale.setPaperUnits(dgnScaledef->GetPostScale())) ||
        Acad::eOk != (es = newAnnoscale.setName(originalName.kwszPtr())))
        return  ScaleError;

    // add the scale to the collection
    if (Acad::eOk == (es = contextCollection->addContext(&newAnnoscale)))
        {
        // get the newly saved scale back and hope it still is the same guy we have just added
        AcDbObjectContext*  residentContext = contextCollection->getContext (originalName);
        acAnnoscale = AcDbAnnotationScale::cast (residentContext);
        if (NULL != acAnnoscale)
            return  RealDwgSuccess;
        // clean up the mess before return
        if (NULL != residentContext)
            delete residentContext;
        }

    return  CantSetAnnotationScale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::AddAnnotationScaleToObject (AcDbObjectP acObject, double inScale, ElementId uniqueId)
    {
    // currently only add annotation scale for modelspace entities
    if (!this->CanSaveAnnotationScale())
        return  false;

    // get DGN annotation scale definition
    ScaleDefinitionCP           scaleDef = m_annotationScaleCollection.FindByFactor (inScale);
    if (NULL == scaleDef)
        return  false;

    AcDbAnnotativeObjectPE*     annotationPE = ACRX_PE_PTR (acObject, AcDbAnnotativeObjectPE);
    AcDbObjectContextInterface* objectContextInterface = ACRX_PE_PTR (acObject, AcDbObjectContextInterface);
    if (NULL == annotationPE || NULL == objectContextInterface)
        return  false;

    // an AcDbBlockReference must have an annotative AcDbBlockTableRecord to be able to add a scale
    if (!objectContextInterface->supportsCollection(acObject, ACDB_ANNOTATIONSCALES_COLLECTION))
        return  false;

    /*-------------------------------------------------------------------------------------------------------
    Workaround a problem with adding an annotative object in database: RealDWG automatically adds an extended
    dictionary for the supported annotation scale.  This new dictionary takes the entity handle value which
    becomes immediately available next to the host object's handle value, effectively escapes from our handseed
    management and causes follow up entities not added to database.  The helper class AcDbDisableAnnoAutoScale
    is supposed to turn ANNOAUTOSCALE off but does not appear to work as it claims.  Before we figure a better
    way around this problem, we have to turn off annotative, save the object to database, then turn annotative
    back on.
    This workaround is only needed for the input object which is to be saved to database.  For a temporary 
    object, such as the mtext used for multileader, it will be deleted hence should not be saved here.
    -------------------------------------------------------------------------------------------------------*/
    if (0 != uniqueId && !acObject->objectId().isValid())
        {
        if (annotationPE->annotative(acObject))
            annotationPE->setAnnotative (acObject, false);

        if ((this->AddEntityToCurrentBlock(AcDbEntity::cast(acObject), uniqueId)).isNull())
            return  false;
        }

    // set the object annotative, except for AcDbBlockReference which has been checked above.
    Acad::ErrorStatus           es = Acad::eOk;
    if (!acObject->isKindOf(AcDbBlockReference::desc()))
        es = annotationPE->setAnnotative (acObject, true);
    if (Acad::eOk != es)
        return  false;

    if (NULL != m_modelScaleDefinition && *m_modelScaleDefinition == *scaleDef)
        {
        // the input scale is the same as model scale and CANNOSCALE has been updated from the model scale, use it:
        AcDbAnnotationScale*    cannoscale = this->GetFileHolder().GetDatabase()->cannoscale ();
        if (NULL != cannoscale && !objectContextInterface->hasContext(acObject, *cannoscale))
            es = objectContextInterface->addContext (acObject, *cannoscale);

        if (NULL != cannoscale)
            delete cannoscale;

        return  Acad::eOk == es;
        }

    // try adding the annotation scale to the object if not already supported
    AcDbAnnotationScale*        annotationScale = NULL;
    if (RealDwgSuccess == this->GetOrAddAnnotationScale(annotationScale, scaleDef) && !objectContextInterface->hasContext(acObject, *annotationScale))
        es = objectContextInterface->addContext (acObject, *annotationScale);

    if (NULL != annotationScale)
        delete annotationScale;

    return  Acad::eOk == es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::CanSaveAnnotationScale ()
    {
    /*-----------------------------------------------------------------------------------------------
    Currently only modelspace annotation scales can be saved.  If the default model is a sheet model,
    check to see if it will be saved as a layout model in which annoation scale is not supported.
    -----------------------------------------------------------------------------------------------*/
    ModelId     currentModelId = this->GetModel()->GetModelId ();
    if (this->GetFile()->GetDefaultModelId() == currentModelId)
        {
        RealDwgModelIndexItemP  modelItem = this->GetFileHolder().GetModelItemByModelId (currentModelId);
        if (NULL != modelItem && RDWGMODEL_TYPE_Sheet != modelItem->GetRealDwgModelType())
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertFromDgnContext::GetUnitScaleFromCurrentModelToTarget ()
    {
    /*----------------------------------------------------------------------------------------------------
    DWG file units are based on the default model and once saved to DWG they apply to all models.  If we 
    are processing a non-default model and need to find file unit size in a table/dictionary section, such 
    as block definition and text style height etc, we need the scale from current model to the default model.
    ----------------------------------------------------------------------------------------------------*/
    DgnModelP   currentModel = this->GetModel ();
    double      modelScaleToTarget = 1.0;
    if (NULL == currentModel || currentModel->IsDefault() || currentModel->IsDictionaryModel())
        return  modelScaleToTarget;

     DgnModelP  defaultModel = this->GetFile()->FindLoadedModelById (this->GetFile()->GetDefaultModelId());
     if (NULL == defaultModel)
        return  modelScaleToTarget;

    DwgOpenUnitMode openUnitMode = this->GetDwgOpenUnitModeFromSaveUnitMode (this->GetTargetUnitMode());
    double          defaultScaleToDGN = 0.0, modelScaleToDGN = 0.0;

    this->GetTransformToDGNFromModelInfo (NULL, &defaultScaleToDGN, NULL, defaultModel->GetModelInfo(), defaultModel, openUnitMode);
    this->GetTransformToDGNFromModelInfo (NULL, &modelScaleToDGN,   NULL, currentModel->GetModelInfo(), currentModel, openUnitMode);

    modelScaleToTarget = 0.0 != modelScaleToDGN ? defaultScaleToDGN / modelScaleToDGN : 1.0;

    return  modelScaleToTarget;
    }

ConvertFromDgnMultiProcessingContext::ConvertFromDgnMultiProcessingContext
(
DgnModelP               modelRef,
ModelId                 modelId,
FileHolder*             pFileHolder,
DgnFileFormatType       format,
DwgFileVersion          version,
DwgSaveUnitMode         saveUnitMode,
bool                    omitReferencePath,
bool                    threeD,
UInt32                  elementCount,
IDwgConversionSettings& settings
)
    :
    ConvertFromDgnContext (modelRef, modelId, pFileHolder, format, version, saveUnitMode, omitReferencePath, threeD, elementCount, settings),
    m_psdFileProcessingMode (PsdFileProcessingMode::WriteToFile),
    m_psdToAcisChoice (&m_psdToAcis)
    {
    }

ConvertFromDgnMultiProcessingContext::~ConvertFromDgnMultiProcessingContext()
    {
    }

void            ConvertFromDgnContext::_OnPreSaveElementIntoCache (ElementHandleR elemHandle)
    {
    //empty on purpose
    }

void            ConvertFromDgnMultiProcessingContext::_OnPreSaveElementIntoCache (ElementHandleR elemHandle)
    {
    m_curElement = elemHandle;
    }

StatusInt       ConvertFromDgnContext::_OnPostSavePsdEntitiesToDatabase (DgnModelP pCache, bool graphicElements, int checkInMask, UInt32& runningCount)
    {
    return SUCCESS;
    }

StatusInt ConvertFromDgnMultiProcessingContext::_OnPostSavePsdEntitiesToDatabase
(
DgnModelP                   pCache,
bool                        graphicElements,
int                         checkInMask,
UInt32&                     runningCount
)
    {
    StatusInt result = SUCCESS;
    if (CurrentPsdToAcis ().WaitForFinish ())
        {
        m_psdFileProcessingMode = PsdFileProcessingMode::ReadFromFile;

        for (BeFileName const& satfileName : CurrentPsdToAcis ().SatFileNames ())
            {
            auto found = m_psdEntityMapping.find (BeFileName (BeFileName::GetFileNameWithoutExtension (satfileName).c_str ()));
            if (found == m_psdEntityMapping.end ())
                {
#ifdef REALDWG_DIAGNOSTICS
                ::wprintf (L"Failed to find the Psd entity corresponding to file %ls]\n", satfileName.c_str ());
#endif
                result = ERROR;
                continue;
                }
            auto eh = found->second;
            m_curAcisFileName = satfileName;
            this->SaveElementToDatabase (eh);
            }
        }

    return result;
    }

PsdFileConversionMP& ConvertFromDgnMultiProcessingContext::CurrentPsdToAcis ()
    {
    return *m_psdToAcisChoice;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
DgnAnnotationScaleList::DgnAnnotationScaleList ()
    {
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
DgnAnnotationScaleList::DgnAnnotationScaleList (ScaleCollectionCR inCollection)
    {
    for (ScaleCollection::const_iterator iter = inCollection.begin(); iter != inCollection.end(); ++iter)
        m_scaleList.push_back (*iter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DgnAnnotationScaleList::AddScale (ScaleDefinitionCR newScale)
    {
    if (NULL == this->FindByName(newScale.GetName()))
        {
        m_scaleList.push_back (newScale);
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
ScaleDefinitionCP   DgnAnnotationScaleList::FindByName (WCharCP inName)
    {
    if (NULL == inName)
        return  NULL;

    for each (ScaleDefinitionCR scaleDef in m_scaleList)
        {
        if (0 == wcsicmp(scaleDef.GetName(), inName))
            return  &scaleDef;
        }

    return  NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
ScaleDefinitionCP   DgnAnnotationScaleList::FindByFactor (double scaleFactor)
    {
    for each (ScaleDefinitionCR scaleDef in m_scaleList)
        {
        if (fabs(scaleDef.GetScale() - scaleFactor) < TOLERANCE_ZeroScale)
            return  &scaleDef;
        }

    return  NULL;
    }
