#include "DWGEntityProcessor.h"
#include "../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbspline.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbtrace.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/db3dface.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbxref.h>
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbray.h>
#include <realdwg/base/dbxline.h>
#include <realdwg/base/dbpoint.h>
#include <realdwg/base/dbshape.h>
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbgroup.h>
#include <realdwg/base/dblight.h>
#include <realdwg/base/dbcamera.h>
#include <realdwg/base/dbhelix.h>
#include <realdwg/base/dbgeodata.h>
#include <realdwg/base/dbnamedpath.h>
#include <realdwg/base/dbmotionpath.h>
#include <realdwg/base/dbsection.h>
#include <realdwg/base/dbsurface.h>
#include <realdwg/base/dbnurbsurface.h>
#include <realdwg/base/dbplanesurface.h>
#include <realdwg/base/dbextrsurf.h>
#include <realdwg/base/dbrevsurf.h>
#include <realdwg/base/dbswepsurf.h>
#include <realdwg/base/dbloftsurf.h>
#include <realdwg/base/dbblendsurf.h>
#include <realdwg/base/dbnetsurf.h>
#include <realdwg/base/dbpatchsurf.h>
#include <realdwg/base/dboffsetsurf.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbtolerance.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbpdfref.h>
#include <realdwg/base/dbdwfref.h>
#include <realdwg/base/dbdgnref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/base/dbpointcloudex.h>
#include <realdwg/base/dbgeopositionmarker.h>
#include <realdwg/base/dbsun.h>
#include <realdwg/base/dbsky.h>
#include <realdwg/base/dbrenderenvironment.h>
#include <realdwg/base/dbrenderentry.h>
#include <realdwg/base/dbrenderglobal.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/dbmentalrayrendersettings.h>
#include <realdwg/base/dbrapidrtrendersettings.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbviewtable.h>
#include <realdwg/base/dbucstable.h>
#include <realdwg/base/dbvptable.h>
#include <realdwg/base/dbappid.h>
#include <realdwg/base/dbspatialfilter.h>
#include <realdwg/base/dblayerfilter.h>
#include <realdwg/base/dbindex.h>
#include <realdwg/base/dbspatialindex.h>
#include <realdwg/base/dblayerindex.h>
#include <realdwg/base/dbcolor.h>
#include <realdwg/base/dbtrans.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/base/dbeval.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbacis.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#endif

#include <iostream>
#include <chrono>
#include <algorithm>

namespace IModelExport {

//=======================================================================================
// DWGEntityProcessor Implementation
//=======================================================================================

DWGEntityProcessor::DWGEntityProcessor()
    : m_outputFormat(ExportFormat::DWG)
    , m_isProcessing(false)
    , m_currentEntityIndex(0)
    , m_totalEntitiesToProcess(0)
{
    InitializeEntityHandlers();
}

DWGEntityProcessor::~DWGEntityProcessor() {
}

//=======================================================================================
// Main Processing Interface Implementation
//=======================================================================================

bool DWGEntityProcessor::ProcessDatabase(void* database, const DWGProcessingOptions& options) {
#ifdef REALDWG_AVAILABLE
    if (!database) {
        LogError("Database is null");
        return false;
    }

    AcDbDatabase* acDatabase = static_cast<AcDbDatabase*>(database);
    m_currentOptions = options;
    m_stats.Reset();
    m_isProcessing = true;

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        bool success = true;

        // Process symbol tables first
        if (options.processLayers) {
            success &= ProcessLayers(database);
        }
        if (options.processLinetypes) {
            success &= ProcessLinetypes(database);
        }
        if (options.processTextStyles) {
            success &= ProcessTextStyles(database);
        }
        if (options.processDimStyles) {
            success &= ProcessDimStyles(database);
        }
        if (options.processMaterials) {
            success &= ProcessMaterials(database);
        }

        // Process model space
        success &= ProcessModelSpace(database, options);

        // Process paper space layouts
        AcDbDictionary* layoutDict;
        if (acDatabase->getLayoutDictionary(layoutDict, AcDb::kForRead) == Acad::eOk) {
            AcDbDictionaryIterator* iter = layoutDict->newIterator();
            for (; !iter->done(); iter->next()) {
                const ACHAR* layoutName;
                if (iter->getName(layoutName) == Acad::eOk) {
                    std::string layoutNameStr;
#ifdef UNICODE
                    // Convert wide string to narrow string
                    int len = WideCharToMultiByte(CP_UTF8, 0, layoutName, -1, nullptr, 0, nullptr, nullptr);
                    if (len > 0) {
                        layoutNameStr.resize(len - 1);
                        WideCharToMultiByte(CP_UTF8, 0, layoutName, -1, &layoutNameStr[0], len, nullptr, nullptr);
                    }
#else
                    layoutNameStr = layoutName;
#endif
                    if (layoutNameStr != "Model") {
                        success &= ProcessPaperSpace(database, layoutNameStr, options);
                    }
                }
            }
            delete iter;
            layoutDict->close();
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        m_stats.processingTimeMs = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        if (m_stats.processedEntities > 0) {
            m_stats.averageTimePerEntity = m_stats.processingTimeMs / m_stats.processedEntities;
        }

        m_isProcessing = false;
        return success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing database: " + std::string(e.what()));
        m_isProcessing = false;
        return false;
    }
#else
    LogError("RealDWG not available");
    return false;
#endif
}

bool DWGEntityProcessor::ProcessModelSpace(void* database, const DWGProcessingOptions& options) {
#ifdef REALDWG_AVAILABLE
    if (!database) {
        LogError("Database is null");
        return false;
    }

    AcDbDatabase* acDatabase = static_cast<AcDbDatabase*>(database);
    return IterateModelSpace(acDatabase, options);
#else
    LogError("RealDWG not available");
    return false;
#endif
}

bool DWGEntityProcessor::ProcessPaperSpace(void* database, const std::string& layoutName, const DWGProcessingOptions& options) {
#ifdef REALDWG_AVAILABLE
    if (!database) {
        LogError("Database is null");
        return false;
    }

    AcDbDatabase* acDatabase = static_cast<AcDbDatabase*>(database);
    return IteratePaperSpace(acDatabase, layoutName, options);
#else
    LogError("RealDWG not available");
    return false;
#endif
}

bool DWGEntityProcessor::ProcessEntity(void* entity, const DWGProcessingOptions& options) {
#ifdef REALDWG_AVAILABLE
    if (!entity) {
        LogError("Entity is null");
        return false;
    }

    AcDbEntity* acEntity = static_cast<AcDbEntity*>(entity);
    
    if (!ShouldProcessEntity(entity, options)) {
        m_stats.skippedEntities++;
        return true;
    }

    DWGEntityType entityType = GetEntityTypeFromAcDb(acEntity);
    std::string entityId = GetEntityId(entity);
    
    UpdateProgress("Processing " + GetEntityTypeName(entityType) + " " + entityId);

    try {
        auto handlerIt = m_entityHandlers.find(entityType);
        if (handlerIt != m_entityHandlers.end()) {
            bool success = handlerIt->second(entity);
            if (success) {
                m_stats.processedEntities++;
                m_stats.entityCounts[entityType]++;
                
                std::string layerName = GetLayerName(acEntity);
                if (!layerName.empty()) {
                    m_stats.layerCounts[layerName]++;
                }
            } else {
                m_stats.errorEntities++;
                LogError("Failed to process entity", entityType, entityId);
            }
            return success;
        } else {
            m_stats.skippedEntities++;
            LogWarning("No handler for entity type: " + GetEntityTypeName(entityType));
            return true;
        }
    }
    catch (const std::exception& e) {
        m_stats.errorEntities++;
        LogError("Exception processing entity: " + std::string(e.what()), entityType, entityId);
        return options.continueOnError;
    }
#else
    LogError("RealDWG not available");
    return false;
#endif
}

//=======================================================================================
// Entity Type Specific Processing Implementation
//=======================================================================================

bool DWGEntityProcessor::ProcessLine(void* entity) {
#ifdef REALDWG_AVAILABLE
    AcDbLine* line = static_cast<AcDbLine*>(entity);
    if (!line) return false;

    try {
        Point3d startPoint = FromAcGePoint3d(line->startPoint());
        Point3d endPoint = FromAcGePoint3d(line->endPoint());
        
        if (m_geometryProcessor) {
            return m_geometryProcessor->ProcessLine(startPoint, endPoint);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception processing line: " + std::string(e.what()));
        return false;
    }
#else
    return false;
#endif
}

bool DWGEntityProcessor::ProcessCircle(void* entity) {
#ifdef REALDWG_AVAILABLE
    AcDbCircle* circle = static_cast<AcDbCircle*>(entity);
    if (!circle) return false;

    try {
        Point3d center = FromAcGePoint3d(circle->center());
        double radius = circle->radius();
        Vector3d normal = FromAcGeVector3d(circle->normal());
        
        if (m_geometryProcessor) {
            return m_geometryProcessor->ProcessCircle(center, radius, normal);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception processing circle: " + std::string(e.what()));
        return false;
    }
#else
    return false;
#endif
}

bool DWGEntityProcessor::ProcessArc(void* entity) {
#ifdef REALDWG_AVAILABLE
    AcDbArc* arc = static_cast<AcDbArc*>(entity);
    if (!arc) return false;

    try {
        Point3d center = FromAcGePoint3d(arc->center());
        double radius = arc->radius();
        double startAngle = arc->startAngle();
        double endAngle = arc->endAngle();
        Vector3d normal = FromAcGeVector3d(arc->normal());
        
        if (m_geometryProcessor) {
            return m_geometryProcessor->ProcessArc(center, radius, startAngle, endAngle, normal);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception processing arc: " + std::string(e.what()));
        return false;
    }
#else
    return false;
#endif
}

bool DWGEntityProcessor::ProcessText(void* entity) {
#ifdef REALDWG_AVAILABLE
    AcDbText* text = static_cast<AcDbText*>(entity);
    if (!text) return false;

    try {
        Point3d position = FromAcGePoint3d(text->position());
        std::string textString;
        
        const ACHAR* textStr = text->textString();
        if (textStr) {
#ifdef UNICODE
            // Convert wide string to narrow string
            int len = WideCharToMultiByte(CP_UTF8, 0, textStr, -1, nullptr, 0, nullptr, nullptr);
            if (len > 0) {
                textString.resize(len - 1);
                WideCharToMultiByte(CP_UTF8, 0, textStr, -1, &textString[0], len, nullptr, nullptr);
            }
#else
            textString = textStr;
#endif
        }
        
        double height = text->height();
        double rotation = text->rotation();
        
        if (m_geometryProcessor) {
            return m_geometryProcessor->ProcessText(position, textString, height, rotation);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception processing text: " + std::string(e.what()));
        return false;
    }
#else
    return false;
#endif
}

//=======================================================================================
// Callback and Event Management Implementation
//=======================================================================================

void DWGEntityProcessor::SetProgressCallback(ProgressCallback callback) {
    m_progressCallback = callback;
}

void DWGEntityProcessor::SetErrorCallback(ErrorCallback callback) {
    m_errorCallback = callback;
}

void DWGEntityProcessor::ClearCallbacks() {
    m_progressCallback = nullptr;
    m_errorCallback = nullptr;
}

//=======================================================================================
// Statistics and Information Implementation
//=======================================================================================

const DWGProcessingStats& DWGEntityProcessor::GetProcessingStats() const {
    return m_stats;
}

void DWGEntityProcessor::ResetProcessingStats() {
    m_stats.Reset();
}

std::vector<std::string> DWGEntityProcessor::GetSupportedEntityTypes() const {
    std::vector<std::string> types;
    for (const auto& handler : m_entityHandlers) {
        types.push_back(GetEntityTypeName(handler.first));
    }
    std::sort(types.begin(), types.end());
    return types;
}

std::vector<std::string> DWGEntityProcessor::GetProcessedLayers() const {
    std::vector<std::string> layers;
    for (const auto& layer : m_stats.layerCounts) {
        layers.push_back(layer.first);
    }
    std::sort(layers.begin(), layers.end());
    return layers;
}

std::vector<std::string> DWGEntityProcessor::GetProcessedMaterials() const {
    std::vector<std::string> materials;
    for (const auto& material : m_stats.materialCounts) {
        materials.push_back(material.first);
    }
    std::sort(materials.begin(), materials.end());
    return materials;
}

bool DWGEntityProcessor::IsEntityTypeSupported(DWGEntityType type) const {
    return m_entityHandlers.find(type) != m_entityHandlers.end();
}

//=======================================================================================
// Configuration Implementation
//=======================================================================================

void DWGEntityProcessor::SetGeometryProcessor(std::shared_ptr<GeometryProcessor> processor) {
    m_geometryProcessor = processor;
}

std::shared_ptr<GeometryProcessor> DWGEntityProcessor::GetGeometryProcessor() const {
    return m_geometryProcessor;
}

void DWGEntityProcessor::SetOutputFormat(ExportFormat format) {
    m_outputFormat = format;
}

ExportFormat DWGEntityProcessor::GetOutputFormat() const {
    return m_outputFormat;
}

//=======================================================================================
// Private Helper Methods Implementation
//=======================================================================================

void DWGEntityProcessor::InitializeEntityHandlers() {
    // Basic 2D entities
    m_entityHandlers[DWGEntityType::Line] = [this](void* entity) { return ProcessLine(entity); };
    m_entityHandlers[DWGEntityType::Point] = [this](void* entity) { return ProcessPoint(entity); };
    m_entityHandlers[DWGEntityType::Circle] = [this](void* entity) { return ProcessCircle(entity); };
    m_entityHandlers[DWGEntityType::Arc] = [this](void* entity) { return ProcessArc(entity); };
    m_entityHandlers[DWGEntityType::Ellipse] = [this](void* entity) { return ProcessEllipse(entity); };
    m_entityHandlers[DWGEntityType::Polyline] = [this](void* entity) { return ProcessPolyline(entity); };
    m_entityHandlers[DWGEntityType::LWPolyline] = [this](void* entity) { return ProcessLWPolyline(entity); };
    m_entityHandlers[DWGEntityType::Polyline2D] = [this](void* entity) { return Process2DPolyline(entity); };
    m_entityHandlers[DWGEntityType::Polyline3D] = [this](void* entity) { return Process3DPolyline(entity); };
    m_entityHandlers[DWGEntityType::Spline] = [this](void* entity) { return ProcessSpline(entity); };
    m_entityHandlers[DWGEntityType::Ray] = [this](void* entity) { return ProcessRay(entity); };
    m_entityHandlers[DWGEntityType::XLine] = [this](void* entity) { return ProcessXLine(entity); };

    // Text entities
    m_entityHandlers[DWGEntityType::Text] = [this](void* entity) { return ProcessText(entity); };
    m_entityHandlers[DWGEntityType::MText] = [this](void* entity) { return ProcessMText(entity); };
    m_entityHandlers[DWGEntityType::AttributeDefinition] = [this](void* entity) { return ProcessAttributeDefinition(entity); };
    m_entityHandlers[DWGEntityType::Attribute] = [this](void* entity) { return ProcessAttribute(entity); };

    // Dimension entities
    m_entityHandlers[DWGEntityType::AlignedDimension] = [this](void* entity) { return ProcessAlignedDimension(entity); };
    m_entityHandlers[DWGEntityType::AngularDimension] = [this](void* entity) { return ProcessAngularDimension(entity); };
    m_entityHandlers[DWGEntityType::DiametricDimension] = [this](void* entity) { return ProcessDiametricDimension(entity); };
    m_entityHandlers[DWGEntityType::LinearDimension] = [this](void* entity) { return ProcessLinearDimension(entity); };
    m_entityHandlers[DWGEntityType::OrdinateDimension] = [this](void* entity) { return ProcessOrdinateDimension(entity); };
    m_entityHandlers[DWGEntityType::RadialDimension] = [this](void* entity) { return ProcessRadialDimension(entity); };
    m_entityHandlers[DWGEntityType::RadialDimensionLarge] = [this](void* entity) { return ProcessRadialDimensionLarge(entity); };
    m_entityHandlers[DWGEntityType::ArcDimension] = [this](void* entity) { return ProcessArcDimension(entity); };

    // Leader entities
    m_entityHandlers[DWGEntityType::Leader] = [this](void* entity) { return ProcessLeader(entity); };
    m_entityHandlers[DWGEntityType::MLeader] = [this](void* entity) { return ProcessMLeader(entity); };

    // Hatch and fill entities
    m_entityHandlers[DWGEntityType::Hatch] = [this](void* entity) { return ProcessHatch(entity); };
    m_entityHandlers[DWGEntityType::Gradient] = [this](void* entity) { return ProcessGradient(entity); };
    m_entityHandlers[DWGEntityType::Solid] = [this](void* entity) { return ProcessSolid(entity); };
    m_entityHandlers[DWGEntityType::Trace] = [this](void* entity) { return ProcessTrace(entity); };

    // 3D face and mesh entities
    m_entityHandlers[DWGEntityType::Face3D] = [this](void* entity) { return Process3DFace(entity); };
    m_entityHandlers[DWGEntityType::PolyFaceMesh] = [this](void* entity) { return ProcessPolyFaceMesh(entity); };
    m_entityHandlers[DWGEntityType::PolygonMesh] = [this](void* entity) { return ProcessPolygonMesh(entity); };
    m_entityHandlers[DWGEntityType::SubDMesh] = [this](void* entity) { return ProcessSubDMesh(entity); };

    // 3D solid entities
    m_entityHandlers[DWGEntityType::Solid3D] = [this](void* entity) { return Process3DSolid(entity); };
    m_entityHandlers[DWGEntityType::Region] = [this](void* entity) { return ProcessRegion(entity); };
    m_entityHandlers[DWGEntityType::Body] = [this](void* entity) { return ProcessBody(entity); };

    // Surface entities
    m_entityHandlers[DWGEntityType::Surface] = [this](void* entity) { return ProcessSurface(entity); };
    m_entityHandlers[DWGEntityType::NurbSurface] = [this](void* entity) { return ProcessNurbSurface(entity); };
    m_entityHandlers[DWGEntityType::PlaneSurface] = [this](void* entity) { return ProcessPlaneSurface(entity); };
    m_entityHandlers[DWGEntityType::ExtrudedSurface] = [this](void* entity) { return ProcessExtrudedSurface(entity); };
    m_entityHandlers[DWGEntityType::RevolvedSurface] = [this](void* entity) { return ProcessRevolvedSurface(entity); };
    m_entityHandlers[DWGEntityType::SweptSurface] = [this](void* entity) { return ProcessSweptSurface(entity); };
    m_entityHandlers[DWGEntityType::LoftedSurface] = [this](void* entity) { return ProcessLoftedSurface(entity); };
    m_entityHandlers[DWGEntityType::BlendSurface] = [this](void* entity) { return ProcessBlendSurface(entity); };
    m_entityHandlers[DWGEntityType::NetworkSurface] = [this](void* entity) { return ProcessNetworkSurface(entity); };
    m_entityHandlers[DWGEntityType::PatchSurface] = [this](void* entity) { return ProcessPatchSurface(entity); };
    m_entityHandlers[DWGEntityType::OffsetSurface] = [this](void* entity) { return ProcessOffsetSurface(entity); };

    // Mesh entities
    m_entityHandlers[DWGEntityType::Mesh] = [this](void* entity) { return ProcessMesh(entity); };

    // Block entities
    m_entityHandlers[DWGEntityType::BlockReference] = [this](void* entity) { return ProcessBlockReference(entity); };
    m_entityHandlers[DWGEntityType::MInsert] = [this](void* entity) { return ProcessMInsert(entity); };
    m_entityHandlers[DWGEntityType::XRef] = [this](void* entity) { return ProcessXRef(entity); };

    // Other entities
    m_entityHandlers[DWGEntityType::MLine] = [this](void* entity) { return ProcessMLine(entity); };
    m_entityHandlers[DWGEntityType::Shape] = [this](void* entity) { return ProcessShape(entity); };
    m_entityHandlers[DWGEntityType::RasterImage] = [this](void* entity) { return ProcessRasterImage(entity); };
    m_entityHandlers[DWGEntityType::WipeOut] = [this](void* entity) { return ProcessWipeOut(entity); };
    m_entityHandlers[DWGEntityType::OLE2Frame] = [this](void* entity) { return ProcessOLE2Frame(entity); };
    m_entityHandlers[DWGEntityType::Viewport] = [this](void* entity) { return ProcessViewport(entity); };
    m_entityHandlers[DWGEntityType::Table] = [this](void* entity) { return ProcessTable(entity); };
    m_entityHandlers[DWGEntityType::Field] = [this](void* entity) { return ProcessField(entity); };
    m_entityHandlers[DWGEntityType::Group] = [this](void* entity) { return ProcessGroup(entity); };
    m_entityHandlers[DWGEntityType::Light] = [this](void* entity) { return ProcessLight(entity); };
    m_entityHandlers[DWGEntityType::Camera] = [this](void* entity) { return ProcessCamera(entity); };
    m_entityHandlers[DWGEntityType::Helix] = [this](void* entity) { return ProcessHelix(entity); };
    m_entityHandlers[DWGEntityType::GeoData] = [this](void* entity) { return ProcessGeoData(entity); };
    m_entityHandlers[DWGEntityType::NamedPath] = [this](void* entity) { return ProcessNamedPath(entity); };
    m_entityHandlers[DWGEntityType::MotionPath] = [this](void* entity) { return ProcessMotionPath(entity); };
    m_entityHandlers[DWGEntityType::Section] = [this](void* entity) { return ProcessSection(entity); };
    m_entityHandlers[DWGEntityType::Tolerance] = [this](void* entity) { return ProcessTolerance(entity); };
    m_entityHandlers[DWGEntityType::FCF] = [this](void* entity) { return ProcessFCF(entity); };
    m_entityHandlers[DWGEntityType::ProxyEntity] = [this](void* entity) { return ProcessProxyEntity(entity); };
    m_entityHandlers[DWGEntityType::UnderlayReference] = [this](void* entity) { return ProcessUnderlayReference(entity); };
    m_entityHandlers[DWGEntityType::DwfReference] = [this](void* entity) { return ProcessDwfReference(entity); };
    m_entityHandlers[DWGEntityType::DgnReference] = [this](void* entity) { return ProcessDgnReference(entity); };
    m_entityHandlers[DWGEntityType::PdfReference] = [this](void* entity) { return ProcessPdfReference(entity); };
    m_entityHandlers[DWGEntityType::PointCloud] = [this](void* entity) { return ProcessPointCloud(entity); };
    m_entityHandlers[DWGEntityType::PointCloudEx] = [this](void* entity) { return ProcessPointCloudEx(entity); };
}

} // namespace IModelExport
