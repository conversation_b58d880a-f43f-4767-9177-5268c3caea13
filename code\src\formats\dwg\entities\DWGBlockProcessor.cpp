#include "DWGBlockProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbblktbl.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/ge/gematrix3d.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <regex>
#include <filesystem>

namespace IModelExport {

//=======================================================================================
// AttributeDefinition Implementation
//=======================================================================================

bool AttributeDefinition::IsValid() const {
    // Validate tag (required)
    if (tag.empty()) {
        return false;
    }
    
    // Validate position
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(height) || !std::isfinite(rotation) || !std::isfinite(widthFactor) || !std::isfinite(obliqueAngle)) {
        return false;
    }
    
    // Validate positive values
    if (height <= 0.0 || widthFactor <= 0.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// AttributeInstance Implementation
//=======================================================================================

bool AttributeInstance::IsValid() const {
    // Validate tag (required)
    if (tag.empty()) {
        return false;
    }
    
    // Validate position
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(height) || !std::isfinite(rotation) || !std::isfinite(widthFactor) || !std::isfinite(obliqueAngle)) {
        return false;
    }
    
    // Validate positive values
    if (height <= 0.0 || widthFactor <= 0.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// BlockDefinition Implementation
//=======================================================================================

bool BlockDefinition::IsValid() const {
    // Validate name (required)
    if (name.empty()) {
        return false;
    }
    
    // Validate base point
    if (!std::isfinite(basePoint.x) || !std::isfinite(basePoint.y) || !std::isfinite(basePoint.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(unitScale) || unitScale <= 0.0) {
        return false;
    }
    
    // Validate entities
    for (const auto& entity : entities) {
        if (entity.entityType.empty()) {
            return false;
        }
        
        for (const auto& point : entity.geometry) {
            if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
                return false;
            }
        }
    }
    
    // Validate attribute definitions
    for (const auto& attDef : attributeDefinitions) {
        if (!attDef.IsValid()) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// BlockReference Implementation
//=======================================================================================

bool BlockReference::IsValid() const {
    // Validate block name (required)
    if (blockName.empty()) {
        return false;
    }
    
    // Validate position
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z)) {
        return false;
    }
    
    // Validate scale
    if (!std::isfinite(scale.x) || !std::isfinite(scale.y) || !std::isfinite(scale.z)) {
        return false;
    }
    
    // Validate positive scale values
    if (scale.x <= 0.0 || scale.y <= 0.0 || scale.z <= 0.0) {
        return false;
    }
    
    // Validate rotation
    if (!std::isfinite(rotation)) {
        return false;
    }
    
    // Validate normal vector
    if (!std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    // Validate array properties if it's an array insert
    if (isArrayInsert) {
        if (rowCount <= 0 || columnCount <= 0) {
            return false;
        }
        
        if (!std::isfinite(rowSpacing) || !std::isfinite(columnSpacing)) {
            return false;
        }
        
        if (rowSpacing < 0.0 || columnSpacing < 0.0) {
            return false;
        }
    }
    
    // Validate attributes
    for (const auto& attribute : attributes) {
        if (!attribute.IsValid()) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// DWGBlockProcessor Implementation
//=======================================================================================

DWGBlockProcessor::DWGBlockProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_blockTolerance(1e-6)
    , m_attributeTolerance(1e-3)
    , m_enableBlockOptimization(true)
    , m_enableAttributeValidation(true)
    , m_enableXRefValidation(true)
    , m_autoRepairBlocks(true)
    , m_maxBlockNestingLevel(10)
    , m_maxBlockEntities(10000)
{
    // Add default block library paths
    m_blockLibraryPaths.push_back("./blocks/");
    m_blockLibraryPaths.push_back("./library/");
    m_defaultBlockLibrary = "./blocks/standard.dwg";
}

DWGProcessingStatus DWGBlockProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract block geometry from element
        // For demonstration, we'll create a sample block reference
        
        if (element.type == ElementType::GeometricElement) {
            BlockReference blockRef;
            blockRef.blockName = "StandardBlock";
            blockRef.position = Point3d(0, 0, 0);
            blockRef.scale = Vector3d(1, 1, 1);
            blockRef.rotation = 0.0;
            blockRef.normal = Vector3d(0, 0, 1);
            
            return ProcessBlockReference(blockRef, "Blocks");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing block entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGBlockProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGBlockProcessor::ProcessBlockDefinition(const BlockDefinition& definition) {
    // Validate block definition
    auto validation = ValidateBlockDefinition(definition);
    if (!validation.isValid) {
        LogError("Block definition validation failed for: " + definition.name);
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Check if block already exists
    if (BlockExists(definition.name)) {
        LogWarning("Block definition already exists: " + definition.name);
        return DWGProcessingStatus::AlreadyExists;
    }
    
    // Create block definition
    if (!CreateBlockDefinition(definition)) {
        LogError("Failed to create block definition: " + definition.name);
        return DWGProcessingStatus::Failed;
    }
    
    m_processedBlockDefinitions++;
    LogInfo("Successfully processed block definition: " + definition.name);
    return DWGProcessingStatus::Success;
}

DWGProcessingStatus DWGBlockProcessor::ProcessBlockReference(const BlockReference& reference, const std::string& layer) {
    // Validate block reference
    auto validation = ValidateBlockReference(reference);
    if (!validation.isValid) {
        LogError("Block reference validation failed for: " + reference.blockName);
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Check if referenced block exists
    if (!BlockExists(reference.blockName)) {
        LogError("Referenced block does not exist: " + reference.blockName);
        return DWGProcessingStatus::MissingDependency;
    }
    
    // Transform reference
    BlockReference transformedReference = reference;
    transformedReference.position = TransformPoint(transformedReference.position);
    
    // Process array insert if applicable
    if (reference.IsArrayInsert()) {
        return ProcessMInsertBlock(transformedReference, layer);
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbBlockReference* blockRef = CreateDWGBlockReference(transformedReference);
        if (!blockRef) {
            LogError("Failed to create DWG block reference");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetBlockReferenceProperties(blockRef, transformedReference)) {
            delete blockRef;
            LogError("Failed to set block reference properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(blockRef, layer)) {
            delete blockRef;
            LogError("Failed to set block reference entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        // Process attributes
        if (!ProcessBlockAttributes(transformedReference)) {
            LogWarning("Failed to process some block attributes");
        }
        
        if (!AddEntityToModelSpace(blockRef)) {
            delete blockRef;
            LogError("Failed to add block reference to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedBlockReferences++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG block reference: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - block reference creation skipped");
    m_processedBlockReferences++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Block Management
//=======================================================================================

bool DWGBlockProcessor::CreateBlockDefinition(const BlockDefinition& definition) {
    // Store block definition
    m_blockDefinitions[definition.name] = definition;
    
    // If it's an XRef, store the path
    if (definition.isXRef && !definition.xrefPath.empty()) {
        m_xrefPaths[definition.name] = definition.xrefPath;
        m_xrefLoadStatus[definition.name] = !definition.isUnloaded;
    }
    
    LogInfo("Created block definition: " + definition.name);
    return true;
}

bool DWGBlockProcessor::UpdateBlockDefinition(const BlockDefinition& definition) {
    auto it = m_blockDefinitions.find(definition.name);
    if (it == m_blockDefinitions.end()) {
        LogError("Block definition not found for update: " + definition.name);
        return false;
    }
    
    it->second = definition;
    LogInfo("Updated block definition: " + definition.name);
    return true;
}

bool DWGBlockProcessor::DeleteBlockDefinition(const std::string& blockName) {
    auto it = m_blockDefinitions.find(blockName);
    if (it == m_blockDefinitions.end()) {
        LogError("Block definition not found for deletion: " + blockName);
        return false;
    }
    
    m_blockDefinitions.erase(it);
    
    // Clean up XRef data if applicable
    m_xrefPaths.erase(blockName);
    m_xrefLoadStatus.erase(blockName);
    
    LogInfo("Deleted block definition: " + blockName);
    return true;
}

bool DWGBlockProcessor::BlockExists(const std::string& blockName) const {
    return m_blockDefinitions.find(blockName) != m_blockDefinitions.end();
}

std::vector<std::string> DWGBlockProcessor::GetAvailableBlocks() const {
    std::vector<std::string> blockNames;
    blockNames.reserve(m_blockDefinitions.size());
    
    for (const auto& pair : m_blockDefinitions) {
        blockNames.push_back(pair.first);
    }
    
    std::sort(blockNames.begin(), blockNames.end());
    return blockNames;
}

BlockDefinition DWGBlockProcessor::GetBlockDefinition(const std::string& blockName) const {
    auto it = m_blockDefinitions.find(blockName);
    if (it != m_blockDefinitions.end()) {
        return it->second;
    }
    
    // Return empty block definition if not found
    return BlockDefinition();
}

//=======================================================================================
// Validation Methods
//=======================================================================================

BlockValidationResult DWGBlockProcessor::ValidateBlockDefinition(const BlockDefinition& definition) const {
    BlockValidationResult result;
    result.isValid = true;
    
    // Validate name
    result.hasValidName = ValidateBlockName(definition.name);
    if (!result.hasValidName) {
        result.AddBlockError("Invalid block name: " + definition.name);
    }
    
    // Validate geometry
    result.hasValidGeometry = ValidateBlockGeometry(definition);
    if (!result.hasValidGeometry) {
        result.AddBlockError("Invalid block geometry");
    }
    
    // Validate attributes
    result.hasValidAttributes = true;
    for (const auto& attDef : definition.attributeDefinitions) {
        if (!ValidateAttributeDefinition(attDef)) {
            result.hasValidAttributes = false;
            result.AddBlockError("Invalid attribute definition: " + attDef.tag);
        }
    }
    
    // Validate base point
    result.hasValidBasePoint = std::isfinite(definition.basePoint.x) && 
                              std::isfinite(definition.basePoint.y) && 
                              std::isfinite(definition.basePoint.z);
    if (!result.hasValidBasePoint) {
        result.AddBlockError("Invalid base point");
    }
    
    // Validate XRef path if applicable
    if (definition.isXRef) {
        result.hasValidXRefPath = ValidateXRefPath(definition.xrefPath);
        if (!result.hasValidXRefPath) {
            result.AddBlockError("Invalid XRef path: " + definition.xrefPath);
        }
    } else {
        result.hasValidXRefPath = true;
    }
    
    // Set counts
    result.entityCount = static_cast<int>(definition.entities.size());
    result.attributeCount = static_cast<int>(definition.attributeDefinitions.size());
    
    // Check entity count limit
    if (result.entityCount > static_cast<int>(m_maxBlockEntities)) {
        result.AddBlockError("Too many entities in block (limit: " + std::to_string(m_maxBlockEntities) + ")");
        result.isValid = false;
    }
    
    return result;
}

BlockReferenceValidationResult DWGBlockProcessor::ValidateBlockReference(const BlockReference& reference) const {
    BlockReferenceValidationResult result;
    result.isValid = true;
    
    // Validate block name
    result.hasValidBlockName = !reference.blockName.empty();
    if (!result.hasValidBlockName) {
        result.AddBlockRefError("Empty block name");
    }
    
    // Check if block exists
    result.blockExists = BlockExists(reference.blockName);
    if (!result.blockExists) {
        result.AddBlockRefError("Referenced block does not exist: " + reference.blockName);
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(reference.position.x) && 
                             std::isfinite(reference.position.y) && 
                             std::isfinite(reference.position.z);
    if (!result.hasValidPosition) {
        result.AddBlockRefError("Invalid position");
    }
    
    // Validate scale
    result.hasValidScale = std::isfinite(reference.scale.x) && 
                          std::isfinite(reference.scale.y) && 
                          std::isfinite(reference.scale.z) &&
                          reference.scale.x > 0.0 && 
                          reference.scale.y > 0.0 && 
                          reference.scale.z > 0.0;
    if (!result.hasValidScale) {
        result.AddBlockRefError("Invalid scale factors");
    }
    
    // Validate rotation
    result.hasValidRotation = std::isfinite(reference.rotation);
    if (!result.hasValidRotation) {
        result.AddBlockRefError("Invalid rotation angle");
    }
    
    // Validate attributes
    result.hasValidAttributes = true;
    for (const auto& attribute : reference.attributes) {
        if (!ValidateAttributeInstance(attribute)) {
            result.hasValidAttributes = false;
            result.AddBlockRefError("Invalid attribute instance: " + attribute.tag);
        }
    }
    
    return result;
}

bool DWGBlockProcessor::ValidateBlockName(const std::string& name) const {
    if (name.empty()) {
        return false;
    }
    
    // Check for reserved names
    if (IsReservedBlockName(name)) {
        return false;
    }
    
    // Check for invalid characters
    static const std::regex invalidChars(R"([<>:"/\\|?*])");
    if (std::regex_search(name, invalidChars)) {
        return false;
    }
    
    return true;
}

bool DWGBlockProcessor::ValidateAttributeDefinition(const AttributeDefinition& attDef) const {
    return attDef.IsValid() && ValidateAttributeTag(attDef.tag);
}

bool DWGBlockProcessor::ValidateAttributeInstance(const AttributeInstance& attribute) const {
    return attribute.IsValid() && ValidateAttributeTag(attribute.tag);
}

bool DWGBlockProcessor::ValidateXRefPath(const std::string& path) const {
    if (path.empty()) {
        return false;
    }
    
    // Check if file exists
    try {
        return std::filesystem::exists(path);
    }
    catch (...) {
        return false;
    }
}

//=======================================================================================
// Transformation Methods
//=======================================================================================

std::vector<Point3d> DWGBlockProcessor::TransformBlockGeometry(const BlockDefinition& definition, const BlockReference& reference) const {
    std::vector<Point3d> transformedPoints;
    
    // Calculate transformation matrix
    double matrix[4][4];
    CalculateTransformMatrix(reference, matrix);
    
    // Transform all geometry points
    for (const auto& entity : definition.entities) {
        for (const auto& point : entity.geometry) {
            Point3d transformedPoint = ApplyTransformMatrix(point, matrix);
            transformedPoints.push_back(transformedPoint);
        }
    }
    
    return transformedPoints;
}

Point3d DWGBlockProcessor::TransformPoint(const Point3d& point, const BlockReference& reference) const {
    double matrix[4][4];
    CalculateTransformMatrix(reference, matrix);
    return ApplyTransformMatrix(point, matrix);
}

Vector3d DWGBlockProcessor::TransformVector(const Vector3d& vector, const BlockReference& reference) const {
    double matrix[4][4];
    CalculateTransformMatrix(reference, matrix);
    return ApplyTransformMatrix(vector, matrix);
}

//=======================================================================================
// Helper Methods
//=======================================================================================

bool DWGBlockProcessor::ValidateBlockGeometry(const BlockDefinition& definition) const {
    // Check that all entities have valid geometry
    for (const auto& entity : definition.entities) {
        if (entity.entityType.empty()) {
            return false;
        }
        
        for (const auto& point : entity.geometry) {
            if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
                return false;
            }
        }
    }
    
    return true;
}

bool DWGBlockProcessor::IsReservedBlockName(const std::string& name) const {
    static const std::vector<std::string> reservedNames = {
        "*Model_Space", "*Paper_Space", "*Paper_Space0"
    };
    
    return std::find(reservedNames.begin(), reservedNames.end(), name) != reservedNames.end();
}

bool DWGBlockProcessor::ValidateAttributeTag(const std::string& tag) const {
    if (tag.empty()) {
        return false;
    }
    
    // Check for invalid characters in tag
    static const std::regex invalidChars(R"([\s<>:"/\\|?*])");
    return !std::regex_search(tag, invalidChars);
}

std::string DWGBlockProcessor::SanitizeBlockName(const std::string& name) const {
    std::string sanitized = name;
    
    // Replace invalid characters with underscores
    static const std::regex invalidChars(R"([<>:"/\\|?*])");
    sanitized = std::regex_replace(sanitized, invalidChars, "_");
    
    // Ensure it's not empty
    if (sanitized.empty()) {
        sanitized = "Block";
    }
    
    return sanitized;
}

void DWGBlockProcessor::CalculateTransformMatrix(const BlockReference& reference, double matrix[4][4]) const {
    // Initialize as identity matrix
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            matrix[i][j] = (i == j) ? 1.0 : 0.0;
        }
    }
    
    // Apply scale
    matrix[0][0] = reference.scale.x;
    matrix[1][1] = reference.scale.y;
    matrix[2][2] = reference.scale.z;
    
    // Apply rotation (simplified - only Z rotation)
    if (std::abs(reference.rotation) > 1e-10) {
        double cos_r = std::cos(reference.rotation);
        double sin_r = std::sin(reference.rotation);
        
        double temp[4][4];
        memcpy(temp, matrix, sizeof(temp));
        
        matrix[0][0] = temp[0][0] * cos_r - temp[0][1] * sin_r;
        matrix[0][1] = temp[0][0] * sin_r + temp[0][1] * cos_r;
        matrix[1][0] = temp[1][0] * cos_r - temp[1][1] * sin_r;
        matrix[1][1] = temp[1][0] * sin_r + temp[1][1] * cos_r;
    }
    
    // Apply translation
    matrix[0][3] = reference.position.x;
    matrix[1][3] = reference.position.y;
    matrix[2][3] = reference.position.z;
}

Point3d DWGBlockProcessor::ApplyTransformMatrix(const Point3d& point, const double matrix[4][4]) const {
    return Point3d(
        matrix[0][0] * point.x + matrix[0][1] * point.y + matrix[0][2] * point.z + matrix[0][3],
        matrix[1][0] * point.x + matrix[1][1] * point.y + matrix[1][2] * point.z + matrix[1][3],
        matrix[2][0] * point.x + matrix[2][1] * point.y + matrix[2][2] * point.z + matrix[2][3]
    );
}

Vector3d DWGBlockProcessor::ApplyTransformMatrix(const Vector3d& vector, const double matrix[4][4]) const {
    return Vector3d(
        matrix[0][0] * vector.x + matrix[0][1] * vector.y + matrix[0][2] * vector.z,
        matrix[1][0] * vector.x + matrix[1][1] * vector.y + matrix[1][2] * vector.z,
        matrix[2][0] * vector.x + matrix[2][1] * vector.y + matrix[2][2] * vector.z
    );
}

bool DWGBlockProcessor::ProcessBlockAttributes(const BlockReference& reference) const {
    for (const auto& attribute : reference.attributes) {
        if (!ProcessAttributeInstance(attribute, reference.blockName)) {
            LogWarning("Failed to process attribute: " + attribute.tag);
        } else {
            m_processedAttributes++;
        }
    }
    
    return true;
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbBlockReference* DWGBlockProcessor::CreateDWGBlockReference(const BlockReference& reference) const {
    try {
        AcGePoint3d insertionPoint(reference.position.x, reference.position.y, reference.position.z);
        AcDbObjectId blockId = GetBlockTableRecordId(reference.blockName);
        
        if (blockId.isNull()) {
            return nullptr;
        }
        
        AcDbBlockReference* blockRef = new AcDbBlockReference(insertionPoint, blockId);
        
        // Set scale factors
        blockRef->setScaleFactors(AcGeScale3d(reference.scale.x, reference.scale.y, reference.scale.z));
        
        // Set rotation
        if (std::abs(reference.rotation) > 1e-10) {
            blockRef->setRotation(reference.rotation);
        }
        
        return blockRef;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGBlockProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGBlockProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

AcDbObjectId DWGBlockProcessor::GetBlockTableRecordId(const std::string& blockName) const {
    // This would query the block table for the block record
    // For now, return a null ID
    return AcDbObjectId::kNull;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
