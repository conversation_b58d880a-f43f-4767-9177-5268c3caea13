/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdBrepConvert.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

#ifdef REALDWG_NOISY_DEBUG
#define DEBUG_DUMP_TOPOLOGY
#define DEBUG_ADDING_PKGEOMETRY
//#define DEBUG_BAD_GEOMETRY
#endif

typedef bvector <PK_CLASS_t>                ParasolidClassList;
typedef bvector <PK_TOPOL_sense_t>          ParasolidSenseList;
typedef bpair <void*, int>                  AcBrepToPsClassEntry;
typedef bmap <void*, int>                   AcBrepToPsClassMap;
typedef bpair <PK_ENTITY_t, PK_LOGICAL_t>   IsPositiveOrientationEntry;
typedef bmap <PK_ENTITY_t, PK_LOGICAL_t>    IsPositiveOrientationMap;

FailuresMonitoring& FailuresMonitoring::Instance ()
    {
    static FailuresMonitoring s_log;
    return s_log;
    }
BeFileName FailuresMonitoring::FileName () const
    {
    BeFileName fileName (_wgetenv (L"AppData"));
    fileName
        .AppendToPath (L"Bentley")
        .AppendToPath (L"MicroStation")
        .AppendToPath (L"AcisToParaSolid")
        .AppendToPath (L"Log");

    BeFileName::CreateNewDirectory (fileName.c_str ());

    static wchar_t s_app = L'A';
    WString appended (L"CantConvertBrep");
    appended += s_app;

    s_app++;

    fileName
        .AppendToPath (appended.c_str ())
        .AppendExtension (L"json");

    BeFileName::BeDeleteFile (fileName.c_str ());
    return fileName;
    }
std::string FailuresMonitoring::FailedLocStr () const
    {
    return "FailedLoc" + std::to_string (m_calls);
    }
std::string FailuresMonitoring::ErrorMsgStr () const
    {
    return "ErrorMessage" + std::to_string (m_calls);
    }
Json::Value FailuresMonitoring::MessageAsJson (int const& loc, std::string const& message /*= ""*/) const
    {
    Json::Value value;
    value[FailedLocStr ().c_str ()] = loc;
    value[ErrorMsgStr ().c_str ()] = message.c_str ();
    return value;
    }
void FailuresMonitoring::SaveCallsCount ()
    {
    Instance ().m_value["Failure count"] = m_calls;
    }
bool FailuresMonitoring::WriteToFile () const
    {
    auto fileName = FileName ();
    Utf8String const string = Json::StyledWriter ().write (m_value);

    BeFile file;
    uint32_t bytesWritten = 0;

    return file.Create (fileName.c_str ()) == BeFileStatus::Success
        && file.Write (&bytesWritten, string.c_str (), (uint32_t) string.size ()) == BeFileStatus::Success
        && file.Close () == BeFileStatus::Success;
    }
bool FailuresMonitoring::IsOn ()
    {
    return Instance().m_isOn;
    }
RealDwgStatus FailuresMonitoring::CantConvertBrepStatus ()
    {
    return static_cast<RealDwgStatus>(REALDWG_StatusBase - 1095);
    }

void AddOneToJson (Json::Value& value, const char *key)
    {
    if (!value.isMember (key))
        value[key] = 1;
    else
        {
        BeAssert (value[key].isInt ());
        int val = value[key].asInt ();
        value[key] = val + 1;
        }
    }

RealDwgStatus FailuresMonitoring::Add (int const& loc, std::string const& message /*= ""*/)
    {
#ifdef REALDWG_NOISY_DEBUG
    if (IsOn ())
        {
        Instance ().m_calls++;
        AddOneToJson (Instance ().m_value, std::to_string (loc).c_str ());
        if (!message.empty ())
            AddOneToJson (Instance ().m_value, message.c_str ());

        //Instance().m_value[Instance().FailedLocStr ().c_str ()] = loc;
        //Instance().m_value[Instance().ErrorMsgStr ().c_str ()] = message.c_str ();
        }
#endif
    return CantConvertBrepStatus ();
    }
void FailuresMonitoring::SetOn ()
    {
    if (!Instance ().m_isOn)
        {
        Instance ().m_isOn = true;
        Reset ();
        }
    }
void FailuresMonitoring::Save ()
    {
    if (IsOn ())
        {
        Instance ().SaveCallsCount ();
        Instance ().WriteToFile ();
        }
    }
void FailuresMonitoring::SetOff ()
    {
    if (Instance ().m_isOn)
        {
        Instance ().m_isOn = false;
        Reset ();
        }
    }
int FailuresMonitoring::FailureCounts ()
    {
    return Instance().m_calls;
    }
void FailuresMonitoring::Reset ()
    {
    Instance ().m_value.clear ();
    Instance ().m_calls = 0;
    }

#ifdef CantConvertBrep
#undef CantConvertBrep
#endif

#define CantConvertBrep FailuresMonitoring::Add (__LINE__)

#define CantConvertBrepLogged(message) FailuresMonitoring::Add (__LINE__, message)

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
static char     SenseToChar (PK_TOPOL_sense_t pkSense)
    {
    // debug topological sense
    char    sign = ' ';
    if (pkSense == PK_TOPOL_sense_positive_c)
        sign = '+';
    else if (pkSense == PK_TOPOL_sense_negative_c)
        sign = '-';
    return  sign;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
static char     OrientationToChar (PK_LOGICAL_t oriented2Geometry)
    {
    // debug topological entity orientation with respect to its geometry
    return  oriented2Geometry ? '+' : '-';
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
static const char*  LooptypeToString (AcBr::LoopType loopType)
    {
    switch (loopType)
        {
        case AcBr::kLoopUnclassified:   return  "unclassified";
        case AcBr::kLoopExterior:       return  "exterior";
        case AcBr::kLoopInterior:       return  "interior";
        case AcBr::kLoopWinding:        return  "winding";
        default:                        return  "unknown";
        }
    }

#ifdef REALDWG_DIAGNOSTICS
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
static void     DiagnosticPrintAxis1 (PK_AXIS1_sf_s const& pkAxis1, TransformCP transform = nullptr, bool endLine = false)
    {
    // debugging PK_AXIS1_sf_s
    DPoint3d    point = DPoint3d::From (pkAxis1.location.coord[0], pkAxis1.location.coord[1], pkAxis1.location.coord[2]);

    if (nullptr != transform)
        transform->Multiply (point);

    printf ("at {%g,%g,%g}, x{%g,%g,%g}", point.x, point.y, point.z, pkAxis1.axis.coord[0], pkAxis1.axis.coord[1], pkAxis1.axis.coord[2]);

    if (endLine)
        printf ("\n");
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
static void     DiagnosticPrintAxis2 (PK_AXIS2_sf_s const& pkAxis2, TransformCP transform = nullptr, bool endLine = false)
    {
    // debugging PK_AXIS2_sf_s
    DPoint3d    point = DPoint3d::From (pkAxis2.location.coord[0], pkAxis2.location.coord[1], pkAxis2.location.coord[2]);

    if (nullptr != transform)
        transform->Multiply (point);

    printf ("at {%g,%g,%g}, z{%g,%g,%g}, x{%g,%g,%g}", point.x, point.y, point.z,
            pkAxis2.axis.coord[0], pkAxis2.axis.coord[1], pkAxis2.axis.coord[2],
            pkAxis2.ref_direction.coord[0], pkAxis2.ref_direction.coord[1], pkAxis2.ref_direction.coord[2]);

    if (endLine)
        printf ("\n");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
static const char*  ClassToString (PK_CLASS_t topologyClass)
    {
    static char     classNumber[50] = { 0 };

    switch (topologyClass)
        {
        case PK_CLASS_body:     return  "Body";
        case PK_CLASS_region:   return  "Region";
        case PK_CLASS_shell:    return  "Shell";
        case PK_CLASS_face:     return  "Face";
        case PK_CLASS_loop:     return  "Loop";
        case PK_CLASS_edge:     return  "Edge";
        case PK_CLASS_vertex:   return  "Vertex";
        // topological class types we do not use to create body from ASM
        case PK_CLASS_fin:      return  "Fin";
        case PK_CLASS_part:     return  "Part";
        case PK_CLASS_assembly: return  "Assembly";
        case PK_CLASS_instance: return  "Instance";
        // geometrical classes
        case PK_CLASS_curve:    return  "Curve";
        case PK_CLASS_line:     return  "Line";
        case PK_CLASS_circle:   return  "Circle";
        case PK_CLASS_ellipse:  return  "Ellipse";
        case PK_CLASS_bcurve:   return  "BSplineCurve";
        case PK_CLASS_icurve:   return  "IntersectCurve";
        case PK_CLASS_fcurve:   return  "ForeignCurve";
        case PK_CLASS_spcurve:  return  "SP-Curve";
        case PK_CLASS_trcurve:  return  "TrimmedCurve";
        case PK_CLASS_cpcurve:  return  "CP-Curve";
        default:
            sprintf (classNumber, "class=%d", (int)topologyClass);
            return  classNumber;
        }
    }
#endif


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/14
+===============+===============+===============+===============+===============+======*/
class    AcBRepToParasolid
{
private:
    ConvertToDgnContext*        m_toDgnContext;
    bool                        m_needTransformation;
    AcGeMatrix3d                m_toParasolidMatrix;
    double                      m_toParasolidScale;
    double                      m_vertexTolerance;
    bool                        m_debugReportStarted;
    bool                        m_isSheetBody;
    bool                        m_hasAnalyticSurface;
    bool                        m_startedPSolidSession;
    // input DWG Brep entity
    AcBrBrep*                   m_acBrepEntity;
    // output Parasolid topology data
    ParasolidClassList          m_pkClasses;
    IntArray                    m_pkParents;
    IntArray                    m_pkChildren;
    ParasolidSenseList          m_pkSenses;
    IntArray                    m_pkGeometryTags;
    IsPositiveOrientationMap    m_isFaceOriented2Surface;
    IsPositiveOrientationMap    m_isEdgeOriented2Curve;
    // intermittent lookup maps to search for a Parasolid class from an AcBr entity
    AcBrepToPsClassMap          m_acBodyToClassIndex;
    AcBrepToPsClassMap          m_acComplexToClassIndex;
    AcBrepToPsClassMap          m_acShellToClassIndex;
    AcBrepToPsClassMap          m_acFaceToClassIndex;
    AcBrepToPsClassMap          m_acLoopToClassIndex;
    AcBrepToPsClassMap          m_acEdgeToClassIndex;
    AcBrepToPsClassMap          m_acVertexToClassIndex;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            InitParasolidData ()
    {
    m_pkClasses.clear ();
    m_pkParents.clear ();
    m_pkChildren.clear ();
    m_pkSenses.clear ();
    m_pkGeometryTags.clear ();
    m_isFaceOriented2Surface.clear ();
    m_isEdgeOriented2Curve.clear ();
    m_acBrepEntity = NULL;
    m_acBodyToClassIndex.clear ();
    m_acComplexToClassIndex.clear ();
    m_acShellToClassIndex.clear ();
    m_acFaceToClassIndex.clear ();
    m_acLoopToClassIndex.clear ();
    m_acEdgeToClassIndex.clear ();
    m_acVertexToClassIndex.clear ();
    m_needTransformation = false;
    m_toParasolidMatrix.setToIdentity ();
    m_toParasolidScale = 1.0;
    m_vertexTolerance = -1.0;
    m_debugReportStarted = false;
    m_isSheetBody = false;
    m_hasAnalyticSurface = false;
    m_startedPSolidSession = false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            FixCurveOrientation (AcGeCurve3d* acCurve, AcBrEdge* acEdge)
    {
    /*--------------------------------------------------------------------------------------------------------------
    This is work around a likely bug in RealDWG R2015 in which AcBrEdge::getOrientToCurve may return us an incorrect 
    flag.  The ARX doc says that the flag should be true if the curve parameterization runs in the same direction as 
    the edge direction, i.e. from vertex1 to vertex2.  But that is not always true, as a case found in TFS# 126992.
    Since we cannot reset the flag for the edge, we check the actual orientation of the curve against what is returned
    from getOrientToCurve.  Then we will revert curve parameterization if the two don't match.  We resort to invert
    the curve due to lack of a set method in AcBrEdge to reset the flag.
    --------------------------------------------------------------------------------------------------------------*/
    AcBrVertex      vertex1, vertex2;
    AcGePoint3d     edgePoint1, edgePoint2, curvePoint1, curvePoint2;
    Adesk::Boolean  orientationPerASM = Adesk::kFalse;

    if (acCurve->isClosed())
        {
        // ASM makes a closed curve to always oriented to the edge? TFS# 131913.
        if (AcBr::eOk == acEdge->getOrientToCurve(orientationPerASM) && Adesk::kFalse == orientationPerASM)
            acCurve->reverseParam ();
        }
    else if (acCurve->hasStartPoint(curvePoint1) && acCurve->hasEndPoint(curvePoint2) &&
        AcBr::eOk == acEdge->getVertex1(vertex1) && AcBr::eOk == acEdge->getVertex2(vertex2) &&
        AcBr::eOk == vertex1.getPoint(edgePoint1) && AcBr::eOk == vertex2.getPoint(edgePoint2) &&
        AcBr::eOk == acEdge->getOrientToCurve(orientationPerASM))
        {
        AcGeVector3d    edgeVector = edgePoint2 - edgePoint1;
        AcGeVector3d    curveVector = curvePoint2 - curvePoint1;

        edgeVector.normalize ();
        curveVector.normalize ();

        Adesk::Boolean  actualOrientation = edgeVector.dotProduct(curveVector) > 0.0;

        if (orientationPerASM != actualOrientation)
            acCurve->reverseParam ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetBasisAxes (PK_AXIS2_sf_t& outAxes, const AcGePoint3d& inOrigin, const AcGeVector3d& inAxis1, const AcGeVector3d& inAxis2)
    {
    // set two mutually perpenticular axes at a point for Parasolid
    if (m_needTransformation)
        {
        AcGePoint3d     transformedOrigin = inOrigin;
        transformedOrigin.transformBy (m_toParasolidMatrix);

        memcpy (&outAxes.location.coord[0], &transformedOrigin, sizeof outAxes.location.coord);
        }
    else
        {
        memcpy (&outAxes.location.coord[0], &inOrigin, sizeof outAxes.location.coord);
        }

    memcpy (&outAxes.axis.coord[0], &inAxis1, sizeof outAxes.axis.coord);
    memcpy (&outAxes.ref_direction.coord[0], &inAxis2, sizeof outAxes.ref_direction.coord);

    PK_VECTOR_normalise (outAxes.axis, &outAxes.axis);
    PK_VECTOR_normalise (outAxes.ref_direction, &outAxes.ref_direction);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
AcBr::ErrorStatus   GetNativeCurve (AcGeCurve3d*& nativeCurve, const AcGeCurve3d* acCurve)
    {
    AcBr::ErrorStatus   status = AcBr::eMissingGeometry;

    if (acCurve->type() == AcGe::kExternalCurve3d)
        {
        const AcGeExternalCurve3d*  externalCurve = (const AcGeExternalCurve3d*) acCurve;

        if (nullptr != externalCurve && externalCurve->isDefined())
            {
            /*---------------------------------------------------------------------------------------------
            A temporary workaround to prevent AcGeExternalCurve3d::isNativeCurve from crashing on degenerated
            curves as reproduced in TFS# 15528.  We will verify Adesk's fix upon availability of R2017 and 
            remove this code.
            ---------------------------------------------------------------------------------------------*/
            AcGeInterval    interval;
            externalCurve->getInterval (interval);

            double          length = interval.length();
            AcGe::EntityId  degenerateType;
            if (externalCurve->isDegenerate(degenerateType))
                return  AcBr::eMissingGeometry;

            if (externalCurve->isNativeCurve(nativeCurve))
                status = AcBr::eOk;
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
AcBr::ErrorStatus   GetNativeCurve (AcGeCurve3d*& acCurve, AcGeCurve3d*& nativeCurve, const AcBrEdge* acEdge)
    {
    AcBr::ErrorStatus   status = acEdge->getCurve (acCurve);
    if (AcBr::eOk != status)
        return  status;

    status = this->GetNativeCurve (nativeCurve, acCurve);

    if (AcBr::eOk != status)
        {
        if (NULL != acCurve)
            delete acCurve;
        if (NULL != nativeCurve)
            delete nativeCurve;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
AcBr::ErrorStatus   GetNativeSurface (AcGeSurface*& acSurface, AcGeSurface*& nativeSurface, const AcBrFace* acFace)
    {
    AcBr::ErrorStatus   status = acFace->getSurface (acSurface);
    if (AcBr::eOk != status)
        return  status;

    status = AcBr::eMissingGeometry;
    
    if (acSurface->type() == AcGe::kExternalBoundedSurface)
        {
        AcGeExternalSurface     externalSurface;
        ((AcGeExternalBoundedSurface*)acSurface)->getBaseSurface (externalSurface);

        if (externalSurface.isDefined() && externalSurface.isNativeSurface(nativeSurface) && nullptr != nativeSurface)
            status = AcBr::eOk;
        }

    if (AcBr::eOk != status)
        {
        if (NULL != acSurface)
            delete acSurface;
        if (NULL != nativeSurface)
            delete nativeSurface;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
int             AddSingularVertex (const AcBrFace& acFace)
    {
    /*-------------------------------------------------------------------------------------------------------
    For a singular loop-vertex case, Parasolid expects a singular vertex.  This method finds the surface from
    the input face, get its apex, create a Parasolid point and a vertex, add them into our list.  For a cone
    surface, its apex is obviously the needed singular point.  For other types of surfaces, since there is no
    apex point like a cone has, just get the parametric start point on the surface.
    --------------------------------------------------------------------------------------------------------*/
    AcGePoint3d         singularPoint;
    int                 vertexIndex = -1;
    AcGe::EntityId      surfaceType = AcGe::kObject;

    AcBr::ErrorStatus   es = acFace.getSurfaceType (surfaceType);
    if (AcBr::eOk != es)
        {
        DIAGNOSTIC_PRINTF ("Error getting surface type from which to create a singular vertex!\n");
        return  vertexIndex;
        }

    AcGeSurface*        acSurface = nullptr, *nativeSurface = nullptr;
    if (AcBr::eOk != (es = this->GetNativeSurface(acSurface, nativeSurface, &acFace)))
        {
        DIAGNOSTIC_PRINTF ("Error getting surface type from which to create a singular vertex!\n");
        return  vertexIndex;
        }

    switch (surfaceType)
        {
        case AcGe::kCone:
            {
            // extract the apex point of the cone
            AcGeCone*   acCone = static_cast <AcGeCone*> (nativeSurface);
            if (nullptr == acCone)
                es = AcBr::eInvalidObject;
            else
                singularPoint = acCone->apex ();
            break;
            }
        case AcGe::kTorus:
            {
            AcGeTorus*  acTorus = static_cast <AcGeTorus*> (nativeSurface);
            if (nullptr == acTorus)
                {
                es = AcBr::eInvalidObject;
                break;
                }

            // extract the surace point of {u0, v0}:
            AcGePoint2d     params;
            double          angles[2];
            acTorus->getAnglesInU (angles[0], angles[1]);
            params.x = angles[0];
            acTorus->getAnglesInV (angles[0], angles[1]);
            params.y = angles[0];

            singularPoint = acTorus->evalPoint (params);
            break;
            }
        case AcGe::kCylinder:
            {
            AcGeCylinder*   acCylinder = static_cast <AcGeCylinder*> (nativeSurface);
            if (nullptr == acCylinder)
                {
                es = AcBr::eInvalidObject;
                break;
                }

            // extract the surace point of {u0, 0.0}
            AcGePoint2d     params;
            acCylinder->getAngles (params.x, params.y);
            params.y = 0.0;

            singularPoint = acCylinder->evalPoint (params);
            break;
            }
        case AcGe::kSphere:
            {
            AcGeSphere*     acSphere = static_cast <AcGeSphere*> (nativeSurface);
            if (nullptr == acSphere)
                {
                es = AcBr::eInvalidObject;
                break;
                }

            // extract the surace point of {u0, v0}:
            AcGePoint2d     params;
            double          angles[2];
            acSphere->getAnglesInU (angles[0], angles[1]);
            params.x = angles[0];
            acSphere->getAnglesInV (angles[0], angles[1]);
            params.y = angles[0];

            singularPoint = acSphere->evalPoint (params);
            break;
            }
        case AcGe::kPlane:
            {
            // extract the origin of the plane
            AcGePlanarEnt*  plane = static_cast <AcGePlanarEnt*> (nativeSurface);
            if (nullptr == plane)
                es = AcBr::eInvalidObject;
            else
                singularPoint = plane->pointOnPlane ();
            break;
            }
        default:
            // try extracting the point of {0,0} on any other type of surface:
            singularPoint = nativeSurface->evalPoint (AcGePoint2d(0.0, 0.0));
            break;
        }

    if (AcBr::eOk == es)
        {
        if (m_needTransformation)
            singularPoint.transformBy (m_toParasolidMatrix);

        // create a Parasolid point
        PK_POINT_sf_t   pointParams;
        memcpy (&pointParams.position.coord[0], &singularPoint, sizeof pointParams.position.coord);

        PK_POINT_t      pointTag = -1;
        PK_ERROR_code_t pkError = PK_POINT_create (&pointParams, &pointTag);

#ifdef REALDWG_DIAGNOSTICS
        if (m_needTransformation)
            singularPoint.transformBy (m_toParasolidMatrix.inverse());
#endif

        if (PK_ERROR_no_errors == pkError)
            {
            // add the point
            m_pkGeometryTags.push_back ((int)pointTag);

            vertexIndex = (int)(m_pkClasses.size());

            // add a vertex class
            m_pkClasses.push_back (PK_CLASS_vertex);

            NOISYDEBUG_PRINTF ("\t\t\t\t\tSingular Point[%d@%d] at {%f,%f,%f}\n", (int)pointTag, vertexIndex, singularPoint.x, singularPoint.y, singularPoint.z);
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Error creating a singular PK_POINT at {%g,%g,%g}: %d!\n", singularPoint.x, singularPoint.y, singularPoint.z, pkError);
            vertexIndex = -2;
            }
        }

    if (nullptr != acSurface)
        delete acSurface;
    if (nullptr != nativeSurface)
        delete nativeSurface;

    return  vertexIndex;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateVertexGeometry (AcBrEntity* brEntity)
    {
    AcBrVertex*     acVertex = dynamic_cast <AcBrVertex*> (brEntity);
    AcGePoint3d     acPoint;
    if (NULL == acVertex || AcBr::eOk != acVertex->getPoint(acPoint))
        return  EntityError;

    if (m_needTransformation)
        acPoint.transformBy (m_toParasolidMatrix);

    PK_POINT_sf_t   pointParams;
    memcpy (&pointParams.position.coord[0], &acPoint, sizeof pointParams.position.coord);
    
    PK_POINT_t      pointTag = -1;
    PK_ERROR_code_t pkError = PK_POINT_create (&pointParams, &pointTag);
    
    if (PK_ERROR_no_errors == pkError)
        m_pkGeometryTags.push_back ((int)pointTag);
    else
        DIAGNOSTIC_PRINTF ("Error creating a PK_POINT at {%g,%g,%g}: %d!\n", acPoint.x, acPoint.y, acPoint.z, pkError);

#ifdef DEBUG_ADDING_PKGEOMETRY
    AcGePoint3d     checkPoint;
    acVertex->getPoint (checkPoint);
    printf ("\t\t\t\t\tPoint[%d@%d] at {%.10f,%.10f,%.10f}\n", (int)pointTag, m_pkClasses.size(), checkPoint.x, checkPoint.y, checkPoint.z);
#endif

    return  PK_ERROR_no_errors == pkError ? RealDwgSuccess : CantConvertBrep;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateEdgeLine (AcGeLinearEnt3d* acLinear)
    {
    PK_ERROR_code_t pkError = PK_ERROR_cant_get_curve;
    if (NULL == acLinear)
        return  pkError;

    AcGePoint3d     point = acLinear->pointOnLine ();
    AcGeVector3d    direction = acLinear->direction ();
    if (m_needTransformation)
        point.transformBy (m_toParasolidMatrix);

    PK_LINE_sf_t    lineParams;
    memcpy (&lineParams.basis_set.location.coord[0], &point, sizeof(lineParams.basis_set.location));
    memcpy (&lineParams.basis_set.axis.coord[0], &direction, sizeof(lineParams.basis_set.axis));

    PK_LINE_t       lineTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_LINE_create(&lineParams, &lineTag)))
        m_pkGeometryTags.push_back ((int)lineTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_LINE at {%g,%g,%g}: %d!\n", acLinear->pointOnLine().x, acLinear->pointOnLine().y, acLinear->pointOnLine().z, pkError);

#ifdef DEBUG_ADDING_PKGEOMETRY
    printf ("\t\t\t\tLine[%d@%d] at {%f,%f,%f}, x{%g,%g,%g}\n", (int)lineTag, m_pkClasses.size(), 
                acLinear->pointOnLine().x, acLinear->pointOnLine().y, acLinear->pointOnLine().z, direction.x, direction.y, direction.z);
#endif

    return  pkError;
    }

#ifdef  DEBUG_BAD_GEOMETRY
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DebugNurbCurve (AcGeSplineEnt3d* acSpline)
    {
    MSBsplineCurve      curve;
    curve.params.order = acSpline->degree() + 1;
    curve.rational = acSpline->isRational ();
    curve.params.closed = acSpline->isClosed ();
    curve.params.numPoles = acSpline->numControlPoints ();
    curve.params.numKnots = acSpline->numKnots ();
    curve.display.polygonDisplay = false;
    curve.display.curveDisplay = true;
    if (BSISUCCESS != curve.Allocate())
        return  false;

    AcGeNurbCurve3d*    acNurbCurve = nullptr;
    if (curve.rational)
        acNurbCurve = (AcGeNurbCurve3d*)acSpline;
    if (nullptr == acNurbCurve || acNurbCurve->numWeights() < curve.params.numPoles)
        curve.rational = false;

    for (int i = 0; i < curve.params.numPoles; i++)
        {
        RealDwgUtil::DPoint3dFromGePoint3d (curve.poles[i], acSpline->controlPointAt(i));
        if (curve.rational)
            curve.weights[i] = acNurbCurve->weightAt (i);
        }

    printf ("\t\t\t\t=>Spline %s fitted, w/continuities=", acSpline->hasFitData() ? "is" : "is not");
    
    for (int i = 0; i < curve.params.numKnots; i++)
        {
        curve.knots[i] = acSpline->knotAt (i);
        printf ("%d ", acSpline->continuityAtKnot(i));
        }

    printf ("\n");
    if (nullptr != acNurbCurve)
        {
        AcGeDoubleArray     discs;
        if (acNurbCurve->getParamsOfG1Discontinuity(discs))
            {
            printf ("\t\t\t\t=>G1 discontinuities=");
            for (int i = 0; i < discs.length(); i++)
                printf ("%g ", discs[i]);
            printf ("\n");
            }

        discs.removeAll ();
        if (acNurbCurve->getParamsOfC1Discontinuity(discs))
            {
            printf ("\t\t\t\t=>C1 discontinuities=");
            for (int i = 0; i < discs.length(); i++)
                printf ("%g ", discs[i]);
            printf ("\n");
            }
        }

    m_toDgnContext->GetTransformToDGN().Multiply (curve.poles, curve.poles, curve.params.numPoles);
    if (curve.rational)
        bsputil_weightPoles (curve.poles, curve.poles, curve.weights, curve.params.numPoles);

    EditElementHandle   eeh;
    if (BSISUCCESS == BSplineCurveHandler::CreateBSplineCurveElement(eeh, nullptr, curve, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))
        {
        UInt32          color = 1;
        UInt32          weight = 2;

        m_toDgnContext->SetElementSymbology (eeh, LEVEL_DEFAULT_LEVEL_ID, &color, nullptr, &weight, nullptr, nullptr, 1.0, 0.0, DgnElementClass::Primary);

        eeh.AddToModel ();

        curve.ReleaseMem ();

        return  true;
        }

    return  false;
    }
#endif  // DEBUG_BAD_GEOMETRY

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CheckBSplineCurve (PK_BCURVE_sf_t const& bcurveParams, PK_BCURVE_t& bcurveTag)
    {
    // fix Parasolid's G1 discontinuity potentially resulted from bad ASM data.
    int                 numDiscs = 0;
    double*             discsAt = nullptr;
    PK_INTERVAL_t       range;

    PK_ERROR_code_t     pkError = PK_BCURVE_find_g1_discontinuity (bcurveTag, &numDiscs, &discsAt);

    if (PK_ERROR_no_errors == pkError && numDiscs > 0 && PK_ERROR_no_errors == (pkError = PK_CURVE_ask_interval(bcurveTag, &range)))
        {
        PK_BCURVE_t                     newCurveTag = -1;
        PK_BCURVE_fitted_fault_t        fitResult;
        PK_BCURVE_create_fitted_o_t     fitOptions;
        PK_BCURVE_create_fitted_o_m (fitOptions);

        fitOptions.curve.type = PK_CURVE_general_curve_c;
        fitOptions.curve.curve.parasolid_curve = bcurveTag;
        fitOptions.range = range;
        
        pkError = PK_BCURVE_create_fitted (&fitOptions, &newCurveTag, &fitResult);

        if (PK_ERROR_no_errors == pkError && PK_BCURVE_fitted_success_c == fitResult.status && PK_ENTITY_null != newCurveTag)
            {
            // fitting succeeded - replace the old curve
            PK_ENTITY_delete (1, &bcurveTag);
            bcurveTag = newCurveTag;
            }
        else if (newCurveTag != PK_ENTITY_null)
            {
            // clean up on fitting failure
            PK_ENTITY_delete (1, &newCurveTag);
            pkError = PK_ERROR_cant_make_bspline;
            }
        }

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateEdgeBCurve (AcGeSplineEnt3d* acSpline)
    {
    PK_ERROR_code_t pkError = PK_ERROR_cant_get_curve;
    if (NULL == acSpline)
        return  pkError;

#ifdef  DEBUG_BAD_GEOMETRY
    this->DebugNurbCurve (acSpline);
#endif

    PK_BCURVE_sf_t  bcurveParams;

    bcurveParams.degree = acSpline->degree ();
    bcurveParams.n_vertices = acSpline->numControlPoints ();
    bcurveParams.is_rational = acSpline->isRational ();
    bcurveParams.is_closed = acSpline->isClosed ();

    // validate rational spline curve
    AcGeNurbCurve3d*    acNurbCurve = NULL;
    if (bcurveParams.is_rational)
        acNurbCurve = (AcGeNurbCurve3d*)acSpline;
    if (nullptr == acNurbCurve || acNurbCurve->numWeights() < bcurveParams.n_vertices)
        bcurveParams.is_rational = PK_LOGICAL_false;

    if (nullptr != acNurbCurve)
        {
        // bail out on an ASM, as opposed to Parasolid, spline discountinuity - TFS 153151:
        AcGeDoubleArray     discs;
        if (acNurbCurve->getParamsOfG1Discontinuity(discs))
            {
            DIAGNOSTIC_PRINTF ("NURB curve error: a G1 discontinuity found at %g!\n", discs.at(0));
            return  PK_ERROR_discontinuous_curve;
            }
        if (acNurbCurve->getParamsOfC1Discontinuity(discs))
            {
            DIAGNOSTIC_PRINTF ("NURB curve error: a C1 discontinuity found at %g!\n", discs.at(0));
            return  PK_ERROR_discontinuous_curve;
            }
        }

    bcurveParams.vertex_dim = bcurveParams.is_rational ? 4 : 3;
    bcurveParams.knot_type = PK_knot_unset_c;
    bcurveParams.form = PK_BCURVE_form_unset_c;
    bcurveParams.self_intersecting = PK_self_intersect_unset_c;

    /*--------------------------------------------------------------------------------------------
    One of Parasolids restrictions on a BCURVE is that, if a B-curve is to be attached to an edge:
        -It must, at the minimum, have G1 continuity.
        -If it is closed, it must have periodic parameterization.

    So we force a closed bcurve to be periodic.  This may push the body to fail but at least won't
    create bad body with horrible rendering like an example of a surface from a cylinder shell cut
    by another perpendicular cylinder.
    --------------------------------------------------------------------------------------------*/
    bcurveParams.is_periodic = bcurveParams.is_closed;

    // Parasolid expects distinct knots
    AcGeDoubleArray distinctKnots;
    AcGeKnotVector  knots = acSpline->knots ();

    knots.getDistinctKnots (distinctKnots);

    bcurveParams.n_knots = distinctKnots.length ();

    bcurveParams.vertex = (double*) malloc (bcurveParams.n_vertices * bcurveParams.vertex_dim * sizeof(double));
    if (NULL == bcurveParams.vertex)
        return  PK_ERROR_memory_full;

    bcurveParams.knot = (double*) malloc (bcurveParams.n_knots * sizeof(double));
    if (NULL == bcurveParams.knot)
        {
        free (bcurveParams.vertex);
        return  PK_ERROR_memory_full;
        }
    
    bcurveParams.knot_mult = (int*) malloc (bcurveParams.n_knots * sizeof(int));
    if (NULL == bcurveParams.knot_mult)
        {
        free (bcurveParams.vertex);
        free (bcurveParams.knot);
        return  PK_ERROR_memory_full;
        }

    // copy control points
    for (int i = 0; i < bcurveParams.n_vertices; i++)
        {
        UInt32      pkIndex = i * bcurveParams.vertex_dim;
        AcGePoint3d pole = acSpline->controlPointAt (i);

        if (m_needTransformation)
            pole.transformBy (m_toParasolidMatrix);

        // copy control point at the index
        memcpy (&bcurveParams.vertex[pkIndex], &pole, 3 * sizeof(double));
        // copy weight if rational
        if (4 == bcurveParams.vertex_dim)
            {
            double  weight = bcurveParams.vertex[pkIndex + 3] = acNurbCurve->weightAt (i);

            // Parasolid expects homogenous/weighted poles
            bcurveParams.vertex[pkIndex + 0] *= weight;
            bcurveParams.vertex[pkIndex + 1] *= weight;
            bcurveParams.vertex[pkIndex + 2] *= weight;
            }
        }

    // copy distinct knots and knot multiplicity
    for (int i = 0; i < bcurveParams.n_knots; i++)
        {
        bcurveParams.knot[i] = distinctKnots[i];
        bcurveParams.knot_mult[i] = knots.multiplicityAt (distinctKnots[i]);
        }

    PK_BCURVE_t     bcurveTag = -1, tagSaved = -1;
    if (PK_ERROR_no_errors == (pkError = PK_BCURVE_create(&bcurveParams, &bcurveTag)) && PK_ENTITY_null != (tagSaved = bcurveTag) &&
        PK_ERROR_no_errors == (pkError = this->CheckBSplineCurve(bcurveParams, bcurveTag)))
        m_pkGeometryTags.push_back ((int)bcurveTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_BCURVE(%d %d): %d!\n", bcurveParams.degree, bcurveParams.n_vertices, pkError);

#ifdef DEBUG_ADDING_PKGEOMETRY
    if (PK_ERROR_no_errors == pkError)
        {
        int     bcurveIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\t\tBCurve[%d@%d]: degree=%d, nPoles=%d, nKnots=%d, %s rational\n", (int)bcurveTag, bcurveIndex,
                bcurveParams.degree, bcurveParams.n_vertices, bcurveParams.n_knots, bcurveParams.is_rational ? "is" : "not");

        if (tagSaved != bcurveTag)
            {
            PK_BCURVE_sf_t  newParams;
            printf ("\t\t\t\t=>Replaced by[%d]", bcurveTag);
            if (PK_ERROR_no_errors == PK_BCURVE_ask(bcurveTag, &newParams))
                printf (": degree=%d, nPoles=%d, nKnots=%d, %s rational", newParams.degree, newParams.n_vertices, newParams.n_knots, newParams.is_rational ? "is" : "not");
            printf ("\n");
            }
        }
#endif

    free (bcurveParams.vertex);
    free (bcurveParams.knot);
    free (bcurveParams.knot_mult);
    
    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateEdgeCircle (AcGeCircArc3d* acCircArc)
    {
    PK_ERROR_code_t pkError = PK_ERROR_cant_get_curve;
    if (NULL == acCircArc)
        return  pkError;

    PK_CIRCLE_sf_t  circleParams;
    this->SetBasisAxes (circleParams.basis_set, acCircArc->center(), acCircArc->normal(), acCircArc->refVec());

    circleParams.radius = acCircArc->radius ();
    if (m_needTransformation)
        circleParams.radius *= m_toParasolidScale;

    PK_CIRCLE_t     circleTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_CIRCLE_create(&circleParams, &circleTag)))
        m_pkGeometryTags.push_back ((int)circleTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_CIRCLE at: %d!\n", acCircArc->center().x, acCircArc->center().y, acCircArc->center().z, pkError);

#ifdef DEBUG_ADDING_PKGEOMETRY
    if (PK_ERROR_no_errors == pkError)
        {
        int         circleIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\t\tCircle[%d@%d]: R=%g, ", (int)circleTag, circleIndex, acCircArc->radius());

        Transform   untransform;
        DiagnosticPrintAxis2 (circleParams.basis_set, &RealDwgUtil::TransformFromGeMatrix3d(untransform, m_toParasolidMatrix.inverse()), true);
        }
#endif

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetPiecewiseInfoFromCurves (PK_BCURVE_piecewise_sf_t& piecewise, AcGeVoidPointerArray& acCurves)
    {
    piecewise.n_segments = acCurves.length ();
    if (piecewise.n_segments < 1)
        return  false;

    piecewise.degree = -1;
    piecewise.is_rational = false;
    piecewise.rep = PK_piecewise_rep_polynomial_c;

    for (int i = 0; i < piecewise.n_segments; i++)
        {
        AcGeCurve3d*    acCurve = (AcGeCurve3d*) acCurves[i];
        if (NULL == acCurve)
            return  false;

        AcGeCurve3d*    nativeCurve = NULL;
        if (AcBr::eOk != this->GetNativeCurve(nativeCurve, acCurve))
            {
            piecewise.degree = -1;
            break;
            }

        AcGeExternalCurve3d*    extCurve = (AcGeExternalCurve3d*) nativeCurve;
        if (extCurve->isLine() || extCurve->isLineSeg())
            {
            if (piecewise.degree < 1)
                piecewise.degree = 1;
            }
        else if (extCurve->isCircArc() || extCurve->isEllipArc())
            {
            if (piecewise.degree < 2)
                piecewise.degree = 2;
            }
        else if (extCurve->isNurbCurve())
            {
            AcGeSplineEnt3d*    acSpline = (AcGeSplineEnt3d*)extCurve;
            if (NULL != acSpline)
                {
                if (acSpline->degree() > piecewise.degree)
                    piecewise.degree = acSpline->degree ();
                piecewise.is_rational = acSpline->isRational ();
                }
            }

        delete nativeCurve;

        if (piecewise.degree < 1)
            return  false;
        }

    if (piecewise.degree < 1)
        return  false;

    piecewise.dim = piecewise.is_rational ? 4 : 3;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            FreeCurveList (AcGeVoidPointerArray& acCurves)
    {
    for (int i = 0; i < acCurves.length(); i++)
        {
        if (NULL != acCurves[i])
            delete acCurves[i];
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateEdgePWCurve (AcGeCompositeCurve3d* acCompositeCurve)
    {
    PK_ERROR_code_t pkError = PK_ERROR_cant_get_curve;
    if (NULL == acCompositeCurve)
        return  pkError;

    AcGeVoidPointerArray    acCurves;
    acCompositeCurve->getCurveList (acCurves);

    // scan curve list for basic piecewise info - support lineaer piecewise for now
    PK_BCURVE_piecewise_sf_t    piecewise;
    if (!this->GetPiecewiseInfoFromCurves(piecewise, acCurves) || piecewise.degree > 1)
        {
        this->FreeCurveList (acCurves);
        return  pkError;
        }

    // allocate data array for piecewise bcurve
    piecewise.coeffs = (double*) malloc (piecewise.n_segments * (piecewise.degree + 1) * piecewise.dim * sizeof(double));
    if (NULL != piecewise.coeffs)
        {
        this->FreeCurveList (acCurves);
        return  PK_ERROR_memory_full;
        }

    pkError = PK_ERROR_no_errors;

    int     nSegments = acCurves.length ();
    for (int i = 0; i < nSegments; i++)
        {
        AcGeCurve3d*    nativeCurve = NULL;
        if (AcBr::eOk != this->GetNativeCurve(nativeCurve, (AcGeCurve3d*)acCurves[i]))
            break;  // should not happen
        
        AcGeExternalCurve3d*    extCurve = (AcGeExternalCurve3d*) nativeCurve;
        if (extCurve->isLine() || extCurve->isLineSeg())
            {
            AcGePoint3d     point;
            if (extCurve->hasStartPoint(point))
                {
                if (m_needTransformation)
                    point.transformBy (m_toParasolidMatrix);

                // copy the point at current index
                memcpy (&piecewise.coeffs[i], &point, 3 * sizeof(double));
                if (piecewise.is_rational)
                    piecewise.coeffs[i + 3] = 1.0;
                }
            else if (extCurve->hasEndPoint(point) && i < (nSegments - 1))
                {
                int         j = piecewise.is_rational ? i + 4 : i + 3;

                if (m_needTransformation)
                    point.transformBy (m_toParasolidMatrix);

                // copy the point at current index
                memcpy (&piecewise.coeffs[i], &point, 3 * sizeof(double));
                if (piecewise.is_rational)
                    piecewise.coeffs[i + 4] = 1.0;
                }
            }
        else
            {
            BeAssert (false && L"Nonlineaer piecewise curve not yet supported!!");
            pkError = PK_ERROR_unsupported_operation;
            break;
            }
        }

     PK_BCURVE_t    bcurveTag = -1;
    if (PK_ERROR_no_errors == pkError)
        {
        if (PK_ERROR_no_errors == (pkError = PK_BCURVE_create_piecewise(&piecewise, &bcurveTag)))
            m_pkGeometryTags.push_back ((int)bcurveTag);
        }
    
    this->FreeCurveList (acCurves);

    if (PK_ERROR_no_errors != pkError)
        DIAGNOSTIC_PRINTF ("Parasolid error creating a piecewise bcurve: %d segments, %d degrees, rep= %d!\n", piecewise.n_segments, piecewise.degree, piecewise.rep);

#ifdef DEBUG_ADDING_PKGEOMETRY
    if (PK_ERROR_no_errors == pkError)
        {
        int         bcurveIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\t\tPiecewiseCurve[%d@%d]: %d segments, %d degrees, rep= %d\n", (int)bcurveTag, bcurveIndex,
                    piecewise.n_segments, piecewise.degree, piecewise.rep);
        }
#endif

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateEdgeEllipse (AcGeEllipArc3d* acEllipArc)
    {
    PK_ERROR_code_t pkError = PK_ERROR_cant_get_curve;
    if (NULL == acEllipArc)
        return  pkError;

    PK_ELLIPSE_sf_t ellipseParams;
    this->SetBasisAxes (ellipseParams.basis_set, acEllipArc->center(), acEllipArc->normal(), acEllipArc->majorAxis());

    ellipseParams.R1 = acEllipArc->majorRadius ();
    ellipseParams.R2 = acEllipArc->minorRadius ();
    if (m_needTransformation)
        {
        ellipseParams.R1 *= m_toParasolidScale;
        ellipseParams.R2 *= m_toParasolidScale;
        }

    PK_ELLIPSE_t    ellipseTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_ELLIPSE_create(&ellipseParams, &ellipseTag)))
        m_pkGeometryTags.push_back ((int)ellipseTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_ELLIPSE at: %d!\n", acEllipArc->center().x, acEllipArc->center().y, acEllipArc->center().z, pkError);

#ifdef DEBUG_ADDING_PKGEOMETRY
    if (PK_ERROR_no_errors == pkError)
        {
        int             ellipseIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\t\tEllipse[%d@%d]: R=%g, %r=%g, ", (int)ellipseTag, ellipseIndex, acEllipArc->majorRadius(), acEllipArc->minorRadius());

        Transform       untransform;
        DiagnosticPrintAxis2 (ellipseParams.basis_set, &RealDwgUtil::TransformFromGeMatrix3d(untransform, m_toParasolidMatrix.inverse()), true);
        }
#endif

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateEdgeGeometry (AcBrEntity* brEntity)
    {
    AcBrEdge*       acEdge = dynamic_cast <AcBrEdge*> (brEntity);
    AcGe::EntityId  curveType;
    if (NULL == acEdge || AcBr::eOk != acEdge->getCurveType(curveType))
        return  EntityError;

    // extract native curve
    AcGeCurve3d     *acCurve = NULL, *nativeCurve = NULL;
    if (AcBr::eOk != this->GetNativeCurve(acCurve, nativeCurve, acEdge))
        return  EntityError;

    // check & invert the curve if its orientation does not match what is returned from AcBrEdge::getOrientToCurve:
    this->FixCurveOrientation (nativeCurve, acEdge);

    PK_ERROR_code_t pkError = PK_ERROR_missing_geom;

    switch (curveType)
        {
        case AcGe::kLinearEnt3d:
        case AcGe::kLine3d:
        case AcGe::kLineSeg3d:
            pkError = this->CreateEdgeLine ((AcGeLinearEnt3d*)nativeCurve);
            break;
        case AcGe::kCircArc3d:
            pkError = this->CreateEdgeCircle ((AcGeCircArc3d*)nativeCurve);
            break;
        case AcGe::kEllipArc3d:
            pkError = this->CreateEdgeEllipse ((AcGeEllipArc3d*)nativeCurve);
            break;
        case AcGe::kSplineEnt3d:
        case AcGe::kCubicSplineCurve3d:
        case AcGe::kDSpline3d:
        case AcGe::kNurbCurve3d:
        case AcGe::kAugPolyline3d:
        case AcGe::kPolyline3d:
            pkError = this->CreateEdgeBCurve ((AcGeSplineEnt3d*)nativeCurve);
            break;
        case AcGe::kCompositeCrv3d:
            pkError = this->CreateEdgePWCurve ((AcGeCompositeCurve3d*)nativeCurve);
            break;
        default:
            DIAGNOSTIC_PRINTF ("Missing curve type %d for a Brep edge!\n", curveType);
            BeAssert (false && L"Unsupported curve type for a Brep edge!!");
        }

    delete acCurve;
    delete nativeCurve;

    return  PK_ERROR_no_errors == pkError ? RealDwgSuccess : CantConvertBrep;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceCone (AcGeCone* acCone, bool* isOriented = nullptr)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acCone)
        return  pkError;

    PK_CONE_sf_t        coneParams;
    coneParams.semi_angle = acCone->halfAngle ();
    coneParams.radius = acCone->baseRadius();

    // AcGeCone::axisOfSymmetry does not appear to have a consistent direction - find it by shooting a vector from the apex to the base center:
    AcGeVector3d        axis = acCone->baseCenter().asVector() - acCone->apex().asVector();
    axis.normalize ();

    // handle error
    if (axis.isZeroLength())
        axis = acCone->axisOfSymmetry ();

    if (m_needTransformation)
        coneParams.radius *= m_toParasolidScale;

    this->SetBasisAxes (coneParams.basis_set, acCone->baseCenter(), axis, acCone->refAxis());

    // fix face-surface orientation flag here as AcDbFace has no set method for that.
    if (nullptr != isOriented && !acCone->isOuterNormal())
        *isOriented = !*isOriented;
        
    PK_CONE_t           coneTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_CONE_create(&coneParams, &coneTag)))
        m_pkGeometryTags.push_back ((int)coneTag);

    m_hasAnalyticSurface = true;

    if (PK_ERROR_no_errors == pkError)
        NOISYDEBUG_PRINTF ("\t\t\tCone[%d@%d] at {%f,%f,%f}, R= %g, %cz{%g,%g,%g}\n", (int)coneTag, m_pkGeometryTags.size()-1,
                           acCone->baseCenter().x, acCone->baseCenter().y, acCone->baseCenter().z, acCone->baseRadius(),
                           acCone->isNormalReversed() ? '-' : '+',
                           coneParams.basis_set.axis.coord[0], coneParams.basis_set.axis.coord[1], coneParams.basis_set.axis.coord[2]);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_CONE for Brep face: %d!\n", pkError);

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceEllipticCone (AcGeSurface* acSurface)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acSurface)
        return  pkError;

    // evaluate 3 points on the elliptic cone base
    AcGePoint3d         majorPoint0 = acSurface->evalPoint (AcGePoint2d(0.0, 0.0));
    AcGePoint3d         majorPoint1 = acSurface->evalPoint (AcGePoint2d(0.0, Angle::Pi()));
    AcGePoint3d         minorPoint = acSurface->evalPoint (AcGePoint2d(0.0, Angle::PiOver2()));
    // evaluate the apex point of the cone
    AcGePoint3d         apexPoint = acSurface->evalPoint (AcGePoint2d(1.0, 0.0));

    // the center point of the base is the mid-point of the two major points:
    AcGePoint3d         center = 0.5 * (majorPoint0 + majorPoint1.asVector());

    // major and minor axes about the center point:
    AcGeVector3d        majorAxis = majorPoint0 - center;
    AcGeVector3d        minorAxis = minorPoint - center;

    // get the normal vector from major minor axes
    AcGeVector3d        zAxis = majorAxis.crossProduct(minorAxis).normalize ();

    PK_CONE_sf_t        coneParams;
    this->SetBasisAxes (coneParams.basis_set, center, zAxis, majorAxis);

    // Parasolid does not have an elliptical cone, so let's hope it is a circular cone:
    coneParams.radius = majorAxis.length ();
    if (m_needTransformation)
        coneParams.radius *= m_toParasolidScale;

    if (fabs(coneParams.radius - minorAxis.length()) > TOLERANCE_CircularRatio)
        DIAGNOSTIC_PRINTF ("ASM's elliptical cone(R= %g, r= %g) has been converted to a circular Parasolid cone!\n", majorAxis.length(), minorAxis.length());

    coneParams.semi_angle = Angle::Pi() - majorAxis.angleTo (apexPoint - majorPoint0);

    PK_CONE_t           coneTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_CONE_create(&coneParams, &coneTag)))
        m_pkGeometryTags.push_back ((int)coneTag);

    m_hasAnalyticSurface = true;

    if (PK_ERROR_no_errors == pkError)
        NOISYDEBUG_PRINTF ("\t\t\tEllipCone[%d@%d] at {%f,%f,%f}, R= %g, r= %g\n", (int)coneTag, m_pkGeometryTags.size()-1, center.x, center.y, center.z, coneParams.radius, minorAxis.length());
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_CONE(elliptic cone) for Brep face: %d!\n", pkError);

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFacePlane (AcGePlane* acPlane)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acPlane)
        return  pkError;

    AcGePoint3d         origin;
    AcGeVector3d        xAxis, yAxis;
    acPlane->getCoordSystem (origin, xAxis, yAxis);

    // AcGePlaneEnt3d::normal is supposed to have taken account of AcGeSurface::isNormalReversed:
    AcGeVector3d        zAxis = acPlane->normal ();
    zAxis.normalize ();

    PK_PLANE_sf_t       planeParams;
    this->SetBasisAxes (planeParams.basis_set, origin, zAxis, xAxis);

    PK_PLANE_t          planeTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_PLANE_create(&planeParams, &planeTag)))
        m_pkGeometryTags.push_back ((int)planeTag);

    if (PK_ERROR_no_errors == pkError)
        NOISYDEBUG_PRINTF ("\t\t\tPlane[%d@%d] at {%f,%f,%f}, x{%g,%g,%g}, y{%g,%g,%g}, %cz{%g,%g,%g}\n", (int)planeTag, m_pkGeometryTags.size()-1, origin.x, origin.y, origin.z, xAxis.x, xAxis.y, xAxis.z, yAxis.x, yAxis.y, yAxis.z, acPlane->isNormalReversed() ? '-':'+', zAxis.x, zAxis.y, zAxis.z);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_PLANE for Brep face: %d!\n", pkError);

    return  pkError;
    }
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t FixOrthogonalAxes (PK_AXIS2_sf_t& axes)
    {
    PK_VECTOR1_t    norm;
    PK_ERROR_code_t pkError = PK_VECTOR_perpendicular (axes.axis, axes.ref_direction, &norm);
    if (PK_ERROR_no_errors != pkError)
        return  pkError;

    pkError = PK_VECTOR_perpendicular (axes.axis, norm, &axes.ref_direction);

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceTorus (AcGeTorus* acTorus, bool* isOriented = nullptr)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acTorus)
        return  pkError;

    PK_TORUS_sf_t       torusParams;
    this->SetBasisAxes (torusParams.basis_set, acTorus->center(), acTorus->axisOfSymmetry(), acTorus->refAxis());

    torusParams.major_radius = acTorus->majorRadius ();
    torusParams.minor_radius = acTorus->minorRadius ();
    if (m_needTransformation && fabs(m_toParasolidScale - 1.0) > TOLERANCE_ZeroScale)
        {
        torusParams.major_radius *= m_toParasolidScale;
        torusParams.minor_radius *= m_toParasolidScale;
        }
    
    // fix face-surface orientation flag here as AcDbFace has no set method for that.
    if (nullptr != isOriented && !acTorus->isOuterNormal())
        *isOriented = !*isOriented;

    PK_TORUS_t          torusTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_TORUS_create(&torusParams, &torusTag)))
        m_pkGeometryTags.push_back ((int)torusTag);

    if (PK_ERROR_vectors_not_orthogonal == pkError)
        {
        // the axes may have fallen out of Parasold's angular precision - fix the axes and try it again:
        if (PK_ERROR_no_errors == (pkError = this->FixOrthogonalAxes(torusParams.basis_set)) &&
            PK_ERROR_no_errors == (pkError = PK_TORUS_create(&torusParams, &torusTag)))
            m_pkGeometryTags.push_back ((int)torusTag);
        }

    m_hasAnalyticSurface = true;

    if (PK_ERROR_no_errors != pkError)
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_TORUS for Brep face: %d!\n", pkError);

#ifdef REALDWG_NOISY_DEBUG
    if (PK_ERROR_no_errors == pkError)
        {
        int         torusIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\tTorus[%d@%d]: R=%g, r=%g, ", (int)torusTag, torusIndex, acTorus->majorRadius(), acTorus->minorRadius());

        Transform   untransform;
        DiagnosticPrintAxis2 (torusParams.basis_set, &RealDwgUtil::TransformFromGeMatrix3d(untransform, m_toParasolidMatrix.inverse()), false);
        printf ("%cnormal\n", acTorus->isNormalReversed() ? '-' : '+');
        }
#endif

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceCylinder (AcGeCylinder* acCylinder, bool* isOriented = nullptr)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acCylinder)
        return  pkError;

    PK_CYL_sf_t         cylinderParams;
    this->SetBasisAxes (cylinderParams.basis_set, acCylinder->origin(), acCylinder->axisOfSymmetry(), acCylinder->refAxis());

    cylinderParams.radius = acCylinder->radius ();
    if (m_needTransformation)
        cylinderParams.radius *= m_toParasolidScale;

    // fix face-surface orientation flag here as AcDbFace has no set method for that.
    if (nullptr != isOriented && !acCylinder->isOuterNormal())
        *isOriented = !*isOriented;

    PK_CYL_t            cylinderTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_CYL_create(&cylinderParams, &cylinderTag)))
        m_pkGeometryTags.push_back ((int)cylinderTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_CYL for Brep face: %d!\n", pkError);

    m_hasAnalyticSurface = true;

#ifdef REALDWG_NOISY_DEBUG
    if (PK_ERROR_no_errors == pkError)
        {
        int         cylinderIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\tCylinder[%d@%d] at {%f,%f,%f}, R= %g, %cz{%g,%g,%g}\n", (int)cylinderTag, cylinderIndex,
                    acCylinder->origin().x, acCylinder->origin().y, acCylinder->origin().z, acCylinder->radius(),
                    acCylinder->isNormalReversed() ? '-' : '+',
                    cylinderParams.basis_set.axis.coord[0], cylinderParams.basis_set.axis.coord[1], cylinderParams.basis_set.axis.coord[2]);
        }
#endif
    
    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceEllipticCylinder (AcGeSurface* acSurface)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acSurface)
        return  pkError;

    // evaluate 3 points on the cylinder base
    AcGePoint3d         majorPoint0 = acSurface->evalPoint (AcGePoint2d(0.0, 0.0));
    AcGePoint3d         majorPoint1 = acSurface->evalPoint (AcGePoint2d(0.0, Angle::Pi()));
    AcGePoint3d         minorPoint = acSurface->evalPoint (AcGePoint2d(0.0, Angle::PiOver2()));

    // the center point of the bsae is the mid-point of the two major points:
    AcGePoint3d         center = 0.5 * (majorPoint0 + majorPoint1.asVector());

    // major and minor axes about the center point:
    AcGeVector3d        majorAxis = majorPoint0 - center;
    AcGeVector3d        minorAxis = minorPoint - center;

    // get the normal vector from major minor axes
    AcGeVector3d        zAxis = majorAxis.crossProduct(minorAxis).normalize ();

    PK_CYL_sf_t         cylinderParams;
    this->SetBasisAxes (cylinderParams.basis_set, center, zAxis, majorAxis);

    // Parasolid does not have an elliptical cylinder, so let's hope it is a circular cylinder:
    cylinderParams.radius = majorAxis.length ();
    if (m_needTransformation)
        cylinderParams.radius *= m_toParasolidScale;

    if (fabs(cylinderParams.radius - minorAxis.length()) > TOLERANCE_CircularRatio)
        DIAGNOSTIC_PRINTF ("ASM's elliptical cylinder(R= %g, r= %g) has been converted to a circular Parasolid cylinder!\n", majorAxis.length(), minorAxis.length());

    PK_CYL_t            cylinderTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_CYL_create(&cylinderParams, &cylinderTag)))
        m_pkGeometryTags.push_back ((int)cylinderTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_CYL(elliptic cylinder) for Brep face: %d!\n", pkError);

    m_hasAnalyticSurface = true;

#ifdef REALDWG_NOISY_DEBUG
    if (PK_ERROR_no_errors == pkError)
        {
        int         cylinderIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\tEllipCylinder[%d@%d] at {%f,%f,%f}, R= %g, r= %g, %cz{%g,%g,%g}\n", (int)cylinderTag, cylinderIndex,
                    center.x, center.y, center.z, majorAxis.length(), minorAxis.length(), acSurface->isNormalReversed() ? '-' : '+',
                    cylinderParams.basis_set.axis.coord[0], cylinderParams.basis_set.axis.coord[1], cylinderParams.basis_set.axis.coord[2]);
        }
#endif
    
    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceSphere (AcGeSphere* acSphere, bool* isOriented = nullptr)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acSphere)
        return  pkError;

    PK_SPHERE_sf_t      sphereParams;
    this->SetBasisAxes (sphereParams.basis_set, acSphere->center(), acSphere->northAxis(), acSphere->refAxis());

    sphereParams.radius = acSphere->radius ();
    if (m_needTransformation)
        sphereParams.radius *= m_toParasolidScale;

    // fix face-surface orientation flag here as AcDbFace has no set method for that.
    if (nullptr != isOriented && !acSphere->isOuterNormal())
        *isOriented = !*isOriented;

    PK_SPHERE_t         sphereTag = -1;
    if (PK_ERROR_no_errors == (pkError = PK_SPHERE_create(&sphereParams, &sphereTag)))
        m_pkGeometryTags.push_back ((int)sphereTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a PK_SPHERE for Brep face: %d!\n", pkError);

    m_hasAnalyticSurface = true;

#ifdef REALDWG_NOISY_DEBUG
    if (PK_ERROR_no_errors == pkError)
        {
        int         sphereIndex = (int)m_pkGeometryTags.size() - 1;
        printf ("\t\t\tSphere[%d@%d] at {%f,%f,%f}, R= %g, %cz{%g,%g,%g}\n", (int)sphereTag, sphereIndex,
                    acSphere->center().x, acSphere->center().y, acSphere->center().z, acSphere->radius(),
                    acSphere->isNormalReversed() ? '-' : '+',
                    sphereParams.basis_set.axis.coord[0], sphereParams.basis_set.axis.coord[1], sphereParams.basis_set.axis.coord[2]);
        }
#endif

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t AllocateBSurfaceMemory (PK_BSURF_sf_t& bsurfaceParams, size_t numPoints)
    {
    bsurfaceParams.vertex = (double*) malloc (numPoints * bsurfaceParams.vertex_dim * sizeof(double));

    bsurfaceParams.u_knot = NULL;
    bsurfaceParams.v_knot = NULL;
    bsurfaceParams.u_knot_mult = NULL;
    bsurfaceParams.v_knot_mult = NULL;
    
    if (NULL != bsurfaceParams.vertex)
        {
        bsurfaceParams.u_knot = (double*) malloc (bsurfaceParams.n_u_knots * sizeof(double));
        bsurfaceParams.u_knot_mult = (int*) malloc (bsurfaceParams.n_u_knots * sizeof(int));

        if (NULL != bsurfaceParams.u_knot && NULL != bsurfaceParams.u_knot_mult)
            {
            bsurfaceParams.v_knot = (double*) malloc (bsurfaceParams.n_v_knots * sizeof(double));
            bsurfaceParams.v_knot_mult = (int*) malloc (bsurfaceParams.n_v_knots * sizeof(int));

            if (NULL != bsurfaceParams.v_knot && NULL != bsurfaceParams.v_knot_mult)
                return  PK_ERROR_no_errors;
            }
        }

    return this->FreeBSurfaceMemory (bsurfaceParams);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t FreeBSurfaceMemory (PK_BSURF_sf_t& bsurfaceParams)
    {
    if (NULL != bsurfaceParams.vertex);
        free (bsurfaceParams.vertex);
    if (NULL != bsurfaceParams.u_knot)
        free (bsurfaceParams.u_knot);
    if (NULL != bsurfaceParams.u_knot_mult)
        free (bsurfaceParams.u_knot_mult);
    if (NULL != bsurfaceParams.v_knot)
        free (bsurfaceParams.v_knot);
    if (NULL != bsurfaceParams.v_knot_mult)
        free (bsurfaceParams.v_knot_mult);
    
    return  PK_ERROR_memory_full;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CheckBSplineSurface (PK_BSURF_sf_t const& bsurfaceParams, PK_BSURF_t& bsurfaceTag)
    {
    /*---------------------------------------------------------------------------------------------------------------
    A geometric discontinuity on a Bspline surface can cause a failure later when attaching it to a face. Turning off 
    session's check for continuity seems to get by this specific failure, but may cause other issues elsewhere.  Both 
    PK_SURF_make_bsurf_2 and making a sheet body may return us multiple surfaces which would require us to re-build 
    topology which is not desirable for an import job.  The best solution we have found so far is to create a new BSpline 
    surface by fitting the existing points of an existing surface.  It may change the parameters and perhaps even the 
    shape of the surface, but that is less of a concern we'd have compared to a complete failure of importing the body.
    ----------------------------------------------------------------------------------------------------------------*/
    int                             nDisc = 0;
    double*                         disconts = nullptr;
    PK_PARAM_direction_t*           dir = nullptr;
    PK_continuity_t*                orders = nullptr;
    PK_SURF_find_discontinuity_o_t  opts;
    PK_SURF_find_discontinuity_o_m (opts);

    // only want to fix G dicontinuity
    opts.level = PK_continuity_g1_c;

    PK_ERROR_code_t                 pkError = PK_SURF_find_discontinuity (bsurfaceTag, &opts, &nDisc, &disconts, &dir, &orders);

    if (PK_ERROR_no_errors == pkError && nDisc > 0)
        {
        PK_UVBOX_t                  uvBox;
        pkError = PK_SURF_ask_uvbox (bsurfaceTag, &uvBox);

        if (PK_ERROR_no_errors == pkError)
            {
            PK_BSURF_fitted_fault_t faults;
            PK_BSURF_t              newSurfaceTag = PK_ENTITY_null;

            PK_BSURF_create_fitted_o_t  options;
            PK_BSURF_create_fitted_o_m (options);

            options.surf.type = PK_SURF_general_surf_c;
            options.surf.surf.parasolid_surf = bsurfaceTag;
            options.u_range.value[0] = uvBox.param[0];
            options.u_range.value[1] = uvBox.param[2];
            options.v_range.value[0] = uvBox.param[1];
            options.v_range.value[1] = uvBox.param[3];

            pkError = PK_BSURF_create_fitted (&options, &newSurfaceTag, &faults);

            if (PK_ERROR_no_errors == pkError && PK_BSURF_fitted_success_c == faults.status && newSurfaceTag != PK_ENTITY_null)
                {
                PK_ENTITY_delete (1, &bsurfaceTag);
                bsurfaceTag = newSurfaceTag;
                }
            else if (newSurfaceTag != PK_ENTITY_null)
                {
                PK_ENTITY_delete (1, &newSurfaceTag);
                pkError = PK_ERROR_cant_make_bspline;
                }
            }
        }

    return  pkError;
    }

#ifdef  DEBUG_BAD_GEOMETRY
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DebugNurbSurface (AcGeNurbSurface* acNurbs)
    {
    AcGePoint3dArray    controlPoints;
    AcGeDoubleArray     weights;
    AcGeKnotVector      uKnots, vKnots;
    int                 degreeU = 0, degreeV = 0, propsU = 0, propsV = 0, nPointsU = 0, nPointsV = 0;

    acNurbs->getDefinition (degreeU, degreeV, propsU, propsV, nPointsU, nPointsV, controlPoints, weights, uKnots, vKnots);
    if (degreeU < 1 || degreeV < 1)
        return  false;

    // AcGeNurbSurface::getDefinition returns a wrong vKnot vector(appearing the same as uknots)!!
    acNurbs->getVKnots (vKnots);

    bool                rational = (propsU & AcGe::NurbSurfaceProperties::kRational) && (propsV & AcGe::NurbSurfaceProperties::kRational);
    int                 nTotalPoints = controlPoints.length ();
    DoubleArray         msWeights, knotsU, knotsV;
    DPoint3dArray       poles;
    DPoint3d            point;

    // copy U-major poles & weights
    for (int j = 0; j < nPointsV; j++)
        {
        for (int i = j; i < nTotalPoints; i += nPointsV)
            {
            m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(point, controlPoints[i]));
            poles.push_back (point);

            if (rational)
                msWeights.push_back (weights[i]);
            }
        }

    for (int i = 0; i < uKnots.length(); i++)
        knotsU.push_back (uKnots[i]);
    for (int i = 0; i < vKnots.length(); i++)
        knotsV.push_back (vKnots[i]);

    MSBsplineSurfacePtr bsurf = MSBsplineSurface::CreateFromPolesAndOrder (poles, &msWeights, &knotsU, degreeU+1, nPointsU, false, &knotsV, degreeV+1, nPointsV, false, false);

    if (bsurf.IsValid())
        {
        bsurf->NormalizeKnots ();

        EditElementHandle   eeh;
        BSplineStatus       status = BSplineSurfaceHandler::CreateBSplineSurfaceElement (eeh, nullptr, *bsurf.get(), *m_toDgnContext->GetModel());

        if (BSPLINE_STATUS_Success == status)
            {
            UInt32          color = 1;
            UInt32          weight = 2;

            m_toDgnContext->SetElementSymbology (eeh, LEVEL_DEFAULT_LEVEL_ID, &color, nullptr, &weight, nullptr, nullptr, 1.0, 0.0, DgnElementClass::Primary);

            eeh.AddToModel ();
            }

        // compare normals calculated by ASM vs. by us:
        AcGeInterval        intervalU, intervalV;
        acNurbs->getEnvelope (intervalU, intervalV);

        double              paramU = intervalU.isBoundedBelow() ? intervalU.lowerBound() : 0.0;
        double              paramV = intervalV.isBoundedBelow() ? intervalV.lowerBound() : 0.0;

        AcGeVector3dArray   derivatives;
        AcGeVector3d        geNormal;
        AcGePoint3d         gePoint0 = acNurbs->evalPoint (AcGePoint2d(paramU, paramV), 1, derivatives, geNormal);

        DPoint3d            dPoint0;
        DVec3d              derivsU[2], derivsV[2], derivUV, dNormal;
        bsurf->EvaluateAllPartials (dPoint0, derivsU[0], derivsV[0], derivsU[1], derivsV[1], derivUV, dNormal, paramU, paramV);

        m_toDgnContext->GetTransformFromDGN().Multiply (dPoint0);
        dNormal.Normalize ();

        bsurf->ReleaseMem ();

        return  true;
        }

    return  false;
    }
#endif  // DEBUG_BAD_GEOMETRY

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsSurfaceNormalInverted (PK_BSURF_t bsurfaceTag, AcGeNurbSurface* acNurbs)
    {
    // get the UV bounds from ASM:
    AcGeInterval    intervalU, intervalV;
    acNurbs->getEnvelope (intervalU, intervalV);

    PK_UV_t         lowerUV, upperUV;
    if (intervalU.isBounded())
        intervalU.getBounds (lowerUV.param[0], upperUV.param[0]);
    else
        lowerUV.param[0] = upperUV.param[0] = 0.0;
    if (intervalV.isBounded())
        intervalV.getBounds (lowerUV.param[1], upperUV.param[1]);
    else
        lowerUV.param[1] = upperUV.param[1] = 0.0;

    // get the surface normal at the lower bounds per ASM calculation:
    AcGeVector3dArray   derivatives;
    AcGeVector3d        acNormal;
    AcGePoint3d         acPoint0 = acNurbs->evalPoint (AcGePoint2d(lowerUV.param[0], lowerUV.param[1]), 1, derivatives, acNormal);

    // get the normal at the same UV params per Parasolid calculation:
    PK_VECTOR_t         pointsNderivs[50], pkNormal;
    if (PK_ERROR_no_errors == PK_SURF_eval_with_normal(bsurfaceTag, lowerUV, 1, 1, PK_LOGICAL_false, pointsNderivs, &pkNormal))
        {
        acNormal.normalize (AcGeContext::gTol);

        AcGeVector3d    checkNormal(pkNormal.coord[0], pkNormal.coord[1], pkNormal.coord[2]);
        checkNormal.normalize (AcGeContext::gTol);

        double          dotProduct = checkNormal.dotProduct (acNormal);
        if (dotProduct < -0.9)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateFaceBSurface (AcGeNurbSurface* acNurbs, bool* isOriented = nullptr)
    {
    PK_ERROR_code_t     pkError = PK_ERROR_wrong_surface;
    if (NULL == acNurbs)
        return  pkError;

#ifdef DEBUG_BAD_GEOMETRY
    this->DebugNurbSurface (acNurbs);
#endif

    PK_BSURF_sf_t       bsurfaceParams;
    double              periodic = 0;

    bsurfaceParams.u_degree = acNurbs->degreeInU ();
    bsurfaceParams.v_degree = acNurbs->degreeInV ();
    bsurfaceParams.n_u_vertices = acNurbs->numControlPointsInU ();
    bsurfaceParams.n_v_vertices = acNurbs->numControlPointsInV ();
    bsurfaceParams.n_u_knots = acNurbs->numKnotsInU ();     // multiplicit knots for now
    bsurfaceParams.n_v_knots = acNurbs->numKnotsInV ();     // multiplicit knots for now
    bsurfaceParams.is_u_closed = acNurbs->isClosedInU ();
    bsurfaceParams.is_v_closed = acNurbs->isClosedInV ();
    bsurfaceParams.is_u_periodic = acNurbs->isPeriodicInU (periodic);
    bsurfaceParams.is_v_periodic = acNurbs->isPeriodicInV (periodic);

    bsurfaceParams.is_rational = acNurbs->isRationalInU() && acNurbs->isRationalInV();
    bsurfaceParams.vertex_dim = bsurfaceParams.is_rational ? 4 : 3;

    bsurfaceParams.u_knot_type = PK_knot_unset_c;
    bsurfaceParams.v_knot_type = PK_knot_unset_c;
    bsurfaceParams.form = PK_BSURF_form_unset_c;
    bsurfaceParams.self_intersecting = PK_self_intersect_unset_c;
    bsurfaceParams.convexity = PK_convexity_unset_c;

    // get control points, weights, and knots
    size_t              numPoints = bsurfaceParams.n_u_vertices * bsurfaceParams.n_v_vertices;
    if (numPoints < 2)
        return  pkError;

    AcGePoint3dArray    poles;
    AcGeDoubleArray     weights;
    AcGeKnotVector      uKnots, vKnots;

    acNurbs->getControlPoints (poles);
    acNurbs->getWeights (weights);
    acNurbs->getUKnots (uKnots);
    acNurbs->getVKnots (vKnots);

    if (poles.length() < numPoints || (bsurfaceParams.is_rational && weights.length() < numPoints) ||
        uKnots.length() < bsurfaceParams.n_u_knots || vKnots.length() < bsurfaceParams.n_v_knots)
        return  pkError;

    if (PK_ERROR_no_errors != this->AllocateBSurfaceMemory(bsurfaceParams, numPoints))
        return  PK_ERROR_memory_full;

    // copy control points (V-major in Parasolid, same as in ASM)
    int                 index;  // use int so we don't have to type cast it every where
    for (index = 0; index < numPoints; index++)
        {
        UInt32          pkIndex = index * bsurfaceParams.vertex_dim;

        if (m_needTransformation)
            poles[index].transformBy (m_toParasolidMatrix);

        // copy control point at index
        memcpy (&bsurfaceParams.vertex[pkIndex], &poles[index], 3 * sizeof(double));

        // handle weights for a rational B-Spline
        if (4 == bsurfaceParams.vertex_dim)
            {
            // Parasolid expects homogenous/weighted poles
            bsurfaceParams.vertex[pkIndex + 0] *= weights[index];
            bsurfaceParams.vertex[pkIndex + 1] *= weights[index];
            bsurfaceParams.vertex[pkIndex + 2] *= weights[index];
            // copy weight
            bsurfaceParams.vertex[pkIndex + 3] = weights[index];
            }
        }

    // Parasolid expects distinct knots
    AcGeDoubleArray     uDistinctKnots, vDistinctKnots;
    uKnots.getDistinctKnots (uDistinctKnots);
    vKnots.getDistinctKnots (vDistinctKnots);

    bsurfaceParams.n_u_knots = uDistinctKnots.length ();
    bsurfaceParams.n_v_knots = vDistinctKnots.length ();

    // copy distinct U-knots
    for (index = 0; index < bsurfaceParams.n_u_knots; index++)
        {
        bsurfaceParams.u_knot[index] = uDistinctKnots[index];
        bsurfaceParams.u_knot_mult[index] = uKnots.multiplicityAt (uDistinctKnots[index]);
        }
    
    // copy distinct V-knots
    for (index = 0; index < bsurfaceParams.n_v_knots; index++)
        {
        bsurfaceParams.v_knot[index] = vDistinctKnots[index];
        bsurfaceParams.v_knot_mult[index] = vKnots.multiplicityAt (vDistinctKnots[index]);
        }
    
    PK_BSURF_t          bsurfaceTag = PK_ENTITY_null, tagSaved = PK_ENTITY_null;
    if (PK_ERROR_no_errors == (pkError = PK_BSURF_create(&bsurfaceParams, &bsurfaceTag)) && PK_ENTITY_null != (tagSaved = bsurfaceTag) &&
        PK_ERROR_no_errors == (pkError = this->CheckBSplineSurface(bsurfaceParams, bsurfaceTag)))
        m_pkGeometryTags.push_back ((int)bsurfaceTag);
    else
        DIAGNOSTIC_PRINTF ("Parasolid error creating a BSpline surface for Brep face: %d!\n", pkError);

    // check surface normal
    if (PK_ERROR_no_errors == pkError && tagSaved == bsurfaceTag && nullptr != isOriented && this->IsSurfaceNormalInverted(bsurfaceTag, acNurbs))
        *isOriented = !(*isOriented);

#ifdef REALDWG_NOISY_DEBUG
    if (PK_ERROR_no_errors == pkError)
        {
        int                 bsurfaceIndex = (int)m_pkGeometryTags.size() - 1;
        AcGePoint2d         param0(0, 0);
        AcGeVector3dArray   derivatives;
        AcGeVector3d        normal;

        AcGePoint3d         point0 = acNurbs->evalPoint (param0, 1, derivatives, normal);

        printf ("\t\t\tNurbSurface[%d@%d]: degree{%d, %d}, nPoles[%d, %d], nKnots[%d, %d], rational[%d, %d], %cnormal0{%g,%g,%g}@{%g,%g,%g}\n", (int)tagSaved, bsurfaceIndex,
                bsurfaceParams.u_degree, bsurfaceParams.v_degree, bsurfaceParams.n_u_vertices, bsurfaceParams.n_v_vertices,
                bsurfaceParams.n_u_knots, bsurfaceParams.n_v_knots, acNurbs->isRationalInU(), acNurbs->isRationalInV(), acNurbs->isNormalReversed() ? '-':'+',
                normal.x, normal.y, normal.z, point0.x, point0.y, point0.z);

        if (tagSaved != bsurfaceTag)
            {
            printf ("\t\t\t=>Replaced by[%d]", bsurfaceTag);
            if (PK_ERROR_no_errors == PK_BSURF_ask(bsurfaceTag, &bsurfaceParams))
                printf (": degree{%d, %d}, nPoles[%d, %d], nKnots[%d, %d], %srational",
                        bsurfaceParams.u_degree, bsurfaceParams.v_degree, bsurfaceParams.n_u_vertices, bsurfaceParams.n_v_vertices,
                        bsurfaceParams.n_u_knots, bsurfaceParams.n_v_knots, bsurfaceParams.is_rational ? "" : "non-");
            printf ("\n");
            }
        }
#endif

    this->FreeBSurfaceMemory (bsurfaceParams);

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateFaceGeometry (AcBrEntity* brEntity, bool* isOriented = nullptr)
    {
    // create geometry for the face and add it to m_pkGeometryTags which will be attached to the face later on
    AcBrFace*       acFace = dynamic_cast <AcBrFace*> (brEntity);
    AcGe::EntityId  surfaceType = AcGe::kObject;
    if (NULL == acFace || AcBr::eOk != acFace->getSurfaceType(surfaceType))
        return  EntityError;

    // extract native surface
    AcGeSurface     *acSurface = NULL, *nativeSurface = NULL;
    if (AcBr::eOk != this->GetNativeSurface(acSurface, nativeSurface, acFace))
        return  EntityError;

    PK_ERROR_code_t pkError = PK_ERROR_missing_geom;
    switch (surfaceType)
        {
        case AcGe::kPlane:
            pkError = this->CreateFacePlane ((AcGePlane*)nativeSurface);
            break;
        case AcGe::kCone:
            pkError = this->CreateFaceCone ((AcGeCone*)nativeSurface, isOriented);
            break;
        case AcGe::kTorus:
            pkError = this->CreateFaceTorus ((AcGeTorus*)nativeSurface, isOriented);
            break;
        case AcGe::kCylinder:
            pkError = this->CreateFaceCylinder ((AcGeCylinder*)nativeSurface, isOriented);
            break;
        case AcGe::kSphere:
            pkError = this->CreateFaceSphere ((AcGeSphere*)nativeSurface, isOriented);
            break;
        case AcGe::kNurbSurface:
            pkError = this->CreateFaceBSurface ((AcGeNurbSurface*)nativeSurface, isOriented);
            break;
        // FUTUREWORK: below two types are not supported in AcGe according to ARX doc in its sample code - will extrat them from external surface:
	case AcGe::kEllipCylinder:
            pkError = this->CreateFaceEllipticCylinder (acSurface);
            break;
	case AcGe::kEllipCone:
            pkError = this->CreateFaceEllipticCone (acSurface);
            break;
        default:
            DIAGNOSTIC_PRINTF ("Missing surface type %d for a Brep face[%d]\n", surfaceType, (m_pkClasses.size() - 1));
            BeAssert (false && L"Unsupported surface for a Brep face!!");
        }

    delete acSurface;
    delete nativeSurface;

    return  PK_ERROR_no_errors == pkError ? RealDwgSuccess : CantConvertBrep;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
int             GetOrAddTopologicalClass (PK_CLASS_t inClass, AcBrEntity* brEntity, bool* isNewEntry = nullptr, bool* isOriented = nullptr)
    {
    if (NULL != isNewEntry)
        *isNewEntry = false;

    if (NULL == brEntity)
        return  -1;

    AcBrepToPsClassMap*                    brEntity2pkClassMap = NULL;
    AcBrepToPsClassMap::const_iterator     iter;

    switch (inClass)
        {
        case PK_CLASS_body:
            iter = m_acBodyToClassIndex.find (brEntity->getEntity());
            if (iter != m_acBodyToClassIndex.end())
                return  iter->second;
            // will add a new body
            brEntity2pkClassMap = &m_acBodyToClassIndex;
            // no geoemtry for a body
            m_pkGeometryTags.push_back (-1);
            break;
        case PK_CLASS_region:
            iter = m_acComplexToClassIndex.find (brEntity->getEntity());
            if (iter != m_acComplexToClassIndex.end())
                return  iter->second;
            // will add a new body
            brEntity2pkClassMap = &m_acComplexToClassIndex;
            // no geoemtry for a complex/region
            m_pkGeometryTags.push_back (-1);
            break;
        case PK_CLASS_shell:
            iter = m_acShellToClassIndex.find (brEntity->getEntity());
            if (iter != m_acShellToClassIndex.end())
                return  iter->second;
            // will add a new shell
            brEntity2pkClassMap = &m_acShellToClassIndex;
            // no geoemtry for a shell
            m_pkGeometryTags.push_back (-1);
            break;
        case PK_CLASS_face:
            iter = m_acFaceToClassIndex.find (brEntity);
            if (iter != m_acFaceToClassIndex.end())
                return  iter->second;
            // will add a new face
            brEntity2pkClassMap = &m_acFaceToClassIndex;
            // add a surface
            if (RealDwgSuccess != this->CreateFaceGeometry(brEntity, isOriented))
                return  -1;
            break;
        case PK_CLASS_loop:
            iter = m_acLoopToClassIndex.find (brEntity->getEntity());
            if (iter != m_acLoopToClassIndex.end())
                return  iter->second;
            // will add a new loop
            brEntity2pkClassMap = &m_acLoopToClassIndex;
            // no geoemtry for a loop
            m_pkGeometryTags.push_back (-1);
            break;
        case PK_CLASS_edge:
            iter = m_acEdgeToClassIndex.find (brEntity->getEntity());
            if (iter != m_acEdgeToClassIndex.end())
                return  iter->second;
            // will add a new edge
            brEntity2pkClassMap = &m_acEdgeToClassIndex;
            // add a curve
            if (RealDwgSuccess != this->CreateEdgeGeometry(brEntity))
                return  -1;
            break;
        case PK_CLASS_vertex:
            iter = m_acVertexToClassIndex.find (brEntity->getEntity());
            if (iter != m_acVertexToClassIndex.end())
                return  iter->second;
            // will add a new vertex
            brEntity2pkClassMap = &m_acVertexToClassIndex;
            // add a point
            if (RealDwgSuccess != this->CreateVertexGeometry(brEntity))
                return  -1;
            break;
        default:
            return  -1;
        }

    if (NULL == brEntity2pkClassMap)
        return  -1;

    // the index at which the input class will be added
    int         newIndex = (int)(m_pkClasses.size());

    // now add the new class
    m_pkClasses.push_back (inClass);

    // add a new map
    brEntity2pkClassMap->insert (AcBrepToPsClassEntry(brEntity->getEntity(), newIndex));

    if (NULL != isNewEntry)
        *isNewEntry = true;

    return  newIndex;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            AddTopologicalRelation (int parent, int child, PK_TOPOL_sense_t sense = PK_TOPOL_sense_none_c)
    {
    /*--------------------------------------------------------------------
    Parasolid topology:
    Entity    Children      No. of children   Parents     No. of parents
    --------------------------------------------------------------------
    Body      Regions             >=1           -             -
    Region    Shells              >=1         Body            1
    Shell     Faces               >=1         Region          1
              Edges               >=1
              Vertex              1
    Face      Loops               >=1         Shell           2
    Loop      Edges               >=1         Face,Edge       >=1
              Fins                >=1
              Vertex              1
    Fin       Edge                1           Loop,Edge       <=2
    Edge      Vertices            <=2         Loop,Fin,Shell  1
              Loop                >=1
              Fin                 >=1
    Vertex    -                   0           Edge,Loop,Shell >=1
    --------------------------------------------------------------------*/
    m_pkParents.push_back (parent);
    m_pkChildren.push_back (child);
    m_pkSenses.push_back (sense);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CheckParasolidCreateFaults (PK_BODY_create_topology_2_r_t& results, PK_ERROR_t pkError)
    {
    if (PK_ERROR_no_errors != pkError)
        {
        DIAGNOSTIC_PRINTF ("Failed building Parasolid topology with error code= %d!\n", pkError);
        return  false;
        }

#ifdef DEBUG_DUMP_TOPOLOGY
    printf ("Dumping topology created by Parasolid: number of entities created = %d\n", results.n_topols);
    for (int i = 0; i < results.n_topols; i++)
        printf ("entity[%d]= %d %s\n", i, results.topols[i], ClassToString(m_pkClasses[i]));
#endif

    if (results.n_create_faults <= 0)
        return  true;

    if (NULL == results.create_faults)
        {
        DIAGNOSTIC_PRINTF ("Unexpected result from building Parasolid topology - null result!\n");
        return  false;
        }

    if (PK_BODY_state_ok_c == results.create_faults[0].state)
        return  true;

#ifdef REALDWG_DIAGNOSTICS
    printf ("Failed building Parasolid topology: state[0]= %d, entities= ", results.create_faults[0].state);
    for (int i = 0; i < results.create_faults[0].n_indices; i++)
        printf ("%d ", results.create_faults[0].indices[0]);
    printf ("\n");
#endif
    
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            AddExclusiveVoidRegions (int bodyIndex, bvector<IntArray>& exteriorRegions)
    {
    for each (IntArray exteriorFaces in exteriorRegions)
        {
        // add a positive void region
        m_pkClasses.push_back (PK_CLASS_region);
        m_pkGeometryTags.push_back (-1);

        int     voidRegionIndex = (int)m_pkClasses.size() - 1;
        this->AddTopologicalRelation (bodyIndex, voidRegionIndex, PK_TOPOL_sense_positive_c);
        NOISYDEBUG_PRINTF ("Body= %d, Region= %d (void)\n", bodyIndex, voidRegionIndex);

        // add a shell to the void region
        m_pkClasses.push_back (PK_CLASS_shell);
        m_pkGeometryTags.push_back (-1);

        int     shellIndex = (int)m_pkClasses.size() - 1;
        this->AddTopologicalRelation (voidRegionIndex, shellIndex, PK_TOPOL_sense_none_c);
        NOISYDEBUG_PRINTF ("\tRegion= %d, Shell= %d\n", voidRegionIndex, shellIndex);

        // add negative faces to the shell
        for each (int faceIndex in exteriorFaces)
            {
            this->AddTopologicalRelation (shellIndex, faceIndex, PK_TOPOL_sense_negative_c);
            NOISYDEBUG_PRINTF ("\t\tShell= %d, Face= -%d\n", shellIndex, faceIndex);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/14
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t CreateSheetBodyFromSurface (PK_BODY_create_topology_2_r_t& results, bvector<IntArray>& exteriorRegions, bool hasHole)
    {
    // This method takes one single face body of a surface and attempts to create a body via PK_SURF_make_sheet_body
    PK_ERROR_code_t     pkError = PK_ERROR_unsupported_operation;

    if (!m_isSheetBody || hasHole || 1 != exteriorRegions.size())
        return  pkError;

    IntArray            faces = exteriorRegions.front ();
    if (1 != faces.size())
        return  pkError;

    // make sure we really have got a surface
    PK_SURF_t           surfaceTag = m_pkGeometryTags[faces.front()];
    PK_CLASS_t          entClass = PK_CLASS_null;
    if (PK_ERROR_no_errors != PK_ENTITY_ask_class(surfaceTag, &entClass) || (PK_CLASS_bsurf != entClass && PK_CLASS_surf != entClass))
        return  pkError;

    PK_UVBOX_t          uvBox;
    if (PK_ERROR_no_errors == (pkError = PK_SURF_ask_uvbox(surfaceTag, &uvBox)))
        pkError = PK_SURF_make_sheet_body (surfaceTag, uvBox, &results.body);

    if (PK_ERROR_no_errors == pkError)
        NOISYDEBUG_PRINTF ("A sheet body[%d] created from a surface[%d], skipping from building the full topology!\n", results.body, surfaceTag);
    else
        NOISYDEBUG_PRINTF ("Failed creating a sheet body from a surface[%d] - building the full topology...\n", surfaceTag);

    return  pkError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateTopologyAndGeometry (PK_BODY_create_topology_2_r_t& results, PK_BODY_create_topology_2_o_t pkOptions)
    {
    /*--------------------------------------------------------------------------------------------------------------
    This method traverses DWG Brep tree and creates coorespondent Parasolid entities as it moves from body downward
    to vertex topology.  It also creates geometries and save them in m_pkGeometryTags, which will be attached to the 
    topology upon a successful completion of building the topology.
    ---------------------------------------------------------------------------------------------------------------*/
    AcBrBrepComplexTraverser    complexTraverser;
    AcBr::ErrorStatus           es = complexTraverser.setBrep (*m_acBrepEntity);
    if (AcBr::eOk != es)
        return  CantConvertBrep;

    // add the top level body and region:
    int         bodyIndex = this->GetOrAddTopologicalClass (PK_CLASS_body, m_acBrepEntity);
    int         regionIndex = this->GetOrAddTopologicalClass (PK_CLASS_region, m_acBrepEntity);
    if (bodyIndex < 0 || regionIndex < 0)
        return  CantConvertBrep;

    /*-------------------------------------------------------------------------------------------------------
    Make the region a child of the solid body
        1) Parasolid seems to use negative sense to denote a solid region and positive for the exclusive void
        2) Parasolid region does not appear to parallel to ASM complex so we merge complexes into shells
    -------------------------------------------------------------------------------------------------------*/
    this->AddTopologicalRelation (bodyIndex, regionIndex, PK_TOPOL_sense_negative_c);
    NOISYDEBUG_PRINTF ("Body= %d, Region= -%d (solid)\n", bodyIndex, regionIndex);

    bvector<IntArray>   exteriorRegions;
    bool                hasHoleLoop = false;

    // traverse complexes/regions
    while (!complexTraverser.done())
        {
        AcBrComplex                 acComplex;
        AcBrComplexShellTraverser   shellTraverser;
        if (AcBr::eOk != shellTraverser.setComplex(complexTraverser) || AcBr::eOk != complexTraverser.getComplex(acComplex))
            return  CantConvertBrep;

        IntArray        exteriorFaces;

        // traverse shells
        while (!shellTraverser.done())
            {
            AcBrShell       shell;
            AcBr::ShellType shellType = AcBr::kShellUnclassified;
            if (AcBr::eOk != shellTraverser.getShell(shell) || AcBr::eOk != shell.getType(shellType))
                return  CantConvertBrep;

            AcBrShellFaceTraverser  faceTraverser;
            if (AcBr::eOk != (es = faceTraverser.setShell(shell)))
                return  CantConvertBrep;

            int     shellIndex = this->GetOrAddTopologicalClass (PK_CLASS_shell, &shell);
            if (shellIndex < 0)
                return  CantConvertBrep;

            this->AddTopologicalRelation (regionIndex, shellIndex);
            NOISYDEBUG_PRINTF ("\tRegion= %d, shell= %d\n", regionIndex, shellIndex);

            // check for sense of the shell - exterial=positive, interior=negative
            bool                isExteriorShell = shellType == AcBr::kShellExterior && exteriorFaces.empty();
            PK_TOPOL_sense_t    shellSense = shellType == AcBr::kShellExterior ? PK_TOPOL_sense_positive_c : PK_TOPOL_sense_negative_c;

            // exterior faces for current shell starts at this index
            size_t              shellFacesStartAt = exteriorFaces.size();

            // traverse faces
            while (!faceTraverser.done())
                {
                AcBrFace        face;
                Adesk::Boolean  face2Surface = Adesk::kFalse;
                if (AcBr::eOk != faceTraverser.getFace(face) || face.isEqualTo(m_acBrepEntity) || AcBr::eOk != face.getOrientToSurface(face2Surface))
                    return  CantConvertBrep;

                AcBrFaceLoopTraverser   loopTraverser;
                bool                    isClosedSurface = false;
                if (AcBr::eOk != (es = loopTraverser.setFace(faceTraverser)))
                    {
                    /*-----------------------------------------------------------------------------------------------
                    AcBr::eDegenerateTopology indicates a closed surface, which is intrinsically bounded in both the 
                    u and v directions, such as a full torus or sphere.  Such a face has no loop boundaries.
                    -----------------------------------------------------------------------------------------------*/
                    if (AcBr::eDegenerateTopology == es)
                        isClosedSurface = true;
                    else
                        return  CantConvertBrep;
                    }

                bool            isNewFace = false;
                int             faceIndex = this->GetOrAddTopologicalClass (PK_CLASS_face, &face, &isNewFace, (bool*)&face2Surface);
                if (faceIndex < 0)
                    return  CantConvertBrepLogged ("face index returned from GetOrAddTopologicalClass is negative");

                // add a shell->face relation
                this->AddTopologicalRelation (shellIndex, faceIndex, shellSense);
                NOISYDEBUG_PRINTF ("\t\tShell= %d, face= %c%d, oriented= %c\n", shellIndex, SenseToChar(shellSense), faceIndex, OrientationToChar(face2Surface));

                // move on to next face if this face has already been processed
                if (!isNewFace)
                    {
                    if (AcBr::eOk != faceTraverser.next())
                        break;      // error
                    else
                        continue;
                    }

                // save face-surface orientation flag in the map
                m_isFaceOriented2Surface.insert (IsPositiveOrientationEntry(faceIndex, (PK_LOGICAL_t)face2Surface));
                // save off the exterior shell faces
                if (isExteriorShell)
                    exteriorFaces.push_back (faceIndex);

                // move on to next face if this face has an intrinsically bounded geometry
                if (isClosedSurface)
                    {
                    if (AcBr::eOk != faceTraverser.next())
                        break;  // error
                    else
                        continue;
                    }

                // traverse face loops
                while (!loopTraverser.done())
                    {
                    AcBrLoop        loop;
                    AcBr::LoopType  loopType = AcBr::kLoopUnclassified;
                    if (AcBr::eOk != loopTraverser.getLoop(loop) || AcBr::eOk != loop.getType(loopType))
                        return  CantConvertBrep;

                    AcBrLoopEdgeTraverser   edgeTraverser;
                    bool                    isSingular = false;
                    if (AcBr::eOk != (es = edgeTraverser.setLoop(loopTraverser)))
                        {
                        // AcBr::eDegenerateTopology indicates a singularity, i.e. loop-vertex:
                        if (AcBr::eDegenerateTopology == es)
                            isSingular = true;
                        else
                            return  CantConvertBrep;
                        }

                    int             loopIndex = this->GetOrAddTopologicalClass (PK_CLASS_loop, &loop);
                    if (loopIndex < 0)
                        return  CantConvertBrep;
                        
                    // set positive sense for exterior and negative for interior loop - not used by Parasolid, but helps debugging
                    PK_TOPOL_sense_t    loopSense = PK_TOPOL_sense_none_c;
                    if (AcBr::kLoopExterior == loopType)
                        loopSense = PK_TOPOL_sense_positive_c;
                    else if (AcBr::kLoopInterior == loopType)
                        loopSense = PK_TOPOL_sense_negative_c;

                    // add a face with a loop child
                    this->AddTopologicalRelation (faceIndex, loopIndex, loopSense);
                    NOISYDEBUG_PRINTF ("\t\t\tFace= %d, loop= %c%d (%s)\n", faceIndex, SenseToChar(loopSense), loopIndex, LooptypeToString(loopType));

                    // move on to next loop on singularity
                    if (isSingular)
                        {
                        if (AcBr::eOk != loopTraverser.next())
                            break;

                        int         vertexIndex = this->AddSingularVertex (face);
                        if (vertexIndex > 0)
                            {
                            this->AddTopologicalRelation (loopIndex, vertexIndex);
                            NOISYDEBUG_PRINTF ("\t\t\t\tSingular Loop-Vertex= %d - %d\n", loopIndex, vertexIndex);
                            }

                        if (AcBr::eOk != loopTraverser.next())
                            break;      // error
                        else
                            continue;
                        }

                    // PK_SURF_make_sheet_body cannot have holes, so we need to know about it prior to creating a sheet body:
                    if (!hasHoleLoop && AcBr::kLoopInterior == loopType)
                        hasHoleLoop = true;

                    // traverse loop edges
                    while (!edgeTraverser.done())
                        {
                        // get both the edge and its two vertices
                        AcBrEdge        edge;
                        AcBrVertex      vertex1, vertex2;
                        Adesk::Boolean  edge2Curve = Adesk::kTrue, edge2Loop = Adesk::kTrue;
                        if (AcBr::eOk != edgeTraverser.getEdge(edge) || edge.isNull() ||
                            AcBr::eOk != edge.getVertex1(vertex1) || vertex1.isNull() ||
                            AcBr::eOk != edge.getVertex2(vertex2) || vertex2.isNull() ||
                            AcBr::eOk != edge.getOrientToCurve(edge2Curve) || AcBr::eOk != edgeTraverser.getEdgeOrientToLoop(edge2Loop))
                            return  CantConvertBrep;

                        PK_TOPOL_sense_t    edgeSense = edge2Loop ? PK_TOPOL_sense_positive_c : PK_TOPOL_sense_negative_c;
                        PK_LOGICAL_t        curveSense = (PK_LOGICAL_t) edge2Curve;

                        // add a loop with a child edge
                        bool        isNewEdge = false;
                        int         edgeIndex = this->GetOrAddTopologicalClass (PK_CLASS_edge, &edge, &isNewEdge);
                        if (edgeIndex < 0)
                            return  CantConvertBrep;
                        this->AddTopologicalRelation (loopIndex, edgeIndex, edgeSense);
                        NOISYDEBUG_PRINTF ("\t\t\t\tLoop= %d, edge= %c%d, %coriented\n", loopIndex, SenseToChar(edgeSense), edgeIndex, OrientationToChar(edge2Curve));

                        // save edge->curve orientation flag in the map
                        m_isEdgeOriented2Curve.insert (IsPositiveOrientationEntry(edgeIndex, curveSense));

                        if (isNewEdge)
                            {
                            // add the new edge with one or two vertex children
                            int     vertex1Index = this->GetOrAddTopologicalClass (PK_CLASS_vertex, &vertex1);
                            int     vertex2Index = this->GetOrAddTopologicalClass (PK_CLASS_vertex, &vertex2);
                            if (vertex1Index < 0 || vertex2Index < 0)
                                return  CantConvertBrep;
                            this->AddTopologicalRelation (edgeIndex, vertex1Index);
                            // allow an intrinsically bounded curve such as circle or elllipse to have a single vertex.
                            if (vertex1Index != vertex2Index)
                                this->AddTopologicalRelation (edgeIndex, vertex2Index);

                            // print edge-vertex infor for debugging purpose only
                            this->PrintEdgeVertex (edgeIndex, vertex1Index, vertex2Index);

                            // this is it - a vertex does not have children!
                            }

                        if (AcBr::eOk != edgeTraverser.next())
                            break;
                        }   // end edge traverser while

                    if (AcBr::eOk != loopTraverser.next())
                        break;
                    }   // end loopTraverser while

                if (AcBr::eOk != faceTraverser.next())
                    break;
                }   // end faceTraverser while

            // for a sheet body, add "interior" faces to close a sheet shell
            if (m_isSheetBody)
                {
                size_t      totalFaces = exteriorFaces.size ();
                for (size_t i = shellFacesStartAt; i < totalFaces; i++)
                    {
                    this->AddTopologicalRelation (shellIndex, exteriorFaces[i], PK_TOPOL_sense_negative_c);
                    NOISYDEBUG_PRINTF ("\t\tShell= %d, face= -%d, (to complete sheet shell)\n", shellIndex, exteriorFaces[i]);
                    }
                }

            if (AcBr::eOk != shellTraverser.next())
                break;
            }   // end shellTraverser while

        if (!exteriorFaces.empty())
            exteriorRegions.push_back (exteriorFaces);

        if (AcBr::eOk != complexTraverser.next())
            break;
        }   // end complexTraverser while

    /*--------------------------------------------------------------------------------------------------------------
    Surface discontinuity can cause geometry attachment to fail.  For a sheet body with a single surface, we may be 
    able to workaround that with PK_SURF_make_sheet_body, which can split a surface for multiple faces.  So if the 
    input ASM entity is a sheet body which has a single surface, don't bother to build the full topology then attach 
    the geometry - directly create the body using PK_SURF_make_sheet_body instead. This should also help performance.
    --------------------------------------------------------------------------------------------------------------*/
    PK_ERROR_code_t     pkError = this->CreateSheetBodyFromSurface (results, exteriorRegions, hasHoleLoop);
    if (PK_ERROR_no_errors == pkError)
        return  CreatedSheetBodyFromSurface;
    
    // complete the universe by adding exlusive void regions to the solid regions
    if (!m_isSheetBody && !exteriorRegions.empty())
        this->AddExclusiveVoidRegions (bodyIndex, exteriorRegions);

    int     nTopols = (int)m_pkClasses.size ();
    int     nParents = (int)m_pkParents.size ();
    int     nChildren = (int)m_pkChildren.size ();
    if (nTopols < 1 || nParents < 1 || nParents != nChildren)
        return  CantConvertBrep;

    // debug the data we have built to create the topology
    this->DumpTopology ();

    pkError = PK_BODY_create_topology_2 (nTopols, &m_pkClasses.front(), nParents, &m_pkParents.front(), &m_pkChildren.front(), &m_pkSenses.front(), &pkOptions, &results);

    if (!this->CheckParasolidCreateFaults(results, pkError))
        return  CantConvertBrepLogged ("PK_BODY_create_topology_2 failed");

    // ISolidKernelEntity does not support general body
    PK_BODY_type_t      bodyType = PK_BODY_type_unspecified_c;
    PK_BODY_ask_type (results.body, &bodyType);
    if (bodyType != PK_BODY_type_solid_c && bodyType != PK_BODY_type_sheet_c)
        return  CantConvertBrep;

    // we have got a Parasolid topology ready to be attached with geometry.
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AttachGeometryToTopology (PK_TOPOL_t* pkTopols, int nTopols)
    {
    if (NULL == pkTopols || nTopols < 1 || nTopols != (int)m_pkGeometryTags.size() || nTopols != (int)m_pkClasses.size())
        return  CantConvertBrep;

    IntArray                faceTags, edgeTags, vertexTags;
    IntArray                surfaceTags, curveTags, pointTags;
    bvector<PK_LOGICAL_t>   face2Surface, edge2Curve;

    for (int i = 0; i < nTopols; i++)
        {
        switch (m_pkClasses[i])
            {
            case PK_CLASS_face:
                {
                faceTags.push_back (pkTopols[i]);
                surfaceTags.push_back (m_pkGeometryTags[i]);

                IsPositiveOrientationMap::const_iterator    iter = m_isFaceOriented2Surface.find (i);
                if (iter == m_isFaceOriented2Surface.end())
                    return  CantConvertBrep;
                face2Surface.push_back (iter->second);
                break;
                }

            case PK_CLASS_edge:
                {
                edgeTags.push_back (pkTopols[i]);
                curveTags.push_back (m_pkGeometryTags[i]);

                IsPositiveOrientationMap::const_iterator    iter = m_isEdgeOriented2Curve.find (i);
                if (iter == m_isEdgeOriented2Curve.end())
                    return  CantConvertBrep;
                edge2Curve.push_back (iter->second);
                break;
                }

            case PK_CLASS_vertex:
                {
                vertexTags.push_back (pkTopols[i]);
                pointTags.push_back (m_pkGeometryTags[i]);

                if (m_vertexTolerance > 0.0)
                    PK_VERTEX_set_precision (pkTopols[i], m_vertexTolerance);
                break;
                }
            }
        }

    // first attach points to vertices for a not intrinsically bounded body
    PK_ERROR_code_t     pkError = PK_ERROR_no_errors;
    int                 arraySize = (int) vertexTags.size ();
    if (arraySize > 0)
        pkError = PK_VERTEX_attach_points (arraySize, &vertexTags.front(), &pointTags.front());

    if (PK_ERROR_no_errors != pkError)
        {
        this->DumpGeometry (faceTags, surfaceTags, face2Surface, edgeTags, curveTags, edge2Curve, vertexTags, pointTags);
        DIAGNOSTIC_PRINTF ("Error attaching %d points to vertices: %d\n", arraySize, pkError);
        return  CantConvertBrep;
        }

    if ((arraySize = (int)edgeTags.size()) > 0)
        {
        PK_EDGE_attach_curves_o_s   options;
        PK_ENTITY_track_r_s         tracking;

        // set default options before copying edge orientation flags
        PK_EDGE_attach_curves_o_m (options);
        options.have_senses = PK_LOGICAL_true;
        options.senses = &edge2Curve.front ();

        // then attach curves to edges
        pkError = PK_EDGE_attach_curves_2 (arraySize, &edgeTags.front(), &curveTags.front(), &options, &tracking);
        }

    if (PK_ERROR_no_errors != pkError)
        {
        this->DumpGeometry (faceTags, surfaceTags, face2Surface, edgeTags, curveTags, edge2Curve, vertexTags, pointTags);
        DIAGNOSTIC_PRINTF ("Error attaching %d curves to edges: %d\n", arraySize, pkError);
        return  CantConvertBrepLogged ("PK_ERROR_code_t is " + std::to_string (pkError));
        }

#ifdef DUMP_FACE_ATTACHMENT
    this->StartDebugReport ();
#endif

    // attach surfaces to faces last
    if ((arraySize = (int)faceTags.size()) > 0)
        pkError = PK_FACE_attach_surfs (arraySize, &faceTags.front(), &surfaceTags.front(), &face2Surface.front());

#ifdef DUMP_FACE_ATTACHMENT
    this->StopDebugReport ();
#endif

    if (PK_ERROR_no_errors != pkError)
        {
        this->DumpGeometry (faceTags, surfaceTags, face2Surface, edgeTags, curveTags, edge2Curve, vertexTags, pointTags);
        DIAGNOSTIC_PRINTF ("Error attaching %d surfaces to faces: %d\n", arraySize, pkError);
        }

    return  PK_ERROR_no_errors == pkError ? RealDwgSuccess : CantConvertBrep;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsOutsideParasolidSizeBox (const AcGePoint3d& minPoint, const AcGePoint3d& maxPoint)
    {
    // The Parasolid body size is defined as 1000x1000x1000 and centered at {0, 0, 0}.
    double      sizeboxHigh = 0.5 * MAX_PS_SizeBox;
    double      sizeboxLow  = -sizeboxHigh;

    return  minPoint.x < sizeboxLow  || minPoint.y < sizeboxLow  || minPoint.z < sizeboxLow ||
            maxPoint.x > sizeboxHigh || maxPoint.y > sizeboxHigh || maxPoint.z > sizeboxHigh;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   TransformToParasolidSpace (AcDbEntityP acEntity)
    {
    /*---------------------------------------------------------------------------------------------------------------
    This method finds the ASM body size and geometric center location, builds a transformation matrix that transforms
    the body into the Parasolid size box.  It also sets Parasolid's vertex tolerance based on ASM body size.
    ---------------------------------------------------------------------------------------------------------------*/
    m_needTransformation = false;

    AcDbExtents     extents;
    if (Acad::eOk != acEntity->getGeomExtents(extents))
        return  EntityError;

    AcGePoint3d     minPoint = extents.minPoint ();
    AcGePoint3d     maxPoint = extents.maxPoint ();
    double          bodySize = minPoint.distanceTo (maxPoint);
    if (bodySize <= TOLERANCE_ZeroSize)
        return  EntityError;

    // scale the body if the size is either too big or too small
    double          scale = 1.0;
    if (bodySize > MAX_PS_SizeBox || bodySize < 10.0)
        {
        scale = MAX_PS_SizeBox / bodySize;

        /*-----------------------------------------------------------------------------------------------
        The scale we get above tightly fits the body to the size box, but we'd like to make some room for
        the sake of Parasolid.  When we shrink a large body into the size box, we scale the body to about
        80% the size box. which seems to work for most cases we have seen so far.  When we enlarge a body, 
        however, we choose to only scale it up to 50% in an attempt to reduce potential geometrical
        distortion by a large scale, which can result in PK_ERROR_bad_tolerance.
        -----------------------------------------------------------------------------------------------*/
        double      largeScale = 0.8 * scale;
        double      smallScale = 0.5 * scale;
        scale = (bodySize > MAX_PS_SizeBox) ? largeScale : smallScale;

        // reset body size for vertex tolernace - we still want to use the large room as the tolerance base
        bodySize *= largeScale;

        m_toParasolidMatrix.setToScaling (scale);
        m_needTransformation = true;
        }

    // translate the body to align its center at the world origin as needed
    if (m_needTransformation || this->IsOutsideParasolidSizeBox(minPoint, maxPoint))
        {
        AcGePoint3d     center = 0.5 * (minPoint + maxPoint.asVector());
        AcGeVector3d    translation = center.asVector().negate ();

        if (m_needTransformation)
            translation.setToProduct (translation, scale);

        m_toParasolidMatrix.setTranslation (translation);

        m_needTransformation = true;
        }

    if (m_needTransformation)
        m_toParasolidScale = scale;

    // loosen up vertex tolerance based on body size
    m_vertexTolerance = ACAD_DISTANCE_TOLERANCE * bodySize;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/15
+---------------+---------------+---------------+---------------+---------------+------*/
PK_ERROR_code_t RepairParasolidBody (PK_BODY_t bodyTag)
    {
    PK_TOPOL_track_r_t  tracking;
    int                 numEdges = 0;
    PK_EDGE_t*          edges = nullptr;
    PK_ERROR_code_t     error = PK_BODY_ask_edges (bodyTag, &numEdges, &edges);

    // repair edges
    if (PK_ERROR_no_errors == error && numEdges > 0)
        {
        PK_EDGE_repair_o_t  edgeOptions;
        PK_EDGE_repair_o_m (edgeOptions);

        edgeOptions.max_tolerance = 10.0 * m_vertexTolerance;

        error = PK_EDGE_repair (numEdges, edges, &edgeOptions, &tracking);

        if (PK_ERROR_no_errors != error)
            DIAGNOSTIC_PRINTF ("\tFailed repairing %d edges! PKError=%d\n", numEdges, error);
        }
    else
        {
        numEdges = 0;
        }

    int                 numFaces = 0;
    PK_FACE_t*          faces = nullptr;
    error = PK_BODY_ask_faces (bodyTag, &numFaces, &faces);

    // repair faces
    if (PK_ERROR_no_errors == error && numFaces > 0)
        {
        PK_FACE_repair_o_t  faceOptions;
        PK_FACE_repair_o_m (faceOptions);

        for (int i = 0; i < numFaces; i++)
            {
            error = PK_FACE_repair (faces[i], &faceOptions, &tracking);

            if (PK_ERROR_no_errors != error)
                DIAGNOSTIC_PRINTF ("\tFailed repairing face %d! PKError=%d\n", faces[i], error);
            }
        }

    PK_EDGE_reset_precision_o_t precisionOptions;
    PK_EDGE_reset_precision_o_m (precisionOptions);

    // reset edge curve precision
    for (int i = 0; i < numEdges; i++)
        {
        PK_reset_prec_t result;

        error = PK_EDGE_reset_precision_2 (edges[i], &precisionOptions, &result);

        if (PK_ERROR_no_errors != error)
            DIAGNOSTIC_PRINTF ("\tFailed resetting precision for edge %d! PKError=%d\n", faces[i], error);
        }

    return  PK_ERROR_no_errors == error;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CheckParasolidBody (PK_BODY_t bodyTag)
    {
    PK_ERROR_code_t     error;
#ifdef DEBUG_DUMP_EDGES
    int                 nEdges = 0;
    PK_EDGE_t*          edges = nullptr;

    PK_BODY_ask_topology_o_t        options;
    PK_BODY_ask_topology_o_m (options);

    int                 nTopols = 0, nRelations = 0;
    PK_TOPOL_t*         topols = NULL;
    PK_CLASS_t*         classes;
    int*                parents = NULL;
    int*                children = NULL;
    PK_TOPOL_sense_t*   senses = NULL;

    this->StartDebugReport ();
    error = PK_BODY_ask_topology (bodyTag, &options, &nTopols, &topols, &classes, &nRelations, &parents, &children, &senses);
    error = PK_BODY_ask_edges (bodyTag, &nEdges, &edges);

    if (PK_ERROR_no_errors == error && nEdges > 1)
        {
        printf ("Dumping %d edges via PK_BODY_ask_edges:\n");
        for (int i = 0; i < nEdges; i++)
            printf ("%d ", edges[i]);
        printf ("\n");
        }
    else
        {
        printf ("Error code returned from PK_BODY_ask_edges = %d, or 0 number of edges returned!\n", error);
        }
    if (NULL != topols)
        delete topols;
    if (NULL != classes)
        delete classes;
    if (NULL != parents)
        delete parents;
    if (NULL != children)
        delete children;
    if (NULL != senses)
        delete senses;
#endif

    int                 nFaults = 0;
    PK_check_fault_t*   faults;

    error = PK_BODY_check (bodyTag, NULL, &nFaults, &faults);

#ifdef DEBUG_DUMP_EDGES
    this->StopDebugReport ();
#endif

    if (PK_ERROR_no_errors == error && nFaults < 1)
        {
        PK_BODY_type_t  bodyType = PK_BODY_type_unspecified_c;
        PK_BODY_ask_type (bodyTag, &bodyType);

        // we don't support general body
        return PK_BODY_type_general_c == bodyType ? false : true;
        }

    bool    allowFaults = true;

    DIAGNOSTIC_PRINTF ("Parsolid body check failed: error= %d", error);

    for (int i = 0; i < nFaults; i++)
        {
        PK_CLASS_t      ent1Class = 0, ent2Class = 0;
        error = PK_ENTITY_ask_class (faults[i].entity_1, &ent1Class);
        error = PK_ENTITY_ask_class (faults[i].entity_2, &ent2Class);

#ifdef REALDWG_DIAGNOSTICS
        printf ("\n\t%d)state= %d, entity1[%s]= %d, entity2[%s]= %d", i, (int)faults[i].state, ClassToString(ent1Class), faults[i].entity_1, ClassToString(ent2Class), faults[i].entity_2);
#endif

        // treat below faults as error: 
        switch (faults[i].state)
            {
            case PK_FACE_state_bad_vertex_c:        // TFS 168352 - torus surface failed rendering
            case PK_FACE_state_bad_edge_c:          // TFS 204884 - view update hung in a Parasolid faceting call
            case PK_FACE_state_bad_loops_c:         // loops/faces have wrong normals!
                allowFaults = false;
                break;
            default:
                allowFaults = true;
            }

        if (!allowFaults)
            break;
        }
    DIAGNOSTIC_PRINTF ("\n");

    return  allowFaults;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CheckParasolidGeometry (PK_GEOM_t geometryTag)
    {
    PK_GEOM_check_o_t   options;
    PK_GEOM_check_o_m (options);

    PK_check_fault_t*   faults = NULL;
    int                 nFaults = 0;

    PK_ERROR_code_t     error = PK_GEOM_check (geometryTag, &options, &nFaults, &faults);

    if (PK_ERROR_no_errors == error && nFaults < 1)
        {
        // Parasolid can fail on attaching a surface with C1 discontinuity to a face
        PK_CLASS_t      entClass = PK_CLASS_null;
        if (PK_ERROR_no_errors == PK_ENTITY_ask_class(geometryTag, &entClass) && (PK_CLASS_bsurf == entClass || PK_CLASS_surf == entClass))
            {
            PK_SURF_find_discontinuity_o_t  opts;
            PK_SURF_find_discontinuity_o_m (opts);

            int                     nDisc = 0;
            double*                 disconts = nullptr;
            PK_PARAM_direction_t*   dir = nullptr;
            PK_continuity_t*        orders = nullptr;

            if (PK_ERROR_no_errors != PK_SURF_find_discontinuity(geometryTag, &opts, &nDisc, &disconts, &dir, &orders) || nDisc > 0)
                {
                DIAGNOSTIC_PRINTF ("\t-BSurface[%d] has %d discontinuity(ies):", geometryTag, nDisc);
                for (int i = 0; i < nDisc; i++)
                    DIAGNOSTIC_PRINTF (" %g(%c c%d)", disconts[i], PK_PARAM_direction_u_c == dir[i] ? 'U' : 'V', orders[i] - PK_continuity_c1_c + 1);
                DIAGNOSTIC_PRINTF ("\n");
                return  false;
                }
            }
        return  true;
        }

#ifdef REALDWG_DIAGNOSTICS
    printf ("Parsolid geometry check failed: error= %d", error);
    for (int i = 0; i < nFaults; i++)
        {
        PK_CLASS_t      ent1Class = 0, ent2Class = 0;
        error = PK_ENTITY_ask_class (faults[i].entity_1, &ent1Class);
        error = PK_ENTITY_ask_class (faults[i].entity_2, &ent2Class);
        printf ("\n\t%d)state= %d, entity1[%s]= %d, entity2[%s]= %d", i, (int)faults[i].state, ClassToString(ent1Class), faults[i].entity_1, ClassToString(ent2Class), faults[i].entity_2);
        }
    printf ("\n");
#endif

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            PrintEdgeVertex (int edgeIndex, int vertex1Index, int vertex2Index)
    {
#ifdef REALDWG_NOISY_DEBUG
    PK_POINT_sf_s   p1, p2;
    if (PK_ERROR_no_errors == PK_POINT_ask(m_pkGeometryTags[vertex1Index], &p1) && 
        PK_ERROR_no_errors == PK_POINT_ask(m_pkGeometryTags[vertex2Index], &p2))
        {
        if (m_needTransformation)
            {
            AcGeMatrix3d    fromParasolidMatrix = m_toParasolidMatrix.inverse ();
            AcGePoint3d     acPoint (p1.position.coord[0], p1.position.coord[1], p1.position.coord[2]);

            acPoint.transformBy (fromParasolidMatrix);
            memcpy (&p1.position.coord[0], &acPoint, sizeof p1.position.coord);

            acPoint.set (p2.position.coord[0], p2.position.coord[1], p2.position.coord[2]);

            acPoint.transformBy (fromParasolidMatrix);
            memcpy (&p2.position.coord[0], &acPoint, sizeof p2.position.coord);
            }

        printf ("\t\t\t\t\tEdge= %d, vertex1= %d{%g,%g,%g}, vertex2= %d{%g,%g,%g}\n", edgeIndex,
                vertex1Index, p1.position.coord[0], p1.position.coord[1], p1.position.coord[2],
                vertex2Index, p2.position.coord[0], p2.position.coord[1], p2.position.coord[2]);
        }
    else
        {
        printf ("\t\t\t\t\tEdge= %d, vertex1= %d, vertex2= %d [error extracting points!]\n", edgeIndex, vertex1Index, vertex2Index);
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            DumpTopology ()
    {
#ifdef DEBUG_DUMP_TOPOLOGY
    size_t      count = 0;
    printf ("Dumping topology passed to Parasolid:\n");
    for each (int topolClass in m_pkClasses)
        printf ("Class[%d]= %d %s\n", count++, topolClass, ClassToString(topolClass));

    count = m_pkParents.size ();
    for (int i = 0; i < count; i++)
        printf ("Relation[%d]= parent= %d, child= %c%d\n", i, m_pkParents[i], SenseToChar(m_pkSenses[i]), m_pkChildren[i]);

    count = 0;
    for each (int geometryTag in m_pkGeometryTags)
        {
        if (geometryTag < 0)
            continue;

        printf ("Geometry[%d]= %d  (", count++, geometryTag);

        PK_POINT_sf_t       point;
        PK_LINE_sf_t        line;
        PK_CIRCLE_sf_t      circle;
        PK_ELLIPSE_sf_t     ellipse;
        PK_BCURVE_sf_t      bcurve;
        PK_BSURF_sf_t       bsurf;
        PK_PLANE_sf_t       plane;
        PK_CLASS_t          entClass = PK_CLASS_null;

        PK_ENTITY_ask_class (geometryTag, &entClass);

        if (PK_CLASS_point == entClass && PK_ERROR_no_errors == PK_POINT_ask(geometryTag, &point))
            printf ("Point: o{%g, %g, %g}", point.position.coord[0], point.position.coord[1], point.position.coord[2]);
        else if (PK_CLASS_line == entClass && PK_ERROR_no_errors == PK_LINE_ask(geometryTag, &line))
            printf ("Line: o{%g, %g, %g}, x{%g, %g, %g}", line.basis_set.location.coord[0], line.basis_set.location.coord[1], line.basis_set.location.coord[2], line.basis_set.axis.coord[0], line.basis_set.axis.coord[1], line.basis_set.axis.coord[2]);
        else if (PK_CLASS_circle == entClass && PK_ERROR_no_errors == PK_CIRCLE_ask(geometryTag, &circle))
            printf ("Circle: o{%g, %g, %g}, R=%g", circle.basis_set.location.coord[0], circle.basis_set.location.coord[1], circle.basis_set.location.coord[2], circle.radius);
        else if (PK_CLASS_ellipse == entClass && PK_ERROR_no_errors == PK_ELLIPSE_ask(geometryTag, &ellipse))
            printf ("Ellipse: o{%g, %g, %g}, R1=%g %R2=%g", ellipse.basis_set.location.coord[0], ellipse.basis_set.location.coord[1], ellipse.basis_set.location.coord[2], ellipse.R1, ellipse.R2);
        else if (PK_CLASS_bcurve == entClass && PK_ERROR_no_errors == PK_BCURVE_ask(geometryTag, &bcurve))
            printf ("BCurve: %d degrees, %d poles", bcurve.degree, bcurve.n_vertices);
        else if (PK_CLASS_plane == entClass && PK_ERROR_no_errors == PK_PLANE_ask(geometryTag, &plane))
            printf ("Plane: o{%g, %g, %g}, x{%g, %g, %g}, z{%g, %g, %g}", plane.basis_set.location.coord[0], plane.basis_set.location.coord[2], plane.basis_set.location.coord[2],
                        plane.basis_set.ref_direction.coord[0], plane.basis_set.ref_direction.coord[1], plane.basis_set.ref_direction.coord[2], 
                        plane.basis_set.axis.coord[0], plane.basis_set.axis.coord[1], plane.basis_set.axis.coord[2]);
        else if (PK_CLASS_bsurf == entClass && PK_ERROR_no_errors == PK_BSURF_ask(geometryTag, &bsurf))
            printf ("BSurface= {%d, %d} degrees", bsurf.u_degree, bsurf.v_degree);
        else
            printf ("??");
        printf (")\n");
        }
#endif    
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            DumpGeometry (IntArrayR faceTags, IntArrayR surfaceTags, bvector<PK_LOGICAL_t>& face2Surface, IntArrayR edgeTags, IntArrayR lineTags, bvector<PK_LOGICAL_t>& edge2Line, IntArrayR vertexTags, IntArrayR pointTags)
    {
#ifdef REALDWG_NOISY_DEBUG
    PK_ERROR_code_t     error;
    int                 i = 0;
    printf ("Dumping geometry:");
    for (; i < faceTags.size(); i++)
        {
        PK_PLANE_sf_t   plane;
        PK_BSURF_sf_t   bsurface;
        PK_CYL_sf_t     cylinder;
        PK_TORUS_sf_t   torus;
        PK_CLASS_t      entClass = PK_CLASS_null;

        PK_ENTITY_ask_class (surfaceTags[i], &entClass);

        if (PK_CLASS_plane == entClass && PK_ERROR_no_errors == (error = PK_PLANE_ask(surfaceTags[i], &plane)))
            {
            printf ("Plane[%d]/Face[%d] ", surfaceTags[i], faceTags[i]);
            DiagnosticPrintAxis2 (plane.basis_set);
            printf (" %c\n", OrientationToChar(face2Surface[i]));
            }
        else if (PK_CLASS_bsurf == entClass && PK_ERROR_no_errors == (error = PK_BSURF_ask(surfaceTags[i], &bsurface)))
            {
            printf ("BSurface[%d]/Face[%d]: degrees{%d, %d}, nPoles[%d, %d], nKnots[%d, %d], %s rational.\n", surfaceTags[i], faceTags[i],
                        bsurface.u_degree, bsurface.v_degree, bsurface.n_u_vertices, bsurface.n_v_vertices,
                        bsurface.n_u_knots, bsurface.n_v_knots, bsurface.is_rational ? "is" : "not");
            }
        else if (PK_CLASS_cyl == entClass && PK_ERROR_no_errors == (error = PK_CYL_ask(surfaceTags[i], &cylinder)))
            {
            printf ("Cylinder[%d]/Face[%d]: at {%g, %g, %g}, R=%g, z{%g, %g, %g}\n", surfaceTags[i], faceTags[i]);
            DiagnosticPrintAxis2 (cylinder.basis_set);
            printf (" %c\n", OrientationToChar(face2Surface[i]));
            }
        else if (PK_CLASS_torus == entClass && PK_ERROR_no_errors == (error = PK_TORUS_ask(surfaceTags[i], &torus)))
            {
            printf ("Torus, R= %g, r= %g, ", torus.minor_radius, torus.minor_radius);
            DiagnosticPrintAxis2 (torus.basis_set);
            printf (" %c\n", OrientationToChar(face2Surface[i]));
            }
        else
            {
            printf ("Surface(class=%d)[%d]/Face[%d]...\n", entClass, surfaceTags[i], faceTags[i]);
            }
        this->CheckParasolidGeometry (surfaceTags[i]);
        }

    for (i = 0; i < edgeTags.size(); i++)
        {
        PK_CURVE_t      curveTag = -1;
        PK_CLASS_t      geomClass = -1;
        PK_VECTOR_t     ends[2];
        PK_LOGICAL_t    sense = PK_LOGICAL_false;
        PK_INTERVAL_t   interval;
        PK_LINE_sf_t    line;
        PK_ELLIPSE_sf_s ellipse;
        PK_CLASS_t      entClass = PK_CLASS_null;

        PK_ENTITY_ask_class (edgeTags[i], &entClass);

        if (PK_CLASS_line == entClass && PK_ERROR_no_errors == (error = PK_LINE_ask(lineTags[i], &line)))
            {
            printf ("Line[%d]/Edge[%d] at {%.10f, %.10f, %.10f}, %c\n", lineTags[i], edgeTags[i],
                        line.basis_set.location.coord[0], line.basis_set.location.coord[1], line.basis_set.location.coord[2], 
                        OrientationToChar(edge2Line[i]));
            }
        else if (PK_CLASS_ellipse == entClass && PK_ERROR_no_errors == (error = PK_ELLIPSE_ask(lineTags[i], &ellipse)))
            {
            printf ("Ellipse[%d]/Edge[%d] at {%.10f, %.10f, %.10f}, R= %.10f, r=%.10f, %c\n", lineTags[i], edgeTags[i],
                        ellipse.basis_set.location.coord[0], ellipse.basis_set.location.coord[1], ellipse.basis_set.location.coord[2], 
                        ellipse.R1, ellipse.R2, OrientationToChar(edge2Line[i]));
            }
        else if (PK_ERROR_no_errors == (error = PK_EDGE_ask_geometry(edgeTags[i], false, &curveTag, &geomClass, ends, &interval, &sense)))
            {
            printf ("Curve[%d]/Edge[%d]: p1{%.10f, %.10f, %.10f}, p2{%.10f, %.10f, %.10f}, %c\n", lineTags[i], edgeTags[i],
                        ends[0].coord[0], ends[0].coord[1], ends[0].coord[2], ends[1].coord[0], ends[1].coord[1], ends[1].coord[2], 
                        OrientationToChar(edge2Line[i]));
            }
        else
            {
            printf ("Curve[%d]/Edge[%d]: failed data extraction!!\n", lineTags[i], edgeTags[i]);
            }
        this->CheckParasolidGeometry (lineTags[i]);
        }

    for (i = 0; i < vertexTags.size(); i++)
        {
        PK_POINT_sf_t   point;
        error = PK_POINT_ask (pointTags[i], &point);

        if (PK_ERROR_no_errors == error)
            printf ("Point[%d]/Vertex[%d]= %.10f, %.10f, %.10f\n", pointTags[i], vertexTags[i], 
                        point.position.coord[0], point.position.coord[1], point.position.coord[2]);
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            StartDebugReport ()
    {
#ifdef REALDWG_DIAGNOSTICS
    this->StopDebugReport ();

    m_debugReportStarted = false;

    BeFileName  reportFileName;
    if (BeFileNameStatus::Success == BeFileName::BeGetTempPath(reportFileName))
        {
        reportFileName.AppendToPath (L"\\Asm2ParasolidReport.xml");

        AString     localeName;
        if (BSISUCCESS == BeStringUtilities::WCharToCurrentLocaleChar(localeName, reportFileName.GetWCharCP()))
            {
            PK_DEBUG_report_start_o_t   reportOptions;
            PK_DEBUG_report_start_o_m (reportOptions);

            if (PK_ERROR_no_errors == PK_DEBUG_report_start(localeName.c_str(), NULL))
                m_debugReportStarted = true;
            }
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            StopDebugReport ()
    {
    if (m_debugReportStarted)
        {
        PK_DEBUG_report_stop ();
        m_debugReportStarted = false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            CleanUpParasolid (PK_BODY_create_topology_2_r_t& results, bool deleteGeometry)
    {
    bvector<PK_ENTITY_t>    entities;
    if (results.body > 0)
        entities.push_back (results.body);

    if (deleteGeometry)
        {
        // delete unattached geometry
        for each (int geometryTag in m_pkGeometryTags)
            {
            if (geometryTag > 0)
                entities.push_back (geometryTag);
            }
        }

    if (entities.size() > 0)
        {
        PK_ERROR_code_t     pkError = PK_ENTITY_delete ((int)entities.size(), &entities.front());

        if (PK_ERROR_no_errors != pkError)
            DIAGNOSTIC_PRINTF ("Failled deleting %d Parasolid entities. [Error=%d]!\n", entities.size(), pkError);
        }

    this->StopDebugReport ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/14
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          ResetNumberOfIsolines (UInt32 newIsolines = 0)
    {
    TcbP                pTcb = nullptr;
    EditElementHandle   type9Eeh (m_toDgnContext->GetFile()->GetPersistentTcbElementRef(), m_toDgnContext->GetModel());
    if (type9Eeh.IsValid() && nullptr != (pTcb = (Tcb *)(((Dgn_header *)type9Eeh.GetElementP())->tcbinfo)))
        {
        UInt32          oldIsolines = pTcb->smartGeomSettings.nIsoparametrics;

        pTcb->smartGeomSettings.nIsoparametrics = 0 == newIsolines ? 2 : newIsolines;

        if (BSISUCCESS == type9Eeh.ReplaceInModel(type9Eeh.GetElementRef()))
            {
            // sync with the TCB cached by the host:
            DwgPlatformHost::Instance()._SetPersistentTcbForSilentSave (m_toDgnContext->GetFile()->GetPersistentTcb ());
            return  oldIsolines;
            }
        }

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateSmartSolidElement (EditElementHandle& outElement, PK_ENTITY_t pkEntity, AcDbEntityP acEntity)
    {
    Transform   transform = m_toDgnContext->GetTransformToDGN ();
    if (m_needTransformation)
        {
        // invert the Brep transformation we have attempted to fit it into Parasolid modeling size box
        Transform   transformToDwg;
        RealDwgUtil::TransformFromGeMatrix3d (transformToDwg, m_toParasolidMatrix.invert());

        transform.InitProduct (transform, transformToDwg);
        }

    // create a template element for the SmartSolid
    EditElementHandle   templateElem;
    DPoint3d            points[2];
    points[0].Zero ();
    points[1].Zero ();
    if (RealDwgSuccess != m_toDgnContext->CreateElementFromVertices(templateElem, points, 2, false, m_toDgnContext->GetTransformToDGN()))
        return  CantConvertBrep;

    m_toDgnContext->ElementHeaderFromEntity (templateElem, acEntity, HEADERRESTOREMASK_Default ^ HEADERRESTOREMASK_XAttributes);

    /*--------------------------------------------------------------------------------------------------------------------
    PSolidUtil::CreateElement fails creating a sphere/torus/cynlinder element on tcb->smartGeomSettings.nIsoparametrics=1.
    PSolidUtill::getAdjustedRadialAngles explicitly excludes a full sweep, hence fails on nIso=1.  We workaround this BRep
    legacy logic by resetting the TCB value and restoring it after the BRep element is created.  We can live with this 
    workaround because it is a unique case that rarely gets hit.  There should be no practical use of ISOLINES=1.  The only
    case we have seen is from the bench mark test file BUDWISER.
    --------------------------------------------------------------------------------------------------------------------*/
    UInt32              oldIsolines = 0;
    if (m_hasAnalyticSurface && m_toDgnContext->GetDatabase()->isolines() == 1)
        oldIsolines = this->ResetNumberOfIsolines ();

    ISolidKernelEntityPtr   kernelEntity = PSolidKernelManager::CreateEntityPtr (pkEntity, transform);
    if (!kernelEntity.IsValid() || BSISUCCESS != PSolidUtil::CreateElement(outElement, *kernelEntity, &templateElem, *m_toDgnContext->GetModel()))
        return  CantConvertBrep;

    outElement.GetElementP()->ehdr.uniqueId = templateElem.GetElementId ();
    outElement.GetElementP()->hdr.dhdr.props.b.invisible = templateElem.GetElementP()->hdr.dhdr.props.b.invisible;

    // restore nIsoparametrics if changed
    if (oldIsolines > 0)
        this->ResetNumberOfIsolines (oldIsolines);

    // set xattributes from xRecords
    m_toDgnContext->ExtractXAttributesFromExtensionDictionary (outElement, acEntity);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsSheetBody (AcDbEntityP acEntity)
    {
    // AcBrEntity::getVolume is an expensive operation so try AcDbEntity types as our first choice:
    if (acEntity->isKindOf(AcDb3dSolid::desc()))
        return  false;

    // a surface or a region is always a sheet body
    if (acEntity->isKindOf(AcDbSurface::desc()) || acEntity->isKindOf(AcDbRegion::desc()))
        return  true;

    // a body type may be sheet or solid - check volume:
    AcDbBody*   body = AcDbBody::cast (acEntity);
    if (nullptr != body)
        {
        double  volume = 0.0;
        return  (AcBr::eOk != m_acBrepEntity->getVolume(volume) || volume < TOLERANCE_ZeroSize);
        }

    return  false;
    }


public:
    AcBRepToParasolid (ConvertToDgnContext* context) : m_toDgnContext(context)
        {
        this->InitParasolidData ();
        if (!PSolidKernelManager::IsSessionStarted())
            {
            PSolidKernelManager::StartSession ();
            m_startedPSolidSession = true;
            }
        }

    ~AcBRepToParasolid ()
        {
        if (NULL != m_acBrepEntity)
            delete m_acBrepEntity;
        if (m_startedPSolidSession)
            PSolidKernelManager::StopSession ();
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert (EditElementHandle& outElement, AcDbEntityP acEntity)
    {
    if (NULL == acEntity)
        return  NullObject;

    RealDwgStatus   status = this->TransformToParasolidSpace (acEntity);

    // transform solid entity to fit in Parasolid model cube:
    if (RealDwgSuccess != status)
        return  status;

    m_acBrepEntity = new AcBrBrep ();
    if (NULL == m_acBrepEntity || AcBr::eOk != m_acBrepEntity->set(*acEntity))
        return  CantConvertBrep;

    // check the input body for sheet/surface type
    m_isSheetBody = this->IsSheetBody (acEntity);

    PK_BODY_create_topology_2_o_t   options;
    PK_BODY_create_topology_2_o_m (options);

    // create either a solid or a sheet body
    options.body_type = m_isSheetBody ? PK_BODY_type_sheet_c : PK_BODY_type_solid_c;

    this->StartDebugReport ();

    // step 1: create Parasolid BRep topology from DWG Brep entity
    PK_BODY_create_topology_2_r_t   results;
    if (RealDwgSuccess == (status = this->CreateTopologyAndGeometry(results, options)))
        {
        // step 2: convert and attach geometry to the Parasolid topology
        if (RealDwgSuccess != (status = this->AttachGeometryToTopology(results.topols, results.n_topols)))
            {
            this->CleanUpParasolid (results, true);
            return  FailuresMonitoring::CantConvertBrepStatus();
            }
        }
    else if (CreatedSheetBodyFromSurface != status)
        {
        this->CleanUpParasolid (results, true);
        return  FailuresMonitoring::CantConvertBrepStatus();
        }

    /*------------------------------------------------------------------------------------------------
    If we have reached here we have successfully created a Parasolid body either from building a full
    topology and attaching geometry, or creating a sheet body directly from a single surface. In either
    case we are ready to clean up Parasolid and create a SmartSolid element from the body.
    ------------------------------------------------------------------------------------------------*/

    // step 3: share Parasolid geometry
    int         nRemoved = 0;
    PK_ERROR_code_t pkError = PK_BODY_share_geom (results.body, true, &nRemoved);

    this->RepairParasolidBody (results.body);

    if (!this->CheckParasolidBody(results.body))
        return  CantConvertBrep;

    // step 4: create an element from the solid body
    status = this->CreateSmartSolidElement(outElement, results.body, acEntity);

    if (RealDwgSuccess != status)
        this->CleanUpParasolid (results, false);
    else
        this->StopDebugReport ();

    return  status;
    }

};  // AcBRepToParasolid



#ifdef BREP_FROM_PARSOLID
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
static const char*  ParameterFormToString (PK_PARAM_form_t paramForm)
    {
    switch (paramForm)
        {
        case PK_PARAM_form_linear_c:        return  "Linear";
        case PK_PARAM_form_circular_c:      return  "Circular";
        default:                            return  "AnyForm";
        }    
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/14
+---------------+---------------+---------------+---------------+---------------+------*/
static const char*  ParameterBoundToString (PK_PARAM_bound_t paramBound)
    {
    switch (paramBound)
        {
        case PK_PARAM_bound_infinite_c:     return  "extends infinitely";
        case PK_PARAM_bound_extendable_c:   return  "may be extended in this direction";
        case PK_PARAM_bound_bound_c:        return  "may not be extended";
        case PK_PARAM_bound_closed_c:       return  "closed ends";
        case PK_PARAM_bound_degenerate_c:   return  "degenerated";
        default:                            return  "unknown";
        }    
    }

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/14
+===============+===============+===============+===============+===============+======*/
class           AcBRepFromParasolid
{
private:
    ConvertFromDgnContext*      m_fromDgnContext;

public:
    AcBRepFromParasolid (ConvertFromDgnContext* context) : m_fromDgnContext(context)
        {
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert (PK_ENTITY_t pkEntity)
    {
    PK_BODY_ask_topology_o_t        options;
    PK_BODY_ask_topology_o_m (options);

    PK_BODY_t           pkBody = pkEntity;
    int                 nTopols = 0, nRelations = 0;
    PK_TOPOL_t*         topols = NULL;
    PK_CLASS_t*         classes;
    int*                parents = NULL;
    int*                children = NULL;
    PK_TOPOL_sense_t*   senses = NULL;

    static bool         s_dumpParasolidBody = false;
    if (s_dumpParasolidBody)
        PK_DEBUG_report_start ("d:\\tmp\\pkbodydump.xml", nullptr);
        
    PK_ERROR_code_t     pkError = PK_BODY_ask_topology (pkBody, &options, &nTopols, &topols, &classes, &nRelations, &parents, &children, &senses);

    if (s_dumpParasolidBody)
        PK_DEBUG_report_stop ();

#ifdef DEBUG_DUMP_TOPOLOGY
    double              scale = 1.0;
    Transform           transform = Transform::FromIdentity();
    UnitDefinition      storageUnit = m_fromDgnContext->GetModel()->GetModelInfo().GetStorageUnit ();
    storageUnit.GetConversionFactorFrom (scale, UnitDefinition::GetStandardUnit(StandardUnit::MetricMeters));
    transform.ScaleMatrixRows (transform, scale, scale, scale);

    for (int i = 0; i < nTopols; i++)
        {
        printf ("%s[%d/%d]: ", ClassToString(classes[i]), i, topols[i]);

        PK_CLASS_t      geomClass = PK_CLASS_null;

        switch (classes[i])
            {
            case PK_CLASS_face:
                {
                PK_SURF_t       surfTag = PK_ENTITY_null;
                if (PK_ERROR_no_errors == (pkError = PK_FACE_ask_surf(topols[i], &surfTag)) && PK_ERROR_no_errors == (pkError = PK_ENTITY_ask_class(surfTag, &geomClass)))
                    {
                    PK_PARAM_sf_s   params[2];
                    PK_PLANE_sf_t   plane;
                    PK_BSURF_sf_t   bsurface;
                    PK_CYL_sf_t     cylinder;
                    PK_TORUS_sf_t   torus;

                    if (PK_CLASS_torus == geomClass && PK_ERROR_no_errors == (pkError = PK_TORUS_ask(surfTag, &torus)))
                        {
                        printf ("Torus, R=%g, r=%g, ", torus.major_radius * scale, torus.minor_radius * scale);
                        DiagnosticPrintAxis2 (torus.basis_set, &transform);
                        }
                    else if (PK_CLASS_plane == geomClass && PK_ERROR_no_errors == (pkError = PK_PLANE_ask(surfTag, &plane)))
                        {
                        printf ("Plane, ");
                        DiagnosticPrintAxis2 (plane.basis_set, &transform);
                        }
                    else if (PK_CLASS_bsurf == geomClass && PK_ERROR_no_errors == (pkError = PK_BSURF_ask(surfTag, &bsurface)))
                        {
                        printf ("BSurface, degrees{%d, %d}, nPoles[%d, %d], nKnots[%d, %d], %s",
                                bsurface.u_degree, bsurface.v_degree, bsurface.n_u_vertices, bsurface.n_v_vertices,
                                bsurface.n_u_knots, bsurface.n_v_knots, bsurface.is_rational ? "rastional" : "non-rational");
                        }
                    else if (PK_CLASS_cyl == geomClass && PK_ERROR_no_errors == (pkError = PK_CYL_ask(surfTag, &cylinder)))
                        {
                        printf ("Cylinder, R=%g, ", cylinder.radius * m_fromDgnContext->GetScaleFromDGN());
                        DiagnosticPrintAxis2 (cylinder.basis_set, &transform);
                        }
                    else if (PK_ERROR_no_errors == (pkError = PK_SURF_ask_params(surfTag, params)))
                        {
                        printf ("Surface= %d, U-%s, V-%s, rangeU=[%g, %g], rangeV=[%g, %g], boundU=[%s, %s], boundV=[%s, %s], U-%s, V-%s", surfTag,
                                ParameterFormToString(params[0].form), ParameterFormToString(params[1].form),
                                params[0].range.value[0], params[0].range.value[1], params[1].range.value[0], params[1].range.value[1],
                                ParameterBoundToString(params[0].bound[0]), ParameterBoundToString(params[0].bound[1]), ParameterBoundToString(params[1].bound[0]), ParameterBoundToString(params[1].bound[1]),
                                params[0].continuous ? "continuous":"discontinuous", params[1].continuous ? "continuous":"discontinuous");
                        }
                    else
                        {
                        printf ("...failed extracting surface!!\n");
                        }
                    }
                break;
                }

            case PK_CLASS_edge:
                {
                PK_CURVE_t      curveTag = PK_ENTITY_null;
                if (PK_ERROR_no_errors == (pkError = PK_EDGE_ask_curve(topols[i], &curveTag)) && PK_ERROR_no_errors == (pkError = PK_ENTITY_ask_class(curveTag, &geomClass)))
                    {
                    PK_VECTOR_t     ends[2];
                    PK_INTERVAL_t   intervals;
                    PK_LOGICAL_t    sense = PK_LOGICAL_true;
                    PK_LINE_sf_t    line;
                    PK_CIRCLE_sf_s  circle;
                    PK_ELLIPSE_sf_s ellipse;

                    if (PK_CLASS_line == geomClass && PK_ERROR_no_errors == (pkError = PK_LINE_ask(curveTag, &line)))
                        {
                        printf ("Line[%d] ", curveTag);
                        DiagnosticPrintAxis1 (line.basis_set, &transform);
                        }
                    else if (PK_CLASS_circle == geomClass && PK_ERROR_no_errors == (pkError = PK_CIRCLE_ask(curveTag, &circle)))
                        {
                        printf ("Circle[%d], R=%g, ", curveTag, circle.radius * scale);
                        DiagnosticPrintAxis2 (circle.basis_set, &transform);
                        }
                    else if (PK_CLASS_ellipse == geomClass && PK_ERROR_no_errors == (pkError = PK_ELLIPSE_ask(curveTag, &ellipse)))
                        {
                        printf ("Ellipse[%d], R=%g, r=%g, ", curveTag, ellipse.R1*scale, ellipse.R2*scale);
                        DiagnosticPrintAxis2 (ellipse.basis_set, &transform);
                        }
                    else if (PK_ERROR_no_errors == (pkError = PK_EDGE_ask_geometry(topols[i], PK_LOGICAL_false, &curveTag, &geomClass, ends, &intervals, &sense)))
                        {
                        DPoint3d    points[2];
                        points[0].InitFromArray (ends[0].coord);
                        points[1].InitFromArray (ends[1].coord);

                        transform.Multiply (points, points, 2);

                        printf ("Curve[%s]= %c%d, p1{%g,%g,%g}, p2{%g,%g,%g}", ClassToString(geomClass), sense ? '+':'-', curveTag,
                                points[0].x, points[0].y, points[0].z, points[1].x, points[1].y, points[1].z);
                        }
                    }
                break;
                }

            case PK_CLASS_vertex:
                {
                PK_POINT_t      pointTag = PK_ENTITY_null;
                PK_POINT_sf_t   point;
                
                if (PK_ERROR_no_errors == (pkError = PK_VERTEX_ask_point(topols[i], &pointTag)) && PK_ERROR_no_errors == (pkError = PK_POINT_ask(pointTag, &point)))
                    {
                    DPoint3d    p = DPoint3d::From (point.position.coord[0], point.position.coord[1], point.position.coord[2]);
                    transform.Multiply (p);
                    printf ("Point[%d], {%.10f,%.10f,%.10f}", pointTag, p.x, p.y, p.z);
                    }
                break;
                }
            }
        printf ("\n");
        }

    for (int i = 0; i < nRelations; i++)
        NOISYDEBUG_PRINTF ("Relation[%d]: parent= %d, child= %c%d\n", i, parents[i], SenseToChar(senses[i]), children[i]);
    NOISYDEBUG_PRINTF ("\n");

#endif  // DEBUG_DUMP_TOPOLOGY

    if (NULL != topols)
        delete topols;
    if (NULL != classes)
        delete classes;
    if (NULL != parents)
        delete parents;
    if (NULL != children)
        delete children;
    if (NULL != senses)
        delete senses;

    return PK_ERROR_no_errors == pkError ? RealDwgSuccess : CantConvertBrep;
    }

};  // AcBRepFromParasolid
#endif  // BREP_FROM_PARSOLID



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CreateElementFromBrep (EditElementHandle& outElement, AcDbEntityP acEntity)
    {
    AcBRepToParasolid   bRep2pSolid (this);
    return  bRep2pSolid.Convert (outElement, acEntity);
    }
