#include "../../include/IExportFormat.h"
#include "../formats/dwg/DWGExporter.h"
#include "../formats/ifc/IFCExporter.h"
#include "../formats/dgn/DGNExporter.h"
#include "../formats/usd/USDExporter.h"

#include <memory>
#include <unordered_map>
#include <functional>

namespace IModelExport {

//=======================================================================================
// Default Export Format Factory Implementation
//=======================================================================================

class DefaultExportFormatFactory : public IExportFormatFactory {
private:
    std::unordered_map<ExportFormat, std::function<std::unique_ptr<IExportFormat>()>> m_factories;

public:
    DefaultExportFormatFactory() {
        RegisterDefaultFactories();
    }

    //===================================================================================
    // Factory Methods
    //===================================================================================

    std::unique_ptr<IDWGExporter> CreateDWGExporter() override {
        return std::make_unique<DWGExporter>();
    }

    std::unique_ptr<IIFCExporter> CreateIFCExporter() override {
        return std::make_unique<IFCExporter>();
    }

    std::unique_ptr<IDGNExporter> CreateDGNExporter() override {
        return std::make_unique<DGNExporter>();
    }

    std::unique_ptr<IUSDExporter> CreateUSDExporter() override {
        return std::make_unique<USDExporter>();
    }

    std::unique_ptr<IExportFormat> CreateExporter(ExportFormat format) override {
        auto it = m_factories.find(format);
        if (it != m_factories.end()) {
            return it->second();
        }
        return nullptr;
    }

    //===================================================================================
    // Registration
    //===================================================================================

    void RegisterExporter(ExportFormat format, std::function<std::unique_ptr<IExportFormat>()> factory) override {
        m_factories[format] = factory;
    }

    bool IsFormatSupported(ExportFormat format) const override {
        return m_factories.find(format) != m_factories.end();
    }

    std::vector<ExportFormat> GetSupportedFormats() const override {
        std::vector<ExportFormat> formats;
        for (const auto& pair : m_factories) {
            formats.push_back(pair.first);
        }
        return formats;
    }

private:
    void RegisterDefaultFactories() {
        // Register DWG exporter
        RegisterExporter(ExportFormat::DWG, []() -> std::unique_ptr<IExportFormat> {
            return std::make_unique<DWGExporter>();
        });

        // Register IFC exporter
        RegisterExporter(ExportFormat::IFC, []() -> std::unique_ptr<IExportFormat> {
            return std::make_unique<IFCExporter>();
        });

        // Register DGN exporter
        RegisterExporter(ExportFormat::DGN, []() -> std::unique_ptr<IExportFormat> {
            return std::make_unique<DGNExporter>();
        });

        // Register USD exporter
        RegisterExporter(ExportFormat::USD, []() -> std::unique_ptr<IExportFormat> {
            return std::make_unique<USDExporter>();
        });
    }
};

//=======================================================================================
// Singleton Factory Access Implementation
//=======================================================================================

std::unique_ptr<IExportFormatFactory> ExportFormatFactory::s_instance = nullptr;

IExportFormatFactory& ExportFormatFactory::Instance() {
    if (!s_instance) {
        s_instance = std::make_unique<DefaultExportFormatFactory>();
    }
    return *s_instance;
}

void ExportFormatFactory::SetInstance(std::unique_ptr<IExportFormatFactory> factory) {
    s_instance = std::move(factory);
}

//=======================================================================================
// Utility Functions Implementation
//=======================================================================================

std::string ToString(ExportFormat format) {
    switch (format) {
        case ExportFormat::DWG: return "DWG";
        case ExportFormat::IFC: return "IFC";
        case ExportFormat::DGN: return "DGN";
        case ExportFormat::USD: return "USD";
        default: return "Unknown";
    }
}

std::string ToString(ExportStatus status) {
    switch (status) {
        case ExportStatus::Success: return "Success";
        case ExportStatus::Warning: return "Warning";
        case ExportStatus::Error: return "Error";
        case ExportStatus::Cancelled: return "Cancelled";
        default: return "Unknown";
    }
}

std::string ToString(ExportLOD lod) {
    switch (lod) {
        case ExportLOD::Low: return "Low";
        case ExportLOD::Medium: return "Medium";
        case ExportLOD::High: return "High";
        case ExportLOD::Custom: return "Custom";
        default: return "Unknown";
    }
}

ExportFormat ParseExportFormat(const std::string& formatStr) {
    std::string upperStr = formatStr;
    std::transform(upperStr.begin(), upperStr.end(), upperStr.begin(), ::toupper);
    
    if (upperStr == "DWG") return ExportFormat::DWG;
    if (upperStr == "IFC") return ExportFormat::IFC;
    if (upperStr == "DGN") return ExportFormat::DGN;
    if (upperStr == "USD" || upperStr == "USDA" || upperStr == "USDC" || upperStr == "USDZ") return ExportFormat::USD;
    
    throw std::invalid_argument("Unknown export format: " + formatStr);
}

std::string GetFileExtension(ExportFormat format) {
    switch (format) {
        case ExportFormat::DWG: return ".dwg";
        case ExportFormat::IFC: return ".ifc";
        case ExportFormat::DGN: return ".dgn";
        case ExportFormat::USD: return ".usd";
        default: return "";
    }
}

std::string GetDefaultFileName(ExportFormat format, const std::string& baseName) {
    return baseName + GetFileExtension(format);
}

//=======================================================================================
// Transform3d Implementation
//=======================================================================================

Transform3d::Transform3d() {
    // Initialize as identity matrix
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            matrix[i][j] = (i == j) ? 1.0 : 0.0;
        }
    }
}

Transform3d Transform3d::Identity() {
    return Transform3d();
}

Transform3d Transform3d::Translation(const Vector3d& translation) {
    Transform3d result;
    result.matrix[0][3] = translation.x;
    result.matrix[1][3] = translation.y;
    result.matrix[2][3] = translation.z;
    return result;
}

Transform3d Transform3d::Rotation(const Vector3d& axis, double angle) {
    Transform3d result;
    
    // Normalize axis
    double length = std::sqrt(axis.x * axis.x + axis.y * axis.y + axis.z * axis.z);
    if (length < 1e-10) {
        return result; // Return identity for zero-length axis
    }
    
    double x = axis.x / length;
    double y = axis.y / length;
    double z = axis.z / length;
    
    double c = std::cos(angle);
    double s = std::sin(angle);
    double t = 1.0 - c;
    
    // Rodrigues' rotation formula
    result.matrix[0][0] = t * x * x + c;
    result.matrix[0][1] = t * x * y - s * z;
    result.matrix[0][2] = t * x * z + s * y;
    
    result.matrix[1][0] = t * x * y + s * z;
    result.matrix[1][1] = t * y * y + c;
    result.matrix[1][2] = t * y * z - s * x;
    
    result.matrix[2][0] = t * x * z - s * y;
    result.matrix[2][1] = t * y * z + s * x;
    result.matrix[2][2] = t * z * z + c;
    
    return result;
}

Transform3d Transform3d::Scale(double scale) {
    Transform3d result;
    result.matrix[0][0] = scale;
    result.matrix[1][1] = scale;
    result.matrix[2][2] = scale;
    return result;
}

//=======================================================================================
// BoundingBox Implementation
//=======================================================================================

bool BoundingBox::IsValid() const {
    return min.x <= max.x && min.y <= max.y && min.z <= max.z;
}

void BoundingBox::Extend(const Point3d& point) {
    if (!IsValid()) {
        min = max = point;
    } else {
        min.x = std::min(min.x, point.x);
        min.y = std::min(min.y, point.y);
        min.z = std::min(min.z, point.z);
        max.x = std::max(max.x, point.x);
        max.y = std::max(max.y, point.y);
        max.z = std::max(max.z, point.z);
    }
}

void BoundingBox::Extend(const BoundingBox& box) {
    if (box.IsValid()) {
        Extend(box.min);
        Extend(box.max);
    }
}

} // namespace IModelExport
