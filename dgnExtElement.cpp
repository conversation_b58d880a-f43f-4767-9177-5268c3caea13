/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnExtElement.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtExtendedElement : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsiclass                                                     Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // do not save xattributes for type-106 elements we cannot roundtrip back:
    bool                saveAppData = context.GetIsSavingApplicationData ();
    if (saveAppData)
        context.SetIsSavingApplicationData (false);

    RealDwgStatus       status = this->SaveToDwg (elemHandle, acObject, existingObject, context);

    // restore user setting
    if (saveAppData)
        context.SetIsSavingApplicationData (true);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsiclass                                                     Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SaveToDwg (ElementHandleR elemHandle, AcDbObjectP& acObject, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    /*-----------------------------------------------------------------------------------
    This is the default handling of type 106 element: try to drop it to components.

    For a renderable type 106, try Parasolid conversion.

    Application should add the ToDwg extension to its element handler to properly save 
    the element to DWG.
    -----------------------------------------------------------------------------------*/
    DisplayHandlerP     displayHandler = elemHandle.GetDisplayHandler ();
    if (NULL == displayHandler)
        return  DropFailed;

    if (context.GetThreeD() && displayHandler->IsRenderable(elemHandle) && RealDwgSuccess == this->SaveElementAsSolidOrMesh(acObject, existingObject, elemHandle, context))
        return  RealDwgSuccess;

    return  this->DropToGeometry (elemHandle, acObject, existingObject, displayHandler, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsiclass                                                     Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropToGeometry (ElementHandleR elemHandle, AcDbObjectP& acObject, AcDbObjectP existingObject, DisplayHandlerP displayHandler, ConvertFromDgnContextR context) const
    {
    DropGeometry        dropGeometry ((DropGeometry::Options)(DropGeometry::OPTION_Complex | DropGeometry::OPTION_AppData));
    AcDbObjectId        blockId;
    if (BSISUCCESS == context.DropElementToBlock(blockId, elemHandle, dropGeometry))
        {
        AcDbBlockReference* insert = new AcDbBlockReference ();
        if (NULL == insert)
            return  OutOfMemoryError;
        if (Acad::eOk != insert->setBlockTableRecord(blockId))
            {
            delete insert;
            return  DropFailed;
            }

        // set the origin at the transformation origin
        DPoint3d            origin = DPoint3d::From (0, 0, 0);
        displayHandler->GetTransformOrigin (elemHandle, origin);
        context.GetTransformFromDGN().Multiply (origin);

        insert->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(origin));

        context.AddEntityToCurrentBlock (insert, elemHandle.GetElementId());

        if (insert->objectId().isValid())
            {
            acObject = insert;
            return  RealDwgSuccess;
            }

        delete insert;
        }

    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveElementAsSolidOrMesh (AcDbObjectP& acObject, AcDbObjectP& existingObject, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    AcRxClass*   typeNeeded = ToDwgExtCone::GetSolidOrSurfaceAcRxClass (true, false, inElement, context.GetSettings());

    if (nullptr == typeNeeded)
        return  NotApplicable;
    else if (AcDbPolyFaceMesh::desc() == typeNeeded)
        return  this->ConvertToMesh (acObject, existingObject, inElement, context);
    else
        return  this->ConvertToSolid (acObject, existingObject, inElement, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToMesh (AcDbObjectP& acObject, AcDbObjectP& existingObject, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbPolyFaceMesh::desc());

    AcDbPolyFaceMesh*   acPolyface = AcDbPolyFaceMesh::cast (acObject);
    if (nullptr == acPolyface)
        return  NotApplicable;

    RealDwgStatus   status = context.SetAcDbPolyfaceMeshFromElement (acPolyface, inElement);
    if (RealDwgSuccess != status)
        return  status;

    if (acPolyface != acObject)
        {
        // a new polyface has replaced existing object - delete the old object
        if (acObject->objectId().isValid())
            {
            DIAGNOSTIC_PRINTF ("Error: input polyface mesh ID=%I64d unexpectedly added in database!\n", inElement.GetElementId());
            acObject->erase ();
            }
        else
            {
            delete acObject;
            }
        acObject = acPolyface;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToSolid (AcDbObjectP& acObject, AcDbObjectP& existingObject, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // do not create a new solid here - a new solid will be created by AcDbBody::acisIn, but try preserve existing solid type:
    acObject = RealDwgUtil::IsObjectAcisType(existingObject) ? existingObject : nullptr;

    AcDbObjectP     newObject = NULL;
    RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, inElement);
    if (RealDwgSuccess == status)
        {
        if (nullptr == acObject)
            acObject = newObject;
        return  status;
        }

    // ACIS type could have been replaced with a different type - a valid status
    if (ReplacedObjectType == status && NULL != newObject)
        {
        if (acObject->objectId().isValid())
            acObject->handOverTo (newObject);
        else if (acObject->isNewObject())
            delete acObject;
        acObject = newObject;
        return  RealDwgSuccess;
        }

    return  status;
    }

};  // ToDwgExtExtendedElement


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtParametricCell : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsiclass                                                     Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // don't bother to drop the cell if it is intentionally set invisible - TFS 686403:
    MSElementCP     elem = elemHandle.GetElementCP ();
    if (nullptr != elem && elem->hdr.dhdr.props.b.invisible)
        return  RealDwgIgnoreElement;

    ParametricCellHandler*  pch = nullptr;
    if (context.GetThreeD() && nullptr != (pch = dynamic_cast<ParametricCellHandler*>(&elemHandle.GetHandler())) && pch->IsRenderable(elemHandle))
        {
        /*-------------------------------------------------------------------------------
        Drop the cell one level to its children to retain correct symbology - TFS 469381.
        Apparently this drop can trigger a deep cloning which adds additional elements in 
        the file cache.  After we have dropped the cell and explicitly saved all children, 
        we will not want these elements to saved then again due the file cache change.
        To workaround this problem we put the newly added cache elements into a no-save 
        list in the file holder. We will later use this list to filter out elements we do
        not want to save.
        -------------------------------------------------------------------------------*/
        ElementId       predropHighestId = context.GetFile()->GetHighestID ();
        ElementAgenda   dropped;
        DropGeometry    dropGeometry ((DropGeometry::Options)(DropGeometry::OPTION_Complex | DropGeometry::OPTION_AppData));

        if (BSISUCCESS == pch->Drop(elemHandle, dropped, dropGeometry))
            {
            size_t      count = 0;

            FOR_EACH (EditElementHandleR child, dropped)
                {
                ToDwgExtension* toDwg = ToDwgExtension::Cast (child.GetHandler());
                if (nullptr == toDwg)
                    {
                    BeAssert (false && L"Missing ToDwgExtension for a dropped element handler!");
                    continue;
                    }

                AcDbObjectP     newObject = nullptr;
                RealDwgStatus   status = toDwg->ToObject (child, newObject, nullptr, context);
                if (RealDwgSuccess != status)
                    {
                    DIAGNOSTIC_PRINTF ("Failed saving dropped element type %d from parametric cell ID=%I64d\n", child.GetElementType(), elemHandle.GetElementId());
                    continue;
                    }

                AcDbEntityP     newEntity = AcDbEntity::cast (newObject);
                if (nullptr == newEntity)
                    {
                    DIAGNOSTIC_PRINTF ("Error on dropped element type %d from parametrc cell ID=%I64d\n", child.GetElementType(), elemHandle.GetElementId());
                    if (nullptr != newObject)
                        delete newObject;
                    continue;
                    }

                // add new entity to database, if not already added
                AcDbObjectId    newId = newEntity->objectId ();
                if (!newId.isValid())
                    {
                    // this is our chance to replace existing object with the first dropped object
                    if (nullptr != existingObject && 0 == count++)
                        existingObject->handOverTo (newEntity);
                    else
                        newId = context.AddEntityToCurrentBlock (newEntity, 0);
                    }

                newEntity->close ();
                }

            // record element ID's cloned by handler's drop operation
            ElementId   postdropHighestId = context.GetFile()->GetHighestID ();
            if (postdropHighestId > predropHighestId)
                context.GetFileHolder().ExcludeElementsFromSaving (predropHighestId + 1, postdropHighestId);

            return  RealDwgSuccess;
            }
        }

    ToDwgExtExtendedElement     type106;
    return type106.ToObject (elemHandle, acObject, existingObject, context);
    }

};  // ToDwgExtParametricCell


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/16
+===============+===============+===============+===============+===============+======*/
struct          ToDwgExtIModelXGraphics : public ToDwgExtExtendedElement
{
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    DisplayHandlerP     displayHandler = elemHandle.GetDisplayHandler ();
    if (nullptr == displayHandler)
        return  DropFailed;

    // treat imodel xgraphics as solids/surfaces and allow them to be dropped as polylines:
    if (this->ShouldDropToWireframe(elemHandle, *displayHandler, context))
        return __super::DropToGeometry (elemHandle, acObject, existingObject, displayHandler, context);

    // drop imodel xgraphics to mesh as Parasolid-ACIS conversion is expensive - TFS 628835:
    return __super::ConvertToMesh (acObject, existingObject, elemHandle, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/19
+---------------+---------------+---------------+---------------+---------------+------*/
bool    ShouldDropToWireframe (ElementHandleCR elemHandle, DisplayHandlerR displayHandler, ConvertFromDgnContextR context) const
    {
    // if user maps solids to wireframe, or the handler says element not renderable, drop it to wireframe:
    if (context.GetSettings().GetSolidSurfaceMapping(false) == SolidSurface_Wireframe || !context.GetThreeD() || !displayHandler.IsRenderable(elemHandle))
        return  true;

    // if the element appears planar, drop to wireframe - TFS 963839:
    ScanRangeCP scanRange = elemHandle.GetIndexRange ();
    DRange3d    range;
    if (nullptr != scanRange)
        DataConvert::ScanRangeToDRange3d (range, *scanRange);
    else
        range.Init ();

    return  range.ZLength() < 5 || range.YLength() < 5 || range.XLength() < 5;
    }
};  // ToDwgExtIModelXGraphics

