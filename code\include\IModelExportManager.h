#pragma once

#include "ExportTypes.h"
#include "IExportFormat.h"
#include <memory>
#include <vector>
#include <future>

namespace IModelExport {

// Forward declarations
class IModelDb;
class ExportContext;

//=======================================================================================
// Main Export Manager Interface
//=======================================================================================

class IModelExportManager {
public:
    virtual ~IModelExportManager() = default;

    //===================================================================================
    // Factory Methods
    //===================================================================================
    
    static std::unique_ptr<IModelExportManager> Create();
    static std::unique_ptr<IModelExportManager> Create(std::shared_ptr<IExportFormatFactory> factory);

    //===================================================================================
    // Configuration
    //===================================================================================

    // Set global export context
    virtual void SetExportContext(std::shared_ptr<ExportContext> context) = 0;
    virtual std::shared_ptr<ExportContext> GetExportContext() const = 0;

    // Format support queries
    virtual bool IsFormatSupported(ExportFormat format) const = 0;
    virtual std::vector<ExportFormat> GetSupportedFormats() const = 0;
    virtual std::string GetFormatInfo(ExportFormat format) const = 0;

    //===================================================================================
    // Synchronous Export Methods
    //===================================================================================

    // Generic export method
    virtual ExportResult Export(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    // Format-specific export methods
    virtual ExportResult ExportToDWG(
        const IModelDb& imodel,
        const DWGExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual ExportResult ExportToIFC(
        const IModelDb& imodel,
        const IFCExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual ExportResult ExportToDGN(
        const IModelDb& imodel,
        const DGNExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual ExportResult ExportToUSD(
        const IModelDb& imodel,
        const USDExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    //===================================================================================
    // Asynchronous Export Methods
    //===================================================================================

    // Generic async export
    virtual std::future<ExportResult> ExportAsync(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    // Format-specific async export methods
    virtual std::future<ExportResult> ExportToDWGAsync(
        const IModelDb& imodel,
        const DWGExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual std::future<ExportResult> ExportToIFCAsync(
        const IModelDb& imodel,
        const IFCExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual std::future<ExportResult> ExportToDGNAsync(
        const IModelDb& imodel,
        const DGNExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    virtual std::future<ExportResult> ExportToUSDAsync(
        const IModelDb& imodel,
        const USDExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    //===================================================================================
    // Batch Export Methods
    //===================================================================================

    struct BatchExportJob {
        ExportFormat format;
        std::unique_ptr<ExportOptions> options;
        std::string outputPath;
        ProgressCallback progressCallback;
    };

    // Export to multiple formats simultaneously
    virtual std::vector<ExportResult> ExportBatch(
        const IModelDb& imodel,
        const std::vector<BatchExportJob>& jobs,
        ProgressCallback overallProgressCallback = nullptr) = 0;

    virtual std::future<std::vector<ExportResult>> ExportBatchAsync(
        const IModelDb& imodel,
        const std::vector<BatchExportJob>& jobs,
        ProgressCallback overallProgressCallback = nullptr) = 0;

    //===================================================================================
    // Validation and Preprocessing
    //===================================================================================

    // Validate export options before export
    virtual bool ValidateExportOptions(
        ExportFormat format,
        const ExportOptions& options,
        std::vector<std::string>& errors) const = 0;

    // Analyze iModel for export compatibility
    virtual bool AnalyzeIModel(
        const IModelDb& imodel,
        ExportFormat format,
        std::vector<std::string>& warnings,
        std::vector<std::string>& errors) const = 0;

    // Get export statistics without performing export
    virtual bool GetExportStatistics(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        size_t& estimatedElements,
        size_t& estimatedFileSize,
        double& estimatedTime) const = 0;

    //===================================================================================
    // Progress and Cancellation
    //===================================================================================

    // Cancel all active exports
    virtual void CancelAllExports() = 0;

    // Cancel specific export by ID
    virtual bool CancelExport(const std::string& exportId) = 0;

    // Get active export status
    virtual std::vector<std::string> GetActiveExportIds() const = 0;
    virtual bool GetExportProgress(const std::string& exportId, ExportProgress& progress) const = 0;

    //===================================================================================
    // Configuration and Settings
    //===================================================================================

    // Global settings
    virtual void SetMaxConcurrentExports(size_t maxExports) = 0;
    virtual size_t GetMaxConcurrentExports() const = 0;

    virtual void SetDefaultTempDirectory(const std::string& tempDir) = 0;
    virtual std::string GetDefaultTempDirectory() const = 0;

    virtual void EnableLogging(bool enable, const std::string& logFile = "") = 0;
    virtual bool IsLoggingEnabled() const = 0;

    //===================================================================================
    // Error Handling and Diagnostics
    //===================================================================================

    // Get last error information
    virtual std::string GetLastError() const = 0;
    virtual std::vector<std::string> GetRecentErrors() const = 0;

    // Clear error history
    virtual void ClearErrorHistory() = 0;

    // Export diagnostics
    virtual void EnableDiagnostics(bool enable) = 0;
    virtual bool IsDiagnosticsEnabled() const = 0;
    virtual std::string GetDiagnosticsReport() const = 0;
};

//===================================================================================
// Convenience Functions
//===================================================================================

// Quick export functions for common scenarios
namespace QuickExport {

    // Export to single format with default options
    ExportResult ToDWG(const IModelDb& imodel, const std::string& outputPath);
    ExportResult ToIFC(const IModelDb& imodel, const std::string& outputPath);
    ExportResult ToDGN(const IModelDb& imodel, const std::string& outputPath);
    ExportResult ToUSD(const IModelDb& imodel, const std::string& outputPath);

    // Export to all supported formats
    std::vector<ExportResult> ToAllFormats(const IModelDb& imodel, const std::string& outputDirectory);

    // Export with progress dialog (platform-specific implementation)
    ExportResult WithProgressDialog(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        void* parentWindow = nullptr);

} // namespace QuickExport

} // namespace IModelExport
