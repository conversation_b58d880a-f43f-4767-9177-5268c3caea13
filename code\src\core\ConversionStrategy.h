#pragma once

#include "../../include/ExportTypes.h"
#include "GeometryProcessor.h"
#include <memory>
#include <functional>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class ExportContext;
class IModelDb;

//=======================================================================================
// Conversion Strategy Manager - Manages format-specific conversion strategies
//=======================================================================================

class ConversionStrategy {
public:
    ConversionStrategy(ExportFormat targetFormat, std::shared_ptr<ExportContext> context);
    virtual ~ConversionStrategy() = default;

    //===================================================================================
    // Strategy Interface
    //===================================================================================

    // Get target format
    ExportFormat GetTargetFormat() const { return m_targetFormat; }
    
    // Get strategy name
    virtual std::string GetStrategyName() const = 0;
    
    // Get strategy description
    virtual std::string GetStrategyDescription() const = 0;
    
    // Check if strategy supports element type
    virtual bool SupportsElementType(ElementType elementType) const = 0;
    
    // Check if strategy supports geometry type
    virtual bool SupportsGeometryType(GeometryData::Type geometryType) const = 0;

    //===================================================================================
    // Element Conversion
    //===================================================================================

    // Convert element to target format
    virtual bool ConvertElement(const ElementInfo& element, 
                               std::vector<std::shared_ptr<void>>& outputEntities) = 0;
    
    // Convert geometry to target format
    virtual bool ConvertGeometry(const GeometryData& geometry,
                                std::shared_ptr<void>& outputEntity) = 0;
    
    // Convert material to target format
    virtual bool ConvertMaterial(const Material& material,
                                std::shared_ptr<void>& outputMaterial) = 0;
    
    // Convert properties to target format
    virtual bool ConvertProperties(const std::unordered_map<std::string, std::string>& properties,
                                  std::unordered_map<std::string, std::string>& outputProperties) = 0;

    //===================================================================================
    // Strategy Configuration
    //===================================================================================

    // Strategy parameters
    struct StrategyParams {
        double geometryTolerance = 1e-6;
        double angleTolerance = 1e-6;
        bool preserveHierarchy = true;
        bool optimizeGeometry = true;
        bool validateOutput = true;
        std::unordered_map<std::string, std::string> customParams;
    };

    virtual void SetStrategyParams(const StrategyParams& params);
    virtual StrategyParams GetStrategyParams() const;

    //===================================================================================
    // Validation and Analysis
    //===================================================================================

    // Validate element for conversion
    virtual bool ValidateElement(const ElementInfo& element, 
                                std::vector<std::string>& errors) const = 0;
    
    // Analyze conversion complexity
    virtual size_t AnalyzeComplexity(const ElementInfo& element) const = 0;
    
    // Estimate conversion time
    virtual double EstimateConversionTime(const ElementInfo& element) const = 0;
    
    // Get conversion statistics
    virtual std::unordered_map<std::string, size_t> GetConversionStatistics() const = 0;

protected:
    ExportFormat m_targetFormat;
    std::shared_ptr<ExportContext> m_context;
    StrategyParams m_params;
    
    // Statistics tracking
    mutable std::unordered_map<std::string, size_t> m_statistics;
    
    // Helper methods
    void IncrementStatistic(const std::string& key) const;
    void LogConversionEvent(const std::string& message) const;
};

//=======================================================================================
// DWG Conversion Strategy
//=======================================================================================

class DWGConversionStrategy : public ConversionStrategy {
public:
    DWGConversionStrategy(std::shared_ptr<ExportContext> context);
    ~DWGConversionStrategy() override = default;

    // Strategy interface
    std::string GetStrategyName() const override { return "DWG Conversion Strategy"; }
    std::string GetStrategyDescription() const override { return "Converts iModel elements to AutoCAD DWG format"; }
    
    bool SupportsElementType(ElementType elementType) const override;
    bool SupportsGeometryType(GeometryData::Type geometryType) const override;

    // Conversion methods
    bool ConvertElement(const ElementInfo& element, 
                       std::vector<std::shared_ptr<void>>& outputEntities) override;
    bool ConvertGeometry(const GeometryData& geometry,
                        std::shared_ptr<void>& outputEntity) override;
    bool ConvertMaterial(const Material& material,
                        std::shared_ptr<void>& outputMaterial) override;
    bool ConvertProperties(const std::unordered_map<std::string, std::string>& properties,
                          std::unordered_map<std::string, std::string>& outputProperties) override;

    // Validation and analysis
    bool ValidateElement(const ElementInfo& element, 
                        std::vector<std::string>& errors) const override;
    size_t AnalyzeComplexity(const ElementInfo& element) const override;
    double EstimateConversionTime(const ElementInfo& element) const override;
    std::unordered_map<std::string, size_t> GetConversionStatistics() const override;

private:
    // DWG-specific conversion methods
    bool ConvertLineGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    bool ConvertArcGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    bool ConvertPolylineGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    bool ConvertSplineGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    bool ConvertSolidGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    bool ConvertMeshGeometry(const GeometryData& geometry, std::shared_ptr<void>& entity);
    
    // Layer and block management
    std::string GetOrCreateLayer(const ElementInfo& element);
    std::string GetOrCreateBlock(const ElementInfo& element);
    
    // Property mapping
    std::unordered_map<std::string, std::string> MapPropertiesToDWG(
        const std::unordered_map<std::string, std::string>& properties);
};

//=======================================================================================
// IFC Conversion Strategy
//=======================================================================================

class IFCConversionStrategy : public ConversionStrategy {
public:
    IFCConversionStrategy(std::shared_ptr<ExportContext> context);
    ~IFCConversionStrategy() override = default;

    // Strategy interface
    std::string GetStrategyName() const override { return "IFC Conversion Strategy"; }
    std::string GetStrategyDescription() const override { return "Converts iModel elements to IFC format"; }
    
    bool SupportsElementType(ElementType elementType) const override;
    bool SupportsGeometryType(GeometryData::Type geometryType) const override;

    // Conversion methods
    bool ConvertElement(const ElementInfo& element, 
                       std::vector<std::shared_ptr<void>>& outputEntities) override;
    bool ConvertGeometry(const GeometryData& geometry,
                        std::shared_ptr<void>& outputEntity) override;
    bool ConvertMaterial(const Material& material,
                        std::shared_ptr<void>& outputMaterial) override;
    bool ConvertProperties(const std::unordered_map<std::string, std::string>& properties,
                          std::unordered_map<std::string, std::string>& outputProperties) override;

    // Validation and analysis
    bool ValidateElement(const ElementInfo& element, 
                        std::vector<std::string>& errors) const override;
    size_t AnalyzeComplexity(const ElementInfo& element) const override;
    double EstimateConversionTime(const ElementInfo& element) const override;
    std::unordered_map<std::string, size_t> GetConversionStatistics() const override;

private:
    // IFC-specific conversion methods
    bool ConvertBuildingElement(const ElementInfo& element, std::shared_ptr<void>& entity);
    bool ConvertSpatialElement(const ElementInfo& element, std::shared_ptr<void>& entity);
    bool ConvertStructuralElement(const ElementInfo& element, std::shared_ptr<void>& entity);
    bool ConvertMEPElement(const ElementInfo& element, std::shared_ptr<void>& entity);
    
    // IFC geometry creation
    std::shared_ptr<void> CreateIfcExtrudedAreaSolid(const GeometryData& geometry);
    std::shared_ptr<void> CreateIfcSweptSolid(const GeometryData& geometry);
    std::shared_ptr<void> CreateIfcBRep(const GeometryData& geometry);
    
    // IFC property sets
    std::shared_ptr<void> CreateIfcPropertySet(const std::string& name,
        const std::unordered_map<std::string, std::string>& properties);
    
    // IFC relationships
    bool CreateIfcRelationships(const ElementInfo& element, std::shared_ptr<void>& entity);
};

//=======================================================================================
// USD Conversion Strategy
//=======================================================================================

class USDConversionStrategy : public ConversionStrategy {
public:
    USDConversionStrategy(std::shared_ptr<ExportContext> context);
    ~USDConversionStrategy() override = default;

    // Strategy interface
    std::string GetStrategyName() const override { return "USD Conversion Strategy"; }
    std::string GetStrategyDescription() const override { return "Converts iModel elements to USD format"; }
    
    bool SupportsElementType(ElementType elementType) const override;
    bool SupportsGeometryType(GeometryData::Type geometryType) const override;

    // Conversion methods
    bool ConvertElement(const ElementInfo& element, 
                       std::vector<std::shared_ptr<void>>& outputEntities) override;
    bool ConvertGeometry(const GeometryData& geometry,
                        std::shared_ptr<void>& outputEntity) override;
    bool ConvertMaterial(const Material& material,
                        std::shared_ptr<void>& outputMaterial) override;
    bool ConvertProperties(const std::unordered_map<std::string, std::string>& properties,
                          std::unordered_map<std::string, std::string>& outputProperties) override;

    // Validation and analysis
    bool ValidateElement(const ElementInfo& element, 
                        std::vector<std::string>& errors) const override;
    size_t AnalyzeComplexity(const ElementInfo& element) const override;
    double EstimateConversionTime(const ElementInfo& element) const override;
    std::unordered_map<std::string, size_t> GetConversionStatistics() const override;

private:
    // USD-specific conversion methods
    bool ConvertToUsdMesh(const GeometryData& geometry, std::shared_ptr<void>& prim);
    bool ConvertToUsdCurves(const GeometryData& geometry, std::shared_ptr<void>& prim);
    bool ConvertToUsdPoints(const GeometryData& geometry, std::shared_ptr<void>& prim);
    
    // USD scene graph management
    std::string CreateUsdPrimPath(const ElementInfo& element);
    bool CreateUsdHierarchy(const ElementInfo& element);
    
    // USD material binding
    bool BindMaterialToPrim(std::shared_ptr<void>& prim, std::shared_ptr<void>& material);
    
    // USD variants and LOD
    bool CreateLODVariants(const ElementInfo& element, std::shared_ptr<void>& prim);
};

//=======================================================================================
// Strategy Factory and Manager
//=======================================================================================

class ConversionStrategyFactory {
public:
    // Create strategy for target format
    static std::unique_ptr<ConversionStrategy> CreateStrategy(
        ExportFormat targetFormat, std::shared_ptr<ExportContext> context);
    
    // Register custom strategy
    static void RegisterStrategy(ExportFormat format, 
        std::function<std::unique_ptr<ConversionStrategy>(std::shared_ptr<ExportContext>)> factory);
    
    // Get supported formats
    static std::vector<ExportFormat> GetSupportedFormats();
    
    // Check if format is supported
    static bool IsFormatSupported(ExportFormat format);

private:
    static std::unordered_map<ExportFormat, 
        std::function<std::unique_ptr<ConversionStrategy>(std::shared_ptr<ExportContext>)>> s_strategies;
};

class ConversionStrategyManager {
public:
    ConversionStrategyManager(std::shared_ptr<ExportContext> context);
    ~ConversionStrategyManager() = default;

    // Strategy management
    bool SetStrategy(ExportFormat format);
    std::shared_ptr<ConversionStrategy> GetStrategy() const;
    std::shared_ptr<ConversionStrategy> GetStrategy(ExportFormat format);
    
    // Multi-format conversion
    bool ConvertElement(const ElementInfo& element, ExportFormat targetFormat,
                       std::vector<std::shared_ptr<void>>& outputEntities);
    
    // Strategy analysis
    std::vector<ExportFormat> GetCompatibleFormats(const ElementInfo& element) const;
    std::unordered_map<ExportFormat, double> EstimateConversionTimes(const ElementInfo& element) const;
    
    // Strategy optimization
    ExportFormat RecommendOptimalFormat(const std::vector<ElementInfo>& elements) const;
    ConversionStrategy::StrategyParams OptimizeStrategyParams(
        ExportFormat format, const std::vector<ElementInfo>& elements) const;

private:
    std::shared_ptr<ExportContext> m_context;
    std::unordered_map<ExportFormat, std::shared_ptr<ConversionStrategy>> m_strategies;
    std::shared_ptr<ConversionStrategy> m_currentStrategy;
};

//=======================================================================================
// Strategy Utilities
//=======================================================================================

namespace StrategyUtils {
    
    // Analyze element compatibility with formats
    std::unordered_map<ExportFormat, double> AnalyzeElementCompatibility(
        const ElementInfo& element);
    
    // Calculate conversion quality score
    double CalculateConversionQuality(const ElementInfo& element, ExportFormat format);
    
    // Generate conversion recommendations
    std::vector<std::string> GenerateConversionRecommendations(
        const std::vector<ElementInfo>& elements, ExportFormat targetFormat);
    
    // Compare conversion strategies
    std::string CompareConversionStrategies(
        const std::vector<ElementInfo>& elements,
        const std::vector<ExportFormat>& formats);
    
    // Optimize conversion parameters
    ConversionStrategy::StrategyParams OptimizeConversionParameters(
        const std::vector<ElementInfo>& elements, ExportFormat format);
}

} // namespace IModelExport
