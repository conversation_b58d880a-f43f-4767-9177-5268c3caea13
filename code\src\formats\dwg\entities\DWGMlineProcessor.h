#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/geplane.h>
#endif

namespace IModelExport {

//=======================================================================================
// Multiline Data Structures (Based on RealDwgFileIO rdMline.cpp)
//=======================================================================================

struct MlineElement {
    double offset = 0.0;                // Offset from centerline
    Color color = Color(1.0f, 1.0f, 1.0f, 1.0f); // Element color
    std::string lineType = "Continuous"; // Line type name
    
    bool IsValid() const {
        return std::isfinite(offset);
    }
};

struct MlineStyle {
    std::string name = "Standard";      // Style name
    std::string description;            // Style description
    
    // Style properties
    bool fillOn = false;                // Fill is on
    Color fillColor = Color(1.0f, 1.0f, 1.0f, 1.0f); // Fill color
    
    bool showMiters = true;             // Show miters
    bool startSquareEnd = false;        // Square start end
    bool startInnerArcs = false;        // Start inner arcs
    bool startRoundEnd = false;         // Round start end
    bool endSquareEnd = false;          // Square end end
    bool endInnerArcs = false;          // End inner arcs
    bool endRoundEnd = false;           // Round end end
    
    // Multiline elements
    std::vector<MlineElement> elements;
    
    bool IsValid() const;
    size_t GetElementCount() const { return elements.size(); }
    double GetTotalWidth() const;
    MlineElement GetElement(size_t index) const;
};

struct MlineVertex {
    Point3d position;                   // Vertex position
    Vector3d direction;                 // Direction at vertex
    Vector3d miterDirection;            // Miter direction
    
    // Vertex properties
    std::vector<double> segmentParameters; // Segment parameters for each element
    
    bool IsValid() const;
};

struct MlineGeometry {
    std::vector<MlineVertex> vertices;  // Multiline vertices
    Vector3d normal = Vector3d(0, 0, 1); // Multiline normal
    
    // Multiline properties
    std::string styleName = "Standard"; // Multiline style name
    double scale = 1.0;                 // Scale factor
    
    enum class Justification {
        Top,                            // Top justification
        Zero,                           // Zero (center) justification
        Bottom                          // Bottom justification
    } justification = Justification::Zero;
    
    // Multiline flags
    bool isClosed = false;              // Multiline is closed
    bool suppressStartCaps = false;     // Suppress start caps
    bool suppressEndCaps = false;       // Suppress end caps
    
    bool IsValid() const;
    size_t GetVertexCount() const { return vertices.size(); }
    double CalculateLength() const;
    BoundingBox3D CalculateBounds() const;
};

//=======================================================================================
// Multiline Validation Results
//=======================================================================================

struct MlineValidationResult : public DWGValidationResult {
    bool hasValidVertices = false;
    bool hasValidStyle = false;
    bool hasValidScale = false;
    bool hasValidNormal = false;
    int invalidVertexCount = 0;
    double totalLength = 0.0;
    
    void AddMlineError(const std::string& error) {
        AddError("Mline: " + error);
    }
    
    void AddMlineWarning(const std::string& warning) {
        AddWarning("Mline: " + warning);
    }
};

struct MlineStyleValidationResult : public DWGValidationResult {
    bool hasValidName = false;
    bool hasValidElements = false;
    bool hasValidColors = false;
    bool hasValidLineTypes = false;
    int elementCount = 0;
    double totalWidth = 0.0;
    
    void AddStyleError(const std::string& error) {
        AddError("MlineStyle: " + error);
    }
    
    void AddStyleWarning(const std::string& warning) {
        AddWarning("MlineStyle: " + warning);
    }
};

//=======================================================================================
// DWG Multiline Processor (Based on RealDwgFileIO rdMline.cpp)
//=======================================================================================

class DWGMlineProcessor : public DWGEntityProcessor {
public:
    DWGMlineProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGMlineProcessor"; }

    // Multiline processing methods
    DWGProcessingStatus ProcessMline(const MlineGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessMlineStyle(const MlineStyle& style);

    // Validation methods
    MlineValidationResult ValidateMlineGeometry(const MlineGeometry& geometry) const;
    MlineStyleValidationResult ValidateMlineStyle(const MlineStyle& style) const;
    bool ValidateMlineVertices(const std::vector<MlineVertex>& vertices) const;
    bool ValidateMlineElements(const std::vector<MlineElement>& elements) const;
    bool ValidateMlineScale(double scale) const;
    bool ValidateMlineNormal(const Vector3d& normal) const;

    // Multiline style management
    bool CreateMlineStyle(const MlineStyle& style);
    bool UpdateMlineStyle(const MlineStyle& style);
    bool DeleteMlineStyle(const std::string& styleName);
    bool MlineStyleExists(const std::string& styleName) const;
    MlineStyle GetMlineStyle(const std::string& styleName) const;
    std::vector<std::string> GetAvailableMlineStyles() const;

    // Multiline geometry processing
    bool ProcessMlineVertices(const MlineGeometry& geometry) const;
    bool ProcessMlineSegments(const MlineGeometry& geometry) const;
    bool ProcessMlineCaps(const MlineGeometry& geometry) const;
    bool ProcessMlineFill(const MlineGeometry& geometry) const;

    // Multiline calculations
    std::vector<Point3d> CalculateElementLine(const MlineGeometry& geometry, size_t elementIndex) const;
    std::vector<Point3d> CalculateSegmentPoints(const MlineVertex& start, const MlineVertex& end, 
                                               const MlineElement& element, double scale) const;
    Vector3d CalculateMiterDirection(const MlineVertex& vertex, const MlineVertex& prevVertex, 
                                    const MlineVertex& nextVertex) const;
    double CalculateElementOffset(const MlineElement& element, double scale, 
                                 MlineGeometry::Justification justification) const;

    // Multiline optimization and repair
    bool RepairMlineGeometry(MlineGeometry& geometry) const;
    bool RepairMlineStyle(MlineStyle& style) const;
    bool OptimizeMlineVertices(MlineGeometry& geometry) const;
    bool SimplifyMlineGeometry(MlineGeometry& geometry, double tolerance = 1e-6) const;
    bool ValidateAndFixMlineVertices(std::vector<MlineVertex>& vertices) const;

    // Multiline conversion
    bool ConvertToPolylines(const MlineGeometry& geometry, std::vector<std::vector<Point3d>>& polylines) const;
    bool ConvertToRegion(const MlineGeometry& geometry, std::vector<Point3d>& boundary) const;
    bool ExplodeMline(const MlineGeometry& geometry, std::vector<std::vector<Point3d>>& lines) const;

private:
    // Multiline style storage
    std::unordered_map<std::string, MlineStyle> m_mlineStyles;
    
    // Vertex processing helpers
    bool ProcessMlineVertex(const MlineVertex& vertex, size_t index, const MlineGeometry& geometry) const;
    bool ValidateMlineVertex(const MlineVertex& vertex) const;
    bool CalculateVertexDirections(MlineVertex& vertex, const MlineVertex& prevVertex, 
                                  const MlineVertex& nextVertex) const;
    
    // Segment processing helpers
    bool ProcessMlineSegment(const MlineVertex& start, const MlineVertex& end, 
                           const MlineGeometry& geometry) const;
    bool CalculateSegmentGeometry(const MlineVertex& start, const MlineVertex& end, 
                                const MlineElement& element, std::vector<Point3d>& points) const;
    
    // Element processing helpers
    bool ProcessMlineElement(const MlineElement& element, size_t elementIndex, 
                           const MlineGeometry& geometry) const;
    bool ValidateMlineElement(const MlineElement& element) const;
    Color GetElementColor(const MlineElement& element) const;
    std::string GetElementLineType(const MlineElement& element) const;
    
    // Cap processing helpers
    bool ProcessStartCaps(const MlineGeometry& geometry) const;
    bool ProcessEndCaps(const MlineGeometry& geometry) const;
    bool ProcessSquareCap(const MlineVertex& vertex, const MlineGeometry& geometry, bool isStart) const;
    bool ProcessRoundCap(const MlineVertex& vertex, const MlineGeometry& geometry, bool isStart) const;
    bool ProcessInnerArcs(const MlineVertex& vertex, const MlineGeometry& geometry, bool isStart) const;
    
    // Fill processing helpers
    bool ProcessMlineFillRegion(const MlineGeometry& geometry) const;
    std::vector<Point3d> CalculateFillBoundary(const MlineGeometry& geometry) const;
    bool ValidateFillBoundary(const std::vector<Point3d>& boundary) const;
    
    // Geometric calculations
    Point3d CalculateOffsetPoint(const Point3d& basePoint, const Vector3d& direction, 
                                const Vector3d& normal, double offset) const;
    Vector3d CalculatePerpendicularVector(const Vector3d& direction, const Vector3d& normal) const;
    double CalculateAngleBetweenVectors(const Vector3d& v1, const Vector3d& v2) const;
    bool CalculateLineIntersection(const Point3d& p1, const Vector3d& d1, 
                                  const Point3d& p2, const Vector3d& d2, Point3d& intersection) const;
    
    // Style processing helpers
    bool ValidateMlineStyleName(const std::string& name) const;
    std::string SanitizeMlineStyleName(const std::string& name) const;
    bool ValidateElementOffsets(const std::vector<MlineElement>& elements) const;
    bool SortElementsByOffset(std::vector<MlineElement>& elements) const;
    
    // Transformation helpers
    void TransformMlineGeometry(MlineGeometry& geometry) const;
    void TransformMlineVertex(MlineVertex& vertex) const;
    Point3d TransformMlinePoint(const Point3d& point, const MlineGeometry& geometry) const;
    Vector3d TransformMlineVector(const Vector3d& vector, const MlineGeometry& geometry) const;
    
    // Optimization helpers
    bool RemoveRedundantVertices(std::vector<MlineVertex>& vertices, double tolerance = 1e-6) const;
    bool MergeCollinearSegments(std::vector<MlineVertex>& vertices, double tolerance = 1e-6) const;
    bool SimplifyMlineElements(std::vector<MlineElement>& elements) const;
    
    // Validation helpers
    bool ValidateVertexSequence(const std::vector<MlineVertex>& vertices) const;
    bool ValidateElementSequence(const std::vector<MlineElement>& elements) const;
    bool ValidateMlineTopology(const MlineGeometry& geometry) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbMline* CreateDWGMline(const MlineGeometry& geometry) const;
    AcDbMlineStyle* CreateDWGMlineStyle(const MlineStyle& style) const;
    
    // Multiline creation helpers
    bool SetMlineVertices(AcDbMline* mline, const std::vector<MlineVertex>& vertices) const;
    bool SetMlineStyle(AcDbMline* mline, const std::string& styleName) const;
    bool SetMlineProperties(AcDbMline* mline, const MlineGeometry& geometry) const;
    
    // Style creation helpers
    bool SetMlineStyleElements(AcDbMlineStyle* style, const std::vector<MlineElement>& elements) const;
    bool SetMlineStyleProperties(AcDbMlineStyle* style, const MlineStyle& styleData) const;
    bool AddMlineStyleToDatabase(AcDbMlineStyle* style, const std::string& name) const;
    
    // Vertex processing helpers
    bool AddMlineVertex(AcDbMline* mline, const MlineVertex& vertex) const;
    bool SetVertexDirection(AcDbMline* mline, int vertexIndex, const Vector3d& direction) const;
    bool SetVertexMiter(AcDbMline* mline, int vertexIndex, const Vector3d& miter) const;
    
    // Property setting helpers
    bool SetMlineJustification(AcDbMline* mline, MlineGeometry::Justification justification) const;
    bool SetMlineScale(AcDbMline* mline, double scale) const;
    bool SetMlineNormal(AcDbMline* mline, const Vector3d& normal) const;
    
    // Error handling for RealDWG operations
    bool HandleMlineCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedMlines = 0;
    mutable size_t m_processedStyles = 0;
    mutable size_t m_processedVertices = 0;
    mutable size_t m_processedElements = 0;
    mutable size_t m_repairedMlines = 0;
    mutable size_t m_optimizedMlines = 0;
    
    // Configuration
    double m_mlineTolerance = 1e-6;
    double m_vertexTolerance = 1e-10;
    double m_angleTolerance = 1e-8;
    double m_simplificationTolerance = 1e-6;
    bool m_enableMlineOptimization = true;
    bool m_enableStyleValidation = true;
    bool m_autoRepairMlines = true;
    int m_maxVertices = 10000;
    int m_maxElements = 100;
    double m_minScale = 0.001;
    double m_maxScale = 1000.0;
    
    // Default styles
    void CreateDefaultMlineStyles();
    MlineStyle CreateStandardStyle() const;
    MlineStyle CreateDoubleLineStyle() const;
    MlineStyle CreateTripleLineStyle() const;
};

//=======================================================================================
// Multiline Utility Functions
//=======================================================================================

class MlineUtils {
public:
    // Style utilities
    static MlineStyle CreateSimpleStyle(const std::string& name, double width, int elementCount = 2);
    static bool ValidateStyleName(const std::string& name);
    static std::string GenerateUniqueStyleName(const std::string& baseName);
    
    // Geometry utilities
    static double CalculateMlineLength(const MlineGeometry& geometry);
    static BoundingBox3D CalculateMlineBounds(const MlineGeometry& geometry);
    static bool IsMlineClosed(const MlineGeometry& geometry, double tolerance = 1e-6);
    
    // Element utilities
    static bool SortElementsByOffset(std::vector<MlineElement>& elements);
    static double CalculateElementSpacing(const std::vector<MlineElement>& elements);
    static bool ValidateElementOffsets(const std::vector<MlineElement>& elements);
    
    // Vertex utilities
    static bool ValidateVertexDirection(const Vector3d& direction);
    static Vector3d CalculateVertexDirection(const Point3d& prev, const Point3d& current, const Point3d& next);
    static Vector3d CalculateMiterDirection(const Vector3d& inDirection, const Vector3d& outDirection);
    
    // Conversion utilities
    static bool ConvertMlineToPolylines(const MlineGeometry& geometry, std::vector<std::vector<Point3d>>& polylines);
    static bool ConvertPolylineToMline(const std::vector<Point3d>& polyline, MlineGeometry& geometry, const std::string& styleName = "Standard");
    
    // Optimization utilities
    static bool OptimizeMlineVertices(std::vector<MlineVertex>& vertices, double tolerance = 1e-6);
    static bool SimplifyMlineGeometry(MlineGeometry& geometry, double tolerance = 1e-6);
    static bool RemoveDuplicateVertices(std::vector<MlineVertex>& vertices, double tolerance = 1e-6);
};

} // namespace IModelExport
