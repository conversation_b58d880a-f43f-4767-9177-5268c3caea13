#include "DWGImageProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbrasterimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gematrix3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <filesystem>
#include <fstream>

namespace IModelExport {

//=======================================================================================
// ImageGeometry Implementation
//=======================================================================================

bool ImageGeometry::IsValid() const {
    // Validate file path
    if (imagePath.empty() || imageFileName.empty()) {
        return false;
    }
    
    // Validate position and vectors
    if (!std::isfinite(insertionPoint.x) || !std::isfinite(insertionPoint.y) || !std::isfinite(insertionPoint.z) ||
        !std::isfinite(uVector.x) || !std::isfinite(uVector.y) || !std::isfinite(uVector.z) ||
        !std::isfinite(vVector.x) || !std::isfinite(vVector.y) || !std::isfinite(vVector.z)) {
        return false;
    }
    
    // Validate dimensions
    if (!std::isfinite(imageWidth) || !std::isfinite(imageHeight) || 
        imageWidth <= 0.0 || imageHeight <= 0.0) {
        return false;
    }
    
    // Validate pixel dimensions
    if (pixelWidth <= 0 || pixelHeight <= 0) {
        return false;
    }
    
    // Validate brightness, contrast, and fade
    if (!std::isfinite(brightness) || !std::isfinite(contrast) || !std::isfinite(fade) ||
        brightness < -100.0 || brightness > 100.0 ||
        contrast < -100.0 || contrast > 100.0 ||
        fade < 0.0 || fade > 100.0) {
        return false;
    }
    
    return true;
}

double ImageGeometry::GetAspectRatio() const {
    return (imageHeight > 0.0) ? (imageWidth / imageHeight) : 1.0;
}

double ImageGeometry::GetPixelAspectRatio() const {
    return (pixelHeight > 0) ? (static_cast<double>(pixelWidth) / pixelHeight) : 1.0;
}

BoundingBox3D ImageGeometry::CalculateBounds() const {
    BoundingBox3D bounds;
    
    // Calculate four corners of the image
    Point3d corner1 = insertionPoint;
    Point3d corner2(insertionPoint.x + uVector.x, insertionPoint.y + uVector.y, insertionPoint.z + uVector.z);
    Point3d corner3(insertionPoint.x + uVector.x + vVector.x, insertionPoint.y + uVector.y + vVector.y, insertionPoint.z + uVector.z + vVector.z);
    Point3d corner4(insertionPoint.x + vVector.x, insertionPoint.y + vVector.y, insertionPoint.z + vVector.z);
    
    bounds.AddPoint(corner1);
    bounds.AddPoint(corner2);
    bounds.AddPoint(corner3);
    bounds.AddPoint(corner4);
    
    return bounds;
}

//=======================================================================================
// UnderlayGeometry Implementation
//=======================================================================================

bool UnderlayGeometry::IsValid() const {
    // Validate file path
    if (filePath.empty() || fileName.empty()) {
        return false;
    }
    
    // Validate position and vectors
    if (!std::isfinite(insertionPoint.x) || !std::isfinite(insertionPoint.y) || !std::isfinite(insertionPoint.z) ||
        !std::isfinite(uVector.x) || !std::isfinite(uVector.y) || !std::isfinite(uVector.z) ||
        !std::isfinite(vVector.x) || !std::isfinite(vVector.y) || !std::isfinite(vVector.z)) {
        return false;
    }
    
    // Validate scale and rotation
    if (!std::isfinite(scaleX) || !std::isfinite(scaleY) || !std::isfinite(rotation) ||
        scaleX <= 0.0 || scaleY <= 0.0) {
        return false;
    }
    
    // Validate fade and contrast
    if (!std::isfinite(fade) || !std::isfinite(contrast) ||
        fade < 0.0 || fade > 100.0 ||
        contrast < 0.0 || contrast > 100.0) {
        return false;
    }
    
    // Type-specific validation
    switch (type) {
        case Type::PDF:
            return pdf.pageNumber > 0;
        case Type::DWF:
            return !dwf.sheetName.empty();
        case Type::DGN:
            return !dgn.modelName.empty();
        default:
            return true;
    }
}

//=======================================================================================
// PointCloudGeometry Implementation
//=======================================================================================

bool PointCloudGeometry::IsValid() const {
    // Validate file path
    if (filePath.empty() || fileName.empty()) {
        return false;
    }
    
    // Validate position and vectors
    if (!std::isfinite(insertionPoint.x) || !std::isfinite(insertionPoint.y) || !std::isfinite(insertionPoint.z) ||
        !std::isfinite(uVector.x) || !std::isfinite(uVector.y) || !std::isfinite(uVector.z) ||
        !std::isfinite(vVector.x) || !std::isfinite(vVector.y) || !std::isfinite(vVector.z) ||
        !std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    // Validate scale and rotation
    if (!std::isfinite(scale) || !std::isfinite(rotation) || scale <= 0.0) {
        return false;
    }
    
    // Validate point count
    if (pointCount < 0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// OLEGeometry Implementation
//=======================================================================================

bool OLEGeometry::IsValid() const {
    // Validate position and size
    if (!std::isfinite(insertionPoint.x) || !std::isfinite(insertionPoint.y) || !std::isfinite(insertionPoint.z) ||
        !std::isfinite(width) || !std::isfinite(height) ||
        width <= 0.0 || height <= 0.0) {
        return false;
    }
    
    // Validate rotation and scale
    if (!std::isfinite(rotation) || !std::isfinite(scaleWidth) || !std::isfinite(scaleHeight) ||
        scaleWidth <= 0.0 || scaleHeight <= 0.0) {
        return false;
    }
    
    // Validate application name
    if (applicationName.empty()) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// DWGImageProcessor Implementation
//=======================================================================================

DWGImageProcessor::DWGImageProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_imageTolerance(1e-6)
    , m_enableImageValidation(true)
    , m_enablePathValidation(true)
    , m_autoRepairImages(true)
    , m_maxImageSize(100000000) // 100MB
    , m_maxPixelDimension(32768)
{
    // Add supported image formats
    m_supportedImageFormats = {".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff", ".gif"};
    m_supportedUnderlayFormats = {".pdf", ".dwf", ".dwfx", ".dgn"};
    m_supportedPointCloudFormats = {".las", ".laz", ".xyz", ".pts", ".ptx"};
}

DWGProcessingStatus DWGImageProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract image geometry from element
        // For demonstration, we'll create a sample image
        
        if (element.type == ElementType::GeometricElement) {
            ImageGeometry image;
            image.imagePath = "sample_image.jpg";
            image.imageFileName = "sample_image.jpg";
            image.insertionPoint = Point3d(0, 0, 0);
            image.uVector = Vector3d(100, 0, 0);
            image.vVector = Vector3d(0, 75, 0);
            image.imageWidth = 100.0;
            image.imageHeight = 75.0;
            image.pixelWidth = 1024;
            image.pixelHeight = 768;
            image.format = ImageGeometry::Format::JPEG;
            
            return ProcessImage(image, "Images");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing image entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGImageProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGImageProcessor::ProcessImage(const ImageGeometry& geometry, const std::string& layer) {
    // Validate image geometry
    auto validation = ValidateImageGeometry(geometry);
    if (!validation.isValid) {
        LogError("Image geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    ImageGeometry transformedGeometry = geometry;
    TransformImageGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbRasterImage* image = CreateDWGRasterImage(transformedGeometry);
        if (!image) {
            LogError("Failed to create DWG raster image entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetImageProperties(image, transformedGeometry)) {
            delete image;
            LogError("Failed to set image properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(image, layer)) {
            delete image;
            LogError("Failed to set image entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(image)) {
            delete image;
            LogError("Failed to add image to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedImages++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG raster image: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - image creation skipped");
    m_processedImages++;
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGImageProcessor::ProcessUnderlay(const UnderlayGeometry& geometry, const std::string& layer) {
    // Validate underlay geometry
    auto validation = ValidateUnderlayGeometry(geometry);
    if (!validation.isValid) {
        LogError("Underlay geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    UnderlayGeometry transformedGeometry = geometry;
    TransformUnderlayGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbUnderlayReference* underlay = CreateDWGUnderlay(transformedGeometry);
        if (!underlay) {
            LogError("Failed to create DWG underlay entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetUnderlayProperties(underlay, transformedGeometry)) {
            delete underlay;
            LogError("Failed to set underlay properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(underlay, layer)) {
            delete underlay;
            LogError("Failed to set underlay entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(underlay)) {
            delete underlay;
            LogError("Failed to add underlay to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedUnderlays++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG underlay: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - underlay creation skipped");
    m_processedUnderlays++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

ImageValidationResult DWGImageProcessor::ValidateImageGeometry(const ImageGeometry& geometry) const {
    ImageValidationResult result;
    result.isValid = true;
    
    // Validate file path
    result.hasValidPath = ValidateImagePath(geometry.imagePath);
    if (!result.hasValidPath) {
        result.AddImageError("Invalid image path: " + geometry.imagePath);
    }
    
    // Validate dimensions
    result.hasValidDimensions = ValidateImageDimensions(geometry);
    if (!result.hasValidDimensions) {
        result.AddImageError("Invalid image dimensions");
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(geometry.insertionPoint.x) && 
                             std::isfinite(geometry.insertionPoint.y) && 
                             std::isfinite(geometry.insertionPoint.z);
    if (!result.hasValidPosition) {
        result.AddImageError("Invalid insertion point");
    }
    
    // Detect format
    result.detectedFormat = DetectImageFormat(geometry.imagePath);
    if (result.detectedFormat == ImageGeometry::Format::Unknown) {
        result.AddImageWarning("Unknown image format");
    }
    
    // Check file size if path validation passed
    if (result.hasValidPath) {
        try {
            std::filesystem::path filePath(geometry.imagePath);
            if (std::filesystem::exists(filePath)) {
                result.fileSize = std::filesystem::file_size(filePath);
                if (result.fileSize > m_maxImageSize) {
                    result.AddImageWarning("Image file is very large: " + std::to_string(result.fileSize) + " bytes");
                }
            }
        }
        catch (...) {
            result.AddImageWarning("Could not determine file size");
        }
    }
    
    return result;
}

UnderlayValidationResult DWGImageProcessor::ValidateUnderlayGeometry(const UnderlayGeometry& geometry) const {
    UnderlayValidationResult result;
    result.isValid = true;
    
    // Validate file path
    result.hasValidPath = ValidateUnderlayPath(geometry.filePath);
    if (!result.hasValidPath) {
        result.AddUnderlayError("Invalid underlay path: " + geometry.filePath);
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(geometry.insertionPoint.x) && 
                             std::isfinite(geometry.insertionPoint.y) && 
                             std::isfinite(geometry.insertionPoint.z);
    if (!result.hasValidPosition) {
        result.AddUnderlayError("Invalid insertion point");
    }
    
    // Validate scale
    result.hasValidScale = std::isfinite(geometry.scaleX) && std::isfinite(geometry.scaleY) &&
                          geometry.scaleX > 0.0 && geometry.scaleY > 0.0;
    if (!result.hasValidScale) {
        result.AddUnderlayError("Invalid scale factors");
    }
    
    // Detect type
    result.detectedType = DetectUnderlayType(geometry.filePath);
    if (result.detectedType == UnderlayGeometry::Type::Unknown) {
        result.AddUnderlayWarning("Unknown underlay type");
    }
    
    return result;
}

bool DWGImageProcessor::ValidateImagePath(const std::string& path) const {
    if (path.empty()) {
        return false;
    }
    
    if (!m_enablePathValidation) {
        return true; // Skip validation if disabled
    }
    
    try {
        std::filesystem::path filePath(path);
        
        // Check if file exists
        if (!std::filesystem::exists(filePath)) {
            return false;
        }
        
        // Check file extension
        std::string extension = filePath.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        return std::find(m_supportedImageFormats.begin(), m_supportedImageFormats.end(), extension) != m_supportedImageFormats.end();
    }
    catch (...) {
        return false;
    }
}

bool DWGImageProcessor::ValidateUnderlayPath(const std::string& path) const {
    if (path.empty()) {
        return false;
    }
    
    if (!m_enablePathValidation) {
        return true; // Skip validation if disabled
    }
    
    try {
        std::filesystem::path filePath(path);
        
        // Check if file exists
        if (!std::filesystem::exists(filePath)) {
            return false;
        }
        
        // Check file extension
        std::string extension = filePath.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        return std::find(m_supportedUnderlayFormats.begin(), m_supportedUnderlayFormats.end(), extension) != m_supportedUnderlayFormats.end();
    }
    catch (...) {
        return false;
    }
}

bool DWGImageProcessor::ValidateImageDimensions(const ImageGeometry& geometry) const {
    // Check physical dimensions
    if (geometry.imageWidth <= 0.0 || geometry.imageHeight <= 0.0) {
        return false;
    }
    
    // Check pixel dimensions
    if (geometry.pixelWidth <= 0 || geometry.pixelHeight <= 0) {
        return false;
    }
    
    // Check maximum pixel dimensions
    if (geometry.pixelWidth > m_maxPixelDimension || geometry.pixelHeight > m_maxPixelDimension) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// Helper Methods
//=======================================================================================

ImageGeometry::Format DWGImageProcessor::DetectImageFormat(const std::string& path) const {
    try {
        std::filesystem::path filePath(path);
        std::string extension = filePath.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        if (extension == ".jpg" || extension == ".jpeg") {
            return ImageGeometry::Format::JPEG;
        } else if (extension == ".png") {
            return ImageGeometry::Format::PNG;
        } else if (extension == ".bmp") {
            return ImageGeometry::Format::BMP;
        } else if (extension == ".tif" || extension == ".tiff") {
            return ImageGeometry::Format::TIFF;
        } else if (extension == ".gif") {
            return ImageGeometry::Format::GIF;
        }
    }
    catch (...) {
        // Ignore errors
    }
    
    return ImageGeometry::Format::Unknown;
}

UnderlayGeometry::Type DWGImageProcessor::DetectUnderlayType(const std::string& path) const {
    try {
        std::filesystem::path filePath(path);
        std::string extension = filePath.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        if (extension == ".pdf") {
            return UnderlayGeometry::Type::PDF;
        } else if (extension == ".dwf" || extension == ".dwfx") {
            return UnderlayGeometry::Type::DWF;
        } else if (extension == ".dgn") {
            return UnderlayGeometry::Type::DGN;
        }
    }
    catch (...) {
        // Ignore errors
    }
    
    return UnderlayGeometry::Type::Unknown;
}

void DWGImageProcessor::TransformImageGeometry(ImageGeometry& geometry) const {
    geometry.insertionPoint = TransformPoint(geometry.insertionPoint);
    geometry.uVector = TransformVector(geometry.uVector);
    geometry.vVector = TransformVector(geometry.vVector);
}

void DWGImageProcessor::TransformUnderlayGeometry(UnderlayGeometry& geometry) const {
    geometry.insertionPoint = TransformPoint(geometry.insertionPoint);
    geometry.uVector = TransformVector(geometry.uVector);
    geometry.vVector = TransformVector(geometry.vVector);
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbRasterImage* DWGImageProcessor::CreateDWGRasterImage(const ImageGeometry& geometry) const {
    try {
        AcDbRasterImage* image = new AcDbRasterImage();
        
        // Set image definition
        // This would require creating an AcDbRasterImageDef first
        // For now, just set basic properties
        
        AcGePoint3d origin(geometry.insertionPoint.x, geometry.insertionPoint.y, geometry.insertionPoint.z);
        AcGeVector3d u(geometry.uVector.x, geometry.uVector.y, geometry.uVector.z);
        AcGeVector3d v(geometry.vVector.x, geometry.vVector.y, geometry.vVector.z);
        
        image->setOrientation(origin, u, v);
        image->setDisplayOpt(AcDbRasterImage::kShow, true);
        image->setDisplayOpt(AcDbRasterImage::kShowUnAligned, true);
        
        return image;
    }
    catch (...) {
        return nullptr;
    }
}

AcDbUnderlayReference* DWGImageProcessor::CreateDWGUnderlay(const UnderlayGeometry& geometry) const {
    try {
        // This would require creating the appropriate underlay reference type
        // based on the geometry type (PDF, DWF, DGN)
        // For now, return nullptr as this requires specific underlay definition setup
        return nullptr;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGImageProcessor::SetImageProperties(AcDbRasterImage* image, const ImageGeometry& geometry) const {
    if (!image) {
        return false;
    }
    
    try {
        // Set brightness, contrast, fade
        image->setBrightness(static_cast<Adesk::Int8>(geometry.brightness));
        image->setContrast(static_cast<Adesk::Int8>(geometry.contrast));
        image->setFade(static_cast<Adesk::Int8>(geometry.fade));
        
        // Set clipping
        if (geometry.isClipped && !geometry.clipBoundary.empty()) {
            AcGePoint2dArray clipPts;
            for (const auto& pt : geometry.clipBoundary) {
                clipPts.append(AcGePoint2d(pt.x, pt.y));
            }
            image->setClipBoundary(clipPts);
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGImageProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGImageProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
