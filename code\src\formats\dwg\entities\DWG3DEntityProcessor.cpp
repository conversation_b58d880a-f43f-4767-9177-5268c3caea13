#include "DWG3DEntityProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/geplane.h>
#include <realdwg/ge/gesurface.h>
#include <realdwg/ge/genurb3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// Face3DGeometry Implementation
//=======================================================================================

bool Face3DGeometry::IsValid() const {
    if (vertices.size() < 3 || vertices.size() > 4) {
        return false;
    }
    
    for (const auto& vertex : vertices) {
        if (!std::isfinite(vertex.x) || !std::isfinite(vertex.y) || !std::isfinite(vertex.z)) {
            return false;
        }
    }
    
    return true;
}

Vector3d Face3DGeometry::CalculateNormal() const {
    if (vertices.size() < 3) {
        return Vector3d(0, 0, 1);
    }
    
    Vector3d v1(vertices[1].x - vertices[0].x, vertices[1].y - vertices[0].y, vertices[1].z - vertices[0].z);
    Vector3d v2(vertices[2].x - vertices[0].x, vertices[2].y - vertices[0].y, vertices[2].z - vertices[0].z);
    
    // Cross product
    Vector3d normal(
        v1.y * v2.z - v1.z * v2.y,
        v1.z * v2.x - v1.x * v2.z,
        v1.x * v2.y - v1.y * v2.x
    );
    
    // Normalize
    double length = std::sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
    if (length > 1e-10) {
        normal.x /= length;
        normal.y /= length;
        normal.z /= length;
    }
    
    return normal;
}

double Face3DGeometry::CalculateArea() const {
    if (vertices.size() < 3) {
        return 0.0;
    }
    
    if (IsTriangle()) {
        Vector3d v1(vertices[1].x - vertices[0].x, vertices[1].y - vertices[0].y, vertices[1].z - vertices[0].z);
        Vector3d v2(vertices[2].x - vertices[0].x, vertices[2].y - vertices[0].y, vertices[2].z - vertices[0].z);
        
        Vector3d cross(
            v1.y * v2.z - v1.z * v2.y,
            v1.z * v2.x - v1.x * v2.z,
            v1.x * v2.y - v1.y * v2.x
        );
        
        return 0.5 * std::sqrt(cross.x * cross.x + cross.y * cross.y + cross.z * cross.z);
    } else if (IsQuad()) {
        // Split quad into two triangles
        Face3DGeometry tri1, tri2;
        tri1.vertices = {vertices[0], vertices[1], vertices[2]};
        tri2.vertices = {vertices[0], vertices[2], vertices[3]};
        return tri1.CalculateArea() + tri2.CalculateArea();
    }
    
    return 0.0;
}

//=======================================================================================
// MeshGeometry Implementation
//=======================================================================================

bool MeshGeometry::IsValid() const {
    if (vertices.empty()) {
        return false;
    }
    
    // Validate vertices
    for (const auto& vertex : vertices) {
        if (!std::isfinite(vertex.x) || !std::isfinite(vertex.y) || !std::isfinite(vertex.z)) {
            return false;
        }
    }
    
    // Validate faces
    for (const auto& face : faces) {
        if (face.size() < 3) {
            return false;
        }
        for (int index : face) {
            if (index < 0 || index >= static_cast<int>(vertices.size())) {
                return false;
            }
        }
    }
    
    return true;
}

bool MeshGeometry::HasValidTopology() const {
    if (!IsValid()) {
        return false;
    }
    
    // Check for degenerate faces
    for (const auto& face : faces) {
        std::set<int> uniqueIndices(face.begin(), face.end());
        if (uniqueIndices.size() != face.size()) {
            return false; // Duplicate vertices in face
        }
    }
    
    return true;
}

//=======================================================================================
// SurfaceGeometry Implementation
//=======================================================================================

bool SurfaceGeometry::IsValid() const {
    // Validate origin and directions
    if (!std::isfinite(origin.x) || !std::isfinite(origin.y) || !std::isfinite(origin.z)) {
        return false;
    }
    
    if (!std::isfinite(uDirection.x) || !std::isfinite(uDirection.y) || !std::isfinite(uDirection.z)) {
        return false;
    }
    
    if (!std::isfinite(vDirection.x) || !std::isfinite(vDirection.y) || !std::isfinite(vDirection.z)) {
        return false;
    }
    
    // Validate parametric bounds
    if (uMin >= uMax || vMin >= vMax) {
        return false;
    }
    
    // Type-specific validation
    switch (type) {
        case Type::Sphere:
            return sphere.radius > 0.0;
        case Type::Cylinder:
            return cylinder.radius > 0.0 && cylinder.height > 0.0;
        case Type::Cone:
            return cone.baseRadius > 0.0 && cone.height > 0.0;
        case Type::Torus:
            return torus.majorRadius > 0.0 && torus.minorRadius > 0.0 && torus.majorRadius > torus.minorRadius;
        case Type::Nurbs:
            return nurbsData.degreeU > 0 && nurbsData.degreeV > 0 && 
                   !nurbsData.controlPoints.empty() && !nurbsData.knotsU.empty() && !nurbsData.knotsV.empty();
        default:
            return true;
    }
}

bool SurfaceGeometry::IsParametric() const {
    return type == Type::Nurbs || type == Type::Cylinder || type == Type::Cone || 
           type == Type::Sphere || type == Type::Torus;
}

Point3d SurfaceGeometry::EvaluatePoint(double u, double v) const {
    switch (type) {
        case Type::Plane:
            return Point3d(
                origin.x + u * uDirection.x + v * vDirection.x,
                origin.y + u * uDirection.y + v * vDirection.y,
                origin.z + u * uDirection.z + v * vDirection.z
            );
            
        case Type::Cylinder: {
            double angle = u * 2.0 * M_PI;
            double height = v * cylinder.height;
            return Point3d(
                origin.x + cylinder.radius * std::cos(angle),
                origin.y + cylinder.radius * std::sin(angle),
                origin.z + height
            );
        }
        
        case Type::Sphere: {
            double theta = u * 2.0 * M_PI;  // Azimuth
            double phi = v * M_PI;          // Elevation
            return Point3d(
                origin.x + sphere.radius * std::sin(phi) * std::cos(theta),
                origin.y + sphere.radius * std::sin(phi) * std::sin(theta),
                origin.z + sphere.radius * std::cos(phi)
            );
        }
        
        default:
            return origin;
    }
}

Vector3d SurfaceGeometry::EvaluateNormal(double u, double v) const {
    switch (type) {
        case Type::Plane:
            return normal;
            
        case Type::Cylinder: {
            double angle = u * 2.0 * M_PI;
            return Vector3d(std::cos(angle), std::sin(angle), 0.0);
        }
        
        case Type::Sphere: {
            double theta = u * 2.0 * M_PI;
            double phi = v * M_PI;
            return Vector3d(
                std::sin(phi) * std::cos(theta),
                std::sin(phi) * std::sin(theta),
                std::cos(phi)
            );
        }
        
        default:
            return Vector3d(0, 0, 1);
    }
}

//=======================================================================================
// Solid3DGeometry Implementation
//=======================================================================================

bool Solid3DGeometry::IsValid() const {
    // Validate center and dimensions
    if (!std::isfinite(center.x) || !std::isfinite(center.y) || !std::isfinite(center.z)) {
        return false;
    }
    
    if (!std::isfinite(dimensions.x) || !std::isfinite(dimensions.y) || !std::isfinite(dimensions.z)) {
        return false;
    }
    
    // Type-specific validation
    switch (type) {
        case Type::Box:
            return box.width > 0.0 && box.height > 0.0 && box.depth > 0.0;
        case Type::Cylinder:
            return cylinder.radius > 0.0 && cylinder.height > 0.0;
        case Type::Sphere:
            return sphere.radius > 0.0;
        case Type::Cone:
            return cone.baseRadius > 0.0 && cone.height > 0.0;
        case Type::Torus:
            return torus.majorRadius > 0.0 && torus.minorRadius > 0.0 && 
                   torus.majorRadius > torus.minorRadius;
        default:
            return true;
    }
}

bool Solid3DGeometry::IsPrimitive() const {
    return type == Type::Box || type == Type::Cylinder || type == Type::Sphere || 
           type == Type::Cone || type == Type::Torus || type == Type::Wedge || type == Type::Pyramid;
}

double Solid3DGeometry::CalculateVolume() const {
    switch (type) {
        case Type::Box:
            return box.width * box.height * box.depth;
            
        case Type::Cylinder:
            return M_PI * cylinder.radius * cylinder.radius * cylinder.height;
            
        case Type::Sphere:
            return (4.0 / 3.0) * M_PI * sphere.radius * sphere.radius * sphere.radius;
            
        case Type::Cone:
            return (1.0 / 3.0) * M_PI * cone.baseRadius * cone.baseRadius * cone.height;
            
        case Type::Torus:
            return 2.0 * M_PI * M_PI * torus.majorRadius * torus.minorRadius * torus.minorRadius;
            
        default:
            return 0.0;
    }
}

double Solid3DGeometry::CalculateSurfaceArea() const {
    switch (type) {
        case Type::Box:
            return 2.0 * (box.width * box.height + box.height * box.depth + box.depth * box.width);
            
        case Type::Cylinder:
            return 2.0 * M_PI * cylinder.radius * (cylinder.radius + cylinder.height);
            
        case Type::Sphere:
            return 4.0 * M_PI * sphere.radius * sphere.radius;
            
        case Type::Cone: {
            double slantHeight = std::sqrt(cone.baseRadius * cone.baseRadius + cone.height * cone.height);
            return M_PI * cone.baseRadius * (cone.baseRadius + slantHeight);
        }
        
        case Type::Torus:
            return 4.0 * M_PI * M_PI * torus.majorRadius * torus.minorRadius;
            
        default:
            return 0.0;
    }
}

//=======================================================================================
// DWG3DEntityProcessor Implementation
//=======================================================================================

DWG3DEntityProcessor::DWG3DEntityProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_meshTolerance(1e-6)
    , m_surfaceTolerance(1e-8)
    , m_solidTolerance(1e-10)
    , m_maxMeshVertices(1000000)
    , m_maxMeshFaces(2000000)
    , m_enableMeshOptimization(true)
    , m_enableSurfaceTessellation(true)
    , m_enableSolidValidation(true)
{
}

DWGProcessingStatus DWG3DEntityProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract 3D geometry from element
        // For demonstration, we'll create sample geometries
        
        if (element.type == ElementType::GeometricElement) {
            // Example: Process as a simple 3D face
            Face3DGeometry face;
            face.vertices = {
                Point3d(0, 0, 0),
                Point3d(10, 0, 0),
                Point3d(10, 10, 0),
                Point3d(0, 10, 0)
            };
            face.normal = Vector3d(0, 0, 1);
            face.isVisible = true;
            
            return ProcessFace3D(face, "3D_Entities");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing 3D entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWG3DEntityProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a 3D entity type
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWG3DEntityProcessor::ProcessFace3D(const Face3DGeometry& geometry, const std::string& layer) {
    // Validate geometry
    if (!ValidateFace3D(geometry)) {
        LogError("Invalid Face3D geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Transform geometry
    Face3DGeometry transformedGeometry = geometry;
    for (auto& vertex : transformedGeometry.vertices) {
        vertex = TransformPoint(vertex);
    }
    transformedGeometry.normal = TransformVector(transformedGeometry.normal);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbFace* face = CreateDWGFace3D(transformedGeometry);
        if (!face) {
            LogError("Failed to create DWG Face3D entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(face, layer)) {
            delete face;
            LogError("Failed to set Face3D properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(face)) {
            delete face;
            LogError("Failed to add Face3D to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedFaces++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG Face3D: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - Face3D creation skipped");
    m_processedFaces++;
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWG3DEntityProcessor::ProcessMesh(const MeshGeometry& geometry, const std::string& layer) {
    // Validate mesh geometry
    auto validation = ValidateMeshGeometry(geometry);
    if (!validation.isValid) {
        LogError("Mesh geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Process based on mesh type
    switch (geometry.type) {
        case MeshGeometry::Type::PolygonMesh:
            return ProcessPolygonMesh(geometry, layer);
        case MeshGeometry::Type::PolyFaceMesh:
            return ProcessPolyFaceMesh(geometry, layer);
        case MeshGeometry::Type::SubDMesh:
            return ProcessSubDMesh(geometry, layer);
        default:
            LogError("Unsupported mesh type");
            return DWGProcessingStatus::UnsupportedEntity;
    }
}

//=======================================================================================
// Validation Methods
//=======================================================================================

Mesh3DValidationResult DWG3DEntityProcessor::ValidateMeshGeometry(const MeshGeometry& geometry) const {
    Mesh3DValidationResult result;
    result.isValid = true;
    
    // Validate vertices
    result.hasValidVertices = true;
    for (const auto& vertex : geometry.vertices) {
        if (!std::isfinite(vertex.x) || !std::isfinite(vertex.y) || !std::isfinite(vertex.z)) {
            result.hasValidVertices = false;
            result.invalidVertexCount++;
        }
    }
    
    if (!result.hasValidVertices) {
        result.AddMeshError("Invalid vertex coordinates found");
    }
    
    // Validate faces
    result.hasValidFaces = ValidateMeshTopology(geometry);
    if (!result.hasValidFaces) {
        result.AddMeshError("Invalid mesh topology");
    }
    
    // Validate normals if present
    if (!geometry.normals.empty()) {
        result.hasValidNormals = true;
        for (const auto& normal : geometry.normals) {
            double length = std::sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
            if (length < 1e-10 || !std::isfinite(length)) {
                result.hasValidNormals = false;
                break;
            }
        }
        
        if (!result.hasValidNormals) {
            result.AddMeshWarning("Invalid normal vectors found");
        }
    }
    
    // Check mesh size limits
    if (geometry.vertices.size() > m_maxMeshVertices) {
        result.AddMeshError("Too many vertices (limit: " + std::to_string(m_maxMeshVertices) + ")");
        result.isValid = false;
    }
    
    if (geometry.faces.size() > m_maxMeshFaces) {
        result.AddMeshError("Too many faces (limit: " + std::to_string(m_maxMeshFaces) + ")");
        result.isValid = false;
    }
    
    return result;
}

bool DWG3DEntityProcessor::ValidateFace3D(const Face3DGeometry& face) const {
    if (!face.IsValid()) {
        return false;
    }
    
    // Check for degenerate face
    if (IsFaceDegenerate(face.vertices, m_meshTolerance)) {
        return false;
    }
    
    return true;
}

bool DWG3DEntityProcessor::ValidateMeshTopology(const MeshGeometry& geometry) const {
    // Check face indices
    for (const auto& face : geometry.faces) {
        if (face.size() < 3) {
            return false;
        }
        
        for (int index : face) {
            if (index < 0 || index >= static_cast<int>(geometry.vertices.size())) {
                return false;
            }
        }
        
        // Check for duplicate indices in face
        std::set<int> uniqueIndices(face.begin(), face.end());
        if (uniqueIndices.size() != face.size()) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// Helper Methods
//=======================================================================================

bool DWG3DEntityProcessor::IsFaceDegenerate(const std::vector<Point3d>& vertices, double tolerance) const {
    if (vertices.size() < 3) {
        return true;
    }
    
    // Calculate face area
    Vector3d v1(vertices[1].x - vertices[0].x, vertices[1].y - vertices[0].y, vertices[1].z - vertices[0].z);
    Vector3d v2(vertices[2].x - vertices[0].x, vertices[2].y - vertices[0].y, vertices[2].z - vertices[0].z);
    
    Vector3d cross(
        v1.y * v2.z - v1.z * v2.y,
        v1.z * v2.x - v1.x * v2.z,
        v1.x * v2.y - v1.y * v2.x
    );
    
    double area = 0.5 * std::sqrt(cross.x * cross.x + cross.y * cross.y + cross.z * cross.z);
    return area < tolerance;
}

Vector3d DWG3DEntityProcessor::CalculateFaceNormal(const std::vector<Point3d>& vertices) const {
    if (vertices.size() < 3) {
        return Vector3d(0, 0, 1);
    }
    
    Vector3d v1(vertices[1].x - vertices[0].x, vertices[1].y - vertices[0].y, vertices[1].z - vertices[0].z);
    Vector3d v2(vertices[2].x - vertices[0].x, vertices[2].y - vertices[0].y, vertices[2].z - vertices[0].z);
    
    Vector3d normal(
        v1.y * v2.z - v1.z * v2.y,
        v1.z * v2.x - v1.x * v2.z,
        v1.x * v2.y - v1.y * v2.x
    );
    
    double length = std::sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
    if (length > 1e-10) {
        normal.x /= length;
        normal.y /= length;
        normal.z /= length;
    }
    
    return normal;
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbFace* DWG3DEntityProcessor::CreateDWGFace3D(const Face3DGeometry& geometry) const {
    try {
        AcDbFace* face = new AcDbFace();
        
        // Set vertices
        if (geometry.vertices.size() >= 3) {
            AcGePoint3d pt0(geometry.vertices[0].x, geometry.vertices[0].y, geometry.vertices[0].z);
            AcGePoint3d pt1(geometry.vertices[1].x, geometry.vertices[1].y, geometry.vertices[1].z);
            AcGePoint3d pt2(geometry.vertices[2].x, geometry.vertices[2].y, geometry.vertices[2].z);
            
            if (geometry.vertices.size() == 4) {
                AcGePoint3d pt3(geometry.vertices[3].x, geometry.vertices[3].y, geometry.vertices[3].z);
                face->setVertexAt(0, pt0);
                face->setVertexAt(1, pt1);
                face->setVertexAt(2, pt2);
                face->setVertexAt(3, pt3);
            } else {
                face->setVertexAt(0, pt0);
                face->setVertexAt(1, pt1);
                face->setVertexAt(2, pt2);
                face->setVertexAt(3, pt2); // Duplicate last vertex for triangle
            }
        }
        
        // Set visibility
        if (!geometry.isVisible) {
            face->setVisibility(AcDb::kInvisible);
        }
        
        return face;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWG3DEntityProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWG3DEntityProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
