/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdResbuf.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* This class is an expandable array of byte data.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class   RealDwgBinaryData : public bvector <byte>
{
public:
struct XDataHeader
    {
    XDataHeader (short groupCode, short bytesToFollow)
        {
        m_groupCode     = groupCode;
        m_bytesToFollow = bytesToFollow;
        m_filler        = 0;
        }
    XDataHeader (const byte* data)
        {
        memcpy (&m_groupCode,        data, sizeof(short));
        memcpy (&m_bytesToFollow,    data+sizeof(short), sizeof(short));
        m_filler = 0;
        }
    short       m_groupCode;
    short       m_bytesToFollow;
    long        m_filler;
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddGroupCode
(
int                         resType,
const void*                 pData,
int                         nBytes
)
    {
    byte            *pByte, *pEnd;
    XDataHeader     header (resType, nBytes);

    for (pByte = (byte *) &header, pEnd = pByte + sizeof(header); pByte < pEnd; )
        push_back (*pByte++);

    for (pByte = (byte *) pData, pEnd = pByte + nBytes; pByte < pEnd; )
        push_back (*pByte++);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddHandleGroupCode
(
int                         resType,
AcDbHandle                  handle
)
    {
    byte            *pByte, *pEnd;
    XDataHeader     header (resType, 8);

    for (pByte = (byte *) &header, pEnd = pByte + sizeof(header); pByte < pEnd; )
        push_back (*pByte++);

    for (pByte = (byte *) &handle + 7, pEnd = pByte - 8; pByte > pEnd; )
        push_back (*pByte--);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                    AppendResBuf
(
struct resbuf const&    pResBuf
)
    {
    for (int iByte=0; iByte < pResBuf.resval.rbinary.clen; iByte++)
        push_back (pResBuf.resval.rbinary.buf[iByte]);
    }

};



/*=================================================================================**//**
* This class subclasses "struct resbuf" so we can add methods. We are going to static_cast
* from struct resbuf to RealDwgResBuf, so no additional data members can be added.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
#include    <realdwg\base\adscodes.h>
class           RealDwgResBuf : public resbuf
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       Create (int  inputCode)
    {
    // figure out the type we need.
    return static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateInt8 (int  inputCode, Adesk::UInt8 value)
    {
#ifdef DEBUG
    AcDb::DwgDataType    dataType;
    BeAssert (AcDb::kDwgInt8 == (dataType = DataTypeFromInputCode (inputCode)) && L"XDATA buffer error - Int8!");
#endif

    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    pResBuf->resval.rint = value;

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateInt16 (int  inputCode, UInt16 value)
    {
#ifdef DEBUG
    AcDb::DwgDataType    dataType;
    BeAssert (AcDb::kDwgInt16 == (dataType = DataTypeFromInputCode (inputCode)) && L"XDATA buffer error - Int16!");
#endif

    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    pResBuf->resval.rint = value;

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateInt32 (int  inputCode, UInt32 value)
    {
    BeAssert (AcDb::kDwgInt32 == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - Int32!");

    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    pResBuf->resval.rlong = value;

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateDouble (int inputCode, double value)
    {
    BeAssert (AcDb::kDwgReal == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - double!");

    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    pResBuf->resval.rreal = value;

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreatePoint (int inputCode, const AcGePoint3d& value)
    {
    BeAssert (AcDb::kDwg3Real == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - point!");

    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    pResBuf->resval.rpoint[0] = value.x;
    pResBuf->resval.rpoint[1] = value.y;
    pResBuf->resval.rpoint[2] = value.z;

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateObjectId (int inputCode, AcDbObjectId value)
    {
    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));

    AcDb::DwgDataType    dataType = DataTypeFromInputCode (inputCode);
    switch (dataType)
        {
        case AcDb::kDwgHandle:
            BeAssert (false && L"XDATA buffer error - expected object ID but got handle!");
            break;

        case AcDb::kDwgHardOwnershipId:
        case AcDb::kDwgSoftOwnershipId:
        case AcDb::kDwgHardPointerId:
        case AcDb::kDwgSoftPointerId:
            {
            Acad::ErrorStatus status = acdbGetAdsName (pResBuf->resval.rlname, value);
            break;
            }

        case AcDb::kDwgText:
            BeAssert (false && L"XDATA buffer error - expected ID but got text!");
            break;

        default:
            BeAssert (false && L"XDATA buffer error - unexpected & unknown data type!");
        }

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateHandle (int inputCode, AcDbHandle value)
    {
    RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));

    AcDb::DwgDataType    dataType = DataTypeFromInputCode (inputCode);
    switch (dataType)
        {
        case AcDb::kDwgHandle:
            pResBuf->resval.mnInt64 = Adesk::UInt64(value);
            break;

        case AcDb::kDwgHardOwnershipId:
        case AcDb::kDwgSoftOwnershipId:
        case AcDb::kDwgHardPointerId:
        case AcDb::kDwgSoftPointerId:
            BeAssert (false && L"XDATA buffer error - expected handle but got object ID!");
            break;

        case AcDb::kDwgText:
            {
            WChar temp[64];
            swprintf (temp, L"%I64x", RealDwgUtil::CastDBHandle(value));
            ACHAR*  newString;
            acutNewString (temp, newString);
            pResBuf->resval.rstring = newString;
            break;
            }
        default:
            BeAssert (false && L"XDATA buffer error - expected handle but got unknown type!");
        }

    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateRegAppXData (const ACHAR* regAppName)
    {
    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (AcDb::kDxfRegAppName));
    acutUpdString (regAppName, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateMicroStationRegAppXData ()
    {
    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (AcDb::kDxfRegAppName));
    acutUpdString (StringConstants::RegAppName_MicroStation, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateString (const ACHAR* stringVal)
    {
    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (AcDb::kDxfXdAsciiString));
    acutUpdString (stringVal, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateString (int inputCode, const ACHAR* stringVal)
    {
    BeAssert (AcDb::kDwgText == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - text!");

    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    acutUpdString (stringVal, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateString (int inputCode, const char* stringVal)
    {
    // multibyte version, used from rdXDataUtil.cpp - see note.
    BeAssert (AcDb::kDwgText == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - text!");

    RealDwgResBuf   *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));
    size_t           bufSize      = 2 * (strlen(stringVal) + 1);
    ACHAR*           wideChars    = (ACHAR*) _alloca (bufSize);
    BeStringUtilities::CurrentLocaleCharToWChar (wideChars, stringVal, bufSize);
    acutUpdString (wideChars, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateBeginGroup ()
    {
    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (AcDb::kDxfXdControlString));
    acutUpdString (StringConstants::XData_BeginGroup, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateEndGroup ()
    {
    RealDwgResBuf *pResBuf      = static_cast <RealDwgResBuf*> (acutNewRb (AcDb::kDxfXdControlString));
    acutUpdString (StringConstants::XData_EndGroup, pResBuf->resval.rstring);
    return pResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateBinaryChain (int inputCode, byte const* pBytesIn, size_t numBytesIn)
    {
    BeAssert (AcDb::kDwgBChunk == DataTypeFromInputCode (inputCode) && L"XDATA buffer error - binary chunk!");

    BeAssert (numBytesIn > 0 && L"XDATA buffer error - binary chunk size!");

    RealDwgResBuf*  pFirstResBuf    = NULL;
    RealDwgResBuf*  pPreviousResBuf = NULL;
    while (numBytesIn > 0)
        {
        RealDwgResBuf *pResBuf = static_cast <RealDwgResBuf*> (acutNewRb (inputCode));

        if (NULL == pFirstResBuf)
            pFirstResBuf = pResBuf;

        pResBuf->resval.rbinary.clen = (numBytesIn > MAX_BINARY_CHUNK_SIZE) ? MAX_BINARY_CHUNK_SIZE : (short)numBytesIn;
        acutNewBuffer (pResBuf->resval.rbinary.buf, pResBuf->resval.rbinary.clen);
        memcpy (pResBuf->resval.rbinary.buf, pBytesIn, pResBuf->resval.rbinary.clen);
        numBytesIn -= pResBuf->resval.rbinary.clen;
        pBytesIn   += pResBuf->resval.rbinary.clen;

        // set up linked list.
        if (NULL != pPreviousResBuf)
            pPreviousResBuf->rbnext = pResBuf;

        pPreviousResBuf = pResBuf;
        }

    // return the first one in the chain.
    return pFirstResBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgResBuf*              Append (RealDwgResBuf* toAppend)
    {
    BeAssert (nullptr == rbnext && L"XDATA buffer error - null resbuf to be appended!");
    rbnext = toAppend;
    return toAppend;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgResBuf*              GetTail () const
    {
    const RealDwgResBuf* pCurr;
    for (pCurr = this; NULL != pCurr->rbnext; pCurr = static_cast <RealDwgResBuf*> (pCurr->rbnext))
        ;
    return const_cast <RealDwgResBuf*> (pCurr);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgResBuf*              InsertAfter (RealDwgResBuf* toInsert)
    {
    RealDwgResBuf *oldNext = static_cast <RealDwgResBuf*> (rbnext);
    rbnext = toInsert;
    toInsert->GetTail()->rbnext = oldNext;
    return toInsert;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 Free (RealDwgResBuf* resBuf)
    {
    if (NULL != resBuf)
        acutRelRb (resBuf);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       Copy (const RealDwgResBuf& source)
    {
    RealDwgResBuf*  newBuf = Create (source.restype);
    newBuf->CopyFrom (source);
    return newBuf;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        CopyFrom (const RealDwgResBuf& source)
    {
    // the resType of this RealDwgResBuf must already be set to the data type it expects.
    BeAssert (source.restype == restype && L"XDATA buffer error - unmatching restype!");
    if (source.restype != restype)
        return false;

    switch (GetDwgDataType())
        {
        case AcDb::kDwgNull:
            memset (&resval, 0, sizeof(resval));
            break;

        case AcDb::kDwgReal:
            resval.rreal = source.resval.rreal;
            break;

        case AcDb::kDwgInt32:
            resval.rlong = source.resval.rlong;
            break;

        case AcDb::kDwgInt16:
            resval.rint= source.resval.rint;
            break;

        case AcDb::kDwgInt8:
            // ?? no specific member
            resval.rint = source.resval.rint;
            break;

        case AcDb::kDwgText:
            acutUpdString (source.resval.rstring, resval.rstring);
            break;

        case AcDb::kDwgBChunk:
            acutNewBuffer (resval.rbinary.buf, resval.rbinary.clen = source.resval.rbinary.clen);
            memcpy (resval.rbinary.buf, source.resval.rbinary.buf, resval.rbinary.clen);
            break;

        case AcDb::kDwgHandle:
        case AcDb::kDwgHardOwnershipId:
        case AcDb::kDwgSoftOwnershipId:
        case AcDb::kDwgHardPointerId:
        case AcDb::kDwgSoftPointerId:
            resval.mnInt64 = source.resval.mnInt64;
            break;

        case AcDb::kDwg3Real:
            memcpy (resval.rpoint, source.resval.rpoint, sizeof (source.resval.rpoint));
            break;

        case AcDb::kDwgNotRecognized:
            break;

        default:
            BeAssert (false && L"XDATA buffer error - unexpected type resbuf!");
            return false;
        }
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        Equals (const RealDwgResBuf& other) const
    {
    if (restype != other.restype)
        return false;

    switch (GetDwgDataType())
        {
        case AcDb::kDwgNull:
            return true;

        case AcDb::kDwgReal:
            return (resval.rreal == other.resval.rreal);

        case AcDb::kDwgInt32:
            return (resval.rlong == other.resval.rlong);

        case AcDb::kDwgInt16:
        case AcDb::kDwgInt8:
            return (resval.rint == other.resval.rint);

        case AcDb::kDwgText:
            return (0 == wcscmp (other.resval.rstring, resval.rstring));

        case AcDb::kDwgBChunk:
            if (resval.rbinary.clen != other.resval.rbinary.clen)
                return false;
            return (0 == (memcmp (resval.rbinary.buf, other.resval.rbinary.buf, resval.rbinary.clen)));

        case AcDb::kDwgHandle:
        case AcDb::kDwgHardOwnershipId:
        case AcDb::kDwgSoftOwnershipId:
        case AcDb::kDwgHardPointerId:
        case AcDb::kDwgSoftPointerId:
            return resval.mnInt64 == other.resval.mnInt64;

        case AcDb::kDwg3Real:
            return ( (resval.rpoint[0] == other.resval.rpoint[0]) && (resval.rpoint[1] == other.resval.rpoint[1]) && (resval.rpoint[2] == other.resval.rpoint[2]) );

        case AcDb::kDwgNotRecognized:
            return true;

        default:
            BeAssert (false && L"XDATA buffer error - unknown type of resbuf!");
            break;
        }

    return false;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgResBuf*              GetNext() const
    {
    return static_cast <RealDwgResBuf*> (rbnext);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetNext (RealDwgResBuf* next)
    {
    rbnext = next;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
int                         GetResType() const
    {
    // the restype can be either an AcDb::DxfCode, or a number in the range or 5000 to 5020.
    return restype;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetResType (int newResType)
    {
    // if it's a type that contains data, free it.
    if (restype == newResType)
        return;

    switch (DataTypeFromInputCode (restype))
        {
        case AcDb::kDwgText:
            if (NULL != resval.rstring)
                {
                acutDelString (resval.rstring);
                resval.rstring = NULL;
                }
            break;
        case AcDb::kDwgBChunk:
            if ( (resval.rbinary.clen > 0) && (NULL != resval.rbinary.buf) )
                {
                acutDelBuffer (resval.rbinary.buf);
                resval.rbinary.buf = NULL;
                resval.rbinary.clen = 0;
                }
        }

    restype = newResType;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDb::DwgDataType           GetDwgDataType() const
    {
    return DataTypeFromInputCode (restype);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Int8                  GetInt8 ()
    {
    return (byte)(resval.rint & 0x00ff);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Int16                GetInt16 ()
    {
    return resval.rint;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetInt16 (Adesk::Int16 newVal)
    {
    BeAssert (AcDb::kDwgInt16 == GetDwgDataType() && L"XDATA buffer error - Int16!");
    resval.rint = newVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Int32                GetInt32 ()
    {
    BeAssert (AcDb::kDwgInt32 == GetDwgDataType() && L"XDATA buffer error - Int32!");
    return resval.rlong;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetInt32 (Adesk::Int32 newVal)
    {
    BeAssert (AcDb::kDwgInt32 == GetDwgDataType() && L"XDATA buffer error - Int32!");
    resval.rlong = newVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
double                      GetDouble ()
    {
    return resval.rreal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetDouble (double newVal)
    {
    BeAssert (AcDb::kDwgReal == GetDwgDataType() && L"XDATA buffer error - double!");
    resval.rreal = newVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcGePoint3d                 GetPoint3d ()
    {
    AcGePoint3d point (resval.rpoint[0], resval.rpoint[1], resval.rpoint[2]);
    return point;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
ACHAR const *               GetString ()
    {
    BeAssert (AcDb::kDwgText == GetDwgDataType() && L"XDATA buffer error - text!");
    return resval.rstring;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetString (ACHAR const* newString)
    {
    BeAssert (AcDb::kDwgText == GetDwgDataType() && L"XDATA buffer error - text!");
    acutUpdString (newString, resval.rstring);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        GetBinaryChunk (RealDwgBinaryData& chunk, bool append)
    {
    BeAssert (AcDb::kDwgBChunk == GetDwgDataType() && L"XDATA buffer error - binary chunk!");
    if (!append)
        chunk.clear();

    chunk.AppendResBuf (*this);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetBinaryData (RealDwgBinaryData& chunk)
    {
    BeAssert (AcDb::kDwgBChunk == GetDwgDataType() && L"XDATA buffer error - not a binary chunk type!");
    size_t dataSize = chunk.size();
    if (NULL != resval.rbinary.buf && (resval.rbinary.clen != dataSize))
        {
        BeAssert (resval.rbinary.clen > 0 && L"XDATA buffer error - 0 size of binary chunk!");
        acutDelBuffer (resval.rbinary.buf);
        resval.rbinary.buf = NULL;
        resval.rbinary.clen = 0;
        }

    if (NULL == resval.rbinary.buf)
        acutNewBuffer (resval.rbinary.buf, dataSize);

    resval.rbinary.clen = (short) dataSize;
    memcpy (resval.rbinary.buf, &chunk.front(), dataSize);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbHandle                  GetHandle ()
    {
#ifdef REALDWG_STORES_INT64HANDLE
    return *((AcDbHandle*)resval.mnInt64);
#else
    // RealDWG appears to store handle as an ascii string in resbuf:
    return  AcDbHandle(resval.rstring);
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                GetObjectId ()
    {
    // address to object ID, aka AcDb::kDxfSoftPointerId
    return  AcDbObjectId((AcDbStub*)resval.mnLongPtr);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        DeleteUntilEndGroup ()
    {
    // we want to delete everything following this resBuf up to but not including an EndGroup.
    RealDwgResBuf* pNext;

    // should be pointing within the group, not at the group start or group end.
    if (AcDb::kDxfXdControlString == this->GetResType())
        {
        BeAssert (false && L"XDATA buffer error - not a control string type!");
        return;
        }

    // There should always be a next!
    if (NULL == (pNext = this->GetNext()))
        {
        BeAssert (false && L"XDATA buffer error - unexpected end of resbuf chain!");
        return;
        }

    RealDwgResBuf* pGroupEnd        = pNext;
    RealDwgResBuf* pBeforeGroupEnd  = NULL;
    for ( ;(NULL != pGroupEnd); pGroupEnd = pGroupEnd->GetNext())
        {
        if ( (AcDb::kDxfXdControlString == pGroupEnd->GetResType()) &&  (0 == wcscmp (pGroupEnd->resval.rstring, StringConstants::XData_EndGroup)) )
            break;
        pBeforeGroupEnd = pGroupEnd;
        }

    if (pNext != pGroupEnd)
        {
        // patch the pointers to isolate the resBufs between pNext and pBeforeEndGroup (inclusive), and free those.
        this->SetNext (pGroupEnd);
        pBeforeGroupEnd->SetNext (NULL);
        RealDwgResBuf::Free (pNext);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDb::DwgDataType    DataTypeFromInputCode (int inputCode)
    {
    return acdbGroupCodeToType (inputCode);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        BinaryDataFromRbChain (RealDwgBinaryData& binaryData, RealDwgResBuf* rbChain)
    {
    for (; NULL != rbChain; rbChain = rbChain->GetNext())
        {
        if (AcDb::kDxfBinaryChunk != rbChain->GetResType())
            return UnexpectedResBufType;

        binaryData.AppendResBuf (*rbChain);
        }

    return RealDwgSuccess;
    }

/*--------------------------------------------------------------------------------------
* @bsimethod                                                RayBentley          07/03
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       RbChainFromBinaryData (const void* pData, size_t dataSize)
    {
    const byte*     pByteData = (const byte *) pData;
    return RealDwgResBuf::CreateBinaryChain (AcDb::kDxfBinaryChunk, pByteData, dataSize);
    }


#if defined (REALDWG_DIAGNOSTICS)
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
CharCP                    GetDwgDataTypeString ()
    {
    switch (GetDwgDataType())
        {
        case AcDb::kDwgNull:
            return "kDwgNull";

        case AcDb::kDwgReal:
            return "kDwgReal";

        case AcDb::kDwgInt32:
            return "kDwgInt32";

        case AcDb::kDwgInt16:
            return "kDwgInt16";

        case AcDb::kDwgInt8:
            return "kDwgInt8";

        case AcDb::kDwgText:
            return "kDwgText";

        case AcDb::kDwgBChunk:
            return "kDwgBChunk";

        case AcDb::kDwgHandle:
            return "kDwgHandle";

        case AcDb::kDwgHardOwnershipId:
            return "KDwgHardOwnershipId";

        case AcDb::kDwgSoftOwnershipId:
            return "KDwgSoftOwnershipId";

        case AcDb::kDwgHardPointerId:
            return "KDwgHardPointerId";

        case AcDb::kDwgSoftPointerId:
            return "KDwgSoftPointerId";

        case AcDb::kDwg3Real:
            return "KDwg3Real";
        }
    return "KDwgNotRecognized";
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        Dump ()
    {
    printf ("Code %d (type %hs), value: ", this->GetResType(), this->GetDwgDataTypeString());
    switch (GetDwgDataType())
        {
        case AcDb::kDwgNull:
            printf ("\n");
            break;

        case AcDb::kDwgReal:
            printf ("%g\n", resval.rreal);
            break;

        case AcDb::kDwgInt32:
            printf ("%d\n", resval.rlong);
            break;

        case AcDb::kDwgInt16:
        case AcDb::kDwgInt8:
            printf ("%d\n", resval.rint);
            break;

        case AcDb::kDwgText:
            printf ("\"%ls\"\n", (NULL != resval.rstring) ? resval.rstring : L"");
            break;

        case AcDb::kDwgBChunk:
            printf ("Len %d\n", resval.rbinary.clen);
            break;

        case AcDb::kDwgHandle:
            printf ("%I64d\n", resval.mnInt64);
            break;

        case AcDb::kDwgHardOwnershipId:
        case AcDb::kDwgSoftOwnershipId:
        case AcDb::kDwgHardPointerId:
        case AcDb::kDwgSoftPointerId:
            {
            Acad::ErrorStatus   status;
            AcDbObjectId        objectId;
            if (Acad::eOk != (status = acdbGetObjectId (objectId, resval.rlname)))
                {
                BeAssert (false && L"XDATA buffer error - not a valid object ID!");
                }

            printf ("ObjectId %08x, Handle %I64d%\n", objectId, objectId.handle());
            break;
            }

        case AcDb::kDwg3Real:
            printf ("%g, %g, %g\n", resval.rpoint[0], resval.rpoint[1], resval.rpoint[2]);
            break;

        case AcDb::kDwgNotRecognized:
            break;

        default:
            BeAssert (false && L"XDATA buffer error - unexpected xdata type!");
            break;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        DumpChain (CharP message)
    {
    if (NULL != message)
        printf ("%hs\n", message);
    for (RealDwgResBuf *pCurr = this; NULL != pCurr; pCurr = static_cast <RealDwgResBuf*> (pCurr->rbnext))
        pCurr->Dump();
    printf ("\n");
    }

#endif
};
