/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DwgExampleHost.h $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#if defined (__DWGEXAMPLEHOST_BUILD__)
#define MYHOST_EXPORTED         __declspec( dllexport )
#else
#define MYHOST_EXPORTED         __declspec( dllimport )
#endif

#if !defined (MAX_LINEWEIGHTS)
#define MAX_LINEWEIGHTS         32
#endif

#include    <Mstn\RealDwg\DwgPlatformHost.h>


/*=================================================================================**//**
* @bsiclass                                                     BentleySystems
+===============+===============+===============+===============+===============+======*/
class    DwgExampleHost : public Bentley::RealDwg::DwgPlatformHost
{
public:
    MYHOST_EXPORTED virtual bool        _GetPassword (WCharCP dwgName, FilePasswordOption options, WCharP password, const size_t bufSize) override;
    MYHOST_EXPORTED virtual WCharCP     _GetRegistryProductRootKey () override;
    MYHOST_EXPORTED virtual WCharCP     _Product () override;
    MYHOST_EXPORTED virtual void        _FatalError (WCharCP format, ...) override;
    MYHOST_EXPORTED virtual bool        _GetDwgConversionSettings (Bentley::RealDwg::IDwgConversionSettings*& settings) override;
    MYHOST_EXPORTED                     DwgExampleHost ();
    MYHOST_EXPORTED                     ~DwgExampleHost ();

private:
    Bentley::RealDwg::IDwgConversionSettings*   m_dwgConversionSettings;
};  // DwgExampleHost


extern DwgExampleHost*      g_dwgExampleHost;
