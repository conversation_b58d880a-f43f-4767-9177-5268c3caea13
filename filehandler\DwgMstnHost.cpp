/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/filehandler/DwgMstnHost.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// windows includes
#define UNICODE
#include    <windows.h>
#include    <Msi.h>         // MsiGetComponentPath
#include    <stdarg.h>      // va_list

// DgnPlatform includes
#include    <Dgnplatform\DgnplatformApi.h>
#include    <Dgnplatform\DgnplatformLib.h>
#include    <DgnPlatform\Tcb\tcb.r.h>       // SolorTime
#include    <DgnPlatform\Tools\fileutil.h>

// DgnDisplay includes
#include    <DgnView\DgnViewLib.h>
#include    <DgnDisplayUI\CommonFileProgressMeter.h>

// Mstn includes
#include    <Mstn\MdlApi\rscdefs.r.h>
#include    <Mstn\MdlApi\msoutput.fdf>
#include    <Mstn\MdlApi\msfile.fdf>
#include    <MStn\MdlApi\msdialog.fdf>
#include    <MStn\MdlApi\dloadlib.h>
#include    <MStn\MdlApi\msdgnobj.fdf>
#include    <MStn\MdlApi\msmisc.fdf>
#include    <MsjInternal\Ustn\dlogstat.r.h>
#include    <MsjInternal\Ustn\msgids.r.h>
#include    <MsjInternal\Ustn\misystem.fdf>
#include    <MsjInternal\Ustn\mifile.fdf>
#include    <MsjInternal\Ustn\findacad.fdf>
#include    <Mstn\MdlApi\msw32utl.fdf>          // mdlWin32_getMstnWindow for the progress meter
#include    <Mstn\MstnResourceUtils.h>
#include    <Mstn\ISessionMgr.h>

// RealDWG includes
#include    <dbapserv.h>
#include    <dbsymtb.h>

USING_NAMESPACE_BENTLEY_DGNPLATFORM
using namespace Bentley::RealDwg;

#include    "DwgMstnHost.h"
#include    "DwgFileHandlerIds.h"

BEGIN_BENTLEY_NAMESPACE
namespace DwgFileHandler {

#if defined (_X64_)
static WChar    s_componentGuid[] = L"{285CAB69-5CB7-240B-697E-996AA63B6415}";
#elif defined (_X86_)
static WChar    s_componentGuid[] = L"{82C5BA96-C57B-42B0-96E7-996AA63B6415}";
#else
#error Unsupported platform by RealDWG!
#endif


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
DwgMstnHost::DwgMstnHost (RscFileHandle rscHandle)
    {
    m_realDwgRegistryRootKey = nullptr;
    m_rscFileHandle = rscHandle;
    m_masterDwgFile = nullptr;
    m_masterDgnFile = nullptr;
    m_activeViewgroupId = 0;
    m_masterFileObjectIds.removeAll ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
WCharCP         DwgMstnHost::_GetRegistryProductRootKey ()
    {
    if (nullptr == m_realDwgRegistryRootKey)
        {
        // get product GUID pointed to by MS_PRODUCTCODEGUID
        WString     productGuid;
        ConfigurationManager::GetVariable (productGuid, L"MS_PRODUCTCODEGUID");

#ifndef SKIP_MICROSOFTSDK
        if (!productGuid.empty())
            {
            WChar   productPath[1024];
            DWORD   regLength = _countof (productPath);

            // This will get the default location. Hence, will make the keys available for developers.
            if (INSTALLSTATE_LOCAL == MsiGetComponentPath (productGuid.c_str(), s_componentGuid, productPath, &regLength))
                {
                WCharCP   relativePath = wcschr (productPath, '\\');
                if (NULL != relativePath && 0 != relativePath[0] && NULL != ++relativePath && 0 != relativePath[0])
                    m_realDwgRegistryRootKey = wcsdup (relativePath);
                }
            }
#endif
        }

    return m_realDwgRegistryRootKey;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
WCharCP         DwgMstnHost::_Product ()
    {
    return L"MicroStation";
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetPassword (WCharCP dwgName, DwgMstnHost::FilePasswordOption options, WCharP password, const size_t bufSize)
    {
    WString     formatString, messageString;
    WChar       filename[MAXNAMELENGTH+1];

    wcsncpy_s (filename, _countof (filename), dwgName, _TRUNCATE);

    mdlFile_abbreviateName (filename, 28); // label length in DIALOGID_DRM_EnterPassword
    MstnPlatform::MstnResourceUtils::GetUstationError (formatString, NULL, MSG_INSECURE_ENVIRONMENT_SHORT);

    WString::Sprintf (messageString, formatString.c_str(), filename);

    char        buf[TEXTID_DRM_PASSWORD_MAX_CHARS + 1] = {0};

    msDialogState.drmpassword = buf;

    bool    ok = mdlDialog_openMessageBox (DIALOGID_DRM_EnterPassword, messageString.c_str(), MessageBoxIconType::NoSymbol)==ACTIONBUTTON_OK;

    // convert to upper case unicode:
    wchar_t pwdBuff[_MAX_PATH] = {0};
    ::OemToCharW( buf, pwdBuff );
    ::CharUpperW( pwdBuff );

    wcsncpy (password, pwdBuff, bufSize);
    password[bufSize-1] = 0;

    return  ok;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_FatalError (WCharCP format, ...)
    {
    /*-----------------------------------------------------------------------------------
    If we are opening a file, the file stays unchanged.  If we are writting a file, we are
    writing the database to a .tmp file, so the original .dgn or .dwg file still stays
    unchanged.  What might have lost is the change made in cache, but there is not much
    we can do about saving it to DWG.  What we can do is to shutdown PowerPlatform
    fracefully by prompting the user with a dialog box.
    -----------------------------------------------------------------------------------*/
    va_list     varArgs;
    va_start (varArgs, format);
    __super::_FatalError (format, varArgs);
    va_end (varArgs);

    // can't save changes to the master file - that might be when we got the fatalError, and we'd just end up right back here.
    DgnFileP    masterFile;
    if (NULL != (masterFile = MstnPlatform::ISessionMgr::GetMasterDgnFile()))
        masterFile->SetAbandonChangesFlag ();

    mdlSystem_exitMicroStation (0, MsgList_NONE, 0, 0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetDwgConversionSettings (Bentley::RealDwg::IDwgConversionSettings*& settings)
    {
    settings = DgnViewLib::GetDwgConversionSettings ();
    return  NULL != settings;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetBigfontInitFile (BeFileNameR fileName)
    {
    return SUCCESS == mdlFile_getAcadBigfontInitFile(fileName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetAcadFontmapFile (BeFileNameR fileName)
    {
    return BSISUCCESS == mdlFile_getAcadFontmapFile (fileName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_ReportMessage (ReportMessageType messageType, ...)
    {
    WString                 briefMessage, formatString, detailMessage;
    OutputMessagePriority   priority = OutputMessagePriority::Info;

    va_list                 varArgs;
    va_start (varArgs, messageType);

    switch (messageType)
        {
        case REPORTMESSAGE_ReadOnlyForced:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            MstnPlatform::MstnResourceUtils::GetUstationString (briefMessage, MsgList_ERRORS, 675);
            MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_ERRORS, 676);
            WString::Sprintf (detailMessage, formatString.c_str(), NULL == fileName ? L"??" : fileName);
            break;
            }

        case REPORTMESSAGE_DgnSeedFileNotFound:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_ERRORS, 185);
            WString::Sprintf (briefMessage, formatString.c_str(), NULL == fileName ? L"??" : fileName);
            MstnPlatform::MstnResourceUtils::GetUstationString (detailMessage, MsgList_ERRORS, 518);
            break;
            }

        case REPORTMESSAGE_BigfontInitFileMissing:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            wchar_t*        fontName = va_arg (varArgs, wchar_t*);
            MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_MESSAGES2, MS_MSGID_ExportingBigFontDetailed);
            MstnPlatform::MstnResourceUtils::GetUstationString (briefMessage, MsgList_MESSAGES2, MS_MSGID_ExportingBigFontBrief);
            WString::Sprintf (detailMessage, formatString.c_str(), NULL == fontName ? L"??" : fontName, NULL == fileName ? L"??" : fileName);

            priority = OutputMessagePriority::Warning;
            break;
            }

        default:        // unexpected message types
            va_end (varArgs);
            return;
        }

    va_end (varArgs);

    NotificationManager::OutputMessage (NotifyMessageDetails(priority, briefMessage.c_str(), detailMessage.c_str(), 0, OutputMessageAlert::None));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetDwgForgroundColor255Name (WStringR colorBookName, WStringR colorName)
    {
    colorBookName.clear ();
    MstnPlatform::MstnResourceUtils::GetUstationString (colorBookName, (MessageListNumber)RSCID_DWGMessages, MSGID_SpecialColorBookName);

    colorName.clear ();
    MstnPlatform::MstnResourceUtils::GetUstationString (colorName, (MessageListNumber)RSCID_DWGMessages, MSGID_ForegroundColor255);

    return  (!colorBookName.empty() && !colorName.empty());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetDwgDefaultDisplayStyle (WStringR displayStyleName, DisplayStylePtr& displayStyle)
    {
    displayStyleName.clear ();
    MstnPlatform::MstnResourceUtils::GetUstationString (displayStyleName, (MessageListNumber)RSCID_DWGMessages, MSGID_DefaultWireframe);

    if (displayStyleName.empty())
        return  false;

    // search the style in DGNLIB's
    DisplayStyleList    stylesInDgnlib;
    DisplayStyleManager::PopulateFromDgnLibs (stylesInDgnlib);

    DisplayStyleCP      styleFound = stylesInDgnlib.FindDisplayStyleByName (displayStyleName.GetWCharCP());

    if (NULL != styleFound)
        displayStyle = styleFound->Clone ();

    return  displayStyle.IsValid();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetTransparencyDisplay ()
    {
    bool        displayTransparency = false;
    if (SUCCESS != mdlFile_getAcadTransparencyDisplay(displayTransparency))
        displayTransparency = true;
    return  displayTransparency;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetPersistentTcbForSilentSave (TcbP pTcb)
    {
    if (NULL == pTcb)
        return  false;
    memcpy (pTcb, tcb, offsetof(Tcb, firstNonPersistentMember));
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_SetPersistentTcbForSilentSave (TcbCP pTcb)
    {
    // sync MicroStation's cached TCB from the updated DGN file header:
    if (nullptr != pTcb)
        memcpy (tcb, pTcb, sizeof(PersistentTcb));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          DwgMstnHost::_GetNumberOfScreens ()
    {
    // extern struct msGraphConfig in dloadlib.h
    return  graphConfig.numScreens;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_CreateProgressMeterSavingChanges (IDgnProgressMeterPtr& outMeter)
    {

    // Configure the global proress meter; it won't actually show a dialog until several seconds from now, and many times it will be destructed and never actually appear.
    if (MstnPlatform::ISessionMgr::AllowedToGiveGuiFeedback() && NULL == DgnPlatformLib::GetHost().GetProgressMeter())
        {
        WString         progressDialogTitle;
        RmgrResource::LoadWString (progressDialogTitle, m_rscFileHandle, MSGLISTID_DwgFileHandlerMessage, DWGMESSAGE_PROGRESS_SaveDialogTitle);

        ProgressMeterVisibilityOptionsPtr visibilityOptions = ProgressMeterVisibilityOptions::Create();
        visibilityOptions->SetInDeterminateProgressBar(false);

        outMeter = CommonFileProgressMeterFactory::Create (mdlWin32_getMstnWindow(-1), progressDialogTitle.c_str(), visibilityOptions.get());
        
        if (!outMeter.IsValid ())
            {
            BeAssert (false);
            }
        else
            {
            DgnPlatformLib::GetHost().SetProgressMeter (outMeter.get());
        
            // This is the root operation; don't bother using IDgnProgressMeter::TaskMark.
            WString     taskName;
            RmgrResource::LoadWString (taskName, m_rscFileHandle, MSGLISTID_DwgFileHandlerMessage, DWGMESSAGE_PROGRESS_SaveOperationTitle);

            outMeter->SetCurrentTaskName (taskName.c_str());
            return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_LoadCooperativeObjectEnablers ()
    {
    mdlSystem_loadApplicationListByCfgExt (L"MS_REALDWG_APPS", L"DwgObjectApp", MdlApplicationClass::User, MdlLoadMessages::None);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_AllowUpgradingAecObjects ()
    {
    if (!ConfigurationManager::IsVariableDefined (L"MS_NO_OPEN_ALERTS") && !ConfigurationManager::IsVariableDefined (L"MS_DWG_NO_AECSAVE_ALERT"))
        {
        WString     warning;
        MstnPlatform::MstnResourceUtils::GetUstationString (warning, MsgList_MESSAGES2, MS_MSGID_OldADTObjects);
        if (ACTIONBUTTON_CANCEL == mdlDialog_openAlertById(DIALOGID_LargeAlert, warning.c_str()))
            return  false;
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgMstnHost::_GetMasterDwgFile (AcDbDatabase*& outDwg, DgnFileP* outDgn)
    {
    if (nullptr != m_masterDwgFile)
        {
        outDwg = m_masterDwgFile;

        if (nullptr != outDgn)
            *outDgn = m_masterDgnFile;

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_SetMasterDwgFile (AcDbDatabase* inDwg, DgnFileP inDgn)
    {
    if (nullptr != inDgn && DgnFilePurpose::MasterFile == inDgn->GetFilePurpose())
        {
        m_masterDwgFile = inDwg;
        m_masterDgnFile = nullptr == inDwg ? nullptr : inDgn;
        }
    else if (nullptr == inDwg && nullptr == inDgn)
        {
        m_masterDwgFile = nullptr;
        m_masterDgnFile = nullptr;
        }
    m_masterFileObjectIds.removeAll ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/15
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId       DwgMstnHost::_GetActiveViewGroup ()
    {
    return  m_activeViewgroupId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgMstnHost::_SetActiveViewGroup (ElementId inElementId)
    {
    m_activeViewgroupId = inElementId;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
int             DwgMstnHost::_GetMasterFileObjectIdsInCache (AcDbObjectIdArray& out) const
    {
    out.removeAll ();
    if (!m_masterFileObjectIds.isEmpty())
        out.append (m_masterFileObjectIds);
    return  out.length();
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          11/21
+---------------+---------------+---------------+---------------+---------------+------*/
int             DwgMstnHost::_AddMasterFileObjectIdInCache (AcDbObjectId objectId)
    {
    if (objectId.isValid())
        m_masterFileObjectIds.append (objectId);
    return m_masterFileObjectIds.length();
    }

}   // Ends DwgFileHandler namespace

END_BENTLEY_NAMESPACE


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
extern "C" DLLEXPORT DgnFileTypeP   fileHandler_getType (int fileType)
    {
    return  Bentley::RealDwg::dwgFileIO_getFileType (fileType);
    }
