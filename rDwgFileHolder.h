/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFileHolder.h $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#include    <Mstn\RealDWG\rDwgAPI.h>

#include    <Bentley\WString.h>

typedef class AcDbHostApplicationProgressMeter* AcDbProgressMeterP;
typedef bool (*AecVerChkFuncPtr)(AcDbDatabase*);

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

class       FileHolder;
class       ConvertContext;
class       ConvertFromDgnContext;


/*=================================================================================**//**
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
struct      StringConstants
    {
    static WCharCP            ContinousLinetypeName;
    static WCharCP            PointBlockName;
    static WCharCP            MainDictionaryItem_MicroStation;
    static WCharCP            MainDictionaryItem_AcadDim;
    static WCharCP            UstnDictionaryItem_Header;
    static WCharCP            UstnDictionaryItem_Application;
    static WCharCP            UstnDictionaryItem_ModelApplication;
    static WCharCP            UstnDictionaryItem_ExtendedNonGraphics;
    static WCharCP            UstnDictionaryItem_DgnStore;
    static WCharCP            UstnDictionaryItem_DgnModel;
    static WCharCP            XDictionaryItem_AcadDimAssoc;
    static WCharCP            XDictionaryItem_LayerFilters;
    static WCharCP            XDictionaryItem_AcLyDictionary;
    static WCharCP            RegAppName_MicroStation;
    static WCharCP            RegAppName_PeUrl;
    static WCharCP            RegAppName_Acad;
    static WCharCP            XDataKey_ApplicationLinkage;
    static WCharCP            XDataKey_ApplicationDependency;
    static WCharCP            XDataKey_ApplicationDependencyOld;
    static WCharCP            XDataKey_CellNameLinkage;
    static WCharCP            XDataKey_GraphicGroup;
    static WCharCP            XDataKey_Transparency;
    static WCharCP            XDataKey_CompSetExpression;
    static WCharCP            XDataKey_CompSetExpressionSummary;
    static WCharCP            XDataKey_XAttribute;
    static WCharCP            XDataKey_ReferenceAttachMethod;
    static WCharCP            XDataKey_ReferenceTreatedAsElement;
    static WCharCP            XDataKey_NamedGroupFlags;
    static WCharCP            XDataKey_DgnModelName;
    static WCharCP            XData_BeginGroup;
    static WCharCP            XData_EndGroup;
    static WCharCP            AShadeLayerName;
    };


/*=================================================================================**//**
* BaseSymbologyData
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
class       BaseSymbologyData
{
public:
                                BaseSymbologyData (const FileHolder* fileHolder);
                                ~BaseSymbologyData ();

virtual UInt32                  GetMinimumColorIndex()      { return 0; }
virtual UInt32                  GetDefaultWeightIndex ()    { return 0; }
virtual int                     GetNColors ()               { return 256; }
virtual UInt32                  GetByCellColorIndex () = 0;
virtual UInt32                  GetByLayerColorIndex () = 0;
virtual Int32                   GetByCellLineStyleId () = 0;
virtual Int32                   GetByLayerLineStyleId () = 0;
virtual UInt32                  GetByCellWeightIndex () = 0;
virtual UInt32                  GetByLayerWeightIndex () = 0;
virtual void                    OverrideHSVTable () {};
virtual void                    SetColorTable (const byte *);

virtual UInt32                  GetColorIndex (RgbColorDefCP pColor, ConvertContext& context);
virtual RgbColorDefCP           GetColor (UInt32 colorIndex);

UInt32                          RemapColorIndex (UInt32 otherFileColorIndex, BaseSymbologyData*  otherSymbologyData, ConvertContext& context);
UInt32                          RemapColorIndexIfChanged (UInt32 currentColorIndex, UInt32 otherFileColorIndex, BaseSymbologyData* otherSymbologyData, ConvertContext& context);
Int32                           RemapLineStyleId (Int32 otherFileLineStyleId, BaseSymbologyData* otherSymbologyData, ConvertContext& context);
Int32                           RemapLineStyleIdIfChanged (Int32 currentLineStyleId, Int32 otherFileLineStyleId, BaseSymbologyData* otherSymbologyData, ConvertContext& context);

protected:

const FileHolder*               m_fileHolder;
int                             m_colorTableCheckSum;
int                             m_nTableColors;
RgbColorDef                     m_colorTable[256];
DgnPlatform::HsvColorDef        m_hsvTable[256];
RgbColorDef                     m_lastColor;
UInt32                          m_lastColorIndex;
AvlTree*                        m_pColorTree;
};


// Colors
enum RealDwgColors
    {
    DWGCOLOR_ByLayer = 256,
    DWGCOLOR_ByBlock = 0,
    DWGColor_Default = 7,
    };

// Weights
enum RealDwgWeights
    {
    DWGWEIGHT_ByLayer = -1,
    DWGWEIGHT_ByBlock = -2,
    DWGWEIGHT_Default = -3,
    };

// Underlays
enum RealDwgUnderlays
    {
    DWGUNDERLAY_Dgn,
    DWGUNDERLAY_Pdf,
    DWGUNDERLAY_Dwf,
    };

// Options to check for old version of AEC files
enum AecFileOpenMode
    {
    AECFILEOPEN_AlertAtSave = 0,
    AECFILEOPEN_ReadOnly    = 1,
    AECFILEOPEN_Upgrade     = 2,
    };


/*=================================================================================**//**
* DwgSymbologyData
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
class   DwgSymbologyData : public BaseSymbologyData
{
public:
                                DwgSymbologyData (const FileHolder* fileHolder);

UInt32                          GetMinimumColorIndex()              { return 1; };       // 0 is used for by block */

virtual UInt32                  GetByCellColorIndex () override     { return DWGCOLOR_ByBlock; }
virtual UInt32                  GetByLayerColorIndex () override    { return DWGCOLOR_ByLayer; }
virtual Int32                   GetByCellLineStyleId () override    { return -1; }                  // Needs work.
virtual Int32                   GetByLayerLineStyleId () override   { return -1; }                 // Needs work.
virtual UInt32                  GetByCellWeightIndex () override    { return DWGWEIGHT_ByBlock; }
virtual UInt32                  GetByLayerWeightIndex () override   { return DWGWEIGHT_ByLayer; }
virtual UInt32                  GetColorIndex (RgbColorDefCP pColor, ConvertContext& context) override;
virtual void                    OverrideHSVTable () override;

private:

};      // DwgSymbologyData

/*=================================================================================**//**
* DgnSymbologyData
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
class   DgnSymbologyData : public BaseSymbologyData
{
public:
DgnSymbologyData (const FileHolder* fileHolder);

UInt32                          GetMinimumColorIndex() { return 1; };       // 0 is used for by block */
void                            ReadColorTable (DgnModelP);

virtual UInt32                  GetByCellColorIndex () override     { return DgnPlatform::COLOR_BYCELL; }
virtual UInt32                  GetByLayerColorIndex () override    { return DgnPlatform::COLOR_BYLEVEL; }
virtual Int32                   GetByCellLineStyleId () override    { return DgnPlatform::STYLE_BYCELL; }
virtual Int32                   GetByLayerLineStyleId () override   { return DgnPlatform::STYLE_BYLEVEL; }
virtual UInt32                  GetByCellWeightIndex () override    { return DgnPlatform::WEIGHT_BYCELL; }
virtual UInt32                  GetByLayerWeightIndex () override   { return DgnPlatform::WEIGHT_BYLEVEL; }
};      // DgnSymbologyData


/*=================================================================================**//**
* RealDwgTableIndexBase
*
*  AutoCAD Tables are based on the AcDbHandle's directly, whereas MicroStation uses
*   a seperate indexing based on 32 bit integer IDs.  This class provides a two way
*   mapping to and from the table IDs and the element Ids.
*
* @bsiclass                                                     RayBentley      11/2002
+===============+===============+===============+===============+===============+======*/
/*=================================================================================**//**
* @bsiclass                                                     RayBentley      11/2002
+===============+===============+===============+===============+===============+======*/
class   SignedTableIndex
{
private:
AvlTree*                        m_handleToDgnIdTree;
AvlTree*                        m_dgnIdToHandleTree;
ElementIdArray                  m_originatedInDwgList;
Int32                           m_nextDgnId;
bool                            m_negativeIdsOnly;

public:
                                SignedTableIndex (bool negativeIdsOnly);
                                ~SignedTableIndex ();

Int32                           GetDgnId (AcDbHandle dbHandle);
AcDbHandle                      GetDBHandle (Int32 dgnId);
AcDbObjectId                    GetObjectId (Int32 dgnId, AcDbDatabase* pDatabase);
Int32                           GetOrNextDgnId (AcDbHandle dbHandle);
StatusInt                       AddEntry (Int32 dgnId, AcDbHandle dbHandle, bool originatedInDwg);
StatusInt                       RemoveEntry (AcDbHandle dbHandle);
void                            SetOriginatedInDwgIds (MSElementDescrCP descr);
ElementIdArrayR                 GetOriginatedInDwgList ();
};

/*=================================================================================**//**
* @bsiclass                                                     RayBentley      11/2002
+===============+===============+===============+===============+===============+======*/
class   LayerTableIndex
{
private:
AvlTree*                        m_handleToDgnIdTree;
AvlTree*                        m_dgnIdToHandleTree;
ElementIdArray                  m_originatedInDwgList;
UInt32                          m_nextDgnId;

public:
                                LayerTableIndex ();
                                ~LayerTableIndex ();

UInt32                          GetDgnId (AcDbHandle dbHandle);
AcDbHandle                      GetDBHandle (UInt32 dgnId);
AcDbObjectId                    GetObjectId (UInt32 dgnId, AcDbDatabase* pDatabase);
UInt32                          GetOrNextDgnId (AcDbHandle dbHandle);
UInt32                          GetNextDgnId ();
StatusInt                       AddEntry (UInt32 dgnId, AcDbHandle dbHandle, bool originatedInDwg, bool colorOverride = false, bool styleOverride = false, bool weightOverride = false);
StatusInt                       RemoveEntry (AcDbHandle dbHandle);
ElementIdArrayR                 GetOriginatedInDwgList ();
bool                            IsIdTableEmpty () { return true==mdlAvlTree_isEmpty(m_handleToDgnIdTree); }

StatusInt                       GetSymbologyOverrides (bool *pColorOverride, bool *pStyleOverride, bool *pWeightOverride, UInt32 levelId);
AcDbObjectId                    GetConstructionClassChild (UInt32 levelId);
AcDbObjectId                    GetPatternClassChild (UInt32 levelId);
AcDbObjectId                    GetLinearPatternedClassChild (UInt32 levelId);
StatusInt                       SetConstructionClassChild (UInt32 levelId, AcDbObjectId layerId);
StatusInt                       SetPatternClassChild (UInt32 levelId, AcDbObjectId  layerId);
StatusInt                       SetLinearPatternedClassChild (UInt32 levelId, AcDbObjectId  layerId);
};

enum  RealDwgModelType
    {
    RDWGMODEL_TYPE_DefaultModel    = 1,          // Saved as main model
    RDWGMODEL_TYPE_NonDefaultModel = 2,          // Saved to seperate file as main model.
    RDWGMODEL_TYPE_Sheet           = 3           // Saved to "Paper Space" sheet.
    };

struct RealDwgModelIndexItem
{
private:

DgnPlatform::ModelId            m_modelId;
DgnPlatform::ModelInfoPtr       m_modelInfo;
DgnFileP                        m_dgnFile;
AcDbObjectId                    m_blockTableRecordId;
AcDbObjectId                    m_layoutId;
RealDwgModelType                m_modelType;
AcDbObjectIdArray               m_imageSequence;
bool                            m_loadInProgress;
bool                            m_annoAllVisible;
AcDbAnnotationScale*            m_annotationScale;
AcDbObjectId                    m_overallViewportId;

public:
                                RealDwgModelIndexItem (DgnPlatform::ModelId modelId, ModelInfoCP pDefaultModelInfo, AcDbObjectId blockTableRecordId, FileHolder* pFileHolder, RealDwgModelType modelType);
                                ~RealDwgModelIndexItem ();

DgnPlatform::ModelId            GetId (void)  { return m_modelId; }
WCharCP                         GetModelName () const { return m_modelInfo->GetName(); }
DgnModelType                    GetModelType () const { return m_modelInfo->GetModelType(); }
void                            GetFrozenLayers (AcDbObjectIdArray& frozenIds) const;
void                            SetFrozenLayers (DgnPlatform::ElementId* pIds, int nIds, ConvertFromDgnContext& context);
AcDbObjectId                    GetBlockTableRecordId () const { return m_blockTableRecordId; }
AcDbObjectId                    GetLayoutId () const { return m_layoutId; }
RealDwgModelType                GetRealDwgModelType () const { return m_modelType; }
ModelInfoCP                     GetModelInfoCP () const { return &*m_modelInfo; }
ModelInfoCR                     GetModelInfo () const { return *m_modelInfo; }
void                            SetModelInfo (ModelInfoCP pModelInfo);
AcDbObjectIdArray               GetImageSequence (void) const { return m_imageSequence; }
void                            SetImageSequence (AcDbObjectIdArray  sequenceIds) { m_imageSequence = sequenceIds; }
void                            SetLoadInProgress (bool inProgress) { m_loadInProgress = inProgress; }
bool                            GetLoadInProgress () const { return m_loadInProgress; }
bool                            GetAnnoAllVisible () const { return m_annoAllVisible; }
void                            SetAnnoAllVisible (bool visible) { m_annoAllVisible = visible; }
AcDbAnnotationScale*            GetAnnotationScale () const { return m_annotationScale; }
void                            SetAnnotationScale (AcDbAnnotationScale* newScale);
AcDbObjectId                    GetOverallViewportId () { return m_overallViewportId; }
};

typedef bvector<RealDwgModelIndexItem*> RealDwgModelIndexItemArray;

struct RealDwgGeneratedTextStyleItem
{
private:

WChar                           m_textStyleName[DgnPlatform::MAX_LINKAGE_STRING_LENGTH];
AcDbObjectId                    m_textStyleObjectId;

public:
                                RealDwgGeneratedTextStyleItem (WCharCP pTextStyleName, AcDbTextStyleTableRecord* pTextStyle) { wcscpy (m_textStyleName, pTextStyleName); m_textStyleObjectId = pTextStyle->objectId();}
WCharCP                         GetTextStyleName () const {return m_textStyleName;}
AcDbObjectId                    GetTextStyleObjectId () const {return m_textStyleObjectId; }
static int                      CompareEntries (const void *itemOne, const void *itemTwo)
    {
    const RealDwgGeneratedTextStyleItem *p1 = static_cast<const RealDwgGeneratedTextStyleItem *>(itemOne);
    const RealDwgGeneratedTextStyleItem *p2 = static_cast<const RealDwgGeneratedTextStyleItem *>(itemTwo);
    return  _wcsicmp(p1->GetTextStyleName (), p2->GetTextStyleName ());
    }
};

struct DgnUnderlayDefInfo
{
private:
ElementId                       m_definitionId;
bool                            m_showRasterRef;
int                             m_xrefDepth;
WString                         m_sourceFileName;
WString                         m_modelName;
bool                            m_useMasterUnit;
double                          m_acadUnitScale;
bool                            m_acadUnitScaleChecked;
DPoint3d                        m_globalOrigin;
    
public:
                                DgnUnderlayDefInfo ();
ElementId                       GetElementId () const { return m_definitionId; }
void                            SetElementId (ElementId id) { m_definitionId = id; }
bool                            GetShowRasterReference () const { return m_showRasterRef; }
void                            SetShowRasterReference (bool show) { m_showRasterRef = show; }
int                             GetXReferenceDepth () const;
void                            SetXReferenceDepth (int depth) { m_xrefDepth = depth; }
WStringCR                       GetSourceFileName () const { return m_sourceFileName; }
void                            SetSourceFileName (WCharCP name);
WStringCR                       GetModelName () const { return m_modelName; }
void                            SetModelName (WCharCP name);
double                          GetAcadUnitScale () const { return m_acadUnitScale; }
void                            SetAcadUnitScale (double scale) { m_acadUnitScale = scale; }
bool                            GetUseMasterUnit () const { return m_useMasterUnit; }
void                            SetUseMasterUnit (bool useMU) { m_useMasterUnit = useMU; }
bool                            IsAcadUnitScaleChecked () const { return m_acadUnitScaleChecked; }
void                            SetIsAcadUnitScaleChecked (bool checked) { m_acadUnitScaleChecked = checked; }
DPoint3dCR                      GetGlobalOrigin () { return m_globalOrigin; }
void                            SetGlobalOrigin (DPoint3dCR go) { m_globalOrigin = go; }
};  // DgnUnderlayDefInfo

typedef bvector<DgnUnderlayDefInfo>  DgnUnderlayDefInfoArray;


/*=================================================================================**//**
* FontMapItem
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
class           FontMapItem
{
public:

WString     m_value;
bool        m_trueType;

                                FontMapItem () {};
                                FontMapItem (WString str, bool truetype) : m_value (str), m_trueType (truetype) {};
};


/*=================================================================================**//**
* Base class for state information during the conversion to or from AutoCad Entities to Dgn Elements.
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
class       FileHolder
{
private:

AcDbDatabase*                   m_database;
DgnFileP                        m_dgnFile;
DgnPlatform::ModelInfoPtr       m_defaultModelInfo;
DwgOpenUnitMode                 m_unitMode;
bool                            m_useUorPerStorageFromSeedDirectly;
RealDwgModelIndexItemArray      m_models;
MSElementDescrP                 m_pHeaderDescr;
DgnPlatform::DimensionStylePtr  m_seedDimensionStyle;
DwgSymbologyData*               m_dwgSymbologyData;
DgnSymbologyData*               m_dgnSymbologyData;
WString                         m_fileName;
UInt32                          m_nextTextNodeNumber;
UInt32                          m_nextViewportNumber;
UInt32                          m_nextDgnUnderlayNumber;
UInt32                          m_nextPdfUnderlayNumber;
UInt32                          m_nextDwfUnderlayNumber;
LayerTableIndex*                m_layerIndex;
SignedTableIndex*               m_linetypeIndex;
SignedTableIndex*               m_textStyleIndex;
ElementIdArray                  m_dwgOriginatedDimStyles;
SignedTableIndex*               m_multilineStyleIndex;
AcDbProgressMeterP              m_loadToCacheMeter;
AvlTree*                        m_pExternalRefByAttachPathTree;
std::set<DgnFontCP>             m_rscFontsToExportTree;
AvlTree*                        m_pGeneratedTextStyles;
AvlTree*                        m_pLayerByNameTree;
AvlTree*                        m_pBlockByIdTree;
bmap<WString, FontMapItem>*     m_pFontMap;
AvlTree*                        m_pPostProcessIdTree;
AvlTree*                        m_pMaterialTree;
DgnPlatform::ElementId          m_pointDisplayBlockId;
DgnFontP                        m_defaultFont;
RotMatrix                       m_viewRotation;
AcDbObjectIdArray               m_layerEraseIds;
AcDbObjectIdArray               m_tableRecordEraseIds;
AcDbObjectIdArray*              m_lockedLayers;
AcDbObjectIdArray               m_unloadedRasterImageDefs;
AcDbObjectIdArray               m_itemTypeBlockDefList;
bool                            m_hasOldVersionAEC;
bool                            m_isC3dObjectEnablerLoaded;
DgnUnderlayDefInfoArray         m_dgnUnderlayDefInfoArray;
bool                            m_saveSheetsAsModelspace;
bvector<AcDbObjectContext*>     m_newViewportAnnotationScales;
ItemTypeLibraryPtr              m_dwgOriginatedItemtypeLibrary;
bool                            m_isDwgAttrdefItemtypeLibraryDirty;
ElementIdArray                  m_elementsExcludedFromSaving;
bool                            m_openCorruptedDwgFile;
bool                            m_openFileReadOnlyForPrinting;
bool                            m_bindingXrefsAttempted;
std::map<WString, ItemTypeLibraryPtr> m_itemLibsMap; // Cache ItemTypeLibraries in a map for using in next time. Defect 1126334:PP : Export to DWG Hangs and crashes.

public:
                                FileHolder (DgnFileP dgnFile, WCharCP pName);
                                FileHolder (DgnFileP dgnFile, WCharCP pFileName, DgnModelP defaultModelRef);
                                ~FileHolder ();

StatusInt                       LoadAcadDatabase ();
StatusInt                       DeleteModel (DgnPlatform::ModelId modelId);
StatusInt                       SaveThumbnail (const byte *pData, int dataSize);
StatusInt                       ReadModelIntoCache (DgnModelP model, DgnPlatform::ModelId modelId, bool loadDictionary);
UInt32                          GetNextAvailableLevelID ()                                  { (unsigned int) m_layerIndex->GetNextDgnId(); }
DwgSymbologyData*               GetDwgSymbologyData ()                                      { return m_dwgSymbologyData; }
DgnSymbologyData*               GetDgnSymbologyData ()                                      { return m_dgnSymbologyData; }
LayerTableIndex*                GetLayerIndex ()                                            { return m_layerIndex; }
SignedTableIndex*               GetLinetypeIndex ()                                         { return m_linetypeIndex; }
SignedTableIndex*               GetTextStyleIndex ()                                        { return m_textStyleIndex; }
ElementIdArrayR                 GetDwgOriginatedDimStyles()                                 { return m_dwgOriginatedDimStyles; }
SignedTableIndex*               GetMultilineStyleIndex()                                    { return m_multilineStyleIndex; }
RealDwgModelIndexItem*          GetActiveModelIndexItem ();
RealDwgModelIndexItem*          GetModelItemByModelId (DgnPlatform::ModelId modelId);
RealDwgModelIndexItem*          GetDefaultModelItem ();
bool                            DisplayPointAsDot ();
AcDbDatabase*                   GetDatabase ()                                          { return m_database; }
AcDbObjectId                    GetLayerByLevelId (UInt32 levelId);
AcDbObjectId                    GetLayerByLevelHandle (LevelHandle const& level);
AcDbObjectId                    GetLayerByXRefAndLevelHandle (LevelHandle const& level, AcDbBlockTableRecord* pXRefBlock, DgnModelRefP modelRef, bool savingChanges);
AcDbObjectId                    GetLayerByName (WCharCP name);
AcDbObjectId                    GetLinetypeByStyleId (Int32 styleId);
AcDbObjectId                    GetLinetypeByName (WCharCP pName);
AcDbObjectId                    GetContinuousLinetype ()                                { return m_database->continuousLinetype(); }
DwgOpenUnitMode                 GetUnitMode ()                                          { return m_unitMode; }
bool                            GetUseUorPerStorageFromSeedDirectly ()                  { return m_useUorPerStorageFromSeedDirectly; }
WStringCR                       GetFileName ()                                          { return m_fileName; }
RealDwgModelIndexItemArray&     GetModelIndexItems ()                                   { return m_models; }
const MSElementDescrP           GetHeaderDescr ()                                       { return m_pHeaderDescr; }
DimensionStyleCP                GetSeedDimensionStyle ()                                { return m_seedDimensionStyle.get(); }
UInt32                          GetShapeFontId (ACHAR const* name, DgnModelP  modelRef);
UInt32                          GetTrueTypeFontId (AcString const& typeFace, DgnModelP modelRef);
UInt32                          GetAndIncrementNextAvailableTextNodeID ()               { return m_nextTextNodeNumber++;}
UInt32                          GetAndIncrementNextAvailableViewportNumber ()           { return m_nextViewportNumber++;}
UInt32                          GetAndIncrementNextAvailableUnderlayNumber (RealDwgUnderlays type);
UInt32                          FontIdFromName (WCharCP pFontName, bool trueType);
DgnFileP                        GetFile ()                                              { return m_dgnFile;}
int                             GetModelCount ()                                        { return (int) m_models.size(); }
DgnPlatform::ElementId          GetPointDisplayBlockId ()                               { return m_pointDisplayBlockId; }
void                            SetPointDisplayBlockId (DgnPlatform::ElementId blockId) { m_pointDisplayBlockId = blockId; }
void                            PostSaveToDatabase (ConvertFromDgnContext& context);
void                            SetViewRotation (RotMatrixCR rotation)                  { m_viewRotation = rotation; }
RotMatrixCR                     GetViewRotation ()                                      { return m_viewRotation; }
AvlTree*                        GetPostProcessTree()                                    { return m_pPostProcessIdTree; }
AvlTree*                        GetGeneratedTextStyleTree()                             { return m_pGeneratedTextStyles; }
DgnFontCP                       GetDefaultFont ()                                       { return m_defaultFont; }
void                            SetDefaultFont (DgnFontP font)                          { m_defaultFont = font; }
UInt32                          GetDefaultFontID ()                                     { return NULL == m_defaultFont ? 0 : GetNumberForFont(*m_defaultFont);}
UInt32                          GetNumberForFont (DgnFontR);
DgnUnderlayDefInfoArray&        GetDgnUnderlayDefInfoArrayR ()                          { return m_dgnUnderlayDefInfoArray; }
ItemTypeLibraryP                GetDwgOriginatedItemtypeLibrary ();
void                            SetDwgOriginatedItemtypeLibrary (ItemTypeLibraryP attrdefLib);
void                            SetDwgAttrdefItemtypeLibraryDirty (bool dirty)          { m_isDwgAttrdefItemtypeLibraryDirty = dirty; }
bool                            IsBlockSavedWithItemType (AcDbObjectId const& blockId);
bool                            SetBlockSavedWithItemType (AcDbObjectId const& blockId);

void                            ScheduleLayerErase (AcDbObjectId entryId)               { m_layerEraseIds.append (entryId); }
void                            ScheduleTableRecordErase (AcDbObjectId entryId)         { m_tableRecordEraseIds.append (entryId); }

UInt64                          SetHandseed (UInt64 elementId);
void                            RestoreHandseed (UInt64 elementId);
void                            SyncHighestDgnElementIdFromHandseed ();
bool                            VerifyDesiredHandleValue (DgnPlatform::ElementId desiredId, AcDbHandle objectHandle);
bool                            NoExistingElementMatchesId (DgnPlatform::ElementId proposedId);
bool                            ExactlyOneAppended (DgnPlatform::ElementId desiredId);
bool                            AllHandlesBelowSeed (AcDbObjectId skipId);
bool                            IsOldVersionAEC ();
bool                            IsCorruptedDwgFile (AcDbDatabaseP dwg = nullptr);
bool                            IsC3dObjectEnablerLoaded ();

StatusInt                       WriteFile (WCharCP pFileName, DgnFileFormatType format, int dxfPrecision, bool useVersionFromSettings, WCharCP pProgressComment);
Acad::ErrorStatus               WriteFileAs (AcDbDatabase* pDatabase, WCharCP pFileName, DgnFileFormatType format, AcDb::AcDbDwgVersion dwgVersion, int dxfPrecision);
AcDbObjectId                    GetBlockByName (AcString const& name);
AcDbObjectId                    GetDimStyleByName (WCharCP pName);

bool                            AddModel (DgnPlatform::ModelId modelId, DgnModelType modelType, WCharCP modelName, int sheetTabOrder = -1);

AcDbObjectId                    GetActiveDimensionStyle (bool bAllowDefault);

void                            AddExternalRef (AcDbObjectId xRefBlockId, DgnAttachmentCP modelRef);

AcDbObjectId                    GetXRefBTRObjectIdByModelRef (DgnAttachmentCP modelRef);
AcDbObjectId                    GetXRefBTRObjectIdByInstanceId (DgnPlatform::ElementId instanceId);

RealDwgGeneratedTextStyleItem*  GetGeneratedTextStyle (WCharCP pStyleName);
void                            AddGeneratedTextStyle (RealDwgGeneratedTextStyleItem *pItem);

DgnPlatform::ElementId          GetTagSetDefinitionId (AcDbObjectId blockObjectId);
StatusInt                       SetTagSetDefinitionId (AcDbObjectId blockObjectId, DgnPlatform::ElementId tagSetDefinitionId);

StatusInt                       Audit (int *pNumErrors, int *pNumFixed, int *pNumEntities, void  (*pMessageFunction) (char *), bool fixErrors);
void                            ReadAcadFontMap ();
void                            AddFontToInstallTree (DgnFontCP font);
void                            ExportRscFonts (WStringCR fontPath);

void                            AddMaterialToTree (WCharCP pName, AcDbObjectId objectId);
AcDbObjectId                    GetMaterialFromTree (WCharCP pName);

void                            InitializeFromSeedModel (DgnModelP seedModelRef);
StatusInt                       LoadAcadDatabaseFromSeedDwgFile (DgnModelP defaultModelRef);

void                            RecordLockedLayers();
void                            RecallLockedLayers();

AcDbObjectIdArray const&        GetUnloadedRasterImageDefs () const;
void                            AddUnloadedRasterImageDefs (AcDbObjectId const& imageDef);
void                            AddViewportAnnotationScale (AcDbAnnotationScale* acAnnoscale);
bool                            IsElementExcludedFromSaving (ElementId id);
void                            ExcludeElementsFromSaving (ElementId from, ElementId to);
bool                            IsFileReadOnlyForPrinting () const { return m_openFileReadOnlyForPrinting; }

private:

void                            Initialize (DgnFileP dgnFile, WStringCR fileName);
void                            IndexAcadModels (void);

UInt32                          FontIdFromNameWithoutMapSearch ( WCharCP pFontName, bool trueType);
void                            RemoveRedundantSeedEntries (AcDbDatabase* pDatabase, DgnModelP modelRef);

public:

ItemTypeLibraryPtr              FindBySchemaNameWithMap ( WString SchemaName, DgnFileR dgnfile );
void                            ClearItemLibrariesMap ();
};

}   // Ends RealDWG namespace

END_BENTLEY_NAMESPACE

ADD_BENTLEY_TYPEDEFS1 (RealDwg, FileHolder, FileHolder, class);
ADD_BENTLEY_TYPEDEFS1 (RealDwg, RealDwgModelIndexItem, RealDwgModelIndexItem, struct);



