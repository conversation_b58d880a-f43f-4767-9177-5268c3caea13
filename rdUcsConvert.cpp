/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdUcsConvert.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveUcsTableRecordToDgn (AcDbUCSTableRecord* ucs)
    {
    DPoint3d                origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, ucs->origin());
    this->GetTransformToDGN().Multiply (origin);

    DVec3d                  xAxis, yAxis;
    RealDwgUtil::DVec3dFromGeVector3d (xAxis, ucs->xAxis());
    RealDwgUtil::DVec3dFromGeVector3d (yAxis, ucs->yAxis());

    RotMatrix               matrix;
    matrix.InitFrom2Vectors (xAxis, yAxis);
    matrix.Transpose ();

    AcString                name;
    if (Acad::eOk != ucs->getName(name))
        name.assign (L"");

    IAuxCoordSysPtr         acs = IACSManager::GetManager().CreateACS (ACSType::Rectangular, origin, matrix, 1.0, name, L"");
    acs->SetElementId (this->ElementIdFromObject(ucs), INVALID_MODELREF);

    EditElementHandle       acsEeh;
    StatusInt               status = IACSManager::GetManager().CreateElementFromACS (acsEeh, acs.get(), m_model);

    if (BSISUCCESS == status)
        this->LoadElementIntoCache (acsEeh);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/14
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ShouldSaveUcsToModel (AcDbUCSTableRecord* ucs, DgnModelP dgnModel)
    {
    /*------------------------------------------------------------------------------------------
    Check if the UCS should be saved to the target model:

        1) A UCS with no saved model name in the xdata should be saved to the default model only.
        2) A UCS with a matching saved model name in xdata should be saved to the target model.

    If a sheet model name is changed in ACAD, the UCS that was originally created from that model 
    is ignored.
    ------------------------------------------------------------------------------------------*/
    bool        isDefaultModel = dgnModel->GetModelId() == dgnModel->GetDgnFileP()->GetDefaultModelId();
    WCharCP     modelName = dgnModel->GetModelName ();
    if (NULL == modelName || L'\0' == modelName[0] || NULL == ucs->xData())
        return  isDefaultModel;

    RealDwgResBuf*  resbuf = RealDwgResBuf::Create (AcDb::kDxfXdAsciiString);
    if (NULL == resbuf)
        return  isDefaultModel;

    if (RealDwgXDataUtil::ExtractMicroStationXDataFromObject(*resbuf, StringConstants::XDataKey_DgnModelName, ucs))
        {
        ACHAR const*    savedName = resbuf->GetString ();
        bool            isSameModel = (NULL != savedName && 0 == wcsicmp(savedName, modelName));

        RealDwgResBuf::Free (resbuf);

        return  isSameModel;
        }

    return  isDefaultModel;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveUcsTableToDgn ()
    {
    /*-----------------------------------------------------------------------------------------------------
    This method is now called during model, instead of dictionary, filling because:
        1) ACS elements must be saved to models
        2) Since Vancouver, we aroundtrip sheet ACS's back to sheet models (via xdata)
        3) Saving & reopening an active sheet model makes the new DgnFile to not have the Default model until
           the model is explicitly reopened, hence ACS's cannot be saved while processing dictionary section.
    ------------------------------------------------------------------------------------------------------*/
    AcDbObjectId                    tableId;
    if ((tableId = m_pFileHolder->GetDatabase()->UCSTableId()).isNull())
        return NullObject;

    AcDbUCSTablePointer             pTable (tableId, AcDb::kForRead);
    AcDbUCSTableIterator*           pIter;
    if (Acad::eOk != pTable.openStatus() || Acad::eOk != pTable->newIterator (pIter))
        return CantOpenObject;

    for (pIter->start(); !pIter->done(); pIter->step())
        {
        AcDbObjectId                entityId;
        if (Acad::eOk != pIter->getRecordId (entityId))
            continue;
        AcDbUCSTableRecordPointer   ucs(entityId, AcDb::kForRead);
        if (Acad::eOk == ucs.openStatus() && ShouldSaveUcsToModel(ucs, m_model))
            this->SaveUcsTableRecordToDgn (ucs);
        }
    delete pIter;
    return RealDwgSuccess;
    }


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtAcsElement : public ToDwgExtension
{
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    IAuxCoordSysPtr     acs = IACSManager::GetManager().CreateACSFromElement (elemHandle);
    if (!acs.IsValid())
        return  MstnElementUnacceptable;
    
    AcDbUCSTablePointer ucsTable (context.GetDatabase()->UCSTableId(), AcDb::kForWrite);
    if (Acad::eOk != ucsTable.openStatus())
        return  CantOpenObject;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbUCSTableRecord::desc());

    AcDbUCSTableRecord* ucs = AcDbUCSTableRecord::cast (acObject);
    if (NULL == ucs)
        return  NullObject;

    RealDwgStatus       status = this->ConvertAcsToUcs (ucs, ucsTable, *acs.get(), context);
    if (RealDwgSuccess != status)
        return  status;

    DgnModelCP          model = context.GetModel ();
    WCharCP             modelName = NULL;
    if (NULL != model && NULL != (modelName = model->GetModelName()) && L'\0' != modelName[0] && context.GetModelIdForDwgModelSpace() != model->GetModelId())
        {
        // the ACS is not in the default model - add the model name as an xdata for roundtrip
        RealDwgResBuf*  resbuf = RealDwgResBuf::CreateString (modelName);
        if (NULL == resbuf)
            return  OutOfMemoryError;

        RealDwgXDataUtil::AddMicroStationXDataToObject (acObject, resbuf, StringConstants::XDataKey_DgnModelName, context.GetDatabase());
        }
    else if (NULL != acObject->xData())
        {
        // clear out our xdata from the UCS table record for all other cases
        RealDwgXDataUtil::RemoveXDataByAppName (acObject, StringConstants::RegAppName_MicroStation);
        }

    // We have to explicitly add the new object in the UCS table, as otherwise the caller will add it to current block.
    if (!acObject->objectId().isValid())
        context.AddRecordToSymbolTable (ucsTable, ucs, acs->GetElementId());

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertAcsToUcs (AcDbUCSTableRecord* ucs, AcDbUCSTable* ucsTable, IAuxCoordSysR acs, ConvertFromDgnContextR context) const
    {
    RotMatrix       matrix;
    // Degeneracy test - TR# 119524.
    if (0.0 == acs.GetRotation(matrix).determinant())
        {
        DIAGNOSTIC_PRINTF ("Ignoring UCS: %ls With 0 Determinant\n", acs.GetName().GetWCharCP());
        return  MstnElementUnacceptable;
        }

    WString         acsName = acs.GetName ();
    context.ValidateName (acsName);

    const ACHAR*    existingName;
    if (Acad::eOk == ucs->getName(existingName) && 0 != wcsicmp(acsName.c_str(), existingName))
        {
        AcString    newName (acsName.c_str());
        context.DeduplicateTableName (ucsTable, newName);
        ucs->setName (newName.kwszPtr());
        }

    matrix.Transpose ();
    matrix.SquareAndNormalizeColumnsAnyOrder (matrix);

    DVec3d          xAxis, yAxis;
    matrix.getColumns (&xAxis, &yAxis, NULL);
    
    ucs->setXAxis (RealDwgUtil::GeVector3dFromDVec3d(xAxis));
    ucs->setYAxis (RealDwgUtil::GeVector3dFromDVec3d(yAxis));

    DPoint3d        origin;
    acs.GetOrigin (origin);
    context.GetTransformFromDGN().Multiply (origin);
    ucs->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d(origin));

    return  RealDwgSuccess;
    }

};  // ToDwgExtAcsElement

