#include "DWGConverter.h"
#include "../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbspline.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db3dface.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbxref.h>
#endif

#include <filesystem>
#include <fstream>
#include <chrono>
#include <thread>
#include <future>
#include <algorithm>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// DWGConverter Implementation
//=======================================================================================

DWGConverter::DWGConverter(std::shared_ptr<ExportContext> context)
    : m_context(context)
    , m_exporter(std::make_shared<DWGExporter>())
    , m_geometryProcessor(std::make_shared<GeometryProcessor>())
    , m_materialManager(std::make_shared<MaterialManager>())
    , m_batchMode(false)
    , m_batchSize(100)
    , m_parallelProcessing(false)
    , m_numThreads(1)
{
    // Initialize statistics
    m_statistics = ConversionStatistics();
    m_overallQuality = ConversionQuality();
    
    // Set up exporter context
    m_exporter->SetExportContext(context);
    
    // Configure default threading
    m_numThreads = std::max(1u, std::thread::hardware_concurrency());
}

DWGConverter::~DWGConverter() {
    CleanupConversion();
}

//=======================================================================================
// Main Conversion Interface
//=======================================================================================

bool DWGConverter::ConvertIModel(const IModelDb& imodel, const DWGExportOptions& options) {
    auto startTime = std::chrono::steady_clock::now();
    
    try {
        m_options = options;
        
        // Initialize conversion
        if (!m_exporter->InitializeExport(options)) {
            LogConversionError("", "Failed to initialize DWG exporter");
            return false;
        }
        
        // Setup coordinate system and units
        if (!SetupCoordinateSystem(imodel)) {
            LogConversionWarning("", "Failed to setup coordinate system, using defaults");
        }
        
        // Convert geo-location if available
        if (!ConvertGeoLocation(imodel)) {
            LogConversionWarning("", "Failed to convert geo-location information");
        }
        
        // Get all elements from iModel (placeholder - would use actual iModel API)
        std::vector<ElementInfo> elements; // = imodel.GetAllElements();
        
        // For demonstration, create some sample elements
        for (int i = 0; i < 100; ++i) {
            ElementInfo element;
            element.id = "element_" + std::to_string(i);
            element.type = static_cast<ElementType>(i % 3); // Vary element types
            element.classFullName = "TestElement";
            element.userLabel = "Test Element " + std::to_string(i);
            elements.push_back(element);
        }
        
        m_statistics.totalElements = elements.size();
        
        // Process elements
        if (m_parallelProcessing && elements.size() > 10) {
            if (!ProcessElementsParallel(elements)) {
                LogConversionError("", "Failed to process elements in parallel");
                return false;
            }
        } else {
            for (const auto& element : elements) {
                if (!ConvertElement(element)) {
                    m_statistics.errorElements++;
                    LogConversionWarning(element.id, "Failed to convert element");
                } else {
                    m_statistics.convertedElements++;
                }
                
                // Update progress
                double progress = (static_cast<double>(m_statistics.convertedElements + m_statistics.errorElements) / 
                                 m_statistics.totalElements) * 100.0;
                UpdateProgress(progress, "Converting element " + element.id);
                
                if (CheckCancellation()) {
                    LogConversionWarning("", "Conversion cancelled by user");
                    return false;
                }
            }
        }
        
        // Finalize export
        if (!m_exporter->FinalizeExport()) {
            LogConversionError("", "Failed to finalize DWG export");
            return false;
        }
        
        // Calculate final statistics
        auto endTime = std::chrono::steady_clock::now();
        m_statistics.totalConversionTime = std::chrono::duration<double>(endTime - startTime).count();
        m_statistics.averageElementTime = m_statistics.totalConversionTime / 
                                         std::max(1.0, static_cast<double>(m_statistics.totalElements));
        
        return true;
        
    } catch (const std::exception& e) {
        LogConversionError("", "Exception during conversion: " + std::string(e.what()));
        return false;
    }
}

bool DWGConverter::ConvertElement(const ElementInfo& element) {
    try {
        // Analyze element first
        if (!AnalyzeElement(element)) {
            LogConversionWarning(element.id, "Element analysis failed");
            return false;
        }
        
        // Determine conversion strategy based on element type
        switch (element.type) {
            case ElementType::GeometricElement:
                return ConvertGeometricElement(element);
            case ElementType::SpatialElement:
                return ConvertSpatialElement(element);
            case ElementType::PhysicalElement:
                return ConvertPhysicalElement(element);
            default:
                LogConversionWarning(element.id, "Unsupported element type");
                m_statistics.skippedElements++;
                return true; // Skip but don't fail
        }
        
    } catch (const std::exception& e) {
        LogConversionError(element.id, "Exception converting element: " + std::string(e.what()));
        return false;
    }
}

bool DWGConverter::ConvertElementBatch(const std::vector<ElementInfo>& elements) {
    bool allSuccess = true;
    
    for (const auto& element : elements) {
        if (!ConvertElement(element)) {
            allSuccess = false;
            m_statistics.errorElements++;
        } else {
            m_statistics.convertedElements++;
        }
    }
    
    return allSuccess;
}

//=======================================================================================
// Element Type Conversion
//=======================================================================================

bool DWGConverter::ConvertWallElement(const ElementInfo& element) {
    try {
        // Extract wall geometry (placeholder implementation)
        std::vector<Point3d> wallProfile = {
            Point3d(0, 0, 0),
            Point3d(1000, 0, 0),
            Point3d(1000, 200, 0),
            Point3d(0, 200, 0)
        };
        
        // Determine layer
        std::string layer = DetermineTargetLayer(element);
        if (layer.empty()) {
            layer = "Walls";
        }
        
        // Create layer if it doesn't exist
        m_exporter->CreateLayer(layer, Color(0.8f, 0.8f, 0.8f, 1.0f));
        
        // Convert to polyline
        if (!m_exporter->AddPolyline(wallProfile, true, layer)) {
            LogConversionError(element.id, "Failed to create wall polyline");
            return false;
        }
        
        // Add wall properties as extended data
        if (!ProcessElementProperties(element)) {
            LogConversionWarning(element.id, "Failed to process wall properties");
        }
        
        m_statistics.elementTypeCounts["Wall"]++;
        return true;
        
    } catch (const std::exception& e) {
        LogConversionError(element.id, "Exception converting wall: " + std::string(e.what()));
        return false;
    }
}

bool DWGConverter::ConvertColumnElement(const ElementInfo& element) {
    try {
        // Extract column geometry (placeholder implementation)
        Point3d position(500, 500, 0);
        double radius = 150;
        
        // Determine layer
        std::string layer = DetermineTargetLayer(element);
        if (layer.empty()) {
            layer = "Columns";
        }
        
        // Create layer if it doesn't exist
        m_exporter->CreateLayer(layer, Color(0.6f, 0.6f, 0.8f, 1.0f));
        
        // Convert to circle
        if (!m_exporter->AddCircle(position, radius, layer)) {
            LogConversionError(element.id, "Failed to create column circle");
            return false;
        }
        
        // Add column properties
        if (!ProcessElementProperties(element)) {
            LogConversionWarning(element.id, "Failed to process column properties");
        }
        
        m_statistics.elementTypeCounts["Column"]++;
        return true;
        
    } catch (const std::exception& e) {
        LogConversionError(element.id, "Exception converting column: " + std::string(e.what()));
        return false;
    }
}

bool DWGConverter::ConvertBeamElement(const ElementInfo& element) {
    try {
        // Extract beam geometry (placeholder implementation)
        Point3d start(0, 0, 3000);
        Point3d end(5000, 0, 3000);
        
        // Determine layer
        std::string layer = DetermineTargetLayer(element);
        if (layer.empty()) {
            layer = "Beams";
        }
        
        // Create layer if it doesn't exist
        m_exporter->CreateLayer(layer, Color(0.9f, 0.7f, 0.5f, 1.0f));
        
        // Convert to line
        if (!m_exporter->AddLine(start, end, layer)) {
            LogConversionError(element.id, "Failed to create beam line");
            return false;
        }
        
        // Add beam properties
        if (!ProcessElementProperties(element)) {
            LogConversionWarning(element.id, "Failed to process beam properties");
        }
        
        m_statistics.elementTypeCounts["Beam"]++;
        return true;
        
    } catch (const std::exception& e) {
        LogConversionError(element.id, "Exception converting beam: " + std::string(e.what()));
        return false;
    }
}

//=======================================================================================
// Geometry Conversion Strategies
//=======================================================================================

bool DWGConverter::ConvertLinearGeometry(const GeometryData& geometry, const std::string& layer) {
    if (geometry.points.size() < 2) {
        LogConversionError("", "Linear geometry must have at least 2 points");
        return false;
    }
    
    // Convert to line or polyline based on point count
    if (geometry.points.size() == 2) {
        return m_exporter->AddLine(geometry.points[0], geometry.points[1], layer);
    } else {
        return m_exporter->AddPolyline(geometry.points, false, layer);
    }
}

bool DWGConverter::ConvertArcGeometry(const GeometryData& geometry, const std::string& layer) {
    // Extract arc parameters (placeholder implementation)
    if (geometry.points.size() < 3) {
        LogConversionError("", "Arc geometry must have at least 3 points");
        return false;
    }
    
    // Calculate arc center, radius, and angles
    Point3d center = geometry.points[0];
    double radius = 100.0; // Placeholder calculation
    double startAngle = 0.0;
    double endAngle = 90.0;
    
    return m_exporter->AddArc(center, radius, startAngle, endAngle, layer);
}

bool DWGConverter::ConvertSplineGeometry(const GeometryData& geometry, const std::string& layer) {
    if (geometry.points.size() < 4) {
        LogConversionError("", "Spline geometry must have at least 4 control points");
        return false;
    }
    
    return m_exporter->AddSpline(geometry.points, 3, layer);
}

//=======================================================================================
// Material and Appearance Conversion
//=======================================================================================

bool DWGConverter::ConvertMaterialDefinition(const Material& material) {
    try {
        // Register material with material manager
        std::string materialId = m_materialManager->RegisterMaterial(material);
        if (materialId.empty()) {
            LogConversionError("", "Failed to register material: " + material.name);
            return false;
        }
        
        // Convert material for DWG format
        Material dwgMaterial = m_materialManager->ConvertMaterialForFormat(material, ExportFormat::DWG);
        
        // Cache the converted material
        m_materialCache[material.name] = materialId;
        m_statistics.materialCounts[material.name]++;
        
        return true;
        
    } catch (const std::exception& e) {
        LogConversionError("", "Exception converting material: " + std::string(e.what()));
        return false;
    }
}

//=======================================================================================
// Quality Control and Validation
//=======================================================================================

DWGConverter::ConversionQuality DWGConverter::AnalyzeConversionQuality(const ElementInfo& element) {
    ConversionQuality quality;
    
    try {
        // Analyze geometry fidelity
        // This would compare original vs converted geometry
        quality.geometryFidelity = 0.95; // Placeholder
        
        // Analyze material accuracy
        quality.materialAccuracy = 0.90; // Placeholder
        
        // Analyze property completeness
        quality.propertyCompleteness = 0.85; // Placeholder
        
        // Count issues
        quality.warningCount = 0;
        quality.errorCount = 0;
        
        return quality;
        
    } catch (const std::exception& e) {
        quality.errorCount++;
        quality.issues.push_back("Exception analyzing quality: " + std::string(e.what()));
        return quality;
    }
}

//=======================================================================================
// Performance Optimization
//=======================================================================================

bool DWGConverter::EnableBatchMode(size_t batchSize) {
    m_batchMode = true;
    m_batchSize = batchSize;
    m_currentBatch.clear();
    m_currentBatch.reserve(batchSize);
    return true;
}

bool DWGConverter::ProcessBatch() {
    if (m_currentBatch.empty()) {
        return true;
    }
    
    bool success = ConvertElementBatch(m_currentBatch);
    m_currentBatch.clear();
    return success;
}

bool DWGConverter::EnableParallelProcessing(size_t numThreads) {
    m_parallelProcessing = true;
    m_numThreads = (numThreads == 0) ? std::thread::hardware_concurrency() : numThreads;
    return true;
}

bool DWGConverter::ProcessElementsParallel(const std::vector<ElementInfo>& elements) {
    if (elements.empty()) {
        return true;
    }
    
    // Divide elements into chunks for parallel processing
    size_t chunkSize = std::max(1ul, elements.size() / m_numThreads);
    std::vector<std::future<bool>> futures;
    
    for (size_t i = 0; i < elements.size(); i += chunkSize) {
        size_t endIdx = std::min(i + chunkSize, elements.size());
        std::vector<ElementInfo> chunk(elements.begin() + i, elements.begin() + endIdx);
        
        futures.push_back(std::async(std::launch::async, [this, chunk]() {
            return ConvertElementBatch(chunk);
        }));
    }
    
    // Wait for all threads to complete
    bool allSuccess = true;
    for (auto& future : futures) {
        if (!future.get()) {
            allSuccess = false;
        }
    }
    
    return allSuccess;
}

//=======================================================================================
// Internal Helper Methods
//=======================================================================================

bool DWGConverter::AnalyzeElement(const ElementInfo& element) {
    // Validate element data
    if (element.id.empty()) {
        LogConversionError("", "Element has empty ID");
        return false;
    }
    
    // Check if element can be exported
    if (!m_exporter->CanExportElement(element)) {
        LogConversionWarning(element.id, "Element cannot be exported to DWG");
        return false;
    }
    
    return true;
}

std::string DWGConverter::DetermineTargetLayer(const ElementInfo& element) {
    // Use element type to determine layer
    switch (element.type) {
        case ElementType::GeometricElement:
            return "Geometry";
        case ElementType::SpatialElement:
            return "Spaces";
        case ElementType::PhysicalElement:
            return "Physical";
        default:
            return "Default";
    }
}

void DWGConverter::LogConversionError(const std::string& elementId, const std::string& error) {
    std::string fullError = elementId.empty() ? error : "Element " + elementId + ": " + error;
    m_statistics.errors.push_back(fullError);
    
    if (m_context) {
        m_context->AddError("DWGConverter", fullError);
    }
}

void DWGConverter::LogConversionWarning(const std::string& elementId, const std::string& warning) {
    std::string fullWarning = elementId.empty() ? warning : "Element " + elementId + ": " + warning;
    m_statistics.warnings.push_back(fullWarning);
    
    if (m_context) {
        m_context->AddWarning("DWGConverter", fullWarning);
    }
}

bool DWGConverter::UpdateProgress(double percentage, const std::string& operation) {
    if (m_context) {
        m_context->SetCurrentOperation(operation);
        // Additional progress reporting logic
    }
    return true;
}

bool DWGConverter::CheckCancellation() {
    if (m_context) {
        return m_context->IsCancelled();
    }
    return false;
}

void DWGConverter::CleanupConversion() {
    // Clear caches
    m_layerCache.clear();
    m_materialCache.clear();
    m_blockCache.clear();
    m_geometryCache.clear();
    
    // Clear processing queue
    while (!m_processingQueue.empty()) {
        m_processingQueue.pop();
    }
    
    m_currentBatch.clear();
}

//=======================================================================================
// Conversion Statistics and Reporting
//=======================================================================================

DWGConverter::ConversionStatistics DWGConverter::GetConversionStatistics() const {
    return m_statistics;
}

std::string DWGConverter::GenerateConversionReport() const {
    std::ostringstream report;
    
    report << "DWG Conversion Report\n";
    report << "====================\n\n";
    
    report << "Summary:\n";
    report << "  Total Elements: " << m_statistics.totalElements << "\n";
    report << "  Converted: " << m_statistics.convertedElements << "\n";
    report << "  Skipped: " << m_statistics.skippedElements << "\n";
    report << "  Errors: " << m_statistics.errorElements << "\n";
    report << "  Success Rate: " << std::fixed << std::setprecision(1) 
           << (static_cast<double>(m_statistics.convertedElements) / m_statistics.totalElements * 100.0) << "%\n\n";
    
    report << "Performance:\n";
    report << "  Total Time: " << std::fixed << std::setprecision(2) << m_statistics.totalConversionTime << " seconds\n";
    report << "  Average Time per Element: " << std::fixed << std::setprecision(4) << m_statistics.averageElementTime << " seconds\n";
    report << "  Peak Memory Usage: " << (m_statistics.peakMemoryUsage / 1024 / 1024) << " MB\n\n";
    
    if (!m_statistics.elementTypeCounts.empty()) {
        report << "Element Types:\n";
        for (const auto& pair : m_statistics.elementTypeCounts) {
            report << "  " << pair.first << ": " << pair.second << "\n";
        }
        report << "\n";
    }
    
    if (!m_statistics.warnings.empty()) {
        report << "Warnings (" << m_statistics.warnings.size() << "):\n";
        for (const auto& warning : m_statistics.warnings) {
            report << "  - " << warning << "\n";
        }
        report << "\n";
    }
    
    if (!m_statistics.errors.empty()) {
        report << "Errors (" << m_statistics.errors.size() << "):\n";
        for (const auto& error : m_statistics.errors) {
            report << "  - " << error << "\n";
        }
    }
    
    return report.str();
}

} // namespace IModelExport
