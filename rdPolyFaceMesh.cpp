/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdPolyFaceMesh.cpp $
|
|  $Copyright: (c) 2013 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPolyFaceMesh : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    UInt32              maxPerFace      = 4;
    bool                bAddColor       = false;
    AcDbPolyFaceMesh*   acMesh          = AcDbPolyFaceMesh::cast (acObject);

    // build DGN polyface header:
    PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateFixedBlockIndexed (maxPerFace);
    BlockedVectorDPoint3dR  pointArray = dgnPolyface->Point ();
    BlockedVectorIntR       indexArray = dgnPolyface->PointIndex ();

    AcDbObjectIterator*     pIterator = acMesh->vertexIterator();
    for (; !pIterator->done(); pIterator->step())
        {
        AcDbEntityPointer   acEntity (pIterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != acEntity.openStatus())
            {
            delete pIterator;
            return  ErrorOpeningVertex;
            }

        AcDbPolyFaceMeshVertex* acVertex;
        AcDbFaceRecord*         acFace;
        if (NULL != (acVertex = AcDbPolyFaceMeshVertex::cast(acEntity.object())))
            {
            // add a vertex
            DPoint3d        point;
            pointArray.push_back (RealDwgUtil::DPoint3dFromGePoint3d(point, acVertex->position()));
            }
        else if (NULL != (acFace = AcDbFaceRecord::cast(acEntity.object())))
            {
            // add a face
            bvector<int>        pointIndices;
            for (UInt32 jFace = 0; jFace < maxPerFace; jFace++)
                {
                Adesk::Int16    index = 0;
                if (Acad::eOk != acFace->getVertexAt(jFace, index))
                    DIAGNOSTIC_PRINTF ("Error getting FACE(id=%I64d) vertex at %d\n", context.ElementIdFromObject(acFace), jFace);
                pointIndices.push_back (index);
                }
            dgnPolyface->AddIndexedFacet (pointIndices);

            bAddColor |= (acMesh->colorIndex() != acFace->colorIndex());
            }
        }
    delete pIterator;

     // add colors to polyface header
    if (bAddColor)
        {
        BlockedVectorIntR       colorIndexArray = dgnPolyface->ColorIndex ();
        BlockedVectorUInt32R    colorTableArray = dgnPolyface->ColorTable ();

        // set table colors and table color indices for DWG face colors:
        colorIndexArray.SetTags (1, 1, MESH_ELM_TAG_FACE_TO_TABLE_COLOR_INDICES, MESH_ELM_INDEX_FAMILY_BY_FACE, MESH_ELM_INDEX_FAMILY_NONE, true);
        colorTableArray.SetTags (1, 1, MESH_ELM_TAG_TABLE_COLOR, MESH_ELM_INDEX_FAMILY_NONE, MESH_ELM_TAG_FACE_LOOP_TO_TABLE_COLOR_INDICES, true);

        int                     cachedIndex = 0, colorArrayTail = 0, colorArrayInverse[257];

        // init inverse lookup table
        for (int i=0; i<257; i++)
            colorArrayInverse[i] = -1;

        for (pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
            {
            AcDbEntityPointer   acEntity (pIterator->objectId(), AcDb::kForRead);
            if (Acad::eOk == acEntity.openStatus() && acEntity->isKindOf(AcDbFaceRecord::desc()))
                {
                UInt32          dgnColorIndex  = context.GetDgnColor (acEntity->colorIndex());

                if (dgnColorIndex <= 255)
                    {
                    // add new color index to the colorArray
                    cachedIndex = colorArrayInverse[dgnColorIndex];
                    if (cachedIndex < 0)
                        {
                        cachedIndex = colorArrayInverse[dgnColorIndex] = colorArrayTail;
                        colorTableArray.push_back ((int)dgnColorIndex); // insert @ colorArrayTail
                        colorArrayTail++;
                        }

                    // add 1-based index into the colorArray
                    colorIndexArray.push_back (cachedIndex + 1);
                    }
                else
                    {
                    colorTableArray.push_back ((int)dgnColorIndex); // insert @ colorArrayTail
                    colorArrayTail++;
                    colorIndexArray.push_back (colorArrayTail);
                    }
                }
            }
        delete pIterator;
        }

    // Transform to UORs
    dgnPolyface->Transform (context.GetTransformToDGN(), false);

    // Create Element
    BentleyStatus  status = MeshHeaderHandler::CreateMeshElement (outElement, NULL, *dgnPolyface, context.GetThreeD(), *context.GetModel());

    if (SUCCESS == status)
        context.ElementHeaderFromEntity (outElement, acMesh);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

};  // ToDgnExtPolyFaceMesh
