/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdHatch.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
static void keepAlive(double a) {

    }

// copied from topaz ChainHeaderHandlers.cpp
// to be applied as cleanuup before regions.
static CurveVectorPtr cloneWithGapsClosed(CurveVectorR original,
    double equalPointFactor,     // equalPointTolerance is (equlPointFactor * ResolveToTolerance)
    double gapMoveFactor,      // typically 100 or 1000 to be applied to equalPointTolerance. Line endpoint may move this much
    double gapExtensionFactor   // typically 100 or 1000 to mulitply equalPointTolerance.   Element end may move this far along extension.
)
    {
    double      equalPointTolerance = equalPointFactor * original.ResolveTolerance(0.0);
    double maxGapWithinPath = original.MaxGapWithinPath();
    double length = original.Length();
    DRange3d range;
    keepAlive(maxGapWithinPath);
    keepAlive(length);
    original.GetRange(range);
    CurveGapOptions gapOptions(equalPointTolerance, gapMoveFactor * equalPointTolerance, gapExtensionFactor * equalPointTolerance);
    auto cvB = original.CloneWithGapsClosed(gapOptions);
    return cvB;
    }

// This file is #included in rDwgDgnExtension.cpp
// NOTE (XXX) (EDL Dec 2019)
//   DEFECT#1065548
// * input is a region with jagged small variation in lines.
// * The call to AddOrSpliceChildCurve corrects the immediate problem.
// * Additional logic for aggressive compression is noted in comments.
// * It is inconsistent have do AddOrSpliceChildCurve apply compression to line-line-line input but NOT have
//     corresponding compresson on linestrings.  But leave it as is for minimum change in behavior.
#if defined (COMMENT)
This is the RecordingFiler dump of a fairly complicated hatch from the test file hatch8.dwg.

It is associative.
It has four loops
    Loop 1 is the hole on the left of the hatch.
    Loop 2 is the circular island in the middle of:
    Loop 3 the elliptical hole on the right.
The fourth loop has four edges
    1. Spline
    2. Elliptical Arc
    3. Spline
    4. Elliptical Arc

  0: kDwgSoftPointerId, ffe01cc0 fdcb8db3 AcDbBlockTableRecord      // AcDbObject:      This is a pointer to the owner of the Hatch.
  1: kDwgUInt32,    0                                               // AcDbObject:      This is the number of reactors. Would be followed by the SoftPointerIds of the reactors, if there were any.
      <Here we would one SoftPointerId for each reactor>
  2: kDwgHardOwnershipId, NULL                                      // AcDbObject:      This is a pointer to the extension dictionary, if there is one.
  3: kDwgHardPointerId, ffe01c70 fdcb8d03 AcDbLayerTableRecord      // AcDbEntity:      The layer that this entity is on.
  4: kDwgHardPointerId, ffe01c98 fdcb8deb AcDbLinetypeTableReco     // AcDbEntity:      The lineType
  5: kDwgHardPointerId, ffe01de8 fdcb8c9b AcDbMaterial              // AcDbEntity:      The Material
  6: kDwgUInt8,    0 (0x0)
  7: kDwgBool false
  8: kDwgBool false
  9 - 11                                                            // R2010 added 3 kDwgBool's, at 9,10 and 11

 12: kDwgUInt16,    2 (0x2)                                         // AcDbEntity:      The colorIndex.
                                                                    // If (0 != colorIndex & 0x8000)
                                                                    //   {   Complex color.
                                                                    //   if (0 != colorIndex & 0x4000)
                                                                    //       AcDbHardPointerId AcDbColorRef.
                                                                    //   else
                                                                    //       AcDbUInt32 color RGB
                                                                    //   if (0 != colorIndex & 0x2000)
                                                                    //       AcDbInt32 transparency
 13: kDwgReal 1.000000                                              // AcDbEntity:      The line type scale
 14: kDwgUInt16,    0 (0x0)
 15: kDwgUInt8,   28 (0x1c)                                         //

 16: kDwgUInt32,    0 (0x0)                                         // AcDbHatch:       1 if it's a gradient fill, 0 otherwise.
 17: kDwgUInt32,    0 (0x0)                                         //                  Reserved?
 18: kDwgReal 0.000000                                              //                  Gradient shift
 19: kDwgReal 0.000000                                              //                  Gradient angle
 20: kDwgUInt32,    0 (0x0)                                         //                  If 1, single color gradient, otherwise multicolors.
 21: kDwgReal 0.000000                                              //                  Gradient color tint.
 22: kDwgUInt32,    0 (0x0)                                         //                  Number of gradient colors
     <For each color, there's a kDwgReal, kDwgUInt16, kDwgUInt32, kDwgUInt8 sequence).

 23: kDwgString(ACHAR)                                              //                  Gradient name
 24: kDwgReal 0.000000                                              //                  Elevation
 25: AcGeVector3d, 0.000000, 0.000000, 1.000000                     //                  Extrusion normal.
 26: kDwgString(ACHAR) ACAD_ISO11W100                               //                  Name of hatch.
 27: kDwgBool false                                                 //                  Solid fill
 28: kDwgBool true                                                  //                  Associative
 29: kDwgInt32,    4 (0x4)                                          //                  Num loops.

 30: kDwgInt32,    0 (0x0)                                          // AcDbHatch:Loop   AcDbHatch::HatchLoopType bits : 0 = Default; 1 = External; 2 = Polyline; 4 = Derived; 8 = Textbox; 16 = Outermost
 31: kDwgInt32,    1 (0x1)                                          //                  Edges within loop, if not flags.polyline not set.
 32: kDwgUInt8,    2 (0x2)                                          //                  AcDbHatch::HatchEdgeType (This one is a circular arc)
 33: AcGePoint2d, 10.371565, 0.470275, 0.000000                     //                  Circular arc center.
 34: kDwgReal 0.348033                                              //                  Circular arc radius.
 35: kDwgReal 0.000000                                              //                  Circular arc start angle
 36: kDwgReal 6.283185                                              //                  Circular arc sweep.
 37: kDwgBoolean true                                               //                  Circular arc isClockwise.
 38: kDwgInt32,    1 (0x1)                                          //                  Number of Associative Ids.
 39: kDwgSoftPointerId, ffe01d60 fdcb8c13 AcDbCircle                //                  A SoftPointerId for each associative Id.

 40: kDwgInt32,   16 (0x10)                                         // AcDbHatch:Loop   AcDbHatch::HatchLoopType (Outermost? doesn't make sense.)
 41: kDwgInt32,    1 (0x1)                                          //                  Edges within loop
 42: kDwgUInt8,    2 (0x2)                                          //                  AcDbHatch::HatchEdgeType (another circular arc)
 43: AcGePoint2d, -0.582476, -0.563326, 0.000000                    //                  Circular arc center.
 44: kDwgReal 0.873905                                              //                  Circular arc radius.
 45: kDwgReal 0.000000                                              //                  Circular arc start angle
 46: kDwgReal 6.283185                                              //                  Circular arc sweep.
 47: kDwgBoolean true                                               //                  Circular arc isCounterClockwise.
 48: kDwgInt32,    1 (0x1)                                          //                  Number of Associative Ids.
 49: kDwgSoftPointerId, ffe01d58 fdcb8c2b AcDbCircle                //                  A SoftPointerId for each associative Id.

 50: kDwgInt32,   16 (0x10)                                         // AcDbHatch:Loop   AcDbHatch::HatchLoopType (Outermost? doesn't make sense.)
 51: kDwgInt32,    1 (0x1)                                          //                  Edges within loop
 52: kDwgUInt8,    3 (0x3)                                          //                  AcDbHatch::HatchEdgeType (elliptical arc)
 53: AcGePoint2d, 10.322334, 0.494885, 0.000000                     //                  Elliptical arc center
 54: AcGeVector2d, 0.000000, 1.208219, 0.000000                     //                  Elliptical arc major axis
 55: kDwgReal 0.488967                                              //                  Elliptical arc ratio of minor/major axis
 56: kDwgReal 0.000000                                              //                  Elliptical arc start
 57: kDwgReal 6.283185                                              //                  Elliptical arc sweep
 58: kDwgBoolean true                                               //                  Elliptical arc isCounterClockwise
 59: kDwgInt32,    1 (0x1)                                          //                  Number of Associative Ids.
 60: kDwgSoftPointerId, ffe01d50 fdcb8c23 AcDbEllipse               //                  A SoftPointerId for each associative Id.

 61: kDwgInt32,    1 (0x1)                                          // AcDbHatch::Loop  AcDbHatch::HatchLoopType (External)
 62: kDwgInt32,    4 (0x4)                                          //                  Edges within loop (4)

                                                                    // (This is the first loop in this path)
 63: kDwgUInt8,    4 (0x4)                                          //                  AcDbHatch::HatchEdgeType (spline)
 64: kDwgInt32,    3 (0x3)                                          //                  Spline degree.
 65: kDwgBoolean false                                              //                  Spline rational
 66: kDwgBoolean false                                              //                  Spline periodic
 67: kDwgInt32,   12 (0xc)                                          //                  Spline Number of knots
 68: kDwgInt32,    8 (0x8)                                          //                  Spline Number of poles
 69: kDwgReal 0.000000                                              //                  NumKnots doubles
 70: kDwgReal 0.000000
 71: kDwgReal 0.000000
 72: kDwgReal 0.000000
 73: kDwgReal 2.546795
 74: kDwgReal 6.785344
 75: kDwgReal 9.871243
 76: kDwgReal 14.581232
 77: kDwgReal 15.643322
 78: kDwgReal 15.643322
 79: kDwgReal 15.643322
 80: kDwgReal 15.643322
 81: AcGePoint2d, 12.124104, -1.699852, 0.000000                    //                  NumPoles points.
 82: AcGePoint2d, 11.319482, -1.423656, 0.000000                    //                  Note: If rational, there would be a weight after each pole.
 83: AcGePoint2d, 9.175752, -0.687794, 0.000000
 84: AcGePoint2d, 5.798296, -0.600809, 0.000000
 85: AcGePoint2d, 1.961083, -0.624103, 0.000000
 86: AcGePoint2d, -1.419584, -3.569433, 0.000000
 87: AcGePoint2d, -2.573327, -1.857371, 0.000000
 88: AcGePoint2d, -2.785621, -1.542343, 0.000000
 89: kDwgInt32,    0 (0x0)                                          // R2010 added a kDwgInt32, at the new index=89

                                                                    // (This is the second loop in this path)
 90: kDwgUInt8,    3 (0x3)                                          //                  AcDbHatch::HatchEdgeType (Elliptical Arc, as above)
 91: AcGePoint2d, -1.006206, -0.019963, 0.000000
 92: AcGeVector2d, 1.685579, 3.082988, 0.000000
 93: kDwgReal 0.302375
 94: kDwgReal 4.039685
 95: kDwgReal 5.231654
 96: kDwgBoolean false
                                                                    // (This is the third loop in this path)
 97: kDwgUInt8,    4 (0x4)                                          //                  AcDbHatch::HatchEdgeType (Spline, as above).
 98: kDwgInt32,    3 (0x3)
 99: kDwgBoolean false
100: kDwgBoolean false
101: kDwgInt32,   11 (0xb)
102: kDwgInt32,    7 (0x7)
103: kDwgReal 0.000000
104: kDwgReal 0.000000
105: kDwgReal 0.000000
106: kDwgReal 0.000000
107: kDwgReal 0.821693
108: kDwgReal 3.932392
109: kDwgReal 9.450546
110: kDwgReal 13.314207
111: kDwgReal 13.314207
112: kDwgReal 13.314207
113: kDwgReal 13.314207
114: AcGePoint2d, -0.979087, 1.952441, 0.000000
115: AcGePoint2d, -0.695313, 2.010470, 0.000000
116: AcGePoint2d, 0.662747, 2.288184, 0.000000
117: AcGePoint2d, 2.076200, -2.175520, 0.000000
118: AcGePoint2d, 7.044223, 1.160791, 0.000000
119: AcGePoint2d, 9.770555, 2.217232, 0.000000
120: AcGePoint2d, 10.893325, 2.652300, 0.000000
121: kDwgInt32,    0 (0x0)                                          // R2010 - same deal as the one above at 89?

                                                                    // (This is the fourth and last loop in this path.)
122: kDwgUInt8,    3 (0x3)                                          //                  AcDbHatch::HatchEdgeType (Elliptical Arc, as above)
123: AcGePoint2d, 10.821318, -0.311207, 0.000000
124: AcGeVector2d, -0.195740, -2.964818, 0.000000
125: kDwgReal 0.520770
126: kDwgReal 3.061773
127: kDwgReal 5.165158
128: kDwgBoolean false

129: kDwgInt32,    4 (0x4)                                          // These are the associative AcDbObjects for this loop. In this case there are four.
130: kDwgSoftPointerId, ffe01d48 fdcb8c3b AcDbSpline
131: kDwgSoftPointerId, ffe01d28 fdcb8c5b AcDbEllipse
132: kDwgSoftPointerId, ffe01d40 fdcb8c33 AcDbSpline
133: kDwgSoftPointerId, ffe01d38 fdcb8c4b AcDbEllipse

134: kDwgInt16,    0 (0x0)                                          // AcDbHatch::HatchStyle (in this case, normal)
135: kDwgInt16,    1 (0x1)                                          // AcDbHatch::HatchPatternType (in this case, pre-defined)

                                                                    // This section is missing if isSolidFill (dataList[24]) is true.
136: kDwgReal 0.785398                                              // Hatch angle
137: kDwgReal 0.050000                                              // Hatch spacing
138: kDwgBool false                                                 // True for double hatch (crosshatch)
                                                                    // The hatch line definitions
139: kDwgUInt16,    1 (0x1)                                         // Number of hatch line definitions. (In this case 1)
140: kDwgReal 0.785398                                              // Line angle
141: kDwgReal 0.000000                                              // BasePoint.x
142: kDwgReal 0.000000                                              // BasePoint.y
143: kDwgReal -0.176777                                             // PatternOffset.x
144: kDwgReal 0.176777                                              // PatternOffset.y
145: kDwgUInt16,    6 (0x6)                                         // Number of dashes (In this case 6)
146: kDwgReal 0.600000                                              // Dash definitions
147: kDwgReal -0.150000
148: kDwgReal 0.600000
149: kDwgReal -0.150000
150: kDwgReal 0.025000
151: kDwgReal -0.150000


// If we need the pixel size, it is written here.
// If we need the seed points, the number of the seed points is in an Int32, followed by numSeedPoints points.

152: kDwgInt32,    1 (0x1)                                          // I Don't know what this stuff is. Looks like some kind of annotation scale repeat of the hatch definition.
153: AcGePoint2d, 0.000000, 0.000000, 0.000000
154: kDwgBool true
155: kDwgHardPointerId, NULL
156: kDwgInt32,    2 (0x2)
157: kDwgString(ACHAR) ACDB_ANNOTATIONSCALES
158: kDwgHardPointerId, NULL
159: kDwgBool false
160: kDwgBool true
161: kDwgInt32,    0 (0x0)
162: kDwgInt16,    3 (0x3)
163: kDwgBool true
164: kDwgBool true
165: kDwgHardPointerId, NULL
166: kDwgUInt16,    1 (0x1)
167: kDwgReal 0.785398
168: kDwgReal 0.000000
169: kDwgReal 0.000000
170: kDwgReal -0.176777
171: kDwgReal 0.176777
172: kDwgUInt16,    6 (0x6)
173: kDwgReal 0.600000
174: kDwgReal -0.150000
175: kDwgReal 0.600000
176: kDwgReal -0.150000
177: kDwgReal 0.025000
178: kDwgReal -0.150000
179: kDwgReal 1.000000
180: AcGeVector3d, 0.000000, 0.000000, 0.000000
181: kDwgInt32,    4 (0x4)
182: kDwgInt32,    0 (0x0)
183: kDwgBool true
184: kDwgInt32,   16 (0x10)
185: kDwgBool true
186: kDwgInt32,   16 (0x10)
187: kDwgBool true
188: kDwgInt32,    1 (0x1)
189: kDwgBool true
190: kDwgString(ACHAR) ACDB_ANNOTATIONSCALE_VIEW_COLLECTION
191: kDwgHardPointerId, NULL
192: kDwgBool false
193: kDwgBool true
194: kDwgInt32,    0 (0x0)
195: kDwgInt16,    3 (0x3)
196: kDwgBool true
197: kDwgBool true
198: kDwgHardPointerId, NULL
199: kDwgUInt16,    0 (0x0)
200: kDwgReal 1.000000
201: AcGeVector3d, 0.000000, 0.000000, 0.000000
202: kDwgInt32,    4 (0x4)
203: kDwgInt32,    0 (0x0)
204: kDwgBool true
205: kDwgInt32,   16 (0x10)
206: kDwgBool true
207: kDwgInt32,   16 (0x10)
208: kDwgBool true
209: kDwgInt32,    1 (0x1)
210: kDwgBool true
211: kDwgSoftPointerId, NULL
212: AcGeVector3d, 0.000000, 0.000000, 1.000000
213: kDwgReal 0.000000
214: kDwgBool false
#endif

struct              SeedData
    {
    int             m_seedPointsExpected;
    int             m_seedPointsSeen;
    double*         m_hatchPixelSize;
    DPoint3dArray*  m_seedPoints;
    Transform       m_transform;
    bool            m_hasDerivedLoops;
    SeedData (DPoint3dArray* seedPoints, double* hatchPixelSize, TransformCR transform, bool derivedLoops)
        {
        m_seedPointsExpected    = 0;
        m_seedPointsSeen        = 0;
        m_seedPoints            = seedPoints;
        m_hatchPixelSize        = hatchPixelSize;
        m_transform             = transform;
        m_hasDerivedLoops       = derivedLoops;
        }

    };

struct              HatchPatternLine
{
double              m_dLineAngle;
AcGePoint2d         m_basePoint;
AcGeVector2d        m_patternOffset;
AcGeDoubleArray     m_dashes;

HatchPatternLine() { m_dLineAngle = 0.0; }
};

typedef bvector<HatchPatternLine> HatchPattern;

static double s_cutBackRadiansTolerance = 1.0e-3;

// Examine the final 3 points of data[].
// If ...
//    There is a reverse turn (dot product of outgoing vectors greater than 0)
//    and one of:
//        (a) Projection of short to long is small (per tolerance)
//        (b) reverse turn angle is small (s_cutBackRadiansTolerance)
// Then eliminate the middle point
//    
//   
static void CompressTail (double tolerance, bvector<DPoint3d> &data)
    {
    if (data.size () >= 3)
        {
        size_t last = data.size () - 1;
        DPoint3d pointA = data[last-2];
        DPoint3d pointB = data[last-1];
        DPoint3d pointC = data[last];
        DVec3d vectorU = pointA - pointB;
        DVec3d vectorV = pointC - pointB;
        DVec3d vectorW;
        double uv = vectorU.DotProduct(vectorV);
        if (uv > 0.0)
            {
            double uu = vectorU.DotProduct (vectorU);
            double vv = vectorV.DotProduct (vectorV);
            double d;
            double theta;
            if (uu > vv)
                {
                double s = uv / uu;
                vectorW = s * vectorU;
                d = vectorW.Distance (vectorV);
                theta = d / sqrt(uu);
                }
            else
                {
                double s = uv / vv;

                vectorW = s * vectorV;
                d = vectorW.Distance(vectorU);
                theta = d / sqrt(uu);
                }
            if (d < tolerance || theta < s_cutBackRadiansTolerance)
                {
                data.pop_back ();
                data.pop_back ();
                data.push_back (pointC);
                }
            }
        }
    }

// If loopCandidate is marked _Open and its start and end are within tolerance,
//    change to closed.
// If closed by either form, run CloneWithGapsClosed.
static boolean closeAndFixup(CurveVectorPtr &loopCandidate, double closureTolerance)
    {
    if (loopCandidate->GetBoundaryType () == CurveVector::BOUNDARY_TYPE_Open)
        {
        DPoint3d xyz0, xyz1;
        loopCandidate->GetStartEnd (xyz0, xyz1);
        if (xyz0.Distance (xyz1) <= closureTolerance)
            loopCandidate->SetBoundaryType (CurveVector::BOUNDARY_TYPE_Outer);
        }
    if (loopCandidate->GetBoundaryType () == CurveVector::BOUNDARY_TYPE_Outer)
        {
        auto options = CurveGapOptions::FromResolvedToleranceFactors (*loopCandidate);
        loopCandidate = loopCandidate->CloneWithGapsClosed (options);
        return true;
        }
    return false;
    }
// On input ..
// * loop is a growing chain of varied curves.
// * child is a new line segment.
// To do
// * normally push the line segment.
// * BUT if the segment has distinctive double-back property with final line segment, change the endpoint of the tail.
static void AddOrSpliceChildCurve(double tolerance, CurveVectorPtr &loop, ICurvePrimitivePtr &primitiveB)
    {
    if (loop->size() > 0)
        {
        auto primitiveA = loop->back();
        DSegment3d segmentA, segmentB;
        if (primitiveB->TryGetLine(segmentB) && primitiveA->TryGetLine(segmentA))
            {
            bvector<DPoint3d> points;
            points.push_back(segmentA.point[0]);
            points.push_back(segmentA.point[1]);
            points.push_back(segmentB.point[1]);
            CompressTail(tolerance, points);
            if (points.size() < 3)
                {
                // Get rid of both segments.  Push a new one if appropriate.
                loop->pop_back();
                if (points.size() == 2)
                    {
                    auto primitiveC = ICurvePrimitive::CreateLine(DSegment3d::From(points[0], points[1]));
                    loop->Add(primitiveC);
                    return;
                    }
                }
            }
        }
    // fall out to just accept the child
    loop->Add(primitiveB);
    }
#ifdef AlwaysConsolidate
// See NOTE (XXX)
static void CompressSpears(double tolerance, bvector<DPoint3d> &dest, bvector<DPoint3d> &source)
    {
    dest.clear ();
    for (auto &xyz : source)
        {
        dest.push_back (xyz);
        CompressTail (tolerance, dest);
        }
    }
static size_t compressDPoint3dArray(DPoint3dArray &source)
    {
    bvector<DPoint3d> compressedPointsA, compressedPointsB;
    PolylineOps::CompressByChordError(compressedPointsA, source, TOLERANCE_SamePointUOR);
    CompressSpears(TOLERANCE_SamePointUOR, compressedPointsB, compressedPointsA);
    source.clear();
    for (auto &xyz : compressedPointsB)
        source.push_back(xyz);
    return source.size();
    }
#endif
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static UInt32               GetColorSize (FilerDataList& dataList, UInt32 reactorCount)
    {
    UInt32              colorSize = 1;
    UInt16FilerData*    colorData;
#if RealDwgVersion == 2009
    UInt32              colorStart = 9 + reactorCount;
#else
    UInt32              colorStart = 12 + reactorCount;
#endif

    // Here's what we expect:
    // dataList[colorStart]: kDwgUInt16,        // AcDbEntity:      The colorIndex.
    //  If (0 != colorIndex & 0x8000)
    //   {   Complex color.
    //   if (0 != colorIndex & 0x4000)
    //       AcDbHardPointerId AcDbColorRef.
    //   else
    //       AcDbUInt32 color RGB
    //   if (0 != colorIndex & 0x2000)
    //       AcDbInt32 transparency


    if (NULL == (colorData = dynamic_cast <UInt16FilerData*> (dataList[colorStart])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on color size!\n");
        return 1;
        }
    UInt16              colorIndex = colorData->GetValue();
    if (0 != (0x8000 & colorIndex))
        {
        BeAssert (  ( (0 != (0x4000 & colorIndex) && (NULL != dynamic_cast <HardPointerIdFilerData*> (dataList[colorStart+1]))) ) ||
                  (NULL != dynamic_cast <UInt32FilerData*> (dataList[colorStart+1])) );
        colorSize++;
        }
    if (0 != (0x2000 & colorIndex))
        {
        if (NULL == dynamic_cast <Int32FilerData*> (dataList[colorStart+1]));
            DIAGNOSTIC_PRINTF ("Hatch filer error on color size!\n");
        colorSize++;
        }

    return colorSize;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SkipPastGradientData
(
UInt32&                     afterGradient,
FilerDataList&              dataList
)
    {
    afterGradient = 0;

    // skip past the variable-sized reactor array.
    UInt32FilerData*    reactorCount;
    if (NULL == (reactorCount = dynamic_cast <UInt32FilerData *> (dataList[1])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on reactor count!\n");
        return BadDataSequence;
        }

    // skip past the other AcDbObject and AcDbEntity stuff. The AcDbHatch data should start with gradient 0 or 1.
#if RealDwgVersion == 2009
    UInt32              gradientDataStart    = 12 + GetColorSize (dataList, reactorCount->GetValue()) + reactorCount->GetValue();
#else
    UInt32              gradientDataStart    = 15 + GetColorSize (dataList, reactorCount->GetValue()) + reactorCount->GetValue();
#endif
    if (NULL == dynamic_cast <UInt32FilerData *> (dataList[gradientDataStart]))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on gradient data!\n");
        return BadDataSequence;
        }
    int     numGradientColors = 0;
    UInt32FilerData* colorCountData;
    if (NULL == (colorCountData = dynamic_cast <UInt32FilerData *> (dataList[gradientDataStart+6])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on color count data!\n");
        return BadDataSequence;
        }
    numGradientColors = colorCountData->GetValue();

    // each gradient color puts out the sequence kDwgReal, kDwgUInt16, kDwgUInt32, kDwgUInt8 (seems true regardless of whether its an indexed, RGB, or Colorbook color.)
    // (That is true whether the gradient flag is turned on or not).
    afterGradient = gradientDataStart + 8 + (numGradientColors * 4);

    // should be followed by a double then a point.
    if (NULL == dynamic_cast <DoubleFilerData *>(dataList[afterGradient]) ||
        NULL == dynamic_cast <Vector3dFilerData *>(dataList[afterGradient+1]))
        DIAGNOSTIC_PRINTF ("Hatch filer error on double followed by a point!\n");

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetEdgeSize
(
UInt32&                     edgeSize,
UInt32                      edgeStart,
FilerDataList&              dataList
)
    {
    // returns the edgeSize, including the edge type.
    edgeSize = 0;

    // the first thing in every edge is the type.
    UInt8FilerData*             edgeTypeData;
    if (NULL == (edgeTypeData = dynamic_cast <UInt8FilerData*> (dataList[edgeStart])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on edge type!\n");
        return BadDataSequence;
        }

    AcDbHatch::HatchEdgeType    edgeType = (AcDbHatch::HatchEdgeType) edgeTypeData->GetValue();
    switch (edgeType)
        {
        case AcDbHatch::kLine:
            // type, beginning and end points.
            edgeSize = 3;
            break;

        case AcDbHatch::kCirArc:
            edgeSize = 6;
            break;

        case AcDbHatch::kEllArc:
            edgeSize = 7;
            break;

        case AcDbHatch::kSpline:
            {
            // Int32 degree, Bool rational, Bool periodic, Int32 numKnots, Int32 numPoles, knots, poles. If rational, poles separated by weights.
            BooleanFilerData*  rationalData;
            if (NULL == (rationalData = dynamic_cast <BooleanFilerData*> (dataList[edgeStart+2])))
                {
                DIAGNOSTIC_PRINTF ("Hatch filer error on spline rational flag!\n");
                return BadDataSequence;
                }
            bool rational = rationalData->GetValue();
            Int32FilerData* numKnotsData;
            if (NULL == (numKnotsData = dynamic_cast <Int32FilerData*> (dataList[edgeStart+4])))
                {
                DIAGNOSTIC_PRINTF ("Hatch filer error on spline's number of knots!\n");
                return BadDataSequence;
                }
            UInt32 numKnots = numKnotsData->GetValue();

            Int32FilerData* numPolesData;
            if (NULL == (numPolesData = dynamic_cast <Int32FilerData*> (dataList[edgeStart+5])))
                {
                DIAGNOSTIC_PRINTF ("Hatch filer error on spline's number of poles!\n");
                return BadDataSequence;
                }
            UInt32 numPoles = numPolesData->GetValue();
#if RealDwgVersion == 2009
            edgeSize = 6 + numKnots + numPoles + (rational ? numPoles : 0);
#else
            // R2010 added an Int32 at the end of spline data, but I don't know what it is for.
            edgeSize = 7 + numKnots + numPoles + (rational ? numPoles : 0);
#endif
            break;
            }

        default:
            {
            DIAGNOSTIC_PRINTF ("Hatch filer error: unknown edge type!\n");
            return BadDataSequence;
            }
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetLoopSize
(
UInt32&                     loopSize,
UInt32                      loopStart,
FilerDataList&              dataList,
bool                        includeObjectIds        // if true, it skips past associative objectIds for the loop.
)
    {
    loopSize = 0;

    // the first thing in the loop is the loop flags.
    Int32FilerData*     loopTypeData;
    if (NULL == (loopTypeData = dynamic_cast <Int32FilerData*> (dataList[loopStart])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on loop type data!\n");
        return BadDataSequence;
        }
    loopSize++;

    AcDbHatch::HatchLoopType       loopType = (AcDbHatch::HatchLoopType) loopTypeData->GetValue();

    if (0 == (loopType & AcDbHatch::kPolyline))
        {
        // for non-polyline, the next thing is the number of edges within the loop.
        Int32FilerData*     edgeCountData;
        if (NULL == (edgeCountData = dynamic_cast <Int32FilerData*> (dataList[loopStart+1])))
            {
            DIAGNOSTIC_PRINTF ("Hatch filer error on edge count!\n");
            return BadDataSequence;
            }
        loopSize++;

        UInt32              edgeCount = edgeCountData->GetValue();
        UInt32              nextEdgeStart = loopStart+2;
        for (UInt32 iEdge=0; iEdge < edgeCount; iEdge++)
            {
            UInt32          edgeSize;
            RealDwgStatus   status;
            if (RealDwgSuccess != (status = GetEdgeSize (edgeSize, nextEdgeStart, dataList)))
                return status;

            nextEdgeStart += edgeSize;
            loopSize += edgeSize;
            }
        }
    else
        {
        // polyline loop. bool hasBulges, bool isClosed, Int32 numPoints, then the points, interspersed with bulges if hasBulges true.
        BooleanFilerData*   hasBulgesData1 = nullptr;
        BoolFilerData*      hasBulgesData2 = nullptr;
        if (nullptr == (hasBulgesData1 = dynamic_cast <BooleanFilerData*> (dataList[loopStart+1])) &&
            nullptr == (hasBulgesData2 = dynamic_cast <BoolFilerData*> (dataList[loopStart+1])))
            {
            DIAGNOSTIC_PRINTF ("Hatch filer error on polyline hasBulges flag!\n");
            return BadDataSequence;
            }
        bool                hasBulges = nullptr != hasBulgesData1 ? hasBulgesData1->GetValue() : hasBulgesData2->GetValue();
        Int32FilerData*     pointCountData;
        if (NULL == (pointCountData = dynamic_cast <Int32FilerData *> (dataList[loopStart + 3])))
            {
            DIAGNOSTIC_PRINTF ("Hatch filer error on polyline points count!\n");
            return BadDataSequence;
            }
        loopSize += (3 + pointCountData->GetValue() + (hasBulges ? pointCountData->GetValue() : 0));
        }

    if (includeObjectIds)
        {
        Int32FilerData*     idCountData;
        if (NULL == (idCountData = dynamic_cast <Int32FilerData*> (dataList[loopStart + loopSize])))
            {
            DIAGNOSTIC_PRINTF ("Hatch filer error on loop ID count!\n");
            return BadDataSequence;
            }
        loopSize += (1 + idCountData->GetValue());
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetHatchLineSize
(
UInt32&                     hatchLineSize,
UInt32                      hatchLineStart,
FilerDataList&              dataList
)
    {
    // initialize
    hatchLineSize = 6;

    // verify that we have five doubles to start (angle, basePoint x,y; patternOffset x,y
    if ( (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchLineStart])) ||
         (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchLineStart + 1])) ||
         (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchLineStart + 2])) ||
         (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchLineStart + 3])) ||
         (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchLineStart + 4])) )
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on getting hatch line size!\n");
        return BadDataSequence;
        }

    // get number of dashes in this line
    UInt16FilerData*    numHatchDashesData;
    if (NULL == (numHatchDashesData = dynamic_cast <UInt16FilerData*> (dataList[hatchLineStart + 5])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on getting number of hatch dashes!\n");
        return BadDataSequence;
        }
    UInt16  numHatchDashes = numHatchDashesData->GetValue();
    hatchLineSize += numHatchDashes;

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetHatchDefinitionSize
(
UInt32&                     hatchDefinitionSize,
UInt32                      hatchDefinitionStart,
FilerDataList&              dataList
)
    {
    // initialize it.
    hatchDefinitionSize = 4;

    // verify that we have two doubles followed by a bool.
    if ( (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchDefinitionStart])) ||
         (NULL == dynamic_cast <DoubleFilerData*> (dataList[hatchDefinitionStart + 1])) ||
         (NULL == dynamic_cast <BoolFilerData*> (dataList[hatchDefinitionStart + 2])) )
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on definition size!\n");
        return BadDataSequence;
        }

    // get number of hatch lines.
    UInt16FilerData*    numHatchLinesData;
    if (NULL == (numHatchLinesData = dynamic_cast <UInt16FilerData*> (dataList[hatchDefinitionStart + 3])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on number of hatch lines!\n");
        return BadDataSequence;
        }
    UInt16  numHatchLines = numHatchLinesData->GetValue();

    for (UInt16 iHatchLine = 0; iHatchLine < numHatchLines; iHatchLine++)
        {
        RealDwgStatus       status;
        UInt32              hatchLineSize;
        if (RealDwgSuccess != (status = GetHatchLineSize (hatchLineSize, hatchDefinitionStart + hatchDefinitionSize, dataList)))
            return status;

        hatchDefinitionSize += hatchLineSize;
        }

    return RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetAssociativeIdsForLoop
(
AcDbHatch*                  pHatch,
UInt32                      loopIndex,
AcDbObjectIdArray&          objIdArray
)
    {
    // see comment above for AcDhHatch data sequence.

    // There is no ObjectARX method to do this, it can only be done with the RecordingFiler.
    RecordingFiler      filer (200);
    filer.RecordData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("In SetAssociativeIdsForLoop");
#endif

    FilerDataList&      dataList        = filer.GetDataList();
    RealDwgStatus       status;

    UInt32              pastGradient;
    if (RealDwgSuccess != (status = SkipPastGradientData (pastGradient, dataList)))
        return status;

    BoolFilerData*      associativeData;
    if (NULL == (associativeData = dynamic_cast <BoolFilerData*> (dataList[pastGradient + 4])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on associative flag!\n");
        return BadDataSequence;
        }
    // set to associative.
    associativeData->SetValue (true);

    // get the number of loops.
    Int32FilerData*    numLoopsData;
    if (NULL == (numLoopsData = dynamic_cast <Int32FilerData*> (dataList[pastGradient + 5])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on number of loops!\n");
        return BadDataSequence;
        }

    if (loopIndex >= (UInt32) numLoopsData->GetValue())
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on loop index!\n");
        return BadDataSequence;
        }

    UInt32              nextLoopStart = pastGradient + 6;
    for (UInt32 iLoop = 0; iLoop <= loopIndex; iLoop++)
        {
        UInt32              loopSize;
        if (RealDwgSuccess != (status = GetLoopSize (loopSize, nextLoopStart, dataList, iLoop < loopIndex)))
            return status;

        nextLoopStart += loopSize;
        }

    // if we got here, nextLoopStart is positioned at the end the loop for which we want to set the objArrays.
    Int32FilerData*     idCountData;
    if (NULL == (idCountData = dynamic_cast <Int32FilerData*> (dataList[nextLoopStart])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on loop ID count!\n");
        return BadDataSequence;
        }

    // this assumes that the value starts at 0.
    if (0 != idCountData->GetValue())
        DIAGNOSTIC_PRINTF ("Hatch filer error: loop count starting at %d prior to being updated!\n", idCountData->GetValue());

    idCountData->SetValue (objIdArray.length());

    // point it after the idCount data.
    nextLoopStart++;
    for (int iObjectId=0; iObjectId < objIdArray.length(); iObjectId++)
        filer.InsertEntryAt (nextLoopStart+iObjectId, new SoftPointerIdFilerData (objIdArray[iObjectId]));

    // put the adjusted data back into the object.
    filer.PlaybackData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    RecordingFiler      confirmFiler (200);
    confirmFiler.RecordData (pHatch);
    confirmFiler.DumpList ("After setting associds");
#endif

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SkipPastHatchLoops
(
UInt32&                     afterLoopData,
UInt32                      startOfLoopData,
FilerDataList&              dataList
)
    {
    // get the number of loops.
    Int32FilerData*    numLoopsData;
    if (NULL == (numLoopsData = dynamic_cast <Int32FilerData*> (dataList[startOfLoopData])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on number of loops!\n");
        return BadDataSequence;
        }

    UInt32              numLoops    = (UInt32) numLoopsData->GetValue();
    UInt32              endOfLoops  = startOfLoopData + 1;
    for (UInt32 iLoop=0; iLoop < numLoops; iLoop++)
        {
        UInt32              loopSize;
        RealDwgStatus       status;
        if (RealDwgSuccess != (status = GetLoopSize (loopSize, endOfLoops, dataList, true)))
            return status;

        endOfLoops += loopSize;
        }

    if (endOfLoops >= dataList.size())
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on loop data!\n");
        return BadDataSequence;
        }

    afterLoopData = endOfLoops;
    return RealDwgSuccess;
    }

#if !defined (USE_FILER)
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static WCharCP                GetShortestAscii (WCharP buffer, double value, bool smallValuePossible, bool precedingComma, bool reallyNeeded)
    {
    WCharP      outBuf   = buffer;
    double      absValue = abs (value);
    if (precedingComma)
        {
        *outBuf = L',';
        outBuf++;
        }
    if ( (0.0 == absValue) || (!smallValuePossible && absValue < 1e-10) || absValue > 1.e+50 )
        {
        *outBuf = L'0';
        *(outBuf+1) = 0;
        return buffer;
        }

        // the patternOffsets cannot be rounded to 0, or we get infinitely dense pattern (TR#271382).
    if ( (absValue < 0.1) && smallValuePossible)
        {
        swprintf (outBuf, L"%g", value);
        // can't strip zeros.
        return buffer;
        }

    if (!reallyNeeded)
        {
        swprintf (outBuf, L"%f", value);
        }
    // here, reallyNeeded is true, meaning we tried to fit it without limiting precision and failed. So we get more aggressive about it.
    else
        {
        if (absValue < 1.0)
            swprintf (outBuf, L"%f", value);
        else if (absValue < 10.0)
            swprintf (outBuf, L"%.5f", value);
        else if (absValue < 100.0)
            swprintf (outBuf, L"%.4f", value);
        else if (absValue < 1000.0)
            swprintf (outBuf, L"%.3f", value);
        else if (absValue < 10000.0)
            swprintf (outBuf, L"%.2f", value);
        else if (absValue < 100000.0)
            swprintf (outBuf, L"%.1f", value);
        else
            {
            // can't strip trailing zeros.
            swprintf (outBuf, L"%.0f", value);
            return buffer;
            }
        }

    // strip trailing zeros.
    for (WCharP end = outBuf + (wcslen (outBuf) - 1); (end > (outBuf+1)) && (*end == L'0'); end--)
        *end = 0;

    return buffer;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/13
+---------------+---------------+---------------+---------------+---------------+------*/
static double   MagnifyHatchPatternDefinition (HatchPattern& hatchPattern)
    {
    double  largestValue = 0.0;
    for each (HatchPatternLine hpLine in hatchPattern)
        {
        if (fabs(hpLine.m_patternOffset.x) > fabs(largestValue))
            largestValue = hpLine.m_patternOffset.x;
        if (fabs(hpLine.m_patternOffset.y) > fabs(largestValue))
            largestValue = hpLine.m_patternOffset.y;

        UInt16  numDashes = hpLine.m_dashes.length ();
        for (UInt16 iDash = 0; iDash < numDashes; iDash++)
            {
            if (fabs(hpLine.m_dashes[iDash]) > fabs(largestValue))
                largestValue = hpLine.m_dashes[iDash];
            }
        }

    //1080169 - [Regression] [From SS10] MicroStation CONNECT hangs when exporting to DWG
    //A HatchPatternLine object is created with NAN (not a number) value
    //As there is bad data in HatchPatternLine object,  largestValue remains zero and below while loop is going in infinite loop 
    if (largestValue == 0.0)
        return 1.0;

    double  magnifier = 1.0;
    while (fabs(largestValue) < MIN_DwgHatchDefinitionValue)
        {
        magnifier *= 10.0;
        largestValue *= 10.0;
        }

    if (magnifier > 1.0)
        {
        for (size_t iLine = 0; iLine < hatchPattern.size(); iLine++)
            {
            hatchPattern[iLine].m_basePoint *= magnifier;
            hatchPattern[iLine].m_patternOffset *= magnifier;

            UInt16  numDashes = hatchPattern[iLine].m_dashes.length ();
            for (UInt16 iDash = 0; iDash < numDashes; iDash++)
                hatchPattern[iLine].m_dashes[iDash] *= magnifier;
            }
        }

    return  magnifier;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetRawPattern
(
AcDbHatch*                  pHatch,
AcDbHatch::HatchPatternType patternType,    // so far, always AcDbHatch::kCustomDefined
WCharP                      patternName,
double                      angle,          // so far, always 0.0
double                      scale,          // so far, always 1.0
HatchPattern&               hatchPattern
)
    {
    WChar           patternFilePath[MAXFILELENGTH];
    RealDwgStatus   status;


    /*-----------------------------------------------------------------------------------
    Below we need to validate pattern name to get around RealDWG's pattern file import 
    mechanism.

    However, pattern name update can effectively change pattern type from user defined to
    customer defined.  In OpenDWG we did not have to do that so we were able to keep a
    user defined type as user defined.  But the hatches we created then were not quite 
    right either, because we also embedded angle & scale into the pattern data.  Editing
    angle or scale did not work right after that.  The fact that we embed angle and scale 
    into pattern data effectively makes the pattern a custom defined type anyway.  While 
    the ability to round trip user defined type is desirable, it proves difficult to make 
    it completely right.  On the other hand, changing the type to custom defined actually
    produces a better result that can survive editing in both ACAD and MS.  Because of 
    that, we can live with the pattern type change, although I have found that we do not 
    always have to do above renaming in order to get around RealDWG's pattern file import 
    mechanism.  We may come back & revist this issue with future releases of AutoCAD or
    RealDWG.
    -----------------------------------------------------------------------------------*/

    if ('_' == *patternName)
        {
        // leading underbars (as in _USER,_01) cause RealDWG to skip the leading underbar when looking for the file name, thus the file isn't found.
        patternName++;
        }

    // A patternName of "U", "U", "U,_I", "U,_O", or "USER", with or without a prefix "_", causes RealDWG to simply ignore the text file and make it a kUserDefined hatch.
    if (0 == wcscmp (patternName, L"U"))
        {
        wcscpy (patternName, L"M");
        }
    else if (0 == wcsncmp (patternName, L"USER", 4))
        {
        wcsncpy (patternName, L"MSTN", 4);
        }
    else if (0 == wcsncmp (patternName, L"U,_", 3))
        {
        patternName[0] = L'M';
        patternName[1] = 0;
        }

    // since we're writing the pattern name in a file where the name is separated from the description by a comma, we cannot have any commas in the patternName. Replace with period.
    RealDwgUtil::ReplaceUnicodeStringChars (patternName, L",", '.');

    // truncate pattern name at the 33rd char as a longer name crashes RealDWG. No need to restore patternName as this is the last method that uses it.
    if (wcslen(patternName) > MAX_DWGPatternNameLength)
        patternName[MAX_DWGPatternNameLength] = 0;

    if (RealDwgSuccess != (status = RealDwgUtil::GetPatternFilePath (patternFilePath, _countof (patternFilePath), patternName)))
        return status;

    /*------------------------------------------------------------------------------------------------------------
    We are about to write out a kCustomDefined pattern file.  Due to the 80 characters per line limit in the ASCII 
    file, we can lose pattern accuracy.  To improve the accuracy, we find the largest value of offset and dashes.
    If the largest value is smaller than 5, we scale up the definition to increase number of significant digits in
    the .pat file.
    ------------------------------------------------------------------------------------------------------------*/
    double  magnifier = MagnifyHatchPatternDefinition (hatchPattern);
    if (magnifier > 1.0)
        scale /= magnifier;
    
    BeFileStatus    fileStatus;
    BeTextFilePtr   patternFile = BeTextFile::Open (fileStatus, patternFilePath, TextFileOpenType::Write, TextFileOptions::None);
    if (!patternFile.IsValid() || BeFileStatus::Success != fileStatus)
        return CantCreatePatternFile;

    WChar    outLine[4096];
    swprintf (outLine, L"*%ls, %ls", patternName, patternName);
    patternFile->PutLine (outLine, true);

    // our attempt at using the RecordingFiler changed, so we have to resort to writing out a pattern file and reading it back in.
    double  toDegrees = 180.0 / msGeomConst_pi;
    for each (HatchPatternLine hpLine in hatchPattern)
        {
        for (int iTry=0; iTry < 2; iTry++)
            {
            WChar    numBuffer[50];
            double  cosine = cos (hpLine.m_dLineAngle);
            double  sine   = sin (hpLine.m_dLineAngle);
            double  xVal   = cosine * hpLine.m_patternOffset.x + sine   * hpLine.m_patternOffset.y;
            double  yVal   = -sine  * hpLine.m_patternOffset.x + cosine * hpLine.m_patternOffset.y;
            outLine[0] = 0;
            wcscat (outLine, GetShortestAscii (numBuffer, hpLine.m_dLineAngle * toDegrees, false, false, iTry > 0));
            wcscat (outLine, GetShortestAscii (numBuffer, hpLine.m_basePoint.x, false, true, iTry > 0));
            wcscat (outLine, GetShortestAscii (numBuffer, hpLine.m_basePoint.y, false, true, iTry > 0));
            wcscat (outLine, GetShortestAscii (numBuffer, xVal, true, true, iTry > 0));
            wcscat (outLine, GetShortestAscii (numBuffer, yVal, true, true, iTry > 0));

            UInt16  numDashes = hpLine.m_dashes.length();
            for (UInt16 iDash = 0; iDash < numDashes; iDash++)
                {
                wcscat (outLine, GetShortestAscii (numBuffer, hpLine.m_dashes[iDash], false, true, iTry > 0));
                }

            if (wcslen (outLine) < 80)
                {
                patternFile->PutLine (outLine, true);
                break;
                }
            else if (iTry > 0)
                {
                // still too long. Put it into the file anyway, but it won't work.
                patternFile->PutLine (outLine, true);
                DIAGNOSTIC_PRINTF ("\t...Failed on try #%d - still too many characters (%d)!\n", iTry, wcslen(outLine));
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Pattern %ls has too many characters (%d > 80), now trying a shorter size...\n", patternName, wcslen(outLine));
                }
            }
        }

    patternFile->Close ();

    // pHatch->setPattern apparently uses the "workingDatabase", and if it's not set, a crash results (TR#271260, 271529).
    // RD2013 seems to have fixed this - even after reverting the related changes the above test cases no longer crash.
    // assert (RealDwgHostApp::Instance().workingDatabase() == pHatch->database());

    // reset angle and scale prior to loading the pattern file which should have net angle & scale computed in pattern definition in above code. TR 295080.
    pHatch->setPatternAngle (angle);
    pHatch->setPatternScale (scale);

    AcDbHatch::HatchStyle   hatchStyle = pHatch->hatchStyle ();

    Acad::ErrorStatus   errorStatus = pHatch->setPattern (AcDbHatch::kCustomDefined, patternName);
    if (Acad::eOk != errorStatus)
        DIAGNOSTIC_PRINTF ("Failed setting pattern %ls. %ls\n", patternName, acadErrorStatusText(errorStatus));

    // delete the pattern file. No sense leaving it around so someone can use it in AutoCAD.
    _wunlink (patternFilePath);

    if (Acad::eOk == errorStatus)
        pHatch->setHatchStyle (hatchStyle);

    return Acad::eOk == errorStatus ? RealDwgSuccess : CantCreatePatternFile;
    }

#else
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetRawPattern
(
AcDbHatch*                  pHatch,
AcDbHatch::HatchPatternType patternType,
WCharCP                   patternName,
double                      angle,
double                      scale,
HatchPattern&               hatchPattern
)
    {
    // see comment above for AcDhHatch data sequence.

    // There is no ObjectARX method to do this, it can only be done with the RecordingFiler.
    RecordingFiler      filer (200);
    filer.RecordData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("In SetRawPattern");
#endif

    // The hatch definition follows all of the loops in the AcDbHatch data sequence, so we have to skip past all of them.
    FilerDataList&      dataList        = filer.GetDataList();
    RealDwgStatus       status;

    UInt32              pastGradient;
    if (RealDwgSuccess != (status = SkipPastGradientData (pastGradient, dataList)))
        return status;

    // set the hatch pattern name.
    ACharFilerData*    hatchNameData;
    if (NULL == (hatchNameData = dynamic_cast <ACharFilerData*> (dataList[pastGradient + 2])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on hatch name!\n");
        return BadDataSequence;
        }
    hatchNameData->SetValue (patternName);

    // get the "isSolid" flag.
    BoolFilerData*      isSolidData;
    if (NULL == (isSolidData = dynamic_cast <BoolFilerData*> (dataList[pastGradient + 3])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on isSolid flag!\n");
        return BadDataSequence;
        }
    bool isSolidFill = isSolidData->GetValue ();


    UInt32              pastHatchLoops;
    if (RealDwgSuccess != (status = SkipPastHatchLoops (pastHatchLoops, pastGradient + 5, dataList)))
        return status;

    // the next thing is the Int16 AcDbHatchStyle
    if (nullptr == dynamic_cast <Int16FilerData*> (dataList[pastHatchLoops]))
        DIAGNOSTIC_PRINTF ("Hatch filer error on hatch style!\n");

    // the next thing is the hatchPatternType, which we want to set to the argument above.
    Int16FilerData*         hatchPatternTypeData;
    if (NULL == (hatchPatternTypeData = dynamic_cast <Int16FilerData*> (dataList[pastHatchLoops + 1])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on pattern type!\n");
        return BadDataSequence;
        }
    hatchPatternTypeData->SetValue (patternType);

    UInt32  hatchDefinitionStart = pastHatchLoops + 2;
    // If we already have a hatch definition (!isSolidFill), figure out its length and delete the definition from the sequence.
    if (!isSolidFill)
        {
        UInt32  hatchDefinitionSize;
        if (RealDwgSuccess != (status = GetHatchDefinitionSize (hatchDefinitionSize, hatchDefinitionStart, dataList)))
            return status;

        filer.DeleteEntryRange (hatchDefinitionStart, hatchDefinitionSize);
        }

    // create the new hatch definition sequence and insert it.
    UInt32  currentPos = hatchDefinitionStart;
    filer.InsertEntryAt (currentPos++, new DoubleFilerData (angle));
    filer.InsertEntryAt (currentPos++, new DoubleFilerData (scale));
    filer.InsertEntryAt (currentPos++, new BoolFilerData (false));       // crosshatch.
    filer.InsertEntryAt (currentPos++, new UInt16FilerData (hatchPattern.size()));
    for each (HatchPatternLine hpLine in hatchPattern)
        {
        filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_dLineAngle));
        filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_basePoint.x));
        filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_basePoint.y));
        filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_patternOffset.x));
        filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_patternOffset.y));
        UInt16  numDashes = hpLine.m_dashes.length();
        filer.InsertEntryAt (currentPos++, new UInt16FilerData (numDashes));
        for (UInt16 iDash = 0; iDash < numDashes; iDash++)
            {
            filer.InsertEntryAt (currentPos++, new DoubleFilerData (hpLine.m_dashes[iDash]));
            }
        }

#if defined (REALDWG_FILER_DEBUG)
    // put the adjusted data back into the object.
    filer.DumpList ("Before setting RawPattern");
#endif
    filer.PlaybackData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    int     patternDefs = pHatch->numPatternDefinitions();
    printf ("PatternDefinitions = %d\n", patternDefs);
    for (int iPattern=0; iPattern < patternDefs; iPattern++)
        {
        double angle, baseX, baseY, offsetX, offsetY;
        AcGeDoubleArray dashes;
        pHatch->getPatternDefinitionAt (iPattern, angle, baseX, baseY, offsetX, offsetY, dashes);
        }

    RecordingFiler      confirmFiler (200);
    confirmFiler.RecordData (pHatch);
    confirmFiler.DumpList ("After setting RawPatter");
#endif

    return RealDwgSuccess;
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetSeedPointsAndHatchPixelSize
(
DPoint3dArray const&        seedPoints,
size_t                      nSeedPoints,
double                      hatchPixelSize,
AcDbHatch*                  pHatch
)
    {
    // see comment above for AcDhHatch data sequence.

    // There is no ObjectARX method to do this, it can only be done with the RecordingFiler.
    RecordingFiler      filer (200);
    filer.RecordData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("In SetSeedPointsAndHatchPixels");
#endif

    FilerDataList&      dataList        = filer.GetDataList();
    RealDwgStatus       status;

    UInt32              pastGradient;
    if (RealDwgSuccess != (status = SkipPastGradientData (pastGradient, dataList)))
        return status;

    // get the "isSolid" flag.
    BoolFilerData*      isSolidData;
    if (NULL == (isSolidData = dynamic_cast <BoolFilerData*> (dataList[pastGradient + 3])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on solid fill flag!\n");
        return BadDataSequence;
        }
    bool isSolidFill = isSolidData->GetValue ();

    // skip past all the loop data.
    UInt32              pastHatchLoops;
    if (RealDwgSuccess != (status = SkipPastHatchLoops (pastHatchLoops, pastGradient + 5, dataList)))
        return status;

    // if necessary, skip past the hatch definition data.
    UInt32      hatchDefinitionStart = pastHatchLoops + 2;
    UInt32      pastHatchDefinition  = pastHatchLoops;
    if (!isSolidFill)
        {
        UInt32  hatchDefinitionSize;
        if (RealDwgSuccess != (status = GetHatchDefinitionSize (hatchDefinitionSize, hatchDefinitionStart, dataList)))
            return status;

        pastHatchDefinition = pastHatchLoops + hatchDefinitionSize;
        }

    // pastHatchDefinition now points to HatchStyle. Skip past it and HatchPatternType.
    UInt32              pixelData = pastHatchDefinition + 2;

    // This is only written out if hatchLoopType is derived. However, this method is called only in that case.
    DoubleFilerData*    pixelSizeData;
    if (NULL == (pixelSizeData = dynamic_cast <DoubleFilerData*> (dataList[pixelData])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on pixel size!\n");
        return BadDataSequence;
        }
    pixelSizeData->SetValue (hatchPixelSize);

    Int32FilerData*     numSeedPointsData;
    if (NULL == (numSeedPointsData = dynamic_cast <Int32FilerData*> (dataList[pixelData + 1])))
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on number of seed points!\n");
        return BadDataSequence;
        }
    Int32   currentNumSeedPoints = numSeedPointsData->GetValue();
    if (currentNumSeedPoints > 0)
        filer.DeleteEntryRange (pixelData + 2, currentNumSeedPoints);

    numSeedPointsData->SetValue (nSeedPoints);
    for (int iSeedPoint = 0; iSeedPoint < (int)nSeedPoints; iSeedPoint++)
        filer.InsertEntryAt (pixelData + 2 + iSeedPoint, new Point2dFilerData (seedPoints[iSeedPoint].x, seedPoints[iSeedPoint].y));

    // put the adjusted data back into the object.
    filer.PlaybackData (pHatch);

#if defined (REALDWG_FILER_DEBUG)
    RecordingFiler      confirmFiler (200);
    confirmFiler.RecordData (pHatch);
    confirmFiler.DumpList ("After setting associds");
#endif

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
double                  GetRangeDiagonal
(
MSElementDescrCP        pDescr
)
    {
    DRange3d            range;

    DataConvert::ScanRangeToDRange3d (range, pDescr->el.hdr.dhdr.range);
    if (!pDescr->el.hdr.dhdr.props.b.is3d)
        range.low.z = range.high.z = 0.0;

    return range.low.Distance (range.high);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt               AddLoopAssocObjIds
(
AcDbHatch*              pHatch,
ElementHandleCR         loopEh,
int                     loopIndex,
bool                    isAssocRegion,
AcDbEntity*             pEntity,                // the entity we're hatching.
ConvertFromDgnContextR  context
)
    {
    if (pHatch->numLoops() <= loopIndex)
        {
        DIAGNOSTIC_PRINTF ("Hatch filer error on number of loop!\n");
        return SUCCESS;
        }

    MSElementDescrCP        pLoopDscr = loopEh.GetElementDescrCP ();
    AcDbObjectIdArray       objIdArray;
    if (isAssocRegion)
        {
        size_t              nBoundaryIds;
        ElementIdArray      boundaryIds;

        // For some reason, the IDs are not always included on the individual region loops.
        // Try to grab them from the parent - this is not optimal as each loop
        // will contain IDs for all elements, but best we can do in this case.
        for (; NULL != pLoopDscr; pLoopDscr = (pLoopDscr->h.myHeader == pLoopDscr) ? NULL : pLoopDscr->h.myHeader)
            {
            ElementHandle   headerElem(pLoopDscr, false, false, context.GetModel());
            DependencyManagerLinkage::GetRootElementIds(boundaryIds, headerElem, DEPENDENCYAPPID_AssocRegion, 0/*TYPE_REGIONBOUNDARY*/);
            if ((nBoundaryIds = boundaryIds.size()) == 0)
                {
                DependencyManagerLinkage::GetRootElementIds(boundaryIds, headerElem, DEPENDENCYAPPID_AssocRegion, 1/*TYPE_ROOTBOUNDARY*/);
                nBoundaryIds = boundaryIds.size ();
                }

            if (nBoundaryIds > 0)
                {
                for (size_t i=0; i < nBoundaryIds; i++)
                    {
                    AcDbObjectId    objectId = context.ExistingObjectIdFromElementId (boundaryIds[i]);

                    if (!objectId.isNull() && !objectId.isErased())
                        objIdArray.append (objectId);
                    }
                break;
                }
            }
        }
    else
        {
        // This is a an associate hatch (or fill) directly applied to the element, so associate directly.
        AcDbObjectId    objectId = context.ExistingObjectIdFromElementId (pLoopDscr->el.ehdr.uniqueId);

        if (objectId.isNull() || objectId.isErased())
            {
            if (NULL != pLoopDscr->h.myHeader)
                {
                ElementHandle   headerElm(pLoopDscr->h.myHeader, false, false, context.GetModel());
                if (GroupedHoleHandler::IsGroupedHole(headerElm) &&
                    !(objectId = context.ExistingObjectIdFromElementId (pLoopDscr->h.myHeader->el.ehdr.uniqueId)).isNull() &&
                    !objectId.isErased())
                    {
                    objIdArray.append (objectId);
                    }
                }
            }
        else
            {
            objIdArray.append (objectId);
            }
        }

    if (objIdArray.length() > 0)
        {
        pHatch->setAssociative (true);

        // ObjectARX does not have this method. You have to give ObjectARX the object ids are you either insertLoop or appendLoop.
        // Also, ObjectARX does not add reactors to make the hatch change when the associated elements are changed. You
        // have to do that yourself.
        SetAssociativeIdsForLoop (pHatch, loopIndex, objIdArray);

        // Add reactors.
        int             numEntities = objIdArray.length();
        AcDbObjectId    hatchId     = pHatch->objectId();
        if (!hatchId.isValid())
            context.AddEntityToCurrentBlock (pHatch, NULL != pLoopDscr->h.myHeader ? pLoopDscr->h.myHeader->el.ehdr.uniqueId : 0);

        for (int iEntity = 0; iEntity < numEntities; iEntity++)
            {
            if ( (NULL != pEntity) && (objIdArray[iEntity] == pEntity->objectId()) )
                pEntity->addPersistentReactor (hatchId);
            else
                {
                AcDbEntityPointer pRootEntity (objIdArray[iEntity], AcDb::kForWrite);
                if (Acad::eOk != pRootEntity.openStatus())
                    {
                    DIAGNOSTIC_PRINTF ("Failed adding hatch loop ID=%I64d! %ls\n", context.ElementIdFromObjectId(objIdArray[iEntity]), acadErrorStatusText(pRootEntity.openStatus()));
                    continue;
                    }
                pRootEntity->addPersistentReactor (hatchId);
                }
            }

        return SUCCESS;
        }

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddLineToLoop
(
AcGeVoidPointerArray&       edgeComponents,
AcGeIntArray&               edgeTypes,
DPoint3dCP                  pStartPoint,
DPoint3dCP                  pEndPoint,
TransformCP                 pTransform
)
    {
    DPoint3d    points[2];

    points[0] = *pStartPoint;
    points[1] = *pEndPoint;
    pTransform->multiply (points, points, 2);
    edgeComponents.append (new AcGeLineSeg2d (RealDwgUtil::GePoint2dFromDPoint3d (points[0]), RealDwgUtil::GePoint2dFromDPoint3d (points[1])));
    edgeTypes.append (AcDbHatch::kLine);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/90
+---------------+---------------+---------------+---------------+---------------+------*/
static int      mdlMline_getBoundaryDescr
(
MSElementDescrH edPP,           /* <=  Shape representing boundary */
MSElementP      mline,          /* =>  Multiline element */
int             minLine,        /* =>  Minimum profile line (-1 for limit) */
int             maxLine,        /* =>  Maximum profile line (-1 for limit) */
DgnModelRefP    modelRef        /* => source */
)
    {
    ElementHandle thisElm (mline, modelRef);

    if (!thisElm.IsValid () || MULTILINE_ELM != thisElm.GetElementType ())
        return ERROR;

    MSElementCR elm     = *thisElm.GetElementCP();
    int         nLines  = elm.mline.nLines;
    int         nPoints = elm.mline.nPoints;
    DPoint3d    workLine[MLINE_MAXPOINTS];

    MultilineHandler::ExtractPointArray (thisElm, workLine, 0, nPoints);

    if (elm.mline.flags.closed)
        workLine[0] = workLine[nPoints-1];

    int         i;
    JointDef    joints[MLINE_MAXPOINTS];

    MultilineHandler::GetCapJointDef (*joints, thisElm, workLine, 0);

    for (i=1; i<nPoints-1; i++)
        MultilineHandler::GetJointDef (*(joints+i), thisElm, workLine, i-1);

    MultilineHandler::GetCapJointDef (*(joints+(nPoints-1)), thisElm, workLine+(nPoints-2), 1);

    int         min, max;
    double      minDist, maxDist;

    MultilineHandler::GetLimitProfiles (min, max, thisElm);

    minDist = elm.mline.profile[min].dist;
    maxDist = elm.mline.profile[max].dist;

    if (minLine >= 0 && minLine < nLines && minLine != maxLine)
        minDist = elm.mline.profile[minLine].dist;

    if (maxLine >= 0 && maxLine < nLines && maxLine != minLine)
        maxDist = elm.mline.profile[maxLine].dist;

    DPoint3d    lineBuf1[MAX_VERTICES];

    // Process the minimum distance line
    for (i=0; i<nPoints; i++)
        bsiDPoint3d_addScaledDVec3d (lineBuf1+i,  workLine+i, (DVec3dP) &joints[i].dir, (double) minDist * joints[i].scale);

    DPoint3d    lineBuf2[MLINE_MAXPOINTS];

    for (i=0; i<nPoints; i++)
        bsiDPoint3d_addScaledDVec3d (lineBuf2+nPoints-i-1, workLine+i, (DVec3dP) &joints[i].dir, (double) maxDist * joints[i].scale);

    memcpy (&lineBuf1[nPoints], lineBuf2, nPoints*sizeof(DPoint3d));
    lineBuf1[nPoints*2] = lineBuf1[0];

    EditElementHandle newEeh;

    if (SUCCESS != ShapeHandler::CreateShapeElement (newEeh, NULL, lineBuf1, nPoints*2+1, thisElm.GetElementCP ()->hdr.dhdr.props.b.is3d, *thisElm.GetModelRef ()))
        return ERROR;

    ElementPropertiesSetter::ApplyTemplate (newEeh, thisElm);

    *edPP = newEeh.ExtractElementDescr ();

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddLoop
(
AcDbHatch*                  pHatch,
ElementHandleCR             loopEh,
ElementHandleCR             hatchEh,
TransformP                  pTransform,
DVec3dP                     pNormal,
bool                        isAssociative,
bool                        isAssocRegion,
int                         loopType,
AcDbEntity*                 pEntity,
ConvertFromDgnContextR      context
)
    {
    int                         loopIndex = pHatch->numLoops();
    bool                        closed, hasConstantWidth, continueLt;
    double                      constantWidth = 0.0;
    DoubleArray                 bulges;
    DPoint3dArray               points;
    DPoint2dArray               widths;

    if (context.CanElementBeRepresentedByLWPolyline (loopEh, false) &&
        SUCCESS == context.ExtractLWPolylineFromElement (points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt, loopEh, (DPoint3d)*pNormal, pTransform, TOLERANCE_ComplexChainGap, true))
        {
        size_t                  nPoints = points.size(), nBulges = bulges.size();
        AcGePoint2dArray        pointArray;
        AcGeDoubleArray         bulgeArray;
        double                  zero = 0.0;

        if (closed && 0 == nBulges && pHatch->isSolidFill())
            {

            // the bspmesh_fixBoundaries API requires us to allocate the BsurfBoudary structure, might realloc it..
            BsurfBoundary*  pBoundaries = (BsurfBoundary *) BSIBaseGeom::Malloc (sizeof(*pBoundaries));
            pBoundaries->points         = (DPoint2d *) BSIBaseGeom::Malloc (nPoints * sizeof(DPoint2d));
            pBoundaries->numPoints      = (Int32)nPoints;
            pBoundaries->pFirst         = NULL;
            // populate the points.
            for (size_t iPoint=0; iPoint < nPoints; iPoint++)
                {
                pBoundaries->points[iPoint].x = points[iPoint].x;
                pBoundaries->points[iPoint].y = points[iPoint].y;
                }

            int         nBounds = 1;
            bspmesh_fixBoundaries (&pBoundaries, &nBounds, 1);

            // if it ended up requring only one boundary, we use it (put the points into pointArray).
            if (1 == nBounds)
                {
                DPoint2d    *pBoundPoints = pBoundaries->points;

                for (UInt32 iPoint=0; iPoint < (UInt32) pBoundaries->numPoints; iPoint++)
                    {
                    if (0 == iPoint || !pBoundPoints[iPoint-1].isEqual (&pBoundPoints[iPoint], context.GetSamePointTolerance()))
                        {
                        pointArray.append (RealDwgUtil::GePoint2dFromDPoint2d (pBoundPoints[iPoint]));
                        bulgeArray.append (0.0);
                        }
                    }
                }

            // free all the generated boundaries.
            for (UInt32 iBound=0; iBound < (UInt32) nBounds; iBound++)
                BSIBaseGeom::Free (pBoundaries[iBound].points);

            BSIBaseGeom::Free (pBoundaries);
            }

        if (0 == pointArray.length())
            {
            for (UInt32 iPoint=0; iPoint<nPoints; iPoint++)
                {
                if (0 == iPoint || !points[iPoint-1].isEqual (&points[iPoint], context.GetSamePointTolerance()))
                    {
                    pointArray.append (RealDwgUtil::GePoint2dFromDPoint3d (points[iPoint]));
                    bulgeArray.append (0 == nBulges ? 0.0 : bulges[iPoint]);
                    }
                }
            }

        if (pointArray.length() > (0 == nBulges ? 2 : 1))
            {
            Acad::ErrorStatus appendStatus = pHatch->appendLoop (loopType | AcDbHatch::kPolyline, pointArray, bulgeArray);
            if (Acad::eOk != appendStatus)
                DIAGNOSTIC_PRINTF ("Failed appending hatch polyline loop! %ls\n", acadErrorStatusText(appendStatus));
            }
        }
    else
        {
        AcGeVoidPointerArray        edgeComponents;
        AcGeIntArray                edgeTypes;
        DPoint3d                    chainStart, chainEnd, childStart, childEnd;

        switch (loopEh.GetElementType())
            {
            case CMPLX_SHAPE_ELM:
            case CMPLX_STRING_ELM:
                {
                bool atStart = true;
                for (ChildElemIter childHandle (loopEh, ExposeChildrenReason::Count); childHandle.IsValid(); childHandle = childHandle.ToNext())
                    {
                    CurveVectorPtr  pathCurve = ICurvePathQuery::ElementToCurveVector (childHandle);
                    if (!pathCurve.IsValid() || !pathCurve->GetStartEnd(childStart, childEnd))
                        continue;

                    if (atStart)
                        {
                        chainStart  = childStart;
                        atStart     = false;
                        }
                    else
                        {
                        if (!chainEnd.isEqual (&childStart, TOLERANCE_ComplexChainGap))
                            AddLineToLoop (edgeComponents, edgeTypes, &chainEnd, &childStart, pTransform);
                        }
                    context.ExtractGeCurve2dFromElement (edgeComponents, edgeTypes, childHandle, pTransform);
                    chainEnd = childEnd;
                    }
                // DD v2 orders points to be closed and throws exception on open loop with fixed tolerance of 1e-6
                if (!chainEnd.isEqual(&chainStart, TOLERANCE_ComplexChainGap))
                    AddLineToLoop (edgeComponents, edgeTypes, &chainEnd, &chainStart, pTransform);
                break;
                }

            case MULTILINE_ELM:
                {
                int                 minLineIndex = -1, maxLineIndex = -1;
                PatternParams       patternParams;
                if (PatternLinkageUtil::ExtractFromElement(NULL, patternParams, NULL, 0, NULL, *loopEh.GetElementCP(), context.GetModel(), 0))
                    {
                    minLineIndex = patternParams.minLine;
                    maxLineIndex = patternParams.maxLine;
                    }

                MSElementDescr      *pBoundaryDscr = NULL;
                if (SUCCESS == mdlMline_getBoundaryDescr (&pBoundaryDscr, const_cast<MSElementP>(loopEh.GetElementCP()), -1, -1, context.GetModel()))
                    {
                    ElementHandle   boundaryEh(pBoundaryDscr, true, false, context.GetModel());
                    AddLoop (pHatch, boundaryEh, hatchEh, pTransform, pNormal, isAssociative, isAssocRegion, loopType, pEntity, context);
                    }
                break;
                }

            default:
                context.ExtractGeCurve2dFromElement (edgeComponents, edgeTypes, loopEh, pTransform);
                break;
            }
        if (edgeComponents.length() > 0)
            {
            Acad::ErrorStatus appendStatus = pHatch->appendLoop (loopType, edgeComponents, edgeTypes);
            if (Acad::eOk != appendStatus)
                DIAGNOSTIC_PRINTF ("Failed appending hatch polyline loop! %ls\n", acadErrorStatusText(appendStatus));
            }
        }

    if (isAssociative && SUCCESS != AddLoopAssocObjIds (pHatch, loopEh, loopIndex, isAssocRegion, pEntity, context))
        {
        if (!pHatch->objectId().isValid())
            context.AddEntityToCurrentBlock (pHatch, hatchEh.GetElementId());
        context.PostProcessingRequired (hatchEh, pHatch->objectId());
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddLoopIds
(
AcDbHatch*                  pHatch,
ElementHandleCR             elemHandle,
int*                        pLoopIndex,
bool                        isAssocRegion,
AcDbEntity*                 pEntity,        // the corresponding AcDbEntity.
ConvertFromDgnContextR      context
)
    {
    if (CELL_HEADER_ELM == elemHandle.GetElementType())
        {
        for (ChildElemIter child(elemHandle, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
            AddLoopIds (pHatch, child, pLoopIndex, isAssocRegion, pEntity, context);
        }
    else
        {
        AddLoopAssocObjIds (pHatch, elemHandle, (*pLoopIndex)++, isAssocRegion, pEntity, context);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AddLoops
(
AcDbHatch*                  pHatch,
ElementHandleCR             loopsEh,
ElementHandleCR             hatchEh,
TransformP                  pTransform,
DVec3dP                     pNormal,
bool                        isAssociative,
bool                        isAssocRegion,
int                         loopType,
AcDbEntity*                 pEntity,        // the entity we are hatching.
ConvertFromDgnContextR      context
)
    {
    MSElementTypes          elementType = (MSElementTypes)loopsEh.GetElementType();

    if (CELL_HEADER_ELM == elementType)
        {
        for (ChildElemIter child(loopsEh, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
            AddLoops (pHatch, child, hatchEh, pTransform, pNormal, isAssociative, isAssocRegion, loopType, pEntity, context);
        }
    else
        {
        if (TEXT_ELM == elementType || TEXT_NODE_ELM == elementType)
            {
            loopType |= AcDbHatch::kTextbox;
            }
        else
            {
            MSElementDescrCP    loopsDescr = loopsEh.GetElementDescrCP ();
            int                 loopOEDCode;
            if (SUCCESS == AssocRegionCellHeaderHandler::GetLoopOedCode(loopsEh, loopOEDCode))
                {
                switch (loopOEDCode)
                    {
                    case DWG_TEXTBOX_PATH:
                        loopType |=  AcDbHatch::kTextbox | AcDbHatch::kExternal;
                        break;

                    case DWG_OUTERMOST_PATH:
                        loopType |= AcDbHatch::kOutermost | AcDbHatch::kExternal;
                        break;

                    case DWG_EXTERNAL_PATH:
                        loopType |= AcDbHatch::kExternal;
                        break;
                    }
                }
            else if (NULL != loopsDescr && NULL != loopsDescr->h.myHeader)
                {
                ElementHandle   headerElm(loopsDescr->h.myHeader, false, false, context.GetModel());
                if (GroupedHoleHandler::IsGroupedHole(headerElm) && loopsDescr->el.hdr.dhdr.props.b.h)
                    loopType |= AcDbHatch::kOutermost;
                else
                    loopType |= AcDbHatch::kExternal;
                }
            }

        AddLoop (pHatch, loopsEh, hatchEh, pTransform, pNormal, isAssociative, isAssocRegion, loopType, pEntity, context);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetHatchSymbology
(
AcDbHatch*                  pHatch,
MSElementDescrCP            pElmdscr,
PatternParams*              pPatParams,
ConvertFromDgnContextR      context
)
    {
    UInt32                  color = pPatParams->color, weight = pPatParams->weight;
    Int32                   style = (int) pPatParams->style;
    MSElementDescrCP        pSymbologyDescr = context.GetSymbologyTemplate(pElmdscr);

    if (NULL != pSymbologyDescr)
        {
        if (PatternParamsModifierFlags::None == (pPatParams->modifiers & PatternParamsModifierFlags::Color))
            color = pSymbologyDescr->el.hdr.dhdr.symb.color;

        if (PatternParamsModifierFlags::None == (pPatParams->modifiers & PatternParamsModifierFlags::Weight))
            weight = pSymbologyDescr->el.hdr.dhdr.symb.weight;

        if (PatternParamsModifierFlags::None == (pPatParams->modifiers & PatternParamsModifierFlags::Style))
           style = pSymbologyDescr->el.hdr.dhdr.symb.style;
        }


    // if it is a cell pattern, use effective cell symbology
    if (PatternParamsModifierFlags::None != (pPatParams->modifiers & PatternParamsModifierFlags::Cell))
        {
        EditElementHandle   cellDef (pPatParams->cellId, context.GetModel());
        if (cellDef.IsValid())
            {
            ElementHandle   symbTemplate;
            if (!cellDef.GetElementCP()->hdr.dhdr.props.b.r && ComplexHeaderDisplayHandler::GetComponentForDisplayParams(symbTemplate, cellDef) && symbTemplate.IsValid())
                {
                MSElementCP elem = symbTemplate.GetElementCP ();

                color  = elem->hdr.dhdr.symb.color;
                style  = elem->hdr.dhdr.symb.style;
                weight = elem->hdr.dhdr.symb.weight;
                }
            }
        }

    context.UpdateEntitySymbologyFromDgn (pHatch, color, weight, style, pSymbologyDescr->el.ehdr.level);

    LayerClassOverride      layerClassOverride = CLASS_LAYER_None;
    AcDb::Visibility        visibility = AcDb::kVisible;

    if ((DgnElementClass)pSymbologyDescr->el.hdr.dhdr.props.b.elementClass == DgnElementClass::Construction)
        {

        switch (context.GetSettings().GetConstructionClassMapping())
            {
            case Construction_Invisible:
                visibility = AcDb::kInvisible;
                break;

            case Construction_Layer:
                layerClassOverride = CLASS_LAYER_Construction;
                break;

            case Construction_DefPoints:
                layerClassOverride = CLASS_LAYER_DefPoints;
                break;
            }
        }

    context.UpdateEntityLevelFromElement (pHatch, pSymbologyDescr, layerClassOverride);
    pHatch->setVisibility (visibility);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2004
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ApplyGradientSettings
(
AcDbHatch*                  pHatch,
GradientSymb const&         gradientSymb
)
    {
    AcString        gradientName;

    pHatch->setHatchObjectType (AcDbHatch::kGradientObject);
    switch (gradientSymb.GetMode())
        {
        default:
        case GradientMode::Linear:
            gradientName = "LINEAR";
            break;

        case GradientMode::Spherical:
            gradientName = "SPHERICAL";
            break;

        case GradientMode::Hemispherical:
            gradientName = "HEMISPHERICAL";
            break;

        case GradientMode::Cylindrical:
            gradientName = "CYLINDER";
            break;

        case GradientMode::Curved:
            gradientName = "CURVED";
            break;
        }

    // "INVLINEAR" crashed ACAD! This is likely an ACAD bug but we still need to work around it:
    if ((gradientSymb.GetFlags() & static_cast<int>(GradientFlags::Invert)) && GradientMode::Linear != gradientSymb.GetMode())
        gradientName = AcString("INV") + gradientName;

    pHatch->setGradient (AcDbHatch::kPreDefinedGradient, gradientName);
    pHatch->setGradientAngle (gradientSymb.GetAngle());
    pHatch->setGradientShift ((float)gradientSymb.GetShift());

    int                 numMstnKeys = gradientSymb.GetNKeys();
    if (numMstnKeys > 2 || numMstnKeys <= 0)
        DIAGNOSTIC_PRINTF ("Ignoring interior gradient keys (not supported by AutoCAD)\n");

    if (0 == numMstnKeys)
        return;

    // AutoCAD always has two keys.
    AcCmColor           colors[2];
    float               values[2];

    for (int iKey=0; (iKey < 2) && (iKey < numMstnKeys); iKey++)
        {
        RgbColorDef     rgbColor;
        double          value;

        gradientSymb.GetKey (rgbColor, value, iKey == 1 ? numMstnKeys - 1 : iKey);
        colors[iKey].setRGB(rgbColor.red, rgbColor.green, rgbColor.blue); //setColorMethod - deprecated in realdwg2021
        values[iKey] = (float)value;
        }

    /*-----------------------------------------------------------------------------------
    DWG always expects two colors.  The single-gradient option is only used by hatch
    dialog box, and the tint option practically produced the second color.  Therefore,
    for single-gradient, add the 2nd color as white.
    -----------------------------------------------------------------------------------*/
    if (1 == numMstnKeys)
        {
        //colors[1].setColorMethod (AcCmEntityColor::kByColor);
        colors[1].setRGB (0xFF, 0xFF, 0xFF);
        values[1] = 1.0;
        }

    pHatch->setGradientColors (2, colors, values);
    pHatch->setGradientOneColorMode (1 == numMstnKeys);
    pHatch->setShadeTintValue ((float)gradientSymb.GetTint());
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsValidPatternDefLine
(
DwgHatchDefLine            *pDefLine,
double                      rangeDiagonal
)
    {
    double          offsetMagnitude = pDefLine->offset.magnitude();

    if (0.0 == offsetMagnitude || rangeDiagonal / offsetMagnitude > MAX_HATCH_ITERATIONS)
        return false;

    if (0 != pDefLine->nDashes)
        {
        double          totalDashLength = 0.0;
        for (int i=0; i<pDefLine->nDashes; i++)
            totalDashLength += fabs (pDefLine->dashes[i]);

        if (0.0 == totalDashLength || rangeDiagonal / totalDashLength > MAX_HATCH_ITERATIONS)
            return false;
        }

    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        ExtractPatternDefinition
(
HatchPattern&               pHatchPattern,
DwgHatchDefLine*            pHatchLines,
int                         nHatchLines,
double                      patternScale,
double                      scaleFromDGN,
double                      rotationAngle,
bool                        mirror,
DPoint3dP                   pOrigin,
double                      rangeDiagonal
)
    {
    double                  scale = patternScale * scaleFromDGN;
    if (0 == nHatchLines || NULL == pHatchLines)
        return ZeroHatchLines;

    RotMatrix               rotationMatrix = RotMatrix::FromAxisAndRotationAngle (2, rotationAngle);

    for (int iLine=0; iLine < nHatchLines; iLine++)
        {
        DwgHatchDefLine             defLine = pHatchLines[iLine];
        HatchPatternLine            patternLine;

        // Validation.
        if (!IsValidPatternDefLine (&defLine, rangeDiagonal/patternScale))
            {
            DIAGNOSTIC_PRINTF ("Ignoring Invalid Hatch Pattern line\n");
            return BadPatternDefLine;
            }

        if (mirror)
            {
            defLine.angle     = -defLine.angle;
            defLine.through.y = -defLine.through.y;
            defLine.offset.y  = -defLine.offset.y;
            }

        patternLine.m_dLineAngle = defLine.angle;
        if (0.0 != rotationAngle)
            {
            DPoint3d    through, offset;

            through.init (&defLine.through);
            offset.init (&defLine.offset);

            patternLine.m_dLineAngle += rotationAngle;
            rotationMatrix.multiply (&through);
            rotationMatrix.multiply (&offset);

            patternLine.m_basePoint     = RealDwgUtil::GePoint2dFromDPoint3d (through);
            patternLine.m_patternOffset = RealDwgUtil::GeVector2dFromDPoint3d (offset);
            }
        else
            {
            patternLine.m_basePoint     = RealDwgUtil::GePoint2dFromDPoint2d (defLine.through);
            patternLine.m_patternOffset = RealDwgUtil::GeVector2dFromDPoint2d (defLine.offset);
            }

        if (1.0 != scale)
            {
            patternLine.m_basePoint *= scale;
            patternLine.m_patternOffset *= scale;
            }

        if (NULL != pOrigin)
            {
            patternLine.m_basePoint.x += pOrigin->x;
            patternLine.m_basePoint.y += pOrigin->y;
            }

        for (int jDash=0; jDash<defLine.nDashes; jDash++)
            patternLine.m_dashes.append (defLine.dashes[jDash] * scale);

        pHatchPattern.push_back (patternLine);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static double               ExtractRotationFromPatternParams
(
AcDbHatch*                  pHatch,
PatternParams*              pPatParams,
TransformCR                 localTransform
)
    {
    RotMatrix           dMatrix;
    dMatrix.InitProduct (localTransform, pPatParams->rMatrix);
    dMatrix.normalizeRowsOf (&dMatrix, NULL);

    DPoint3d            headerExtrusion;
    RealDwgUtil::DPoint3dFromGeVector3d (headerExtrusion, pHatch->normal());

    double              extrusionAngle = 0.0;
    DPoint3d            extrusionDirection;
    RealDwgUtil::ExtractExtrusion (extrusionDirection, extrusionAngle, dMatrix);
    if (extrusionDirection.DotProduct (headerExtrusion) < 0.0)
        extrusionAngle = -extrusionAngle;

    return extrusionAngle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        ExtractPatternDefinitionFromHatchParams
(
AcDbHatch*                  pHatch,
HatchPattern&               hatchPattern,
WCharP                      patternName,
PatternParams*              pPatternParams,
DPoint3dP                   pOrigin,
double                      rangeDiagonal,
ConvertFromDgnContextR      context
)
    {
    double                      angle1 = (PatternParamsModifierFlags::None != (pPatternParams->modifiers & PatternParamsModifierFlags::Angle1)) ? pPatternParams->angle1 : 0.0;
    double                      scaleFromDGN = context.GetScaleFromDGN();
    DwgHatchDefLine             hatchLines[2];
    int                         nHatchLines;

    nHatchLines = 0;
    memset (hatchLines, 0, sizeof(hatchLines));
    if (PatternParamsModifierFlags::None != (pPatternParams->modifiers & PatternParamsModifierFlags::Space1) && 0.0 != pPatternParams->space1)
        {
        // Validation:  TR: 104301.
        if (fabs (pPatternParams->space1) < 1.0E-8)         // UORs.
            {
            DIAGNOSTIC_PRINTF ("Ignoring pattern with invalid spacing: %f\n" , pPatternParams->space1);
            return BadHatchSpacing;
            }

        wcscpy (patternName, L"HATCH");
        hatchLines[nHatchLines].angle = angle1;
        hatchLines[nHatchLines].offset.x = -pPatternParams->space1 * sin (hatchLines[nHatchLines].angle);
        hatchLines[nHatchLines].offset.y =  pPatternParams->space1 * cos (hatchLines[nHatchLines].angle);
        hatchLines[nHatchLines].nDashes = 0;
        nHatchLines++;
        }

    if (PatternParamsModifierFlags::None != (pPatternParams->modifiers & PatternParamsModifierFlags::Space2) && 0.0 != pPatternParams->space2)
        {
        wcscpy (patternName, L"CROSSHATCH");
        hatchLines[nHatchLines].angle = (PatternParamsModifierFlags::None != (pPatternParams->modifiers & PatternParamsModifierFlags::Angle2)) ? pPatternParams->angle2 : (angle1 + msGeomConst_piOver2);
        hatchLines[nHatchLines].offset.x = -pPatternParams->space2 * sin (hatchLines[nHatchLines].angle);
        hatchLines[nHatchLines].offset.y =  pPatternParams->space2 * cos (hatchLines[nHatchLines].angle);
        hatchLines[nHatchLines].nDashes = 0;
        nHatchLines++;
        }

    return ExtractPatternDefinition (hatchPattern, hatchLines, nHatchLines, 1.0, scaleFromDGN,
                                        ExtractRotationFromPatternParams (pHatch, pPatternParams, context.GetLocalTransform()), 
                                        pPatternParams->rMatrix.determinant() < 0.0,
                                        pOrigin, rangeDiagonal);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        ExtractPatternDefinitionFromCell
(
AcDbHatch*                  pHatch,
HatchPattern&               hatchPattern,
WCharP                      patternName,
MSElementDescrCP            pBoundaryDescr,
PatternParams*              pPatternParams,
DPoint3dP                   pOrigin,
double                      rangeDiagonal,
ConvertFromDgnContextR      context
)
    {
    EditElementHandle       cellDef(pPatternParams->cellId, context.GetModel());
    if (!cellDef.IsValid())
        return  CantAccessMstnElement;

    MSElementCP             cellElem = cellDef.GetElementCP ();
    int                     nHatchLines;
    DwgHatchDefLine         hatchLines[MAX_DWG_EXPANDEDHATCH_LINES];

    // MicroStation automatically detects and discards patterns with spacing less than one half UOR.
    // We need to ignore these upon save as well, or risk hanging AutoCAD (and MicroStation)
    // when the tiny pattern is regened.  (Problem from Fernando Moriera for Atstrid Castillo).
    DPoint3d        spacing;
    spacing.x = pPatternParams->space1 + pPatternParams->scale * (double) (cellElem->hdr.dhdr.range.xhighlim - cellElem->hdr.dhdr.range.xlowlim);
    spacing.y = pPatternParams->space2 + pPatternParams->scale * (double) (cellElem->hdr.dhdr.range.yhighlim - cellElem->hdr.dhdr.range.ylowlim);

    // Validate pattern size - A pattern that is too small will cause AutoCAD (and MicroStation to hang.)
    if (spacing.x < .5 || spacing.y < .5)
        {
        DIAGNOSTIC_PRINTF ("Ignoring pattern with invalid spacing: %f, %f ", spacing.x, spacing.y);
        return BadHatchSpacing;
        }

    double          diagonal = GetRangeDiagonal (pBoundaryDescr);
    double          approxNPatterns = (diagonal * diagonal) / (spacing.x * spacing.y);

    //New dwg save settings added to support large valid patterns, ADO-880861
    bool allowLargePatterns = context.GetSettings().AllowLargePatternPlacement();
    if (!allowLargePatterns)
        {
         if (approxNPatterns > MAX_PATTERN_LIMIT)
             {
             DIAGNOSTIC_PRINTF("Ignoring pattern with invalid spacing, approximate Patterns: %f\n", approxNPatterns);
             return TooManyHatchLines;
             }
        }

    StatusInt       status = RealDwgUtil::ExtractDwgDefinitionFromCell (hatchLines, &nHatchLines, MAX_DWG_EXPANDEDHATCH_LINES, cellDef,
                                                                        pPatternParams->space1 / pPatternParams->scale, 
                                                                        pPatternParams->space2 / pPatternParams->scale);
    if (SUCCESS != status)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    double          angle = (PatternParamsModifierFlags::None == (pPatternParams->modifiers & PatternParamsModifierFlags::Angle1)) ? 0.0 : pPatternParams->angle1;

    RealDwgUtil::TerminatedStringCopy (patternName, pPatternParams->cellName, MAX_CELLNAME_LENGTH);
    wcsupr (patternName);

    if (nHatchLines == MAX_DWG_EXPANDEDHATCH_LINES)
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_PatternCellTruncated, false, patternName, patternName);

    RealDwgStatus       rdwgStatus = ExtractPatternDefinition (hatchPattern,
                                                       hatchLines, nHatchLines, pPatternParams->scale, context.GetScaleFromDGN(),
                                                       angle + ExtractRotationFromPatternParams (pHatch, pPatternParams, context.GetLocalTransform()),
                                                       pPatternParams->rMatrix.determinant() < 0.0,
                                                       pOrigin, rangeDiagonal);

    return rdwgStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 AssociativeIdsPresent
(
MSElementDescrCP            pDescr
)
    {
    ElementIdArray      boundaryIds;
    DependencyManagerLinkage::GetRootElementIdsInMSElement(boundaryIds, const_cast <MSElement *> (&pDescr->el), DEPENDENCYAPPID_AssocRegion, 0/*TYPE_REGIONBOUNDARY*/);

    size_t              nLinkIds = boundaryIds.size ();
    if (0 == nLinkIds)
        {
        DependencyManagerLinkage::GetRootElementIdsInMSElement(boundaryIds, const_cast <MSElement *> (&pDescr->el), DEPENDENCYAPPID_AssocRegion, 1/*TYPE_ROOTBOUNDARY*/);
        nLinkIds = boundaryIds.size ();
        }
    for (size_t i=0; i < nLinkIds; i++)
        if (boundaryIds[i] != INVALID_ELEMENTID)
            return true;

    if (pDescr->h.isHeader)
        {
        for (pDescr = pDescr->h.firstElem; NULL != pDescr; pDescr = pDescr->h.next)
            if (AssociativeIdsPresent (pDescr))
                return true;
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsHatchByElement (ElementHandleCR inElement)
    {
    /*-------------------------------------------------------------------------------------------------
    Prior to Vancouver, we compare hatch object ID against input element ID.  If they are not the same, 
    i.e., the input element must be saved as a boundary object while the hatch entity as added for fill 
    or pattern, we know the output hatch should be associative.
    We cannot compare ID's anymore, because we no longer do pre-saving objects to database - we now 
    post-saving objects to database.  So we have to check element type to determine if the input element
    is filled/patterned by element.
    -------------------------------------------------------------------------------------------------*/
    MSElementTypes  type = (MSElementTypes)inElement.GetElementType ();
    switch (type)
        {
        case SHAPE_ELM:
        case CURVE_ELM:
        case CMPLX_SHAPE_ELM:
        case CMPLX_STRING_ELM:
        case ELLIPSE_ELM:
        case ARC_ELM:
        case BSPLINE_CURVE_ELM:
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     ApplySolidFillColor (AcDbHatch* acHatch, UInt32 fillColor, ElementHandleCR inElement, ConvertFromDgnContextR context)
    {
    if (context.UseLevelSymbologyOverrides() && COLOR_BYLEVEL != fillColor)
        {
        bool    colorOverridden = false;
        context.GetFileHolder().GetLayerIndex()->GetSymbologyOverrides (&colorOverridden, NULL, NULL, inElement.GetElementCP()->ehdr.level);

        if (colorOverridden)
            fillColor = COLOR_BYLEVEL;
        }

    acHatch->setColor (context.GetColorFromDgn(fillColor, acHatch->colorIndex()), true);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     CanSaveHatchAnnotationScale (PatternParams const& patternParams)
    {
    /*---------------------------------------------------------------------------------------------------
    A temporary workaround of TFS#61012 - cells such as GRAVEL with small effective scale cannot be saved 
    to DWG and retain reasonable accuracy, due to the 80-character per line limit in the ASCII pattern file.
    Art Cooney promised to look at a better workaround in a future release (perhaps R2016?).  Before that 
    change becomes available, we simply remove the annotation scale from such a pattern.
    ---------------------------------------------------------------------------------------------------*/
    if (patternParams.GetCellId() > 0 && 0 == wcsicmp(patternParams.GetCellName(), L"GRAVEL"))
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::HatchFromElement
(
AcDbHatch*                  pHatch,
ElementHandleCR             elemHandle,     // the element we're creating the hatch for.
AcDbEntity*                 pEntity,        // the corresponding AcDbEntity.
bool                        isPatterned,
bool                        isFilled
)
    {
    if (NULL == pHatch || (!isPatterned && !isFilled))
        return  NotApplicable;

    /*-------------------------------------------------------------------------------------------
    If user has specified a view in which patterns are turned OFF, we try to handle not displayed
    patterns by following these rules:

        1. Only handle patterned elements - fill is controlled by FILLMODE in ACAD.
        2. FILLMODE has no impact on xref's, however.
        3. Skip patterns in master file only(when in a ref file, viewInfo=null in below code).
        4. If fill is also turned off, don't skip patterns.

    While above rules suffice TFS404002, one case is left uncovered: master file has patterns 
    turned off and fill turned on.  In this case, patterns in a ref file will still be always 
    saved and displayed in the master file - just like how it works in ACAD.  The only way to 
    get around this case to also Skip pattern elements in a ref file, but that probably is a 
    good idea.  We leave this case out until we hear a real user request in the future.
    -------------------------------------------------------------------------------------------*/
    if (isPatterned && this->GetLevelDisplayView() >= 0)
        {
        ViewInfoP   viewInfo = m_dgnFile->GetViewGroups().GetActiveViewInfoP (this->GetLevelDisplayView());
        if (nullptr != viewInfo && 0 == viewInfo->GetViewFlags().patterns && 0 != viewInfo->GetViewFlags().fill)
            return  RealDwgIgnoreElement;
        }

    IAreaFillPropertiesQuery*   areaObj = dynamic_cast <IAreaFillPropertiesQuery*> (&elemHandle.GetHandler());
    if (NULL == areaObj)
        return MstnElementUnacceptable;

    DVec3d                  normal          = DVec3d::From(0, 0, 1);
    DPoint3d                normalPoint     = DPoint3d::From(0, 0, 0);
    if (!RealDwgUtil::GetElementNormal(&normal, &normalPoint, elemHandle))
        return MstnElementUnacceptable;

    DgnModelP               model           = this->GetModel();
    DgnFileP                dgnFile         = model->GetDgnFileP ();
    if (NULL == model || NULL == dgnFile)
        return  BadModel;

    // Set entity header now because properties such as color may get overridden.
    this->UpdateEntityPropertiesFromElement (AcDbEntity::cast(pHatch), elemHandle);

    // default to classic hatch object - may change to gradient fill as needed:
    pHatch->setHatchObjectType (AcDbHatch::kHatchObject);

    // default pattern background fill to none
    AcCmColor                   backgroundColor;
    //backgroundColor.setColorMethod (AcCmEntityColor::kNone);
    backgroundColor.setNone();
    DPoint3d                    patternOrigin = DPoint3d::From(0,0,0);
    PatternParams               patternParams;
    bvector<DwgHatchDefLine>    hatchLines;
    bool                        annotative = false;
    MSElementDescrCP            pElmDscr = elemHandle.GetElementDescrCP();
    StatusInt                   status = BSISUCCESS;

    patternParams.Init ();

    // set gradient or solid fill if not a patterned element:
    if (isFilled && !isPatterned)
        {
        pHatch->setPattern (AcDbHatch::kPreDefined, AcString ("SOLID"));

        bool                alwaysFilled = false;
        UInt32              fillColor;
        GradientSymbPtr     gradientSymb;
        if (areaObj->GetGradientFill(elemHandle, gradientSymb))
            ApplyGradientSettings (pHatch, *gradientSymb);
        else if (areaObj->GetSolidFill(elemHandle, &fillColor, &alwaysFilled))
            ApplySolidFillColor (pHatch, fillColor, elemHandle, *this);
        }
    else
        {
        PatternParamsPtr    tmpParams;

        // try to extract the associative pattern.
        if (!areaObj->GetPattern(elemHandle, tmpParams, &hatchLines, &patternOrigin, 0))
            return  BadHatch;

        patternParams = *tmpParams;

        // if we got here, we were able to get the patternParams.
        if (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::TrueScale))
            {
            double          trueScale;

            // get the modelRef for the default model in the dgnFile of modelRef.
            ModelId         defaultModelID = dgnFile->GetDefaultModelId ();
            DgnModelP       defaultModelRef = model->GetModelId() == defaultModelID ? model : dgnFile->FindLoadedModelById(defaultModelID);

            if (BSISUCCESS == modelInfo_getUorScaleBetweenModels(&trueScale, defaultModelRef, model))
                patternParams.scale *= trueScale;
            }

        // set annotative hatch only in modelspace
        annotative = PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::AnnotationScale);
        if (annotative && (!CanSaveHatchAnnotationScale(patternParams) || !this->AddAnnotationScaleToObject(pHatch, patternParams.annotationscale, elemHandle.GetElementId())))
            {
            // remove annotation scale from the pattern in a paperspace
            annotative = false;
            patternParams.scale *= patternParams.annotationscale;
            }

        if (patternParams.rMatrix.Determinant() < 0.0)
            normal.Negate ();

        DVec3d          zColumn;
        patternParams.rMatrix.GetColumn (zColumn, 2);
        if (zColumn.DotProduct(normal) < 0.0)
            normal.Negate();

        // transfer the element symbology to the AcDbHatch object.
        SetHatchSymbology (pHatch, pElmDscr, &patternParams, *this);

        // if the area is both filled & patterned, add fill color now
        bool                alwaysFilled = false;
        UInt32              fillColor;
        if (isFilled && areaObj->GetSolidFill(elemHandle, &fillColor, &alwaysFilled))
            backgroundColor = this->GetColorFromDgn (fillColor, pHatch->backgroundColor().colorIndex());
        }

    // set or remove pattern background color
    pHatch->setBackgroundColor (backgroundColor);

    DVec3d      loopNormal = normal;
    this->GetLocalTransform().MultiplyMatrixOnly ((DPoint3dR)normal);
    this->GetTransformFromDGN().Multiply (normalPoint);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (this->GetSettings().IsZeroZCoordinateEnforced())
        normalPoint.z = 0.0;

    pHatch->setNormal (RealDwgUtil::GeVector3dFromDVec3d (normal));
    pHatch->setElevation (normal.DotProduct(normalPoint));

    Transform               compositeTransform = this->GetTransformFromDGN();
    Transform               displayTransform;
    Transform               inverseDisplayTransform;
    if (RealDwgUtil::GetExtrusionTransform (displayTransform, pHatch->normal(), pHatch->elevation()))
        {
        inverseDisplayTransform.inverseOf (&displayTransform);
        compositeTransform.productOf (&inverseDisplayTransform, &compositeTransform);
        }

    bool                    hasPattern      = false;
    HatchPattern            hatchPattern;
    WChar                   patternName[MAX_CELLNAME_LENGTH];
    if (isPatterned)
        {
        double          rangeDiagonal = GetRangeDiagonal (pElmDscr);
        RealDwgStatus   rdwgStatus;
        bool            hasOrigin = (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::DwgHatchOrigin));

        compositeTransform.multiply (&patternOrigin, &patternOrigin);

        if (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::DwgHatchDef))
            {
            if (BSISUCCESS != (status = LinkageUtil::ExtractNamedStringLinkageByIndex(patternName, MAX_CELLNAME_LENGTH, STRING_LINKAGE_KEY_DWGPatternName, 0, &pElmDscr->el)))
                return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

            if (RealDwgSuccess != (rdwgStatus = ExtractPatternDefinition (hatchPattern, &hatchLines.front(), patternParams.dwgHatchDef.nDefLines,
                                                               1.0, this->GetScaleFromDGN(), ExtractRotationFromPatternParams (pHatch, &patternParams, this->GetLocalTransform()),
                                                               patternParams.rMatrix.determinant() < 0.0, NULL, rangeDiagonal)))
                return rdwgStatus;
            }
        else if (0 != patternParams.cellId)
            {
            rdwgStatus = ExtractPatternDefinitionFromCell (pHatch, hatchPattern, patternName, pElmDscr, &patternParams, hasOrigin ? NULL : &patternOrigin, rangeDiagonal, *this);
            }
        else
            {
            rdwgStatus = ExtractPatternDefinitionFromHatchParams (pHatch, hatchPattern, patternName, &patternParams, hasOrigin ? NULL : &patternOrigin, rangeDiagonal, *this);
            }

        if (RealDwgSuccess != rdwgStatus)
            return rdwgStatus;
        hasPattern = true;
        }

    bool                isAssocRegion;
    bool                isAssociative = false;
    RegionParams        regionParams;

    // flood parameters
    bool                isFlood = false;
    size_t              nSeedPoints = 0;
    DPoint3dArray       seedPoints;
    double              hatchPixelSize = 0.0;

    if (isAssocRegion = (SUCCESS == AssocRegionCellHeaderHandler::GetRegionParams(elemHandle, regionParams)))
        {
        switch (regionParams.GetType())
            {
            case RegionType::Flood:
                {
                if (SUCCESS == AssocRegionCellHeaderHandler::GetFloodSeedPoints(elemHandle, &seedPoints))
                    {
                    nSeedPoints = seedPoints.size ();
                    isFlood = true;
                    isAssociative = AssociativeIdsPresent (pElmDscr);
                    compositeTransform.Multiply (&seedPoints[0], (int)nSeedPoints);

                    if (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::PixelSize))
                        hatchPixelSize = patternParams.dwgHatchDef.pixelSize;
                    else
                        hatchPixelSize = DWG_FLOOD_PIXELSIZE * this->GetScaleFromDGN();
                    }
                break;
                }

            case RegionType::ExclusiveOr:
                isAssociative = AssociativeIdsPresent (pElmDscr);
                break;

            default:            // AutoCAD doesn't have boolean types.  Make non-associative hatch.
                break;
            }
        // if it is a new hatch, add it to database as some later operations require its database residency
        if (isAssociative && pHatch->objectId().isNull())
            this->AddEntityToCurrentBlock (AcDbEntity::cast(pHatch), pElmDscr->el.ehdr.uniqueId);
        }
    else
        {
        // attempt to make associative only if it is a hatch-by-element and the hatch is to be added to the target entity:
        isAssociative = IsHatchByElement(elemHandle) && NULL != pEntity && pHatch != pEntity;
        }

    if (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::IslandStyle))
        pHatch->setHatchStyle ((AcDbHatch::HatchStyle) patternParams.dwgHatchDef.islandStyle);
    else
        pHatch->setHatchStyle (AcDbHatch::kNormal);

    // Check for new style hatch...
    if (PatternParamsModifierFlags::None != (patternParams.modifiers & PatternParamsModifierFlags::DwgHatchOrigin))
        pHatch->setOriginPoint (RealDwgUtil::GePoint2dFromDPoint3d (patternOrigin));

    // Remove current loops and objectIDs.
    pHatch->removeAssocObjIds ();
    pHatch->setAssociative (false);
    for (int iLoop = pHatch->numLoops() -1; iLoop >= 0; iLoop--)
        pHatch->removeLoopAt (iLoop);

    AddLoops (pHatch, elemHandle, elemHandle, &compositeTransform, &loopNormal, isAssociative, isAssocRegion, isFlood ? AcDbHatch::kDerived : AcDbHatch::kDefault, pEntity, *this);

    // if necessary, add the pattern here.
    if (hasPattern)
        {
        SetRawPattern (pHatch, AcDbHatch::kCustomDefined, patternName, 0.0, 1.0, hatchPattern);

        RealDwgUtil::SetObjectAnnotative (pHatch, annotative);
        }

    // if it's a flood, we need to set the seed points and hatch pixel size, but it has to be done after the loops have been added.
    if (isFlood)
        SetSeedPointsAndHatchPixelSize (seedPoints, nSeedPoints, hatchPixelSize, pHatch);

    if (0 == pHatch->numLoops())
        return BadHatch;

    return RealDwgSuccess;
    }

#if defined (TEXTPATH_DEBUG)
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static int              CountTextPaths (MSElementDescrP edP)
    {
    int     elementCount = 0;
    MSElementDescrP testEdP;
    for (testEdP = edP; NULL != testEdP; testEdP=testEdP->h.next)
        elementCount++;

    return elementCount;
    }
#endif


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtHatch : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToElement
(
AcDbObjectP                 acObject,
EditElementHandleR          outElement,
ConvertToDgnContextR        context
) const override
    {
    AcDbHatch*              pHatch = AcDbHatch::cast (acObject);
    AcCmColor               backgroundColor = pHatch->backgroundColor ();
    bool                    filled = pHatch->isSolidFill() || !backgroundColor.isNone();
    Transform               extrusionTransform, transformToDGN = context.GetTransformToDGN ();
    RotMatrix               rMatrix;
    Int32                   hatchStyle = pHatch->hatchStyle();
    bool                    derivedLoopFound = false, textFound = false;

    auto numLoops = pHatch->numLoops();
    if (numLoops > 2000)
        {
        DIAGNOSTIC_PRINTF ("Skipped a bad hatch(ID=%I64d) with %d loops!\n", context.ElementIdFromObject(acObject), numLoops);
        return  RealDwgStatus::BadData;
        }

#if defined (REALDWG_FILER_DEBUG_VERBOSE)
    // open test file hatch8.dwg to get the dump output at the top of this file!
    RecordingFiler          filer (220);
    filer.RecordData (pHatch);
    filer.DumpList ("Hatch in ToElement");
#endif

    if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, pHatch->normal(), pHatch->elevation()))
        {
        extrusionTransform.getMatrix (&rMatrix);
        transformToDGN.InitProduct (transformToDGN, extrusionTransform);
        }
    else
        {
        rMatrix.InitIdentity ();
        }

    DgnModelP           model = context.GetModel ();
    ElementAgenda       loopsAgenda;

    for (int iPath=0, nLoops = numLoops; iPath < nLoops; iPath++)
        {
        Int32           loopType = pHatch->loopTypeAt (iPath);
        bool            external   = 0 != (AcDbHatch::kExternal  & loopType);
        bool            textBox    = 0 != (AcDbHatch::kTextbox   & loopType);
        bool            outermost  = 0 != (AcDbHatch::kOutermost & loopType);
        bool            textIsland = 0 != (AcDbHatch::kTextIsland & loopType);
        bool            notClosed  = 0 != (AcDbHatch::kNotClosed & loopType);

        derivedLoopFound |= (0 != (AcDbHatch::kDerived & loopType));

        // Fill always ignores unclosed boundaries
        if (notClosed && filled)
            continue;

        if (!external)
            {
            switch (hatchStyle)
                {
                case AcDbHatch::kOuter:
                    if (!outermost)
                        continue;
                    break;

                case AcDbHatch::kIgnore:
                    continue;
                }
            }

        // may have to reverse loop direction
        DVec3d      normal, *pHatchNormal = NULL;
        if (!textBox && !textIsland && external && pHatch->isGradient())
            {
            RealDwgUtil::DVec3dFromGeVector3d (normal, pHatch->normal());
            pHatchNormal = &normal;
            }

        CurveVector::BoundaryType   boundaryType = CurveVector::BOUNDARY_TYPE_Outer;
        if ((!external && !outermost) || textBox)
            boundaryType = CurveVector::BOUNDARY_TYPE_Inner;
        /*---------------------------------------------------------------------------------------------------
        When a loop is not closed, it should be a part of a closed loop formed by a collection of open loops.
        Keep these "loops" as open loops to allow them to eventually form a closed loop.  There are cases in
        which the bit kNotClosed is erroneously set in a hatch which only has a single loop, TFS#69857 as such
        an example, we want to close the loop or otherwise our assoc handler would fail.
        ---------------------------------------------------------------------------------------------------*/
        if (notClosed && nLoops > 1)
            boundaryType = CurveVector::BOUNDARY_TYPE_Open;

        EditElementHandle           pathElement;
        if (RealDwgSuccess == ElementFromLoop(pathElement, pHatch, iPath, boundaryType, pHatchNormal, transformToDGN, context))
            {
            MSElementDescrP pPathDescr = pathElement.GetElementDescrP ();

            if (textBox || textIsland)
                {
                pPathDescr->el.hdr.dhdr.props.b.h = true;
                textFound = true;
                }

            if (pathElement.IsValid())
                this->AddElementsToAgenda (loopsAgenda, pathElement, model);
            }
        }

    if (loopsAgenda.size() < 1)
        return  BadHatch;

    DPoint3dArray   seedPoints;
    double          hatchPixelSize = 0.0;
    /*--------------------------------------------------------------------------------------------------------
    We used to read seed points only if derivedLoopFound = true, but that flag does not seem reliable as we'd
    expect. TFS#67659 has just such a hatch which does not have the kDerived bit set but has valid seed points.
    Yet, TFS#81218 has another case which also does not have this bit set, and has an invalid seed point at 
    HPORIGIN=0,0,0.  To make both cases to work, we need to check for both conditions.  Checking points is not
    a bullet proof method but good enough to be used for handling these DWG cases our patterning logic does not
    handle.
    --------------------------------------------------------------------------------------------------------*/
    this->GetSeedPointsAndHatchPixelSize (&seedPoints, &hatchPixelSize, pHatch, transformToDGN, derivedLoopFound, context.GetDatabase());

    this->CreateAssociativeRegion (outElement, loopsAgenda, seedPoints, filled, textFound, pHatch, context);

    if (pHatch->isHatch())
        {
        // add pattern name string linkage:
        if (0 != *pHatch->patternName ())
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DWGPatternName, pHatch->patternName ());

        // Add pattern linkage
        AddPatternLinkage (outElement, rMatrix, transformToDGN, hatchPixelSize, pHatch, context);
        }

    // ignore boundary roots if the assoc region is in a shared cell def:
    if (pHatch->associative() && !context.IsInSharedCellCreation())
        context.AddPostProcessObject (pHatch);
    else
        AssocRegionCellHeaderHandler::ValidateLoops (outElement, false);

    context.ElementHeaderFromEntity (outElement, AcDbEntity::cast (acObject));

    // pHatch->backgroundColor() can return bogus data for SOLID pattern - TFS 748228. Invert to v8.11 way of determining fill color.
    backgroundColor = pHatch->isSolidFill() ? pHatch->color() : pHatch->backgroundColor();

    if (filled)
        this->SetFilled (outElement, pHatch, backgroundColor, context);
    else
        RealDwgUtil::SetElementInvisible (outElement, true, true);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContext& context) const
    {
    EditElementHandle   eeh(context.ElementIdFromObject(acObject), context.GetModel());
    if (!eeh.IsValid())
        return  CantAccessMstnElement;

    ElementRefP         elementRef = eeh.GetElementRef ();
    if (NULL == elementRef)
        return  CantAccessMstnElement;

    StatusInt           status = AssocRegionCellHeaderHandler::ValidateLoops (eeh, true);

    if (BSISUCCESS == status)
        status = eeh.ReplaceInModel (elementRef);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
void    AddElementsToAgenda (ElementAgendaR elemAgenda, EditElementHandleR chainEeh, DgnModelP model) const
    {
    /*-----------------------------------------------------------------------------------
    Workaround to add a chain of elements to an ElementAgenda - replace this when we have
    alternatives for mdlElmdscr_reverse & mdlElmdscr_assembleChains.
    -----------------------------------------------------------------------------------*/
    for (MSElementDescrP elmdscr = chainEeh.GetElementDescrP(); NULL != elmdscr; elmdscr = elmdscr->h.next)
        {
        MSElementDescrP     currElmdscr = NULL;
        if (BSISUCCESS == elmdscr->DuplicateSingle(&currElmdscr, true, false))
            {
            EditElementHandle   singleEeh (currElmdscr, true, false, model);
            elemAgenda.Insert (singleEeh);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SeedDataCallback
(
AcDb::DxfCode   dxfCode,
const void*     data,
void*           userData
)
    {
    SeedData*   seedData = (SeedData*) userData;
    if (98 == dxfCode)
        {
        int*    numSeedPointsP = (int*) data;
        if (*numSeedPointsP < MAX_AllocaSize)
            seedData->m_seedPointsExpected = (*numSeedPointsP) < MAX_FloodSeedPoints ? (*numSeedPointsP) : MAX_FloodSeedPoints;
        }
    else if ( (AcDb::kDxfXCoord == dxfCode) && (seedData->m_seedPointsExpected > 0) && (seedData->m_seedPointsSeen < seedData->m_seedPointsExpected) )
        {
        seedData->m_seedPointsSeen++;

        // we've got a 2d point, expend to 3D.
        DPoint2dP   point2dP = (DPoint2dP) data;

        // if the hatch loops are not derived from the boundary, filter out 0,0 points - TFS#81218:
        if (!seedData->m_hasDerivedLoops && fabs(point2dP->x) < TOLERANCE_PointEqual && fabs(point2dP->y) < TOLERANCE_PointEqual)
            return;

        DPoint3d    point3d;
        point3d.x = point2dP->x;
        point3d.y = point2dP->y;
        point3d.z = 0.0;

        // transform it.
        seedData->m_transform.Multiply (point3d);

        // We only want to include the seed point if it's not the same as previous seed points
        size_t  iPoint, numPoints = seedData->m_seedPoints->size();
        for (iPoint = 0; iPoint < numPoints; iPoint++)
            {
            if (point3d.IsEqual (seedData->m_seedPoints->at(iPoint), TOLERANCE_PointEqual))
                break;
            }

        // got all the way through without finding a match.
        if (iPoint >= numPoints)
            seedData->m_seedPoints->push_back (point3d);
        }
    else if (47 == dxfCode)
        {
        *seedData->m_hatchPixelSize = *((double*) data);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        GetSeedPointsAndHatchPixelSize
(
DPoint3dArray*              seedPoints,
double*                     hatchPixelSize,
AcDbHatch*                  pHatch,
TransformCR                 transform,
bool                        hasDerivedLoops,
AcDbDatabase*               pDatabase
) const
    {
    SeedData    seedData (seedPoints, hatchPixelSize, transform, hasDerivedLoops);
    ExtractionFiler filer (SeedDataCallback, pDatabase, &seedData);
    filer.ExtractFrom (pHatch);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               CreateAssociativeRegion
(
EditElementHandleR          outElement,
ElementAgendaR              loopsAgenda,
DPoint3dArray const&        seedPoints,
bool                        filled,
bool                        hasText,
AcDbHatch*                  pHatch,
ConvertToDgnContextR        context
) const
    {
    // set associative region cell name
    AcString                cellName = AcString ("DWG Hatch: ") + pHatch->patternName();
    
    // text margin appears harded coded in ACAD, 0.25 for DTEXT and 0.5 for MTEXT:
    double                  textMargin = 0.25;
    
    // collect element ID's of boundary objects
    bvector<DependencyRoot> boundaryRoots;
    if (pHatch->associative())
        {
        AcDbObjectIdArray   idArray;
        if (Acad::eOk == pHatch->getAssocObjIds(idArray))
            {
            DependencyRoot  depRoot;
            depRoot.refattid = 0;
            depRoot.ref = NULL;
            depRoot.refTransform = Transform::FromIdentity ();

            for (int i = 0; i < idArray.length(); i++)
                {
                depRoot.elemid = context.ElementIdFromObjectId (idArray[i]);
                boundaryRoots.push_back (depRoot);

                // bump up the text margin to 0.5 if any root is of an mtext
                if (hasText && idArray[i].objectClass()->isDerivedFrom(AcDbMText::desc()))
                    textMargin = 0.5;
                }
            }
        }

    // region loop style
    RegionLoops             loopStyle;
    switch (pHatch->hatchStyle())
        {
        case AcDbHatch::kIgnore:    loopStyle = RegionLoops::Ignore;           break;
        case AcDbHatch::kOuter:     loopStyle = RegionLoops::Outer;            break;
        default: 
        case AcDbHatch::kNormal:    loopStyle = RegionLoops::Alternating;      break;
        }

    // set region params
    RegionParams            regionParams;
    regionParams.SetType (seedPoints.size() > 0 ? RegionType::Flood : RegionType::ExclusiveOr);
    regionParams.SetAssociative (true);
    regionParams.SetInteriorText (hasText, textMargin);
    regionParams.SetFloodParams (loopStyle, TOLERANCE_AssociativeRegion);
    regionParams.SetInvisibleBoundary (!filled);
    regionParams.SetFlattenBoundary (false, NULL);

    // send the data to assoc region handler to create a new element:
    BentleyStatus status = AssocRegionCellHeaderHandler::CreateAssocRegionElement (outElement, loopsAgenda, &boundaryRoots[0], boundaryRoots.size(), &seedPoints[0], seedPoints.size(), regionParams, cellName.kwszPtr());

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               AddPatternLinkage
(
EditElementHandleR          regionElement,
RotMatrixCR                 rMatrix,
TransformCR                 transform,
double                      hatchPixelSize,
AcDbHatch*                  pHatch,
ConvertToDgnContextR        context
) const
    {
    IAreaFillPropertiesEdit*    areaFill = dynamic_cast <IAreaFillPropertiesEdit*> (&regionElement.GetHandler(MISSING_HANDLER_PERMISSION_ChangeAttrib));
    if (areaFill == NULL)
        return  MstnElementUnacceptable;

    PatternParams       patternParams;

    patternParams.Init ();

    patternParams.dwgHatchDef.nDefLines = (short) pHatch->numPatternDefinitions();

    // we used to check for cross hatch type but never really created such a hatch. Instead of returning much later we return now for performance reasons.
    if (patternParams.dwgHatchDef.nDefLines <= 0)
        return  BadPatternDefLine;

    double              scaleToDGN = context.GetScaleToDGN();
    DwgHatchDefLine*    pHatchLines = NULL;

    /*---------------------------------------------------------------------------------------------------------------------------------
    Create new style hatch that has an origin which is not always at 0,0.  The new style is denoted by new flag PatternParamsModifierFlags::DwgHatchOrigin.
    The origin stored as xdata is new since R2006.  It is on hatch's ECS, with the z-coordinate coming from the hatch elevation.
    ---------------------------------------------------------------------------------------------------------------------------------*/
    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::DwgHatchOrigin;
    AcGePoint2d         originPoint = pHatch->originPoint ();

    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::RawDwgLoops;
    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::RotMatrix;
    patternParams.rMatrix = rMatrix;
    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::HoleStyle;
    patternParams.SetHoleStyle (PatternParamsHoleStyleType::Parity);

    if (0.0 != pHatch->patternAngle())
        {
        patternParams.angle1 = pHatch->patternAngle();
        patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::Angle1;
        }

    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::DwgHatchDef;
    if (patternParams.dwgHatchDef.nDefLines > MAX_DWG_EXPANDEDHATCH_LINES)
        {
        DIAGNOSTIC_PRINTF ("Number of Hatch lines: %d exceeds maximum, truncated to %d\n", patternParams.dwgHatchDef.nDefLines, MAX_DWG_EXPANDEDHATCH_LINES);
        patternParams.dwgHatchDef.nDefLines = MAX_DWG_EXPANDEDHATCH_LINES;
        }

    if (pHatch->patternScale() != 1.0)
        {
        patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::Scale;
        patternParams.scale     = pHatch->patternScale();
        }

    // apply annotation scale in default model:
    double              annotationScale = 1.0;
    bool                isAnnotative = context.GetDisplayedAnnotationScale (annotationScale, pHatch);
    if (isAnnotative)
        {
        ModelInfoCR     modelInfo = context.GetModel()->GetModelInfo ();
        if (modelInfo.GetIsUseAnnotationScaleOn())
            {
            patternParams.SetAnnotationScale (annotationScale);
            // apply inverse annotation scale on pattern data
            scaleToDGN /= annotationScale;
            }
        }

    pHatchLines = (DwgHatchDefLine *) _alloca (patternParams.dwgHatchDef.nDefLines * sizeof(DwgHatchDefLine));

    for (int iHatchLine=0; iHatchLine<patternParams.dwgHatchDef.nDefLines; iHatchLine++)
        {
        DwgHatchDefLine     *pHatchLine = &pHatchLines[iHatchLine];
        AcGeDoubleArray     dashes;

        pHatch->getPatternDefinitionAt (iHatchLine, pHatchLine->angle, pHatchLine->through.x, pHatchLine->through.y, pHatchLine->offset.x, pHatchLine->offset.y, dashes);
        pHatchLine->nDashes = dashes.length() > MAX_DWG_HATCH_LINE_DASHES ? MAX_DWG_HATCH_LINE_DASHES : dashes.length();

        for (int jDash=0; jDash<pHatchLine->nDashes; jDash++)
            pHatchLine->dashes[jDash] = scaleToDGN * dashes[jDash];

        // Subtract origin from base point which will be accounted for with DGN hatch offset in stroke code.
        pHatchLine->through.x = (pHatchLine->through.x - originPoint.x) * scaleToDGN;
        pHatchLine->through.y = (pHatchLine->through.y - originPoint.y) * scaleToDGN;
        pHatchLine->offset.x  *= scaleToDGN;
        pHatchLine->offset.y  *= scaleToDGN;
        }

    // ObjectARX doesn't have anything about pixel size.
    if (0.0 != hatchPixelSize)
        {
        patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::PixelSize;
        patternParams.dwgHatchDef.pixelSize = hatchPixelSize;
        }

    patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::IslandStyle;
    patternParams.dwgHatchDef.islandStyle = pHatch->hatchStyle();

    // transform the ECS origin to ours in WCS:
    DPoint3d    hatchOrigin;
    RealDwgUtil::DPoint3dFromGePoint2d (hatchOrigin, originPoint);
    hatchOrigin.z = pHatch->elevation ();
    rMatrix.Multiply (hatchOrigin);
    context.GetTransformToDGN().Multiply (hatchOrigin);

    // validate the region element
    MSElementDescrP     elmdscr = regionElement.GetElementDescrP ();
    if (NULL == elmdscr)
        return  MstnElementUnacceptable;
    elmdscr->Validate (context.GetModel(), false);

    // set hatch origin offset from element origin:
    DPoint3d            elementOrigin;
    PatternLinkageUtil::GetHatchOrigin (elementOrigin, elmdscr->el);
    if (!elementOrigin.IsEqual(hatchOrigin, TOLERANCE_UORPointEqual))
        {
        patternParams.modifiers = patternParams.modifiers | PatternParamsModifierFlags::Offset;
        patternParams.offset.DifferenceOf (hatchOrigin, elementOrigin);
        }

    return areaFill->AddPattern (regionElement, patternParams, pHatchLines, 0) ? RealDwgSuccess : BadHatch;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
GradientMode    GradientModeFromName (bool* pInvert, WCharCP pName) const
    {
    *pInvert     = false;

    if (0 == _wcsnicmp (pName, L"INV", 3))
        {
        pName += 3;
        *pInvert = true;
        }
    if (0 == _wcsicmp (pName, L"SPHERICAL"))
        return GradientMode::Spherical;
    else if (0 == _wcsicmp (pName, L"HEMISPHERICAL"))
        return GradientMode::Hemispherical;
    else if (0 == _wcsicmp (pName, L"CYLINDER"))
        return GradientMode::Cylindrical;
    else if (0 == _wcsicmp (pName, L"CURVED"))
        return GradientMode::Curved;

    return GradientMode::Linear;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool     IsRepetitive(DPoint3dCR start, DPoint3dCR end, DPoint3dCR lastStart, DPoint3dCR lastEnd, MSElementDescrCP dscr, MSElementDescrCP lastDscr, int edgeNo, double tol) const
    {
    /*-----------------------------------------------------------------------------------
    Check for potential repetitive elements based on current start-end points and last 
    start-end points.  Exclude arcs as they may be a result from a buldge factor.
    -----------------------------------------------------------------------------------*/
    if (NULL == dscr || NULL == lastDscr || (ARC_ELM == dscr->el.ehdr.type && ARC_ELM == lastDscr->el.ehdr.type))
        return  false;

    if (start.Distance(lastStart) < tol && end.Distance(lastEnd) < tol)
        return  true;

    // check the points in reversed order only after the 2nd edge
    if (edgeNo < 2)
        return  false;

    return  (start.Distance(lastEnd) < tol && end.Distance(lastStart) < tol);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool     IsRepetitive(CurveVectorPtr& loopCurves, ICurvePrimitivePtr& newCurve, int edgeNo, double tol) const
    {
    /*------------------------------------------------------------------------------------------------
    A trivial rejection of duplicated edges - two adjacent edges with coincident start and end points.
    What this method does not do is a loop algorithm that runs through entire input edges and re-chain
    them to produce a valid loop.  That is done in the geom lib when creating assoc region from the
    CurveVector.  Only miminim effort is made here for the sake of performance.
    ------------------------------------------------------------------------------------------------*/

    // start & end points of the new curve
    DPoint3d            start, end;
    if (loopCurves->empty() || !newCurve->GetStartEnd(start, end))
        return  false;

    // start & end points of the previous curve in the loop queue
    DPoint3d            lastStart, lastEnd;
    ICurvePrimitivePtr  lastCurve = loopCurves->back ();
    if (!lastCurve.IsValid() || !lastCurve->GetStartEnd(lastStart, lastEnd))
        return  false;

    // previous curve type & new curve type:
    ICurvePrimitive::CurvePrimitiveType lastType = lastCurve->GetCurvePrimitiveType ();
    ICurvePrimitive::CurvePrimitiveType newType = newCurve->GetCurvePrimitiveType ();

    /*----------------------------------------------------------------------------------------------------------
    Check for potential repetitive elements based on current start-end points and last start-end points.  
    Exclude arcs as they may be a result from a buldge factor (both are arcs).  Exclude different start & end 
    curve type as they may not be true overlaps (a green leaf in TFS 15347).
    ----------------------------------------------------------------------------------------------------------*/
    if (lastType != newType || ICurvePrimitive::CURVE_PRIMITIVE_TYPE_Arc == newType)
        return  false;

    if (start.Distance(lastStart) < tol && end.Distance(lastEnd) < tol)
        return  true;

    // check the points in reversed order only after the 2nd edge
    if (edgeNo < 2)
        return  false;

    if (start.Distance(lastEnd) < tol && end.Distance(lastStart) < tol)
        return  true;

    /*----------------------------------------------------------------------------------------------------------
    Before we declare that there is no repetition found between the two curves, we want to check splines for 
    either full overlaping or inclusive, as some hatch loops contain such bad edges that throw our parity code 
    out, the letter m of "mesa" in test file from TFS# 15347 for instance.
    ----------------------------------------------------------------------------------------------------------*/
    if (ICurvePrimitive::CURVE_PRIMITIVE_TYPE_BsplineCurve == newType)
        {
        DPoint3d    curvePoint;
        double      param = 0.0;
        if (lastCurve->ClosestPointBounded(start, param, curvePoint) && start.Distance(curvePoint) < tol &&
            lastCurve->ClosestPointBounded(end, param, curvePoint) && end.Distance(curvePoint) < tol)
            {
            DPoint3d    midPoints[2];
            if (newCurve->FractionToPoint(0.3, midPoints[0]) && newCurve->FractionToPoint(0.7, midPoints[1]) &&
                lastCurve->ClosestPointBounded(midPoints[0], param, curvePoint) && midPoints[0].Distance(curvePoint) < tol &&
                lastCurve->ClosestPointBounded(midPoints[1], param, curvePoint) && midPoints[1].Distance(curvePoint) < tol)
                {
                // all 4 points from the new curve overlap on last curve, keep the longer one and remove the shorter:
                double  lastLength = 0.0, newLength = 0.0;
                if (!lastCurve->FastLength(lastLength) || !newCurve->FastLength(newLength))
                    return  false;

                if (lastLength > newLength)
                    return  true;

                loopCurves->pop_back ();
                }
            }
        }

    /*----------------------------------------------------------------------------------------------------------
    This edge does not overlap with pervious edge, but check if it overlaps two previous adjacent edges as a case
    found in TFS 207477 which would otherwise fail on CloneWithGapsClosed.  To workaround this 3-edge overlapping
    case, check if this new edge folds back and completely overlaps 2 previous edges.  Do this only if all 3 
    edges are linear.
    ----------------------------------------------------------------------------------------------------------*/
    if (edgeNo > 2 && loopCurves->size() > 2 && ICurvePrimitive::CURVE_PRIMITIVE_TYPE_Line == newType && ICurvePrimitive::CURVE_PRIMITIVE_TYPE_Line == lastType &&
       (start.Distance(lastStart) < tol || start.Distance(lastEnd) < tol || end.Distance(lastEnd) < tol || end.Distance(lastStart) < tol))
        {
        DVec3d      newVector, lastVector;
        newVector.NormalizedDifference (end, start);
        lastVector.NormalizedDifference (lastEnd, lastStart);
        
        if (fabs(fabs(newVector.DotProduct(lastVector)) - 1.0) < MIN_AngleRadians)
            {
            // get the curve before the last curve
            lastCurve = *(loopCurves->end() - 2);
            if (lastCurve.IsValid() && ICurvePrimitive::CURVE_PRIMITIVE_TYPE_Line == lastCurve->GetCurvePrimitiveType() &&
                lastCurve->GetStartEnd(lastStart, lastEnd))
                {
                // check the new edge's points to see if either one touchs the edge past two counts:
                if (start.Distance(lastStart) < tol || end.Distance(lastEnd) < tol ||
                    start.Distance(lastEnd) < tol || end.Distance(lastStart) < tol)
                    return  true;
                }
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool    IsDangling (DPoint3dCR prevEnd, DPoint3dCR start, DPoint3dCR end, AcGeVoidPointerArray& edges, AcGeIntArray& edgeTypes, int currEdge, TransformCR transform, double tol) const
    {
    /*------------------------------------------------------------------------------------------------------
    Attempt to trivial reject a dangling edge - an edge with one point laying outside of the boundary loop,
    and the other point sharing with previous end and next start points.  TFS 157668.
    ------------------------------------------------------------------------------------------------------*/
    int         numEdges = edges.length ();
    if (numEdges < 4)
        return  false;

    int         nextEdge = (currEdge + 1) < numEdges ? (currEdge + 1) : 0;
    DPoint3d    nextStart, nextEnd;

    switch (edgeTypes[nextEdge])
        {
        case AcDbHatch::kLine:
            {
            AcGeLineSeg2d*      lineSeg = (AcGeLineSeg2d*) edges[nextEdge];
            if (nullptr == lineSeg)
                return  false;
            RealDwgUtil::DPoint3dFromGePoint2d (nextStart, lineSeg->startPoint());
            RealDwgUtil::DPoint3dFromGePoint2d (nextEnd, lineSeg->endPoint());
            break;
            }
        case AcDbHatch::kCirArc:
            {
            AcGeCircArc2d*      arc = (AcGeCircArc2d*) edges[nextEdge];
            if (nullptr == arc)
                return  false;
            RealDwgUtil::DPoint3dFromGePoint2d (nextStart, arc->startPoint());
            RealDwgUtil::DPoint3dFromGePoint2d (nextEnd, arc->endPoint());
            break;
            }
        case AcDbHatch::kEllArc:
            {
            AcGeEllipArc2d*     ellipse = (AcGeEllipArc2d*) edges[nextEdge];
            if (nullptr == ellipse)
                return  false;
            RealDwgUtil::DPoint3dFromGePoint2d (nextStart, ellipse->startPoint());
            RealDwgUtil::DPoint3dFromGePoint2d (nextEnd, ellipse->endPoint());
            break;
            }
        case AcDbHatch::kSpline:
            {
            AcGeNurbCurve2d*    spline = (AcGeNurbCurve2d*) edges[nextEdge];
            if (nullptr == spline)
                return  false;
            RealDwgUtil::DPoint3dFromGePoint2d (nextStart, spline->startPoint());
            RealDwgUtil::DPoint3dFromGePoint2d (nextEnd, spline->endPoint());
            break;
            }
        default:
            return  false;  // error
        }

    transform.Multiply (nextStart);
    transform.Multiply (nextEnd);

    // check if next edge touches previous edge
    if (prevEnd.Distance(nextStart) < tol || prevEnd.Distance(nextEnd) < tol)
        {
        bool    touchedAtStart = prevEnd.Distance (start) < tol; 
        bool    touchedAtEnd = prevEnd.Distance (end) < tol;

        /*--------------------------------------------------------------------------------------------------
        If one point touches previous point but the other one does not, it is either a dangling edge or an
        edge overlapped with/repetive to next edge.  Reject either case.  Also reject a degenerated edge.
        Do not reject the case in which no points touch - valid edges can be randomly ordered in the edge
        list.
        --------------------------------------------------------------------------------------------------*/
        if (touchedAtStart != touchedAtEnd || touchedAtStart && touchedAtEnd)
            return  true;
        }
    
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetLoopTolerance (AcDbHatch* hatch, TransformCR transform) const
    {
    AcDbExtents extents;
    double      tolerance = TOLERANCE_SamePointUOR;

    if (Acad::eOk == hatch->getGeomExtents(extents))
        {
        double  diagonalSize = extents.minPoint().distanceTo (extents.maxPoint());

        // scale the diagonal length to a desired tolerance
        tolerance = 0.001 * diagonalSize;

        // the tolerance shall be in UOR's
        double  scaleToUors = 1.0;
        if (transform.isUniformScale(NULL, &scaleToUors))
            tolerance *= scaleToUors;

        // cap the tolerance to 2 UOR's
        if (tolerance > TOLERANCE_LoopClosure)
            tolerance = TOLERANCE_LoopClosure;
        }

    return  tolerance;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ElementFromLoop
(
EditElementHandleR          outElement,
AcDbHatch*                  pHatch,
int                         loopIndex,
CurveVector::BoundaryType   boundaryType,
DVec3dCP                    pHatchNormal,
TransformCR                 transform,
ConvertToDgnContextR        context
) const
    {
    CurveVectorPtr      loopCurves = CurveVector::Create (boundaryType);
    if (!loopCurves.IsValid())
        return  OutOfMemoryError;

    Adesk::Int32        loopType = pHatch->loopTypeAt (loopIndex);
    DgnModelP           model = context.GetModel();
    bool                closed = 0 == (loopType & AcDbHatch::kNotClosed);
    RealDwgStatus       status = RealDwgSuccess;

    if (0 == (loopType & AcDbHatch::kPolyline))
        {
        // the loop consists of individual segments
        AcGeVoidPointerArray        edgePtrs;
        AcGeIntArray                edgeTypes;
        bool                        gapFound = false;
        DPoint3d                    currentStart, currentEnd, firstStart, firstEnd;

        if (Acad::eOk != pHatch->getLoopAt(loopIndex, loopType, edgePtrs, edgeTypes) || edgeTypes.isEmpty())
            return EntityError;

        firstStart.initDisconnect ();
        firstEnd.initDisconnect ();

        double                      loopTolerance = this->GetLoopTolerance (pHatch, transform);
        int                         iEdge = 0;
        int                         numEdges = edgeTypes.length ();
        ICurvePrimitivePtr          childCurve;
        CurveVectorPtr unorderedChildren = CurveVector::Create (CurveVector::BOUNDARY_TYPE_None);
        for (; iEdge < numEdges; iEdge++)
            {
            void*                   pEdge = edgePtrs[iEdge];

            switch (edgeTypes[iEdge])
                {
                case AcDbHatch::kLine:
                    {
                    AcGeLineSeg2d*  pLineSeg = (AcGeLineSeg2d*) pEdge;
                    DSegment3d      lineSeg;

                    RealDwgUtil::DPoint3dFromGePoint2d (lineSeg.point[0], pLineSeg->startPoint());
                    RealDwgUtil::DPoint3dFromGePoint2d (lineSeg.point[1], pLineSeg->endPoint());
                    transform.Multiply (lineSeg.point, lineSeg.point, 2);
                    context.ValidatePoints (lineSeg.point, 2);

                    childCurve = ICurvePrimitive::CreateLine (lineSeg);
                    break;
                    }

                case AcDbHatch::kCirArc:
                    {
                    AcGeCircArc2d*  pCircArc = (AcGeCircArc2d*) pEdge;
                    DPoint2d        center;
                    double          radius = pCircArc->radius(), startAngle = pCircArc->startAng(), endAngle = pCircArc->endAng(), sweepAngle;
                    DEllipse3d      ellipse;

                    RealDwgUtil::DPoint2dFromGePoint2d (center, pCircArc->center());

                    if (pCircArc->isClockWise())
                        {
                        if ((sweepAngle = (startAngle - endAngle)) > 0.0)
                            sweepAngle -= msGeomConst_2pi;

                        startAngle = -startAngle;
                        }
                    else
                        {
                        if ((sweepAngle = (endAngle - startAngle)) < 0.0)
                            sweepAngle += msGeomConst_2pi;
                        }

                    // A small overlapping arc can cause CloneWithGapsClosed to create bad loop - letter C in TFS#16392.
                    // For a valid(i.e. no overlapping) arc, a gap shall be created and then filled by a line segment.
                    if (fabs(sweepAngle) < 0.02)
                        continue;

                    ellipse.initFromXYMajorMinor (center.x, center.y, 0.0, radius,  radius, 0.0, startAngle, sweepAngle);
                    ellipse = context.TransformDEllipse (ellipse, transform);

                    childCurve = ICurvePrimitive::CreateArc (ellipse);
                    break;
                    }

                case AcDbHatch::kEllArc:
                    {
                    AcGeEllipArc2d*  pEllipArc = (AcGeEllipArc2d*) pEdge;
                    DPoint2d         center, xVector, yVector;
                    double           startAngle = pEllipArc->startAng(), endAngle = pEllipArc->endAng(), sweepAngle, minMajRatio = 0.0;
                    DEllipse3d       ellipse;

                    RealDwgUtil::DPoint2dFromGePoint2d (center, pEllipArc->center());
                    RealDwgUtil::DPoint2dFromGeVector2d (xVector, pEllipArc->majorAxis());
                    RealDwgUtil::DPoint2dFromGeVector2d (yVector, pEllipArc->minorAxis());

                    // In 1.13 DwgDirect started normalizing the returned axis vector - the following code should work either way. (TR# 158521).
                    xVector.normalize();
                    yVector.normalize();
                    xVector.scale (&xVector, pEllipArc->majorRadius());
                    yVector.scale (&yVector, pEllipArc->minorRadius());

                    sweepAngle = endAngle - startAngle;

                    ellipse.init (center.x,  center.y,  0.0, xVector.x, xVector.y, 0.0, yVector.x, yVector.y, 0.0, startAngle, sweepAngle);
                    ellipse = context.TransformDEllipse (ellipse, transform);

                    childCurve = ICurvePrimitive::CreateArc (ellipse);
                    break;
                    }

                case AcDbHatch::kSpline:
                    {
                    AcGeNurbCurve2d*    pSpline = (AcGeNurbCurve2d*) pEdge;
                    int                 nPoles = pSpline->numControlPoints();
                    DPoint3dArray       poles;
                    DPoint3d            point;
                    for (int iPole = 0; iPole < nPoles; iPole++)
                        poles.push_back (RealDwgUtil::DPoint3dFromGePoint2d(point, pSpline->controlPointAt(iPole)));

                    bool                rational = Adesk::kTrue == pSpline->isRational();
                    DoubleArray     weights;
                    if (rational)
                        {
                        for (int iWeight = 0; iWeight < nPoles; iWeight++)
                            weights.push_back (pSpline->weightAt (iWeight));
                        }

                    int                 nKnots = pSpline->numKnots ();
                    DoubleArray         knots;
                    for (int iKnot = 0; iKnot < nKnots; iKnot++)
                        knots.push_back (pSpline->knotAt(iKnot));

                    int                 degree = pSpline->degree ();
                    MSBsplineCurve      curve;
                    if (BSISUCCESS == context.CreateBsplineCurveFromDwgSplineParams(curve, degree, nPoles, nKnots, false, rational, false, &poles.front(),
                                                        0 == nKnots ? NULL : &knots.front(), rational ? &weights.front() : NULL, transform))
                        {
                        childCurve = ICurvePrimitive::CreateBsplineCurve (curve);
                        bspcurv_freeCurve (&curve);
                        }
                    else
                        {
                        DIAGNOSTIC_PRINTF ("Failed creating spline CurveVector from a hatch loop edge!\n");
                        }
                    break;
                    }

                default:
                    DIAGNOSTIC_PRINTF ("Error: unknown hatch edge type %d.\n", edgeTypes[iEdge]);
                    break;
                }

            if (childCurve.IsValid())
                {
                DPoint3d            start, end;
                unorderedChildren->push_back (childCurve->Clone());
        
                if (childCurve->GetStartEnd(start, end))
                    {
                    if (loopCurves->empty())
                        {
                        firstStart = start;
                        firstEnd = end;
                        }
                    else if (this->IsRepetitive(loopCurves, childCurve, iEdge, loopTolerance) || 
                             this->IsDangling(currentEnd, start, end, edgePtrs, edgeTypes, iEdge, transform, loopTolerance))
                        {
                        // skip repetitive edges as our fill does not work well with these, TR 347482.
                        // skip dangling edges as CloneWithGapsClosed can't seem to handle that well.
                        continue;
                        }
                    else if (start.Distance(currentEnd) > 1.0)
                        {
                        // there is a gap, but if it is the last edge and the loop is closed, skip the bad edge - for Takenaka TFS 191926.
                        if (iEdge + 1 == numEdges && firstStart.Distance(currentEnd) < loopTolerance)
                            continue;
                        else
                            gapFound = true;
                        }
                    currentStart = start;
                    currentEnd = end;
                    }
                static double s_addOrSpliceFactor = 10.0;
                AddOrSpliceChildCurve (s_addOrSpliceFactor * loopTolerance, loopCurves, childCurve);
                }
            }

        if (loopCurves->empty())
            return  BadHatch;
#ifdef SAVE_WORKING_GEOMETRY_TO_FILE
        DRange3d range;
        unorderedChildren->GetRange (range);
        auto shiftTransform = Transform::From (2.0 * range.XLength (), 0, 0);
        {
        auto save1 = unorderedChildren->Clone();
        save1->TransformInPlace(shiftTransform);
        EditElementHandle eh;
        DraftingElementSchema::ToElement(eh, *save1, NULL, context.GetThreeD(), *model);
        eh.AddToModel();
        }
#endif
        // If simple reordering produced one loope, take it in place of whatever is has 
        // survived the more complicated splice . . .
        auto allLoops = unorderedChildren->AssembleChains ();
#ifdef SAVE_WORKING_GEOMETRY_TO_FILE
        {
        auto save1 = allLoops->Clone();
        save1->TransformInPlace(shiftTransform);
        save1->TransformInPlace(shiftTransform);
        EditElementHandle eh;
        DraftingElementSchema::ToElement(eh, *save1, NULL, context.GetThreeD(), *model);
        eh.AddToModel();
        }
#endif
        if (allLoops->size() == 1)
            {
            auto primitive = allLoops->at(0);
            auto loop = primitive->GetChildCurveVectorP ();
            static double s_closureFactor = 100.0;
            if (closeAndFixup(loop, s_closureFactor * loopTolerance))
                loopCurves = loop;
            }
        // check overlap of the first and the last segments of the entire loop
        childCurve = loopCurves->front ();
        if (iEdge > 2 && loopCurves->size() > 1 && this->IsRepetitive(loopCurves, childCurve, iEdge, loopTolerance))
            {
            // remove the last segment
            loopCurves->pop_back ();
            }

#ifdef DEBUG_RAW_BOUNDARY
        static bool s_saveToFile = false;
        if (s_saveToFile)
            {
            EditElementHandle eh;
            DraftingElementSchema::ToElement(eh, *loopCurves.get(), NULL, context.GetThreeD(), *model);
            eh.AddToModel ();
            if (gapFound)
                {
                CurveVectorPtr loop1 = loopCurves->Clone ();
                double              maxGap = loop1->ReorderForSmallGaps ();

                EditElementHandle eh;
                DraftingElementSchema::ToElement(eh, *loop1.get(), NULL, context.GetThreeD(), *model);
                eh.AddToModel ();

                CurveGapOptions     gapOptions (loopTolerance, TOLERANCE_VectorEqual, MINIMUM_GeCurve2DSweep);
                CurveVectorPtr loop2 = loop1->CloneWithGapsClosed (gapOptions);

                EditElementHandle eh2;
                DraftingElementSchema::ToElement(eh, *loop2.get(), NULL, context.GetThreeD(), *model);
                eh.AddToModel ();

                CurveVectorPtr loop3 = loopCurves->CloneWithGapsClosed (gapOptions);

                EditElementHandle eh3;
                DraftingElementSchema::ToElement(eh, *loop3.get(), NULL, context.GetThreeD(), *model);
                eh.AddToModel ();
                }
            }
#endif

        if (gapFound)
            {
            // CurveGapOptions     gapOptions (loopTolerance, TOLERANCE_VectorEqual, MINIMUM_GeCurve2DSweep);
            static double s_multiplier1 = 1000.0;
            static double s_multiplier2 = 1000.0;
            // CurveGapOptions     gapOptions (loopTolerance, s_multiplier1 * loopTolerance, s_multiplier2 * loopTolerance);
            // auto loopCurves1 = loopCurves->CloneWithGapsClosed (gapOptions);
            auto loopCurves1 = cloneWithGapsClosed (*loopCurves, 1.0, s_multiplier1, s_multiplier2);
            double              maxGap = loopCurves->ReorderForSmallGaps ();
            // loopCurves = loopCurves->CloneWithGapsClosed (gapOptions);
            loopCurves = cloneWithGapsClosed(*loopCurves, 1.0, s_multiplier1, s_multiplier2);


            double              reorderedLength = loopCurves->Length ();
            double              originalLength = loopCurves1->Length ();
            if (originalLength < reorderedLength)
                loopCurves = loopCurves1;
            }
#ifdef AlwaysConsolidate
// activate this block of code to get
//  1) compact all consecutive line/linestring geometry to single linestrings
//  2) aggressively compress within linestrings.
        loopCurves->ConsolidateAdjacentPrimitives();
        for (size_t i = 0; i < loopCurves->size(); i++)
            {
            auto cp = loopCurves->at (i);
            auto points = cp->GetLineStringP();
            if (points)
                {
                bvector<DPoint3d> pointsA, pointsB;
                PolylineOps::CompressByChordError(pointsA, *points, TOLERANCE_SamePointUOR);
                CompressSpears (TOLERANCE_SamePointUOR, pointsB, pointsA);
                points->clear ();
                for (auto &xyz : pointsB)
                    points->push_back(xyz);
                }
            }
        loopCurves->SimplifyLinestrings(TOLERANCE_SamePointUOR, true, false);
#endif
        if (NULL != pHatchNormal)
            {
            // get calculated loop normal
            DVec3d      loopNormal;
            DPoint3d    center;
            double area;
            if (loopCurves->CentroidNormalArea(center, loopNormal, area) && pHatchNormal->DotProduct(loopNormal) < 0.0)
                {
                // loop normal is opposing to the hatch normal, reverse loop direction
                loopCurves = loopCurves->CloneReversed ();
                }
            }
        }
    else
        {
        // the loop is a polyline
        size_t                      tmpPointCount;
        double                      bulge0 = 0.0, bulge1 = 0.0;
        DPoint2d                    p0, p1;
        AcGePoint2dArray            points;
        AcGeDoubleArray             bulges;
        DPoint3dArray               tmpPointArray;

        Acad::ErrorStatus           loopStatus;
        if (Acad::eOk != (loopStatus = pHatch->getLoopAt(loopIndex, loopType, points, bulges)) || 0 >= points.length())
            return  BadHatch;

        RealDwgUtil::DPoint2dFromGePoint2d (p1, points[0]);
        bulge1 = bulges.isEmpty () ? 0.0 : bulges[0];

        // Note: when the polyline is closed, RealDWG inserts a final point that is equal to the first point. That point is not stored in the file.
        UInt32 nSegments = points.length() - 1;
        for (UInt32 iSegment = 1; iSegment <= nSegments; iSegment++)
            {
            p0 = p1;
            bulge0 = bulge1;

            RealDwgUtil::DPoint2dFromGePoint2d (p1, points[iSegment]);
            bulge1 = bulges.isEmpty () ? 0.0 : bulges[iSegment];

            if (ISVALIDBULGEFACTOR(bulge0))
                {
                if ((tmpPointCount = tmpPointArray.size()) > 0)
                    {
                    transform.Multiply (&tmpPointArray[0], &tmpPointArray[0], (int)tmpPointCount);
                    // SEE TOP OF FILE NOTE (XXX)   compressDPoint3dArray(tmpPointArray);

                    ICurvePrimitivePtr  childLine = ICurvePrimitive::CreateLineString (tmpPointArray);
                    if (childLine.IsValid())
                        loopCurves->Add (childLine);

                    // break point - a new segment will start after the bulge arc
                    tmpPointArray.clear ();
                    }

                DEllipse3d      ellipse;
                RealDwgUtil::BulgeFactorToDEllipse3d (ellipse, p0, p1, bulge0);
                ellipse = context.TransformDEllipse (ellipse, transform);

                ICurvePrimitivePtr  childArc = ICurvePrimitive::CreateArc (ellipse);
                if (childArc.IsValid())
                    loopCurves->Add (childArc);
                }
            else
                {
                if (tmpPointArray.size() == 0)
                    tmpPointArray.push_back (DPoint3d::From(p0.x, p0.y));

                tmpPointArray.push_back (DPoint3d::From(p1.x, p1.y));
                }
            }

        if ((tmpPointCount = tmpPointArray.size()) > 0)
            {
            transform.Multiply (&tmpPointArray[0], &tmpPointArray[0], (int)tmpPointCount);
            // SEE TOP OF FILE NOTE (XXX)   tmpPointCount = compressDPoint3dArray (tmpPointArray);
            // Don't create degenerate loops (TR# 168301)
            size_t  kPoint = 1;
            for (; kPoint < tmpPointCount; kPoint++)
                if (tmpPointArray.at(kPoint-1).Distance(tmpPointArray.at(kPoint)) > TOLERANCE_SamePointUOR)
                    break;

            if (kPoint == tmpPointCount)
                {
                DIAGNOSTIC_PRINTF ("Ignoring degenerate loop in hatch\n");
                }
            else
                {
                ICurvePrimitivePtr  childLinestring = ICurvePrimitive::CreateLineString (tmpPointArray);
                if (childLinestring.IsValid())
                    loopCurves->Add (childLinestring);
                }
            }
        }
    //TFS#1128653:Hatch & Pattern disappeared on Save as DWG
    if (loopCurves->empty() || !loopCurves->IsAnyRegionType())
        return  BadHatch;

    // create complex shape for the loop
    if (BSISUCCESS != DraftingElementSchema::ToElement(outElement, *loopCurves.get(), NULL, context.GetThreeD(), *model))
        return  BadHatch;

    return  outElement.IsValid() ? RealDwgSuccess : BadHatch;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetFilled
(
EditElementHandleR          regionElement,
AcDbHatch*                  pHatch,
AcCmColor&                  backgroundColor,
ConvertToDgnContextR        context
) const
    {
    IAreaFillPropertiesEdit*    areaFill = dynamic_cast <IAreaFillPropertiesEdit*> (&regionElement.GetHandler(MISSING_HANDLER_PERMISSION_ChangeAttrib));
    if (NULL == areaFill)
        return;

    if (pHatch->isGradient())
        {
        GradientSymbPtr     gradSymb = GradientSymb::Create ();

        gradSymb->SetAngle (pHatch->gradientAngle());
        gradSymb->SetTint  (pHatch->getShadeTintValue());
        gradSymb->SetShift (pHatch->gradientShift());

        bool                invert = false;
        gradSymb->SetMode (this->GradientModeFromName(&invert, pHatch->gradientName()));

        UInt16              flags = 0;
        if (invert)
            flags |= static_cast<int>(GradientFlags::Invert);
        gradSymb->SetFlags (flags);

        RgbColorDef         keyColors[MAX_GRADIENT_KEYS];
        double              keyValues[MAX_GRADIENT_KEYS];
        AcCmColor*          colors = NULL;
        UInt32              count = 0;
        float*              values = NULL;
        pHatch->getGradientColors (count, colors, values);
        for (UInt32 iColor=0; iColor<count; iColor++)
            {
            RgbFactor       trueColor = context.GetRGBFactor (colors[iColor].entityColor(), 255.0);

            keyColors[iColor].red   = (unsigned int)trueColor.red;
            keyColors[iColor].green = (unsigned int)trueColor.green;
            keyColors[iColor].blue  = (unsigned int)trueColor.blue;
            keyValues[iColor]       = values[iColor];
            }

        // this used to test pHatch->getGradientOneColorMode() and set GRADIENT_FLAGS_SingleColor if it was true.
        // that's redundant with our nKeys, so I don't think it was needed. [Presumably (color.length() == 1) in that case]
        // I eliminated GRADIENT_FLAGS_SingleColor 3/7/2005. BJB
        gradSymb->SetKeys (count, keyColors, keyValues);

        areaFill->AddGradientFill (regionElement, *gradSymb);
        }
    else
        {
        UInt32              fillColor = 0, *pFillColor = NULL;
        if (!backgroundColor.isNone())
            {
            // this is a hatch with fill + pattern, add fill color now:
            fillColor = context.GetDgnColor (backgroundColor);
            pFillColor = &fillColor;
            }

        areaFill->AddSolidFill (regionElement, pFillColor);
        }
    }
};  // ToDgnExtHatch




/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtAssocRegion : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    bool    isPatterned = false, isSolidFilled = false, isGradientFilled = false, isOutlined = false;
    context.IsFilledOrPatterned (elemHandle, &isSolidFilled, &isGradientFilled, &isOutlined, &isPatterned);

    bool    isFilled = isSolidFilled || isGradientFilled;

    // determine if we need to create 2 hatches for fill + pattern:
    bool    create2Hatches = context.ShouldSeperateFillAndPattern (isSolidFilled, isGradientFilled, isPatterned);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbHatch::desc());

    RealDwgStatus   status = context.HatchFromElement (AcDbHatch::cast(acObject), elemHandle, NULL, create2Hatches ? false : isPatterned, isFilled);

    // add a pattern hatch for an assoc region that is both filled and patterned:
    if (RealDwgSuccess == status && create2Hatches)
        context.AddSingleHatchEntity (elemHandle, true, false);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ToObjectPostProcess
(
ElementHandleR              inElement,
AcDbObjectP                 acObject, 
ConvertFromDgnContextR      context
) const override
    {
    AcDbHatch*              pHatch = AcDbHatch::cast (acObject);
    if (NULL == pHatch)
        return  EntityError;

    bool                    isAssocRegion = NULL != dynamic_cast <AssocRegionCellHeaderHandler*> (&inElement.GetHandler());

    pHatch->removeAssocObjIds ();

    int                     loopIndex = 0;
    AddLoopIds (pHatch, inElement, &loopIndex, isAssocRegion, NULL, context);

    return RealDwgSuccess;
    }
};  // ToDwgExtAssocRegion


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::AddHatchEntity (AcDbEntityP boundaryEntity, ElementHandleCR elemHandle, bool addFill, bool addPattern, bool showOutline)
    {
    /*-----------------------------------------------------------------------------------------------------------------
    Add a hatch for different cases:

    1) Outlined fill: add a solid hatch prior to adding the boundary entity
    2) Patterned: add a patterned hatch prior to adding the boundary entity
    3) Outlined fill + patterned: add a solid hatch, a patterned hatch, or a composite hatch then the boundary entity
    4) Opaque fill: add the boundary entity prior to adding a solid hatch
    5) Opaque fill + patterned: add the boundary entity, a solid hatch, then a patterned hatch, or just a composite hatch.

    Opaque and gradient fills will become non-associative hatch entities whereas patterned and outlined fills will become
    associative hatches which need to have valid object ID's prior to making them associative.

    This method attempts to order both entity position in file as well as to set the priority order if it exists.  However,
    for file position to work, the boundary entity should not have been saved to database yet.  This method saves entities
    in the order it sees needed.  If the boundary entity is already a database resident, make sure priority is set prior to
    call this method.
    -----------------------------------------------------------------------------------------------------------------*/
    AcDbHatch*      newHatch1 = new AcDbHatch ();
    AcDbHatch*      newHatch2 = NULL;
    bool            isHatch1Valid = true;
    bool            create2Hatches = this->ShouldSeperateFillAndPattern(addFill, false, addPattern);

    if (showOutline)
        {
        // Add hatch then add boundary for cases 1 & 2, or add a solid hatch, a pattern then the boundary for case 3:
        if (this->AddEntityToCurrentBlock(AcDbEntity::cast(newHatch1), 0).isNull())
            isHatch1Valid = false;

        if (create2Hatches)
            {
            // case 3
            newHatch2 = new AcDbHatch ();
            if (this->AddEntityToCurrentBlock(newHatch2, 0).isNull())
                {
                delete newHatch2;
                newHatch2 = NULL;
                }
            }

        if (nullptr != boundaryEntity && !boundaryEntity->objectId().isValid() && this->AddEntityToCurrentBlock(boundaryEntity, elemHandle.GetElementId()).isNull())
            isHatch1Valid = false;
        }
    else // opaque fill
        {
        // Add the boundary then a hatch for case 4, or add the boundary, a solid hatch and a pattern for case 5:
        if (nullptr != boundaryEntity && !boundaryEntity->objectId().isValid() && this->AddEntityToCurrentBlock(boundaryEntity, elemHandle.GetElementId()).isNull())
            isHatch1Valid = false;

        if (create2Hatches)
            {
            // case 5
            newHatch2 = new AcDbHatch ();
            if (this->AddEntityToCurrentBlock(newHatch2, 0).isNull())
                {
                delete newHatch2;
                newHatch2 = NULL;
                }
            }

        if (this->AddEntityToCurrentBlock(AcDbEntity::cast(newHatch1), 0).isNull())
            isHatch1Valid = false;
        }

    if (!isHatch1Valid)
        {
        if (newHatch1->objectId().isNull())
            delete newHatch1;
        else
            newHatch1->erase ();
        return  NullObjectId;
        }
    
    // Create a fill or a pattern
    RealDwgStatus   status = this->HatchFromElement (newHatch1, elemHandle, boundaryEntity, addPattern, addFill);

    if (RealDwgSuccess != status)
        {
        if (newHatch1->objectId().isNull())
            delete newHatch1;
        else
            newHatch1->erase ();
        return  status;
        }

    newHatch1->close ();

    /*-----------------------------------------------------------------------------------
    If the priority sorter is active, set the new filled or patterned hatch:
        a) before the boundary object (i.e. first hatch then boundary) for cases 1-3, or 
        b) after the boundary object (i.e. first boundary then hatch) for cases 4-5
    -----------------------------------------------------------------------------------*/
    int             relativeOrder = (showOutline || !addFill) ? -2 : 1;
    PrioritySorter* prioritySorter = this->GetPrioritySorter ();
    if (NULL != prioritySorter)
        prioritySorter->AddElement (elemHandle, relativeOrder, this->ElementIdFromObject(newHatch1));

    // Add a patterned hatch for cases 3 & 5:
    if (create2Hatches)
        {
        if (NULL == newHatch2)
            return  NullObject;

        status = this->HatchFromElement (newHatch2, elemHandle, boundaryEntity, addPattern, false);

        if (RealDwgSuccess == status)
            {
            newHatch2->close ();
            if (NULL != prioritySorter)
                {
                /*------------------------------------------------------------------------------------------------
                Set the patterned hatch: 
                    a) before the boundary but after the fill hatch (fill, pattern, then boundary), for case 3, or
                    b) the last entity (boundary, fill, then pattern), for case 5
                ------------------------------------------------------------------------------------------------*/
                relativeOrder = showOutline ? -1 : 2;
                prioritySorter->AddElement (elemHandle, relativeOrder, this->ElementIdFromObject(newHatch2));
                }
            }
        else if (newHatch2->objectId().isNull())
            {
            delete newHatch2;
            }
        else
            {
            newHatch2->erase ();
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::AddSingleHatchEntity (ElementHandleCR inElement, bool patterned, bool filled)
    {
    AcDbHatch*      newHatch = new AcDbHatch ();
    AcDbObjectId    newId = this->AddEntityToCurrentBlock (AcDbEntity::cast(newHatch), 0);

    if (newId.isNull())
        {
        delete newHatch;
        return  NullObjectId;
        }

    RealDwgStatus   status = this->HatchFromElement (newHatch, inElement, NULL, patterned, filled);

    if (RealDwgSuccess == status)
        {
        newHatch->close ();

        /*----------------------------------------------------------------------------------------------
        In case of a sort ents table will be added into the owner block, we want to ensure this newly
        created hatch entity to be added into our priority list, as otherwise it will be left out of the
        sortents table, resulting in an incorrect display order as a case shown in TFS#14945.  The filled
        hatch entity created prior to the pattern entity will be added to the priority list with default
        secondary priority of 0 later on, hence we set the 2nd priority to 1 for this one.
        ----------------------------------------------------------------------------------------------*/
        PrioritySorter*     prioritySorter = this->GetPrioritySorter ();
        if (NULL != prioritySorter)
            prioritySorter->AddElement (inElement, 1, this->ElementIdFromObjectId(newId));
        }
    else
        {
        newHatch->erase ();
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/16
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::ShouldDropPattern (ElementHandleCR checkElement)
    {
    if (!m_dropUnsupportedPatterns)
        return  false;

    IAreaFillPropertiesQuery*   areaQuery = dynamic_cast<IAreaFillPropertiesQuery*>(&checkElement.GetHandler());
    if (nullptr == areaQuery)
        return  false;

    PatternParamsPtr            patternParams;
    if (!areaQuery->GetPattern(checkElement, patternParams, nullptr, nullptr, 0) || !patternParams.IsValid())
        return  false;

    // check effective linestyle used on the element - drop pattern if a non-solid linestyle is used:
    Int32       lstyle = patternParams->GetStyle ();
    if (0 != lstyle && STYLE_Invalid != lstyle)
        {
        if (IS_LINECODE(lstyle))
            return  true;

        ElementRefP     elemRef = checkElement.GetElementRef ();
        if (lstyle == STYLE_ByCell && nullptr != elemRef)
            {
            ElementRefP parentRef = elemRef->GetParentElementRef ();
            if (nullptr != parentRef && parentRef->GetElementType() == SHARED_CELL_ELM)
                {
                MSElementCP elm = parentRef->GetUnstableMSElementCP ();
                if (nullptr != elm)
                    {
                    lstyle = elm->hdr.dhdr.symb.style;
                    if (IS_LINECODE(lstyle) && 0 != lstyle && STYLE_Invalid != lstyle)
                        return  true;
                    }
                }
            }

        if (lstyle == STYLE_ByLevel)
            {
            LevelHandle     level = m_model->GetLevelCache().GetLevel (checkElement.GetElementCP()->ehdr.level, false);
            if (level.IsValid())
                {
                lstyle = level.GetByLevelLineStyle().GetStyle ();
            
                if (IS_LINECODE(lstyle) && 0 != lstyle && STYLE_Invalid != lstyle)
                    return  true;
                }
            }

        WString     styleName = LineStyleManager::GetNameFromNumber (lstyle, checkElement.GetDgnFileP());
        if (!styleName.empty() && !styleName.EqualsI(StringConstants::ContinousLinetypeName))
            {
            // treat missing linestyle definition as a solid style - TFS 983063
            if (LineStyleManager::GetStyleIDForDesignFile(styleName.c_str(), *checkElement.GetDgnFileP(), false, false) != 0)
                return  true;
            }
        }

    return  false;
    }
