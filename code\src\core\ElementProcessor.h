#pragma once

#include "../../include/ExportTypes.h"
#include "GeometryProcessor.h"
#include <memory>
#include <functional>
#include <queue>

namespace IModelExport {

// Forward declarations
class ExportContext;
class IModelDb;

//=======================================================================================
// Element Processing Pipeline
//=======================================================================================

class ElementProcessor {
public:
    ElementProcessor(std::shared_ptr<ExportContext> context);
    ~ElementProcessor();

    //===================================================================================
    // Processing Pipeline Configuration
    //===================================================================================

    // Processing stages
    enum class ProcessingStage {
        PreProcess,     // Initial validation and preparation
        Extract,        // Extract geometry and properties from iModel
        Transform,      // Apply coordinate transformations
        Convert,        // Convert to target format
        Validate,       // Validate converted data
        PostProcess,    // Final cleanup and optimization
        Complete        // Processing complete
    };

    // Processing filters
    using ElementFilter = std::function<bool(const ElementInfo&)>;
    using GeometryFilter = std::function<bool(const GeometryData&)>;
    using PropertyFilter = std::function<bool(const std::string&, const std::string&)>;

    // Set processing filters
    void SetElementFilter(ElementFilter filter);
    void SetGeometryFilter(GeometryFilter filter);
    void SetPropertyFilter(PropertyFilter filter);

    // Processing callbacks
    using StageCallback = std::function<bool(ProcessingStage, const ElementInfo&)>;
    using ProgressCallback = std::function<bool(double, const std::string&)>;
    using ErrorCallback = std::function<void(const std::string&, const std::string&)>;

    void SetStageCallback(StageCallback callback);
    void SetProgressCallback(ProgressCallback callback);
    void SetErrorCallback(ErrorCallback callback);

    //===================================================================================
    // Main Processing Methods
    //===================================================================================

    // Process all elements in iModel
    bool ProcessAllElements(const IModelDb& imodel);
    
    // Process specific elements
    bool ProcessElements(const std::vector<ElementInfo>& elements);
    
    // Process single element
    bool ProcessElement(const ElementInfo& element);
    
    // Process elements in batches
    bool ProcessElementsBatch(const std::vector<ElementInfo>& elements, size_t batchSize = 100);

    //===================================================================================
    // Element Analysis and Classification
    //===================================================================================

    // Analyze element and determine processing strategy
    struct ElementAnalysis {
        ElementType type;
        GeometryData::Type geometryType;
        size_t complexityScore;
        std::vector<std::string> dependencies;
        std::vector<std::string> requiredProcessors;
        bool canProcessInParallel;
        double estimatedProcessingTime;
    };

    ElementAnalysis AnalyzeElement(const ElementInfo& element) const;
    
    // Classify elements by type and complexity
    std::unordered_map<ElementType, std::vector<ElementInfo>> ClassifyElements(
        const std::vector<ElementInfo>& elements) const;
    
    // Build element dependency graph
    std::unordered_map<std::string, std::vector<std::string>> BuildDependencyGraph(
        const std::vector<ElementInfo>& elements) const;
    
    // Determine optimal processing order
    std::vector<ElementInfo> OptimizeProcessingOrder(const std::vector<ElementInfo>& elements) const;

    //===================================================================================
    // Geometry Extraction and Processing
    //===================================================================================

    // Extract geometry from element
    std::vector<GeometryData> ExtractGeometry(const ElementInfo& element) const;
    
    // Process geometry for target format
    std::vector<GeometryData> ProcessGeometry(const std::vector<GeometryData>& geometry, 
                                             ExportFormat targetFormat) const;
    
    // Validate geometry
    bool ValidateGeometry(const std::vector<GeometryData>& geometry, 
                         std::vector<std::string>& errors) const;
    
    // Optimize geometry for export
    std::vector<GeometryData> OptimizeGeometry(const std::vector<GeometryData>& geometry, 
                                              ExportFormat targetFormat) const;

    //===================================================================================
    // Property Extraction and Processing
    //===================================================================================

    // Extract properties from element
    std::unordered_map<std::string, std::string> ExtractProperties(const ElementInfo& element) const;
    
    // Process properties for target format
    std::unordered_map<std::string, std::string> ProcessProperties(
        const std::unordered_map<std::string, std::string>& properties, 
        ExportFormat targetFormat) const;
    
    // Validate properties
    bool ValidateProperties(const std::unordered_map<std::string, std::string>& properties,
                           std::vector<std::string>& errors) const;

    //===================================================================================
    // Material and Appearance Processing
    //===================================================================================

    // Extract material information
    std::vector<Material> ExtractMaterials(const ElementInfo& element) const;
    
    // Process materials for target format
    std::vector<Material> ProcessMaterials(const std::vector<Material>& materials,
                                          ExportFormat targetFormat) const;
    
    // Assign materials to geometry
    bool AssignMaterialsToGeometry(std::vector<GeometryData>& geometry,
                                  const std::vector<Material>& materials) const;

    //===================================================================================
    // Coordinate System and Units Processing
    //===================================================================================

    // Transform element coordinates
    bool TransformElementCoordinates(ElementInfo& element) const;
    
    // Convert element units
    bool ConvertElementUnits(ElementInfo& element, double scaleFactor) const;
    
    // Validate coordinate system
    bool ValidateCoordinateSystem(const ElementInfo& element, 
                                 std::vector<std::string>& warnings) const;

    //===================================================================================
    // Parallel Processing Support
    //===================================================================================

    // Process elements in parallel
    bool ProcessElementsParallel(const std::vector<ElementInfo>& elements, 
                                size_t numThreads = 0);
    
    // Thread-safe element processing
    bool ProcessElementThreadSafe(const ElementInfo& element);
    
    // Synchronize parallel processing results
    bool SynchronizeResults();

    //===================================================================================
    // Error Handling and Recovery
    //===================================================================================

    // Error recovery strategies
    enum class RecoveryStrategy {
        Skip,           // Skip problematic element
        Retry,          // Retry processing with different parameters
        Simplify,       // Simplify geometry and retry
        Fallback,       // Use fallback processing method
        Abort           // Abort entire processing
    };

    // Handle processing error
    bool HandleProcessingError(const ElementInfo& element, const std::string& error,
                              RecoveryStrategy strategy = RecoveryStrategy::Skip);
    
    // Validate processing results
    bool ValidateProcessingResults(const std::vector<ElementInfo>& processedElements,
                                  std::vector<std::string>& errors) const;
    
    // Generate processing report
    struct ProcessingReport {
        size_t totalElements;
        size_t processedElements;
        size_t skippedElements;
        size_t errorElements;
        double totalProcessingTime;
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
        std::unordered_map<ElementType, size_t> elementTypeCounts;
    };

    ProcessingReport GenerateProcessingReport() const;

    //===================================================================================
    // Processing Statistics and Monitoring
    //===================================================================================

    // Get processing statistics
    struct ProcessingStats {
        size_t elementsProcessed;
        size_t elementsSkipped;
        size_t elementsWithErrors;
        double averageProcessingTime;
        double totalProcessingTime;
        size_t memoryUsage;
        std::chrono::system_clock::time_point startTime;
        std::chrono::system_clock::time_point endTime;
    };

    ProcessingStats GetProcessingStatistics() const;
    
    // Monitor processing progress
    double GetProcessingProgress() const;
    
    // Check if processing is cancelled
    bool IsProcessingCancelled() const;
    
    // Cancel processing
    void CancelProcessing();

private:
    //===================================================================================
    // Internal Data Structures
    //===================================================================================

    struct ProcessingTask {
        ElementInfo element;
        ProcessingStage stage;
        std::chrono::system_clock::time_point startTime;
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
        bool completed;
    };

    std::shared_ptr<ExportContext> m_context;
    std::shared_ptr<GeometryProcessor> m_geometryProcessor;
    
    // Processing configuration
    ElementFilter m_elementFilter;
    GeometryFilter m_geometryFilter;
    PropertyFilter m_propertyFilter;
    
    // Callbacks
    StageCallback m_stageCallback;
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;
    
    // Processing state
    std::queue<ProcessingTask> m_processingQueue;
    std::vector<ProcessingTask> m_completedTasks;
    std::atomic<bool> m_processingCancelled{false};
    std::atomic<size_t> m_elementsProcessed{0};
    std::atomic<size_t> m_elementsSkipped{0};
    std::atomic<size_t> m_elementsWithErrors{0};
    
    // Threading
    mutable std::mutex m_processingMutex;
    std::vector<std::thread> m_workerThreads;

    //===================================================================================
    // Internal Processing Methods
    //===================================================================================

    // Process element through all stages
    bool ProcessElementInternal(const ElementInfo& element);
    
    // Execute specific processing stage
    bool ExecuteProcessingStage(ProcessingTask& task);
    
    // Pre-process element
    bool PreProcessElement(ProcessingTask& task);
    
    // Extract element data
    bool ExtractElementData(ProcessingTask& task);
    
    // Transform element data
    bool TransformElementData(ProcessingTask& task);
    
    // Convert element data
    bool ConvertElementData(ProcessingTask& task);
    
    // Validate element data
    bool ValidateElementData(ProcessingTask& task);
    
    // Post-process element
    bool PostProcessElement(ProcessingTask& task);

    //===================================================================================
    // Element Type Specific Processors
    //===================================================================================

    // Process geometric elements
    bool ProcessGeometricElement(ProcessingTask& task);
    
    // Process spatial elements
    bool ProcessSpatialElement(ProcessingTask& task);
    
    // Process physical elements
    bool ProcessPhysicalElement(ProcessingTask& task);
    
    // Process functional elements
    bool ProcessFunctionalElement(ProcessingTask& task);
    
    // Process information elements
    bool ProcessInformationElement(ProcessingTask& task);
    
    // Process definition elements
    bool ProcessDefinitionElement(ProcessingTask& task);

    //===================================================================================
    // Geometry Type Specific Processors
    //===================================================================================

    // Process point geometry
    bool ProcessPointGeometry(const GeometryData& geometry, ProcessingTask& task);
    
    // Process line geometry
    bool ProcessLineGeometry(const GeometryData& geometry, ProcessingTask& task);
    
    // Process curve geometry
    bool ProcessCurveGeometry(const GeometryData& geometry, ProcessingTask& task);
    
    // Process surface geometry
    bool ProcessSurfaceGeometry(const GeometryData& geometry, ProcessingTask& task);
    
    // Process solid geometry
    bool ProcessSolidGeometry(const GeometryData& geometry, ProcessingTask& task);
    
    // Process mesh geometry
    bool ProcessMeshGeometry(const GeometryData& geometry, ProcessingTask& task);

    //===================================================================================
    // Utility Methods
    //===================================================================================

    // Update processing progress
    bool UpdateProgress(double percentage, const std::string& operation);
    
    // Log processing error
    void LogProcessingError(const std::string& elementId, const std::string& error);
    
    // Log processing warning
    void LogProcessingWarning(const std::string& elementId, const std::string& warning);
    
    // Calculate element complexity score
    size_t CalculateComplexityScore(const ElementInfo& element) const;
    
    // Estimate processing time
    double EstimateProcessingTime(const ElementInfo& element) const;
    
    // Check processing dependencies
    bool CheckProcessingDependencies(const ElementInfo& element) const;
    
    // Worker thread function
    void WorkerThreadFunction();
    
    // Cleanup processing resources
    void CleanupProcessingResources();
};

//=======================================================================================
// Element Processing Utilities
//=======================================================================================

namespace ElementProcessingUtils {
    
    // Create default element filters
    ElementProcessor::ElementFilter CreateElementTypeFilter(const std::vector<ElementType>& types);
    ElementProcessor::ElementFilter CreateBoundingBoxFilter(const BoundingBox& bounds);
    ElementProcessor::ElementFilter CreatePropertyFilter(const std::string& propertyName, 
                                                         const std::string& propertyValue);
    
    // Create default geometry filters
    ElementProcessor::GeometryFilter CreateGeometryTypeFilter(const std::vector<GeometryData::Type>& types);
    ElementProcessor::GeometryFilter CreateComplexityFilter(size_t maxComplexity);
    ElementProcessor::GeometryFilter CreateSizeFilter(double minSize, double maxSize);
    
    // Processing optimization
    std::vector<ElementInfo> SortElementsByComplexity(const std::vector<ElementInfo>& elements);
    std::vector<ElementInfo> GroupElementsByType(const std::vector<ElementInfo>& elements);
    size_t CalculateOptimalBatchSize(const std::vector<ElementInfo>& elements, size_t availableMemory);
    
    // Error analysis
    std::unordered_map<std::string, size_t> AnalyzeProcessingErrors(
        const std::vector<std::string>& errors);
    std::vector<std::string> SuggestErrorResolutions(const std::string& error);
    
    // Performance monitoring
    double CalculateProcessingThroughput(size_t elementsProcessed, double timeElapsed);
    size_t EstimateMemoryUsage(const std::vector<ElementInfo>& elements);
    double PredictRemainingTime(size_t elementsRemaining, double currentThroughput);
}

} // namespace IModelExport
