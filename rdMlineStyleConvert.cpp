/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdMlineStyleConvert.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    <PERSON>      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveDwgMultilineStylesToDgn ()
    {
    AcDbObjectId            mlStyleDictionaryId;

    if ((mlStyleDictionaryId = m_pFileHolder->GetDatabase()->mLStyleDictionaryId ()).isNull())
        return  CantCreateMlineStyle;

    AcDbDictionaryPointer   mlStyleDictionary (mlStyleDictionaryId, AcDb::kForRead);
    if (Acad::eOk != mlStyleDictionary.openStatus())
        return  CantCreateMlineStyle;

    MSElementDescrP         tableElmdscr = NULL;
    ElementId               activeStyleId = this->ElementIdFromObjectId (m_pFileHolder->GetDatabase()->cmlstyleID());
    if (SUCCESS != DgnTableUtilities::CreateTable (&tableElmdscr, MS_MLINESTYLE_TABLE_LEVEL, sizeof (MlineStyleTableElm), this->ElementIdFromObjectId (mlStyleDictionaryId)))
        return  CantCreateMlineStyle;

    tableElmdscr->el.ehdr.uniqueId = this->ElementIdFromObjectId (mlStyleDictionaryId);

    MSElementDescrP         childrenElmdscr = NULL;
    AcDbDictionaryIterator* iterator = mlStyleDictionary->newIterator();
    for ( ; !iterator->done(); iterator->next())
        {
        EditElementHandle               mlStyleElement;
        AcDbMlineStylePointer           mlStyleObject(iterator->objectId(), AcDb::kForRead);

        if (Acad::eOk == mlStyleObject.openStatus() && RealDwgSuccess == this->SaveDwgMultilineStyleToElement(mlStyleElement, mlStyleObject))
            {
            MSElementDescrP             entryElmdscr  = mlStyleElement.ExtractElementDescr ();
            if (NULL == childrenElmdscr)
                childrenElmdscr = entryElmdscr;
            else
                childrenElmdscr->AddToChain (entryElmdscr);
            if (entryElmdscr->el.ehdr.uniqueId == activeStyleId)
                {
                WChar     activeStyleName[1024];
                // add active style name on mline style table element:
                if (BSISUCCESS == LinkageUtil::ExtractNamedStringLinkageByIndex(activeStyleName, 1024, STRING_LINKAGE_KEY_Name, 0, &entryElmdscr->el))
                    LinkageUtil::AppendStringLinkageUsingDescr (&tableElmdscr, STRING_LINKAGE_KEY_Name, activeStyleName);
                // create a new active mline style element:
                MSElementDescrP activeElmdscr = NULL;
                if (BSISUCCESS != entryElmdscr->Duplicate(&activeElmdscr))
                    continue;

                // set the new element to be the active style
                activeElmdscr->el.ehdr.uniqueId = 0;
                ((MlineStyleEntryElm*)&activeElmdscr->el)->entryFlags.isDefault = true;
                // Vancouver seems to want the style name on the active element - don't delete style name.
                //mdlLinkage_deleteStringLinkage (&activeElmdscr->el, STRING_LINKAGE_KEY_Name, 0);
                // insert it as the first entry in mline style table, and append the rest entries after it:
                activeElmdscr->AddToChain (childrenElmdscr);
                childrenElmdscr = activeElmdscr;
                }
            }
        }
    delete iterator;

    if (NULL != childrenElmdscr)
        {
        tableElmdscr->AppendDescr (childrenElmdscr);
        this->LoadElementIntoCache (tableElmdscr);
        }

    m_pFileHolder->GetMultilineStyleIndex()->SetOriginatedInDwgIds (tableElmdscr);
    tableElmdscr->Release ();

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateDgnMultilineProfiles
(
int*                        pNProfiles,
DgnPlatform::MlineProfile*  pProfiles,
AcDbMlineStyle*             pStyle
)
    {
    *pNProfiles = pStyle->numElements();

    if (*pNProfiles < 1)
        return MlineProfileCountError;

    if (*pNProfiles > MULTILINE_MAX)
        *pNProfiles = MULTILINE_MAX;

    for (int iProfile = 0; iProfile < *pNProfiles; iProfile++, pProfiles++)
        {
        double              profileDistance;

        memset (pProfiles, 0, sizeof(*pProfiles));

        AcCmColor           color;
        AcDbObjectId        linetypeId;
        pStyle->getElementAt (iProfile, profileDistance, color, linetypeId);

        // set line distance from workline
        pProfiles->dist = this->GetScaleToDGN() * profileDistance;

        pProfiles->symb.color = this->GetDgnColor (color.colorIndex());
        pProfiles->symb.weight = 0;
        pProfiles->symb.style  = this->GetDgnStyle (linetypeId);
        if (0 != pProfiles->symb.style)
            pProfiles->symb.customStyle = true;

        pProfiles->symb.useWeight = false;
        pProfiles->symb.useStyle = pProfiles->symb.useColor = true;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveDwgMultilineStyleToElement (EditElementHandleR mlineStyleElement, AcDbMlineStyle* dwgMlinestyle)
    {
    int                 nProfiles;
    MlineProfile        profiles[MULTILINE_MAX];
    RealDwgStatus       rdwgStatus = this->CreateDgnMultilineProfiles (&nProfiles, profiles, dwgMlinestyle);
    if (RealDwgSuccess != rdwgStatus)
        return rdwgStatus;

    MultilineProfilePtr mlineProfile = MultilineProfile::Create ();
    MultilineStylePtr   dgnMlinestyle = MultilineStyle::Create (dwgMlinestyle->name(), *this->GetFile());

    for (int index = 0; index < nProfiles; index++)
        {
        mlineProfile->SetProfile (profiles[index]);
        dgnMlinestyle->InsertProfile (*mlineProfile, index);
        }

    dgnMlinestyle->SetOriginAngle (dwgMlinestyle->startAngle());
    dgnMlinestyle->SetEndAngle (dwgMlinestyle->endAngle());
    dgnMlinestyle->SetFilled (dwgMlinestyle->filled());
    dgnMlinestyle->SetFillColor (this->GetDgnColor(dwgMlinestyle->fillColor().colorIndex()));

    // each of MultilineStyle::GetXxxCap creates a new MultilineSymbology which initializes data with method Clear.
    MultilineSymbologyPtr   mlineSymbology = dgnMlinestyle->GetMidCap ();

    mlineSymbology->SetCapColorFromSegment (true);
    mlineSymbology->SetCapLine (dwgMlinestyle->showMiters());
    dgnMlinestyle->SetMidCap (*mlineSymbology);

    mlineSymbology = dgnMlinestyle->GetOrgCap ();
    mlineSymbology->SetCapColorFromSegment (true);
    mlineSymbology->SetCapLine (dwgMlinestyle->startSquareCap());
    mlineSymbology->SetCapInnerArc (dwgMlinestyle->startInnerArcs());
    mlineSymbology->SetCapOuterArc (dwgMlinestyle->startRoundCap());
    dgnMlinestyle->SetOrgCap (*mlineSymbology);

    mlineSymbology = dgnMlinestyle->GetEndCap ();
    mlineSymbology->SetCapColorFromSegment (true);
    mlineSymbology->SetCapLine (dwgMlinestyle->endSquareCap());
    mlineSymbology->SetCapInnerArc (dwgMlinestyle->endInnerArcs());
    mlineSymbology->SetCapOuterArc (dwgMlinestyle->endRoundCap());
    dgnMlinestyle->SetEndCap (*mlineSymbology);

    EditElementHandle tmp;
    dgnMlinestyle->ToElement(tmp);
    mlineStyleElement.Duplicate (tmp);
    MSElementP          element = mlineStyleElement.GetElementP ();
    if (NULL != element)
        {
        element->ehdr.uniqueId = this->ElementIdFromObject (dwgMlinestyle);
        return RealDwgSuccess;
        }

    return  BadMlineStyle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ValidateMlineStyleName
(
Bentley::WString&           name,
const AcDbDictionary*       pStyleDictionary
)
    {
    size_t      nChars = name.length ();
    WCharP    nameChars = (WCharP) _alloca ((nChars+50) * sizeof(WChar));
    WCharCP   suggestedName = nChars > 0 ? name.c_str() : NULL;

    wcscpy (nameChars, name.c_str());

    // DWG allows max 32 chars in a mlinestyle name
    if (nChars > MAX_DWGMLineStyleNameLength)
        nameChars[MAX_DWGMLineStyleNameLength] = 0;

    // Validate table name
    this->ValidateName (name);

    // ACAD allows only upper case of mline style name.
    wcsupr (nameChars);

    if (NULL == pStyleDictionary)
        {
        name.assign (nameChars);
        return;
        }

    // Deduplicate style name
    WChar     suffixChars[256] = L"";
    WChar     nameBase[1024];
    wcscpy (nameBase, nameChars);

    size_t      nameBaseLen = wcslen (nameBase);

    for (int nameIndex = 1; pStyleDictionary->has(nameChars); nameIndex++)
        {
        swprintf (suffixChars, L"%d", nameIndex);

        // ensure final style name < 32 chars
        size_t  nSuffixChars = wcslen(suffixChars) + 1;

        if (nameBaseLen + nSuffixChars >= MAX_DWGMLineStyleNameLength)
            {
            nameBase[MAX_DWGMLineStyleNameLength - nSuffixChars - 1] = 0;
            nameBaseLen = wcslen (nameBase);
            }

        swprintf (nameChars, (NULL == suggestedName) ? L"%ls_%ls" : L"%ls%ls", nameBase, suffixChars);
        }

    name.assign (nameChars);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsProfile1BeforeProfile2 (MlineProfile const& profile1, MlineProfile const& profile2)
    {
    // true to keep the order as is, false to swap the two entries - matters when overlapping as in round tripping the BUDWISER benchmark.
    return profile1.dist >= profile2.dist;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveDgnMultilineStyleToDatabase (MultilineStylePtr dgnMlinestyle, AcDbDictionary* mlinestyleDictionary)
    {
    WString                 styleName = dgnMlinestyle->GetName ();
    if (styleName.empty())
        return  RealDwgIgnoreElement;

    // get tcb data
    TcbCP                   pTcb = this->GetFile()->GetPersistentTcb ();
    if (NULL == pTcb)
        BeAssert (false && L"Missing TCB in active file!");

    AcDbMlineStylePointer   dwgMlinestyle;
    ElementId               dgnStyleId = dgnMlinestyle->GetID ();
    AcDbObjectId            existingObjectId = this->ExistingObjectIdFromElementId (dgnStyleId);
    bool                    isNewObject = existingObjectId.isNull ();

    if (!isNewObject)
        {
        // try to open existing mline style object:
        dwgMlinestyle.open (existingObjectId, AcDb::kForWrite);
        if (Acad::eOk != dwgMlinestyle.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Failed opening existing mline style <%ls>. [%ls]\n", styleName.c_str(), acadErrorStatusText(dwgMlinestyle.openStatus()));
            // if existing ID happens to be used by a different object, go on by creating the style with a new ID:
            if (Acad::eNotThatKindOfClass == dwgMlinestyle.openStatus())
                isNewObject = true;
            else
                return CantOpenObject;
            }
        }

    WString                 currentStyleName;
    if (isNewObject)
        {
        dwgMlinestyle.create ();
        dwgMlinestyle->initMlineStyle();
        currentStyleName.clear ();
        }
    else
        {
        currentStyleName.assign (dwgMlinestyle->name());
        }

    /*---------------------------------------------------------------------------------------------------------
    Validate style name if necessary. It is tricky to validate mline style names due to (a)an mline style has
    a name limit of 32 characters and (2)an mline style name string can be out of synch with its dictionary key.
    We have to be careful about handling both newly created styles and existing modified ones.  At the time of 
    creating a new style we want to have a unique name prior to adding the style to database; yet here we also 
    have to handle a potential name change from editing an existing style.

    TR 133933 shows just such a case where truncated names may not be unique - this causes terrible problems 
    with mline dictionary.
    
    Our strategy is to separately validate these two types of styles: newly created styles are validated at the
    time of their creation, and modified styles whose ID's are saved in an exported ID table get handled here.
    ---------------------------------------------------------------------------------------------------------*/
    Acad::ErrorStatus   acStatus = Acad::eOk;
    if (!currentStyleName.empty() && 0 != currentStyleName.compare(styleName))
        {
        this->ValidateMlineStyleName (styleName, mlinestyleDictionary);

        // rename dictionary key
        if (!mlinestyleDictionary->setName(currentStyleName.c_str(), styleName.c_str()))
            DIAGNOSTIC_PRINTF ("Error renaming mline style dictionary from <%ls> to <%ls>!\n", currentStyleName.c_str(), styleName.c_str());
        // replace mline style name string
        acStatus = dwgMlinestyle->setName (styleName.c_str());
        }
    else if (isNewObject)
        {
        this->ValidateMlineStyleName (styleName, mlinestyleDictionary);

        // add dictionary with a new key, followed by setting new name string in mline style:
        acStatus = Acad::eCreateFailed;
        if (!this->AddObjectToDictionary(mlinestyleDictionary, dwgMlinestyle, styleName.c_str(), dgnStyleId).isNull())
            acStatus = dwgMlinestyle->setName (styleName.c_str());
        }

    if (Acad::eOk != acStatus)
        {
        DIAGNOSTIC_PRINTF ("Error setting mline style name <%ls>! [%ls]\n", styleName.c_str(), acadErrorStatusText(acStatus));
        return  InvalidName;
        }

    dwgMlinestyle->setStartAngle (Angle::AdjustToSweep(dgnMlinestyle->GetOriginAngle(), 0.0, Angle::TwoPi()));
    dwgMlinestyle->setEndAngle(Angle::AdjustToSweep(dgnMlinestyle->GetEndAngle(), 0.0, Angle::TwoPi()));

    dwgMlinestyle->setFilled (dgnMlinestyle->GetFilled());
    dwgMlinestyle->setFillColor (this->GetColorFromDgn(dgnMlinestyle->GetFillColor(), dwgMlinestyle->fillColor().colorIndex()));

    MultilineSymbologyPtr   mlineSymbology = dgnMlinestyle->GetOrgCap ();
    dwgMlinestyle->setStartSquareCap (mlineSymbology->UseCapLine());
    dwgMlinestyle->setStartRoundCap (mlineSymbology->UseCapOuterArc());
    dwgMlinestyle->setStartInnerArcs (mlineSymbology->UseCapInnerArc());

    mlineSymbology = dgnMlinestyle->GetEndCap ();
    dwgMlinestyle->setEndSquareCap (mlineSymbology->UseCapLine());
    dwgMlinestyle->setEndRoundCap (mlineSymbology->UseCapOuterArc());
    dwgMlinestyle->setEndInnerArcs (mlineSymbology->UseCapInnerArc());

    mlineSymbology = dgnMlinestyle->GetMidCap ();
    dwgMlinestyle->setShowMiters (mlineSymbology->UseCapLine());

    int                     nProfiles = dgnMlinestyle->GetProfileLineCount ();
    bvector<MlineProfile>   profiles;

    for (int i = 0; i < nProfiles; i++)
        {
        MultilineProfilePtr     mlineProfile = dgnMlinestyle->GetProfile (i);
        profiles.push_back (mlineProfile->GetProfileCR());
        }

    // Sort by profile distance.
    std::sort (profiles.begin(), profiles.end(), IsProfile1BeforeProfile2);

    while (dwgMlinestyle->numElements() > nProfiles)
        {
        acStatus = dwgMlinestyle->removeElementAt (dwgMlinestyle->numElements() - 1);
        if (Acad::eOk != acStatus)
            {
            DIAGNOSTIC_PRINTF ("Failed removing a profile in mline style %ls.  [%ls]\n", styleName.c_str(), acadErrorStatusText(acStatus));
            break;
            }
        }

    int             count = 0;
    for each (MlineProfile profile in profiles)
        {
        double      distance = profile.dist * this->GetScaleFromDGN();

        // Make "active" symbology explicit because DWG does not have such a cancept.
        // This will help to reduce duplicated mline styles from mlines.
        if (!profile.symb.useColor)
            profile.symb.color = this->GetActiveDgnColor ();
        if (!profile.symb.useWeight)
            profile.symb.weight = this->GetActiveDgnWeight ();
        if (!profile.symb.useStyle)
            profile.symb.style = this->GetActiveDgnLinestyle ();

        if (count < dwgMlinestyle->numElements())
            {
            double          currDistance;
            AcCmColor       currColor;
            AcDbObjectId    currLineType;

            dwgMlinestyle->getElementAt (count, currDistance, currColor, currLineType);
            dwgMlinestyle->setElement (count, distance, this->GetColorFromDgn(profile.symb.color, currColor.colorIndex()), this->GetLineTypeFromDgn(profile.symb.style));
            }
        else
            {
            int             newIndex;
            dwgMlinestyle->addElement (newIndex, distance, this->GetColorFromDgn(profile.symb.color, -1), this->GetLineTypeFromDgn(profile.symb.style), false);
            }

        if (++count > nProfiles)
            break;
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SaveDgnMultilineStylesToDatabase ()
    {
    MultilineStyleCollection    mlstyleCollection (*m_dgnFile);
    SignedTableIndex*           tableIndex = m_pFileHolder->GetMultilineStyleIndex ();
    ElementIdArrayR             originatedInDwgList = tableIndex->GetOriginatedInDwgList ();

    // if there is any DGN entry element deleted, delete it from dwg database:
    if (this->SavingChanges() && !originatedInDwgList.empty())
        {
        AvlTreeP        currentIdTree = mdlAvlTree_init (AVLKEY_UINT64);

        // save currently existing ID's in an AVL tree:
        for each (MultilineStylePtr mlineStyle in mlstyleCollection)
            {
            ElementId   mlineStyleId = mlineStyle->GetID();
            mdlAvlTree_insertNode (currentIdTree, (void*)&mlineStyleId, sizeof(ElementId));
            }

        // now go through the "originatedInDwgIds" (those that were created from DWG elements when we started) and delete those that are no longer in the "current" list.
        this->DeleteSymbolTableRecords (tableIndex, currentIdTree);
        }

    AcDbDictionaryPointer   mlinestyleDictionary (this->GetDatabase()->mLStyleDictionaryId(), AcDb::kForWrite);
    if (Acad::eOk != mlinestyleDictionary.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Failed opening mline style table dictionary. [%ls]\n", acadErrorStatusText(mlinestyleDictionary.openStatus()));
        return  CantOpenObject;
        }

    // save undeleted mline style table entries:
    for each (MultilineStylePtr dgnMlinestyle in mlstyleCollection)
        this->SaveDgnMultilineStyleToDatabase (dgnMlinestyle, mlinestyleDictionary);

    // some DGN files may not have a linestyle table
    ElementHandle       tableElement = MultilineStyle::GetTableElement (*m_dgnFile);
    if (!tableElement.IsValid())
        return  RealDwgSuccess;

    // set active mlinestyle
    WString             activeName = MultilineStyle::GetActiveStyleName (tableElement);
    if (!activeName.empty())
        {
        activeName.ToUpper ();

        AcDbObjectId        activeStyleId;
        if (Acad::eOk == mlinestyleDictionary->getAt (activeName.c_str(), activeStyleId))
            m_pFileHolder->GetDatabase()->setCmlstyleID (activeStyleId);
        }

    return RealDwgSuccess;
    }
