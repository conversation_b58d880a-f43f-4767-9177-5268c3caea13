/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdGroup.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/


// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtGroup : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    // For a group originally created in a DGN sheet model, leave it till post process.
    if (FindModelNameFromGroupXRecord (NULL, acObject))
        {
        context.AddPostProcessObject (acObject);
        return  RealDwgIgnoreElement;
        }

    return  CreateNamedGroupElement (acObject, outElement, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BarryBentley    08/02
+---------------+---------------+---------------+---------------+---------------+------*/
MSElementP              CreateFarRefElement
(
) const
    {
    MSElementP      componentEl;

    componentEl = (MSElement *) memutil_calloc (1, MAX_INTERNAL_ELEM_SIZE, 'NGTM');
    memset (componentEl, 0, MAX_INTERNAL_ELEM_SIZE);
    componentEl->ehdr.type = NAMED_GROUP_COMPONENT_ELM;
    componentEl->ehdr.elementSize = componentEl->ehdr.attrOffset = sizeof(NamedGroupComponentElm)/sizeof(short);
    componentEl->ehdr.complex = 1;

    return componentEl;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BarryBentley    05/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                    CreateAndAppendElementWithDependencyLinkage
(
MSElementDescrP         edP,
DependencyLinkageCR     dlR,
ConvertToDgnContextR    context
) const
    {
    MSElementP          componentEl;
    size_t              elemSize = sizeof(NamedGroupComponentElm) + DependencyManagerLinkage::GetSizeofLinkage(dlR, 512);
    componentEl = (MSElementP) _alloca (elemSize);
    memset (componentEl, 0, elemSize);
    componentEl->ehdr.type = NAMED_GROUP_COMPONENT_ELM;
    componentEl->ehdr.elementSize = componentEl->ehdr.attrOffset = sizeof(NamedGroupComponentElm)/sizeof(short);
    componentEl->ehdr.complex = 1;

    DependencyManagerLinkage::AppendLinkageToMSElement (componentEl, dlR, 0);
    edP->AppendElement (*componentEl);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                    CreateAndAppendElementWithPathDependencyLinkage
(
MSElementDescrP         pHdrElmdscr,
DependencyLinkage**     ppDependencies,
UInt32                  numPaths,
ConvertToDgnContextR    context
) const
    {
    MSElement       *pCompElement = CreateFarRefElement ();

    for (UInt32 i = 0; i < numPaths; i++)
        DependencyManagerLinkage::AppendLinkageToMSElement (pCompElement, *ppDependencies[i], 0);

    pHdrElmdscr->AppendElement (*pCompElement);

    memutil_free (pCompElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/07
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    FindModelNameFromGroupXRecord
(
AcString*               pModelName,
AcDbObject*             pObject
) const
    {
    AcDbXrecord*    pXrecord = RealDwgUtil::GetXRecord (pObject, StringConstants::XDataKey_ApplicationDependency, AcDb::kForRead);

    if (NULL == pXrecord)
        pXrecord = RealDwgUtil::GetXRecord (pObject, StringConstants::XDataKey_ApplicationDependencyOld, AcDb::kForRead);

    if (NULL == pXrecord)
        return false;

    RealDwgResBuf*  pDependencyData;
    if (Acad::eOk != pXrecord->rbChain (reinterpret_cast <struct resbuf**>(&pDependencyData), NULL))
        {
        pXrecord->close();
        return false;
        }

    // find the last resbuf in the chain.
    RealDwgResBuf*      pLast;
    for (pLast = pDependencyData; NULL != pLast->GetNext(); pLast = pLast->GetNext())
        ;

    bool    returnVal = false;
    if (AcDb::kDxfXTextString == pLast->restype)
        {
        AcString    modelString (pLast->resval.rstring);

        if (0 == modelString.substr(6).compare(L"Model:"))
            {
            AcString    modelName = modelString.substr (6,-1);

            if (0 != modelName.compare(L"Default"))
                {
                if (NULL != pModelName)
                    *pModelName = modelName;

                returnVal = true;
                }
            }

        }
    pXrecord->close();
    acutRelRb (pDependencyData);

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt               RetrieveFarReferencingDependencies
(
EditElementHandleR      outElement,
AcDbObject*             inObject,
ConvertToDgnContextR    context
) const
    {
    AcDbXrecord*        acXrecord = RealDwgUtil::GetXRecord (inObject, StringConstants::XDataKey_ApplicationDependency, AcDb::kForRead);

    if (NULL == acXrecord)
        acXrecord = RealDwgUtil::GetXRecord (inObject, StringConstants::XDataKey_ApplicationDependencyOld, AcDb::kForRead);

    if (NULL == acXrecord)
        return BSIERROR;

    RealDwgResBuf*      pDependencyResBuf;
    if (Acad::eOk != acXrecord->rbChain (reinterpret_cast <struct resbuf **> (&pDependencyResBuf), NULL))
        {
        acXrecord->close();
        return BSIERROR;
        }
    acXrecord->close();

    MSElementDescrP         elmdscr = outElement.ExtractElementDescr ();
    if (NULL == elmdscr)
        return BSIERROR;

    RealDwgResBuf*          pResbufHead = pDependencyResBuf;
    DependencyLinkage*      pThisDependency     = NULL;
    DependencyLinkage**     ppPathDependencies  = NULL;
    UInt32                  numPaths = 0, maxCount = 0;
    while (SUCCESS == RealDwgXDataUtil::DependencyLinkageFromXData (&pThisDependency, &pDependencyResBuf, context))
        {
        if (DEPENDENCY_DATA_TYPE_FAR_ELEM_ID_V == pThisDependency->u.f.rootDataType)
            {
            // attach direct far ref dependencies to a new element.
            CreateAndAppendElementWithDependencyLinkage (elmdscr, *pThisDependency, context);

            free (pThisDependency);
            }
        else if (DEPENDENCY_DATA_TYPE_PATH_V == pThisDependency->u.f.rootDataType)
            {
            if (numPaths >= maxCount)
                {
                maxCount += MAX_FARREFDEPENDENCIES_INIT_COUNT;
                ppPathDependencies = (DependencyLinkage **)realloc (ppPathDependencies, maxCount * sizeof ppPathDependencies);
                if (NULL == ppPathDependencies)
                    return  BSIERROR;
                }

            ppPathDependencies[numPaths++] = pThisDependency;
            }
        else
            {
            free (pThisDependency);
            continue;
            }

        if ( (NULL != pDependencyResBuf) && AcDb::kDxfXdControlString == pDependencyResBuf->GetResType())
            pDependencyResBuf = pDependencyResBuf->GetNext ();
        }

    // attach the left out path dependencies to a new element.
    if (numPaths > 0)
        {
        CreateAndAppendElementWithPathDependencyLinkage (elmdscr, ppPathDependencies, numPaths, context);
        for (UInt32 i = 0; i < numPaths; i++)
            free (ppPathDependencies[i]);
        free (ppPathDependencies);
        }

    // get named group flags
    if ( (NULL != pDependencyResBuf) && (AcDb::kDxfInt32 == pDependencyResBuf->GetResType()))
        {
        // skip legacy data used for named group flags:
        pDependencyResBuf = pDependencyResBuf->GetNext ();
        }

    // check original DGN model name from which the named group was created:
    if ( (NULL != pDependencyResBuf) && (AcDb::kDxfXTextString == pDependencyResBuf->GetResType()) )
        {
        ACHAR const *modelName = pDependencyResBuf->GetString();
        if (0 != *modelName)
            {
            /*-------------------------------------------------------------------
            If the named group was originated from Default model, put the element
            back to Default model.
            -------------------------------------------------------------------*/
            if (0 == wcscmp (modelName, L"Model:Default"))
                elmdscr->el.ehdr.nonModel = false;
            }
        }

    outElement.SetElementDescr (elmdscr, true, false);

    RealDwgResBuf::Free (pResbufHead);

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                    RetrieveApplicationData
(
EditElementHandleR      outElement,
AcDbObject*             inObject
) const
    {
    RealDwgResBuf*  pMicroStationXData;
    if (NULL == (pMicroStationXData = static_cast <RealDwgResBuf*> (inObject->xData (StringConstants::RegAppName_MicroStation))))
        return;

    MSElementP  element = (MSElementP) malloc (MAX_ELEMENT_SIZE);
    if (NULL == element)
        return;

    outElement.GetElementCP()->CopyTo (*element);

    bool        isComponentSet = false;
    AcString    string;
    if (SUCCESS == RealDwgXDataUtil::GetXDataStringByKey (string, pMicroStationXData, StringConstants::XDataKey_CompSetExpressionSummary))
        {
        LinkageUtil::SetStringLinkage (element, STRING_LINKAGE_KEY_ComponentSetExpressionSummary, string.kwszPtr());
        isComponentSet = true;
        }
    if (SUCCESS == RealDwgXDataUtil::GetXDataStringByKey (string, pMicroStationXData, StringConstants::XDataKey_CompSetExpression))
        {
        LinkageUtil::SetStringLinkage (element, STRING_LINKAGE_KEY_ComponentSetExpression, string.kwszPtr());
        isComponentSet = true;
        }

    if (isComponentSet)
        {
        // retrieve named group flags saved for component set.
        RealDwgResBuf* pData = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdInteger32, StringConstants::XDataKey_NamedGroupFlags, false);
        if (NULL != pData)
            {
            BeAssert (AcDb::kDxfXdInteger32 == pData->GetResType() && L"Unexpected xdata on AcDbGroup!");
            NamedGroupFlags  flags(pData->GetInt32());

            // remove DWG supported flags:
            flags.m_selectMembers = 0;
            flags.m_anonymous = 0;

            // restore saved DGN flags:
            NamedGroupHeaderElm *pNamedGroupHeader = reinterpret_cast <NamedGroupHeaderElm*>(element);
            pNamedGroupHeader->controlFlags |= flags.ToUInt32();
            }
        }

    RealDwgResBuf* pBinaryChunk = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdBinaryChunk, StringConstants::XDataKey_ApplicationLinkage, false);
    if (NULL != pBinaryChunk)
        {
        BeAssert (AcDb::kDxfXdBinaryChunk == pBinaryChunk->GetResType() && L"Unexpected xdata on AcDbGroup!");

        RealDwgBinaryData   binaryData;
        pBinaryChunk->GetBinaryChunk (binaryData, false);

        for (RealDwgResBuf* pNext = pBinaryChunk->GetNext(); (NULL != pNext) && (AcDb::kDxfXdBinaryChunk == pNext->GetResType()); pNext = pNext->GetNext())
            pNext->GetBinaryChunk (binaryData, true);

        size_t              dataSize = binaryData.size() / 2;
        if (dataSize > 0 && (element->ehdr.elementSize + dataSize) < MAX_ELEMENT_WORDS)
            {
            Int16           *pEnd = element->buf + element->hdr.ehdr.elementSize;
            memcpy (pEnd, (UShort *) &binaryData.front(), 2 * dataSize);
            element->ehdr.elementSize += (UInt32)dataSize;
            }
        }

    outElement.ReplaceElement (element);
    free (element);

    // free the xData.
    RealDwgResBuf::Free (pMicroStationXData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           CreateNamedGroupElement
(
AcDbObject*             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const
    {
    AcDbGroup*              acGroup = AcDbGroup::cast (acObject);
    WChar                   nameChars[MAX_NAMEDGROUP_NAME_LENGTH], descriptionChars[MAX_NAMEDGROUP_DESCR_LENGTH];
    NamedGroupFlags         flags;

    if (acGroup->isSelectable())
        flags.m_selectMembers = true;

    if (acGroup->isAnonymous())
        {
        if (!context.CreateUnnamedGroups () && !acGroup->isSelectable())
            return RealDwgIgnoreElement;

        flags.m_anonymous = true;
        }

    flags.m_allowFarReferences = true;

    ACHAR*                  groupName = NULL;
    if (Acad::eOk != acGroup->getName(groupName))
        return RealDwgIgnoreElement;

    RealDwgUtil::AcStringToMSWChar (nameChars, groupName, MAX_NAMEDGROUP_NAME_LENGTH);
    RealDwgUtil::AcStringToMSWChar (descriptionChars, acGroup->description(), MAX_NAMEDGROUP_DESCR_LENGTH);

    acutDelString (groupName);

    NamedGroupPtr       namedGroup;
    NamedGroupStatus    status = NamedGroup::Create (namedGroup, nameChars, descriptionChars, flags, context.GetModel());
    if (NG_Success == status)
        {
        NamedGroupMemberFlags   memberFlags;
        memberFlags.m_forwardPropagate = NamedGroup::NG_FLAG_GroupLock;
        memberFlags.m_backwardPropagate = NamedGroup::NG_FLAG_GroupLock;
        memberFlags.m_groupPropagate = NamedGroup::NG_FLAG_GroupLock;

        AcDbObjectIdArray       entries;
        acGroup->allEntityIds (entries);

        for (int iEntry=0, count = entries.length(); iEntry<count; iEntry++)
            namedGroup->AddMember (context.ElementIdFromObjectId(entries[iEntry]), INVALID_MODELREF, memberFlags);

        MSElementDescrP     elmdscr = NULL;
        namedGroup->CreateElementDescr (&elmdscr);
        elmdscr->el.ehdr.uniqueId = context.ElementIdFromObjectId (acGroup->objectId());

        /*-------------------------------------------------------------------------------
        Set near ref named groups in non model section so they can be viewed in all
        models, just like how they are in ACAD.  May be reset to a model for far ref.

        AutoCAD's groups are in dictionary section and can be viewed in all models.
        Therefore it is desirable to place them in nonModel section.  However, currently
        for far referencing, the named groups fail to work because dependency manager
        fails to resolve far ref'ed elements.  Dependency manager apparently makes an
        assumption that the reference file attachements are in the same model cache as one
        named group elements in.  To work around this dependency failure for round tripped
        named groups, we place them to the best section as we can:

        1) For near referencing, place named groups in nonModel section, as that is how
            it is supported by ACAD.
        2) For round tripped far referencing, if a named group was originally created in
            Default model, we place it in Default model.  If it was created in a sheet
            model, we place it in the sheet model.
        3) There is no good way to handle far referencing named groups in a non-default
            design model, which can not be saved in DWG file.  In this case, we place them
            to nonModel section.

        To achieve above goal, a model name is stored as a the last entry in xrecord chain
        attached to a group object.  This assumes that the model name is not changed in
        ACAD.  If model name can not be found, the named group will be placed in non-Model
        section.
        -------------------------------------------------------------------------------*/
        elmdscr->el.ehdr.nonModel = true;

        outElement.SetElementDescr (elmdscr, true, false);

        // retrieve and add far referencing dependencies we stored in xdata.
        RetrieveApplicationData (outElement, acObject);
        RetrieveFarReferencingDependencies (outElement, acObject, context);
        }

    return outElement.IsValid() ? RealDwgSuccess : CantCreateNamedGroup;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    EditElementHandle   namedGroupElem;
    RealDwgStatus       status;
    if (RealDwgSuccess != (status = CreateNamedGroupElement (acObject, namedGroupElem, context)))
        return status;

    AcString    modelName;

    /*-------------------------------------------------------------------------------
    Find the model in which the named group was originally created, and check the named group element into that model.
    -------------------------------------------------------------------------------*/
    if (FindModelNameFromGroupXRecord (&modelName, acObject))
        {
        ModelId     modelId = context.GetFile()->FindModelIdByName (modelName);
        if (INVALID_MODELID != modelId)
            {
            DgnModelP   modelCache = context.GetFile()->LoadRootModelById (NULL, modelId);

            if (NULL != modelCache)
                status = RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (dgnModel_loadDscrFromFile(modelCache, namedGroupElem.GetElementDescrP(), context.GetFileTime()));
            else
                status = CantCreateNamedGroup;
            }
        }

    return  status;
    }

};  // ToDgnExtGroup



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtNamedGroup : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToObject
(
ElementHandleR              elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&                acObject,           // The object created, if any.
AcDbObjectP                 existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR      context             // The context
) const override
    {
    NamedGroupPtr           namedGroup = NULL;
    if (SUCCESS != NamedGroup::Create(namedGroup, elemHandle, context.GetModel()))
        return  CantCreateNamedGroup;

    WString                 name = namedGroup->GetName ();
    if (name.empty())
        return  InvalidName;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbGroup::desc());
    AcDbGroup*              acGroup = AcDbGroup::cast (acObject);
    if (NULL == acGroup)
        return  NullObject;

    // make sure the name doesn't contain any characters that AutoCAD can't handle.
    context.ValidateName (name);

    // if we can add a new group to the parent dictionary, don't proceed - it will crash!
    if (acGroup->objectId().isNull() && !AddGroupToDictionary(acGroup, name, elemHandle.GetElementId(), context))
        return  CantCreateNamedGroup;

    // see if we need to set the anonymous flags.
    NamedGroupFlagsCR       flags = namedGroup->GetFlags ();
    if (flags.m_anonymous)
        {
        // setting to anonymous changes the name.
        if (!acGroup->isAnonymous())
            acGroup->setAnonymous ();
        }
    else
        {
        // see if the name has changed.
        if (0 != name.compare(acGroup->name()))
            acGroup->setName (name.c_str());
        }

    // set description.
    WString                 description = namedGroup->GetDescription ();
    if (!description.empty())
        acGroup->setDescription (description.c_str());

    // set selectable
    acGroup->setSelectable (flags.m_selectMembers);

    // the members are added in the post-processing phase.
    context.PostProcessingRequired (elemHandle, acObject->objectId());

    if (context.GetIsSavingApplicationData())
        AddApplicationDataToAcObject (acObject, elemHandle.GetElementDescrCP(), flags.ToUInt32(), context);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToObjectPostProcess
(
ElementHandleR              inElement,
AcDbObjectP                 acObject,
ConvertFromDgnContextR      context
) const override
    {
    NamedGroupPtr           namedGroup = NULL;

    if (NG_Success == NamedGroup::Create(namedGroup, inElement, context.GetModel()))
        {
        AcDbGroup*          acGroup = AcDbGroup::cast (acObject);
        bool                hasFarReference = false;

        acGroup->clear();

        NamedGroupMemberP   pMember;
        AcDbDatabase*       pDatabase = context.GetDatabase();
        for (unsigned int iMember=0; NULL != (pMember = namedGroup->GetMember(iMember)); iMember++)
            {
            if (!pMember->IsFarReference(context.GetModel()))
                {
                AcDbObjectId            objectId;

                if ((Acad::eOk == pDatabase->getAcDbObjectId(objectId, false, context.DBHandleFromElementId(pMember->GetElementId()), 0)) &&
                    !objectId.isErased())
                    {
                    if (objectId.objectClass()->isDerivedFrom (AcDbEntity::desc()))
                        acGroup->append (objectId);
                    else
                        DIAGNOSTIC_PRINTF ("Ignoring non-Entity member (%ls) of saved group (%ls)\n", objectId.objectClass()->name(), acGroup->name());
                    }
                }
            else
                {
                hasFarReference = true;
                }
            }

        if (hasFarReference && context.GetIsSavingApplicationData())
            AddFarReferencingDependenciesToAcObject (acGroup, inElement, namedGroup->GetFlags().ToUInt32(), context);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                    AddApplicationDataToAcObject
(
AcDbObject*             pObject,
MSElementDescrCP        pElmDscr,
UInt32                  flags,
ConvertFromDgnContextR  context
) const
    {
    /*-----------------------------------------------------------------------------------
    For Now extract app linkage and component set expression summary string linkage which
    are used by AutoPlant.  We may also need to extract xattributes in the future when
    we are ready to do that for all none graphical objects.
    -----------------------------------------------------------------------------------*/
    int             appLinkageSize = 0;
    UShort          appLinkageBuffer[MAX_ELEMENT_SIZE];

    if (SUCCESS == context.ExtractApplicationLinkageFromElement(appLinkageBuffer, &appLinkageSize, &pElmDscr->el))
        {
        bool            changed             = false;
        RealDwgResBuf*  pMicroStationXData  = static_cast <RealDwgResBuf*> (pObject->xData (StringConstants::RegAppName_MicroStation));

        // create XData if needed, put appLinkage onto it.
        changed = RealDwgXDataUtil::SetAppLinkage (&pMicroStationXData, appLinkageBuffer, appLinkageSize, context.GetDatabase());

        WChar             string[1024];
        string[0] = 0;

        if (BSISUCCESS == LinkageUtil::ExtractNamedStringLinkageByIndex(string, 1024, STRING_LINKAGE_KEY_ComponentSetExpressionSummary, 0, &pElmDscr->el))
            changed = RealDwgXDataUtil::SetXDataStringByKey (pMicroStationXData, StringConstants::XDataKey_CompSetExpressionSummary, string) || changed;
        if (BSISUCCESS == LinkageUtil::ExtractNamedStringLinkageByIndex(string, 1024, STRING_LINKAGE_KEY_ComponentSetExpression, 0, &pElmDscr->el))
            changed = RealDwgXDataUtil::SetXDataStringByKey (pMicroStationXData, StringConstants::XDataKey_CompSetExpression, string) || changed;

        if (0 != string[0])
            changed = RealDwgXDataUtil::SetXDataInt32ByKey (pMicroStationXData, StringConstants::XDataKey_NamedGroupFlags, flags) || changed;

        if (changed)
            pObject->setXData (pMicroStationXData);

        RealDwgResBuf::Free (pMicroStationXData);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                    AddFarReferencingDependenciesToAcObject
(
AcDbObject*             pObject,
ElementHandleCR         inElement,
int                     flags,
ConvertFromDgnContextR  context
) const
    {
    UInt32              maxCount            = MAX_FARREFDEPENDENCIES_INIT_COUNT;
    DependencyLinkage   **ppDependencies    = (DependencyLinkage **) malloc(maxCount * sizeof (DependencyLinkage*));
    if (NULL == ppDependencies)
        return;

    RealDwgResBuf*      pDependencyResBuf   = NULL;
    RealDwgResBuf*      pLast               = NULL;
    MSElementDescrCP    pHdrElmdscr         = inElement.GetElementDescrCP ();
    for (MSElementDescrCP pEd = pHdrElmdscr->h.firstElem; NULL != pEd; pEd = pEd->h.next)
        {
        // scan and count dependencies so we can allocate enough buffer:
        UInt32      numDependencies = CountFarRefDependenciesPerElement (&pEd->el);
        if (0 == numDependencies)
            continue;

        if (numDependencies > maxCount)
            {
            while (numDependencies > maxCount)
                maxCount += MAX_FARREFDEPENDENCIES_INIT_COUNT;

            ppDependencies = (DependencyLinkage **) realloc(ppDependencies, maxCount * sizeof (DependencyLinkage**));
            if (NULL == ppDependencies)
                return;
            }

        numDependencies = 0;

        if (SUCCESS == BuildFarRefDependencyListPerElement (ppDependencies, numDependencies, maxCount, const_cast <MSElement*>(&pEd->el)))
            {
            for (UInt32 iDependency = 0; iDependency < numDependencies; iDependency++)
                {
                RealDwgResBuf* pNew;
                if (NULL != (pNew = RealDwgXDataUtil::XDataFromDependencyLinkage (ppDependencies[iDependency], context)))
                    {
                    if (NULL == pDependencyResBuf)
                        pDependencyResBuf = pLast = pNew;
                    else
                        pLast = pLast->Append (pNew);

                    pLast = pLast->GetTail ();
                    }

                free (ppDependencies[iDependency]);
                }
            }
        }

    free (ppDependencies);

    if (NULL != pDependencyResBuf)
        {
        // append named group flags:
        pLast = pLast->Append (RealDwgResBuf::CreateInt32 (AcDb::kDxfInt32, (UInt32)flags));

        // append model name+id if the named group is in a non-default model:
        AcString modelName = GetNamedGroupModelName (inElement);
        if (!modelName.isEmpty())
            pLast = pLast->Append (RealDwgResBuf::CreateString (AcDb::kDxfXTextString, modelName.kwszPtr()));

        // create xrecord for the group object
        AcDbXrecord*  pDependencyXrecord = RealDwgUtil::CreateXRecord (pObject, StringConstants::XDataKey_ApplicationDependency);

        if (NULL != pDependencyXrecord)
            {
            pDependencyXrecord->setFromRbChain (*pDependencyResBuf);
            pDependencyXrecord->close ();
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Failed creating dependency XRecord for named group of ID: %I64d\n", pHdrElmdscr->el.ehdr.uniqueId);
            }

        RealDwgResBuf::Free (pDependencyResBuf);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool             CollectFarRefDependencies (DependencyLinkageP* ppDependencyList, UInt32& numDependencies, DependencyLinkageCP thisDependency)
    {
    if (NULL != thisDependency &&
        DEPENDENCYAPPID_NamedGroup == thisDependency->appID &&
        (DEPENDENCY_DATA_TYPE_FAR_ELEM_ID_V == thisDependency->u.f.rootDataType ||
         DEPENDENCY_DATA_TYPE_PATH_V == thisDependency->u.f.rootDataType))
        {
        if (NULL == ppDependencyList)
            {
            // just scan and count number of dependencies:
            numDependencies++;
            return  true;
            }

        size_t          thisSize = DependencyManagerLinkage::GetSizeofLinkage (*thisDependency, 0);

        ppDependencyList[numDependencies] = (DependencyLinkageP) malloc (thisSize);

        memcpy (ppDependencyList[numDependencies++], thisDependency, thisSize);

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt               BuildFarRefDependencyListPerElement
(
DependencyLinkageP*     ppDependencyList,
UInt32&                 numDependencies,
UInt32                  maxCount,
MSElementCP             pElement
) const
    {
    numDependencies = 0;

    for (DependencyLinkageIterator iter(pElement); iter.IsValid() && numDependencies < maxCount; iter.ToNext())
        CollectFarRefDependencies (ppDependencyList, numDependencies, iter.GetLinkage());

    return  numDependencies > 0 ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/08
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                  CountFarRefDependenciesPerElement (MSElementCP pElement) const
    {
    UInt32              numDependencies = 0;

    for (DependencyLinkageIterator iter(pElement); iter.IsValid(); iter.ToNext())
        CollectFarRefDependencies (NULL, numDependencies, iter.GetLinkage());

    return  numDependencies;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/07
+---------------+---------------+---------------+---------------+---------------+------*/
AcString                GetNamedGroupModelName (ElementHandleCR inElement) const
    {
    /*-----------------------------------------------------------------------------------
    Save named group's model name only if the model is the Default design model or a
    sheet model.  A none default design model can't even survive in DWG, so there is no
    point to save it name.  If a named group is already in non-default model section, it
    will be round tripped back anyway.
    -----------------------------------------------------------------------------------*/
    DgnModelP   model = inElement.GetDgnModelP ();
    if (NULL != model && !model->IsDictionaryModel())
        {
        if (model->IsDefault())
            {
            return  AcString(L"Model:Default");
            }
        else if (DgnModelType::Sheet == model->GetModelInfo().GetModelType())
            {
            WCharCP     modelName = model->GetModelName ();

            if (NULL != modelName)
                return  AcString(L"Model:") + AcString(modelName);
            }
        }

    return  AcString();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    AddGroupToDictionary (AcDbGroup* acGroup, WStringCR name, ElementId id, ConvertFromDgnContextR context) const
    {
    AcDbDictionaryPointer   groupDictionary (context.GetFileHolder().GetDatabase()->groupDictionaryId(), AcDb::kForWrite);
    if (Acad::eOk != groupDictionary.openStatus())
        return  false;

    // Observations about newly constructed instance of AcDbGroup as of RealDwg 2009:
    // 1. If you call setName or seAnonymous before adding group to the group dictionary, the methods crash.
    // 2. Adding group to the dictionary sets its name, so there is no need to call setName.
    // 3. Calling group->setAnonymouse changes the name of the group to something *A1.
    // 3. The GroupDictionary must be closed before calling group->setAnonymous, otherwise fatalError is called
    //    telling you that eWasOpenForWrite is the error. That must be because setAnonymous changes the group
    //    name, and thus must also change the dictionary.
    if (context.AddObjectToDictionary(groupDictionary, acGroup, name.c_str(), id).isNull())
        return  false;

    // don't need the group dictionary any more, and if we leave it open subsequent calls to setAnonymous fail.
    groupDictionary.close();

    return  true;
    }

};  // ToDwgExtNamedGroup

