#pragma once

#include "../../core/GeometryProcessor.h"
#include "../../../include/ExportTypes.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbspline.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbtrace.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/db3dface.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbxref.h>
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbray.h>
#include <realdwg/base/dbxline.h>
#include <realdwg/base/dbpoint.h>
#include <realdwg/base/dbshape.h>
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbgroup.h>
#include <realdwg/base/dblight.h>
#include <realdwg/base/dbcamera.h>
#include <realdwg/base/dbhelix.h>
#include <realdwg/base/dbgeodata.h>
#include <realdwg/base/dbnamedpath.h>
#include <realdwg/base/dbmotionpath.h>
#include <realdwg/base/dbsection.h>
#include <realdwg/base/dbsurface.h>
#include <realdwg/base/dbnurbsurface.h>
#include <realdwg/base/dbplanesurface.h>
#include <realdwg/base/dbextrsurf.h>
#include <realdwg/base/dbrevsurf.h>
#include <realdwg/base/dbswepsurf.h>
#include <realdwg/base/dbloftsurf.h>
#include <realdwg/base/dbblendsurf.h>
#include <realdwg/base/dbnetsurf.h>
#include <realdwg/base/dbpatchsurf.h>
#include <realdwg/base/dboffsetsurf.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbtolerance.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbpdfref.h>
#include <realdwg/base/dbdwfref.h>
#include <realdwg/base/dbdgnref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/base/dbpointcloudex.h>
#include <realdwg/base/dbgeopositionmarker.h>
#include <realdwg/base/dbsun.h>
#include <realdwg/base/dbsky.h>
#include <realdwg/base/dbrenderenvironment.h>
#include <realdwg/base/dbrenderentry.h>
#include <realdwg/base/dbrenderglobal.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/dbmentalrayrendersettings.h>
#include <realdwg/base/dbrapidrtrendersettings.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbviewtable.h>
#include <realdwg/base/dbucstable.h>
#include <realdwg/base/dbvptable.h>
#include <realdwg/base/dbappid.h>
#include <realdwg/base/dbspatialfilter.h>
#include <realdwg/base/dblayerfilter.h>
#include <realdwg/base/dbindex.h>
#include <realdwg/base/dbspatialindex.h>
#include <realdwg/base/dblayerindex.h>
#include <realdwg/base/dbcolor.h>
#include <realdwg/base/dbtrans.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/base/dbeval.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbacis.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#include <realdwg/base/dbassoccompoundactionparam.h>
#include <realdwg/base/dbassocedgeactionparam.h>
#include <realdwg/base/dbassocfaceactionparam.h>
#include <realdwg/base/dbassocpathactionparam.h>
#include <realdwg/base/dbassocpointrefactionparam.h>
#include <realdwg/base/dbassocvertexactionparam.h>
#include <realdwg/base/dbassocasmbodyactionparam.h>
#include <realdwg/base/dbassocblendsurfaceactionbody.h>
#include <realdwg/base/dbassocextrudesurfaceactionbody.h>
#include <realdwg/base/dbassocfilletactionbody.h>
#include <realdwg/base/dbassocloftsurfaceactionbody.h>
#include <realdwg/base/dbassocnetworksurfaceactionbody.h>
#include <realdwg/base/dbassocoffsetsurfaceactionbody.h>
#include <realdwg/base/dbassocpatchsurfaceactionbody.h>
#include <realdwg/base/dbassocplanesurfaceactionbody.h>
#include <realdwg/base/dbassocrevolvesurfaceactionbody.h>
#include <realdwg/base/dbassocsweepsurfaceactionbody.h>
#include <realdwg/base/dbassoctrimsurfaceactionbody.h>
#include <realdwg/base/dbassoc3pointangulardimactionbody.h>
#include <realdwg/base/dbassocaligneddimactionbody.h>
#include <realdwg/base/dbassocordinatedimactionbody.h>
#include <realdwg/base/dbassocrotateddimactionbody.h>
#include <realdwg/base/dbassocrestoreentitystateactionbody.h>
#include <realdwg/base/dbassocmleaderactionbody.h>
#include <realdwg/base/dbassocperssubentid.h>
#include <realdwg/base/dbassocedgeperssubentid.h>
#include <realdwg/base/dbassocfaceperssubentid.h>
#include <realdwg/base/dbassocvertexperssubentid.h>
#include <realdwg/base/dbassocexternalbodyperssubentid.h>
#include <realdwg/base/dbassocindexperssubentid.h>
#include <realdwg/base/dbassocpathperssubentid.h>
#include <realdwg/base/dbassocpointperssubentid.h>
#include <realdwg/base/dbassocsingledependency.h>
#include <realdwg/base/dbassocdimdependencybody.h>
#include <realdwg/base/dbassocobjectpointer.h>
#include <realdwg/base/dbassocstatus.h>
#include <realdwg/base/dbassocglobal.h>
#include <realdwg/base/dbassocmanager.h>
#include <realdwg/base/dbassocactionparam.h>
#include <realdwg/base/dbassocactionbody.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#include <realdwg/base/dbassoccompoundactionparam.h>
#include <realdwg/base/dbassocedgeactionparam.h>
#include <realdwg/base/dbassocfaceactionparam.h>
#include <realdwg/base/dbassocpathactionparam.h>
#include <realdwg/base/dbassocpointrefactionparam.h>
#include <realdwg/base/dbassocvertexactionparam.h>
#include <realdwg/base/dbassocasmbodyactionparam.h>
#endif

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>

namespace IModelExport {

//=======================================================================================
// DWG Entity Type Definitions - Complete AutoCAD Entity Support
//=======================================================================================

enum class DWGEntityType {
    // Basic 2D Entities
    Line,                   // AcDbLine
    Point,                  // AcDbPoint
    Circle,                 // AcDbCircle
    Arc,                    // AcDbArc
    Ellipse,                // AcDbEllipse
    Polyline,               // AcDbPolyline
    LWPolyline,             // AcDbPolyline (lightweight)
    Polyline2D,             // AcDb2dPolyline
    Polyline3D,             // AcDb3dPolyline
    Spline,                 // AcDbSpline
    Ray,                    // AcDbRay
    XLine,                  // AcDbXline (construction line)

    // Text Entities
    Text,                   // AcDbText
    MText,                  // AcDbMText (multiline text)
    AttributeDefinition,    // AcDbAttributeDefinition
    Attribute,              // AcDbAttribute

    // Dimension Entities
    AlignedDimension,       // AcDbAlignedDimension
    AngularDimension,       // AcDbAngularDimension
    DiametricDimension,     // AcDbDiametricDimension
    LinearDimension,        // AcDbRotatedDimension
    OrdinateDimension,      // AcDbOrdinateDimension
    RadialDimension,        // AcDbRadialDimension
    RadialDimensionLarge,   // AcDbRadialDimensionLarge
    ArcDimension,           // AcDbArcDimension

    // Leader Entities
    Leader,                 // AcDbLeader
    MLeader,                // AcDbMLeader (multileader)

    // Hatch and Fill
    Hatch,                  // AcDbHatch
    Gradient,               // AcDbHatch with gradient
    Solid,                  // AcDbSolid (2D solid fill)
    Trace,                  // AcDbTrace

    // 3D Face Entities
    Face3D,                 // AcDb3dFace
    PolyFaceMesh,           // AcDbPolyFaceMesh
    PolygonMesh,            // AcDbPolygonMesh
    SubDMesh,               // AcDbSubDMesh (subdivision mesh)

    // 3D Solid Entities
    Solid3D,                // AcDb3dSolid
    Region,                 // AcDbRegion
    Body,                   // AcDbBody

    // Surface Entities
    Surface,                // AcDbSurface (base)
    NurbSurface,            // AcDbNurbSurface
    PlaneSurface,           // AcDbPlaneSurface
    ExtrudedSurface,        // AcDbExtrudedSurface
    RevolvedSurface,        // AcDbRevolvedSurface
    SweptSurface,           // AcDbSweptSurface
    LoftedSurface,          // AcDbLoftedSurface
    BlendSurface,           // AcDbBlendSurface
    NetworkSurface,         // AcDbNetworkSurface
    PatchSurface,           // AcDbPatchSurface
    OffsetSurface,          // AcDbOffsetSurface

    // Mesh Entities
    Mesh,                   // AcDbMesh

    // Block Entities
    BlockReference,         // AcDbBlockReference
    MInsert,                // AcDbMInsert (array insert)
    XRef,                   // External reference

    // Multiline
    MLine,                  // AcDbMline

    // Shape and Font
    Shape,                  // AcDbShape

    // Image and OLE
    RasterImage,            // AcDbRasterImage
    WipeOut,                // AcDbWipeout
    OLE2Frame,              // AcDbOle2Frame

    // Viewport
    Viewport,               // AcDbViewport

    // Table
    Table,                  // AcDbTable

    // Field
    Field,                  // AcDbField

    // Group
    Group,                  // AcDbGroup

    // Light and Camera
    Light,                  // AcDbLight
    WebLight,               // AcDbWebLight
    DistantLight,           // AcDbDistantLight
    PointLight,             // AcDbPointLight
    SpotLight,              // AcDbSpotLight
    Camera,                 // AcDbCamera

    // Helix
    Helix,                  // AcDbHelix

    // Geographic
    GeoData,                // AcDbGeoData
    GeoPositionMarker,      // AcDbGeoPositionMarker

    // Path and Motion
    NamedPath,              // AcDbNamedPath
    MotionPath,             // AcDbMotionPath

    // Section
    Section,                // AcDbSection

    // Tolerance
    Tolerance,              // AcDbTolerance
    FCF,                    // AcDbFcf (feature control frame)

    // Proxy
    ProxyEntity,            // AcDbProxyEntity

    // Underlay References
    UnderlayReference,      // AcDbUnderlayReference (base)
    DwfReference,           // AcDbDwfReference
    DgnReference,           // AcDbDgnReference
    PdfReference,           // AcDbPdfReference

    // Point Cloud
    PointCloud,             // AcDbPointCloud
    PointCloudEx,           // AcDbPointCloudEx

    // Rendering
    Sun,                    // AcDbSun
    Sky,                    // AcDbSky
    RenderEnvironment,      // AcDbRenderEnvironment
    RenderEntry,            // AcDbRenderEntry
    RenderGlobal,           // AcDbRenderGlobal
    Material,               // AcDbMaterial
    VisualStyle,            // AcDbVisualStyle
    MentalRayRenderSettings, // AcDbMentalRayRenderSettings
    RapidRTRenderSettings,  // AcDbRapidRTRenderSettings

    // Layout and Plot
    Layout,                 // AcDbLayout
    PlotSettings,           // AcDbPlotSettings

    // Dictionary and XRecord
    Dictionary,             // AcDbDictionary
    XRecord,                // AcDbXrecord

    // Symbol Tables
    LayerTableRecord,       // AcDbLayerTableRecord
    LinetypeTableRecord,    // AcDbLinetypeTableRecord
    TextStyleTableRecord,   // AcDbTextStyleTableRecord
    DimStyleTableRecord,    // AcDbDimStyleTableRecord
    BlockTableRecord,       // AcDbBlockTableRecord
    ViewTableRecord,        // AcDbViewTableRecord
    UCSTableRecord,         // AcDbUCSTableRecord
    ViewportTableRecord,    // AcDbViewportTableRecord
    RegAppTableRecord,      // AcDbRegAppTableRecord
    MLineStyleTableRecord,  // AcDbMlineStyle

    // Filters and Indexes
    SpatialFilter,          // AcDbSpatialFilter
    LayerFilter,            // AcDbLayerFilter
    SpatialIndex,           // AcDbSpatialIndex
    LayerIndex,             // AcDbLayerIndex

    // Dynamic Blocks
    DynamicBlockReference,  // Dynamic block reference

    // Evaluation and Constraints
    EvalGraph,              // AcDbEvalGraph

    // Associative Objects
    AssocNetwork,           // AcDbAssocNetwork
    AssocAction,            // AcDbAssocAction
    AssocActionBody,        // AcDbAssocActionBody
    AssocActionParam,       // AcDbAssocActionParam
    AssocDependency,        // AcDbAssocDependency
    AssocGeomDependency,    // AcDbAssocGeomDependency
    AssocValueDependency,   // AcDbAssocValueDependency
    AssocVariable,          // AcDbAssocVariable

    // Custom and Unknown
    CustomEntity,           // Custom entity types
    Unknown                 // Unknown or unsupported entity type
};

//=======================================================================================
// DWG Entity Creator - Factory for creating all DWG entity types
//=======================================================================================

class DWGEntityCreator {
public:
    DWGEntityCreator();
    ~DWGEntityCreator();

    //===================================================================================
    // Basic 2D Entity Creation
    //===================================================================================

    // Line entities
    bool CreateLine(const Point3d& start, const Point3d& end, const std::string& layer = "");
    bool CreateRay(const Point3d& basePoint, const Vector3d& direction, const std::string& layer = "");
    bool CreateXLine(const Point3d& basePoint, const Vector3d& direction, const std::string& layer = "");

    // Point entities
    bool CreatePoint(const Point3d& position, const std::string& layer = "");

    // Circle and arc entities
    bool CreateCircle(const Point3d& center, double radius, const std::string& layer = "");
    bool CreateArc(const Point3d& center, double radius, double startAngle, double endAngle, const std::string& layer = "");
    bool CreateEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, const std::string& layer = "");

    // Polyline entities
    bool CreatePolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "");
    bool CreateLWPolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "");
    bool Create2DPolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "");
    bool Create3DPolyline(const std::vector<Point3d>& points, const std::string& layer = "");

    // Spline entities
    bool CreateSpline(const std::vector<Point3d>& controlPoints, int degree = 3, const std::string& layer = "");
    bool CreateNurbsCurve(const std::vector<Point3d>& controlPoints, const std::vector<double>& knots,
                         const std::vector<double>& weights, int degree, const std::string& layer = "");

    //===================================================================================
    // Text Entity Creation
    //===================================================================================

    // Text entities
    bool CreateText(const Point3d& position, const std::string& text, double height,
                   double rotation = 0.0, const std::string& layer = "");
    bool CreateMText(const Point3d& position, const std::string& text, double width, double height,
                    const std::string& layer = "");

    // Attribute entities
    bool CreateAttributeDefinition(const Point3d& position, const std::string& tag, const std::string& prompt,
                                  const std::string& defaultValue, double height, const std::string& layer = "");
    bool CreateAttribute(const Point3d& position, const std::string& tag, const std::string& value,
                        double height, const std::string& layer = "");

    //===================================================================================
    // Dimension Entity Creation
    //===================================================================================

    // Linear dimensions
    bool CreateAlignedDimension(const Point3d& start, const Point3d& end, const Point3d& textPos,
                               const std::string& layer = "");
    bool CreateLinearDimension(const Point3d& start, const Point3d& end, const Point3d& textPos,
                              double rotation, const std::string& layer = "");
    bool CreateOrdinateDimension(const Point3d& definingPoint, const Point3d& leaderEndPoint,
                                bool useXAxis, const std::string& layer = "");

    // Angular dimensions
    bool CreateAngularDimension(const Point3d& center, const Point3d& start1, const Point3d& end1,
                               const Point3d& start2, const Point3d& end2, const Point3d& textPos,
                               const std::string& layer = "");
    bool Create3PointAngularDimension(const Point3d& center, const Point3d& start, const Point3d& end,
                                     const Point3d& textPos, const std::string& layer = "");

    // Radial dimensions
    bool CreateRadialDimension(const Point3d& center, const Point3d& chordPoint, double leaderLength,
                              const std::string& layer = "");
    bool CreateDiametricDimension(const Point3d& chordPoint, const Point3d& farChordPoint,
                                 double leaderLength, const std::string& layer = "");
    bool CreateRadialDimensionLarge(const Point3d& center, const Point3d& chordPoint,
                                   const Point3d& overrideCenter, const Point3d& jogPoint,
                                   double jogAngle, const std::string& layer = "");

    // Arc dimension
    bool CreateArcDimension(const Point3d& center, const Point3d& start, const Point3d& end,
                           const Point3d& arcPoint, const std::string& layer = "");

    //===================================================================================
    // Leader Entity Creation
    //===================================================================================

    // Leader entities
    bool CreateLeader(const std::vector<Point3d>& points, const std::string& annotation,
                     const std::string& layer = "");
    bool CreateMLeader(const std::vector<Point3d>& leaderPoints, const std::string& text,
                      const Point3d& textLocation, const std::string& layer = "");

    //===================================================================================
    // Hatch and Fill Entity Creation
    //===================================================================================

    // Hatch entities
    bool CreateHatch(const std::vector<std::vector<Point3d>>& boundaries, const std::string& pattern,
                    double scale = 1.0, double angle = 0.0, const std::string& layer = "");
    bool CreateGradientHatch(const std::vector<std::vector<Point3d>>& boundaries, const Color& color1,
                            const Color& color2, const std::string& layer = "");
    bool CreateSolidHatch(const std::vector<std::vector<Point3d>>& boundaries, const Color& color,
                         const std::string& layer = "");

    // Solid and trace entities
    bool CreateSolid(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4,
                    const std::string& layer = "");
    bool CreateTrace(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4,
                    const std::string& layer = "");

    //===================================================================================
    // 3D Face and Mesh Entity Creation
    //===================================================================================

    // 3D face entities
    bool Create3DFace(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4,
                     const std::string& layer = "");
    bool CreatePolyFaceMesh(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces,
                           const std::string& layer = "");
    bool CreatePolygonMesh(const std::vector<std::vector<Point3d>>& controlPoints, int mSize, int nSize,
                          const std::string& layer = "");
    bool CreateSubDMesh(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces,
                       int subdivisionLevel, const std::string& layer = "");

    //===================================================================================
    // 3D Solid Entity Creation
    //===================================================================================

    // 3D solid entities
    bool Create3DSolid(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces,
                      const std::string& layer = "");
    bool CreateBox(const Point3d& corner, double length, double width, double height,
                  const std::string& layer = "");
    bool CreateCylinder(const Point3d& center, double radius, double height, const std::string& layer = "");
    bool CreateSphere(const Point3d& center, double radius, const std::string& layer = "");
    bool CreateCone(const Point3d& center, double baseRadius, double topRadius, double height,
                   const std::string& layer = "");
    bool CreateTorus(const Point3d& center, double majorRadius, double minorRadius,
                    const std::string& layer = "");
    bool CreateWedge(const Point3d& corner, double length, double width, double height,
                    const std::string& layer = "");
    bool CreatePyramid(const Point3d& center, double baseRadius, double topRadius, double height,
                      int sides, const std::string& layer = "");

    // Boolean operations
    bool CreateUnion(const std::vector<std::shared_ptr<void>>& solids, const std::string& layer = "");
    bool CreateSubtraction(const std::shared_ptr<void>& solid1, const std::shared_ptr<void>& solid2,
                          const std::string& layer = "");
    bool CreateIntersection(const std::shared_ptr<void>& solid1, const std::shared_ptr<void>& solid2,
                           const std::string& layer = "");

    // Extrusion and revolution
    bool CreateExtrudedSolid(const std::vector<Point3d>& profile, const Vector3d& direction, double height,
                            const std::string& layer = "");
    bool CreateRevolvedSolid(const std::vector<Point3d>& profile, const Point3d& axisStart,
                            const Point3d& axisEnd, double angle, const std::string& layer = "");
    bool CreateSweptSolid(const std::vector<Point3d>& profile, const std::vector<Point3d>& path,
                         const std::string& layer = "");
    bool CreateLoftedSolid(const std::vector<std::vector<Point3d>>& crossSections, bool ruled,
                          bool closed, const std::string& layer = "");

    //===================================================================================
    // Region Entity Creation
    //===================================================================================

    // Region entities
    bool CreateRegion(const std::vector<std::vector<Point3d>>& boundaries, const std::string& layer = "");
    bool CreateRegionFromCurves(const std::vector<std::shared_ptr<void>>& curves, const std::string& layer = "");

    //===================================================================================
    // Body Entity Creation
    //===================================================================================

    // Body entities
    bool CreateBody(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces,
                   const std::string& layer = "");

private:
    //===================================================================================
    // Internal State
    //===================================================================================

#ifdef REALDWG_AVAILABLE
    AcDbDatabase* m_database;

    // Entity creation helpers
    template<typename T>
    bool CreateAndAddEntity(T* entity, const std::string& layer);

    bool SetEntityProperties(AcDbEntity* entity, const std::string& layer);
    AcDbObjectId AddEntityToModelSpace(AcDbEntity* entity);
    AcDbObjectId GetOrCreateLayer(const std::string& layerName);

    // Conversion helpers
    AcGePoint3d ToAcGePoint3d(const Point3d& point);
    AcGeVector3d ToAcGeVector3d(const Vector3d& vector);
    AcCmColor ToAcCmColor(const Color& color);
#endif

    // Error handling
    void LogError(const std::string& message);
    void LogWarning(const std::string& message);

    // Statistics
    mutable std::unordered_map<DWGEntityType, size_t> m_entityCounts;
    mutable size_t m_totalEntitiesCreated;

public:
    //===================================================================================
    // Surface Entity Creation
    //===================================================================================

    // Basic surfaces
    bool CreatePlaneSurface(const Point3d& corner, const Vector3d& uVector, const Vector3d& vVector,
                           double uLength, double vLength, const std::string& layer = "");
    bool CreateNurbSurface(const std::vector<std::vector<Point3d>>& controlPoints,
                          const std::vector<double>& uKnots, const std::vector<double>& vKnots,
                          const std::vector<std::vector<double>>& weights, int uDegree, int vDegree,
                          const std::string& layer = "");

    // Procedural surfaces
    bool CreateExtrudedSurface(const std::vector<Point3d>& profile, const Vector3d& direction,
                              double height, const std::string& layer = "");
    bool CreateRevolvedSurface(const std::vector<Point3d>& profile, const Point3d& axisStart,
                              const Point3d& axisEnd, double startAngle, double endAngle,
                              const std::string& layer = "");
    bool CreateSweptSurface(const std::vector<Point3d>& profile, const std::vector<Point3d>& path,
                           const std::string& layer = "");
    bool CreateLoftedSurface(const std::vector<std::vector<Point3d>>& crossSections, bool ruled,
                            bool closed, const std::string& layer = "");
    bool CreateBlendSurface(const std::shared_ptr<void>& surface1, const std::shared_ptr<void>& surface2,
                           const std::vector<Point3d>& continuityPoints, const std::string& layer = "");
    bool CreateNetworkSurface(const std::vector<std::vector<Point3d>>& uCurves,
                             const std::vector<std::vector<Point3d>>& vCurves,
                             const std::string& layer = "");
    bool CreatePatchSurface(const std::vector<std::vector<Point3d>>& boundaries,
                           const std::vector<Point3d>& constraintPoints, const std::string& layer = "");
    bool CreateOffsetSurface(const std::shared_ptr<void>& baseSurface, double offsetDistance,
                            const std::string& layer = "");

    //===================================================================================
    // Mesh Entity Creation
    //===================================================================================

    // Mesh entities
    bool CreateMesh(const std::vector<Point3d>& vertices, const std::vector<int>& indices,
                   const std::vector<Vector3d>& normals = {}, const std::string& layer = "");
    bool CreateTriangleMesh(const std::vector<Point3d>& vertices, const std::vector<int>& triangles,
                           const std::string& layer = "");
    bool CreateQuadMesh(const std::vector<Point3d>& vertices, const std::vector<int>& quads,
                       const std::string& layer = "");

    //===================================================================================
    // Block and Reference Entity Creation
    //===================================================================================

    // Block entities
    bool CreateBlockReference(const std::string& blockName, const Point3d& position,
                             const Transform3d& transform = Transform3d::Identity(),
                             const std::string& layer = "");
    bool CreateMInsert(const std::string& blockName, const Point3d& position, int rows, int columns,
                      double rowSpacing, double columnSpacing, const std::string& layer = "");
    bool CreateXRef(const std::string& fileName, const Point3d& position,
                   const Transform3d& transform = Transform3d::Identity(), const std::string& layer = "");

    //===================================================================================
    // Multiline Entity Creation
    //===================================================================================

    // Multiline entities
    bool CreateMLine(const std::vector<Point3d>& points, const std::string& styleName,
                    double scale = 1.0, const std::string& layer = "");

    //===================================================================================
    // Shape and Font Entity Creation
    //===================================================================================

    // Shape entities
    bool CreateShape(const Point3d& position, const std::string& shapeName, double size,
                    double rotation = 0.0, const std::string& layer = "");

    //===================================================================================
    // Image and OLE Entity Creation
    //===================================================================================

    // Image entities
    bool CreateRasterImage(const std::string& imagePath, const Point3d& position,
                          const Vector3d& uVector, const Vector3d& vVector,
                          double width, double height, const std::string& layer = "");
    bool CreateWipeOut(const std::vector<Point3d>& boundary, const std::string& layer = "");
    bool CreateOLE2Frame(const Point3d& position, double width, double height,
                        const std::string& oleData, const std::string& layer = "");

    //===================================================================================
    // Viewport Entity Creation
    //===================================================================================

    // Viewport entities
    bool CreateViewport(const Point3d& center, double width, double height,
                       const Point3d& viewTarget, const Vector3d& viewDirection,
                       double viewHeight, const std::string& layer = "");

    //===================================================================================
    // Table Entity Creation
    //===================================================================================

    // Table entities
    bool CreateTable(const Point3d& position, int rows, int columns, double rowHeight, double columnWidth,
                    const std::vector<std::vector<std::string>>& data, const std::string& layer = "");

    //===================================================================================
    // Field Entity Creation
    //===================================================================================

    // Field entities
    bool CreateField(const std::string& fieldCode, const std::string& format,
                    const std::string& layer = "");

    //===================================================================================
    // Group Entity Creation
    //===================================================================================

    // Group entities
    bool CreateGroup(const std::string& groupName, const std::vector<std::shared_ptr<void>>& entities,
                    const std::string& description = "");

    //===================================================================================
    // Light and Camera Entity Creation
    //===================================================================================

    // Light entities
    bool CreatePointLight(const Point3d& position, const Color& color, double intensity,
                         const std::string& layer = "");
    bool CreateSpotLight(const Point3d& position, const Point3d& target, const Color& color,
                        double intensity, double coneAngle, const std::string& layer = "");
    bool CreateDistantLight(const Vector3d& direction, const Color& color, double intensity,
                           const std::string& layer = "");
    bool CreateWebLight(const Point3d& position, const std::string& webFile, const Color& color,
                       double intensity, const std::string& layer = "");

    // Camera entities
    bool CreateCamera(const Point3d& position, const Point3d& target, const Vector3d& upVector,
                     double fieldOfView, const std::string& layer = "");

    //===================================================================================
    // Helix Entity Creation
    //===================================================================================

    // Helix entities
    bool CreateHelix(const Point3d& center, const Vector3d& axis, double baseRadius, double topRadius,
                    double height, double turns, bool rightHanded, const std::string& layer = "");

    //===================================================================================
    // Geographic Entity Creation
    //===================================================================================

    // Geographic entities
    bool CreateGeoData(const Point3d& designPoint, const Point3d& referencePoint,
                      const std::string& coordinateSystem, const std::string& layer = "");
    bool CreateGeoPositionMarker(const Point3d& position, double latitude, double longitude,
                                double elevation, const std::string& layer = "");

    //===================================================================================
    // Path and Motion Entity Creation
    //===================================================================================

    // Path entities
    bool CreateNamedPath(const std::string& pathName, const std::vector<Point3d>& points,
                        const std::string& layer = "");
    bool CreateMotionPath(const std::vector<Point3d>& points, const std::vector<double>& times,
                         const std::string& layer = "");

    //===================================================================================
    // Section Entity Creation
    //===================================================================================

    // Section entities
    bool CreateSection(const Point3d& start, const Point3d& end, const Vector3d& direction,
                      const std::string& layer = "");

    //===================================================================================
    // Tolerance Entity Creation
    //===================================================================================

    // Tolerance entities
    bool CreateTolerance(const Point3d& position, const std::string& toleranceText,
                        const Vector3d& direction, const std::string& layer = "");
    bool CreateFCF(const Point3d& position, const std::string& featureControlFrame,
                  const Vector3d& direction, const std::string& layer = "");

    //===================================================================================
    // Underlay Reference Entity Creation
    //===================================================================================

    // Underlay entities
    bool CreateDwfReference(const std::string& fileName, const Point3d& position,
                           const Transform3d& transform, const std::string& layer = "");
    bool CreateDgnReference(const std::string& fileName, const Point3d& position,
                           const Transform3d& transform, const std::string& layer = "");
    bool CreatePdfReference(const std::string& fileName, const Point3d& position,
                           const Transform3d& transform, const std::string& layer = "");

    //===================================================================================
    // Point Cloud Entity Creation
    //===================================================================================

    // Point cloud entities
    bool CreatePointCloud(const std::string& fileName, const Point3d& position,
                         const Transform3d& transform, const std::string& layer = "");
    bool CreatePointCloudEx(const std::string& fileName, const Point3d& position,
                           const Transform3d& transform, const std::string& layer = "");

    //===================================================================================
    // Utility Methods
    //===================================================================================

    // Entity management
    std::unordered_map<DWGEntityType, size_t> GetEntityCounts() const;
    size_t GetTotalEntitiesCreated() const;
    void ResetStatistics();

    // Entity type detection
    DWGEntityType GetEntityType(const std::shared_ptr<void>& entity) const;
    std::string GetEntityTypeName(DWGEntityType type) const;

    // Validation
    bool ValidateEntityData(DWGEntityType type, const std::vector<Point3d>& points) const;
    bool ValidateLayer(const std::string& layerName) const;

    // Database management
    void SetDatabase(void* database);
    void* GetDatabase() const;
};