#include "DWGMlineProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/geline3d.h>
#include <realdwg/ge/gearc3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// MlineElement Implementation
//=======================================================================================

bool MlineElement::IsValid() const {
    // Validate offset
    if (!std::isfinite(offset)) {
        return false;
    }
    
    // Validate color
    if (!color.IsValid()) {
        return false;
    }
    
    // Validate line weight
    if (!std::isfinite(lineWeight) || lineWeight < 0.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// MlineStyle Implementation
//=======================================================================================

bool MlineStyle::IsValid() const {
    // Validate name
    if (name.empty()) {
        return false;
    }
    
    // Validate description
    // Description can be empty, so no validation needed
    
    // Validate elements
    if (elements.empty()) {
        return false;
    }
    
    for (const auto& element : elements) {
        if (!element.IsValid()) {
            return false;
        }
    }
    
    // Validate start and end caps
    if (!std::isfinite(startAngle) || !std::isfinite(endAngle)) {
        return false;
    }
    
    return true;
}

double MlineStyle::GetMaxOffset() const {
    if (elements.empty()) {
        return 0.0;
    }
    
    auto maxElement = std::max_element(elements.begin(), elements.end(),
        [](const MlineElement& a, const MlineElement& b) {
            return a.offset < b.offset;
        });
    
    return maxElement->offset;
}

double MlineStyle::GetMinOffset() const {
    if (elements.empty()) {
        return 0.0;
    }
    
    auto minElement = std::min_element(elements.begin(), elements.end(),
        [](const MlineElement& a, const MlineElement& b) {
            return a.offset < b.offset;
        });
    
    return minElement->offset;
}

double MlineStyle::GetTotalWidth() const {
    return GetMaxOffset() - GetMinOffset();
}

//=======================================================================================
// MlineVertex Implementation
//=======================================================================================

bool MlineVertex::IsValid() const {
    // Validate position
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z)) {
        return false;
    }
    
    // Validate direction
    if (!std::isfinite(direction.x) || !std::isfinite(direction.y) || !std::isfinite(direction.z)) {
        return false;
    }
    
    // Validate miter direction
    if (!std::isfinite(miterDirection.x) || !std::isfinite(miterDirection.y) || !std::isfinite(miterDirection.z)) {
        return false;
    }
    
    // Validate line parameters
    for (const auto& param : lineParameters) {
        if (!std::isfinite(param)) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// MlineGeometry Implementation
//=======================================================================================

bool MlineGeometry::IsValid() const {
    // Validate vertices
    if (vertices.size() < 2) {
        return false;
    }
    
    for (const auto& vertex : vertices) {
        if (!vertex.IsValid()) {
            return false;
        }
    }
    
    // Validate style
    if (!style.IsValid()) {
        return false;
    }
    
    // Validate scale
    if (!std::isfinite(scale) || scale <= 0.0) {
        return false;
    }
    
    // Validate normal
    if (!std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    return true;
}

double MlineGeometry::CalculateLength() const {
    if (vertices.size() < 2) {
        return 0.0;
    }
    
    double totalLength = 0.0;
    
    for (size_t i = 1; i < vertices.size(); ++i) {
        const Point3d& p1 = vertices[i-1].position;
        const Point3d& p2 = vertices[i].position;
        
        double dx = p2.x - p1.x;
        double dy = p2.y - p1.y;
        double dz = p2.z - p1.z;
        
        totalLength += std::sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    return totalLength;
}

BoundingBox3D MlineGeometry::CalculateBounds() const {
    BoundingBox3D bounds;
    
    if (vertices.empty()) {
        return bounds;
    }
    
    // Calculate bounds considering the mline width
    double halfWidth = style.GetTotalWidth() * scale * 0.5;
    
    for (const auto& vertex : vertices) {
        // Add vertex position with width consideration
        Point3d minPoint(vertex.position.x - halfWidth, vertex.position.y - halfWidth, vertex.position.z);
        Point3d maxPoint(vertex.position.x + halfWidth, vertex.position.y + halfWidth, vertex.position.z);
        
        bounds.AddPoint(minPoint);
        bounds.AddPoint(maxPoint);
    }
    
    return bounds;
}

//=======================================================================================
// DWGMlineProcessor Implementation
//=======================================================================================

DWGMlineProcessor::DWGMlineProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_mlineTolerance(1e-6)
    , m_vertexTolerance(1e-8)
    , m_enableMlineValidation(true)
    , m_enableStyleValidation(true)
    , m_autoRepairMlines(true)
    , m_maxMlineVertices(10000)
    , m_maxMlineElements(100)
{
    CreateDefaultMlineStyles();
}

DWGProcessingStatus DWGMlineProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract mline geometry from element
        // For demonstration, we'll create a sample mline
        
        if (element.type == ElementType::GeometricElement) {
            MlineGeometry mline;
            
            // Create simple mline with 3 vertices
            MlineVertex v1, v2, v3;
            v1.position = Point3d(0, 0, 0);
            v1.direction = Vector3d(1, 0, 0);
            v1.miterDirection = Vector3d(0, 1, 0);
            
            v2.position = Point3d(50, 0, 0);
            v2.direction = Vector3d(1, 1, 0);
            v2.miterDirection = Vector3d(-1, 1, 0);
            
            v3.position = Point3d(100, 50, 0);
            v3.direction = Vector3d(0, 1, 0);
            v3.miterDirection = Vector3d(-1, 0, 0);
            
            mline.vertices = {v1, v2, v3};
            mline.style = GetDefaultMlineStyle();
            mline.scale = 1.0;
            mline.normal = Vector3d(0, 0, 1);
            mline.isClosed = false;
            
            return ProcessMline(mline, "Mlines");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing mline entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGMlineProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGMlineProcessor::ProcessMline(const MlineGeometry& geometry, const std::string& layer) {
    // Validate mline geometry
    auto validation = ValidateMlineGeometry(geometry);
    if (!validation.isValid) {
        LogError("Mline geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    MlineGeometry transformedGeometry = geometry;
    TransformMlineGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbMline* mline = CreateDWGMline(transformedGeometry);
        if (!mline) {
            LogError("Failed to create DWG mline entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetMlineProperties(mline, transformedGeometry)) {
            delete mline;
            LogError("Failed to set mline properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(mline, layer)) {
            delete mline;
            LogError("Failed to set mline entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(mline)) {
            delete mline;
            LogError("Failed to add mline to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedMlines++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG mline: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - mline creation skipped");
    m_processedMlines++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Style Management
//=======================================================================================

bool DWGMlineProcessor::CreateMlineStyle(const MlineStyle& style) {
    // Validate style
    if (!style.IsValid()) {
        LogError("Invalid mline style: " + style.name);
        return false;
    }
    
    // Check if style already exists
    if (m_mlineStyles.find(style.name) != m_mlineStyles.end()) {
        LogWarning("Mline style already exists: " + style.name);
        return true;
    }
    
    // Store style
    m_mlineStyles[style.name] = style;
    LogInfo("Created mline style: " + style.name);
    return true;
}

bool DWGMlineProcessor::UpdateMlineStyle(const MlineStyle& style) {
    auto it = m_mlineStyles.find(style.name);
    if (it == m_mlineStyles.end()) {
        LogError("Mline style not found for update: " + style.name);
        return false;
    }
    
    if (!style.IsValid()) {
        LogError("Invalid mline style for update: " + style.name);
        return false;
    }
    
    it->second = style;
    LogInfo("Updated mline style: " + style.name);
    return true;
}

bool DWGMlineProcessor::DeleteMlineStyle(const std::string& styleName) {
    // Don't allow deletion of standard style
    if (styleName == "Standard") {
        LogError("Cannot delete standard mline style");
        return false;
    }
    
    auto it = m_mlineStyles.find(styleName);
    if (it == m_mlineStyles.end()) {
        LogError("Mline style not found for deletion: " + styleName);
        return false;
    }
    
    m_mlineStyles.erase(it);
    LogInfo("Deleted mline style: " + styleName);
    return true;
}

MlineStyle DWGMlineProcessor::GetMlineStyle(const std::string& styleName) const {
    auto it = m_mlineStyles.find(styleName);
    if (it != m_mlineStyles.end()) {
        return it->second;
    }
    
    // Return default style if not found
    return GetDefaultMlineStyle();
}

std::vector<std::string> DWGMlineProcessor::GetAvailableMlineStyles() const {
    std::vector<std::string> styleNames;
    styleNames.reserve(m_mlineStyles.size());
    
    for (const auto& pair : m_mlineStyles) {
        styleNames.push_back(pair.first);
    }
    
    std::sort(styleNames.begin(), styleNames.end());
    return styleNames;
}

//=======================================================================================
// Validation Methods
//=======================================================================================

MlineValidationResult DWGMlineProcessor::ValidateMlineGeometry(const MlineGeometry& geometry) const {
    MlineValidationResult result;
    result.isValid = true;
    
    // Validate vertices
    result.hasValidVertices = ValidateMlineVertices(geometry.vertices);
    if (!result.hasValidVertices) {
        result.AddMlineError("Invalid mline vertices");
    }
    
    // Validate style
    result.hasValidStyle = ValidateMlineStyle(geometry.style);
    if (!result.hasValidStyle) {
        result.AddMlineError("Invalid mline style");
    }
    
    // Validate scale
    result.hasValidScale = std::isfinite(geometry.scale) && geometry.scale > 0.0;
    if (!result.hasValidScale) {
        result.AddMlineError("Invalid mline scale");
    }
    
    // Validate normal
    result.hasValidNormal = std::isfinite(geometry.normal.x) && 
                           std::isfinite(geometry.normal.y) && 
                           std::isfinite(geometry.normal.z);
    if (!result.hasValidNormal) {
        result.AddMlineError("Invalid normal vector");
    }
    
    // Calculate metrics
    result.vertexCount = static_cast<int>(geometry.vertices.size());
    result.elementCount = static_cast<int>(geometry.style.elements.size());
    result.totalLength = geometry.CalculateLength();
    
    // Check limits
    if (result.vertexCount > static_cast<int>(m_maxMlineVertices)) {
        result.AddMlineError("Too many vertices (limit: " + std::to_string(m_maxMlineVertices) + ")");
        result.isValid = false;
    }
    
    if (result.elementCount > static_cast<int>(m_maxMlineElements)) {
        result.AddMlineError("Too many elements (limit: " + std::to_string(m_maxMlineElements) + ")");
        result.isValid = false;
    }
    
    if (result.totalLength < m_mlineTolerance) {
        result.AddMlineWarning("Mline length is very small: " + std::to_string(result.totalLength));
    }
    
    return result;
}

bool DWGMlineProcessor::ValidateMlineVertices(const std::vector<MlineVertex>& vertices) const {
    if (vertices.size() < 2) {
        return false;
    }
    
    for (const auto& vertex : vertices) {
        if (!vertex.IsValid()) {
            return false;
        }
    }
    
    // Check that consecutive vertices are not coincident
    for (size_t i = 1; i < vertices.size(); ++i) {
        const Point3d& p1 = vertices[i-1].position;
        const Point3d& p2 = vertices[i].position;
        
        double dx = p2.x - p1.x;
        double dy = p2.y - p1.y;
        double dz = p2.z - p1.z;
        double distance = std::sqrt(dx * dx + dy * dy + dz * dz);
        
        if (distance < m_vertexTolerance) {
            return false;
        }
    }
    
    return true;
}

bool DWGMlineProcessor::ValidateMlineStyle(const MlineStyle& style) const {
    return style.IsValid();
}

//=======================================================================================
// Helper Methods
//=======================================================================================

void DWGMlineProcessor::TransformMlineGeometry(MlineGeometry& geometry) const {
    // Transform vertices
    for (auto& vertex : geometry.vertices) {
        vertex.position = TransformPoint(vertex.position);
        vertex.direction = TransformVector(vertex.direction);
        vertex.miterDirection = TransformVector(vertex.miterDirection);
    }
    
    // Transform normal
    geometry.normal = TransformVector(geometry.normal);
}

void DWGMlineProcessor::CreateDefaultMlineStyles() {
    // Create standard mline style
    MlineStyle standardStyle;
    standardStyle.name = "Standard";
    standardStyle.description = "Standard mline style";
    
    // Add two elements for a double line
    MlineElement element1, element2;
    element1.offset = 0.5;
    element1.color = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    element1.lineTypeName = "Continuous";
    element1.lineWeight = 0.25;
    
    element2.offset = -0.5;
    element2.color = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    element2.lineTypeName = "Continuous";
    element2.lineWeight = 0.25;
    
    standardStyle.elements = {element1, element2};
    standardStyle.showMiters = true;
    standardStyle.startSquareCap = false;
    standardStyle.startInnerArcs = true;
    standardStyle.startRoundCap = false;
    standardStyle.endSquareCap = false;
    standardStyle.endInnerArcs = true;
    standardStyle.endRoundCap = false;
    standardStyle.startAngle = 90.0 * M_PI / 180.0; // 90 degrees
    standardStyle.endAngle = 90.0 * M_PI / 180.0;   // 90 degrees
    
    m_mlineStyles["Standard"] = standardStyle;
    
    // Create single line style
    MlineStyle singleStyle;
    singleStyle.name = "Single";
    singleStyle.description = "Single line mline style";
    
    MlineElement singleElement;
    singleElement.offset = 0.0;
    singleElement.color = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    singleElement.lineTypeName = "Continuous";
    singleElement.lineWeight = 0.25;
    
    singleStyle.elements = {singleElement};
    singleStyle.showMiters = false;
    singleStyle.startSquareCap = false;
    singleStyle.startInnerArcs = false;
    singleStyle.startRoundCap = false;
    singleStyle.endSquareCap = false;
    singleStyle.endInnerArcs = false;
    singleStyle.endRoundCap = false;
    singleStyle.startAngle = 90.0 * M_PI / 180.0;
    singleStyle.endAngle = 90.0 * M_PI / 180.0;
    
    m_mlineStyles["Single"] = singleStyle;
}

MlineStyle DWGMlineProcessor::GetDefaultMlineStyle() const {
    auto it = m_mlineStyles.find("Standard");
    if (it != m_mlineStyles.end()) {
        return it->second;
    }
    
    // Create minimal default style if Standard doesn't exist
    MlineStyle defaultStyle;
    defaultStyle.name = "Default";
    defaultStyle.description = "Default mline style";
    
    MlineElement element;
    element.offset = 0.0;
    element.color = Color(1.0f, 1.0f, 1.0f, 1.0f);
    element.lineTypeName = "Continuous";
    element.lineWeight = 0.25;
    
    defaultStyle.elements = {element};
    defaultStyle.showMiters = false;
    defaultStyle.startSquareCap = false;
    defaultStyle.startInnerArcs = false;
    defaultStyle.startRoundCap = false;
    defaultStyle.endSquareCap = false;
    defaultStyle.endInnerArcs = false;
    defaultStyle.endRoundCap = false;
    defaultStyle.startAngle = 90.0 * M_PI / 180.0;
    defaultStyle.endAngle = 90.0 * M_PI / 180.0;
    
    return defaultStyle;
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbMline* DWGMlineProcessor::CreateDWGMline(const MlineGeometry& geometry) const {
    try {
        AcDbMline* mline = new AcDbMline();
        
        // Set mline style (would need to create AcDbMlineStyle first)
        // For now, just set basic properties
        
        // Set scale
        mline->setScale(geometry.scale);
        
        // Set normal
        AcGeVector3d normal(geometry.normal.x, geometry.normal.y, geometry.normal.z);
        mline->setNormal(normal);
        
        // Add vertices
        for (const auto& vertex : geometry.vertices) {
            AcGePoint3d point(vertex.position.x, vertex.position.y, vertex.position.z);
            AcGeVector3d direction(vertex.direction.x, vertex.direction.y, vertex.direction.z);
            AcGeVector3d miterDir(vertex.miterDirection.x, vertex.miterDirection.y, vertex.miterDirection.z);
            
            mline->appendSeg(point);
        }
        
        // Set closed flag
        mline->setClosedMline(geometry.isClosed);
        
        return mline;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGMlineProcessor::SetMlineProperties(AcDbMline* mline, const MlineGeometry& geometry) const {
    if (!mline) {
        return false;
    }
    
    try {
        // Set justification
        switch (geometry.justification) {
            case MlineGeometry::Justification::Top:
                mline->setJustification(AcDb::kTop);
                break;
            case MlineGeometry::Justification::Zero:
                mline->setJustification(AcDb::kZero);
                break;
            case MlineGeometry::Justification::Bottom:
                mline->setJustification(AcDb::kBottom);
                break;
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGMlineProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGMlineProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
