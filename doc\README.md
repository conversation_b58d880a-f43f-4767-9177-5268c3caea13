# RealDwgFileIO Framework Architecture Documentation

This directory contains comprehensive documentation for the RealDwgFileIO framework architecture.

## Documentation Structure

### Core Documentation
- [Framework Summary](framework-summary.md) - **START HERE** - Complete framework overview
- [Overall Architecture](architecture-overview.md) - High-level system architecture and design patterns
- [Core Components](core-components.md) - Detailed breakdown of main framework components
- [Conversion System](conversion-system.md) - DWG ↔ DGN conversion architecture
- [Entity Conversion](entity-conversion.md) - Individual entity type conversion mappings
- [Data Flow](data-flow.md) - Complete data flow diagrams and processing pipelines

### Additional Documentation (To Be Created)
- [File Handling](file-handling.md) - File I/O and format management
- [View System](view-system.md) - Viewport and view conversion system
- [Symbology System](symbology-system.md) - Color, linestyle, and material conversion
- [Extension Points](extension-points.md) - Plugin and customization architecture

## Quick Start

For a quick understanding of the framework:
1. **Start with [Framework Summary](framework-summary.md)** - Complete overview and executive summary
2. Review [Overall Architecture](architecture-overview.md) - System design and patterns
3. Examine [Core Components](core-components.md) - Key classes and modules
4. Study [Conversion System](conversion-system.md) - Main conversion logic and flow
5. Explore [Entity Conversion](entity-conversion.md) - Specific entity mapping details
6. Understand [Data Flow](data-flow.md) - Processing pipelines and threading

## Framework Overview

The RealDwgFileIO framework is a comprehensive system for bidirectional conversion between AutoCAD DWG/DXF files and Bentley DGN files. It provides:

- **File Format Support**: DWG, DXF, and DGN file formats
- **Bidirectional Conversion**: Complete DWG ↔ DGN conversion capabilities
- **Entity Mapping**: Comprehensive mapping between AutoCAD and MicroStation entities
- **View System**: Viewport and view conversion with display styles
- **Symbology**: Color, linestyle, material, and visual style conversion
- **Extension Architecture**: Plugin system for custom entity types and conversion logic

## Key Technologies

- **RealDWG SDK**: Autodesk's official DWG/DXF access library
- **DgnPlatform**: Bentley's DGN file access and manipulation framework
- **MicroStation Platform**: Integration with MicroStation's geometry and display systems
- **ACIS/Parasolid**: 3D solid modeling kernel integration
- **Multi-threading**: Parallel processing support for large file conversions
