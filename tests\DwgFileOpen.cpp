/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/tests/DwgFileOpen.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include "DwgUnitTestsPCH.h"

DwgTestHost* DwgFileOpen::s_dwgExampleHost = nullptr;

void DwgTestHost::_FatalError (WCharCP format, ...)
    {
    va_list     varArgs;
    va_start (varArgs, format);
    ASSERT_TRUE (false) << format;
    va_end (varArgs);
    }

void DwgFileOpen::InitHosts ()
    {
    if (nullptr != s_dwgExampleHost)
        return;

    BeFileName fontPath (_wgetenv (L"OutRoot"));
    fontPath
        .AppendToPath (L"Winx64")
        .AppendToPath (L"Product")
        .AppendToPath (L"DwgExampleHost")
        .AppendToPath (L"Fonts");

    EXPECT_TRUE (BeFileName::DoesPathExist (fontPath.c_str ()));
    EXPECT_TRUE (SUCCESS == ConfigurationManager::DefineVariable (L"MS_FONTPATH", fontPath.c_str ()));

    BeFileName fontCfgFile (fontPath);
    fontCfgFile
        .AppendToPath (L"MstnFontConfig")
        .AppendExtension (L"xml");

    EXPECT_TRUE (BeFileName::DoesPathExist (fontCfgFile.c_str ()));
    EXPECT_TRUE (SUCCESS == ConfigurationManager::DefineVariable (L"MS_FONTCONFIGFILE", fontCfgFile.c_str ()));

    // initialize DgnPlatformHost
    HostInterface::DgnConsoleAppHost::Initialize ();
    if (!Raster::RasterCoreLib::IsInitialized ())
        Raster::RasterCoreLib::Initialize (*new Raster::RasterCoreLib::Host);

    BeFileName TranseedFileName (_wgetenv (L"OutRoot"));
    TranseedFileName
        .AppendToPath (L"Winx64")
        .AppendToPath (L"Product")
        .AppendToPath (L"DwgExampleHost")
        .AppendToPath (L"transeed")
        .AppendExtension (L"dgn");

    EXPECT_TRUE (BeFileName::DoesPathExist (TranseedFileName.c_str ()));
    EXPECT_TRUE (SUCCESS == ConfigurationManager::DefineVariable (L"MS_TRANSEED", TranseedFileName.c_str ()));

    // create and initialize DwgPlatformHost
    s_dwgExampleHost = new DwgTestHost ();

    // register this app as a file handler so DgnPlatform can demand load us, but do not want file ECProperties:
    RealDwg::DwgPlatformHost::Initialize (*s_dwgExampleHost, true, false);
    }

DwgFileOpen::DwgFileOpen (DwgFileOpenParams const& params)
    :m_params (params)
    {}

DgnFilePtr DwgFileOpen::OpenDwgFile (BeFileNameCR dwgFileName)
    {
    InitHosts ();

    bool fileExists = !dwgFileName.IsEmpty () && BeFileName::DoesPathExist (dwgFileName.c_str ());
    EXPECT_TRUE (fileExists);
    if (!fileExists)
        return nullptr;

    // prepare a new DgnDocument to open a DWG file
    DgnDocumentPtr  dgnDocument = DgnDocument::CreateForLocalFile (dwgFileName.GetName ());
    if (!dgnDocument.IsValid ())
        return nullptr;

    // create a DgnFile
    DgnFilePtr      dwgFile = DgnFile::Create (*dgnDocument.get (), m_params.m_openMode);
    if (!dwgFile.IsValid ())
        return nullptr;

    // load the DWG file and fill the dictionary model
    StatusInt       status;
    if (DGNFILE_STATUS_Success != dwgFile->LoadDgnFile (&status))
        {
        EXPECT_TRUE (false) << L"DgnFile::LoadDgnFile failed with status " << status;
        return nullptr;
        }

    // Turn on/off the multiprocessing file open
    if (m_params.m_useMultiProcess)
        {
        ConfigurationManager::UndefineVariable (L"MS_DWG_OPEN_SINGLEPROCESSING");
        ConfigurationManager::DefineVariable (L"MS_DWG_USEACISOUT", L"1");
        }
    else
        {
        ConfigurationManager::DefineVariable (L"MS_DWG_OPEN_SINGLEPROCESSING", L"1");
        //ConfigurationManager::UndefineVariable (L"MS_DWG_USEACISOUT");
        ConfigurationManager::DefineVariable (L"MS_DWG_USEACISOUT", L"1");
        }

    // fill the default model section
    if (NULL == dwgFile->LoadRootModelById (NULL, dwgFile->GetDefaultModelId (), true))
        return nullptr;

    // load DGN ECSchemas - requires folder \ECSchemas\ to exist!
    DgnECManagerR   dgnECManager = DgnECManager::GetManager ();

    bvector<SchemaInfo> schemaInfos;
    dgnECManager.DiscoverSchemas (schemaInfos, *dwgFile, ECSCHEMAPERSISTENCE_Stored);

    if (schemaInfos.size () == 0)
        printf ("No DGNEC schemas are loaded!\n");

    if (WString::IsNullOrEmpty (m_params.m_outputDgn.c_str ()))
        return dwgFile;

    // save the loaded file as a V8 DGN file
    if (WString::IsNullOrEmpty (m_params.m_outputDgn.c_str ()))
        return dwgFile;

    BeFileName::CreateNewDirectory (BeFileName::GetDirectoryName (m_params.m_outputDgn.c_str ()).c_str ());

    auto saveToDgn = dwgFile->DoSaveTo (m_params.m_outputDgn.c_str (), DgnFileFormatType::V8);
    EXPECT_EQ (saveToDgn, BSISUCCESS);
    if (saveToDgn != BSISUCCESS)
        return nullptr;
    return dwgFile;
    }
