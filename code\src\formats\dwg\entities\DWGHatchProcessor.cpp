#include "DWGHatchProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbpolyline.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gecurve2d.h>
#include <realdwg/ge/geline2d.h>
#include <realdwg/ge/gearc2d.h>
#include <realdwg/ge/geellip2d.h>
#include <realdwg/ge/gespline2d.h>
#endif

#include <algorithm>
#include <cmath>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// HatchBoundary Implementation
//=======================================================================================

bool HatchBoundary::IsValid() const {
    if (loops.empty()) {
        return false;
    }
    
    for (const auto& loop : loops) {
        if (loop.edges.empty()) {
            return false;
        }
        
        for (const auto& edge : loop.edges) {
            if (!edge.IsValid()) {
                return false;
            }
        }
    }
    
    return true;
}

bool HatchBoundary::IsClosed() const {
    for (const auto& loop : loops) {
        if (!loop.IsClosed()) {
            return false;
        }
    }
    return true;
}

double HatchBoundary::CalculateArea() const {
    double totalArea = 0.0;
    
    for (const auto& loop : loops) {
        double loopArea = loop.CalculateArea();
        if (loop.isOuter) {
            totalArea += loopArea;
        } else {
            totalArea -= loopArea; // Subtract island areas
        }
    }
    
    return std::abs(totalArea);
}

//=======================================================================================
// HatchLoop Implementation
//=======================================================================================

bool HatchLoop::IsValid() const {
    if (edges.empty()) {
        return false;
    }
    
    for (const auto& edge : edges) {
        if (!edge.IsValid()) {
            return false;
        }
    }
    
    return true;
}

bool HatchLoop::IsClosed() const {
    if (edges.empty()) {
        return false;
    }
    
    // Check if first and last points connect
    const double tolerance = 1e-6;
    Point3d firstStart = edges.front().GetStartPoint();
    Point3d lastEnd = edges.back().GetEndPoint();
    
    double distance = std::sqrt(
        (lastEnd.x - firstStart.x) * (lastEnd.x - firstStart.x) +
        (lastEnd.y - firstStart.y) * (lastEnd.y - firstStart.y) +
        (lastEnd.z - firstStart.z) * (lastEnd.z - firstStart.z)
    );
    
    return distance < tolerance;
}

double HatchLoop::CalculateArea() const {
    if (edges.empty()) {
        return 0.0;
    }
    
    // Use Green's theorem for polygon area calculation
    double area = 0.0;
    
    for (const auto& edge : edges) {
        switch (edge.type) {
            case HatchEdge::Type::Line: {
                Point3d start = edge.GetStartPoint();
                Point3d end = edge.GetEndPoint();
                area += (start.x * end.y - end.x * start.y);
                break;
            }
            
            case HatchEdge::Type::Arc: {
                // Approximate arc with line segments for area calculation
                std::vector<Point3d> points = edge.TessellateArc(16);
                for (size_t i = 1; i < points.size(); ++i) {
                    area += (points[i-1].x * points[i].y - points[i].x * points[i-1].y);
                }
                break;
            }
            
            default:
                // For complex curves, tessellate and calculate
                std::vector<Point3d> points = edge.Tessellate(32);
                for (size_t i = 1; i < points.size(); ++i) {
                    area += (points[i-1].x * points[i].y - points[i].x * points[i-1].y);
                }
                break;
        }
    }
    
    return std::abs(area) * 0.5;
}

//=======================================================================================
// HatchEdge Implementation
//=======================================================================================

bool HatchEdge::IsValid() const {
    // Validate start and end points
    if (!std::isfinite(startPoint.x) || !std::isfinite(startPoint.y) || !std::isfinite(startPoint.z) ||
        !std::isfinite(endPoint.x) || !std::isfinite(endPoint.y) || !std::isfinite(endPoint.z)) {
        return false;
    }
    
    // Type-specific validation
    switch (type) {
        case Type::Line:
            return true; // Lines are always valid if points are valid
            
        case Type::Arc:
            return std::isfinite(arc.center.x) && std::isfinite(arc.center.y) && std::isfinite(arc.center.z) &&
                   std::isfinite(arc.radius) && arc.radius > 0.0 &&
                   std::isfinite(arc.startAngle) && std::isfinite(arc.endAngle);
                   
        case Type::Ellipse:
            return std::isfinite(ellipse.center.x) && std::isfinite(ellipse.center.y) && std::isfinite(ellipse.center.z) &&
                   std::isfinite(ellipse.majorRadius) && ellipse.majorRadius > 0.0 &&
                   std::isfinite(ellipse.minorRadius) && ellipse.minorRadius > 0.0 &&
                   std::isfinite(ellipse.startAngle) && std::isfinite(ellipse.endAngle);
                   
        case Type::Spline:
            return spline.degree > 0 && !spline.controlPoints.empty() && !spline.knots.empty();
            
        default:
            return false;
    }
}

Point3d HatchEdge::GetStartPoint() const {
    return startPoint;
}

Point3d HatchEdge::GetEndPoint() const {
    return endPoint;
}

std::vector<Point3d> HatchEdge::Tessellate(int segments) const {
    std::vector<Point3d> points;
    
    switch (type) {
        case Type::Line:
            points.push_back(startPoint);
            points.push_back(endPoint);
            break;
            
        case Type::Arc:
            points = TessellateArc(segments);
            break;
            
        case Type::Ellipse:
            points = TessellateEllipse(segments);
            break;
            
        case Type::Spline:
            points = TessellateSpline(segments);
            break;
    }
    
    return points;
}

std::vector<Point3d> HatchEdge::TessellateArc(int segments) const {
    std::vector<Point3d> points;
    
    if (segments < 2) segments = 2;
    
    double deltaAngle = (arc.endAngle - arc.startAngle) / (segments - 1);
    
    for (int i = 0; i < segments; ++i) {
        double angle = arc.startAngle + i * deltaAngle;
        Point3d point(
            arc.center.x + arc.radius * std::cos(angle),
            arc.center.y + arc.radius * std::sin(angle),
            arc.center.z
        );
        points.push_back(point);
    }
    
    return points;
}

std::vector<Point3d> HatchEdge::TessellateEllipse(int segments) const {
    std::vector<Point3d> points;
    
    if (segments < 2) segments = 2;
    
    double deltaAngle = (ellipse.endAngle - ellipse.startAngle) / (segments - 1);
    
    for (int i = 0; i < segments; ++i) {
        double angle = ellipse.startAngle + i * deltaAngle;
        Point3d point(
            ellipse.center.x + ellipse.majorRadius * std::cos(angle),
            ellipse.center.y + ellipse.minorRadius * std::sin(angle),
            ellipse.center.z
        );
        points.push_back(point);
    }
    
    return points;
}

std::vector<Point3d> HatchEdge::TessellateSpline(int segments) const {
    std::vector<Point3d> points;
    
    if (spline.controlPoints.empty()) {
        return points;
    }
    
    // Simple linear interpolation for demonstration
    // Real implementation would use proper NURBS evaluation
    if (segments < 2) segments = 2;
    
    for (int i = 0; i < segments; ++i) {
        double t = static_cast<double>(i) / (segments - 1);
        
        // Linear interpolation between first and last control points
        Point3d point(
            spline.controlPoints.front().x + t * (spline.controlPoints.back().x - spline.controlPoints.front().x),
            spline.controlPoints.front().y + t * (spline.controlPoints.back().y - spline.controlPoints.front().y),
            spline.controlPoints.front().z + t * (spline.controlPoints.back().z - spline.controlPoints.front().z)
        );
        points.push_back(point);
    }
    
    return points;
}

//=======================================================================================
// HatchPattern Implementation
//=======================================================================================

bool HatchPattern::IsValid() const {
    if (name.empty()) {
        return false;
    }
    
    if (!std::isfinite(scale) || scale <= 0.0) {
        return false;
    }
    
    if (!std::isfinite(angle)) {
        return false;
    }
    
    // Validate pattern lines
    for (const auto& line : lines) {
        if (!std::isfinite(line.angle) || !std::isfinite(line.basePoint.x) || 
            !std::isfinite(line.basePoint.y) || !std::isfinite(line.offset.x) || 
            !std::isfinite(line.offset.y)) {
            return false;
        }
        
        for (double dash : line.dashes) {
            if (!std::isfinite(dash)) {
                return false;
            }
        }
    }
    
    return true;
}

//=======================================================================================
// HatchGeometry Implementation
//=======================================================================================

bool HatchGeometry::IsValid() const {
    if (!boundary.IsValid()) {
        return false;
    }
    
    if (!std::isfinite(elevation)) {
        return false;
    }
    
    if (!std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    // Validate pattern if solid fill is disabled
    if (!isSolidFill && !pattern.IsValid()) {
        return false;
    }
    
    return true;
}

double HatchGeometry::CalculateArea() const {
    return boundary.CalculateArea();
}

//=======================================================================================
// DWGHatchProcessor Implementation
//=======================================================================================

DWGHatchProcessor::DWGHatchProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_hatchTolerance(1e-6)
    , m_boundaryTolerance(1e-8)
    , m_enableBoundaryOptimization(true)
    , m_enablePatternValidation(true)
    , m_autoRepairBoundaries(true)
    , m_maxBoundaryLoops(1000)
    , m_maxBoundaryEdges(10000)
{
}

DWGProcessingStatus DWGHatchProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract hatch geometry from element
        // For demonstration, we'll create a sample rectangular hatch
        
        if (element.type == ElementType::GeometricElement) {
            HatchGeometry hatch;
            
            // Create rectangular boundary
            HatchLoop loop;
            loop.isOuter = true;
            
            // Add four line edges for rectangle
            HatchEdge edge1, edge2, edge3, edge4;
            edge1.type = HatchEdge::Type::Line;
            edge1.startPoint = Point3d(0, 0, 0);
            edge1.endPoint = Point3d(100, 0, 0);
            
            edge2.type = HatchEdge::Type::Line;
            edge2.startPoint = Point3d(100, 0, 0);
            edge2.endPoint = Point3d(100, 50, 0);
            
            edge3.type = HatchEdge::Type::Line;
            edge3.startPoint = Point3d(100, 50, 0);
            edge3.endPoint = Point3d(0, 50, 0);
            
            edge4.type = HatchEdge::Type::Line;
            edge4.startPoint = Point3d(0, 50, 0);
            edge4.endPoint = Point3d(0, 0, 0);
            
            loop.edges = {edge1, edge2, edge3, edge4};
            hatch.boundary.loops = {loop};
            
            // Set hatch properties
            hatch.isSolidFill = false;
            hatch.pattern.name = "ANSI31";
            hatch.pattern.scale = 1.0;
            hatch.pattern.angle = 45.0 * M_PI / 180.0; // 45 degrees
            hatch.elevation = 0.0;
            hatch.normal = Vector3d(0, 0, 1);
            
            return ProcessHatch(hatch, "Hatches");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing hatch entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGHatchProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGHatchProcessor::ProcessHatch(const HatchGeometry& geometry, const std::string& layer) {
    // Validate hatch geometry
    auto validation = ValidateHatchGeometry(geometry);
    if (!validation.isValid) {
        LogError("Hatch geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    HatchGeometry transformedGeometry = geometry;
    TransformHatchGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbHatch* hatch = CreateDWGHatch(transformedGeometry);
        if (!hatch) {
            LogError("Failed to create DWG hatch entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetHatchProperties(hatch, transformedGeometry)) {
            delete hatch;
            LogError("Failed to set hatch properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(hatch, layer)) {
            delete hatch;
            LogError("Failed to set hatch entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(hatch)) {
            delete hatch;
            LogError("Failed to add hatch to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedHatches++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG hatch: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - hatch creation skipped");
    m_processedHatches++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

HatchValidationResult DWGHatchProcessor::ValidateHatchGeometry(const HatchGeometry& geometry) const {
    HatchValidationResult result;
    result.isValid = true;
    
    // Validate boundary
    result.hasValidBoundary = ValidateHatchBoundary(geometry.boundary);
    if (!result.hasValidBoundary) {
        result.AddHatchError("Invalid hatch boundary");
    }
    
    // Validate pattern
    if (!geometry.isSolidFill) {
        result.hasValidPattern = ValidateHatchPattern(geometry.pattern);
        if (!result.hasValidPattern) {
            result.AddHatchError("Invalid hatch pattern");
        }
    } else {
        result.hasValidPattern = true;
    }
    
    // Validate normal
    result.hasValidNormal = std::isfinite(geometry.normal.x) && 
                           std::isfinite(geometry.normal.y) && 
                           std::isfinite(geometry.normal.z);
    if (!result.hasValidNormal) {
        result.AddHatchError("Invalid normal vector");
    }
    
    // Calculate area
    result.calculatedArea = geometry.CalculateArea();
    if (result.calculatedArea < m_hatchTolerance) {
        result.AddHatchWarning("Hatch area is very small: " + std::to_string(result.calculatedArea));
    }
    
    // Count loops and edges
    result.loopCount = static_cast<int>(geometry.boundary.loops.size());
    result.edgeCount = 0;
    for (const auto& loop : geometry.boundary.loops) {
        result.edgeCount += static_cast<int>(loop.edges.size());
    }
    
    // Check limits
    if (result.loopCount > static_cast<int>(m_maxBoundaryLoops)) {
        result.AddHatchError("Too many boundary loops (limit: " + std::to_string(m_maxBoundaryLoops) + ")");
        result.isValid = false;
    }
    
    if (result.edgeCount > static_cast<int>(m_maxBoundaryEdges)) {
        result.AddHatchError("Too many boundary edges (limit: " + std::to_string(m_maxBoundaryEdges) + ")");
        result.isValid = false;
    }
    
    return result;
}

bool DWGHatchProcessor::ValidateHatchBoundary(const HatchBoundary& boundary) const {
    if (!boundary.IsValid()) {
        return false;
    }
    
    // Check that boundary is closed
    if (!boundary.IsClosed()) {
        return false;
    }
    
    // Validate each loop
    for (const auto& loop : boundary.loops) {
        if (!ValidateHatchLoop(loop)) {
            return false;
        }
    }
    
    return true;
}

bool DWGHatchProcessor::ValidateHatchLoop(const HatchLoop& loop) const {
    if (!loop.IsValid()) {
        return false;
    }
    
    // Check that loop is closed
    if (!loop.IsClosed()) {
        return false;
    }
    
    // Validate each edge
    for (const auto& edge : loop.edges) {
        if (!ValidateHatchEdge(edge)) {
            return false;
        }
    }
    
    return true;
}

bool DWGHatchProcessor::ValidateHatchEdge(const HatchEdge& edge) const {
    return edge.IsValid();
}

bool DWGHatchProcessor::ValidateHatchPattern(const HatchPattern& pattern) const {
    return pattern.IsValid();
}

//=======================================================================================
// Helper Methods
//=======================================================================================

void DWGHatchProcessor::TransformHatchGeometry(HatchGeometry& geometry) const {
    // Transform boundary points
    for (auto& loop : geometry.boundary.loops) {
        for (auto& edge : loop.edges) {
            edge.startPoint = TransformPoint(edge.startPoint);
            edge.endPoint = TransformPoint(edge.endPoint);
            
            // Transform type-specific data
            switch (edge.type) {
                case HatchEdge::Type::Arc:
                    edge.arc.center = TransformPoint(edge.arc.center);
                    break;
                case HatchEdge::Type::Ellipse:
                    edge.ellipse.center = TransformPoint(edge.ellipse.center);
                    break;
                case HatchEdge::Type::Spline:
                    for (auto& cp : edge.spline.controlPoints) {
                        cp = TransformPoint(cp);
                    }
                    break;
                default:
                    break;
            }
        }
    }
    
    // Transform normal
    geometry.normal = TransformVector(geometry.normal);
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbHatch* DWGHatchProcessor::CreateDWGHatch(const HatchGeometry& geometry) const {
    try {
        AcDbHatch* hatch = new AcDbHatch();
        
        // Set hatch plane
        AcGeVector3d normal(geometry.normal.x, geometry.normal.y, geometry.normal.z);
        AcGePoint3d origin(0, 0, geometry.elevation);
        hatch->setHatchObjectType(AcDbHatch::kHatchObject);
        hatch->setAssociative(false);
        hatch->setPattern(AcDbHatch::kPreDefinedHatch, geometry.pattern.name.c_str());
        hatch->setPatternScale(geometry.pattern.scale);
        hatch->setPatternAngle(geometry.pattern.angle);
        
        // Add boundary loops
        for (const auto& loop : geometry.boundary.loops) {
            if (!AddHatchLoop(hatch, loop)) {
                delete hatch;
                return nullptr;
            }
        }
        
        // Evaluate hatch
        hatch->evaluateHatch();
        
        return hatch;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGHatchProcessor::AddHatchLoop(AcDbHatch* hatch, const HatchLoop& loop) const {
    try {
        AcGePoint2dArray vertices;
        AcGeDoubleArray bulges;
        
        // Convert edges to polyline representation for simplicity
        for (const auto& edge : loop.edges) {
            vertices.append(AcGePoint2d(edge.startPoint.x, edge.startPoint.y));
            
            if (edge.type == HatchEdge::Type::Arc) {
                // Calculate bulge for arc
                double bulge = CalculateArcBulge(edge);
                bulges.append(bulge);
            } else {
                bulges.append(0.0); // No bulge for lines
            }
        }
        
        // Add loop to hatch
        hatch->appendLoop(AcDbHatch::kDefault, vertices, bulges);
        
        return true;
    }
    catch (...) {
        return false;
    }
}

double DWGHatchProcessor::CalculateArcBulge(const HatchEdge& edge) const {
    if (edge.type != HatchEdge::Type::Arc) {
        return 0.0;
    }
    
    // Calculate bulge from arc parameters
    double sweepAngle = edge.arc.endAngle - edge.arc.startAngle;
    return std::tan(sweepAngle / 4.0);
}

bool DWGHatchProcessor::SetHatchProperties(AcDbHatch* hatch, const HatchGeometry& geometry) const {
    if (!hatch) {
        return false;
    }
    
    try {
        // Set solid fill or pattern
        if (geometry.isSolidFill) {
            hatch->setPattern(AcDbHatch::kUserDefinedHatch, "SOLID");
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGHatchProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGHatchProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
