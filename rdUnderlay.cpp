/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdUnderlay.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           UnderlayBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
WString         GetLogicalName (RealDwgUnderlays underlayType, ConvertToDgnContextR context) const
    {
    RealDwgObjectTypeId         stringId;
    switch (underlayType)
        {
        case DWGUNDERLAY_Dgn:   stringId = REALDWGMESSAGE_OBJECTTYPE_DgnUnderlay;   break;
        case DWGUNDERLAY_Pdf:   stringId = REALDWGMESSAGE_OBJECTTYPE_PdfUnderlay;   break;
        case DWGUNDERLAY_Dwf:   stringId = REALDWGMESSAGE_OBJECTTYPE_DwfUnderlay;   break;
        default:                return  WString ();
        }

    WString     underlayName;
    // hard code logical name for DGN underlay as it will be used else where such as reference manager
    if (REALDWGMESSAGE_OBJECTTYPE_DgnUnderlay == stringId)
        underlayName.assign (REFERENCE_NAME_DgnUnderlay);
    else
        RmgrResource::LoadWString (underlayName, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgObjectTypes, stringId);

    UInt32      underlayIndex = context.GetFileHolder().GetAndIncrementNextAvailableUnderlayNumber (underlayType);

    WChar       indexChars[MAXLOGICALNAMELENGTH];
    if (BSISUCCESS != BeStringUtilities::Itow(indexChars, underlayIndex, _countof(indexChars), 10))
        return  WString ();

    WString     logicalName = underlayName + WString(L" ") + WString(indexChars);

    return  logicalName;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FindUnderlayFilePath (WStringR foundPath, WCharCP inputPath, WCharCP currentFilePath) const
    {
    if (nullptr == inputPath || 0 == inputPath[0])
        return  false;

    // prepend current path to a relative path before checking:
    WString     checkPath (inputPath);
    if (checkPath.StartsWith(L".\\") || checkPath.StartsWith(L"..\\"))
        {
        size_t  fileNameStart = checkPath.find_last_of(L'\\');
        checkPath = WString(currentFilePath) + checkPath.substr(fileNameStart + 1);
        }

    if (BeFileNameStatus::Success == BeFileName::CheckAccess(checkPath.GetWCharCP(), BeFileNameAccess::Read))
        {
        // the original input path appears valid, use it:
        foundPath = WString (checkPath);
        return  true;
        }

    // the original input path is invalid, search for it by separating its file and directory names:
    WString     fileName = BeFileName::GetFileNameAndExtension (checkPath.GetWCharCP());
    WString     searchPath = BeFileName::GetDirectoryName (checkPath.GetWCharCP());

    // add current file path to the search path
    if (NULL != currentFilePath && 0 != currentFilePath[0])
        searchPath += WString(L";") + WString(currentFilePath);

    // add MS_REF to the search path
    searchPath += WString (L";$(MS_RFDIR)");

    // search for the DGN file
    DgnBaseMoniker::SearchStatus    searchStatus = DgnBaseMoniker::SearchStatus::NotAttempted;
    foundPath = DgnBaseMoniker::SearchForFile (searchStatus, fileName.GetWCharCP(), NULL, searchPath.GetWCharCP(), false);

    if (searchStatus <= DgnBaseMoniker::SearchStatus::Failed || foundPath.empty())
        {
        DIAGNOSTIC_PRINTF ("Underlay %ls is not found on search path %ls\n", fileName.c_str(), searchPath.c_str());
        return  false;
        }

    return  true;
    }

};  // UnderlayBase


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPdfUnderlay : public ToDgnExtension, public UnderlayBase
{
    mutable     DPoint2d        m_pixelsPerUOR;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    // raster in shared cell is still not supported!
    if (context.IsInSharedCellCreation() && !context.GetSettings().SaveRasterToSharedCell())
        return DwgObjectUnsupported;

    AcDbPdfReference*   pdfUnderlay = AcDbPdfReference::cast (acObject);
    if (NULL == pdfUnderlay)
        return  NullObject;
        
    // get file specs
    WStringCR           dwgFileName = context.GetFileHolder().GetFileName ();
    WString             originalFileName, baseFileName, itemName, foundFileName;
    if (RealDwgSuccess != this->GetPdfFilePath(originalFileName, baseFileName, itemName, foundFileName, dwgFileName, pdfUnderlay))
        return  FileNotFound;

    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
    ////PDF as Vector attachment
    if (settings.OpenPDFAsVector())
    {
        DgnDocumentPtr doc = DgnDocument::CreateForLocalFile(originalFileName.c_str());
        DgnFilePtr dgnFile = DgnFile::Create(*doc, DgnFileOpenMode::ReadOnly);
        StatusInt fileLoadStatus;

        DgnDocumentMonikerPtr monikerPtr = doc->GetMonikerPtr();

        DgnModelP           model = context.GetModel();
        DgnAttachmentP newAttachment = NULL;

        model->CreateDgnAttachment(newAttachment, *monikerPtr, NULL, true);
        //tranform here 
        RasterFileQuickInfo pdfInfo;
        StatusInt           status = mdlRaster_fileInfoMinimalGetExt(&pdfInfo, foundFileName.GetWCharCP(), NULL);
        if (BSISUCCESS != status)
            pdfInfo.extent.Init(DEFAULT_DPI, DEFAULT_DPI);

        DgnModelRefP modelRef = dynamic_cast<DgnModelRefP>(model);
        PDFMeasureModelAppData* measureData;

        if (NULL != modelRef->GetDgnAttachmentsP())
        {
            for each (DgnAttachmentP attachment in *modelRef->GetDgnAttachmentsP())
            {
                DgnModelP refP = attachment->GetDgnModelP();
                measureData = dynamic_cast<PDFMeasureModelAppData*>(refP->FindAppData(PDFMeasureModelAppData::GetKey()));
            }
        }

        Transform           transform;
        this->CalculateVectorPdfExtent(transform, pdfUnderlay, context, measureData);

        newAttachment->TransformAttachment(transform);
        fileLoadStatus = newAttachment->WriteToModel();
    }
    else
    {
        // get raster info
        RasterFileQuickInfo pdfInfo;
        StatusInt           status = mdlRaster_fileInfoMinimalGetExt(&pdfInfo, foundFileName.GetWCharCP(), NULL);
        if (BSISUCCESS != status)
            pdfInfo.extent.Init(DEFAULT_DPI, DEFAULT_DPI);

        Transform           transform;
        DPoint2d            pdfExtent = this->CalculatePdfExtent(transform, foundFileName.GetWCharCP(), pdfInfo, pdfUnderlay, context);

        DgnModelP           model = context.GetModel();
        status = this->CreatePdfAttachment(outElement, originalFileName, transform, pdfExtent, model);
        if (BSISUCCESS != status)
            return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

        // get the raster element handler from the new pdf attachment element
        IRasterAttachmentEdit*  rasterEditor = dynamic_cast <IRasterAttachmentEdit*> (&outElement.GetHandler());
        if (NULL == rasterEditor)
            return  BadElementHandler;

        rasterEditor->SetTransform(outElement, transform, &pdfExtent, true);
        rasterEditor->SetScanningResolution(outElement, pdfInfo.scanningResolution);
        rasterEditor->SetGeoreferencePriority(outElement, GeoreferencePriority_Attachment);
        rasterEditor->SetInvertState(outElement, false);
        rasterEditor->SetPrintState(outElement, true);
        rasterEditor->SetOpenReadWrite(outElement, false);
        rasterEditor->SetLogicalName(outElement, this->GetLogicalName(DWGUNDERLAY_Pdf, context).c_str());
        rasterEditor->SetDisplayBorderState(outElement, pdfUnderlay->isFrameVisible());

        // set desccription from original file name + item name
        this->SetDescription(outElement, rasterEditor, baseFileName, itemName);

        // try setting page number from item name (1 based)
        if (!itemName.empty())
        {
            UInt32          pageNumber = 0;
            if (1 == swscanf(itemName.c_str(), L"%d", &pageNumber) && pageNumber > 0)
                rasterEditor->SetPageNumber(outElement, pageNumber - 1);
        }

        bool                isMonochrome = pdfUnderlay->isMonochrome();
        rasterEditor->SetLogicalBackgroundIndex(outElement, isMonochrome ? 1 : 0);

        if (!isMonochrome)
            rasterEditor->SetContrast(outElement, (short)ToDgnExtRasterAttachment::GetSignedRangeFromPercent(pdfUnderlay->contrast() * 2 - 100.0));

        unsigned char       fadeRange = ToDgnExtRasterAttachment::GetRangeFromPercent(pdfUnderlay->fade());
        rasterEditor->SetImageTransparencyLevel(outElement, fadeRange);

        // turn on transparency to ensure fade can be applied
        if (!isMonochrome && fadeRange > 0)
            rasterEditor->SetTransparencyState(outElement, true);

        RgbColorDef         backgroundColor, foregroundColor;
        bool                synch = pdfUnderlay->isAdjustedForBackground();
        ToDgnExtRasterAttachment::ExtractBackgroundForegroundColors(&backgroundColor, &foregroundColor, pdfUnderlay, synch, context);

        UInt32              index = 0;
        if (BSISUCCESS == IRasterAttachmentQuery::ColorIndexFromRgbInModel(index, *model, foregroundColor))
            rasterEditor->SetForegroundColor(outElement, index);
        if (BSISUCCESS == IRasterAttachmentQuery::ColorIndexFromRgbInModel(index, *model, backgroundColor))
            rasterEditor->SetBackgroundColor(outElement, index);

        bool                isOn = pdfUnderlay->isOn();
        for (int view = 0; view < 8; view++)
            rasterEditor->SetViewState(outElement, view, isOn);

        ToDgnExtRasterAttachment::SetDisplayPriorityPlane(outElement, rasterEditor);

        this->ClipPdfAttachment(outElement, rasterEditor, pdfUnderlay, pdfInfo.extent, context);

        context.ElementHeaderFromEntity(outElement, AcDbEntity::cast(acObject));
    }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   GetPdfFilePath (WStringR pathName, WStringR baseName, WStringR itemName, WStringR foundPath, WStringCR dwgFileName, AcDbPdfReference* pdfUnderlay) const
    {
    // get full path from underlay definition
    AcDbPdfDefinitionPointer    underlayDef(pdfUnderlay->definitionId(), AcDb::kForRead);
    if (Acad::eOk == underlayDef.openStatus())
        {
        const ACHAR*    path = NULL;
        underlayDef->getActiveFileName(path);

        if (NULL != path && 0 != path[0])
            pathName.assign (path);
        else
            {
            path = underlayDef->getSourceFileName();
            pathName.assign(path);
            }

        const ACHAR*    itemChars = underlayDef->getItemName ();
        if (NULL != itemChars)
            itemName.assign (itemChars);
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Failed to open PDF underlay ID=%I64d. [%ls]\n", (Adesk::UInt64)pdfUnderlay->objectId().handle(), acadErrorStatusText(underlayDef.openStatus()));
        }

    if (pathName.empty())
        return  FileNotFound;
    
    // get root file name
    size_t              pathEnd = pathName.find_last_of (L'\\');
    if (pathEnd == WString::npos)
        baseName = pathName;
    else
        baseName = pathName.substr (pathEnd + 1);

    // try finding the PDF file
    WString             dwgDir = BeFileName::GetDirectoryName (dwgFileName.GetWCharCP());
    if (!this->FindUnderlayFilePath(foundPath, pathName.GetWCharCP(), dwgDir.empty() ? NULL : dwgDir.GetWCharCP()))
        {
        DIAGNOSTIC_PRINTF ("Cannot find PDF file %ls\n", pathName.c_str());
        foundPath = pathName;
        return  FileNotFound;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            SetDescription (EditElementHandleR outElement, IRasterAttachmentEdit* rasterEditor, WStringCR fileName, WStringCR itemName) const
    {
    if (!fileName.empty() || !itemName.empty())
        {
        WString         description;
        if (!fileName.empty())
            description = fileName;
        if (!itemName.empty())
            description += WString(L" - ") + itemName;

        return rasterEditor->SetAttachDescription (outElement, description.c_str());
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                  Sandip Ichake       03/22
+---------------+---------------+---------------+---------------+---------------+------*/
void        CalculateVectorPdfExtent(TransformR outTransform, AcDbPdfReference* pdfUnderlay, ConvertToDgnContextR context, PDFMeasureModelAppData* pdfMeasureData) const
    {
    ModelInfoCR             modelInfo = context.GetModel()->GetModelInfo();
    // Set output matrix
    RealDwgUtil::TransformFromGeMatrix3d(outTransform, pdfUnderlay->transform());

    // set the scale into the raster matrix - pixel size in UOR's is what is used by type-94 element
    RotMatrix   matrix;
    outTransform.GetMatrix(matrix);

    double xScale = 1.0;
    double yScale = 1.0;

    if (NULL != pdfMeasureData)
    {
        xScale = pdfMeasureData->getXScale();
        yScale = pdfMeasureData->getYScale();
        matrix.ScaleColumns(xScale * 72, yScale * 72, 1.0);
    }
    else
    {
        UnitDefinition unitDef = modelInfo.GetMasterUnit();
        if (unitDef.GetBase() == UnitBase::Meter)
        {
            RotMatrix scaleMatrix = RotMatrix::FromScale(unitDef.GetDenominator() / (0.0254 * unitDef.GetNumerator()));
            matrix = scaleMatrix * matrix;
        }
    }
    outTransform.SetMatrix(matrix);
    // also transform the translation to UOR's
    DPoint3d    origin;
    outTransform.GetTranslation(origin);
    context.GetTransformToDGN().Multiply(origin);
    outTransform.SetTranslation(origin);

    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
DPoint2d        CalculatePdfExtent (TransformR outTransform, WCharCP foundFilename, const RasterFileQuickInfo& pdfInfo, AcDbPdfReference* pdfUnderlay, ConvertToDgnContextR context) const
    {
    DPoint2d    pdfExtent;
    //Fix TR TFS #1376; Extract extent directly from PDF file.
    RasterGeomFacility::GetDimensionForDWGUnderlay(foundFilename,0,pdfExtent.x,pdfExtent.y);

    //Fix TFS 1112249: PDF raster attachment comes in at wrong scale when opening DWG file
    ModelInfoCR             modelInfo = context.GetModel()->GetModelInfo();
    SheetDefCP                sheetDef = modelInfo.GetSheetDefCP();

    if (sheetDef != nullptr)
    {
        double                  uorPerInch = 1.0, storagePerInch = 1.0;
        UnitDefinition unitDef;

        sheetDef->GetUnits(unitDef);

        UnitDefinition      unitInfoStd = UnitDefinition::GetStandardUnit(StandardUnit::EnglishInches);

        if (SUCCESS == unitDef.GetConversionFactorFrom(storagePerInch, unitInfoStd))
            uorPerInch = storagePerInch * modelInfo.GetUorPerStorage();

        pdfExtent.x *= uorPerInch;
        pdfExtent.y *= uorPerInch;
    }
    else
    {
        // scale extent size to UOR's
        double      uorsPerStorage = context.GetScaleToDGN ();
        pdfExtent.x *= uorsPerStorage;
        pdfExtent.y *= uorsPerStorage;
    }

    DPoint2d    uorsPerPixel =  DPoint2d::From (pdfExtent.x/ pdfInfo.extent.x,  pdfExtent.y/ pdfInfo.extent.y);

    // safe guard the scale. although this should never happen
    if (0.0 == uorsPerPixel.x)
        uorsPerPixel.x = 1.0;
    if (0.0 == uorsPerPixel.y)
        uorsPerPixel.y = 1.0;

    // save the inverse of this scale for later use:
    m_pixelsPerUOR.x = 1.0 / uorsPerPixel.x;
    m_pixelsPerUOR.y = 1.0 / uorsPerPixel.y;

    // Set output matrix
    RealDwgUtil::TransformFromGeMatrix3d (outTransform, pdfUnderlay->transform());

    // set the scale into the raster matrix - pixel size in UOR's is what is used by type-94 element
    RotMatrix   matrix;
    outTransform.GetMatrix (matrix);
    matrix.ScaleColumns (uorsPerPixel.x, uorsPerPixel.y, 1.0);
    outTransform.SetMatrix (matrix);

    // also transform the translation to UOR's
    DPoint3d    origin;
    outTransform.GetTranslation (origin);
    context.GetTransformToDGN().Multiply (origin);
    outTransform.SetTranslation (origin);

    return  pdfExtent;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       CreatePdfAttachment (EditElementHandleR outElement, WStringCR fileName, TransformCR transform, DPoint2dCR extent, DgnModelP model) const
    {
    // create a new attachment for the PDF file
    WCharCP                 fileNameChars = fileName.GetWCharCP ();
    DgnDocumentMonikerPtr   moniker = DgnDocumentMoniker::CreateFromRawData(fileNameChars, fileNameChars, NULL, IRasterAttachmentQuery::GetSearchPath(model).GetWCharCP(), false, NULL);

    return IRasterAttachmentEdit::CreateRasterAttachment (outElement, NULL, *moniker.get(), transform, extent, *model);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ClipPdfAttachment (EditElementHandleR outElement, IRasterAttachmentEdit* rasterEditor, AcDbPdfReference* pdfUnderlay, DPoint2dCR pdfSize, ConvertToDgnContextR context) const
    {
    DgnModelP       model = context.GetModel ();
    unsigned short  clipStatus = AD_IMAGE_NOT_CLIPPED;
    DPoint2dArray   clipPoints;
    UInt32          nClipPoints = 0;
    if (pdfUnderlay->isClipped())
        {
        const AcGePoint2dArray  acClipPoints = pdfUnderlay->clipBoundary ();

        nClipPoints = acClipPoints.length ();
        for (UInt32 i = 0; i < nClipPoints; i++)
            {
            DPoint2d        point;
            RealDwgUtil::DPoint2dFromGePoint2d (point, acClipPoints[i]);

            // transform to UOR's
            context.GetTransformToDGN().Multiply (point, point);
            // transform to pixels
            point.x *= m_pixelsPerUOR.x;
            point.y *= m_pixelsPerUOR.y;

            clipPoints.push_back (point);
            }

        clipStatus = AD_IMAGE_CLIPPED;
        }

    if (nClipPoints < 2)
        return  InvalidPolygonVertexCount;
    
    RasterClipPropertiesPtr clipProperties(RasterClipProperties::Create());

    unsigned short  clipType = nClipPoints > 2 ? AD_IMAGE_CLIPBOUND_POLYGON : AD_IMAGE_CLIPBOUND_RECT;
    bool            isInverted = pdfUnderlay->isClipInverted ();

    ToDgnExtRasterAttachment::FillRasterClipPropertiesFromDwg (*clipProperties, &pdfSize, nClipPoints, clipPoints.data(), clipStatus, clipType, *model, isInverted, false);

    rasterEditor->SetClipProperties (outElement, *clipProperties);
    rasterEditor->SetClipState (outElement, pdfUnderlay->isClipped());

    return  RealDwgSuccess;
    }


};  // ToDgnExtPdfUnderlay



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtDwfUnderlay : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    // Currently not supported, just log a message
    AcDbDwfReference*   dwfUnderlay = AcDbDwfReference::cast (acObject);
    if (NULL == dwfUnderlay)
        return  NullObject;

    WString             fileName = this->GetFileNameString (dwfUnderlay);
    WString             objectName;
    RmgrResource::LoadWString (objectName, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgObjectTypes, REALDWGMESSAGE_OBJECTTYPE_DwfUnderlay);

    RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Info, REALDWGMESSAGECENTER_NotSupported, false, objectName.GetWCharCP(), fileName.GetWCharCP());
    
    return  DwgObjectUnsupported;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
WString         GetFileNameString (AcDbDwfReference* dwfUnderlay) const
    {
    WString                     fileName;
    AcDbDwfDefinitionPointer    underlayDef(dwfUnderlay->definitionId(), AcDb::kForRead);
    if (Acad::eOk == underlayDef.openStatus())
        {
        const ACHAR*    path = NULL;
        underlayDef->getActiveFileName(path);

        if (NULL != path && 0 != path[0])
            fileName.assign (path);

        const ACHAR*    model = underlayDef->getItemName ();
        if (NULL != model)
            fileName += WString(L", ") + WString(model);
        }

    return  fileName;
    }


};  // ToDgnExtDwfUnderlay



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtDgnUnderlay : public ToDgnExtension, public UnderlayBase
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbDgnReference*       dgnUnderlay = AcDbDgnReference::cast (acObject);
    if (NULL == dgnUnderlay)
        return  NullObject;

    ReferenceFileElm*       refData = (ReferenceFileElm*) calloc (1, MAX_ELEMENT_SIZE * sizeof(ReferenceFileElm));
    if (NULL == refData)
        return  OutOfMemoryError;

    // find the DGN underlay definition from our file holder list
    DgnUnderlayDefInfo      dgndef = this->GetDgnUnderlayDefInfo (dgnUnderlay, context);
    WString                 pathName = dgndef.GetSourceFileName ();
    if (pathName.empty())
        return  FileNotFound;

    refData->ehdr.type                      = REFERENCE_ATTACH_ELM;
    refData->fb_opts.snap_lock              = true;
    refData->fb_opts.locate_lock            = true;
    refData->fb_opts.attachmentFromDWG      = false;
    refData->fb_opts.levelControlsDisplay   = false;
    refData->fd_opts.rotateClipping         = true;
    refData->fd_opts.lstyleScale            = true;
    refData->fd_opts.displayRasterRefs      = true;
    refData->fd_opts.doNotDisplayAsNested   = false;    // no overlay
    refData->fd_opts.clipFront              = false;
    refData->fd_opts.clipBack               = false;
    refData->zFront                         = 0.0;
    refData->zBack                          = 0.0;
    refData->nestDepth                      = (UInt16) dgndef.GetXReferenceDepth ();
    refData->ehdr.uniqueId                  = context.ElementIdFromObject (dgnUnderlay);
    refData->fileNumber                     = context.GetNextReferenceFileNumber ();
    refData->fd_opts.display                = dgnUnderlay->isOn ();
    refData->fd_opts.displayBoundary        = dgnUnderlay->isFrameVisible ();
    refData->fd_opts.displayRasterRefs      = dgndef.GetShowRasterReference ();

    // extract DGN specific data from xdata created from DWG save
    ToDgnExtBlockReference::RoundTripDgnSpecificData (refData, acObject, context);

    // set scale: use x-scale for reference scale, and leave y and z scales to the matrix
    AcGeMatrix3d        acMatrix = dgnUnderlay->transform ();
    AcGeScale3d         acScale = dgnUnderlay->scaleFactors ();

    refData->scale = fabs(acScale.sx) < TOLERANCE_ZeroScale ? 1.0 : acScale.sx;

    // set rotation matrix from underlay's matrix which includes both rotation and scale
    RealDwgUtil::RotMatrixFromGeMatrix3d (refData->transform, acMatrix);

    // invert the x-scale in the matrix as we have used it for attachment scale
    double              invScale = 1.0 / refData->scale;
    refData->transform.ScaleColumns (invScale, invScale, invScale);

    // apply ACAD's master/sub unit scale from reference file
    this->ApplyAcadUnitScale (refData, dgndef);

    // set origin
    RealDwgUtil::DPoint3dFromGePoint3d (refData->masterOrigin, dgnUnderlay->position());
    context.GetTransformToDGN().Multiply (refData->masterOrigin);

    // set ref origin
    refData->refOrigin = dgndef.GetGlobalOrigin ();

    // get roundtrip parameters from xdata
    ToDgnExtBlockReference::RoundTripDgnSpecificData (refData, acObject, context);

    // set clipping points
    DPoint2dArray       clipPoints;
    refData->nClipPoints = this->GetClippingPoints (clipPoints, dgnUnderlay, context.GetTransformToDGN());

    ToDgnExtBlockReference::SetElementSizeWithClippingPoints (refData, clipPoints.data(), refData->nClipPoints);

    // set color adjustment from contrast & fade
    this->SetColorAdjustment ((MSElementP)refData, dgnUnderlay);

    // set reference transparency from entity transparency
    this->SetTransparency ((MSElementP)refData, dgnUnderlay);

    // set names
    WString     modelName = dgndef.GetModelName ();
    WString     logicalName = this->GetLogicalName (DWGUNDERLAY_Dgn, context);

    // append model name string linkage
    if (!modelName.empty())
        LinkageUtil::AppendStringLinkage ((MSElementP)refData, STRING_LINKAGE_KEY_ModelName, modelName.GetWCharCP());

    StatusInt   status = DgnAttachment::InitializeDgnAttachmentElementFromReferenceFileElm (outElement, *refData, NULL, pathName.GetWCharCP(), nullptr,
                                        logicalName.GetWCharCP(), refData->nClipPoints, clipPoints.data(), 0, nullptr, nullptr, nullptr, context.GetModel());

    free (refData);

    if (BSISUCCESS == status)
        {
        RealDwgUtil::AddPathToReferenceElement (outElement, pathName.GetWCharCP());

        context.ElementHeaderFromEntity (outElement, dgnUnderlay);
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
DgnUnderlayDefInfo  GetDgnUnderlayDefInfo (AcDbDgnReference* dgnUnderlay, ConvertToDgnContextR context) const
    {
    DgnUnderlayDefInfoArray&    dgndefArray = context.GetFileHolder().GetDgnUnderlayDefInfoArrayR ();
    ElementId                   dgndefId = context.ElementIdFromObjectId (dgnUnderlay->definitionId());

    for each (DgnUnderlayDefInfo dgndef in dgndefArray)
        {
        if (dgndef.GetElementId() == dgndefId)
            return  dgndef;
        }
    
    return  DgnUnderlayDefInfo();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ApplyAcadUnitScale (ReferenceFileElm* refData, DgnUnderlayDefInfo const& dgndef) const
    {
    /*---------------------------------------------------------------------------------------------------------------
    ACAD scales reference with no true units.  It has options of master and sub units.  When master is used, there is
    no scale applied - as if it treats 1 master unit in reference file as 1 storage unit, effectively a unitless scale.
    To make such a pure scale working, we need to invert the UOR scale ratio of ref/master as in reference_scaleRatio.

    When sub-unit is used, ACAD applies a scale of sub/master.  This allows us to scale reference by units, with a
    scale conversion between sub and master units.  A plus side of turning on this flag is when the underlaid DGN uses
    the same master units, the display scale can be retained the same as seen in ACAD (eg. a ft-in scenario).
    ---------------------------------------------------------------------------------------------------------------*/
    refData->fd_opts.scaleByUnits           = !dgndef.GetUseMasterUnit();
    refData->fd_opts.scaleByStorageUnits    = false;

    refData->scale *= dgndef.GetAcadUnitScale ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
UInt16          GetClippingPoints (DPoint2dArrayR clipPoints, AcDbDgnReference* dgnUnderlay, TransformCR toDGN) const
    {
    clipPoints.clear ();

    if (dgnUnderlay->isClipped())
        {
        ToDgnExtBlockReference::GetClippingPointsFromFilterPoints (clipPoints, dgnUnderlay->clipBoundary());

        if (clipPoints.size() > 0)
            {
            Transform       matrix = toDGN;
            AcGeScale3d     acScale = dgnUnderlay->scaleFactors ();

            if (!acScale.isEqualTo(AcGeScale3d(1, 1, 1)))
                matrix.ScaleMatrixColumns (acScale.sx, acScale.sy, acScale.sz);

            matrix.Multiply (clipPoints, clipPoints);

            if (dgnUnderlay->isClipInverted())
                {
                // this is an inverted xclip, add a disconnect point at the top of the point array
                DPoint2d    connectPoint;
                connectPoint.InitDisconnect ();
                clipPoints.insert (clipPoints.begin(), connectPoint);
                }
            }
        }

    return  (UInt16)clipPoints.size ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetColorAdjustment (MSElementP refElem, AcDbUnderlayReference* underlay) const
    {
    double      colorAdjustment[2];

    colorAdjustment[0] = underlay->fade ();
    colorAdjustment[1] = underlay->isMonochrome() ? 100.0 : 0.0;

    if (BSISUCCESS != linkage_appendDoubleArrayLinkage(refElem, DOUBLEARRAY_LINKAGE_KEY_RefColorAdjustment, _countof(colorAdjustment), colorAdjustment))
        DIAGNOSTIC_PRINTF ("Can't append color adjustment linkage for underlay\n");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetTransparency (MSElementP refElem, AcDbUnderlayReference* underlay) const
    {
    AcCmTransparency    dwgTransparency = underlay->transparency ();

    if (dwgTransparency.isByLayer())
        {
        AcDbLayerTableRecordPointer     layer(underlay->layerId(), AcDb::kForRead);
        if (Acad::eOk == layer.openStatus())
            dwgTransparency = layer->transparency ();
        }

    if (dwgTransparency.isByAlpha())
        {
        double          dgnTransparency[1];

        dgnTransparency[0] = 1.0 - dwgTransparency.alphaPercent();

        if (dgnTransparency[0] > 1.0)
            dgnTransparency[0] = 1.0;

        if (fabs(dgnTransparency[0]) > MIN_Transparency)
            linkage_appendDoubleArrayLinkage (refElem, DOUBLEARRAY_LINKAGE_KEY_RefTransparency, 1, dgnTransparency);
        }
    }


};  // ToDgnExtDgnUnderlay



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           DgnUnderlayDefInfoCollector
{
private:
    ConvertToDgnContext*        m_toDgnContext;
    AcDbDictionary*             m_dgndefList;
    WString                     m_currentFilePath;
    UnderlayBase                m_underlayBase;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            CopyDgnUnderlayDefInfo (DgnUnderlayDefInfo& dgndefInfo, AcDbDgnDefinition* acDgndef)
    {
    // copy from DWG's DGN definition to our local data holder
    dgndefInfo.SetElementId (m_toDgnContext->ElementIdFromObject(acDgndef));

    const ACHAR*    fileName = acDgndef->getSourceFileName ();
    if (NULL != fileName)
        dgndefInfo.SetSourceFileName (fileName);

    const ACHAR*    modelName = acDgndef->getItemName ();
    if (NULL != modelName)
        dgndefInfo.SetModelName (modelName);

    dgndefInfo.SetShowRasterReference (acDgndef->showRasterRef());
    dgndefInfo.SetXReferenceDepth (acDgndef->xrefDepth());
    dgndefInfo.SetUseMasterUnit (acDgndef->useMasterUnits()); 

    dgndefInfo.SetAcadUnitScale (1.0);
    dgndefInfo.SetIsAcadUnitScaleChecked (false);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            RemoveDefaultModelName (DgnUnderlayDefInfo& dgndefInfo)
    {
    /*--------------------------------------------------------------------------------------------------------------
    We generally would like to set the model name, even for the Default model, for MicroStation to work properly, 
    but the model name has to be correct.  For a V7 DGN reference file, there is no model at all, but RealDWG seems 
    to set the model name as Default anyway.  We need to remove the incorrectly presumed Default model name.
    --------------------------------------------------------------------------------------------------------------*/
    if (dgndefInfo.GetModelName().EqualsI(L"Default"))
        dgndefInfo.SetModelName (L"");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FindDgnUnderlayFilePath (WStringR foundPath, WCharCP inputPath)
    {
    return  m_underlayBase.FindUnderlayFilePath (foundPath, inputPath, m_currentFilePath.GetWCharCP());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            CheckDgnDefinitionUnits (DgnUnderlayDefInfo& dgndefInfo, DgnFileP refFile)
    {
    if (dgndefInfo.IsAcadUnitScaleChecked())
        return;

    // atttempt to get and load the underlay model
    ModelId     modelId = refFile->FindModelIdByName (dgndefInfo.GetModelName().GetWCharCP());
    if (INVALID_MODELID != modelId && modelId >= 0)
        {
        DgnModelPtr     refModel = refFile->LoadModelById (modelId);

        if (refModel.IsValid())
            {
            ModelInfoCR refModelInfo = refModel->GetModelInfo ();
            ModelInfoCR masterModelInfo = m_toDgnContext->GetModel()->GetModelInfo ();

            // ACAD always applies sub-units when the DGN model has Feet-Inches, in which case we need the sub-unit scale;
            bool        useMasterUnit = dgndefInfo.GetUseMasterUnit ();
            if (useMasterUnit && StandardUnit::EnglishFeet == refModelInfo.GetMasterUnit().IsStandardUnit() && StandardUnit::EnglishInches             == refModelInfo.GetSubUnit().IsStandardUnit())
                useMasterUnit = false;

            double      scale = 1.0;
            if (useMasterUnit)
                {
                // get master file's uors per master
                double      masterUOR = ModelInfo::GetUorPerMaster (&masterModelInfo);
                // get reference's uors per master unit, or per sub unit, depending on its attachment option in ACAD
                double      refUOR = ModelInfo::GetUorPerMaster (&refModelInfo);
                // apply the scale to revert the reference scale ratio so to make ACAD's unitless display scale effective
                scale = fabs(refUOR) > TOLERANCE_ZeroSize ? masterUOR / refUOR : 1.0;
                }
            else
                {
                // get sub-unit scale factor from reference file, and we will apply a unit scale
                refModelInfo.GetSubUnit().GetConversionFactorFrom (scale, masterModelInfo.GetStorageUnit());
                dgndefInfo.SetUseMasterUnit (false);
                }

            dgndefInfo.SetAcadUnitScale (scale);
            dgndefInfo.SetGlobalOrigin (refModelInfo.GetGlobalOrigin());
            }
        }
    else
        {
        // V7 DGN does not have models.  Adding a "Default" model name can result in missing reference models.
        this->RemoveDefaultModelName (dgndefInfo);
        }

    dgndefInfo.SetIsAcadUnitScaleChecked (true);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   UpdateDgnUnderlayDefInfoList (DgnUnderlayDefInfoArray& dgndefArray)
    {
    size_t      numDgndefs = dgndefArray.size();

    for (size_t i = 0; i < numDgndefs; i++)
        {
        if (dgndefArray[i].IsAcadUnitScaleChecked())
            continue;

        // check for the existence of the DGN file, and search for it as necessary
        WString         foundPath;
        if (!this->FindDgnUnderlayFilePath(foundPath, dgndefArray[i].GetSourceFileName().GetWCharCP()))
            {
            this->RemoveDefaultModelName (dgndefArray[i]);
            continue;
            }
        
        // prepare a new DgnDocument to open a new DgnFile
        DgnFileStatus   fileStatus = DGNFILE_STATUS_Success;
        DgnDocumentPtr  dgnDocument = DgnDocument::CreateFromFileName (fileStatus, foundPath.GetWCharCP(), NULL, DEFDGNFILE_ID, DgnDocument::FetchMode::Read);
        if (DGNFILE_STATUS_Success != fileStatus || !dgnDocument.IsValid())
            {
            this->RemoveDefaultModelName (dgndefArray[i]);
            continue;
            }

        // open the DGN file
        DgnFilePtr      dgnRefFile = DgnFile::Create (*dgnDocument.get(), DgnFileOpenMode::ReadOnly);
        if (!dgnRefFile.IsValid())
            {
            this->RemoveDefaultModelName (dgndefArray[i]);
            continue;
            }

        // load the DGN file and fill the dictionary model
        if (DGNFILE_STATUS_Success == dgnRefFile->LoadDgnFile(NULL))
            {
            CheckDgnDefinitionUnits (dgndefArray[i], dgnRefFile.get());

            // while we have the DGN file opened, try to check other defs of different models in the same file
            for (size_t j = i + 1; j < numDgndefs; j++)
                {
                if (dgndefArray[i].GetSourceFileName() == dgndefArray[j].GetSourceFileName())
                    CheckDgnDefinitionUnits (dgndefArray[j], dgnRefFile.get());
                }
            }
        else
            {
            this->RemoveDefaultModelName (dgndefArray[i]);
            }
        }

    return  RealDwgSuccess;
    }

public:
// the constructor
DgnUnderlayDefInfoCollector (ConvertToDgnContext* context, AcDbDictionary* defs)
    {
    m_toDgnContext = context;
    m_dgndefList = defs;
    m_currentFilePath.clear ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Collect ()
    {
    DgnUnderlayDefInfoArray&    defArray = m_toDgnContext->GetFileHolder().GetDgnUnderlayDefInfoArrayR ();
    defArray.clear ();

    // find current file path
    WStringCR   currFile = m_toDgnContext->GetFileHolder().GetFileName ();
    if (!currFile.empty())
        m_currentFilePath = BeFileName::GetDirectoryName (currFile.GetWCharCP());

    // copy DWG data to our file holder list
    AcDbDictionaryIterator*     iterator = m_dgndefList->newIterator();
    for (; !iterator->done(); iterator->next())
        {
        AcDbDgnDefinitionPointer    acDgnUnderlayDef(iterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != acDgnUnderlayDef.openStatus())
            continue;

        DgnUnderlayDefInfo      dgndefInfo;
        CopyDgnUnderlayDefInfo (dgndefInfo, acDgnUnderlayDef);

        defArray.push_back (dgndefInfo);
        }

    // check sub-units and set a scale for each def we have copied
    return UpdateDgnUnderlayDefInfoList (defArray);
    }

};  // DgnUnderlayDefInfoCollector



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
DgnUnderlayDefInfo::DgnUnderlayDefInfo ()
    {
    m_acadUnitScaleChecked = false;
    m_definitionId = INVALID_ELEMENTID;
    m_showRasterRef = false;
    m_xrefDepth = 0;
    m_useMasterUnit = true;
    m_acadUnitScale = 1.0;
    m_sourceFileName.clear ();
    m_modelName.clear ();
    m_globalOrigin.Zero ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DgnUnderlayDefInfo::SetSourceFileName (WCharCP name)
    {
    m_sourceFileName.clear ();
    if (NULL != name && 0 != name[0])
        m_sourceFileName.assign (name);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DgnUnderlayDefInfo::SetModelName (WCharCP name)
    {
    m_modelName.clear ();
    if (NULL != name && 0 != name[0])
        m_modelName.assign (name);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
int             DgnUnderlayDefInfo::GetXReferenceDepth () const
    {
    // ACAD does not seem to handle nested DGN ref's consistently.  For now set infinite nesting as the default nesting.
    return m_xrefDepth < 0 ? DWG_REFERENCE_NEST_DEPTH : m_xrefDepth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CollectDgnUnderlayDefinitionInfo ()
    {
    /*--------------------------------------------------------------------------------------------------------------
    The whole purpose of collecting DgnUnderlayDefInfos prior to importing DgnUnderlays is because we may have to
    open the DGN reference file to check for subunits.  The same DGN file may be used for multiple DgnDefinitions.  
    For efficiency we only want to open it once for all definitions that use the same file.
    --------------------------------------------------------------------------------------------------------------*/
    AcDbObjectId                objectId = this->GetFileHolder().GetDatabase()->namedObjectsDictionaryId ();
    AcDbSmartDictionaryPointer  mainDictionary (objectId, AcDb::kForRead);
    if (Acad::eOk != mainDictionary.openStatus())
        return  CantOpenObject;

    if (Acad::eOk != mainDictionary->getAt (L"ACAD_DGNDEFINITIONS", objectId))
        return  NullObjectId;

    AcDbSmartDictionaryPointer  dgndefList (objectId, AcDb::kForRead);
    if (Acad::eOk != dgndefList.openStatus())
        return  CantOpenObject;

    DgnUnderlayDefInfoCollector  dgndefCollector (this, dgndefList);
 
    return dgndefCollector.Collect ();
    }




/*---------------------------------------------------------------------------------------
    Code from here on saves underlays back to DWG
---------------------------------------------------------------------------------------*/
/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgUnderlay
{
public:
    ConvertFromDgnContext*      m_fromDgnContext;
    ElementHandleCP             m_attachmentElemHandle;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            Initialize (ConvertFromDgnContext* context, ElementHandleCP inElm)
    {
    m_fromDgnContext = context;
    m_attachmentElemHandle = inElm;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    GetOrAddUnderlayDefinition (AcDbUnderlayDefinition* underlayDef, WCharCP underlayName, WCharCP fileName, WCharCP itemName)
    {
    // get/add a new underlay definition from/to the DWG dictionary
    AcDbObjectId                objectId = m_fromDgnContext->GetFileHolder().GetDatabase()->namedObjectsDictionaryId ();
    AcDbSmartDictionaryPointer  mainDictionary (objectId, AcDb::kForWrite);
    if (Acad::eOk != mainDictionary.openStatus())
        return  AcDbObjectId::kNull;

    if (Acad::eOk != mainDictionary->getAt(underlayName, objectId))
        {
        // try adding ACAD_DGN/PDFDEFINITIONS to the main dictionary
        AcDbDictionary*         newDeflist = new AcDbDictionary ();
        objectId = m_fromDgnContext->AddObjectToDictionary (mainDictionary, newDeflist, underlayName, 0);
        if (objectId.isNull())
            {
            delete newDeflist;
            return  AcDbObjectId::kNull;
            }
        else
            {
            newDeflist->close ();
            }
        }

    AcDbSmartDictionaryPointer  underlayDeflist (objectId, AcDb::kForWrite);
    if (Acad::eOk != underlayDeflist.openStatus())
        return  AcDbObjectId::kNull;

    // compose a underlay entry name for search
    WString                     entryName (fileName);
    if (NULL != itemName && 0 != itemName[0])
        entryName += WString(L" - ") + WString(itemName);

    // if the entry name exists in the list, use it
    if (Acad::eOk == underlayDeflist->getAt(entryName.c_str(), objectId))
        return  objectId;

    // try adding the new underlay to the underlay def list dictionary
    objectId = m_fromDgnContext->AddObjectToDictionary (underlayDeflist, underlayDef, entryName.c_str(), 0);

    return  objectId;
    }

};  // IToDwgUnderlay


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ConvertReferenceToDgnUnderlay : public ToDwgUnderlay
{
public:
    // the constructor
    ConvertReferenceToDgnUnderlay (ConvertFromDgnContext* context, ElementHandleCP inElm) { __super::Initialize(context, inElm); }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert (AcDbDgnReference* dgnUnderlay, DgnAttachmentP dgnAttachment)
    {
    if (NULL == m_fromDgnContext || NULL == m_attachmentElemHandle || !m_attachmentElemHandle->IsValid() || NULL == dgnUnderlay || NULL == dgnAttachment)
        return  CantAccessMstnElement;

    if (REFERENCE_ATTACH_ELM != m_attachmentElemHandle->GetElementType())
        return  WrongMstnElementType;

    // update existing or create a new DGN underlay definition
    AcDbObjectId    dgnUnderlayDefId = dgnUnderlay->definitionId ();
    if (RealDwgSuccess != this->UpdateDgnUnderlayDefinition(dgnUnderlayDefId, dgnAttachment))
        return  CantCreateUnderlay;

    dgnUnderlay->setDefinitionId (dgnUnderlayDefId);

    // get clipping points
    DgnClipData             clipData;
    m_fromDgnContext->ExtractClipFromReference (clipData, dgnAttachment);

    // get transformation
    Transform               refTransform;
    DPoint3d                refBasePoint = DPoint3d::From(0, 0, 0);
    const ReferenceFileElm* refElem = &m_attachmentElemHandle->GetElementCP()->referenceFileElm;
    m_fromDgnContext->GetTransformsFromReference (refTransform, refBasePoint, clipData, dgnAttachment, refElem);

    dgnUnderlay->setTransform (RealDwgUtil::GeMatrix3dFromTransform(refTransform));

    this->SetClippingBoundary (dgnUnderlay, clipData, refTransform, dgnAttachment);

    dgnUnderlay->setIsOn (dgnAttachment->IsDisplayed());

    this->SetColorParams (dgnUnderlay, dgnAttachment);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void    SetClippingBoundary (AcDbDgnReference* dgnUnderlay, DgnClipData const& clipData, TransformCR refTransform, DgnAttachmentP dgnAttachment)
    {
    AcGePoint2dArray    acPoints (0, 10);

    if (dgnAttachment->IsClipped())
        {
        // get reference origin in target DWG units
        DPoint3d        refOrigin;
        refTransform.GetTranslation (refOrigin);
        // get reference rotation matrix
        RotMatrix       refMatrix;
        refTransform.GetMatrix (refMatrix);
        refMatrix.Invert ();

        // get clipping origin in target DWG units
        DPoint3d        clipOrigin = clipData.m_origin;
        Transform       transformToDwg = m_fromDgnContext->GetTransformFromDGN ();
        transformToDwg.Multiply (clipOrigin);

        // subtract clip origin from reference origin in the world coordinate system and transform it to the reference plane
        clipOrigin.Subtract (refOrigin);
        refMatrix.Multiply (clipOrigin);

        // we will also need to transform each clipping point from the clipping plane to the reference plane
        refMatrix.InitProduct (refMatrix, clipData.m_matrix);

        // will add the clipping origin on the reference plane
        Transform       transformToRef = Transform::From (refMatrix, clipOrigin);

        // will scale to DWG units
        RotMatrix       scaleMatrix;
        transformToDwg.GetMatrix (scaleMatrix);

        transformToRef.InitProduct (transformToRef, scaleMatrix);

        // now we are ready to collect the clippings and do the transformation
        for each (DPoint2d point in clipData.m_pointArray)
            {
            DPoint3d    point3d = DPoint3d::From(point.x, point.y);

            transformToRef.Multiply (point3d);

            point.Init (point3d);

            acPoints.append (RealDwgUtil::GePoint2dFromDPoint2d(point));
            }
        }

    // re-set clipping points
    Acad::ErrorStatus   es;
    es = dgnUnderlay->setClipBoundary (acPoints);
    es = dgnUnderlay->setIsClipped (dgnAttachment->IsClipped());
    es = dgnUnderlay->setClipInverted (clipData.m_isOutsideClip);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetColorParams (AcDbDgnReference* dgnUnderlay, DgnAttachmentP dgnAttachment)
    {
    UInt16      valueAdjustment = dgnAttachment->GetHsvValueAdjustment ();
    UInt16      satuationAdjustment = dgnAttachment->GetHsvSaturationAdjustment ();

    dgnUnderlay->setFade ((Adesk::UInt8)valueAdjustment);
    dgnUnderlay->setContrast (100);
    dgnUnderlay->setIsMonochrome (satuationAdjustment > 90);
    dgnUnderlay->setTransparency (m_fromDgnContext->GetTransparencyFromDgn(dgnAttachment->GetDisplayTransparency(), INVALID_LEVEL));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   UpdateDgnUnderlayDefinition (AcDbObjectId& dgndefId, DgnAttachmentCP dgnAttachment)
    {
    WString     fileName = dgnAttachment->GetAttachFileName ();
    if (fileName.empty())
        return  FileNotFound;

    WString     modelName = WString (dgnAttachment->GetAttachModelName());
    if (modelName.empty())
        {
        // get the default model name from ModelInfo
        ModelInfoCP     modelInfo = dgnAttachment->GetModelInfoCP ();
        if (NULL == modelInfo)
            modelName.assign (L"Default");  // should not happen
        else
            modelName.assign (modelInfo->GetName());
        }

    // make the file name as DGN underlay name
    WString     baseName;
    this->BuildDgnUnderlayName (fileName, baseName, modelName);
    
    // open existing or create a new DGN def
    AcDbDgnDefinitionPointer    dgnUnderlayDef(dgndefId, AcDb::kForWrite);
    if (Acad::eOk != dgnUnderlayDef.openStatus() && Acad::eOk != dgnUnderlayDef.create())
        {
        DIAGNOSTIC_PRINTF ("Error creating DGN Underlay definition!\n");
        return  CantCreateUnderlay;
        }

    // update the DGN def
    dgnUnderlayDef->setSourceFileName (fileName.c_str());
    dgnUnderlayDef->setItemName (modelName.c_str());

    // for an existing DGN definition, we are done updating it
    if (dgndefId.isValid())
        return  RealDwgSuccess;

    // there is no existing DGN underlay def, get one by names or add a new entry to DGN definition dictionary
    dgndefId = this->GetOrAddUnderlayDefinition (dgnUnderlayDef, L"ACAD_DGNDEFINITIONS", baseName.GetWCharCP(), modelName.GetWCharCP());

    return  dgndefId.isValid() ? RealDwgSuccess : CantCreateUnderlay;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            BuildDgnUnderlayName (WStringR fileName, WStringR baseName, WStringCR modelName)
    {
    AcString    xrefPath = m_fromDgnContext->XRefPathFromDgnPath (fileName.GetWCharCP(), fileName.GetWCharCP(), true, modelName.GetWCharCP(), m_fromDgnContext->GetModel(), NULL);

    // parse desired path components from xref path and original DGN attachment name
    WString     devName, dirName, extName;
    BeFileName::ParseName (&devName, &dirName, &baseName, NULL, xrefPath.kwszPtr());
    BeFileName::ParseName (NULL, NULL, NULL, &extName, fileName.GetWCharCP());

    // build DGN underlay file name from the path components
    BeFileName::BuildName (fileName, devName.GetWCharCP(), dirName.GetWCharCP(), baseName.GetWCharCP(), extName.GetWCharCP());
    }

};  // ConvertReferenceToDgnUnderlay



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/12
+===============+===============+===============+===============+===============+======*/
class           ConvertPdfRasterToPdfUnderlay : ToDwgUnderlay
{
public:
    // the constructor
    ConvertPdfRasterToPdfUnderlay (ConvertFromDgnContext* context, ElementHandleCP inElm) { __super::Initialize(context, inElm); }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert (AcDbPdfReference* pdfUnderlay, DgnRasterCP raster)
    {
    if (NULL == m_fromDgnContext || NULL == m_attachmentElemHandle || !m_attachmentElemHandle->IsValid() || NULL == pdfUnderlay || NULL == raster)
        return  CantAccessMstnElement;
    
    WStringCR   fileSpec = raster->GetOpenParams().GetFilespec ();
    WStringCR   fileName = raster->GetOpenParams().GetFilename ();

    // get existing or create a new PDf underlay definition
    AcDbObjectId    pdfUnderlayDefId = pdfUnderlay->definitionId();
    if (RealDwgSuccess != this->UpdatePdfUnderlayDefinition(pdfUnderlayDefId, fileName, fileSpec, raster))
        return  CantCreateUnderlay;

    pdfUnderlay->setDefinitionId (pdfUnderlayDefId);

    // we don't have a global variable to control image frame display, so turn it on if anyone is on
    IRasterAttachmentQuery* rasterQuery = dynamic_cast <IRasterAttachmentQuery*> (&m_attachmentElemHandle->GetHandler());
    if (NULL != rasterQuery && rasterQuery->GetDisplayBorderState(*m_attachmentElemHandle))
        m_fromDgnContext->GetFileHolder().GetDatabase()->setPdfframe (true);

    this->SetColorParams (pdfUnderlay, raster);

    RasterFileQuickInfo     pdfInfo;
    memset(&pdfInfo, 0, sizeof(pdfInfo));

    if (BSISUCCESS != mdlRaster_fileInfoMinimalGetExt(&pdfInfo, fileSpec.empty() ? fileName.c_str() : fileSpec.c_str(), m_fromDgnContext->GetModel()))
        return  CantCreateUnderlay;
    
    DPoint2d    pixelPerUors = DPoint2d::From (1.0, 1.0);
    Transform   transform = this->GetPdfTransform (pixelPerUors, raster, pdfInfo);
    pdfUnderlay->setTransform (RealDwgUtil::GeMatrix3dFromTransform(transform));

    this->SetClippingBoundary (pdfUnderlay, raster, pixelPerUors, pdfInfo.extent);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   UpdatePdfUnderlayDefinition (AcDbObjectId& pdfdefId, WStringCR fileName, WStringCR fileSpec, DgnRasterCP raster)
    {
    if (fileName.empty())
        return  FileNotFound;

    WString     baseName;
    BeFileName::ParseName (NULL, NULL, &baseName, NULL, fileName.GetWCharCP());

    WString     pdfPath, existingPdfPath;

    AcDbPdfDefinitionPointer    pdfUnderlayDef(pdfdefId, AcDb::kForWrite);
    if (Acad::eOk == pdfUnderlayDef.openStatus())
        {
        WCharCP existingName = pdfUnderlayDef->getSourceFileName ();
        if (NULL != existingName && 0 != existingName[0])
            existingPdfPath.assign (existingName);
        }

    // get the data to update/create the PDF def
    m_fromDgnContext->ImagePathFromDgnPath (pdfPath, fileSpec.GetWCharCP(), fileName.GetWCharCP(), existingPdfPath.GetWCharCP(), m_attachmentElemHandle->GetModelRef());

    WChar           pageNumberChars[128] = {0};
    RasterSourceCP  rasterSource = raster->GetRasterSourceCP ();
    if (NULL != rasterSource)
        {
        // get the PDF page that is referenced (1 base for Underlay vs 0 base for DGN)
        UInt32      pageNumber = rasterSource->GetPageNumber () + 1;

        if (BSISUCCESS != BeStringUtilities::Itow(pageNumberChars, pageNumber, _countof(pageNumberChars), 10))
            pageNumberChars[0] = 0;
        }

    // default to page number 1
    if (0 == pageNumberChars[0])
        pageNumberChars[0] = L'1';

    // create a new PDF def if no existing def found
    if (!pdfdefId.isValid() && Acad::eOk != pdfUnderlayDef.create())
        return  OutOfMemoryError;

    // update the PDF def
    pdfUnderlayDef->setSourceFileName (pdfPath.c_str());
    pdfUnderlayDef->setItemName (pageNumberChars);



    // for an existing PDF definition, we are done updating it
    if (pdfdefId.isValid())
        return  RealDwgSuccess;

    // there is no existing PDF underlay def, get or add a new entry from/to PDF definition dictionary
    pdfdefId = this->GetOrAddUnderlayDefinition (pdfUnderlayDef, L"ACAD_PDFDEFINITIONS", baseName.GetWCharCP(), pageNumberChars);

    return  pdfdefId.isValid() ? RealDwgSuccess : CantCreateUnderlay;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
Transform       GetPdfTransform (DPoint2dR pixelPerUors, DgnRasterCP raster, const RasterFileQuickInfo& pdfInfo)
    {
    //Fix TR TFS #2454; Extract extent directly from PDF file.
    DPoint2d    pdfExtent;
    RasterSourceCP  rasterSource = raster->GetRasterSourceCP ();
    if (NULL != rasterSource)
        RasterGeomFacility::GetDimensionForDWGUnderlay(*rasterSource,0,pdfExtent.x,pdfExtent.y);
 
    //Fix TFS 1112249: PDF raster attachment comes in at wrong scale when opening DWG file
    ModelInfoCR             modelInfo = m_fromDgnContext->GetModel()->GetModelInfo();
    SheetDefCP                sheetDef = modelInfo.GetSheetDefCP();

    if (sheetDef != nullptr)
    {
        UnitDefinition unitDef;
        double                  uorPerInch = 1.0, storagePerInch = 1.0;

        sheetDef->GetUnits(unitDef);

        UnitDefinition      unitInfoStd = UnitDefinition::GetStandardUnit(StandardUnit::EnglishInches);

        if (SUCCESS == unitDef.GetConversionFactorFrom(storagePerInch, unitInfoStd))
            uorPerInch = storagePerInch * modelInfo.GetUorPerStorage();

        pdfExtent.x *= uorPerInch;
        pdfExtent.y *= uorPerInch;
    }
    else
    {
        // scale pixel size to UOR's
        double      uorsPerStorage = m_fromDgnContext->GetScaleToDGN();
        pdfExtent.x *= uorsPerStorage;
        pdfExtent.y *= uorsPerStorage;
    }
    DPoint2d    uorsPerPixel =  DPoint2d::From (pdfExtent.x/ pdfInfo.extent.x,  pdfExtent.y/ pdfInfo.extent.y);

    // safe guard the scale. although this should never happen
    if (0.0 == uorsPerPixel.x)
        uorsPerPixel.x = 1.0;
    if (0.0 == uorsPerPixel.y)
        uorsPerPixel.y = 1.0;

    // scale pixel to target units
    pixelPerUors.Init (1.0 / uorsPerPixel.x, 1.0 / uorsPerPixel.y);

    // set the output transform from raster rotation, scale and translation
    RotMatrix   matrix = RotMatrix::From (raster->GetTransform());
    matrix.ScaleColumns (pixelPerUors.x, pixelPerUors.y, 1.0);

    DPoint3d    origin;
    raster->GetTransform().GetTranslation (origin);
    m_fromDgnContext->GetTransformFromDGN().Multiply (origin);

    Transform   transform = Transform::From (matrix, origin);

    return  transform;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetClippingBoundary (AcDbPdfReference* pdfUnderlay, DgnRasterCP raster, DPoint2dCR pixelPerUors, DPoint2dCR extents)
    {
    size_t      numPoints = 0;
    UShort      clipStatus = AD_IMAGE_NOT_CLIPPED;
    UShort      clipType = AD_IMAGE_CLIPBOUND_RECT;
    DPoint2d    clipPoints[RasterReference_MAXVERTICIESINCOMPONENT];
    bool        isMaskClip = false;

    m_fromDgnContext->CreateDwgImageClipBoundary (raster->GetClipProperties(), &numPoints, clipPoints, &clipStatus, &clipType, &isMaskClip, true);
    
    AcGePoint2dArray    acPoints (0, 10);
    if (AD_IMAGE_NOT_CLIPPED != clipStatus)
        {
        //TFS#9994; I don't think we need to AdjustDWGImageClipBoundary for underlay
        //m_fromDgnContext->AdjustDWGImageClipBoundary (clipPoints, &numPoints, &extents, clipType, clipStatus, true);

        for (size_t i = 0; i < numPoints; i++)
            {
            //Transform from pixel
            clipPoints[i].x /= pixelPerUors.x;
            clipPoints[i].y /= pixelPerUors.y;

            // transform from UOR's
            m_fromDgnContext->GetTransformFromDGN().Multiply (clipPoints[i], clipPoints[i]);

            acPoints.append (RealDwgUtil::GePoint2dFromDPoint2d(clipPoints[i]));
            }
        }

    pdfUnderlay->setClipBoundary (acPoints);
    pdfUnderlay->setIsClipped (raster->GetClipState() && AD_IMAGE_NOT_CLIPPED != clipStatus);
    pdfUnderlay->setClipInverted (isMaskClip);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetColorParams (AcDbPdfReference* pdfUnderlay, DgnRasterCP raster)
    {
    ImageColorMode  colorMode = raster->GetColorMode ();
    pdfUnderlay->setIsMonochrome (ImageColorMode::Monochrome == colorMode);
    pdfUnderlay->setContrast ((Adesk::Int8)((GetPercentFromSignedRange(raster->GetContrast()) + 100.0) / 2.0));

    // TFS#976776 If transparancy is off, there is no fade. Set fade value to 0.
    if (raster->GetTransparencyState())
        pdfUnderlay->setFade ((Adesk::Int8)((GetPercentFromRange (raster->GetImageTransparencyLevel()) + 100.0) / 2.0));
    else
        pdfUnderlay->setFade (0);
}

};  // ConvertPdfRasterToPdfUnderlay



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertFromDgnContext::IsDgnUnderlay (DgnAttachmentP dgnAttachment)
    {
    /*--------------------------------------------------------------------------------------------------------
    There are two ways a user can selectively save a DGN attachment to a DGN underlay:

    1) Select "DGN Underlay" in DWG SaveAs settings: all attachments of that category are set to DGN underlays.
    2) A DGN attachment with logical name prefix of English string "DGN Underlay"

    The savemergerefs app checks for both above cases, retains all such attachments that meet the criteria as
    DGN attachment, and then sets their logical names to "DGN Underlay".  So all we need here is to check for
    prefix "DGN Underlay" in the logical name.
    --------------------------------------------------------------------------------------------------------*/
    DgnFileP    attachedFile = dgnAttachment->GetDgnFileP ();
    if (NULL != attachedFile && attachedFile != this->GetFile())
        {
        WCharCP logicalName = dgnAttachment->GetLogicalName ();
        if (NULL != logicalName && 0 == _wcsnicmp(logicalName, REFERENCE_NAME_DgnUnderlay, _countof(REFERENCE_NAME_DgnUnderlay) - 1))
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsSameUnderlayFile (WStringCR dgnAttachName, const ACHAR* dwgAttachName)
    {
    WString     baseName, extName, dgnFileName;
    BeFileName::ParseName (NULL, NULL, &baseName, &extName, dgnAttachName.GetWCharCP());
    BeFileName::BuildName (dgnFileName, NULL, NULL, baseName.GetWCharCP(), extName.GetWCharCP());
    if (dgnFileName.empty())
        return  false;
    
    baseName.clear ();
    extName.clear ();

    WString     dwgFileName;
    BeFileName::ParseName (NULL, NULL, &baseName, &extName, dwgAttachName);
    BeFileName::BuildName (dwgFileName, NULL, NULL, baseName.GetWCharCP(), extName.GetWCharCP());
    if (dwgFileName.empty())
        return  false;

    return  0 == dgnFileName.CompareToI(dwgFileName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::DeleteUnusedUnderlayDefinitions ()
    {
    AcDbObjectId                objectId = this->GetFileHolder().GetDatabase()->namedObjectsDictionaryId ();
    AcDbSmartDictionaryPointer  mainDictionary (objectId, AcDb::kForRead);
    if (Acad::eOk != mainDictionary.openStatus())
        return  CantOpenObject;

    // try to delete unused DGN underlay definitions from the DGN def dictionary
    if (Acad::eOk == mainDictionary->getAt(L"ACAD_DGNDEFINITIONS", objectId))
        {
        AcDbSmartDictionaryPointer  dgndefList (objectId, AcDb::kForWrite);
        if (Acad::eOk == dgndefList.openStatus())
            {
            AcDbDictionaryIterator*     dictionaryIter = dgndefList->newIterator();
            for (; !dictionaryIter->done(); dictionaryIter->next())
                {
                AcDbDgnDefinitionPointer    dgnUnderlayDef(dictionaryIter->objectId(), AcDb::kForWrite);
                if (Acad::eOk != dgnUnderlayDef.openStatus())
                    {
                    DIAGNOSTIC_PRINTF ("Failed opening PDF underlay definition! [%ls]\n", acadErrorStatusText(dgnUnderlayDef.openStatus()));
                    continue;
                    }

                bool                    attachmentFound = false;
                for each (DgnUnderlayDefInfo dgndefInfo in m_dgnUnderlayDefInfoArray)
                    {
                    if (!IsSameUnderlayFile(dgndefInfo.GetSourceFileName(), dgnUnderlayDef->getSourceFileName()))
                        continue;

                    WString             modelName = dgndefInfo.GetModelName ();
                    if (modelName.empty() || 0 != modelName.CompareToI(dgnUnderlayDef->getItemName()))
                        continue;

                    // an attachment exists - do not delete the definition from the DGN def dictionary
                    attachmentFound = true;
                    break;
                    }

                if (!attachmentFound)
                    dgnUnderlayDef->erase ();
                }
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Failed opening ACAD_DGNDEFINITIONS dictionary! [%ls]\n", acadErrorStatusText(dgndefList.openStatus()));
            }
        }

    // try to delete unused PDF underlay definitions from the PDF def dictionary
    if (Acad::eOk == mainDictionary->getAt(L"ACAD_PDFDEFINITIONS", objectId))
        {
        AcDbSmartDictionaryPointer  pdfdefList (objectId, AcDb::kForWrite);
        if (Acad::eOk == pdfdefList.openStatus())
            {
            AcDbDictionaryIterator*     dictionaryIter = pdfdefList->newIterator();
            for (; !dictionaryIter->done(); dictionaryIter->next())
                {
                AcDbPdfDefinitionPointer    pdfUnderlayDef(dictionaryIter->objectId(), AcDb::kForWrite);
                if (Acad::eOk != pdfUnderlayDef.openStatus())
                    {
                    DIAGNOSTIC_PRINTF ("Failed opening PDF underlay definition! [%ls]\n", acadErrorStatusText(pdfUnderlayDef.openStatus()));
                    continue;
                    }

                // loop through raster attachments and find PDF's
                DgnRasterCollectionCR   rasterList = Raster::DgnRasterCollection::GetRasters (this->GetModel());
                bool                    attachmentFound = false;

                for (Raster::DgnRasterCollection::const_iterator iter = rasterList.begin(); iter != rasterList.end(); ++iter)
                    {
                    DgnRasterCP         raster = *iter;
                    if (NULL == raster)
                        continue;

                    // first check if they have same file names
                    if (!IsSameUnderlayFile(raster->GetOpenParams().GetFilename(), pdfUnderlayDef->getSourceFileName()))
                        continue;

                    // then check to see if the attachment is of a PDF file type
                    RasterSourceCP      rasterSource = raster->GetRasterSourceCP ();
                    if (NULL == rasterSource)
                        continue;

                    if (IMAGEFILE_PDF != rasterSource->GetRasterFile().GetFileFormat())
                        continue;

                    // a PDF attachment found - do not delete the definition from the PDF def dictionary
                    attachmentFound = true;
                    break;
                    }

                if (!attachmentFound)
                    pdfUnderlayDef->erase ();
                }
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Failed opening ACAD_DGNDEFINITIONS dictionary! [%ls]\n", acadErrorStatusText(pdfdefList.openStatus()));
            }
        }

    return  RealDwgSuccess;
    }

