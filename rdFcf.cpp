/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdFcf.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          04/13
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtFcf : public ToDgnExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbFcf*    acTolerance = AcDbFcf::cast (acObject);
    if (NULL == acTolerance)
        return  NullObject;

    RealDwgStatus   status = context.WorldDrawToElements (acTolerance, outElement);
    if (RealDwgSuccess == status)
        {
        DPoint3d    origin;
        RealDwgUtil::DPoint3dFromGePoint3d (origin, acTolerance->location());

        context.GetTransformToDGN().Multiply (origin);

        MSElementP  cellHeader = outElement.GetElementP ();
        if (NULL != cellHeader && CELL_HEADER_ELM == cellHeader->ehdr.type)
            {
            if (cellHeader->hdr.dhdr.props.b.is3d)
                cellHeader->cell_3d.origin = origin;
            else
                cellHeader->cell_2d.origin.Init (origin);
            }
        }

    return  status;
    }

};      // ToDgnExtFcf
