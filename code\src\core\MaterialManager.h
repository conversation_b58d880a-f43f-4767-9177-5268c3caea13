#pragma once

#include "../../include/ExportTypes.h"
#include <unordered_map>
#include <memory>
#include <mutex>

namespace IModelExport {

//=======================================================================================
// Material Manager - Handles material registration, conversion and caching
//=======================================================================================

class MaterialManager {
public:
    MaterialManager();
    ~MaterialManager();

    //===================================================================================
    // Material Registration and Retrieval
    //===================================================================================

    // Register a material and return its unique ID
    std::string RegisterMaterial(const Material& material);
    
    // Get material by ID
    bool GetMaterial(const std::string& materialId, Material& material) const;
    
    // Get all registered material IDs
    std::vector<std::string> GetAllMaterialIds() const;
    
    // Check if material exists
    bool HasMaterial(const std::string& materialId) const;
    
    // Remove material
    bool RemoveMaterial(const std::string& materialId);
    
    // Clear all materials
    void ClearMaterials();

    //===================================================================================
    // Material Search and Matching
    //===================================================================================

    // Find material by name
    std::string FindMaterialByName(const std::string& name) const;
    
    // Find similar material (by color, properties)
    std::string FindSimilarMaterial(const Material& material, double tolerance = 0.1) const;
    
    // Get materials by category/type
    std::vector<std::string> GetMaterialsByType(const std::string& type) const;

    //===================================================================================
    // Material Conversion and Mapping
    //===================================================================================

    // Convert material for specific export format
    Material ConvertMaterialForFormat(const Material& source, ExportFormat targetFormat) const;
    
    // Map material properties between formats
    std::unordered_map<std::string, std::string> MapMaterialProperties(
        const Material& material, ExportFormat sourceFormat, ExportFormat targetFormat) const;
    
    // Validate material for format compatibility
    bool ValidateMaterialForFormat(const Material& material, ExportFormat format, 
                                  std::vector<std::string>& warnings) const;

    //===================================================================================
    // Default Materials
    //===================================================================================

    // Create default materials for common use cases
    void CreateDefaultMaterials();
    
    // Get default material for specific purpose
    std::string GetDefaultMaterial(const std::string& purpose = "default") const;
    
    // Create material from color
    std::string CreateMaterialFromColor(const Color& color, const std::string& name = "");

    //===================================================================================
    // Material Libraries
    //===================================================================================

    // Load materials from library file
    bool LoadMaterialLibrary(const std::string& libraryPath);
    
    // Save materials to library file
    bool SaveMaterialLibrary(const std::string& libraryPath) const;
    
    // Import materials from external format
    bool ImportMaterials(const std::string& filePath, ExportFormat sourceFormat);
    
    // Export materials to external format
    bool ExportMaterials(const std::string& filePath, ExportFormat targetFormat) const;

    //===================================================================================
    // Material Analysis
    //===================================================================================

    // Analyze material usage statistics
    struct MaterialUsageStats {
        std::string materialId;
        std::string name;
        size_t usageCount;
        std::vector<std::string> usedByElements;
    };
    
    std::vector<MaterialUsageStats> GetMaterialUsageStatistics() const;
    
    // Find unused materials
    std::vector<std::string> FindUnusedMaterials() const;
    
    // Optimize material library (remove duplicates, unused materials)
    size_t OptimizeMaterialLibrary();

    //===================================================================================
    // Material Validation
    //===================================================================================

    // Validate material definition
    bool ValidateMaterial(const Material& material, std::vector<std::string>& errors) const;
    
    // Check for material conflicts
    std::vector<std::string> CheckMaterialConflicts() const;
    
    // Repair invalid materials
    bool RepairMaterial(Material& material) const;

private:
    //===================================================================================
    // Internal Data Structures
    //===================================================================================

    struct MaterialEntry {
        Material material;
        std::string id;
        size_t usageCount;
        std::chrono::system_clock::time_point createdTime;
        std::chrono::system_clock::time_point lastUsedTime;
        std::vector<std::string> usedByElements;
        std::unordered_map<std::string, std::string> metadata;
    };

    mutable std::mutex m_mutex;
    std::unordered_map<std::string, MaterialEntry> m_materials;
    std::unordered_map<std::string, std::string> m_nameToIdMap;
    std::atomic<size_t> m_nextMaterialId{1};
    
    // Default materials
    std::unordered_map<std::string, std::string> m_defaultMaterials;
    
    // Material conversion rules
    struct ConversionRule {
        ExportFormat sourceFormat;
        ExportFormat targetFormat;
        std::function<Material(const Material&)> converter;
    };
    std::vector<ConversionRule> m_conversionRules;

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Generate unique material ID
    std::string GenerateMaterialId();
    
    // Calculate material hash for similarity comparison
    size_t CalculateMaterialHash(const Material& material) const;
    
    // Compare materials for similarity
    double CalculateMaterialSimilarity(const Material& mat1, const Material& mat2) const;
    
    // Normalize material properties
    Material NormalizeMaterial(const Material& material) const;
    
    // Setup default conversion rules
    void SetupConversionRules();
    
    // Convert color space
    Color ConvertColorSpace(const Color& color, const std::string& sourceSpace, 
                           const std::string& targetSpace) const;
    
    // Validate texture path
    bool ValidateTexturePath(const std::string& texturePath) const;
    
    // Update material usage statistics
    void UpdateMaterialUsage(const std::string& materialId, const std::string& elementId);

    //===================================================================================
    // Format-Specific Conversion Methods
    //===================================================================================

    // Convert material for DWG format
    Material ConvertMaterialForDWG(const Material& source) const;
    
    // Convert material for IFC format
    Material ConvertMaterialForIFC(const Material& source) const;
    
    // Convert material for DGN format
    Material ConvertMaterialForDGN(const Material& source) const;
    
    // Convert material for USD format
    Material ConvertMaterialForUSD(const Material& source) const;

    //===================================================================================
    // Material Property Mapping
    //===================================================================================

    // Map diffuse color properties
    Color MapDiffuseColor(const Color& source, ExportFormat targetFormat) const;
    
    // Map specular properties
    Color MapSpecularColor(const Color& source, float shininess, ExportFormat targetFormat) const;
    
    // Map transparency/opacity
    float MapTransparency(float transparency, ExportFormat targetFormat) const;
    
    // Map texture properties
    std::string MapTexturePath(const std::string& texturePath, ExportFormat targetFormat) const;

    //===================================================================================
    // Material Library I/O
    //===================================================================================

    // Parse material from JSON
    bool ParseMaterialFromJson(const std::string& json, Material& material) const;
    
    // Serialize material to JSON
    std::string SerializeMaterialToJson(const Material& material) const;
    
    // Load materials from XML
    bool LoadMaterialsFromXml(const std::string& xmlPath);
    
    // Save materials to XML
    bool SaveMaterialsToXml(const std::string& xmlPath) const;

    //===================================================================================
    // Validation Helpers
    //===================================================================================

    // Validate color values
    bool ValidateColor(const Color& color, std::vector<std::string>& errors) const;
    
    // Validate material name
    bool ValidateMaterialName(const std::string& name, std::vector<std::string>& errors) const;
    
    // Validate texture references
    bool ValidateTextureReferences(const Material& material, std::vector<std::string>& errors) const;
    
    // Check for circular dependencies
    bool CheckCircularDependencies(const std::string& materialId) const;
};

//=======================================================================================
// Material Utility Functions
//=======================================================================================

namespace MaterialUtils {
    
    // Create material from basic properties
    Material CreateBasicMaterial(const std::string& name, const Color& diffuseColor);
    
    // Create metallic material
    Material CreateMetallicMaterial(const std::string& name, const Color& baseColor, float metallic, float roughness);
    
    // Create glass material
    Material CreateGlassMaterial(const std::string& name, const Color& tint, float transparency);
    
    // Create textured material
    Material CreateTexturedMaterial(const std::string& name, const std::string& diffuseTexture);
    
    // Blend two materials
    Material BlendMaterials(const Material& mat1, const Material& mat2, float blendFactor);
    
    // Calculate material brightness
    float CalculateBrightness(const Material& material);
    
    // Adjust material brightness
    Material AdjustBrightness(const Material& material, float factor);
    
    // Convert material to grayscale
    Material ConvertToGrayscale(const Material& material);
    
    // Generate material preview color
    Color GeneratePreviewColor(const Material& material);
    
    // Check if materials are equivalent
    bool AreMaterialsEquivalent(const Material& mat1, const Material& mat2, double tolerance = 1e-6);
    
    // Get material type string
    std::string GetMaterialTypeString(const Material& material);
    
    // Parse material type from string
    bool ParseMaterialType(const std::string& typeString, Material& material);
}

} // namespace IModelExport
