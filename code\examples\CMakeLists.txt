# DWG Complete Implementation Examples

cmake_minimum_required(VERSION 3.16)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
)

# Common DWG sources
set(DWG_COMMON_SOURCES
    ../src/formats/dwg/entities/DWGEntityProcessor.cpp
    ../src/formats/dwg/entities/DWGEntityProcessorFactory.cpp
    ../src/formats/dwg/entities/DWGSplineProcessor.cpp
    ../src/formats/dwg/entities/DWGPolylineProcessor.cpp
    ../src/formats/dwg/entities/DWG3DEntityProcessor.cpp
    ../src/formats/dwg/entities/DWGDimensionProcessor.cpp
    ../src/formats/dwg/entities/DWGBlockProcessor.cpp
    ../src/formats/dwg/geometry/DWGGeometryProcessor.cpp
    ../src/formats/dwg/styles/DWGStyleManager.cpp
    ../src/formats/dwg/DWGExporter.cpp
)

# Complete example executable
add_executable(dwg_complete_example
    dwg_complete_example.cpp
    ${DWG_COMMON_SOURCES}
)

# Compiler definitions
target_compile_definitions(dwg_complete_example PRIVATE
    # Disable RealDWG for example (unless available)
    # REALDWG_AVAILABLE
)

# Compiler flags
if(MSVC)
    target_compile_options(dwg_complete_example PRIVATE
        /W4                     # Warning level 4
        /WX-                    # Don't treat warnings as errors for examples
        /permissive-            # Disable non-conforming code
        /Zc:__cplusplus         # Enable correct __cplusplus macro
    )
else()
    target_compile_options(dwg_complete_example PRIVATE
        -Wall                   # Enable all warnings
        -Wextra                 # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -Wno-unused-parameter   # Disable unused parameter warnings for examples
    )
endif()

# Set output directory
set_target_properties(dwg_complete_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
)

# Optional: Link with RealDWG if available
if(REALDWG_FOUND)
    target_link_libraries(dwg_complete_example ${REALDWG_LIBRARIES})
    target_include_directories(dwg_complete_example PRIVATE ${REALDWG_INCLUDE_DIRS})
    target_compile_definitions(dwg_complete_example PRIVATE REALDWG_AVAILABLE)
endif()

# Add custom target to run the example
add_custom_target(run_dwg_example
    COMMAND dwg_complete_example
    DEPENDS dwg_complete_example
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/examples
    COMMENT "Running DWG complete implementation example"
)

# Installation
install(TARGETS dwg_complete_example
    RUNTIME DESTINATION examples
    COMPONENT Examples
)
