#----------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/filehandler/DwgFileHandler.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#----------------------------------------------------------------------------------------

%if !defined (RealDwgVersion)
RealDwgVersion = 2023
%endif

appName         = DwgFileHandler
NOSTRICT        = 1

%include mdl.mki
%if BUILD_USING_VS2010
PublicApiIncludes    = -I$(build)VC10Api/PublicAPI/ -I${BuildContext}/VendorAPI/
PublicApiRscIncludes = -i$(build)VC10Api/PublicAPI/
%endif
%include corelibs.mki
%include realdwg.mki

oldObjDir %= $(o)
objDir   = $(oldObjDir)$(RealDwgVersion)/

#----------------------------------------------------------------------
#   List non-translatable resource modules.
#----------------------------------------------------------------------
appRscs         = $(rscObjects)DwgFileHandlerMod.rsc

cDefs +% -DRealDwgVersion=$(RealDwgVersion)

#----------------------------------------------------------------------
#   Define search directories. The RealDWG include subdirectories must
#   be cincapnd'ed here because the base RealDWG #include files include
#   them without subdirectory specifications.
#----------------------------------------------------------------------
dirToSearch     = $(BuildContext)VendorAPI/RealDwg/Base/
%include cincapnd.mki

dirToSearch     = $(realDwgFileIODir)
%include cincapnd.mki

#----------------------------------------------------------------------
#   Create output directories.
#----------------------------------------------------------------------
always:
    !~@mkdir $(o)
    ~mkdir $(rscObjects)
    ~mkdir $(RealDwgFileHandlerDir)

%if defined (PRG)
always:
    !~@mkdir $(RealDwgSymbolDir)
    ~mkdir $(RealDwgMapDir)

%endif

#----------------------------------------------------------------------
#   Set up to use dlmlink.mki
#
#   Note: we need to use the obscure DLM_DELAYLOADHOOK feature because we
#         build this .mke file with different compilers at different times.
#         Without that, it can leave DelayLoadHook.obj in the common directory
#         compiled with the wrong compiler.
#----------------------------------------------------------------------
DLM_NAME                    =   $(appName)
DLM_OBJECT_DEST             =   $(o)
DLM_LIBDEF_SRC              =   $(baseDir)
DLM_OBJECT_FILES            =   $(appObjects)
DLM_EXPORT_DEST             =   $(o)
DLM_NOENTRY                 =   1
DLM_NO_INITIALIZE_FUNCTION  =   1
DLM_NO_DEF                  =   1
DLM_NO_DLS                  =   1
DLM_DEST                    =   $(RealDwgFileHandlerDir)
DLM_DELAYLOADHOOK           =   $(tools)delayLoadHook.cpp
DLM_CREATE_LIB_CONTEXT_LINK =   1
DLM_CONTEXT_LOCATION        =   $(BuildContext)Delivery/RealDwg$(RealDwgVersion)/
DLM_LIB_CONTEXT_LOCATION    =   $(BuildContext)Delivery/RealDwg$(RealDwgVersion)/

LINKER_LIBRARIES            =   $(RealDwgFileIOLib)                             \
                                $(RealDwgCoreLibs)                              \
                                $(DgnPlatformElementHandlerLibs)

LINKER_LIBRARIES_DELAYLOADED =  $(BuildContextSubPartsLib)$(DgnDisplayUILib)    \
%if RealDwgVersion >= 2017
                                $(BuildContextSubPartsLib)AcPal.lib             \
%endif
                                msi.lib
appIncludes                 =   $(baseDir)DwgMstnHost.h                         \
                                $(baseDir)DwgFileHandlerIds.h

#----------------------------------------------------------------------
#   Build non-translatable resources.
#----------------------------------------------------------------------
$(rscObjects)DwgFileHandlerMod.rsc      : $(baseDir)DwgFileHandlerMod.r

#-----------------------------------------------------------------------------
# Compile DLM Source Files
#-----------------------------------------------------------------------------
MultiCompileDepends=$(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)DwgMstnHost$(oext)             : $(baseDir)DwgMstnHost.cpp $(appIncludes) ${MultiCompileDepends}

$(o)DwgFileHandler$(oext)          : $(baseDir)DwgFileHandler.cpp $(appIncludes) ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

%include dlmlink.mki

#----------------------------------------------------------------------
# Link the MA
#----------------------------------------------------------------------
MA_NAME         = $(appName)
RIGHTSCOMPLIANT = true
MA_DEST         = $(mdlFileHandler)
MA_RSC_FILES    = $(appRscs)
MA_NO_VERSION   = 1

%include $(SharedMki)malink.mki

MuiBaseName     = $(appName).ma
MuiTranskitDeliveryDir = $(ContextDeliveryDir)TransKit/MdlApps/DwgFileHandler/

%include $(PrivMki)CreateAndSymlinkMdlAppMui.mki

