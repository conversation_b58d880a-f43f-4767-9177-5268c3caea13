/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnCone.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* ToDWG for cone element.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtCone : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool        ElementHasFull3dRange (MSElementCP element)
    {
    DRange3d dRange;
    DataConvert::ScanRangeToDRange3d (dRange, element->hdr.dhdr.range);

    return  fabs(dRange.high.x - dRange.low.x) > 1 &&
            fabs(dRange.high.y - dRange.low.y) > 1 &&
            fabs(dRange.high.z - dRange.low.z) > 1;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static AcRxClass*  GetSolidOrSurfaceAcRxClass (bool isSolid, bool isFlat, ElementHandleCR elemHandle, IDwgConversionSettings& settings)
    {
    SolidSurfaceMapping mapping = settings.GetSolidSurfaceMapping (isFlat);

    switch (mapping)
        {
        default:
        case SolidSurface_Acis:
            {
            if (isSolid)
                {
                if (ElementHasFull3dRange (elemHandle.GetElementCP()))
                    return AcDb3dSolid::desc();
                else
                    return AcDbRegion::desc();
                }
            else
                return AcDbBody::desc();
            }

        case SolidSurface_Polyface:
            return AcDbPolyFaceMesh::desc();

        case SolidSurface_Wireframe:
            return NULL;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ConeHandler& coneHandler, ElementHandleCR elemHandle, ConvertFromDgnContextR context) const
    {
    // If the cone is an uncapped a right circular cylinder, we can represent it as a circle with an extrusion.
    // If it's a capped zero-height cylinder, we can represent it as an AcDbEllipse (which can be filled).
    // Otherwise we have to create it as a solid element, and how we do that depends on settings.
    double      topRadius    = coneHandler.ExtractTopRadius (elemHandle);
    double      bottomRadius = coneHandler.ExtractBottomRadius (elemHandle);
    DPoint3dCP  topCenter    = coneHandler.ExtractTopCenter (elemHandle);
    DPoint3dCP  bottomCenter = coneHandler.ExtractBottomCenter (elemHandle);
    bool        capped       = coneHandler.ExtractCapFlag (elemHandle);
    bool        isCylinder   = fabs(topRadius - bottomRadius) < TOLERANCE_UORPointEqual;

    AcRxClass*      type;

    if (isCylinder && !capped)
        type = AcDbCircle::desc();
    else if (isCylinder && capped && bottomCenter->Distance(*topCenter) < TOLERANCE_UORPointEqual)
        type = AcDbEllipse::desc();
    else 
        type = GetSolidOrSurfaceAcRxClass (!capped, false, elemHandle, context.GetSettings());

    return type;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetNormal (DPoint3dR roundedNormal, ConeHandler& coneHandler, ElementHandleCR elemHandle, TransformCR transform) const
    {
    RotMatrix       rotMatrix;
    DVec3d          column2;
    coneHandler.ExtractRotation (rotMatrix, elemHandle);
    rotMatrix.GetColumn (column2, 2);
    transform.MultiplyMatrixOnly (column2);
    RealDwgUtil::RoundDirectionVector (roundedNormal, column2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/14
+---------------+---------------+---------------+---------------+---------------+------*/
AcGeVector3d    GetMajorAxis (double radius, ConeHandler& coneHandler, ElementHandleCR elemHandle, TransformCR transform) const
    {
    RotMatrix   rotMatrix;
    coneHandler.ExtractRotation (rotMatrix, elemHandle);

    DVec3d      xAxis;
    rotMatrix.GetColumn (xAxis, 0);

    transform.MultiplyMatrixOnly (xAxis);
    xAxis.Normalize ();
    xAxis.ScaleToLength (radius);

    return  RealDwgUtil::GeVector3dFromDVec3d (xAxis);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbCircleFromCone (AcDbCircle* acCircle, ConeHandler& coneHandler, ElementHandleCR elemHandle, ConvertFromDgnContextR context) const
    {
    // we know that the (topRadius == bottomRadius) and !capped.
    double      radius       = coneHandler.ExtractTopRadius (elemHandle) * context.GetScaleFromDGN();
    DPoint3d    topCenter    = *coneHandler.ExtractTopCenter (elemHandle);
    DPoint3d    bottomCenter = *coneHandler.ExtractBottomCenter (elemHandle);

    TransformCR transform   = context.GetTransformFromDGN ();
    transform.Multiply (topCenter, topCenter);
    transform.Multiply (bottomCenter, bottomCenter);
    
    DVec3d  extrusionVector;
    double extrusionDistance = extrusionVector.NormalizedDifference (topCenter, bottomCenter);

    DPoint3d roundedNormal;
    this->GetNormal (roundedNormal, coneHandler, elemHandle, transform);

    if(roundedNormal.DotProduct(extrusionVector) < 0)
        {
        roundedNormal.Negate();
        }

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (context.GetSettings().IsZeroZCoordinateEnforced())
        bottomCenter.z = 0.0;

    acCircle->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (roundedNormal));
    acCircle->setCenter (RealDwgUtil::GePoint3dFromDPoint3d (bottomCenter));
    acCircle->setRadius (radius);
    acCircle->setThickness (extrusionDistance);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbEllipseFromCone (AcDbEllipse* acEllipse, ConeHandler& coneHandler, ElementHandleR elemHandle, ConvertFromDgnContextR context) const
    {
    // we know that it is a zero height cylinder : (topRadius == bottomRadius) and (topCenter == bottomCenter). 
    double      radius      = coneHandler.ExtractTopRadius (elemHandle) * context.GetScaleFromDGN();
    DPoint3d    center      = *coneHandler.ExtractBottomCenter (elemHandle);
    bool        capped      = coneHandler.ExtractCapFlag (elemHandle);

    TransformCR transform   = context.GetTransformFromDGN ();
    transform.Multiply (center, center);
    
    DPoint3d    roundedNormal;
    this->GetNormal (roundedNormal, coneHandler, elemHandle, transform);

    AcGeVector3d    majorAxis = this->GetMajorAxis (radius, coneHandler, elemHandle, transform);

    Acad::ErrorStatus   es = acEllipse->set (RealDwgUtil::GePoint3dFromDPoint3d (center), RealDwgUtil::GeVector3dFromDPoint3d (roundedNormal), majorAxis, 1.0);

    if (Acad::eOk != es)
        DIAGNOSTIC_PRINTF ("Error creating AcDbEllipse from element ID=%I64d [%ls]\n", elemHandle.GetElementId(), acadErrorStatusText(es));

    return  Acad::eOk == es ? RealDwgSuccess : BadData;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // The conversion from DWG never creates a cone.
    assert (NULL == existingObject);

    ConeHandler*    coneHandler;
    if (NULL == (coneHandler = dynamic_cast <ConeHandler*> (&elemHandle.GetHandler())))
        {
        assert (false);
        return NoConversionMethod;
        }

    AcRxClass*  typeNeeded = GetTypeNeeded (*coneHandler, elemHandle, context);

    DropGeometry    dropSolids (DropGeometry::OPTION_Solids);
    dropSolids.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

    // drop to wireframe
    if (NULL == typeNeeded)
        return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropSolids);

    // do not create a new solid - it shall be created by AcDbBody::acisIn, but preserve existing type
    if (RealDwgUtil::IsObjectAcisType(existingObject) || !RealDwgUtil::IsClassAcisType(typeNeeded))
        acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    else
        acObject = nullptr;

    // here we have created an AcDbObject of some type. Populate it according to type.
    AcDbCircle* acCircle;
    if (NULL != (acCircle = AcDbCircle::cast (acObject)))
        return SetAcDbCircleFromCone (acCircle, *coneHandler, elemHandle, context);

    AcDbEllipse* acEllipse;
    if (NULL != (acEllipse = AcDbEllipse::cast (acObject)))
        return SetAcDbEllipseFromCone (acEllipse, *coneHandler, elemHandle, context);

    // solids, regions, bodies all treated the same.
    if (RealDwgUtil::IsClassAcisType(typeNeeded))
        {
        AcDbObjectP     newObject = NULL;
        RealDwgStatus   status =  context.ConvertSolidElementToAcis (newObject, acObject, elemHandle);
        if (RealDwgSuccess == status && nullptr == acObject)
            {
            acObject = newObject;
            }
        else if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
            {
            if (acObject->objectId().isValid())
                {
                if (Acad::eOk != acObject->handOverTo(newObject))
                    acObject->erase ();
                }
            else if (acObject->isNewObject())
                {
                delete acObject;
                }
            acObject = newObject;
            status = RealDwgSuccess;
            }
        return  status;
        }

    AcDbPolyFaceMesh* acPolyFaceMesh;
    if (NULL != (acPolyFaceMesh = AcDbPolyFaceMesh::cast (acObject)))
        {
        RealDwgStatus   status = context.SetAcDbPolyfaceMeshFromElement (acPolyFaceMesh, elemHandle);
        if (RealDwgSuccess == status && acPolyFaceMesh != acObject)
            {
            // a new polyface has replaced existing one - delete the old object
            delete acObject;
            acObject = acPolyFaceMesh;
            }
        return  status;
        }

    assert (false);
    return NoConversionMethod;
    }

};


