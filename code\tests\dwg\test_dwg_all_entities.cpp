#include <gtest/gtest.h>
#include "../../src/formats/dwg/entities/DWGEntityProcessorFactory.h"
#include "../../src/formats/dwg/entities/DWG3DEntityProcessor.h"
#include "../../src/formats/dwg/entities/DWGDimensionProcessor.h"
#include "../../src/formats/dwg/entities/DWGBlockProcessor.h"
#include "../../src/formats/dwg/entities/DWGImageProcessor.h"
#include "../../src/formats/dwg/entities/DWGTableProcessor.h"
#include "../../src/formats/dwg/entities/DWGMlineProcessor.h"
#include "../../src/formats/dwg/entities/DWGViewportProcessor.h"
#include "../../src/formats/dwg/DWGExporter.h"

using namespace IModelExport;

//=======================================================================================
// Mock DWG Exporter for Testing
//=======================================================================================

class MockDWGExporter : public DWGExporter {
public:
    MockDWGExporter() : DWGExporter() {}
    
    // Override methods for testing
    Point3d TransformPoint(const Point3d& point) const override {
        return point; // No transformation for testing
    }
    
    Vector3d TransformVector(const Vector3d& vector) const override {
        return vector; // No transformation for testing
    }
    
    double TransformLength(double length) const override {
        return length; // No transformation for testing
    }
    
    void LogError(const std::string& message) override {
        m_testErrors.push_back(message);
    }
    
    void LogWarning(const std::string& message) override {
        m_testWarnings.push_back(message);
    }
    
    void LogInfo(const std::string& message) override {
        m_testInfos.push_back(message);
    }
    
    // Test accessors
    const std::vector<std::string>& GetTestErrors() const { return m_testErrors; }
    const std::vector<std::string>& GetTestWarnings() const { return m_testWarnings; }
    const std::vector<std::string>& GetTestInfos() const { return m_testInfos; }
    
    void ClearTestLogs() {
        m_testErrors.clear();
        m_testWarnings.clear();
        m_testInfos.clear();
    }

private:
    std::vector<std::string> m_testErrors;
    std::vector<std::string> m_testWarnings;
    std::vector<std::string> m_testInfos;
};

//=======================================================================================
// Test Fixture
//=======================================================================================

class DWGAllEntitiesTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_mockExporter = std::make_unique<MockDWGExporter>();
    }
    
    void TearDown() override {
        m_mockExporter.reset();
    }
    
    ElementInfo CreateTestElement(ElementType type, const std::string& id = "test_element") {
        ElementInfo element;
        element.id = id;
        element.type = type;
        return element;
    }
    
    std::unique_ptr<MockDWGExporter> m_mockExporter;
};

//=======================================================================================
// Entity Processor Factory Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, FactoryBasicFunctionality) {
    // Test supported entity types
    auto supportedTypes = DWGEntityProcessorFactory::GetSupportedEntityTypes();
    EXPECT_FALSE(supportedTypes.empty());
    
    // Test categories
    auto categories = DWGEntityProcessorFactory::GetProcessorCategories();
    EXPECT_FALSE(categories.empty());
    
    // Test basic entity support
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Line"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Circle"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Text"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Spline"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Polyline"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Hatch"));
    
    // Test unsupported entity
    EXPECT_FALSE(DWGEntityProcessorFactory::IsEntityTypeSupported("UnsupportedEntity"));
}

TEST_F(DWGAllEntitiesTest, FactoryProcessorCreation) {
    // Test creating basic processors
    auto lineProcessor = DWGEntityProcessorFactory::CreateProcessor("Line", m_mockExporter.get());
    ASSERT_NE(lineProcessor, nullptr);
    EXPECT_EQ(lineProcessor->GetProcessorName(), "DWGLineProcessor");
    
    auto circleProcessor = DWGEntityProcessorFactory::CreateProcessor("Circle", m_mockExporter.get());
    ASSERT_NE(circleProcessor, nullptr);
    EXPECT_EQ(circleProcessor->GetProcessorName(), "DWGCircleProcessor");
    
    auto textProcessor = DWGEntityProcessorFactory::CreateProcessor("Text", m_mockExporter.get());
    ASSERT_NE(textProcessor, nullptr);
    EXPECT_EQ(textProcessor->GetProcessorName(), "DWGTextProcessor");
    
    // Test creating advanced processors
    auto splineProcessor = DWGEntityProcessorFactory::CreateProcessor("Spline", m_mockExporter.get());
    ASSERT_NE(splineProcessor, nullptr);
    EXPECT_EQ(splineProcessor->GetProcessorName(), "DWGSplineProcessor");
    
    auto polylineProcessor = DWGEntityProcessorFactory::CreateProcessor("Polyline", m_mockExporter.get());
    ASSERT_NE(polylineProcessor, nullptr);
    EXPECT_EQ(polylineProcessor->GetProcessorName(), "DWGPolylineProcessor");
    
    auto hatchProcessor = DWGEntityProcessorFactory::CreateProcessor("Hatch", m_mockExporter.get());
    ASSERT_NE(hatchProcessor, nullptr);
    EXPECT_EQ(hatchProcessor->GetProcessorName(), "DWGHatchProcessor");
}

TEST_F(DWGAllEntitiesTest, FactoryBatchCreation) {
    // Test creating all processors
    auto allProcessors = DWGEntityProcessorFactory::CreateAllProcessors(m_mockExporter.get());
    EXPECT_FALSE(allProcessors.empty());
    
    // Verify we have processors for major categories
    EXPECT_TRUE(allProcessors.find("Line") != allProcessors.end());
    EXPECT_TRUE(allProcessors.find("Circle") != allProcessors.end());
    EXPECT_TRUE(allProcessors.find("Text") != allProcessors.end());
    EXPECT_TRUE(allProcessors.find("Spline") != allProcessors.end());
    EXPECT_TRUE(allProcessors.find("Polyline") != allProcessors.end());
    EXPECT_TRUE(allProcessors.find("Hatch") != allProcessors.end());
    
    // Test creating processors by category
    auto basic2DProcessors = DWGEntityProcessorFactory::CreateProcessorsForCategory("Basic2D", m_mockExporter.get());
    EXPECT_FALSE(basic2DProcessors.empty());
    
    auto textProcessors = DWGEntityProcessorFactory::CreateProcessorsForCategory("Text", m_mockExporter.get());
    EXPECT_FALSE(textProcessors.empty());
}

//=======================================================================================
// 3D Entity Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, 3DEntityProcessorBasicFunctionality) {
    DWG3DEntityProcessor processor(m_mockExporter.get());
    
    // Test Face3D geometry
    Face3DGeometry face;
    face.vertices = {
        Point3d(0, 0, 0),
        Point3d(10, 0, 0),
        Point3d(10, 10, 0),
        Point3d(0, 10, 0)
    };
    face.normal = Vector3d(0, 0, 1);
    
    EXPECT_TRUE(face.IsValid());
    EXPECT_TRUE(face.IsQuad());
    EXPECT_FALSE(face.IsTriangle());
    
    auto status = processor.ProcessFace3D(face, "3DLayer");
    EXPECT_EQ(status, DWGProcessingStatus::Skipped); // Without RealDWG
}

TEST_F(DWGAllEntitiesTest, MeshGeometryValidation) {
    DWG3DEntityProcessor processor(m_mockExporter.get());
    
    // Create test mesh
    MeshGeometry mesh;
    mesh.type = MeshGeometry::Type::PolygonMesh;
    mesh.vertices = {
        Point3d(0, 0, 0), Point3d(10, 0, 0), Point3d(20, 0, 0),
        Point3d(0, 10, 0), Point3d(10, 10, 0), Point3d(20, 10, 0)
    };
    mesh.faces = {{0, 1, 4, 3}, {1, 2, 5, 4}};
    mesh.meshM = 3;
    mesh.meshN = 2;
    
    auto validation = processor.ValidateMeshGeometry(mesh);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidVertices);
    EXPECT_TRUE(validation.hasValidTopology);
}

//=======================================================================================
// Dimension Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, DimensionProcessorBasicFunctionality) {
    DWGDimensionProcessor processor(m_mockExporter.get());
    
    // Test linear dimension
    DimensionGeometry dimension;
    dimension.type = DimensionGeometry::Type::Linear;
    dimension.defPoint1 = Point3d(0, 0, 0);
    dimension.defPoint2 = Point3d(100, 0, 0);
    dimension.dimLinePoint = Point3d(50, 20, 0);
    dimension.textPosition = Point3d(50, 20, 0);
    
    EXPECT_TRUE(dimension.IsValid());
    
    auto validation = processor.ValidateDimensionGeometry(dimension);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidPoints);
    
    auto status = processor.ProcessDimension(dimension, "DimensionLayer");
    EXPECT_EQ(status, DWGProcessingStatus::Skipped); // Without RealDWG
}

TEST_F(DWGAllEntitiesTest, LeaderGeometryValidation) {
    DWGDimensionProcessor processor(m_mockExporter.get());
    
    LeaderGeometry leader;
    leader.type = LeaderGeometry::Type::Straight;
    leader.vertices = {
        Point3d(0, 0, 0),
        Point3d(50, 50, 0),
        Point3d(100, 50, 0)
    };
    leader.arrowPoint = Point3d(0, 0, 0);
    leader.textPoint = Point3d(100, 50, 0);
    leader.text = "Leader Text";
    
    EXPECT_TRUE(leader.IsValid());
    
    auto validation = processor.ValidateLeaderGeometry(leader);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidVertices);
    EXPECT_TRUE(validation.hasValidText);
}

//=======================================================================================
// Block Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, BlockProcessorBasicFunctionality) {
    DWGBlockProcessor processor(m_mockExporter.get());
    
    // Test block definition
    BlockDefinition blockDef;
    blockDef.name = "TestBlock";
    blockDef.description = "Test block for unit testing";
    blockDef.basePoint = Point3d(0, 0, 0);
    
    // Add some entities to the block
    BlockDefinition::BlockEntity entity1;
    entity1.entityType = "Line";
    entity1.geometry = {Point3d(0, 0, 0), Point3d(10, 10, 0)};
    entity1.layer = "0";
    blockDef.entities.push_back(entity1);
    
    EXPECT_TRUE(blockDef.IsValid());
    EXPECT_TRUE(blockDef.HasGeometry());
    EXPECT_EQ(blockDef.GetEntityCount(), 1);
    
    auto validation = processor.ValidateBlockDefinition(blockDef);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidName);
    EXPECT_TRUE(validation.hasValidGeometry);
}

TEST_F(DWGAllEntitiesTest, BlockReferenceValidation) {
    DWGBlockProcessor processor(m_mockExporter.get());
    
    BlockReference blockRef;
    blockRef.blockName = "TestBlock";
    blockRef.position = Point3d(100, 100, 0);
    blockRef.scale = Vector3d(1, 1, 1);
    blockRef.rotation = 0.0;
    
    EXPECT_TRUE(blockRef.IsValid());
    EXPECT_FALSE(blockRef.IsArrayInsert());
    
    auto validation = processor.ValidateBlockReference(blockRef);
    EXPECT_TRUE(validation.hasValidBlockName);
    EXPECT_TRUE(validation.hasValidPosition);
    EXPECT_TRUE(validation.hasValidScale);
}

//=======================================================================================
// Image Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, ImageProcessorBasicFunctionality) {
    DWGImageProcessor processor(m_mockExporter.get());
    
    // Test image geometry
    ImageGeometry image;
    image.imagePath = "test_image.jpg";
    image.imageFileName = "test_image.jpg";
    image.insertionPoint = Point3d(0, 0, 0);
    image.uVector = Vector3d(100, 0, 0);
    image.vVector = Vector3d(0, 100, 0);
    image.imageWidth = 100.0;
    image.imageHeight = 100.0;
    image.pixelWidth = 1024;
    image.pixelHeight = 1024;
    image.format = ImageGeometry::Format::JPEG;
    
    EXPECT_TRUE(image.IsValid());
    EXPECT_NEAR(image.GetAspectRatio(), 1.0, 1e-6);
    
    auto validation = processor.ValidateImageGeometry(image);
    EXPECT_TRUE(validation.hasValidDimensions);
    EXPECT_TRUE(validation.hasValidPosition);
    EXPECT_EQ(validation.detectedFormat, ImageGeometry::Format::JPEG);
}

TEST_F(DWGAllEntitiesTest, UnderlayGeometryValidation) {
    DWGImageProcessor processor(m_mockExporter.get());
    
    UnderlayGeometry underlay;
    underlay.type = UnderlayGeometry::Type::PDF;
    underlay.filePath = "test_drawing.pdf";
    underlay.fileName = "test_drawing.pdf";
    underlay.layoutName = "Layout1";
    underlay.insertionPoint = Point3d(0, 0, 0);
    underlay.uVector = Vector3d(210, 0, 0);  // A4 width
    underlay.vVector = Vector3d(0, 297, 0);  // A4 height
    underlay.pdf.pageNumber = 1;
    
    EXPECT_TRUE(underlay.IsValid());
    
    auto validation = processor.ValidateUnderlayGeometry(underlay);
    EXPECT_TRUE(validation.hasValidPosition);
}

//=======================================================================================
// Table Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, TableProcessorBasicFunctionality) {
    DWGTableProcessor processor(m_mockExporter.get());
    
    // Test table geometry
    TableGeometry table;
    table.insertionPoint = Point3d(0, 0, 0);
    table.numRows = 3;
    table.numColumns = 2;
    table.rowHeights = {10.0, 8.0, 8.0};
    table.columnWidths = {50.0, 100.0};
    
    // Initialize cells
    table.cells.resize(table.numRows);
    for (int i = 0; i < table.numRows; ++i) {
        table.cells[i].resize(table.numColumns);
        for (int j = 0; j < table.numColumns; ++j) {
            table.cells[i][j].text = "Cell(" + std::to_string(i) + "," + std::to_string(j) + ")";
            table.cells[i][j].type = TableCell::Type::Text;
        }
    }
    
    EXPECT_TRUE(table.IsValid());
    EXPECT_TRUE(table.HasValidDimensions());
    EXPECT_NEAR(table.CalculateTableWidth(), 150.0, 1e-6);
    EXPECT_NEAR(table.CalculateTableHeight(), 26.0, 1e-6);
    
    auto validation = processor.ValidateTableGeometry(table);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidDimensions);
    EXPECT_TRUE(validation.hasValidCells);
}

TEST_F(DWGAllEntitiesTest, FieldGeometryValidation) {
    DWGTableProcessor processor(m_mockExporter.get());
    
    FieldGeometry field;
    field.type = FieldGeometry::Type::Date;
    field.fieldCode = "%<\\AcVar Date \\f \"M/d/yyyy\">%";
    field.formatString = "M/d/yyyy";
    field.isAutoUpdate = true;
    
    EXPECT_TRUE(field.IsValid());
    EXPECT_TRUE(field.NeedsEvaluation());
    
    auto validation = processor.ValidateFieldGeometry(field);
    EXPECT_TRUE(validation.hasValidCode);
    EXPECT_TRUE(validation.hasValidFormat);
}

//=======================================================================================
// Multiline Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, MlineProcessorBasicFunctionality) {
    DWGMlineProcessor processor(m_mockExporter.get());
    
    // Test multiline style
    MlineStyle style;
    style.name = "TestStyle";
    style.description = "Test multiline style";
    style.fillOn = false;
    style.showMiters = true;
    
    // Add elements
    MlineElement element1;
    element1.offset = -5.0;
    element1.color = Color(1.0f, 0.0f, 0.0f, 1.0f);
    element1.lineType = "Continuous";
    
    MlineElement element2;
    element2.offset = 5.0;
    element2.color = Color(0.0f, 1.0f, 0.0f, 1.0f);
    element2.lineType = "Continuous";
    
    style.elements = {element1, element2};
    
    EXPECT_TRUE(style.IsValid());
    EXPECT_EQ(style.GetElementCount(), 2);
    EXPECT_NEAR(style.GetTotalWidth(), 10.0, 1e-6);
    
    auto validation = processor.ValidateMlineStyle(style);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidName);
    EXPECT_TRUE(validation.hasValidElements);
}

TEST_F(DWGAllEntitiesTest, MlineGeometryValidation) {
    DWGMlineProcessor processor(m_mockExporter.get());
    
    MlineGeometry mline;
    mline.styleName = "Standard";
    mline.scale = 1.0;
    mline.justification = MlineGeometry::Justification::Zero;
    mline.normal = Vector3d(0, 0, 1);
    
    // Add vertices
    MlineVertex vertex1;
    vertex1.position = Point3d(0, 0, 0);
    vertex1.direction = Vector3d(1, 0, 0);
    vertex1.miterDirection = Vector3d(1, 0, 0);
    
    MlineVertex vertex2;
    vertex2.position = Point3d(100, 0, 0);
    vertex2.direction = Vector3d(1, 0, 0);
    vertex2.miterDirection = Vector3d(1, 0, 0);
    
    MlineVertex vertex3;
    vertex3.position = Point3d(100, 100, 0);
    vertex3.direction = Vector3d(0, 1, 0);
    vertex3.miterDirection = Vector3d(0, 1, 0);
    
    mline.vertices = {vertex1, vertex2, vertex3};
    
    EXPECT_TRUE(mline.IsValid());
    EXPECT_EQ(mline.GetVertexCount(), 3);
    EXPECT_NEAR(mline.CalculateLength(), 200.0, 1e-6);
    
    auto validation = processor.ValidateMlineGeometry(mline);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidVertices);
    EXPECT_TRUE(validation.hasValidStyle);
}

//=======================================================================================
// Viewport Processor Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, ViewportProcessorBasicFunctionality) {
    DWGViewportProcessor processor(m_mockExporter.get());
    
    // Test viewport geometry
    ViewportGeometry viewport;
    viewport.center = Point3d(100, 100, 0);
    viewport.width = 200.0;
    viewport.height = 150.0;
    viewport.viewCenter = Point3d(0, 0, 0);
    viewport.viewTarget = Point3d(0, 0, 0);
    viewport.viewDirection = Vector3d(0, 0, 1);
    viewport.upVector = Vector3d(0, 1, 0);
    viewport.viewHeight = 300.0;
    viewport.viewWidth = 400.0;
    viewport.scale = 1.0;
    
    EXPECT_TRUE(viewport.IsValid());
    EXPECT_NEAR(viewport.GetAspectRatio(), 200.0/150.0, 1e-6);
    
    auto validation = processor.ValidateViewportGeometry(viewport);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidBoundary);
    EXPECT_TRUE(validation.hasValidView);
    EXPECT_TRUE(validation.hasValidScale);
}

TEST_F(DWGAllEntitiesTest, LayoutGeometryValidation) {
    DWGViewportProcessor processor(m_mockExporter.get());
    
    LayoutGeometry layout;
    layout.name = "TestLayout";
    layout.paperWidth = 297.0;  // A4
    layout.paperHeight = 210.0;
    layout.paperUnits = "mm";
    layout.plotType = LayoutGeometry::PlotType::Layout;
    layout.plotScale = 1.0;
    layout.plotCentered = true;
    
    EXPECT_TRUE(layout.IsValid());
    EXPECT_NEAR(layout.GetPaperAspectRatio(), 297.0/210.0, 1e-6);
    
    auto validation = processor.ValidateLayoutGeometry(layout);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidName);
    EXPECT_TRUE(validation.hasValidPaper);
}

//=======================================================================================
// Integration Tests
//=======================================================================================

TEST_F(DWGAllEntitiesTest, ProcessorIntegrationTest) {
    // Test that all processors can be created and work together
    auto allProcessors = DWGEntityProcessorFactory::CreateAllProcessors(m_mockExporter.get());
    
    // Verify we have a reasonable number of processors
    EXPECT_GE(allProcessors.size(), 10);
    
    // Test that each processor can handle basic operations
    for (const auto& pair : allProcessors) {
        const auto& processor = pair.second;
        EXPECT_FALSE(processor->GetProcessorName().empty());
        
        // Create a dummy element and test if processor can handle it
        ElementInfo element = CreateTestElement(ElementType::GeometricElement, "test_" + pair.first);
        
        // This should not crash
        bool canProcess = processor->CanProcessEntity(element);
        (void)canProcess; // Suppress unused variable warning
    }
}

TEST_F(DWGAllEntitiesTest, ProcessorCategoryTest) {
    auto categories = DWGEntityProcessorFactory::GetProcessorCategories();
    
    // Verify we have expected categories
    std::set<std::string> expectedCategories = {
        "Basic2D", "Text", "Advanced2D", "Fill", "Dimension", 
        "Block", "3D", "Layout", "Media", "Data"
    };
    
    for (const auto& category : expectedCategories) {
        EXPECT_TRUE(std::find(categories.begin(), categories.end(), category) != categories.end())
            << "Missing category: " << category;
        
        auto processorsInCategory = DWGEntityProcessorFactory::GetProcessorsInCategory(category);
        EXPECT_FALSE(processorsInCategory.empty()) << "No processors in category: " << category;
    }
}
