#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/ge/gecurve2d.h>
#include <realdwg/ge/geintrvl.h>
#endif

namespace IModelExport {

//=======================================================================================
// Hatch Pattern Data Structures
//=======================================================================================

struct HatchLine {
    double angle = 0.0;           // Line angle in radians
    Point3d basePoint;            // Base point for pattern
    Vector3d offset;              // Offset vector
    std::vector<double> dashes;   // Dash pattern (empty for solid)
    
    bool IsValid() const {
        return std::isfinite(angle) && 
               std::isfinite(basePoint.x) && std::isfinite(basePoint.y) && std::isfinite(basePoint.z) &&
               std::isfinite(offset.x) && std::isfinite(offset.y) && std::isfinite(offset.z);
    }
};

struct HatchPattern {
    std::string name = "SOLID";
    std::vector<HatchLine> lines;
    double scale = 1.0;
    double angle = 0.0;
    bool isCustom = false;
    
    // Predefined patterns
    static HatchPattern CreateSolid();
    static HatchPattern CreateAnsi31();
    static HatchPattern CreateAnsi32();
    static HatchPattern CreateAnsi33();
    static HatchPattern CreateAnsi34();
    static HatchPattern CreateAnsi35();
    static HatchPattern CreateAnsi36();
    static HatchPattern CreateAnsi37();
    static HatchPattern CreateAnsi38();
    
    bool IsValid() const;
    bool IsSolid() const { return name == "SOLID" || lines.empty(); }
};

struct HatchBoundary {
    enum class Type {
        Polyline,
        Circle,
        Ellipse,
        Spline,
        Region
    };
    
    Type type = Type::Polyline;
    std::vector<Point3d> vertices;
    bool isClosed = true;
    bool isOuter = true;  // true for outer boundary, false for island
    
    // Circle/Ellipse specific
    Point3d center;
    double radius = 0.0;
    Vector3d majorAxis;
    double radiusRatio = 1.0;
    
    // Spline specific
    int degree = 3;
    std::vector<double> knots;
    std::vector<double> weights;
    bool isRational = false;
    bool isPeriodic = false;
    
    bool IsValid() const;
    double CalculateArea() const;
    bool ContainsPoint(const Point3d& point) const;
};

struct HatchGeometry {
    std::vector<HatchBoundary> boundaries;
    HatchPattern pattern;
    Color fillColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    double elevation = 0.0;
    Vector3d normal = Vector3d(0, 0, 1);
    
    // Hatch properties
    bool isSolid = false;
    bool isGradient = false;
    bool isAssociative = false;
    double pixelSize = 1.0;
    
    // Gradient properties
    Color gradientColor1 = Color(1.0f, 1.0f, 1.0f, 1.0f);
    Color gradientColor2 = Color(0.0f, 0.0f, 0.0f, 1.0f);
    double gradientAngle = 0.0;
    bool gradientCentered = false;
    
    bool IsValid() const;
    bool HasValidBoundaries() const;
    double CalculateTotalArea() const;
    int GetBoundaryCount() const { return static_cast<int>(boundaries.size()); }
    int GetIslandCount() const;
};

//=======================================================================================
// Hatch Validation Results
//=======================================================================================

struct HatchValidationResult : public DWGValidationResult {
    bool hasValidBoundaries = false;
    bool hasValidPattern = false;
    bool hasValidColors = false;
    bool hasValidGradient = false;
    int invalidBoundaryCount = 0;
    int selfIntersectionCount = 0;
    double totalArea = 0.0;
    
    void AddHatchError(const std::string& error) {
        AddError("Hatch: " + error);
    }
    
    void AddHatchWarning(const std::string& warning) {
        AddWarning("Hatch: " + warning);
    }
};

//=======================================================================================
// DWG Hatch Processor
//=======================================================================================

class DWGHatchProcessor : public DWGEntityProcessor {
public:
    DWGHatchProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGHatchProcessor"; }

    // Hatch-specific processing methods
    DWGProcessingStatus ProcessHatch(const HatchGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessSolidFill(const HatchGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessGradientFill(const HatchGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPatternFill(const HatchGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessRegion(const HatchGeometry& geometry, const std::string& layer = "");

    // Validation methods
    HatchValidationResult ValidateHatchGeometry(const HatchGeometry& geometry) const;
    bool ValidateBoundaries(const std::vector<HatchBoundary>& boundaries) const;
    bool ValidatePattern(const HatchPattern& pattern) const;
    bool ValidateColors(const HatchGeometry& geometry) const;
    bool ValidateGradient(const HatchGeometry& geometry) const;

    // Boundary processing
    bool ProcessBoundary(const HatchBoundary& boundary) const;
    bool ValidateBoundaryGeometry(const HatchBoundary& boundary) const;
    bool IsBoundaryClockwise(const HatchBoundary& boundary) const;
    bool FixBoundaryOrientation(HatchBoundary& boundary) const;
    bool SimplifyBoundary(HatchBoundary& boundary, double tolerance = 1e-6) const;

    // Pattern processing (based on RealDwgFileIO pattern handling)
    bool ProcessHatchPattern(const HatchPattern& pattern) const;
    bool ValidatePatternLines(const std::vector<HatchLine>& lines) const;
    bool OptimizePattern(HatchPattern& pattern) const;
    HatchPattern GetPredefinedPattern(const std::string& name) const;

    // Boundary analysis and repair
    bool RepairHatchGeometry(HatchGeometry& geometry) const;
    bool DetectSelfIntersections(const HatchBoundary& boundary) const;
    bool RemoveSelfIntersections(HatchBoundary& boundary) const;
    bool ValidateBoundaryNesting(const std::vector<HatchBoundary>& boundaries) const;
    bool FixBoundaryNesting(std::vector<HatchBoundary>& boundaries) const;

    // Area calculations
    double CalculateBoundaryArea(const HatchBoundary& boundary) const;
    double CalculatePolygonArea(const std::vector<Point3d>& vertices) const;
    double CalculateCircleArea(double radius) const;
    double CalculateEllipseArea(const Vector3d& majorAxis, double radiusRatio) const;

    // Conversion methods
    bool ConvertToSolid(const HatchGeometry& complex, HatchGeometry& solid) const;
    bool ConvertToPolyline(const HatchBoundary& boundary, std::vector<Point3d>& vertices) const;
    bool ConvertToRegion(const HatchGeometry& hatch, std::vector<HatchBoundary>& regions) const;

private:
    // Boundary creation helpers
    bool CreatePolylineBoundary(const std::vector<Point3d>& vertices, HatchBoundary& boundary) const;
    bool CreateCircleBoundary(const Point3d& center, double radius, HatchBoundary& boundary) const;
    bool CreateEllipseBoundary(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, HatchBoundary& boundary) const;
    bool CreateSplineBoundary(const std::vector<Point3d>& controlPoints, int degree, HatchBoundary& boundary) const;
    
    // Pattern creation helpers
    bool CreateSolidPattern(HatchPattern& pattern) const;
    bool CreateLinePattern(double angle, double spacing, HatchPattern& pattern) const;
    bool CreateCrossHatchPattern(double angle1, double angle2, double spacing, HatchPattern& pattern) const;
    bool CreateCustomPattern(const std::vector<HatchLine>& lines, HatchPattern& pattern) const;
    
    // Geometric utilities
    bool IsPointInPolygon(const Point3d& point, const std::vector<Point3d>& polygon) const;
    bool DoLinesIntersect(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4) const;
    Point3d CalculatePolygonCentroid(const std::vector<Point3d>& vertices) const;
    bool IsPolygonClockwise(const std::vector<Point3d>& vertices) const;
    
    // Transformation and coordinate processing
    void TransformHatchGeometry(HatchGeometry& geometry) const;
    void ValidateAndFixBoundaries(std::vector<HatchBoundary>& boundaries) const;
    void NormalizeHatchData(HatchGeometry& geometry) const;
    
    // Optimization algorithms
    bool OptimizeBoundaries(std::vector<HatchBoundary>& boundaries) const;
    bool MergeBoundaries(std::vector<HatchBoundary>& boundaries, double tolerance = 1e-6) const;
    bool RemoveRedundantBoundaries(std::vector<HatchBoundary>& boundaries) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbHatch* CreateDWGHatch(const HatchGeometry& geometry) const;
    AcDbRegion* CreateDWGRegion(const HatchGeometry& geometry) const;
    AcDbSolid* CreateDWGSolid(const HatchGeometry& geometry) const;
    AcDbWipeout* CreateDWGWipeout(const HatchGeometry& geometry) const;
    
    // Boundary creation for RealDWG
    bool AddPolylineBoundary(AcDbHatch* hatch, const HatchBoundary& boundary) const;
    bool AddCircleBoundary(AcDbHatch* hatch, const HatchBoundary& boundary) const;
    bool AddEllipseBoundary(AcDbHatch* hatch, const HatchBoundary& boundary) const;
    bool AddSplineBoundary(AcDbHatch* hatch, const HatchBoundary& boundary) const;
    
    // Pattern setting for RealDWG
    bool SetHatchPattern(AcDbHatch* hatch, const HatchPattern& pattern) const;
    bool SetSolidFill(AcDbHatch* hatch, const Color& color) const;
    bool SetGradientFill(AcDbHatch* hatch, const HatchGeometry& geometry) const;
    bool SetPatternFill(AcDbHatch* hatch, const HatchPattern& pattern) const;
    
    // Property setting helpers
    bool SetHatchProperties(AcDbHatch* hatch, const HatchGeometry& geometry) const;
    bool SetHatchAssociativity(AcDbHatch* hatch, bool associative) const;
    bool SetHatchElevation(AcDbHatch* hatch, double elevation, const Vector3d& normal) const;
    
    // Error handling for RealDWG operations
    bool HandleHatchCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedHatches = 0;
    mutable size_t m_repairedHatches = 0;
    mutable size_t m_simplifiedBoundaries = 0;
    mutable size_t m_convertedToSolid = 0;
    mutable size_t m_processedPatterns = 0;
    
    // Configuration
    double m_boundaryTolerance = 1e-10;
    double m_areaTolerance = 1e-12;
    double m_patternTolerance = 1e-10;
    double m_simplificationTolerance = 1e-6;
    int m_maxBoundaries = 1000;
    int m_maxPatternLines = 100;
    bool m_enableAutoRepair = true;
    bool m_enableSimplification = true;
    bool m_enableOptimization = true;
    bool m_allowSelfIntersections = false;
};

//=======================================================================================
// Hatch Utility Functions
//=======================================================================================

class HatchUtils {
public:
    // Pattern utilities
    static HatchPattern CreateStandardPattern(const std::string& name, double scale = 1.0, double angle = 0.0);
    static bool IsStandardPattern(const std::string& name);
    static std::vector<std::string> GetStandardPatternNames();
    
    // Boundary utilities
    static bool IsValidBoundary(const HatchBoundary& boundary);
    static bool IsBoundaryInside(const HatchBoundary& inner, const HatchBoundary& outer);
    static bool DoBoundariesOverlap(const HatchBoundary& boundary1, const HatchBoundary& boundary2);
    
    // Area calculations
    static double CalculatePolygonArea(const std::vector<Point3d>& vertices);
    static double CalculateSignedArea(const std::vector<Point3d>& vertices);
    static bool IsClockwise(const std::vector<Point3d>& vertices);
    
    // Geometric operations
    static bool PointInPolygon(const Point3d& point, const std::vector<Point3d>& polygon);
    static bool LineIntersection(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4, Point3d& intersection);
    static double DistancePointToLine(const Point3d& point, const Point3d& lineStart, const Point3d& lineEnd);
    
    // Boundary operations
    static bool SimplifyBoundary(std::vector<Point3d>& vertices, double tolerance = 1e-6);
    static bool RemoveDuplicateVertices(std::vector<Point3d>& vertices, double tolerance = 1e-6);
    static bool FixBoundaryOrientation(std::vector<Point3d>& vertices, bool shouldBeClockwise = true);
    
    // Validation utilities
    static bool ValidateHatchData(const HatchGeometry& geometry);
    static bool ValidatePatternData(const HatchPattern& pattern);
    static bool ValidateBoundaryData(const HatchBoundary& boundary);
    
    // Conversion utilities
    static bool ConvertBoundaryToPolyline(const HatchBoundary& boundary, std::vector<Point3d>& vertices);
    static bool ConvertPolylineToBoundary(const std::vector<Point3d>& vertices, HatchBoundary& boundary);
    static bool ConvertCircleToBoundary(const Point3d& center, double radius, HatchBoundary& boundary, int segments = 32);
};

} // namespace IModelExport
