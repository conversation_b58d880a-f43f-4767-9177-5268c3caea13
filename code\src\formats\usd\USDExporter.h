#pragma once

#include "../../../include/IExportFormat.h"
#include "../../../include/ExportTypes.h"
#include "../../core/ExportContext.h"

// OpenUSD includes
#ifdef OPENUSD_AVAILABLE
#include <pxr/pxr.h>
#include <pxr/usd/usd/stage.h>
#include <pxr/usd/usd/prim.h>
#include <pxr/usd/usdGeom/mesh.h>
#include <pxr/usd/usdGeom/xform.h>
#include <pxr/usd/usdGeom/scope.h>
#include <pxr/usd/usdGeom/points.h>
#include <pxr/usd/usdGeom/curves.h>
#include <pxr/usd/usdShade/material.h>
#include <pxr/usd/usdShade/shader.h>
#include <pxr/base/gf/matrix4d.h>
#include <pxr/base/gf/vec3f.h>
#include <pxr/base/vt/array.h>
#endif

#include <memory>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class IModelDb;

//=======================================================================================
// USD Exporter Implementation using OpenUSD
//=======================================================================================

class USDExporter : public IUSDExporter {
public:
    USDExporter();
    virtual ~USDExporter();

    //===================================================================================
    // IExportFormat Interface
    //===================================================================================

    ExportFormat GetFormat() const override { return ExportFormat::USD; }
    std::string GetFormatName() const override { return "Universal Scene Description"; }
    std::string GetFileExtension() const override { return ".usd"; }
    std::vector<std::string> GetSupportedVersions() const override;

    bool SupportsGeometry() const override { return true; }
    bool SupportsMetadata() const override { return true; }
    bool SupportsMaterials() const override { return true; }
    bool SupportsTextures() const override { return true; }
    bool SupportsAnimations() const override { return true; }
    bool SupportsHierarchy() const override { return true; }

    ExportResult Export(
        const IModelDb& imodel,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) override;

    bool ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const override;
    bool CanExportElement(const ElementInfo& element) const override;

    void SetExportContext(std::shared_ptr<ExportContext> context) override;
    std::shared_ptr<ExportContext> GetExportContext() const override;

    //===================================================================================
    // IUSDExporter Interface
    //===================================================================================

    bool SetUSDFormat(USDExportOptions::USDFormat format) override;
    bool CreateStage(const std::string& stageName) override;
    bool CreateXform(const std::string& primPath, const Transform3d& transform) override;
    bool CreateMesh(const std::string& primPath, const std::vector<Point3d>& vertices, const std::vector<int>& indices) override;

    bool AddGeomMesh(const std::string& primPath, const std::vector<Point3d>& points, 
                   const std::vector<int>& faceVertexCounts, const std::vector<int>& faceVertexIndices) override;
    bool AddMaterial(const std::string& materialPath, const Material& material) override;
    bool AssignMaterial(const std::string& primPath, const std::string& materialPath) override;
    bool AddVariant(const std::string& variantSetName, const std::string& variantName, const std::string& primPath) override;

protected:
    bool InitializeExport(const ExportOptions& options) override;
    bool FinalizeExport() override;
    void CleanupExport() override;

private:
    //===================================================================================
    // OpenUSD Integration
    //===================================================================================

#ifdef OPENUSD_AVAILABLE
    // USD stage and prims
    pxr::UsdStageRefPtr m_stage;
    pxr::UsdPrim m_rootPrim;
    pxr::UsdPrim m_geometryScope;
    pxr::UsdPrim m_materialsScope;
    
    // Helper methods
    bool InitializeOpenUSD();
    void CleanupOpenUSD();
    bool CreateDefaultHierarchy();
    bool SetupStageMetadata();
    
    // Prim creation helpers
    pxr::UsdPrim CreatePrim(const std::string& primPath, const std::string& typeName);
    pxr::UsdGeomXform CreateXformPrim(const std::string& primPath);
    pxr::UsdGeomMesh CreateMeshPrim(const std::string& primPath);
    pxr::UsdGeomScope CreateScopePrim(const std::string& primPath);
    
    // Geometry conversion helpers
    pxr::GfMatrix4d ToGfMatrix4d(const Transform3d& transform);
    pxr::GfVec3f ToGfVec3f(const Point3d& point);
    pxr::GfVec3f ToGfVec3f(const Vector3d& vector);
    pxr::GfVec3f ToGfVec3f(const Color& color);
    pxr::VtArray<pxr::GfVec3f> ToVtVec3fArray(const std::vector<Point3d>& points);
    pxr::VtArray<int> ToVtIntArray(const std::vector<int>& indices);
    
    // Material creation helpers
    pxr::UsdShadeMaterial CreateUsdMaterial(const std::string& materialPath, const Material& material);
    pxr::UsdShadeShader CreatePreviewSurfaceShader(const pxr::UsdShadeMaterial& material, const Material& materialData);
    bool SetMaterialParameters(pxr::UsdShadeShader& shader, const Material& material);
    
    // Variant management
    bool CreateVariantSet(const std::string& primPath, const std::string& variantSetName);
    bool AddVariantToPrim(const std::string& primPath, const std::string& variantSetName, const std::string& variantName);
    
    // Animation support
    bool SetTimeSample(pxr::UsdAttribute& attribute, double time, const pxr::VtValue& value);
    bool CreateAnimationLayer(const std::string& layerName);
#endif

    //===================================================================================
    // Export State Management
    //===================================================================================

    struct ExportState {
        std::string outputPath;
        USDExportOptions::USDFormat format;
        std::string stageName;
        bool exportAnimations;
        bool exportVariants;
        double timeCodesPerSecond;
        bool initialized;
        bool finalized;
        
        // Statistics
        size_t primsCreated;
        size_t meshesCreated;
        size_t materialsCreated;
        size_t variantsCreated;
        
        // Error tracking
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };

    ExportState m_state;
    std::shared_ptr<ExportContext> m_context;
    
    // Prim and material caching
    std::unordered_map<std::string, std::string> m_primCache;     // iModel ID -> USD prim path
    std::unordered_map<std::string, std::string> m_materialCache; // material name -> USD material path
    std::unordered_map<std::string, std::string> m_textureCache;  // texture path -> USD texture path
    
    //===================================================================================
    // iModel Element Processing
    //===================================================================================

    // Main processing methods
    bool ProcessIModelElements(const IModelDb& imodel, ProgressCallback progressCallback);
    bool ProcessElement(const ElementInfo& element);
    
    // Element type processors
    bool ProcessGeometricElement(const ElementInfo& element);
    bool ProcessSpatialElement(const ElementInfo& element);
    bool ProcessPhysicalElement(const ElementInfo& element);
    bool ProcessGroupElement(const ElementInfo& element);
    
    // Geometry processors
    bool ProcessMeshGeometry(const ElementInfo& element);
    bool ProcessCurveGeometry(const ElementInfo& element);
    bool ProcessSurfaceGeometry(const ElementInfo& element);
    bool ProcessSolidGeometry(const ElementInfo& element);
    bool ProcessPointGeometry(const ElementInfo& element);
    
    // Hierarchy processors
    bool ProcessElementHierarchy(const ElementInfo& element);
    bool CreateElementXform(const ElementInfo& element);
    bool AssignElementToParent(const std::string& childPath, const std::string& parentPath);
    
    // Property processors
    bool ProcessElementProperties(const ElementInfo& element);
    bool ProcessMaterialProperties(const ElementInfo& element);
    bool ProcessDisplayProperties(const ElementInfo& element);
    bool ProcessCustomProperties(const ElementInfo& element);
    
    //===================================================================================
    // USD Hierarchy Management
    //===================================================================================

    // Hierarchy creation
    bool CreateSpatialHierarchy(const IModelDb& imodel);
    bool CreateModelHierarchy(const IModelDb& imodel);
    bool CreateCategoryHierarchy(const IModelDb& imodel);
    
    // Prim path management
    std::string GeneratePrimPath(const ElementInfo& element);
    std::string SanitizePrimName(const std::string& name);
    bool ValidatePrimPath(const std::string& primPath);
    std::string GetUniquePrimPath(const std::string& basePath);
    
    // Reference and payload management
    bool CreateReference(const std::string& primPath, const std::string& referencePath);
    bool CreatePayload(const std::string& primPath, const std::string& payloadPath);
    
    //===================================================================================
    // Coordinate System and Units
    //===================================================================================

    // Coordinate transformation
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    Transform3d TransformMatrix(const Transform3d& transform) const;
    
    // Units setup
    bool SetupUSDUnits();
    bool SetStageMetersPerUnit(double metersPerUnit);
    bool SetStageUpAxis(const std::string& upAxis);
    
    //===================================================================================
    // Material and Texture Management
    //===================================================================================

    // Material processing
    std::string ProcessMaterial(const Material& material);
    bool CreateMaterialBinding(const std::string& primPath, const std::string& materialPath);
    bool CreateMaterialVariant(const std::string& primPath, const std::string& variantName, const std::string& materialPath);
    
    // Texture processing
    std::string ProcessTexture(const std::string& texturePath);
    bool CreateTextureShader(const std::string& shaderPath, const std::string& texturePath);
    bool ConnectTextureToMaterial(const std::string& materialPath, const std::string& texturePath, const std::string& input);
    
    //===================================================================================
    // Animation Support
    //===================================================================================

    // Animation creation
    bool CreateAnimationData(const ElementInfo& element);
    bool SetTransformAnimation(const std::string& primPath, const std::vector<Transform3d>& transforms, const std::vector<double>& times);
    bool SetVisibilityAnimation(const std::string& primPath, const std::vector<bool>& visibility, const std::vector<double>& times);
    bool SetMaterialAnimation(const std::string& primPath, const std::vector<std::string>& materials, const std::vector<double>& times);
    
    //===================================================================================
    // Variant Management
    //===================================================================================

    // Variant creation
    bool CreateLODVariants(const std::string& primPath, const ElementInfo& element);
    bool CreateMaterialVariants(const std::string& primPath, const ElementInfo& element);
    bool CreateConfigurationVariants(const std::string& primPath, const ElementInfo& element);
    
    //===================================================================================
    // Error Handling and Logging
    //===================================================================================

    void LogError(const std::string& message);
    void LogWarning(const std::string& message);
    void LogInfo(const std::string& message);
    
    bool HandleError(const std::string& elementId, const std::string& error);
    bool ShouldContinueOnError() const;
    
    //===================================================================================
    // Progress Reporting
    //===================================================================================

    bool UpdateProgress(ProgressCallback callback, double percentage, const std::string& operation);
    bool CheckCancellation(ProgressCallback callback);
    
    //===================================================================================
    // Utility Methods
    //===================================================================================

    bool FileExists(const std::string& path) const;
    bool CreateDirectoryIfNeeded(const std::string& path) const;
    std::string GetTemporaryFileName() const;
    
    // USD validation
    bool ValidateUSDStage() const;
    bool ValidateUSDPrim(const std::string& primPath) const;
    
    // File format helpers
    std::string GetFileExtensionForFormat(USDExportOptions::USDFormat format) const;
    bool SaveStageToFile(const std::string& filePath, USDExportOptions::USDFormat format);
};

} // namespace IModelExport
