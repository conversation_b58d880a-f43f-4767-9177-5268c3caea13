#include "DWGTableProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbtablestyle.h>
#include <realdwg/base/dbcellstylemap.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <sstream>
#include <regex>
#include <chrono>
#include <iomanip>

namespace IModelExport {

//=======================================================================================
// TableCell Implementation
//=======================================================================================

bool TableCell::IsValid() const {
    // Validate text height
    if (!std::isfinite(textHeight) || textHeight <= 0.0) {
        return false;
    }
    
    // Validate rotation
    if (!std::isfinite(rotation)) {
        return false;
    }
    
    // Validate margins
    if (!std::isfinite(leftMargin) || !std::isfinite(rightMargin) || 
        !std::isfinite(topMargin) || !std::isfinite(bottomMargin) ||
        leftMargin < 0.0 || rightMargin < 0.0 || topMargin < 0.0 || bottomMargin < 0.0) {
        return false;
    }
    
    // Validate color
    if (!backgroundColor.IsValid() || !textColor.IsValid()) {
        return false;
    }
    
    return true;
}

std::string TableCell::GetDisplayText() const {
    if (type == Type::Field && !fieldCode.empty()) {
        // For fields, evaluate the field code
        return EvaluateFieldCode(fieldCode);
    }
    return text;
}

std::string TableCell::EvaluateFieldCode(const std::string& code) const {
    // Simple field evaluation - real implementation would be more complex
    if (code.find("DATE") != std::string::npos) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::ostringstream oss;
        oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d");
        return oss.str();
    } else if (code.find("TIME") != std::string::npos) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::ostringstream oss;
        oss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
        return oss.str();
    } else if (code.find("FILENAME") != std::string::npos) {
        return "drawing.dwg";
    }
    
    return code; // Return code as-is if not recognized
}

//=======================================================================================
// TableGeometry Implementation
//=======================================================================================

bool TableGeometry::IsValid() const {
    // Validate insertion point
    if (!std::isfinite(insertionPoint.x) || !std::isfinite(insertionPoint.y) || !std::isfinite(insertionPoint.z)) {
        return false;
    }
    
    // Validate direction vectors
    if (!std::isfinite(direction.x) || !std::isfinite(direction.y) || !std::isfinite(direction.z) ||
        !std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    // Validate dimensions
    if (numRows <= 0 || numColumns <= 0) {
        return false;
    }
    
    // Validate row heights and column widths
    if (rowHeights.size() != static_cast<size_t>(numRows) || 
        columnWidths.size() != static_cast<size_t>(numColumns)) {
        return false;
    }
    
    for (double height : rowHeights) {
        if (!std::isfinite(height) || height <= 0.0) {
            return false;
        }
    }
    
    for (double width : columnWidths) {
        if (!std::isfinite(width) || width <= 0.0) {
            return false;
        }
    }
    
    // Validate cells array
    if (cells.size() != static_cast<size_t>(numRows)) {
        return false;
    }
    
    for (const auto& row : cells) {
        if (row.size() != static_cast<size_t>(numColumns)) {
            return false;
        }
        
        for (const auto& cell : row) {
            if (!cell.IsValid()) {
                return false;
            }
        }
    }
    
    return true;
}

bool TableGeometry::HasValidDimensions() const {
    return numRows > 0 && numColumns > 0 && 
           rowHeights.size() == static_cast<size_t>(numRows) &&
           columnWidths.size() == static_cast<size_t>(numColumns);
}

double TableGeometry::CalculateTableWidth() const {
    return std::accumulate(columnWidths.begin(), columnWidths.end(), 0.0);
}

double TableGeometry::CalculateTableHeight() const {
    return std::accumulate(rowHeights.begin(), rowHeights.end(), 0.0);
}

BoundingBox3D TableGeometry::CalculateBounds() const {
    BoundingBox3D bounds;
    
    double totalWidth = CalculateTableWidth();
    double totalHeight = CalculateTableHeight();
    
    // Calculate table corners based on direction and normal vectors
    Vector3d rightVector = direction;
    Vector3d upVector = Vector3d(
        normal.y * direction.z - normal.z * direction.y,
        normal.z * direction.x - normal.x * direction.z,
        normal.x * direction.y - normal.y * direction.x
    );
    
    // Normalize vectors
    double rightLength = std::sqrt(rightVector.x * rightVector.x + rightVector.y * rightVector.y + rightVector.z * rightVector.z);
    double upLength = std::sqrt(upVector.x * upVector.x + upVector.y * upVector.y + upVector.z * upVector.z);
    
    if (rightLength > 1e-10) {
        rightVector.x /= rightLength;
        rightVector.y /= rightLength;
        rightVector.z /= rightLength;
    }
    
    if (upLength > 1e-10) {
        upVector.x /= upLength;
        upVector.y /= upLength;
        upVector.z /= upLength;
    }
    
    // Calculate four corners
    Point3d corner1 = insertionPoint;
    Point3d corner2(insertionPoint.x + totalWidth * rightVector.x, 
                   insertionPoint.y + totalWidth * rightVector.y, 
                   insertionPoint.z + totalWidth * rightVector.z);
    Point3d corner3(insertionPoint.x + totalWidth * rightVector.x + totalHeight * upVector.x,
                   insertionPoint.y + totalWidth * rightVector.y + totalHeight * upVector.y,
                   insertionPoint.z + totalWidth * rightVector.z + totalHeight * upVector.z);
    Point3d corner4(insertionPoint.x + totalHeight * upVector.x,
                   insertionPoint.y + totalHeight * upVector.y,
                   insertionPoint.z + totalHeight * upVector.z);
    
    bounds.AddPoint(corner1);
    bounds.AddPoint(corner2);
    bounds.AddPoint(corner3);
    bounds.AddPoint(corner4);
    
    return bounds;
}

TableCell& TableGeometry::GetCell(int row, int column) {
    static TableCell emptyCell; // Return reference to empty cell if out of bounds
    
    if (row >= 0 && row < numRows && column >= 0 && column < numColumns) {
        return cells[row][column];
    }
    
    return emptyCell;
}

const TableCell& TableGeometry::GetCell(int row, int column) const {
    static const TableCell emptyCell; // Return reference to empty cell if out of bounds
    
    if (row >= 0 && row < numRows && column >= 0 && column < numColumns) {
        return cells[row][column];
    }
    
    return emptyCell;
}

//=======================================================================================
// FieldGeometry Implementation
//=======================================================================================

bool FieldGeometry::IsValid() const {
    // Validate field code
    if (fieldCode.empty()) {
        return false;
    }
    
    // Validate position
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z)) {
        return false;
    }
    
    // Validate text properties
    if (!std::isfinite(textHeight) || !std::isfinite(rotation) || textHeight <= 0.0) {
        return false;
    }
    
    return true;
}

bool FieldGeometry::NeedsEvaluation() const {
    return isAutoUpdate || evaluatedValue.empty();
}

std::string FieldGeometry::EvaluateField() const {
    // Simple field evaluation based on type
    switch (type) {
        case Type::Date: {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::ostringstream oss;
            
            if (formatString.find("yyyy") != std::string::npos) {
                oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d");
            } else {
                oss << std::put_time(std::localtime(&time_t), "%m/%d/%Y");
            }
            return oss.str();
        }
        
        case Type::Time: {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::ostringstream oss;
            oss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
            return oss.str();
        }
        
        case Type::FileName:
            return "drawing.dwg";
            
        case Type::Formula:
            return EvaluateFormula(fieldCode);
            
        case Type::Custom:
            return customValue.empty() ? fieldCode : customValue;
            
        default:
            return fieldCode;
    }
}

std::string FieldGeometry::EvaluateFormula(const std::string& formula) const {
    // Simple formula evaluation - real implementation would be much more complex
    // This is just a demonstration
    
    if (formula.find("SUM") != std::string::npos) {
        return "100.00"; // Dummy sum result
    } else if (formula.find("COUNT") != std::string::npos) {
        return "5"; // Dummy count result
    } else if (formula.find("AVERAGE") != std::string::npos) {
        return "20.00"; // Dummy average result
    }
    
    return formula; // Return formula as-is if not recognized
}

//=======================================================================================
// DWGTableProcessor Implementation
//=======================================================================================

DWGTableProcessor::DWGTableProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_tableTolerance(1e-6)
    , m_fieldTolerance(1e-3)
    , m_enableTableValidation(true)
    , m_enableFieldValidation(true)
    , m_autoRepairTables(true)
    , m_maxTableRows(1000)
    , m_maxTableColumns(100)
    , m_maxCellTextLength(1000)
{
}

DWGProcessingStatus DWGTableProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract table geometry from element
        // For demonstration, we'll create a sample table
        
        if (element.type == ElementType::AnnotationElement) {
            TableGeometry table;
            table.insertionPoint = Point3d(0, 0, 0);
            table.direction = Vector3d(1, 0, 0);
            table.normal = Vector3d(0, 0, 1);
            table.numRows = 3;
            table.numColumns = 2;
            table.rowHeights = {10.0, 8.0, 8.0};
            table.columnWidths = {50.0, 100.0};
            
            // Initialize cells
            table.cells.resize(table.numRows);
            for (int i = 0; i < table.numRows; ++i) {
                table.cells[i].resize(table.numColumns);
                for (int j = 0; j < table.numColumns; ++j) {
                    table.cells[i][j].text = "Cell(" + std::to_string(i) + "," + std::to_string(j) + ")";
                    table.cells[i][j].type = TableCell::Type::Text;
                    table.cells[i][j].textHeight = 2.5;
                    table.cells[i][j].alignment = TableCell::Alignment::MiddleCenter;
                }
            }
            
            return ProcessTable(table, "Tables");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing table entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGTableProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::AnnotationElement; // Simplified check
}

DWGProcessingStatus DWGTableProcessor::ProcessTable(const TableGeometry& geometry, const std::string& layer) {
    // Validate table geometry
    auto validation = ValidateTableGeometry(geometry);
    if (!validation.isValid) {
        LogError("Table geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    TableGeometry transformedGeometry = geometry;
    TransformTableGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbTable* table = CreateDWGTable(transformedGeometry);
        if (!table) {
            LogError("Failed to create DWG table entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetTableProperties(table, transformedGeometry)) {
            delete table;
            LogError("Failed to set table properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(table, layer)) {
            delete table;
            LogError("Failed to set table entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(table)) {
            delete table;
            LogError("Failed to add table to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedTables++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG table: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - table creation skipped");
    m_processedTables++;
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGTableProcessor::ProcessField(const FieldGeometry& geometry, const std::string& layer) {
    // Validate field geometry
    auto validation = ValidateFieldGeometry(geometry);
    if (!validation.isValid) {
        LogError("Field geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    FieldGeometry transformedGeometry = geometry;
    transformedGeometry.position = TransformPoint(transformedGeometry.position);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbField* field = CreateDWGField(transformedGeometry);
        if (!field) {
            LogError("Failed to create DWG field entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetFieldProperties(field, transformedGeometry)) {
            delete field;
            LogError("Failed to set field properties");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedFields++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG field: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - field creation skipped");
    m_processedFields++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

TableValidationResult DWGTableProcessor::ValidateTableGeometry(const TableGeometry& geometry) const {
    TableValidationResult result;
    result.isValid = true;
    
    // Validate dimensions
    result.hasValidDimensions = ValidateTableDimensions(geometry);
    if (!result.hasValidDimensions) {
        result.AddTableError("Invalid table dimensions");
    }
    
    // Validate cells
    result.hasValidCells = ValidateTableCells(geometry);
    if (!result.hasValidCells) {
        result.AddTableError("Invalid table cells");
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(geometry.insertionPoint.x) && 
                             std::isfinite(geometry.insertionPoint.y) && 
                             std::isfinite(geometry.insertionPoint.z);
    if (!result.hasValidPosition) {
        result.AddTableError("Invalid insertion point");
    }
    
    // Calculate metrics
    result.rowCount = geometry.numRows;
    result.columnCount = geometry.numColumns;
    result.totalWidth = geometry.CalculateTableWidth();
    result.totalHeight = geometry.CalculateTableHeight();
    
    // Check limits
    if (result.rowCount > static_cast<int>(m_maxTableRows)) {
        result.AddTableError("Too many rows (limit: " + std::to_string(m_maxTableRows) + ")");
        result.isValid = false;
    }
    
    if (result.columnCount > static_cast<int>(m_maxTableColumns)) {
        result.AddTableError("Too many columns (limit: " + std::to_string(m_maxTableColumns) + ")");
        result.isValid = false;
    }
    
    return result;
}

FieldValidationResult DWGTableProcessor::ValidateFieldGeometry(const FieldGeometry& geometry) const {
    FieldValidationResult result;
    result.isValid = true;
    
    // Validate field code
    result.hasValidCode = ValidateFieldCode(geometry.fieldCode);
    if (!result.hasValidCode) {
        result.AddFieldError("Invalid field code: " + geometry.fieldCode);
    }
    
    // Validate format
    result.hasValidFormat = ValidateFieldFormat(geometry.formatString);
    if (!result.hasValidFormat) {
        result.AddFieldWarning("Invalid format string: " + geometry.formatString);
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(geometry.position.x) && 
                             std::isfinite(geometry.position.y) && 
                             std::isfinite(geometry.position.z);
    if (!result.hasValidPosition) {
        result.AddFieldError("Invalid position");
    }
    
    // Validate text properties
    result.hasValidTextProperties = std::isfinite(geometry.textHeight) && geometry.textHeight > 0.0 &&
                                   std::isfinite(geometry.rotation);
    if (!result.hasValidTextProperties) {
        result.AddFieldError("Invalid text properties");
    }
    
    return result;
}

bool DWGTableProcessor::ValidateTableDimensions(const TableGeometry& geometry) const {
    // Check basic dimensions
    if (geometry.numRows <= 0 || geometry.numColumns <= 0) {
        return false;
    }
    
    // Check array sizes
    if (geometry.rowHeights.size() != static_cast<size_t>(geometry.numRows) ||
        geometry.columnWidths.size() != static_cast<size_t>(geometry.numColumns)) {
        return false;
    }
    
    // Check individual dimensions
    for (double height : geometry.rowHeights) {
        if (!std::isfinite(height) || height <= 0.0) {
            return false;
        }
    }
    
    for (double width : geometry.columnWidths) {
        if (!std::isfinite(width) || width <= 0.0) {
            return false;
        }
    }
    
    return true;
}

bool DWGTableProcessor::ValidateTableCells(const TableGeometry& geometry) const {
    // Check cells array structure
    if (geometry.cells.size() != static_cast<size_t>(geometry.numRows)) {
        return false;
    }
    
    for (const auto& row : geometry.cells) {
        if (row.size() != static_cast<size_t>(geometry.numColumns)) {
            return false;
        }
        
        for (const auto& cell : row) {
            if (!ValidateTableCell(cell)) {
                return false;
            }
        }
    }
    
    return true;
}

bool DWGTableProcessor::ValidateTableCell(const TableCell& cell) const {
    if (!cell.IsValid()) {
        return false;
    }
    
    // Check text length
    if (cell.text.length() > m_maxCellTextLength) {
        return false;
    }
    
    return true;
}

bool DWGTableProcessor::ValidateFieldCode(const std::string& code) const {
    if (code.empty()) {
        return false;
    }
    
    // Basic field code validation
    // Real implementation would parse and validate field syntax
    return true;
}

bool DWGTableProcessor::ValidateFieldFormat(const std::string& format) const {
    // Allow empty format strings
    if (format.empty()) {
        return true;
    }
    
    // Basic format validation
    // Real implementation would validate format syntax
    return true;
}

//=======================================================================================
// Helper Methods
//=======================================================================================

void DWGTableProcessor::TransformTableGeometry(TableGeometry& geometry) const {
    geometry.insertionPoint = TransformPoint(geometry.insertionPoint);
    geometry.direction = TransformVector(geometry.direction);
    geometry.normal = TransformVector(geometry.normal);
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbTable* DWGTableProcessor::CreateDWGTable(const TableGeometry& geometry) const {
    try {
        AcDbTable* table = new AcDbTable();
        
        // Set table size
        table->setSize(geometry.numRows, geometry.numColumns);
        
        // Set position and direction
        AcGePoint3d position(geometry.insertionPoint.x, geometry.insertionPoint.y, geometry.insertionPoint.z);
        AcGeVector3d direction(geometry.direction.x, geometry.direction.y, geometry.direction.z);
        table->setPosition(position);
        table->setDirection(direction);
        
        // Set row heights and column widths
        for (int i = 0; i < geometry.numRows; ++i) {
            table->setRowHeight(i, geometry.rowHeights[i]);
        }
        
        for (int j = 0; j < geometry.numColumns; ++j) {
            table->setColumnWidth(j, geometry.columnWidths[j]);
        }
        
        return table;
    }
    catch (...) {
        return nullptr;
    }
}

AcDbField* DWGTableProcessor::CreateDWGField(const FieldGeometry& geometry) const {
    try {
        AcDbField* field = new AcDbField();
        
        // Set field code
        field->setFieldCode(geometry.fieldCode.c_str());
        
        // Set evaluation option
        if (geometry.isAutoUpdate) {
            field->setEvaluationOption(AcDbField::kAutomatic);
        } else {
            field->setEvaluationOption(AcDbField::kOnDemand);
        }
        
        return field;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGTableProcessor::SetTableProperties(AcDbTable* table, const TableGeometry& geometry) const {
    if (!table) {
        return false;
    }
    
    try {
        // Set cell contents
        for (int i = 0; i < geometry.numRows; ++i) {
            for (int j = 0; j < geometry.numColumns; ++j) {
                const TableCell& cell = geometry.cells[i][j];
                
                // Set cell text
                table->setTextString(i, j, cell.GetDisplayText().c_str());
                
                // Set cell text height
                table->setTextHeight(i, j, cell.textHeight);
                
                // Set cell alignment
                AcDb::CellAlignment alignment = AcDb::kMiddleCenter;
                switch (cell.alignment) {
                    case TableCell::Alignment::TopLeft: alignment = AcDb::kTopLeft; break;
                    case TableCell::Alignment::TopCenter: alignment = AcDb::kTopCenter; break;
                    case TableCell::Alignment::TopRight: alignment = AcDb::kTopRight; break;
                    case TableCell::Alignment::MiddleLeft: alignment = AcDb::kMiddleLeft; break;
                    case TableCell::Alignment::MiddleCenter: alignment = AcDb::kMiddleCenter; break;
                    case TableCell::Alignment::MiddleRight: alignment = AcDb::kMiddleRight; break;
                    case TableCell::Alignment::BottomLeft: alignment = AcDb::kBottomLeft; break;
                    case TableCell::Alignment::BottomCenter: alignment = AcDb::kBottomCenter; break;
                    case TableCell::Alignment::BottomRight: alignment = AcDb::kBottomRight; break;
                }
                table->setAlignment(i, j, alignment);
            }
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGTableProcessor::SetFieldProperties(AcDbField* field, const FieldGeometry& geometry) const {
    if (!field) {
        return false;
    }
    
    try {
        // Set format string if provided
        if (!geometry.formatString.empty()) {
            field->setFormat(geometry.formatString.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGTableProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGTableProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
