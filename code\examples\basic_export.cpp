#include "../include/IModelExportManager.h"
#include "../include/ExportTypes.h"

#include <iostream>
#include <filesystem>
#include <chrono>

using namespace IModelExport;

//=======================================================================================
// Progress Callback Example
//=======================================================================================

bool ProgressCallback(const ExportProgress& progress) {
    static auto lastUpdate = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    
    // Update progress every 100ms to avoid too frequent updates
    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() > 100) {
        std::cout << "\rProgress: " << std::fixed << std::setprecision(1) 
                  << progress.percentage << "% - " << progress.currentOperation 
                  << " (" << progress.processedElements << "/" << progress.totalElements << ")";
        std::cout.flush();
        lastUpdate = now;
    }
    
    // Return false to cancel export
    return true;
}

//=======================================================================================
// Basic Export Example
//=======================================================================================

void BasicExportExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "=== Basic Export Example ===" << std::endl;
    
    try {
        // Create export manager
        auto exportManager = IModelExportManager::Create();
        
        // Load iModel (placeholder - actual implementation would load from iModelNative)
        // IModelDb imodel = LoadIModel(imodelPath);
        
        // For demonstration, we'll create a mock iModel
        std::cout << "Loading iModel: " << imodelPath << std::endl;
        
        // Create output directory
        std::filesystem::create_directories(outputDir);
        
        // Export to DWG
        {
            std::cout << "\nExporting to DWG..." << std::endl;
            
            DWGExportOptions dwgOptions;
            dwgOptions.outputPath = outputDir + "/model.dwg";
            dwgOptions.version = DWGExportOptions::DWGVersion::R2021;
            dwgOptions.levelOfDetail = ExportLOD::High;
            dwgOptions.includeMetadata = true;
            dwgOptions.exportAsBlocks = true;
            dwgOptions.preserveLayers = true;
            
            // Note: In real implementation, pass actual iModel
            // auto result = exportManager->ExportToDWG(imodel, dwgOptions, ProgressCallback);
            
            // Mock result for demonstration
            ExportResult result;
            result.status = ExportStatus::Success;
            result.outputFile = dwgOptions.outputPath;
            result.exportedElements = 1250;
            result.exportTime = 15.3;
            
            if (result.status == ExportStatus::Success) {
                std::cout << "\nDWG export completed successfully!" << std::endl;
                std::cout << "Output file: " << result.outputFile << std::endl;
                std::cout << "Exported elements: " << result.exportedElements << std::endl;
                std::cout << "Export time: " << result.exportTime << " seconds" << std::endl;
            } else {
                std::cout << "\nDWG export failed: " << ToString(result.status) << std::endl;
                for (const auto& error : result.errors) {
                    std::cout << "Error: " << error << std::endl;
                }
            }
        }
        
        // Export to IFC
        {
            std::cout << "\nExporting to IFC..." << std::endl;
            
            IFCExportOptions ifcOptions;
            ifcOptions.outputPath = outputDir + "/model.ifc";
            ifcOptions.version = IFCExportOptions::IFCVersion::IFC4;
            ifcOptions.fileFormat = IFCExportOptions::IFCFileFormat::STEP;
            ifcOptions.levelOfDetail = ExportLOD::High;
            ifcOptions.includeMetadata = true;
            ifcOptions.exportSpaces = true;
            ifcOptions.exportSystems = true;
            ifcOptions.projectInfo = "Sample Building Project";
            
            // Mock result for demonstration
            ExportResult result;
            result.status = ExportStatus::Success;
            result.outputFile = ifcOptions.outputPath;
            result.exportedElements = 980;
            result.exportTime = 22.1;
            
            if (result.status == ExportStatus::Success) {
                std::cout << "\nIFC export completed successfully!" << std::endl;
                std::cout << "Output file: " << result.outputFile << std::endl;
                std::cout << "Exported elements: " << result.exportedElements << std::endl;
                std::cout << "Export time: " << result.exportTime << " seconds" << std::endl;
            }
        }
        
        // Export to USD
        {
            std::cout << "\nExporting to USD..." << std::endl;
            
            USDExportOptions usdOptions;
            usdOptions.outputPath = outputDir + "/model.usd";
            usdOptions.format = USDExportOptions::USDFormat::Binary;
            usdOptions.levelOfDetail = ExportLOD::High;
            usdOptions.includeMetadata = true;
            usdOptions.includeMaterials = true;
            usdOptions.includeTextures = true;
            usdOptions.exportVariants = true;
            
            // Mock result for demonstration
            ExportResult result;
            result.status = ExportStatus::Success;
            result.outputFile = usdOptions.outputPath;
            result.exportedElements = 1180;
            result.exportTime = 18.7;
            
            if (result.status == ExportStatus::Success) {
                std::cout << "\nUSD export completed successfully!" << std::endl;
                std::cout << "Output file: " << result.outputFile << std::endl;
                std::cout << "Exported elements: " << result.exportedElements << std::endl;
                std::cout << "Export time: " << result.exportTime << " seconds" << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "Export failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Batch Export Example
//=======================================================================================

void BatchExportExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Batch Export Example ===" << std::endl;
    
    try {
        auto exportManager = IModelExportManager::Create();
        
        // Create output directory
        std::filesystem::create_directories(outputDir);
        
        // Prepare batch export jobs
        std::vector<IModelExportManager::BatchExportJob> jobs;
        
        // DWG job
        {
            IModelExportManager::BatchExportJob job;
            job.format = ExportFormat::DWG;
            job.options = std::make_unique<DWGExportOptions>();
            auto* dwgOptions = static_cast<DWGExportOptions*>(job.options.get());
            dwgOptions->outputPath = outputDir + "/batch_model.dwg";
            dwgOptions->version = DWGExportOptions::DWGVersion::R2021;
            dwgOptions->levelOfDetail = ExportLOD::Medium;
            job.outputPath = dwgOptions->outputPath;
            jobs.push_back(std::move(job));
        }
        
        // IFC job
        {
            IModelExportManager::BatchExportJob job;
            job.format = ExportFormat::IFC;
            job.options = std::make_unique<IFCExportOptions>();
            auto* ifcOptions = static_cast<IFCExportOptions*>(job.options.get());
            ifcOptions->outputPath = outputDir + "/batch_model.ifc";
            ifcOptions->version = IFCExportOptions::IFCVersion::IFC4;
            ifcOptions->levelOfDetail = ExportLOD::Medium;
            job.outputPath = ifcOptions->outputPath;
            jobs.push_back(std::move(job));
        }
        
        // USD job
        {
            IModelExportManager::BatchExportJob job;
            job.format = ExportFormat::USD;
            job.options = std::make_unique<USDExportOptions>();
            auto* usdOptions = static_cast<USDExportOptions*>(job.options.get());
            usdOptions->outputPath = outputDir + "/batch_model.usd";
            usdOptions->format = USDExportOptions::USDFormat::Binary;
            usdOptions->levelOfDetail = ExportLOD::Medium;
            job.outputPath = usdOptions->outputPath;
            jobs.push_back(std::move(job));
        }
        
        std::cout << "Starting batch export of " << jobs.size() << " formats..." << std::endl;
        
        // Execute batch export
        // auto results = exportManager->ExportBatch(imodel, jobs, ProgressCallback);
        
        // Mock results for demonstration
        std::vector<ExportResult> results;
        for (const auto& job : jobs) {
            ExportResult result;
            result.status = ExportStatus::Success;
            result.outputFile = job.outputPath;
            result.exportedElements = 850;
            result.exportTime = 12.5;
            results.push_back(result);
        }
        
        // Report results
        std::cout << "\nBatch export completed!" << std::endl;
        for (size_t i = 0; i < results.size(); ++i) {
            const auto& result = results[i];
            std::cout << "Format " << ToString(jobs[i].format) << ": " 
                      << ToString(result.status) << " - " << result.outputFile << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Batch export failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Async Export Example
//=======================================================================================

void AsyncExportExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Async Export Example ===" << std::endl;
    
    try {
        auto exportManager = IModelExportManager::Create();
        
        // Create output directory
        std::filesystem::create_directories(outputDir);
        
        // Start multiple async exports
        std::vector<std::future<ExportResult>> futures;
        
        // DWG async export
        {
            DWGExportOptions dwgOptions;
            dwgOptions.outputPath = outputDir + "/async_model.dwg";
            dwgOptions.levelOfDetail = ExportLOD::Medium;
            
            // futures.push_back(exportManager->ExportToDWGAsync(imodel, dwgOptions, ProgressCallback));
        }
        
        // IFC async export
        {
            IFCExportOptions ifcOptions;
            ifcOptions.outputPath = outputDir + "/async_model.ifc";
            ifcOptions.levelOfDetail = ExportLOD::Medium;
            
            // futures.push_back(exportManager->ExportToIFCAsync(imodel, ifcOptions, ProgressCallback));
        }
        
        std::cout << "Started " << futures.size() << " async export operations..." << std::endl;
        std::cout << "Waiting for completion..." << std::endl;
        
        // Wait for all exports to complete
        for (size_t i = 0; i < futures.size(); ++i) {
            try {
                // auto result = futures[i].get();
                // std::cout << "Export " << (i + 1) << " completed: " << ToString(result.status) << std::endl;
                std::cout << "Export " << (i + 1) << " completed: Success (mock)" << std::endl;
            } catch (const std::exception& e) {
                std::cout << "Export " << (i + 1) << " failed: " << e.what() << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "Async export failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Main Function
//=======================================================================================

int main(int argc, char* argv[]) {
    std::string imodelPath = "sample.bim";
    std::string outputDir = "output";
    
    if (argc > 1) {
        imodelPath = argv[1];
    }
    if (argc > 2) {
        outputDir = argv[2];
    }
    
    std::cout << "iModel Export Framework Examples" << std::endl;
    std::cout << "================================" << std::endl;
    std::cout << "Input iModel: " << imodelPath << std::endl;
    std::cout << "Output directory: " << outputDir << std::endl;
    
    // Run examples
    BasicExportExample(imodelPath, outputDir + "/basic");
    BatchExportExample(imodelPath, outputDir + "/batch");
    AsyncExportExample(imodelPath, outputDir + "/async");
    
    std::cout << "\nAll examples completed!" << std::endl;
    return 0;
}
