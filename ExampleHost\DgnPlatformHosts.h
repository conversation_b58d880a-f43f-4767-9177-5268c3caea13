/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DgnPlatformHosts.h $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#include <PSolid/PSolidCore.h>
#include <RasterCore/RasterCoreLib.h>

#define BEGIN_HOSTINTERFACE_NAMESPACE   namespace HostInterface {
#define END_HOSTINTERFACE_NAMESPACE     }
#define USING_NAMESPACE_HOSTINTERFACE   using namespace HostInterface;

BEGIN_HOSTINTERFACE_NAMESPACE

/*=================================================================================**//**
* @bsiclass                                                     BentleySystems  
+===============+===============+===============+===============+===============+======*/
struct DgnConsoleAppNotificationAdmin : DgnPlatform::DgnPlatformLib::Host::NotificationAdmin
    {
    virtual StatusInt _OutputMessage (DgnPlatform::NotifyMessageDetails const&) override;
    virtual void _OutputPrompt (WCharCP) override;
    }; 

/*=================================================================================**//**
* @bsiclass                                                     BentleySystems  
+===============+===============+===============+===============+===============+======*/
struct DgnConsoleAppHost : DgnPlatform::DgnPlatformLib::Host
    {
    virtual NotificationAdmin& _SupplyNotificationAdmin () override {return *new DgnConsoleAppNotificationAdmin;}
    virtual SolidsKernelAdmin& _SupplySolidsKernelAdmin() override {return *new Bentley::DgnPlatform::PSolidKernelAdmin;}
    static void Initialize();
    }; 

END_HOSTINTERFACE_NAMESPACE

