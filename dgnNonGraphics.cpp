/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnNonGraphics.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
*   Base class to convert a none graphic element to an xRecord
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtNonGraphics : public ToDwgExtension
{
protected:
    mutable ElementId               m_elementId;
    mutable UInt16                  m_elementType;
    mutable UInt32                  m_attrOffset;
    mutable ConvertFromDgnContext*  m_fromDgnContext;


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // non-graphical elements will be saved to the MicroStation dictionary - don't bother with models that aren't saved:
    if (!this->ShouldSaveToDwg(context))
        return  RealDwgIgnoreElement;

    // get or create a new xrecord
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbXrecord::desc());
    AcDbXrecord*        acXrecord = AcDbXrecord::cast (acObject);
    if (NULL == acXrecord)
        return  NullObject;

    // build resbuf from binary element data
    MSElementCP         element = elemHandle.GetElementCP ();
    RealDwgResBuf*      rbChain = RealDwgResBuf::RbChainFromBinaryData (&element->hdr.dhdr, element->Size() - sizeof(Elm_hdr));

    // save resbuf to the xrecord
    acXrecord->setFromRbChain (*rbChain);

    RealDwgResBuf::Free (rbChain);

    // save element info for later process to set dictionary name etc
    m_elementId = element->ehdr.uniqueId;
    m_elementType = element->ehdr.type;
    m_attrOffset = element->ehdr.attrOffset;
    m_fromDgnContext = &context;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual bool    ShouldSaveToDwg (ConvertFromDgnContextR context) const
    {
    // all non-graphical elements in the dictionary model should be saved to DWG
    return  context.GetModel()->IsDictionaryModel();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual WString         GetXRecordNameForElement (ModelId modeId) const
    {
    // empty name in the default implementation
    return  WString();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddOrSetXAttributesToXrecord (AcDbXrecord* acXrecord, AcDbObjectId* inOwnerId, ElementHandleCR elemHandle) const
    {
    if (NULL == acXrecord)
        return  NotApplicable;
    
    /*------------------------------------------------------------------------------------------
    Before we add an extendion dictionary to an object, we have to make sure the xRecord is a
    database resident; otherwise the extension dictionary cannot be created.

    If the xRecord is already a database resident, i.e. we are editing an existing non-graphic
    element such as type 66 or DgnStore, update its name to ensure correct element size to be 
    saved in the name for better round trip purpose.
    -------------------------------------------------------------------------------------*/
    RealDwgStatus   status = RealDwgSuccess;

    if (acXrecord->objectId().isValid())
        status = this->UpdateDictionaryName(acXrecord, inOwnerId, elemHandle) ? RealDwgSuccess : CantOpenObject;
    else if (nullptr != inOwnerId && inOwnerId->isValid())
        status = this->AddToOwnerDictionary(acXrecord, *inOwnerId, elemHandle) ? RealDwgSuccess : CantOpenObject;
    else
        status = this->AddToMicroStationDictionary(acXrecord, elemHandle) ? RealDwgSuccess : CantCreateObject;

    // Now we can add xattributes. Forget about linkages for now.
    if (RealDwgSuccess == status)
        m_fromDgnContext->UpdateExtensionDictionaryFromXAttributes (acXrecord, elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool            UpdateDictionaryName (AcDbXrecord* xrecord, AcDbObjectId* inOwnerId, ElementHandleCR elemHandle) const
    {
    /*-----------------------------------------------------------------------------------
    Since we have element's attrOffset in dictionary name we have to make sure that the 
    name is upto date with any potential size change as a result of file editing.
    -----------------------------------------------------------------------------------*/
    AcDbObjectId        ownerId = (nullptr != inOwnerId && inOwnerId->isValid()) ? *inOwnerId : xrecord->ownerId ();
    if (ownerId.isValid())
        {
        AcDbDictionaryPointer msDictionary(ownerId, AcDb::kForWrite);
        if (Acad::eOk == msDictionary.openStatus())
            {
            AcString    oldName;
            if (Acad::eOk == msDictionary->nameAt(xrecord->objectId(), oldName))
                {
                // update only if names are different. Return true if they are the same, to allow caller proceed next steps - TFS 686249:
                WString newName = this->GetXRecordNameForElement (m_fromDgnContext->GetModelId());
                if (0 != oldName.compareNoCase(newName.c_str()))
                    return msDictionary->setName (oldName.kwszPtr(), newName.c_str());
                else
                    return  true;
                }
            }
        }
    
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            AddToOwnerDictionary (AcDbXrecord* acXrecord, AcDbObjectId& ownerId, ElementHandleCR elemHandle) const
    {
    // Add xRecord to the MicroStation dictionary.  The dictionary name comes from its implementation of GetXRecordNameForElement.
    AcDbDictionaryPointer   ownerDictionary(ownerId, AcDb::kForWrite);
    if (Acad::eOk == ownerDictionary.openStatus())
        {
        WString         entryName = this->GetXRecordNameForElement (m_fromDgnContext->GetModelId());
        AcDbObjectId    newId = m_fromDgnContext->AddObjectToDictionary (ownerDictionary, acXrecord, entryName.GetWCharCP(), elemHandle.GetElementId());
        return  newId.isValid();
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            AddToMicroStationDictionary (AcDbXrecord* acXrecord, ElementHandleCR elemHandle) const
    {
    // Add xRecord to the MicroStation dictionary.  The dictionary name comes from its implementation of GetXRecordNameForElement.
    AcDbObjectId    ownerId = m_fromDgnContext->GetMicroStationDictionaryId (true);
    return  this->AddToOwnerDictionary(acXrecord, ownerId, elemHandle);
    }

};  // ToDwgNonGraphics


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtType66 : public ToDwgExtNonGraphics
{
private:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Chris.Wu          05/21
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus SaveDgnGCSAsAcDbGeoData (AcDbDatabase *pDb, DgnGCSP mstnGCS) const
    {
    if (NULL == pDb || NULL == mstnGCS)
        return ERROR;

    const AcString csId(mstnGCS->GetName());

    /*Firstly, use gcs name to create the projected Coordinate System*/
    AcDbGeoCoordinateSystem* pGeoCS;
    AcDbGeoCoordinateSystem::create(csId, pGeoCS);

    /*If GCS name fails , try the wkt*/
    if (NULL == pGeoCS)
        {
        WString WKT;
        mstnGCS->GetWellKnownText(WKT, GeoCoordinates::BaseGCS::wktFlavorAutodesk, false);
        const AcString csWkt(WKT.c_str());
        AcDbGeoCoordinateSystem::create(csWkt, pGeoCS);
        }

    /*if wkt also fails, try the GCS name mapping file*/
    if (NULL == pGeoCS)
        {
        WString acGCSName;
        RealDwgUtil::ReadGCSNameFromMappingFile(acGCSName, mstnGCS->GetName(), false);
        if (!WString::IsNullOrEmpty(acGCSName.c_str()))
            AcDbGeoCoordinateSystem::create(AcString(acGCSName.c_str()), pGeoCS);
        }

    if (NULL == pGeoCS)
        return ERROR;

    AcString wktStr;
    pGeoCS->getWktRepresentation(wktStr);

    if (wktStr.isEmpty())
        return ERROR;

    AcDbObjectId geoId;
    acdbGetGeoDataObjId(pDb, geoId);

    /*Post our Geodata to Database,
    AcDbGeoData is not an Entity, it is stored in Model-Space's Extension Dictionary*/
    AcDbObjectPointer<AcDbGeoData> pGeoData;
    if (geoId.isNull())
        {
        pGeoData.create();
        pGeoData->setBlockTableRecordId(acdbSymUtil()->blockModelSpaceId(pDb));
        pGeoData->postToDb(geoId);
        }
    else
        {
        pGeoData.open(geoId, AcDb::kForWrite);
        }

    if (NULL == pGeoData.object())
        return ERROR;

    Acad::ErrorStatus es; //For debug

    UnitDefinition  unitDef;
    StandardUnit    dgnUnits;
    mstnGCS->GetUnitDefinition(unitDef, dgnUnits);

    AcDb::UnitsValue acdbUnit;
    acdbUnit = RealDwgUtil::AcDbUnitsValueFromDgnUnits(dgnUnits);

    if (AcDb::kUnitsUndefined != acdbUnit)
        {
        es = pGeoData->setHorizontalUnits(acdbUnit);
        es = pGeoData->setVerticalUnits(acdbUnit);
        }
    else
        {
        es = pGeoData->setHorizontalUnits(AcDb::kUnitsMeters);
        es = pGeoData->setVerticalUnits(AcDb::kUnitsMeters);
        }

    es = pGeoData->setCoordinateSystem(wktStr);
    es = pGeoData->setCoordinateType(AcDbGeoData::kCoordTypLocal); //kCoordTypGrid ? kCoordTypGeographic ?
    es = pGeoData->setDesignPoint(AcGePoint3d::kOrigin);
    es = pGeoData->setUpDirection(AcGeVector3d::kZAxis);


    double lon,lat, minLat, minLon, maxLat, maxLon;
    lon = mstnGCS->GetOriginLongitude();
    lat = mstnGCS->GetOriginLatitude();

    minLat = mstnGCS->GetMinimumUsefulLatitude();
    maxLat = mstnGCS->GetMaximumUsefulLatitude();
    minLon = mstnGCS->GetMinimumUsefulLongitude();
    maxLon = mstnGCS->GetMaximumUsefulLongitude();

    /*Note, if the origin is not in the useful area, set it to be the center point*/
    if (lon <= minLon || lon >= maxLon || lat <= minLat || lat >= maxLat)
        {
        lon = (minLon + maxLon) / 2.0;
        lat = (minLat + maxLat) / 2.0;
        }

    /*Note: AcGePoint3d expected to be Long,Lat,alt*/
    AcGePoint3d refPt, refPtInLatLong(lon, lat, 0.0);

    /*Get WCS point*/
    es = pGeoData->transformFromLonLatAlt(refPtInLatLong, refPt);
    es = pGeoData->setReferencePoint(refPt);
    es = pGeoData->setNorthDirectionVector(AcGeVector2d::kYAxis);

    /*clean up*/
    delete pGeoCS;
    pGeoData.close();

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Chris.Wu          05/21
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus DgnGCSToAcDbGeoData
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
ConvertFromDgnContextR  context             // The context
) const
    {
    AcDbDatabase*   database = context.GetFileHolder().GetDatabase();
    if (NULL == database)
        return ERROR;

    /*AcDbGeoData, available since 2009*/
    int  acadVersion = RealDwgUtil::MSVersionFromAcVersion(database->originalFileVersion());
    if (acadVersion <= DwgFileVersion_2010)
        return ERROR;
    
    DgnModelP   cache;
    if (NULL == (cache = elemHandle.GetDgnModelP()))
        return ERROR;

    MSElementCP element = elemHandle.GetElementCP();

    DgnGCSP   mstnGCS;
    if (NULL == (mstnGCS = Bentley::GeoCoordinates::DgnGCS::FromGeoCoordType66(&element->applicationElm, cache)))
        return ERROR;

    if (SUCCESS != SaveDgnGCSAsAcDbGeoData(database, mstnGCS))
        return ERROR;

    return SUCCESS;
    }

public:

virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    //Firstly, to check if we can save the DgnGCS as AcDbGeoData
    if (SUCCESS == DgnGCSToAcDbGeoData (elemHandle, context))
        return RealDwgSuccess;

    RealDwgStatus   status = __super::ToObject (elemHandle, acObject, existingObject, context);
    if (RealDwgSuccess != status)
        return  status;

    /*-----------------------------------------------------------------------------------
    Note that a type 66 may be a model or a dictionary element. If this is a model element, 
    SaveElementToDatabase only adds an entity to a block, not to a dictionary.

    Below method will ensure adding the new xRecord to MicroStation dictionary, regardless
    what model this type 66 is in.
    -----------------------------------------------------------------------------------*/
    return  __super::AddOrSetXAttributesToXrecord (AcDbXrecord::cast(acObject), nullptr, elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual bool    ShouldSaveToDwg (ConvertFromDgnContextR context) const override
    {
    // only save type-66 elements in dictionary, default and sheet models that are saved in the same file:
    if (__super::ShouldSaveToDwg(context))
        return  true;

    DgnModelP   currentModel = context.GetModel ();
    if (context.GetModelIdForDwgModelSpace() == currentModel->GetModelId() ||
        (DgnModelType::Sheet == currentModel->GetModelType() && !context.GetSettings().SaveSheetsToSeparateFiles()))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual WString         GetXRecordNameForElement (ModelId modelId) const override
    {
    /*-----------------------------------------------------------------------------------
    For a type 66 xRecord, two names are used:
        1) for a dictionary type 66: "ApplicationElement elementId attrOffset"
        2) for a model type 66: "ModelApplicationElement modelId elementId attrOffset"
    -----------------------------------------------------------------------------------*/
    WString     entryName;
    if (modelId < 0)
        entryName.Sprintf (L"%ls %I64d %d", StringConstants::UstnDictionaryItem_Application, m_elementId, m_attrOffset);
    else
        entryName.Sprintf (L"%ls %d %I64d %d", StringConstants::UstnDictionaryItem_ModelApplication, modelId, m_elementId, m_attrOffset);
    return  entryName;
    }

};  // ToDwgExtType66


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtType9 : public ToDwgExtNonGraphics
{
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    RealDwgStatus   status = __super::ToObject (elemHandle, acObject, existingObject, context);
    if (RealDwgSuccess != status)
        return  status;

    // add to database prior to adding an XAttributes xRecord as an extended dictionary:
    if (!acObject->objectId().isValid())
        status = AddToMicroStationDictionary(AcDbXrecord::cast(acObject), elemHandle) ? RealDwgSuccess : CantCreateObject;

    // add XAttributes
    if (RealDwgSuccess == status)
        m_fromDgnContext->UpdateExtensionDictionaryFromXAttributes (acObject, elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual WString         GetXRecordNameForElement (ModelId modeId) const override
    {
    // Header xRecord uses a fixed name "Header"
    return WString (StringConstants::UstnDictionaryItem_Header);
    }

};  // ToDwgExtType9


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtDgnStore : public ToDwgExtNonGraphics
{
protected:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    UInt32      storeId = 0, appId = 0;
    if (SUCCESS != DgnStoreHdrHandler::GetDgnStoreIds(&storeId, &appId, elemHandle))
        return  MstnElementUnacceptable;

    // DgnStore app's we care to save:
    if (XMLFRAGMENT_ID != storeId || !(LINKAGEID_ECXAttributes & appId))    // ECX schema
        return  RealDwgIgnoreElement;

    // set xRecord data from element
    RealDwgStatus   status =  __super::ToObject(elemHandle, acObject, existingObject, context);
    if (RealDwgSuccess != status)
        return  status;

    // add XAttributes to the xRecord
    status =  __super::AddOrSetXAttributesToXrecord (AcDbXrecord::cast(acObject), nullptr, elemHandle);
    if (RealDwgSuccess != status)
        return  status;

    // create and add child type-38's to the header type-39 xRecord dictionary
    if (elemHandle.GetElementCP()->IsComplexHeader() && elemHandle.GetElementCP()->GetComplexComponentCount() > 0)
        status = this->SaveComponentsToDwg (acObject, elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   SaveComponentsToDwg (AcDbObjectP parentObject, ElementHandleCR parentElement) const
    {
    if (nullptr == parentObject)
        return  NullObject;

    // create a new extension dictionary if not already exists on the parent xRecord
    AcDbObjectId    ownerId = parentObject->extensionDictionary ();
    if (!ownerId.isValid())
        {
        // save parent object prior to adding a new extended dictionary to it:
        if (!parentObject->objectId().isValid() && parentObject->isKindOf(AcDbEntity::desc()))
            m_fromDgnContext->AddEntityToCurrentBlock (AcDbEntity::cast(parentObject), parentElement.GetElementId());

        // create & add an extension dictionary to the parent xRecord
        if (Acad::eOk != parentObject->createExtensionDictionary())
            {
            DIAGNOSTIC_PRINTF ("Failed creating a new extended dictionary for element %I64d\n", parentElement.GetElementId());
            return  OutOfMemoryError;
            }

        // make sure we get a valid extention dictionary on which we can add children:
        if (!(ownerId = parentObject->extensionDictionary()).isValid())
            {
            DIAGNOSTIC_PRINTF ("Failed opening a newly created extended dictionary for element %I64d\n", parentElement.GetElementId());
            return  NullObjectId;
            }
        }

    RealDwgStatus   status = RealDwgSuccess;

    // iterate through all children
    for (ChildElemIter child(parentElement, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
        {
        // get existing xrecord for the child element if exists:
        ElementId       childId = child.GetElementId ();
        AcDbObjectId    existingObjectId;
        AcDbObjectP     existingObject = nullptr, xrecordObject = nullptr;
        if (m_fromDgnContext->SavingChanges() && (existingObjectId = m_fromDgnContext->ExistingObjectIdFromElementId(childId)).isValid() &&
            Acad::eOk != acdbOpenObject(existingObject, existingObjectId, AcDb::kForWrite))
            continue;

        // set the child xRecord from the child element
        status = __super::ToObject (child, xrecordObject, existingObject, *m_fromDgnContext);
        if (RealDwgSuccess != status)
            continue;

        // add or set the XAttributes to the child xRecord
        status = __super::AddOrSetXAttributesToXrecord (AcDbXrecord::cast(xrecordObject), &ownerId, child);

        if (RealDwgSuccess != status)
            DIAGNOSTIC_PRINTF ("Failed adding a child xRecord to extended dictionary from element %I64d\n", childId);

        if (nullptr != existingObject)
            existingObject->close ();
        if (nullptr != xrecordObject && existingObject != xrecordObject)
            xrecordObject->close ();
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual WString         GetXRecordNameForElement (ModelId modeId) const override
    {
    // DgnStore xRecord uses name: "DgnStoreElement elementType elementId attrOffset"
    WString         entryName;
    entryName.Sprintf (L"%ls %d %I64d %d", StringConstants::UstnDictionaryItem_DgnStore, m_elementType, m_elementId, m_attrOffset);
    return  entryName;
    }

};  // ToDwgExtDgnStore


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/11
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtSchemaElement : public ToDwgExtDgnStore
{
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // do not save DWG attribute definition schema
    SchemaElementHandler*   schemaHandler = dynamic_cast <SchemaElementHandler*> (&elemHandle.GetHandler());
    SchemaInfo              schemaInfo;
    if (nullptr != schemaHandler && BSISUCCESS == schemaHandler->SchemaInfoFromElement(elemHandle, schemaInfo))
        {
        ItemTypeLibraryPtr  itemtypeLibrary = context.GetFileHolder().FindBySchemaNameWithMap ( schemaInfo.GetSchemaName(), *context.GetFile() );
        if (itemtypeLibrary.IsValid() && itemtypeLibrary->IsDwgAttributeDefinitionsLibrary())
            {
            // if we hit here when editing a DWG file, we know DWG Attribute Definition library is dirty:
            if (context.SavingChanges())
                context.GetFileHolder().SetDwgAttrdefItemtypeLibraryDirty (true);
            return  RealDwgIgnoreElement;
            }
        }
    
    return  __super::ToObject (elemHandle, acObject, existingObject, context);
    }

};  // ToDwgExtSchemaElement


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/12
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtExtendedNonGraphics : public ToDwgExtNonGraphics   // type 107
{
public:
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // set the blob data from the type-107 to the xRecord
    RealDwgStatus   status = __super::ToObject (elemHandle, acObject, existingObject, context);
    if (RealDwgSuccess != status)
        return  status;

    // get or create a DgnModel dictionary as a parent of the type-107 xRecord
    AcDbObjectId    ownerId;
    if (Acad::eOk != this->GetOrCreateDgnModelDictionary(ownerId, elemHandle.GetDgnModelP()))
        return  CantOpenObject;
    
    // add or update XAttributes to the xRecord then save it to DWG
    return  this->AddOrSetXAttributesToXrecord (AcDbXrecord::cast(acObject), &ownerId, elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual bool    ShouldSaveToDwg (ConvertFromDgnContextR context) const override
    {
    // only save type-107 elements in dictionary, default and sheet models that are saved in the same file:
    if (__super::ShouldSaveToDwg(context))
        return  true;

    DgnModelP   currentModel = context.GetModel ();
    if (context.GetModelIdForDwgModelSpace() == currentModel->GetModelId() ||
        (DgnModelType::Sheet == currentModel->GetModelType() && !context.GetSettings().SaveSheetsToSeparateFiles()))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus   GetOrCreateDgnModelDictionary (AcDbObjectId& outId, DgnModelP model) const
    {
    if (nullptr == model)
        return  Acad::eInvalidInput;

    // all dictionary elements shall be saved on MicroStation root level
    outId = m_fromDgnContext->GetMicroStationDictionaryId (true);
    if (model->IsDictionaryModel())
        return  Acad::eOk;

    // extract or create the DgnModel dictionary entry under the MicroStation dictionary
    AcDbDictionaryPointer   mstnDictionary (outId, AcDb::kForWrite);
    if (Acad::eOk != mstnDictionary.openStatus())
        return  mstnDictionary.openStatus();

    // make up a persistent name: "DgnModel modelId modelName"
    WString     entryName;
    this->GetDictionaryNameForModel (entryName, model);
    
    // find existing DgnModel dictionary
    Acad::ErrorStatus   es = mstnDictionary->getAt (entryName.c_str(), outId);
    if (Acad::eOk == es)
        return  es;

    // create a new DgnModel dictionary
    AcDbDictionary*     newEntry = new AcDbDictionary ();
    if (nullptr == newEntry)
        return  Acad::eOutOfMemory;

    // add the new DgnModel entry to MicroStation dictionary
    es = mstnDictionary->setAt (entryName.c_str(), newEntry, outId);

    if (!newEntry->objectId().isValid())
        {
        delete newEntry;
        return  Acad::eInvalidObjectId;
        }

    newEntry->close ();

    return  es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetDictionaryNameForModel (WStringR entryName, DgnModelP model) const
    {
    DgnFileP    dgnFile = model->GetDgnFileP ();
    WString     modelName;
    if (nullptr != dgnFile && model->GetModelId() == dgnFile->GetDefaultModelId())
        modelName.assign (L"Model");
    else
        modelName.assign (model->GetModelName());

    entryName.Sprintf (L"%ls %d %ls", StringConstants::UstnDictionaryItem_DgnModel, model->GetModelId(), modelName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual WString         GetXRecordNameForElement (ModelId modelId) const override
    {
    // type 107 xRecord uses name: "ExtendedNonGraphics modelId elementId attrOffset"
    WString             entryName;
    entryName.Sprintf (L"%ls %d %I64d %d", StringConstants::UstnDictionaryItem_ExtendedNonGraphics, modelId, m_elementId, m_attrOffset);
    return  entryName;
    }

};  // ToDwgExtExtendedNonGraphics




/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::SaveDictionaryElementsToDatabase (UInt32 runningCount)
    {
    if (NULL == m_model || !m_model->IsDictionaryModel())
        {
        assert (false && L"Current model is expected to be the dictionary model!!");
        return;
        }

    AcDbObjectId            microStationDictionaryId = this->GetMicroStationDictionaryId (true);
    AcDbDictionaryPointer   pMicroStationDictionary (microStationDictionaryId, AcDb::kForWrite);
    if (Acad::eOk != pMicroStationDictionary.openStatus())
        return;

    PersistentElementRefListIterator    iter;
    PersistentElementRefList*           elemList = m_model->GetControlElementsP ();

    for (PersistentElementRefP elemRef = iter.GetFirstElementRef(*elemList); NULL != elemRef; elemRef = iter.GetNextElementRef())
        {
        Elm_hdr const*      ehdr = elemRef->GetElementHeaderCP ();
        if (NULL == ehdr)
            continue;

        switch (ehdr->type)
            {
            // these are the types we save to MicroStation Dictionary:
            case DGNFIL_HEADER_ELM:
                {
                // Header information handled seperately (extracted to database variables), unless we save settings:
                if (this->GetSettings().SaveMicroStationSettings())
                    break;
                else
                    continue;
                }

            case MICROSTATION_ELM:
                if (MSATTRDATA_LEVEL == ehdr->level)    // skip tagset
                    continue;
                // fall through for other type 66's
            case DGNSTORE_HDR:
            case DGNSTORE_COMP:
            case EXTENDED_NONGRAPHIC_ELM:
                {
                if (this->GetIsSavingApplicationData())
                    break;
                else
                    continue;
                }

            // other non-graphic elements supported and not controlled by any settings:
            case NAMED_GROUP_HDR_ELM:
                break;

            // all other types are ignored - either processed elsewhere or unsupported at all.
            default:
                continue;
            }

        runningCount += (1 + elemRef->GetComponentCount());
        this->ReportProgress ((double) runningCount / (double) m_elementCount);

        ElementHandle   elemHandle(elemRef, m_model);

        ToDwgExtension* toDwg = ToDwgExtension::Cast (elemHandle.GetHandler());
        if (NULL == toDwg)
            continue;

        AcDbObjectId    existingObjectId;
        AcDbObjectP     existingObject = NULL;
        if (m_savingChanges && (existingObjectId = this->ExistingObjectIdFromElementId(ehdr->uniqueId)).isValid() &&
            Acad::eOk != acdbOpenObject(existingObject, existingObjectId, AcDb::kForWrite))
            continue;

        /*-------------------------------------------------------------------------------
        Since adding extension dictionary requires hosting object's database residency,
        we will let ToObject to add objects to MicroStation dictionary. We have to close
        the dictionary now such that it can be opened again in the ToObject method.
        -------------------------------------------------------------------------------*/
        pMicroStationDictionary->close ();
        
        AcDbObjectP     resultObject = NULL;
        RealDwgStatus   status = toDwg->ToObject (elemHandle, resultObject, existingObject, *this);

        if (NULL != existingObject)
            existingObject->close ();

        if (RealDwgIgnoreElement == status)
            DIAGNOSTIC_PRINTF ("Ignored dictionary element type=%d, ID=%I64d\n", ehdr->type, ehdr->uniqueId);
        else if (RealDwgSuccess != status && NoConversionMethod != status)
            DIAGNOSTIC_PRINTF ("Failed saving dictionary element type=%d, ID=%I64d\n", ehdr->type, ehdr->uniqueId);

        if (nullptr != resultObject && existingObject != resultObject)
            resultObject->close ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ParseDictionaryNameForModelId (ModelId& modelId, WStringR modelName, WStringCR entryName, size_t startAt)
    {
    WString     parseString = entryName.substr (startAt);
    if (parseString.empty())
        return  false;

    // step over the space char
    if (parseString.at(0) == L' ')
        parseString.erase (0, 1);

    // next is the model ID
    WCharP      trailingChars;
    modelId = BeStringUtilities::Wcstol (parseString.c_str(), &trailingChars, 10);

    modelName.clear ();

    if (modelId <= INVALID_MODELID)
        return  false;

    // legacy dictionaries such as type-66 do not have a model name, in which case next string is return anyway
    if (nullptr != trailingChars)
        {
        modelName.assign (trailingChars);
        if (modelName.at(0) == L' ')
            modelName.erase (0, 1);
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
static Acad::ErrorStatus    DeleteObjectById (AcDbObjectId id, const ACHAR* entryName)
    {
    AcDbSmartObjectPointer<AcDbObject>  acObject(id, AcDb::kForWrite);
    Acad::ErrorStatus                   es = acObject.openStatus ();

    if (Acad::eOk != es || Acad::eOk != (es = acObject->erase()))
        DIAGNOSTIC_PRINTF ("Failed purging dictionary %ls! [%ls]\n", entryName, acadErrorStatusText(es));

    return  es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::PurgeMicroStationDictionary ()
    {
    /*----------------------------------------------------------------------------------------------------------
    The MicroStation dictionary in a DWG file holds data saved from both dictionary and model elements.  When
    a model is removed or a model non-graphical element is deleted in MicroStation, we want to remove these
    dictionary entries as well.
    ----------------------------------------------------------------------------------------------------------*/
    if (!this->GetIsSavingApplicationData())
        return  RealDwgSuccess;

    AcDbDictionaryPointer   acMicroStationDictionary (this->GetMicroStationDictionaryId(false), AcDb::kForWrite);
    if (Acad::eOk != acMicroStationDictionary.openStatus())
        return  CantOpenObject;

    DgnFileP                dgnFile = this->GetFile ();
    if (nullptr == dgnFile)
        return  CantAccessMstnElement;

    size_t              nCharsDgnModel      = wcslen (StringConstants::UstnDictionaryItem_DgnModel);
    size_t              nCharsDgnStore      = wcslen (StringConstants::UstnDictionaryItem_DgnStore);
    size_t              nCharsType66Model   = wcslen (StringConstants::UstnDictionaryItem_ModelApplication);
    size_t              nCharsType66Dict    = wcslen (StringConstants::UstnDictionaryItem_Application);
    size_t              nCharsType107       = wcslen (StringConstants::UstnDictionaryItem_ExtendedNonGraphics);
    Acad::ErrorStatus   es = Acad::eOk;

    ModelIndex const&       modelIndex = dgnFile->GetModelIndex ();
    AcDbDictionaryIterator* iterator = acMicroStationDictionary->newIterator ();
    for (; !iterator->done(); iterator->next())
        {
        WString         entryName = WString (iterator->name());
        WString         modelName;
        ModelId         modelId = 0;
        ElementId       elementId = INVALID_ELEMENTID;
        size_t          nCharsToNext = 0;

        if (entryName.StartsWith(StringConstants::UstnDictionaryItem_DgnModel) && 
            ParseDictionaryNameForModelId(modelId, modelName, entryName, nCharsDgnModel) &&
            modelId != this->GetModelIdForDwgModelSpace())
            {
            // purge DgnModel dictionary entries
            AcDbDictionaryPointer   dictionaryEntry(iterator->objectId(), AcDb::kForWrite);
            if (Acad::eOk != (es = dictionaryEntry.openStatus()))
                {
                DIAGNOSTIC_PRINTF ("Failed opening dictionary %ls! [%ls]\n", iterator->name(), acadErrorStatusText(es));
                continue;
                }

            ModelIndexItemCP    modelItem = modelIndex.GetItemByID (modelId, false);
            WCharCP             itemName = nullptr;

            if (nullptr == modelItem || nullptr == (itemName = modelItem->GetName()) || 0 != BeStringUtilities::Wcsicmp(itemName, modelName.GetWCharCP()))
                {
                // the DGN model no longer exists in the DGN file, erase this dictionary
                es = dictionaryEntry->erase ();
                if (Acad::eOk != es)
                    DIAGNOSTIC_PRINTF ("Failed purging dictionary %ls! [%ls]\n", iterator->name(), acadErrorStatusText(es));
                }
            else
                {
                // the model index appears valid - verify actual model
                DgnModelP       model = dgnFile->FindLoadedModelById (modelId);
                if (nullptr == model)
                    {
                    // no model - erase the entry with its children
                    es = dictionaryEntry->erase ();
                    if (Acad::eOk != es)
                        DIAGNOSTIC_PRINTF ("Failed purging dictionary %ls! [%ls]\n", iterator->name(), acadErrorStatusText(es));
                    continue;
                    }

                // model is valid, erase no longer existing children:
                AcDbDictionaryIterator*     childIter = dictionaryEntry->newIterator ();
                for (; !childIter->done(); childIter->next())
                    {
                    elementId = this->ElementIdFromObjectId (childIter->objectId());

                    if (0 == elementId || INVALID_ELEMENTID == elementId || nullptr == model->FindElementByID(elementId))
                        es = DeleteObjectById (childIter->objectId(), childIter->name());
                    }
                delete childIter;

                // if all children are erased, also erase the parent
                if (0 == dictionaryEntry->numEntries())
                    es = dictionaryEntry->erase ();
                }
            }
        else if (entryName.StartsWith(StringConstants::UstnDictionaryItem_ModelApplication) &&
                 ParseDictionaryNameForModelId(modelId, modelName, entryName, nCharsType66Model) &&
                 modelId != this->GetModelIdForDwgModelSpace() && modelId >= 0)
            {
            // purge model type-66 dictionary entries (model name was not saved in the dictionary name so do not check model name).
            DgnModelP       model = dgnFile->FindLoadedModelById (modelId);

            if (nullptr == model)
                {
                // the model where the type-66 originately came from no longer exists, erase the entry
                es = DeleteObjectById (iterator->objectId(), iterator->name());
                continue;
                }

            // if original type-66 element no longer exists in the model - erase the entry
            elementId = this->ElementIdFromObjectId (iterator->objectId());
            if (0 == elementId || INVALID_ELEMENTID == elementId || nullptr == model->FindElementByID(elementId))
                es = DeleteObjectById (iterator->objectId(), iterator->name());
            }
        else if (entryName.StartsWith(StringConstants::UstnDictionaryItem_Application))
            {
            // purge the non-model type-66 dictionary entries
            elementId = this->ElementIdFromObjectId (iterator->objectId());

            if (0 == elementId || INVALID_ELEMENTID == elementId || nullptr == dgnFile->FindByElementId(elementId, true))
                es = DeleteObjectById (iterator->objectId(), iterator->name());
            }
        else if (entryName.StartsWith(StringConstants::UstnDictionaryItem_DgnStore))
            {
            // purge the DgnStore dictionary entries
            elementId = this->ElementIdFromObjectId (iterator->objectId());

            // don't bother with nesting into its components, either all in or all out:
            if (0 == elementId || INVALID_ELEMENTID == elementId || nullptr == dgnFile->FindByElementId(elementId, true))
                es = DeleteObjectById (iterator->objectId(), iterator->name());
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Dictionary %ls not purged!\n", iterator->name());
            }
        }

    delete iterator;

    return  RealDwgSuccess;
    }
