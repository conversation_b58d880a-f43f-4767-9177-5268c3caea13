#pragma once

#include "../../core/GeometryProcessor.h"
#include "../../../include/ExportTypes.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbcurve.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/acgi.h>
#include <realdwg/base/gevec3d.h>
#include <realdwg/base/gepoint3d.h>
#include <realdwg/base/geline3d.h>
#include <realdwg/base/gearc3d.h>
#include <realdwg/base/gecircle3d.h>
#include <realdwg/base/geellipse3d.h>
#include <realdwg/base/genurb3d.h>
#include <realdwg/base/geplane.h>
#include <realdwg/base/gesphere.h>
#include <realdwg/base/gecylinder.h>
#include <realdwg/base/gecone.h>
#include <realdwg/base/getorus.h>
#endif

#include <memory>
#include <vector>
#include <unordered_map>

namespace IModelExport {

//=======================================================================================
// DWG Geometry Processor - Specialized geometry processing for DWG format
//=======================================================================================

class DWGGeometryProcessor : public GeometryProcessor {
public:
    DWGGeometryProcessor();
    virtual ~DWGGeometryProcessor();

    //===================================================================================
    // DWG-Specific Geometry Conversion
    //===================================================================================

    // Convert iModel geometry to DWG entities
    bool ConvertToDWGEntity(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    
    // Convert specific geometry types
    bool ConvertLineToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertArcToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertCircleToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertEllipseToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertSplineToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertPolylineToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    
    // Surface conversion
    bool ConvertPlaneToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertCylinderToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertSphereToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertConeToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertTorusToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertNURBSSurfaceToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    
    // Solid conversion
    bool ConvertBoxToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertWedgeToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertPyramidToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertExtrudedSolidToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertRevolvedSolidToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertSweptSolidToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertBooleanSolidToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    
    // Mesh conversion
    bool ConvertMeshToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertSubDMeshToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);
    bool ConvertPointCloudToDWG(const GeometryData& geometry, std::shared_ptr<void>& dwgEntity);

    //===================================================================================
    // Advanced Geometry Processing
    //===================================================================================

    // Geometry analysis and optimization
    bool AnalyzeDWGGeometry(const GeometryData& geometry, GeometryAnalysis& analysis);
    bool OptimizeForDWG(GeometryData& geometry);
    bool SimplifyGeometry(GeometryData& geometry, double tolerance);
    bool RepairGeometry(GeometryData& geometry);
    
    // Tessellation and approximation
    bool TessellateForDWG(const GeometryData& geometry, std::vector<Point3d>& points, std::vector<int>& indices);
    bool ApproximateCurve(const CurveData& curve, std::vector<Point3d>& points, double tolerance);
    bool ApproximateSurface(const SurfaceData& surface, std::vector<Point3d>& points, 
                           std::vector<int>& indices, double tolerance);
    
    // Geometry validation
    bool ValidateForDWG(const GeometryData& geometry, std::vector<std::string>& issues);
    bool CheckGeometryLimits(const GeometryData& geometry);
    bool CheckTopology(const GeometryData& geometry);
    
    //===================================================================================
    // Coordinate System and Transformation
    //===================================================================================

    // Coordinate transformation
    bool TransformGeometry(GeometryData& geometry, const Transform3d& transform);
    bool TransformPoints(std::vector<Point3d>& points, const Transform3d& transform);
    bool TransformVectors(std::vector<Vector3d>& vectors, const Transform3d& transform);
    
    // Units conversion
    bool ConvertUnits(GeometryData& geometry, double scaleFactor);
    bool ConvertLengthUnits(std::vector<Point3d>& points, double scaleFactor);
    bool ConvertAngleUnits(std::vector<double>& angles, double scaleFactor);
    
    // Precision and tolerance
    bool AdjustPrecision(GeometryData& geometry, double precision);
    bool SnapToGrid(std::vector<Point3d>& points, double gridSize);
    bool RemoveDuplicatePoints(std::vector<Point3d>& points, double tolerance);

    //===================================================================================
    // DWG-Specific Geometry Features
    //===================================================================================

    // Layer and visual properties
    struct DWGGeometryProperties {
        std::string layerName;
        Color color;
        std::string lineType;
        double lineWeight;
        double lineTypeScale;
        bool visible;
        int colorIndex;
        std::string material;
    };
    
    bool ApplyDWGProperties(std::shared_ptr<void>& dwgEntity, const DWGGeometryProperties& properties);
    bool ExtractDWGProperties(const std::shared_ptr<void>& dwgEntity, DWGGeometryProperties& properties);
    
    // Extended data and custom properties
    bool AddExtendedData(std::shared_ptr<void>& dwgEntity, const std::string& appName, 
                        const std::unordered_map<std::string, std::string>& data);
    bool GetExtendedData(const std::shared_ptr<void>& dwgEntity, const std::string& appName,
                        std::unordered_map<std::string, std::string>& data);
    
    // Geometry grouping and organization
    bool CreateGeometryGroup(const std::vector<std::shared_ptr<void>>& entities, const std::string& groupName);
    bool AddToGeometryGroup(const std::shared_ptr<void>& entity, const std::string& groupName);
    bool RemoveFromGeometryGroup(const std::shared_ptr<void>& entity, const std::string& groupName);

    //===================================================================================
    // Performance and Memory Management
    //===================================================================================

    // Batch processing
    bool ProcessGeometryBatch(const std::vector<GeometryData>& geometries, 
                             std::vector<std::shared_ptr<void>>& dwgEntities);
    
    // Memory optimization
    bool OptimizeMemoryUsage();
    bool ClearGeometryCache();
    size_t GetMemoryUsage() const;
    
    // Streaming processing for large datasets
    bool StartStreamingProcess();
    bool ProcessGeometryStream(const GeometryData& geometry);
    bool EndStreamingProcess();

    //===================================================================================
    // Quality Control and Metrics
    //===================================================================================

    // Conversion quality assessment
    struct ConversionQuality {
        double geometricAccuracy;    // 0.0 to 1.0
        double topologicalIntegrity; // 0.0 to 1.0
        double visualFidelity;       // 0.0 to 1.0
        size_t warningCount;
        size_t errorCount;
        std::vector<std::string> issues;
    };
    
    ConversionQuality AssessConversionQuality(const GeometryData& original, 
                                             const std::shared_ptr<void>& converted);
    
    // Performance metrics
    struct ProcessingMetrics {
        size_t totalGeometries;
        size_t processedGeometries;
        size_t failedGeometries;
        double totalProcessingTime;
        double averageProcessingTime;
        size_t memoryUsage;
        std::unordered_map<std::string, size_t> geometryTypeCounts;
    };
    
    ProcessingMetrics GetProcessingMetrics() const;
    void ResetMetrics();

private:
    //===================================================================================
    // Internal State and Configuration
    //===================================================================================

    // Processing configuration
    struct ProcessingConfig {
        double geometryTolerance = 1e-6;
        double angleTolerance = 1e-6;
        bool enableOptimization = true;
        bool enableValidation = true;
        bool enableRepair = true;
        size_t maxVertices = 100000;
        size_t maxFaces = 200000;
        double maxCoordinate = 1e6;
        double minCoordinate = -1e6;
    };
    
    ProcessingConfig m_config;
    ProcessingMetrics m_metrics;
    
    // Geometry cache for optimization
    std::unordered_map<std::string, std::shared_ptr<void>> m_geometryCache;
    std::unordered_map<std::string, GeometryData> m_processedGeometry;
    
    // Streaming state
    bool m_streamingMode = false;
    std::vector<GeometryData> m_streamBuffer;
    
#ifdef REALDWG_AVAILABLE
    // RealDWG specific state
    AcDbDatabase* m_database = nullptr;
    
    // Geometry creation helpers
    AcDbLine* CreateDWGLine(const Point3d& start, const Point3d& end);
    AcDbArc* CreateDWGArc(const Point3d& center, double radius, double startAngle, double endAngle);
    AcDbCircle* CreateDWGCircle(const Point3d& center, double radius);
    AcDbEllipse* CreateDWGEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio);
    AcDbSpline* CreateDWGSpline(const std::vector<Point3d>& controlPoints, int degree);
    AcDbPolyline* CreateDWGPolyline(const std::vector<Point3d>& points, bool closed);
    AcDb3dSolid* CreateDWG3DSolid(const GeometryData& geometry);
    AcDbSurface* CreateDWGSurface(const GeometryData& geometry);
    AcDbSubDMesh* CreateDWGMesh(const GeometryData& geometry);
    
    // Conversion helpers
    AcGePoint3d ToAcGePoint3d(const Point3d& point);
    AcGeVector3d ToAcGeVector3d(const Vector3d& vector);
    Point3d FromAcGePoint3d(const AcGePoint3d& point);
    Vector3d FromAcGeVector3d(const AcGeVector3d& vector);
    
    // Geometry analysis helpers
    bool AnalyzeCurveGeometry(AcDbCurve* curve, GeometryAnalysis& analysis);
    bool AnalyzeSurfaceGeometry(AcDbSurface* surface, GeometryAnalysis& analysis);
    bool AnalyzeSolidGeometry(AcDb3dSolid* solid, GeometryAnalysis& analysis);
#endif

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Geometry type detection
    GeometryData::Type DetectGeometryType(const GeometryData& geometry);
    bool IsLinearGeometry(const GeometryData& geometry);
    bool IsPlanarGeometry(const GeometryData& geometry);
    bool IsClosedGeometry(const GeometryData& geometry);
    
    // Geometry validation helpers
    bool ValidatePoints(const std::vector<Point3d>& points);
    bool ValidateIndices(const std::vector<int>& indices, size_t pointCount);
    bool ValidateNormals(const std::vector<Vector3d>& normals);
    bool ValidateUVCoordinates(const std::vector<Point3d>& uvs);
    
    // Error handling and logging
    void LogProcessingError(const std::string& message);
    void LogProcessingWarning(const std::string& message);
    void UpdateMetrics(const GeometryData& geometry, bool success);
    
    // Utility methods
    std::string GenerateGeometryId(const GeometryData& geometry);
    bool IsGeometryCached(const std::string& geometryId);
    void CacheGeometry(const std::string& geometryId, const std::shared_ptr<void>& dwgEntity);
    std::shared_ptr<void> GetCachedGeometry(const std::string& geometryId);
    
    // Cleanup
    void CleanupResources();
};

//=======================================================================================
// DWG Geometry Utilities
//=======================================================================================

namespace DWGGeometryUtils {
    
    // Geometry type mapping
    std::string GetDWGEntityType(GeometryData::Type geometryType);
    GeometryData::Type GetGeometryType(const std::string& dwgEntityType);
    
    // Precision and tolerance helpers
    double GetRecommendedTolerance(const GeometryData& geometry);
    bool IsWithinTolerance(double value1, double value2, double tolerance);
    Point3d SnapToTolerance(const Point3d& point, double tolerance);
    
    // Geometry bounds and limits
    BoundingBox CalculateBounds(const GeometryData& geometry);
    bool IsWithinDWGLimits(const GeometryData& geometry);
    bool ScaleToFitLimits(GeometryData& geometry);
    
    // Geometry complexity analysis
    size_t CalculateComplexity(const GeometryData& geometry);
    double EstimateProcessingTime(const GeometryData& geometry);
    size_t EstimateMemoryUsage(const GeometryData& geometry);
    
    // Conversion quality helpers
    double CalculateGeometricDeviation(const GeometryData& original, const GeometryData& converted);
    double CalculateVisualSimilarity(const GeometryData& original, const GeometryData& converted);
    bool CompareTopology(const GeometryData& original, const GeometryData& converted);
}

} // namespace IModelExport
