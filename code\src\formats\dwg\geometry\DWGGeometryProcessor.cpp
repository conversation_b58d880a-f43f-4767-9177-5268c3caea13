#include "DWGGeometryProcessor.h"
#include <algorithm>
#include <cmath>
#include <limits>

namespace IModelExport {

//=======================================================================================
// Transform3D Implementation
//=======================================================================================

Transform3D Transform3D::Identity() {
    Transform3D transform;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            transform.matrix[i][j] = (i == j) ? 1.0 : 0.0;
        }
    }
    return transform;
}

Transform3D Transform3D::Translation(const Vector3d& translation) {
    Transform3D transform = Identity();
    transform.matrix[0][3] = translation.x;
    transform.matrix[1][3] = translation.y;
    transform.matrix[2][3] = translation.z;
    return transform;
}

Transform3D Transform3D::Rotation(const Vector3d& axis, double angle) {
    Transform3D transform = Identity();
    
    // Normalize axis
    double length = std::sqrt(axis.x * axis.x + axis.y * axis.y + axis.z * axis.z);
    if (length < 1e-10) {
        return transform; // Return identity for zero-length axis
    }
    
    Vector3d normalizedAxis(axis.x / length, axis.y / length, axis.z / length);
    
    double cos_a = std::cos(angle);
    double sin_a = std::sin(angle);
    double one_minus_cos = 1.0 - cos_a;
    
    double x = normalizedAxis.x;
    double y = normalizedAxis.y;
    double z = normalizedAxis.z;
    
    // Rodrigues' rotation formula
    transform.matrix[0][0] = cos_a + x * x * one_minus_cos;
    transform.matrix[0][1] = x * y * one_minus_cos - z * sin_a;
    transform.matrix[0][2] = x * z * one_minus_cos + y * sin_a;
    
    transform.matrix[1][0] = y * x * one_minus_cos + z * sin_a;
    transform.matrix[1][1] = cos_a + y * y * one_minus_cos;
    transform.matrix[1][2] = y * z * one_minus_cos - x * sin_a;
    
    transform.matrix[2][0] = z * x * one_minus_cos - y * sin_a;
    transform.matrix[2][1] = z * y * one_minus_cos + x * sin_a;
    transform.matrix[2][2] = cos_a + z * z * one_minus_cos;
    
    return transform;
}

Transform3D Transform3D::Scale(const Vector3d& scale) {
    Transform3D transform = Identity();
    transform.matrix[0][0] = scale.x;
    transform.matrix[1][1] = scale.y;
    transform.matrix[2][2] = scale.z;
    return transform;
}

Point3d Transform3D::TransformPoint(const Point3d& point) const {
    return Point3d(
        matrix[0][0] * point.x + matrix[0][1] * point.y + matrix[0][2] * point.z + matrix[0][3],
        matrix[1][0] * point.x + matrix[1][1] * point.y + matrix[1][2] * point.z + matrix[1][3],
        matrix[2][0] * point.x + matrix[2][1] * point.y + matrix[2][2] * point.z + matrix[2][3]
    );
}

Vector3d Transform3D::TransformVector(const Vector3d& vector) const {
    return Vector3d(
        matrix[0][0] * vector.x + matrix[0][1] * vector.y + matrix[0][2] * vector.z,
        matrix[1][0] * vector.x + matrix[1][1] * vector.y + matrix[1][2] * vector.z,
        matrix[2][0] * vector.x + matrix[2][1] * vector.y + matrix[2][2] * vector.z
    );
}

Transform3D Transform3D::Multiply(const Transform3D& other) const {
    Transform3D result;
    
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.matrix[i][j] = 0.0;
            for (int k = 0; k < 4; ++k) {
                result.matrix[i][j] += matrix[i][k] * other.matrix[k][j];
            }
        }
    }
    
    return result;
}

//=======================================================================================
// BoundingBox3D Implementation
//=======================================================================================

BoundingBox3D::BoundingBox3D() {
    Reset();
}

BoundingBox3D::BoundingBox3D(const Point3d& min, const Point3d& max)
    : minPoint(min), maxPoint(max) {
}

void BoundingBox3D::Reset() {
    minPoint = Point3d(std::numeric_limits<double>::max(), 
                      std::numeric_limits<double>::max(), 
                      std::numeric_limits<double>::max());
    maxPoint = Point3d(std::numeric_limits<double>::lowest(), 
                      std::numeric_limits<double>::lowest(), 
                      std::numeric_limits<double>::lowest());
}

void BoundingBox3D::AddPoint(const Point3d& point) {
    minPoint.x = std::min(minPoint.x, point.x);
    minPoint.y = std::min(minPoint.y, point.y);
    minPoint.z = std::min(minPoint.z, point.z);
    
    maxPoint.x = std::max(maxPoint.x, point.x);
    maxPoint.y = std::max(maxPoint.y, point.y);
    maxPoint.z = std::max(maxPoint.z, point.z);
}

void BoundingBox3D::AddBoundingBox(const BoundingBox3D& other) {
    if (!other.IsValid()) {
        return;
    }
    
    AddPoint(other.minPoint);
    AddPoint(other.maxPoint);
}

bool BoundingBox3D::IsValid() const {
    return minPoint.x <= maxPoint.x && 
           minPoint.y <= maxPoint.y && 
           minPoint.z <= maxPoint.z;
}

bool BoundingBox3D::Contains(const Point3d& point) const {
    return point.x >= minPoint.x && point.x <= maxPoint.x &&
           point.y >= minPoint.y && point.y <= maxPoint.y &&
           point.z >= minPoint.z && point.z <= maxPoint.z;
}

bool BoundingBox3D::Intersects(const BoundingBox3D& other) const {
    return !(maxPoint.x < other.minPoint.x || minPoint.x > other.maxPoint.x ||
             maxPoint.y < other.minPoint.y || minPoint.y > other.maxPoint.y ||
             maxPoint.z < other.minPoint.z || minPoint.z > other.maxPoint.z);
}

Point3d BoundingBox3D::GetCenter() const {
    return Point3d(
        (minPoint.x + maxPoint.x) * 0.5,
        (minPoint.y + maxPoint.y) * 0.5,
        (minPoint.z + maxPoint.z) * 0.5
    );
}

Vector3d BoundingBox3D::GetSize() const {
    return Vector3d(
        maxPoint.x - minPoint.x,
        maxPoint.y - minPoint.y,
        maxPoint.z - minPoint.z
    );
}

double BoundingBox3D::GetVolume() const {
    if (!IsValid()) {
        return 0.0;
    }
    
    Vector3d size = GetSize();
    return size.x * size.y * size.z;
}

//=======================================================================================
// DWGGeometryProcessor Implementation
//=======================================================================================

DWGGeometryProcessor::DWGGeometryProcessor()
    : m_transform(Transform3D::Identity())
    , m_tolerance(1e-6)
    , m_angleTolerance(1e-8)
    , m_enableValidation(true)
    , m_enableRepair(true)
    , m_enableOptimization(true)
{
}

void DWGGeometryProcessor::SetTransform(const Transform3D& transform) {
    m_transform = transform;
}

Transform3D DWGGeometryProcessor::GetTransform() const {
    return m_transform;
}

void DWGGeometryProcessor::SetTolerance(double tolerance) {
    m_tolerance = std::max(tolerance, 1e-12); // Minimum tolerance
}

double DWGGeometryProcessor::GetTolerance() const {
    return m_tolerance;
}

//=======================================================================================
// Point Validation and Repair
//=======================================================================================

bool DWGGeometryProcessor::ValidatePoint(const Point3d& point) const {
    return std::isfinite(point.x) && std::isfinite(point.y) && std::isfinite(point.z);
}

Point3d DWGGeometryProcessor::RepairPoint(const Point3d& point) const {
    Point3d repaired = point;
    
    // Replace infinite or NaN values with zero
    if (!std::isfinite(repaired.x)) {
        repaired.x = 0.0;
    }
    if (!std::isfinite(repaired.y)) {
        repaired.y = 0.0;
    }
    if (!std::isfinite(repaired.z)) {
        repaired.z = 0.0;
    }
    
    return repaired;
}

bool DWGGeometryProcessor::CoerceInvalidElevation(double& elevation) const {
    if (!std::isfinite(elevation)) {
        elevation = 0.0;
        return true; // Repaired
    }
    
    // Check for extremely large values that might cause issues
    const double maxElevation = 1e6;
    if (std::abs(elevation) > maxElevation) {
        elevation = (elevation > 0) ? maxElevation : -maxElevation;
        return true; // Repaired
    }
    
    return false; // No repair needed
}

bool DWGGeometryProcessor::RepairPoints(std::vector<Point3d>& points) const {
    bool repaired = false;
    
    for (auto& point : points) {
        if (!ValidatePoint(point)) {
            point = RepairPoint(point);
            repaired = true;
        }
    }
    
    return repaired;
}

//=======================================================================================
// Vector Operations
//=======================================================================================

bool DWGGeometryProcessor::ValidateVector(const Vector3d& vector) const {
    return std::isfinite(vector.x) && std::isfinite(vector.y) && std::isfinite(vector.z);
}

Vector3d DWGGeometryProcessor::NormalizeVector(const Vector3d& vector) const {
    double length = std::sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    
    if (length < m_tolerance) {
        return Vector3d(0, 0, 1); // Default to Z-axis if vector is too small
    }
    
    return Vector3d(vector.x / length, vector.y / length, vector.z / length);
}

double DWGGeometryProcessor::VectorLength(const Vector3d& vector) const {
    return std::sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
}

double DWGGeometryProcessor::DotProduct(const Vector3d& v1, const Vector3d& v2) const {
    return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
}

Vector3d DWGGeometryProcessor::CrossProduct(const Vector3d& v1, const Vector3d& v2) const {
    return Vector3d(
        v1.y * v2.z - v1.z * v2.y,
        v1.z * v2.x - v1.x * v2.z,
        v1.x * v2.y - v1.y * v2.x
    );
}

//=======================================================================================
// Distance and Angle Calculations
//=======================================================================================

double DWGGeometryProcessor::DistancePointToPoint(const Point3d& p1, const Point3d& p2) const {
    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;
    double dz = p2.z - p1.z;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

double DWGGeometryProcessor::DistancePointToLine(const Point3d& point, const Point3d& lineStart, const Point3d& lineEnd) const {
    Vector3d lineVector(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
    Vector3d pointVector(point.x - lineStart.x, point.y - lineStart.y, point.z - lineStart.z);
    
    double lineLength = VectorLength(lineVector);
    if (lineLength < m_tolerance) {
        return DistancePointToPoint(point, lineStart);
    }
    
    Vector3d normalizedLine = NormalizeVector(lineVector);
    double projection = DotProduct(pointVector, normalizedLine);
    
    // Clamp projection to line segment
    projection = std::max(0.0, std::min(lineLength, projection));
    
    Point3d closestPoint(
        lineStart.x + projection * normalizedLine.x,
        lineStart.y + projection * normalizedLine.y,
        lineStart.z + projection * normalizedLine.z
    );
    
    return DistancePointToPoint(point, closestPoint);
}

double DWGGeometryProcessor::AngleBetweenVectors(const Vector3d& v1, const Vector3d& v2) const {
    double len1 = VectorLength(v1);
    double len2 = VectorLength(v2);
    
    if (len1 < m_tolerance || len2 < m_tolerance) {
        return 0.0;
    }
    
    double dot = DotProduct(v1, v2) / (len1 * len2);
    
    // Clamp to valid range for acos
    dot = std::max(-1.0, std::min(1.0, dot));
    
    return std::acos(dot);
}

double DWGGeometryProcessor::CalculateCurvature(const Point3d& p1, const Point3d& p2, const Point3d& p3) const {
    Vector3d v1(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Vector3d v2(p3.x - p2.x, p3.y - p2.y, p3.z - p2.z);
    
    Vector3d cross = CrossProduct(v1, v2);
    double crossLength = VectorLength(cross);
    double v1Length = VectorLength(v1);
    
    if (v1Length < m_tolerance) {
        return 0.0;
    }
    
    return crossLength / (v1Length * v1Length * v1Length);
}

//=======================================================================================
// Transformation Methods
//=======================================================================================

Point3d DWGGeometryProcessor::TransformPoint(const Point3d& point) const {
    return m_transform.TransformPoint(point);
}

Vector3d DWGGeometryProcessor::TransformVector(const Vector3d& vector) const {
    return m_transform.TransformVector(vector);
}

std::vector<Point3d> DWGGeometryProcessor::TransformPoints(const std::vector<Point3d>& points) const {
    std::vector<Point3d> transformed;
    transformed.reserve(points.size());
    
    for (const auto& point : points) {
        transformed.push_back(TransformPoint(point));
    }
    
    return transformed;
}

//=======================================================================================
// Geometric Analysis
//=======================================================================================

bool DWGGeometryProcessor::ArePointsCollinear(const std::vector<Point3d>& points, double tolerance) const {
    if (points.size() < 3) {
        return true;
    }
    
    // Use first two points to define the line
    Vector3d lineDirection(points[1].x - points[0].x, points[1].y - points[0].y, points[1].z - points[0].z);
    double lineLength = VectorLength(lineDirection);
    
    if (lineLength < m_tolerance) {
        return false; // First two points are coincident
    }
    
    lineDirection = NormalizeVector(lineDirection);
    
    // Check if all other points lie on this line
    for (size_t i = 2; i < points.size(); ++i) {
        double distance = DistancePointToLine(points[i], points[0], points[1]);
        if (distance > tolerance) {
            return false;
        }
    }
    
    return true;
}

bool DWGGeometryProcessor::ArePointsCoplanar(const std::vector<Point3d>& points, double tolerance) const {
    if (points.size() < 4) {
        return true;
    }
    
    // Find three non-collinear points to define the plane
    Vector3d normal;
    bool planeFound = false;
    
    for (size_t i = 0; i < points.size() - 2 && !planeFound; ++i) {
        for (size_t j = i + 1; j < points.size() - 1 && !planeFound; ++j) {
            for (size_t k = j + 1; k < points.size() && !planeFound; ++k) {
                Vector3d v1(points[j].x - points[i].x, points[j].y - points[i].y, points[j].z - points[i].z);
                Vector3d v2(points[k].x - points[i].x, points[k].y - points[i].y, points[k].z - points[i].z);
                
                normal = CrossProduct(v1, v2);
                if (VectorLength(normal) > m_tolerance) {
                    normal = NormalizeVector(normal);
                    planeFound = true;
                }
            }
        }
    }
    
    if (!planeFound) {
        return true; // All points are collinear
    }
    
    // Check if all points lie on this plane
    for (const auto& point : points) {
        double distance = DistancePointToPlane(point, points[0], normal);
        if (distance > tolerance) {
            return false;
        }
    }
    
    return true;
}

double DWGGeometryProcessor::CalculatePolygonArea(const std::vector<Point3d>& points) const {
    if (points.size() < 3) {
        return 0.0;
    }
    
    // Use the shoelace formula for 3D polygons
    Vector3d normal(0, 0, 0);
    
    for (size_t i = 0; i < points.size(); ++i) {
        size_t j = (i + 1) % points.size();
        Vector3d cross = CrossProduct(
            Vector3d(points[i].x, points[i].y, points[i].z),
            Vector3d(points[j].x, points[j].y, points[j].z)
        );
        normal.x += cross.x;
        normal.y += cross.y;
        normal.z += cross.z;
    }
    
    return 0.5 * VectorLength(normal);
}

//=======================================================================================
// Collision Detection
//=======================================================================================

bool DWGGeometryProcessor::LineLineIntersection(const Point3d& line1Start, const Point3d& line1End,
                                               const Point3d& line2Start, const Point3d& line2End,
                                               Point3d& intersection) const {
    Vector3d d1(line1End.x - line1Start.x, line1End.y - line1Start.y, line1End.z - line1Start.z);
    Vector3d d2(line2End.x - line2Start.x, line2End.y - line2Start.y, line2End.z - line2Start.z);
    Vector3d d3(line2Start.x - line1Start.x, line2Start.y - line1Start.y, line2Start.z - line1Start.z);
    
    Vector3d cross = CrossProduct(d1, d2);
    double crossLength = VectorLength(cross);
    
    if (crossLength < m_tolerance) {
        return false; // Lines are parallel
    }
    
    // Calculate parameters
    double t1 = DotProduct(CrossProduct(d3, d2), cross) / (crossLength * crossLength);
    double t2 = DotProduct(CrossProduct(d3, d1), cross) / (crossLength * crossLength);
    
    // Check if intersection is within line segments
    if (t1 >= 0.0 && t1 <= 1.0 && t2 >= 0.0 && t2 <= 1.0) {
        intersection = Point3d(
            line1Start.x + t1 * d1.x,
            line1Start.y + t1 * d1.y,
            line1Start.z + t1 * d1.z
        );
        return true;
    }
    
    return false;
}

bool DWGGeometryProcessor::LineCircleIntersection(const Point3d& lineStart, const Point3d& lineEnd,
                                                 const Point3d& circleCenter, double radius,
                                                 std::vector<Point3d>& intersections) const {
    intersections.clear();
    
    Vector3d lineVector(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
    Vector3d centerVector(lineStart.x - circleCenter.x, lineStart.y - circleCenter.y, lineStart.z - circleCenter.z);
    
    double a = DotProduct(lineVector, lineVector);
    double b = 2.0 * DotProduct(centerVector, lineVector);
    double c = DotProduct(centerVector, centerVector) - radius * radius;
    
    double discriminant = b * b - 4.0 * a * c;
    
    if (discriminant < 0.0) {
        return false; // No intersection
    }
    
    if (std::abs(a) < m_tolerance) {
        return false; // Degenerate line
    }
    
    double sqrtDiscriminant = std::sqrt(discriminant);
    double t1 = (-b - sqrtDiscriminant) / (2.0 * a);
    double t2 = (-b + sqrtDiscriminant) / (2.0 * a);
    
    // Check if intersections are within line segment
    if (t1 >= 0.0 && t1 <= 1.0) {
        intersections.push_back(Point3d(
            lineStart.x + t1 * lineVector.x,
            lineStart.y + t1 * lineVector.y,
            lineStart.z + t1 * lineVector.z
        ));
    }
    
    if (std::abs(discriminant) > m_tolerance && t2 >= 0.0 && t2 <= 1.0) {
        intersections.push_back(Point3d(
            lineStart.x + t2 * lineVector.x,
            lineStart.y + t2 * lineVector.y,
            lineStart.z + t2 * lineVector.z
        ));
    }
    
    return !intersections.empty();
}

bool DWGGeometryProcessor::BoundingBoxIntersection(const BoundingBox3D& box1, const BoundingBox3D& box2) const {
    return box1.Intersects(box2);
}

//=======================================================================================
// Helper Methods
//=======================================================================================

double DWGGeometryProcessor::DistancePointToPlane(const Point3d& point, const Point3d& planePoint, const Vector3d& planeNormal) const {
    Vector3d normalizedNormal = NormalizeVector(planeNormal);
    Vector3d pointVector(point.x - planePoint.x, point.y - planePoint.y, point.z - planePoint.z);
    return std::abs(DotProduct(pointVector, normalizedNormal));
}

} // namespace IModelExport
