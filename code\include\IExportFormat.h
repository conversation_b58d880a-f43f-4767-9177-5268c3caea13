#pragma once

#include "ExportTypes.h"
#include <memory>

namespace IModelExport {

// Forward declarations
class IModelDb;
class ExportContext;

//=======================================================================================
// Base Export Format Interface
//=======================================================================================

class IExportFormat {
public:
    virtual ~IExportFormat() = default;

    // Format identification
    virtual ExportFormat GetFormat() const = 0;
    virtual std::string GetFormatName() const = 0;
    virtual std::string GetFileExtension() const = 0;
    virtual std::vector<std::string> GetSupportedVersions() const = 0;

    // Capability queries
    virtual bool SupportsGeometry() const = 0;
    virtual bool SupportsMetadata() const = 0;
    virtual bool SupportsMaterials() const = 0;
    virtual bool SupportsTextures() const = 0;
    virtual bool SupportsAnimations() const = 0;
    virtual bool SupportsHierarchy() const = 0;

    // Export operations
    virtual ExportResult Export(
        const IModelDb& imodel,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) = 0;

    // Validation
    virtual bool ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const = 0;
    virtual bool CanExportElement(const ElementInfo& element) const = 0;

    // Configuration
    virtual void SetExportContext(std::shared_ptr<ExportContext> context) = 0;
    virtual std::shared_ptr<ExportContext> GetExportContext() const = 0;

protected:
    // Helper methods for derived classes
    virtual bool InitializeExport(const ExportOptions& options) = 0;
    virtual bool FinalizeExport() = 0;
    virtual void CleanupExport() = 0;
};

//=======================================================================================
// Format-Specific Interfaces
//=======================================================================================

class IDWGExporter : public IExportFormat {
public:
    virtual ~IDWGExporter() = default;

    // DWG-specific operations
    virtual bool SetDWGVersion(DWGExportOptions::DWGVersion version) = 0;
    virtual bool LoadTemplate(const std::string& templatePath) = 0;
    virtual bool CreateLayer(const std::string& layerName, const Color& color) = 0;
    virtual bool CreateBlock(const std::string& blockName) = 0;

    // Entity creation
    virtual bool AddLine(const Point3d& start, const Point3d& end, const std::string& layer = "") = 0;
    virtual bool AddCircle(const Point3d& center, double radius, const std::string& layer = "") = 0;
    virtual bool AddText(const Point3d& position, const std::string& text, double height, const std::string& layer = "") = 0;
    virtual bool AddPolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "") = 0;
};

class IIFCExporter : public IExportFormat {
public:
    virtual ~IIFCExporter() = default;

    // IFC-specific operations
    virtual bool SetIFCVersion(IFCExportOptions::IFCVersion version) = 0;
    virtual bool SetProjectInfo(const std::string& projectName, const std::string& description) = 0;
    virtual bool CreateSite(const std::string& siteName) = 0;
    virtual bool CreateBuilding(const std::string& buildingName, const std::string& siteId) = 0;
    virtual bool CreateBuildingStorey(const std::string& storeyName, const std::string& buildingId, double elevation) = 0;

    // IFC entity creation
    virtual bool AddWall(const std::string& wallId, const std::vector<Point3d>& profile, double height, const std::string& storeyId) = 0;
    virtual bool AddSlab(const std::string& slabId, const std::vector<Point3d>& boundary, double thickness, const std::string& storeyId) = 0;
    virtual bool AddColumn(const std::string& columnId, const Point3d& position, double height, double width, double depth, const std::string& storeyId) = 0;
    virtual bool AddBeam(const std::string& beamId, const Point3d& start, const Point3d& end, double width, double height, const std::string& storeyId) = 0;
};

class IDGNExporter : public IExportFormat {
public:
    virtual ~IDGNExporter() = default;

    // DGN-specific operations
    virtual bool SetDGNVersion(DGNExportOptions::DGNVersion version) = 0;
    virtual bool LoadSeedFile(const std::string& seedPath) = 0;
    virtual bool CreateLevel(const std::string& levelName, int levelNumber, const Color& color) = 0;
    virtual bool CreateCellDefinition(const std::string& cellName) = 0;

    // Element creation
    virtual bool AddLineElement(const Point3d& start, const Point3d& end, int level = 0) = 0;
    virtual bool AddArcElement(const Point3d& center, double radius, double startAngle, double sweepAngle, int level = 0) = 0;
    virtual bool AddTextElement(const Point3d& position, const std::string& text, double height, int level = 0) = 0;
    virtual bool AddShapeElement(const std::vector<Point3d>& points, int level = 0) = 0;
    virtual bool AddCellElement(const std::string& cellName, const Point3d& origin, const Transform3d& transform, int level = 0) = 0;
};

class IUSDExporter : public IExportFormat {
public:
    virtual ~IUSDExporter() = default;

    // USD-specific operations
    virtual bool SetUSDFormat(USDExportOptions::USDFormat format) = 0;
    virtual bool CreateStage(const std::string& stageName) = 0;
    virtual bool CreateXform(const std::string& primPath, const Transform3d& transform) = 0;
    virtual bool CreateMesh(const std::string& primPath, const std::vector<Point3d>& vertices, const std::vector<int>& indices) = 0;

    // USD prim creation
    virtual bool AddGeomMesh(const std::string& primPath, const std::vector<Point3d>& points, 
                           const std::vector<int>& faceVertexCounts, const std::vector<int>& faceVertexIndices) = 0;
    virtual bool AddMaterial(const std::string& materialPath, const Material& material) = 0;
    virtual bool AssignMaterial(const std::string& primPath, const std::string& materialPath) = 0;
    virtual bool AddVariant(const std::string& variantSetName, const std::string& variantName, const std::string& primPath) = 0;
};

//=======================================================================================
// Factory Interface
//=======================================================================================

class IExportFormatFactory {
public:
    virtual ~IExportFormatFactory() = default;

    // Factory methods
    virtual std::unique_ptr<IDWGExporter> CreateDWGExporter() = 0;
    virtual std::unique_ptr<IIFCExporter> CreateIFCExporter() = 0;
    virtual std::unique_ptr<IDGNExporter> CreateDGNExporter() = 0;
    virtual std::unique_ptr<IUSDExporter> CreateUSDExporter() = 0;

    // Generic factory method
    virtual std::unique_ptr<IExportFormat> CreateExporter(ExportFormat format) = 0;

    // Registration
    virtual void RegisterExporter(ExportFormat format, std::function<std::unique_ptr<IExportFormat>()> factory) = 0;
    virtual bool IsFormatSupported(ExportFormat format) const = 0;
    virtual std::vector<ExportFormat> GetSupportedFormats() const = 0;
};

//=======================================================================================
// Singleton Factory Access
//=======================================================================================

class ExportFormatFactory {
public:
    static IExportFormatFactory& Instance();
    static void SetInstance(std::unique_ptr<IExportFormatFactory> factory);

private:
    static std::unique_ptr<IExportFormatFactory> s_instance;
};

} // namespace IModelExport
