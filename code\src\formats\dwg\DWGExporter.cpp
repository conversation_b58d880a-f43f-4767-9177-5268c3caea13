#include "DWGExporter.h"
#include "../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbxutil.h>
#endif

#include <filesystem>
#include <fstream>
#include <chrono>

namespace IModelExport {

//=======================================================================================
// DWGExporter Implementation
//=======================================================================================

DWGExporter::DWGExporter() 
#ifdef REALDWG_AVAILABLE
    : m_database(nullptr)
    , m_hostApp(nullptr)
    , m_layerTable(nullptr)
    , m_blockTable(nullptr)
    , m_textStyleTable(nullptr)
    , m_linetypeTable(nullptr)
#endif
{
    m_state.initialized = false;
    m_state.finalized = false;
    m_state.entitiesCreated = 0;
    m_state.layersCreated = 0;
    m_state.blocksCreated = 0;
    m_state.version = DWGExportOptions::DWGVersion::R2021;
}

DWGExporter::~DWGExporter() {
    CleanupExport();
}

//=======================================================================================
// IExportFormat Interface Implementation
//=======================================================================================

std::vector<std::string> DWGExporter::GetSupportedVersions() const {
    return {"R2018", "R2021", "R2024"};
}

ExportResult DWGExporter::Export(
    const IModelDb& imodel,
    const ExportOptions& options,
    ProgressCallback progressCallback) {
    
    ExportResult result;
    auto startTime = std::chrono::steady_clock::now();
    
    try {
        // Validate and cast options
        const DWGExportOptions* dwgOptions = dynamic_cast<const DWGExportOptions*>(&options);
        if (!dwgOptions) {
            result.status = ExportStatus::Error;
            result.errors.push_back("Invalid options type for DWG export");
            return result;
        }

        // Initialize export
        if (!InitializeExport(*dwgOptions)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 10.0, "Initializing DWG export")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Process iModel elements
        if (!ProcessIModelElements(imodel, progressCallback)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            result.warnings = m_state.warnings;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 90.0, "Finalizing DWG file")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Finalize export
        if (!FinalizeExport()) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Complete progress
        if (progressCallback) {
            UpdateProgress(progressCallback, 100.0, "Export completed");
        }

        // Set result
        result.status = m_state.warnings.empty() ? ExportStatus::Success : ExportStatus::Warning;
        result.outputFile = m_state.outputPath;
        result.warnings = m_state.warnings;
        result.errors = m_state.errors;
        result.exportedElements = m_state.entitiesCreated;

    } catch (const std::exception& e) {
        result.status = ExportStatus::Error;
        result.errors.push_back("Exception during export: " + std::string(e.what()));
        LogError(result.errors.back());
    }

    auto endTime = std::chrono::steady_clock::now();
    result.exportTime = std::chrono::duration<double>(endTime - startTime).count();

    CleanupExport();
    return result;
}

bool DWGExporter::ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const {
    const DWGExportOptions* dwgOptions = dynamic_cast<const DWGExportOptions*>(&options);
    if (!dwgOptions) {
        errors.push_back("Options must be of type DWGExportOptions");
        return false;
    }

    // Validate output path
    if (dwgOptions->outputPath.empty()) {
        errors.push_back("Output path cannot be empty");
        return false;
    }

    // Validate template file if specified
    if (!dwgOptions->templateFile.empty() && !FileExists(dwgOptions->templateFile)) {
        errors.push_back("Template file does not exist: " + dwgOptions->templateFile);
        return false;
    }

    // Validate geometry tolerance
    if (dwgOptions->geometryTolerance <= 0.0) {
        errors.push_back("Geometry tolerance must be positive");
        return false;
    }

    return true;
}

bool DWGExporter::CanExportElement(const ElementInfo& element) const {
    // Check if element type is supported
    switch (element.type) {
        case ElementType::GeometricElement:
        case ElementType::SpatialElement:
        case ElementType::PhysicalElement:
            return true;
        case ElementType::FunctionalElement:
        case ElementType::InformationElement:
            return false; // These don't have geometric representation
        default:
            return false;
    }
}

void DWGExporter::SetExportContext(std::shared_ptr<ExportContext> context) {
    m_context = context;
}

std::shared_ptr<ExportContext> DWGExporter::GetExportContext() const {
    return m_context;
}

//=======================================================================================
// IDWGExporter Interface Implementation
//=======================================================================================

bool DWGExporter::SetDWGVersion(DWGExportOptions::DWGVersion version) {
    m_state.version = version;
    return true;
}

bool DWGExporter::LoadTemplate(const std::string& templatePath) {
    if (!FileExists(templatePath)) {
        LogError("Template file does not exist: " + templatePath);
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (m_database) {
        // Load template into existing database
        Acad::ErrorStatus es = m_database->readDwgFile(templatePath.c_str());
        if (es != Acad::eOk) {
            LogError("Failed to load template file: " + templatePath);
            return false;
        }
        return true;
    }
#endif

    LogWarning("RealDWG not available - template loading skipped");
    return false;
}

bool DWGExporter::CreateLayer(const std::string& layerName, const Color& color) {
#ifdef REALDWG_AVAILABLE
    if (!m_database || !m_layerTable) {
        LogError("Database not initialized");
        return false;
    }

    // Check if layer already exists
    if (m_layerTable->has(layerName.c_str())) {
        return true; // Layer already exists
    }

    // Create new layer
    AcDbLayerTableRecord* layerRecord = new AcDbLayerTableRecord();
    layerRecord->setName(layerName.c_str());
    layerRecord->setColor(ToAcCmColor(color));

    AcDbObjectId layerId;
    Acad::ErrorStatus es = m_layerTable->add(layerId, layerRecord);
    layerRecord->close();

    if (es != Acad::eOk) {
        LogError("Failed to create layer: " + layerName);
        return false;
    }

    m_layerCache[layerName] = layerId.asOldId();
    m_state.layersCreated++;
    return true;
#else
    LogWarning("RealDWG not available - layer creation skipped");
    return false;
#endif
}

bool DWGExporter::CreateBlock(const std::string& blockName) {
#ifdef REALDWG_AVAILABLE
    if (!m_database || !m_blockTable) {
        LogError("Database not initialized");
        return false;
    }

    // Check if block already exists
    if (m_blockTable->has(blockName.c_str())) {
        return true; // Block already exists
    }

    // Create new block
    AcDbBlockTableRecord* blockRecord = new AcDbBlockTableRecord();
    blockRecord->setName(blockName.c_str());

    AcDbObjectId blockId;
    Acad::ErrorStatus es = m_blockTable->add(blockId, blockRecord);
    blockRecord->close();

    if (es != Acad::eOk) {
        LogError("Failed to create block: " + blockName);
        return false;
    }

    m_blockCache[blockName] = blockId.asOldId();
    m_state.blocksCreated++;
    return true;
#else
    LogWarning("RealDWG not available - block creation skipped");
    return false;
#endif
}

//=======================================================================================
// Entity Creation Methods
//=======================================================================================

bool DWGExporter::AddLine(const Point3d& start, const Point3d& end, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform points
    Point3d transformedStart = TransformPoint(start);
    Point3d transformedEnd = TransformPoint(end);

    // Create line entity
    AcDbLine* line = new AcDbLine(
        ToAcGePoint3d(transformedStart),
        ToAcGePoint3d(transformedEnd)
    );

    // Set properties
    if (!SetEntityProperties(line, layer)) {
        delete line;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(line);
    if (entityId.isNull()) {
        delete line;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - line creation skipped");
    return false;
#endif
}

bool DWGExporter::AddCircle(const Point3d& center, double radius, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform geometry
    Point3d transformedCenter = TransformPoint(center);
    double transformedRadius = TransformLength(radius);

    // Create circle entity
    AcDbCircle* circle = new AcDbCircle(
        ToAcGePoint3d(transformedCenter),
        AcGeVector3d::kZAxis,
        transformedRadius
    );

    // Set properties
    if (!SetEntityProperties(circle, layer)) {
        delete circle;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(circle);
    if (entityId.isNull()) {
        delete circle;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - circle creation skipped");
    return false;
#endif
}

bool DWGExporter::AddText(const Point3d& position, const std::string& text, double height, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform geometry
    Point3d transformedPosition = TransformPoint(position);
    double transformedHeight = TransformLength(height);

    // Create text entity
    AcDbText* textEntity = new AcDbText(
        ToAcGePoint3d(transformedPosition),
        text.c_str(),
        transformedHeight
    );

    // Set properties
    if (!SetEntityProperties(textEntity, layer)) {
        delete textEntity;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(textEntity);
    if (entityId.isNull()) {
        delete textEntity;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - text creation skipped");
    return false;
#endif
}

bool DWGExporter::AddPolyline(const std::vector<Point3d>& points, bool closed, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    if (points.size() < 2) {
        LogError("Polyline must have at least 2 points");
        return false;
    }

    // Create polyline entity
    AcDbPolyline* polyline = new AcDbPolyline();
    
    // Add vertices
    for (size_t i = 0; i < points.size(); ++i) {
        Point3d transformedPoint = TransformPoint(points[i]);
        AcGePoint2d point2d(transformedPoint.x, transformedPoint.y);
        polyline->addVertexAt(i, point2d);
    }

    // Set closed state
    polyline->setClosed(closed);

    // Set properties
    if (!SetEntityProperties(polyline, layer)) {
        delete polyline;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(polyline);
    if (entityId.isNull()) {
        delete polyline;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - polyline creation skipped");
    return false;
#endif
}

//=======================================================================================
// Protected Methods
//=======================================================================================

bool DWGExporter::InitializeExport(const ExportOptions& options) {
    const DWGExportOptions& dwgOptions = static_cast<const DWGExportOptions&>(options);
    
    m_state.outputPath = dwgOptions.outputPath;
    m_state.version = dwgOptions.version;
    
    // Create output directory if needed
    if (!CreateDirectoryIfNeeded(std::filesystem::path(m_state.outputPath).parent_path().string())) {
        LogError("Failed to create output directory");
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (!InitializeRealDWG()) {
        LogError("Failed to initialize RealDWG");
        return false;
    }

    // Load template if specified
    if (!dwgOptions.templateFile.empty()) {
        if (!LoadTemplate(dwgOptions.templateFile)) {
            LogWarning("Failed to load template, using default");
        }
    }

    // Create default layers, text styles, etc.
    if (!CreateDefaultLayers() || !CreateDefaultTextStyles() || !CreateDefaultLinetypes()) {
        LogError("Failed to create default drawing setup");
        return false;
    }
#endif

    m_state.initialized = true;
    return true;
}

bool DWGExporter::FinalizeExport() {
    if (!m_state.initialized) {
        LogError("Export not initialized");
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (m_database) {
        // Save database to file
        Acad::ErrorStatus es = m_database->saveAs(m_state.outputPath.c_str());
        if (es != Acad::eOk) {
            LogError("Failed to save DWG file: " + m_state.outputPath);
            return false;
        }
    }
#endif

    m_state.finalized = true;
    return true;
}

void DWGExporter::CleanupExport() {
#ifdef REALDWG_AVAILABLE
    CleanupRealDWG();
#endif
    
    m_state.initialized = false;
    m_state.finalized = false;
}

} // namespace IModelExport
