#include "DWGExporter.h"
#include "../../core/ExportContext.h"
#include "entities/DWGEntityProcessor.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbxutil.h>
#endif

#include <filesystem>
#include <fstream>
#include <chrono>

namespace IModelExport {

//=======================================================================================
// DWGExporter Implementation
//=======================================================================================

DWGExporter::DWGExporter() 
#ifdef REALDWG_AVAILABLE
    : m_database(nullptr)
    , m_hostApp(nullptr)
    , m_layerTable(nullptr)
    , m_blockTable(nullptr)
    , m_textStyleTable(nullptr)
    , m_linetypeTable(nullptr)
#endif
{
    m_state.initialized = false;
    m_state.finalized = false;
    m_state.entitiesCreated = 0;
    m_state.layersCreated = 0;
    m_state.blocksCreated = 0;
    m_state.version = DWGExportOptions::DWGVersion::R2021;
}

DWGExporter::~DWGExporter() {
    CleanupExport();
}

//=======================================================================================
// IExportFormat Interface Implementation
//=======================================================================================

std::vector<std::string> DWGExporter::GetSupportedVersions() const {
    return {"R2018", "R2021", "R2024"};
}

ExportResult DWGExporter::Export(
    const IModelDb& imodel,
    const ExportOptions& options,
    ProgressCallback progressCallback) {
    
    ExportResult result;
    auto startTime = std::chrono::steady_clock::now();
    
    try {
        // Validate and cast options
        const DWGExportOptions* dwgOptions = dynamic_cast<const DWGExportOptions*>(&options);
        if (!dwgOptions) {
            result.status = ExportStatus::Error;
            result.errors.push_back("Invalid options type for DWG export");
            return result;
        }

        // Initialize export
        if (!InitializeExport(*dwgOptions)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 10.0, "Initializing DWG export")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Process iModel elements
        if (!ProcessIModelElements(imodel, progressCallback)) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            result.warnings = m_state.warnings;
            return result;
        }

        // Update progress
        if (progressCallback && !UpdateProgress(progressCallback, 90.0, "Finalizing DWG file")) {
            result.status = ExportStatus::Cancelled;
            return result;
        }

        // Finalize export
        if (!FinalizeExport()) {
            result.status = ExportStatus::Error;
            result.errors = m_state.errors;
            return result;
        }

        // Complete progress
        if (progressCallback) {
            UpdateProgress(progressCallback, 100.0, "Export completed");
        }

        // Set result
        result.status = m_state.warnings.empty() ? ExportStatus::Success : ExportStatus::Warning;
        result.outputFile = m_state.outputPath;
        result.warnings = m_state.warnings;
        result.errors = m_state.errors;
        result.exportedElements = m_state.entitiesCreated;

    } catch (const std::exception& e) {
        result.status = ExportStatus::Error;
        result.errors.push_back("Exception during export: " + std::string(e.what()));
        LogError(result.errors.back());
    }

    auto endTime = std::chrono::steady_clock::now();
    result.exportTime = std::chrono::duration<double>(endTime - startTime).count();

    CleanupExport();
    return result;
}

bool DWGExporter::ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const {
    const DWGExportOptions* dwgOptions = dynamic_cast<const DWGExportOptions*>(&options);
    if (!dwgOptions) {
        errors.push_back("Options must be of type DWGExportOptions");
        return false;
    }

    // Validate output path
    if (dwgOptions->outputPath.empty()) {
        errors.push_back("Output path cannot be empty");
        return false;
    }

    // Validate template file if specified
    if (!dwgOptions->templateFile.empty() && !FileExists(dwgOptions->templateFile)) {
        errors.push_back("Template file does not exist: " + dwgOptions->templateFile);
        return false;
    }

    // Validate geometry tolerance
    if (dwgOptions->geometryTolerance <= 0.0) {
        errors.push_back("Geometry tolerance must be positive");
        return false;
    }

    return true;
}

bool DWGExporter::CanExportElement(const ElementInfo& element) const {
    // Check if element type is supported
    switch (element.type) {
        case ElementType::GeometricElement:
        case ElementType::SpatialElement:
        case ElementType::PhysicalElement:
            return true;
        case ElementType::FunctionalElement:
        case ElementType::InformationElement:
            return false; // These don't have geometric representation
        default:
            return false;
    }
}

void DWGExporter::SetExportContext(std::shared_ptr<ExportContext> context) {
    m_context = context;
}

std::shared_ptr<ExportContext> DWGExporter::GetExportContext() const {
    return m_context;
}

//=======================================================================================
// IDWGExporter Interface Implementation
//=======================================================================================

bool DWGExporter::SetDWGVersion(DWGExportOptions::DWGVersion version) {
    m_state.version = version;
    return true;
}

bool DWGExporter::LoadTemplate(const std::string& templatePath) {
    if (!FileExists(templatePath)) {
        LogError("Template file does not exist: " + templatePath);
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (m_database) {
        // Load template into existing database
        Acad::ErrorStatus es = m_database->readDwgFile(templatePath.c_str());
        if (es != Acad::eOk) {
            LogError("Failed to load template file: " + templatePath);
            return false;
        }
        return true;
    }
#endif

    LogWarning("RealDWG not available - template loading skipped");
    return false;
}

bool DWGExporter::CreateLayer(const std::string& layerName, const Color& color) {
#ifdef REALDWG_AVAILABLE
    if (!m_database || !m_layerTable) {
        LogError("Database not initialized");
        return false;
    }

    // Check if layer already exists
    if (m_layerTable->has(layerName.c_str())) {
        return true; // Layer already exists
    }

    // Create new layer
    AcDbLayerTableRecord* layerRecord = new AcDbLayerTableRecord();
    layerRecord->setName(layerName.c_str());
    layerRecord->setColor(ToAcCmColor(color));

    AcDbObjectId layerId;
    Acad::ErrorStatus es = m_layerTable->add(layerId, layerRecord);
    layerRecord->close();

    if (es != Acad::eOk) {
        LogError("Failed to create layer: " + layerName);
        return false;
    }

    m_layerCache[layerName] = layerId.asOldId();
    m_state.layersCreated++;
    return true;
#else
    LogWarning("RealDWG not available - layer creation skipped");
    return false;
#endif
}

bool DWGExporter::CreateBlock(const std::string& blockName) {
#ifdef REALDWG_AVAILABLE
    if (!m_database || !m_blockTable) {
        LogError("Database not initialized");
        return false;
    }

    // Check if block already exists
    if (m_blockTable->has(blockName.c_str())) {
        return true; // Block already exists
    }

    // Create new block
    AcDbBlockTableRecord* blockRecord = new AcDbBlockTableRecord();
    blockRecord->setName(blockName.c_str());

    AcDbObjectId blockId;
    Acad::ErrorStatus es = m_blockTable->add(blockId, blockRecord);
    blockRecord->close();

    if (es != Acad::eOk) {
        LogError("Failed to create block: " + blockName);
        return false;
    }

    m_blockCache[blockName] = blockId.asOldId();
    m_state.blocksCreated++;
    return true;
#else
    LogWarning("RealDWG not available - block creation skipped");
    return false;
#endif
}

//=======================================================================================
// Entity Creation Methods
//=======================================================================================

bool DWGExporter::AddLine(const Point3d& start, const Point3d& end, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform points
    Point3d transformedStart = TransformPoint(start);
    Point3d transformedEnd = TransformPoint(end);

    // Create line entity
    AcDbLine* line = new AcDbLine(
        ToAcGePoint3d(transformedStart),
        ToAcGePoint3d(transformedEnd)
    );

    // Set properties
    if (!SetEntityProperties(line, layer)) {
        delete line;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(line);
    if (entityId.isNull()) {
        delete line;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - line creation skipped");
    return false;
#endif
}

bool DWGExporter::AddCircle(const Point3d& center, double radius, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform geometry
    Point3d transformedCenter = TransformPoint(center);
    double transformedRadius = TransformLength(radius);

    // Create circle entity
    AcDbCircle* circle = new AcDbCircle(
        ToAcGePoint3d(transformedCenter),
        AcGeVector3d::kZAxis,
        transformedRadius
    );

    // Set properties
    if (!SetEntityProperties(circle, layer)) {
        delete circle;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(circle);
    if (entityId.isNull()) {
        delete circle;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - circle creation skipped");
    return false;
#endif
}

bool DWGExporter::AddText(const Point3d& position, const std::string& text, double height, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    // Transform geometry
    Point3d transformedPosition = TransformPoint(position);
    double transformedHeight = TransformLength(height);

    // Create text entity
    AcDbText* textEntity = new AcDbText(
        ToAcGePoint3d(transformedPosition),
        text.c_str(),
        transformedHeight
    );

    // Set properties
    if (!SetEntityProperties(textEntity, layer)) {
        delete textEntity;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(textEntity);
    if (entityId.isNull()) {
        delete textEntity;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - text creation skipped");
    return false;
#endif
}

bool DWGExporter::AddPolyline(const std::vector<Point3d>& points, bool closed, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    if (!m_database) {
        LogError("Database not initialized");
        return false;
    }

    if (points.size() < 2) {
        LogError("Polyline must have at least 2 points");
        return false;
    }

    // Create polyline entity
    AcDbPolyline* polyline = new AcDbPolyline();
    
    // Add vertices
    for (size_t i = 0; i < points.size(); ++i) {
        Point3d transformedPoint = TransformPoint(points[i]);
        AcGePoint2d point2d(transformedPoint.x, transformedPoint.y);
        polyline->addVertexAt(i, point2d);
    }

    // Set closed state
    polyline->setClosed(closed);

    // Set properties
    if (!SetEntityProperties(polyline, layer)) {
        delete polyline;
        return false;
    }

    // Add to model space
    AcDbObjectId entityId = AddEntityToModelSpace(polyline);
    if (entityId.isNull()) {
        delete polyline;
        return false;
    }

    m_state.entitiesCreated++;
    return true;
#else
    LogWarning("RealDWG not available - polyline creation skipped");
    return false;
#endif
}

//=======================================================================================
// Protected Methods
//=======================================================================================

bool DWGExporter::InitializeExport(const ExportOptions& options) {
    const DWGExportOptions& dwgOptions = static_cast<const DWGExportOptions&>(options);

    m_state.outputPath = dwgOptions.outputPath;
    m_state.version = dwgOptions.version;

    // Create output directory if needed
    if (!CreateDirectoryIfNeeded(std::filesystem::path(m_state.outputPath).parent_path().string())) {
        LogError("Failed to create output directory");
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (!InitializeRealDWG()) {
        LogError("Failed to initialize RealDWG");
        return false;
    }

    // Load template if specified
    if (!dwgOptions.templateFile.empty()) {
        if (!LoadTemplate(dwgOptions.templateFile)) {
            LogWarning("Failed to load template, using default");
        }
    }

    // Create default layers, text styles, etc.
    if (!CreateDefaultLayers() || !CreateDefaultTextStyles() || !CreateDefaultLinetypes()) {
        LogError("Failed to create default drawing setup");
        return false;
    }
#endif

    m_state.initialized = true;
    return true;
}

//=======================================================================================
// RealDWG Integration Implementation
//=======================================================================================

#ifdef REALDWG_AVAILABLE
bool DWGExporter::InitializeRealDWG() {
    try {
        // Create host application services
        m_hostApp = new DWGHostAppServices();
        acdbSetHostApplicationServices(m_hostApp);

        // Create new database
        m_database = new AcDbDatabase(false, true);
        if (!m_database) {
            LogError("Failed to create AcDbDatabase");
            return false;
        }

        // Set database version
        AcDb::AcDbDwgVersion dwgVersion;
        switch (m_state.version) {
            case DWGExportOptions::DWGVersion::R2018:
                dwgVersion = AcDb::kDHL_1800;
                break;
            case DWGExportOptions::DWGVersion::R2021:
                dwgVersion = AcDb::kDHL_1021;
                break;
            case DWGExportOptions::DWGVersion::R2024:
                dwgVersion = AcDb::kDHL_1024;
                break;
            default:
                dwgVersion = AcDb::kDHL_1021;
        }
        m_database->setDwgVersion(dwgVersion);

        // Get symbol tables
        Acad::ErrorStatus es;
        es = m_database->getLayerTable(m_layerTable, AcDb::kForWrite);
        if (es != Acad::eOk) {
            LogError("Failed to get layer table");
            return false;
        }

        es = m_database->getBlockTable(m_blockTable, AcDb::kForWrite);
        if (es != Acad::eOk) {
            LogError("Failed to get block table");
            return false;
        }

        es = m_database->getTextStyleTable(m_textStyleTable, AcDb::kForWrite);
        if (es != Acad::eOk) {
            LogError("Failed to get text style table");
            return false;
        }

        es = m_database->getLinetypeTable(m_linetypeTable, AcDb::kForWrite);
        if (es != Acad::eOk) {
            LogError("Failed to get linetype table");
            return false;
        }

        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception in InitializeRealDWG: " + std::string(e.what()));
        return false;
    }
}

void DWGExporter::CleanupRealDWG() {
    if (m_linetypeTable) {
        m_linetypeTable->close();
        m_linetypeTable = nullptr;
    }

    if (m_textStyleTable) {
        m_textStyleTable->close();
        m_textStyleTable = nullptr;
    }

    if (m_blockTable) {
        m_blockTable->close();
        m_blockTable = nullptr;
    }

    if (m_layerTable) {
        m_layerTable->close();
        m_layerTable = nullptr;
    }

    if (m_database) {
        delete m_database;
        m_database = nullptr;
    }

    if (m_hostApp) {
        delete m_hostApp;
        m_hostApp = nullptr;
    }
}

bool DWGExporter::CreateDefaultLayers() {
    if (!m_layerTable) {
        return false;
    }

    // Create default layer "0"
    if (!m_layerTable->has(L"0")) {
        AcDbLayerTableRecord* layer0 = new AcDbLayerTableRecord();
        layer0->setName(L"0");
        layer0->setColor(AcCmColor(AcCmEntityColor::kACIWhite));

        AcDbObjectId layerId;
        Acad::ErrorStatus es = m_layerTable->add(layerId, layer0);
        layer0->close();

        if (es != Acad::eOk) {
            LogError("Failed to create default layer 0");
            return false;
        }

        m_currentLayerId = layerId;
        m_layerCache["0"] = layerId.asOldId();
    }

    // Create additional default layers
    std::vector<std::pair<std::string, Color>> defaultLayers = {
        {"Geometry", Color(1.0f, 1.0f, 1.0f, 1.0f)},
        {"Text", Color(1.0f, 1.0f, 0.0f, 1.0f)},
        {"Dimensions", Color(0.0f, 1.0f, 0.0f, 1.0f)},
        {"Hidden", Color(0.5f, 0.5f, 0.5f, 1.0f)}
    };

    for (const auto& layerInfo : defaultLayers) {
        if (!CreateLayer(layerInfo.first, layerInfo.second)) {
            LogWarning("Failed to create default layer: " + layerInfo.first);
        }
    }

    m_state.layersCreated = defaultLayers.size() + 1;
    return true;
}

bool DWGExporter::CreateDefaultTextStyles() {
    if (!m_textStyleTable) {
        return false;
    }

    // Create standard text style
    if (!m_textStyleTable->has(L"Standard")) {
        AcDbTextStyleTableRecord* textStyle = new AcDbTextStyleTableRecord();
        textStyle->setName(L"Standard");
        textStyle->setFileName(L"txt.shx");
        textStyle->setTextSize(2.5);

        AcDbObjectId styleId;
        Acad::ErrorStatus es = m_textStyleTable->add(styleId, textStyle);
        textStyle->close();

        if (es != Acad::eOk) {
            LogError("Failed to create standard text style");
            return false;
        }

        m_currentTextStyleId = styleId;
    }

    return true;
}

bool DWGExporter::CreateDefaultLinetypes() {
    if (!m_linetypeTable) {
        return false;
    }

    // Create continuous linetype
    if (!m_linetypeTable->has(L"Continuous")) {
        AcDbLinetypeTableRecord* linetype = new AcDbLinetypeTableRecord();
        linetype->setName(L"Continuous");
        linetype->setComments(L"Solid line");

        AcDbObjectId linetypeId;
        Acad::ErrorStatus es = m_linetypeTable->add(linetypeId, linetype);
        linetype->close();

        if (es != Acad::eOk) {
            LogError("Failed to create continuous linetype");
            return false;
        }

        m_currentLinetypeId = linetypeId;
    }

    return true;
}
#endif

bool DWGExporter::FinalizeExport() {
    if (!m_state.initialized) {
        LogError("Export not initialized");
        return false;
    }

#ifdef REALDWG_AVAILABLE
    if (m_database) {
        // Save database to file
        Acad::ErrorStatus es = m_database->saveAs(m_state.outputPath.c_str());
        if (es != Acad::eOk) {
            LogError("Failed to save DWG file: " + m_state.outputPath);
            return false;
        }
    }
#endif

    m_state.finalized = true;
    return true;
}

void DWGExporter::CleanupExport() {
#ifdef REALDWG_AVAILABLE
    CleanupRealDWG();
#endif
    
    m_state.initialized = false;
    m_state.finalized = false;
}

//=======================================================================================
// Conversion Helper Methods
//=======================================================================================

#ifdef REALDWG_AVAILABLE
AcGePoint3d DWGExporter::ToAcGePoint3d(const Point3d& point) const {
    Point3d transformed = TransformPoint(point);
    return AcGePoint3d(transformed.x, transformed.y, transformed.z);
}

AcGeVector3d DWGExporter::ToAcGeVector3d(const Vector3d& vector) const {
    Vector3d transformed = TransformVector(vector);
    return AcGeVector3d(transformed.x, transformed.y, transformed.z);
}

AcCmColor DWGExporter::ToAcCmColor(const Color& color) const {
    AcCmColor acColor;
    acColor.setRGB(
        static_cast<Adesk::UInt8>(color.r * 255),
        static_cast<Adesk::UInt8>(color.g * 255),
        static_cast<Adesk::UInt8>(color.b * 255)
    );
    return acColor;
}

AcDbObjectId DWGExporter::AddEntityToModelSpace(AcDbEntity* entity) {
    if (!entity || !m_database) {
        return AcDbObjectId::kNull;
    }

    AcDbBlockTable* blockTable;
    Acad::ErrorStatus es = m_database->getBlockTable(blockTable, AcDb::kForRead);
    if (es != Acad::eOk) {
        return AcDbObjectId::kNull;
    }

    AcDbBlockTableRecord* modelSpace;
    es = blockTable->getAt(ACDB_MODEL_SPACE, modelSpace, AcDb::kForWrite);
    blockTable->close();

    if (es != Acad::eOk) {
        return AcDbObjectId::kNull;
    }

    AcDbObjectId entityId;
    es = modelSpace->appendAcDbEntity(entityId, entity);
    modelSpace->close();
    entity->close();

    if (es != Acad::eOk) {
        return AcDbObjectId::kNull;
    }

    return entityId;
}

bool DWGExporter::SetEntityProperties(AcDbEntity* entity, const std::string& layer) {
    if (!entity) {
        return false;
    }

    // Set layer
    if (!layer.empty()) {
        AcDbObjectId layerId = GetOrCreateLayer(layer);
        if (!layerId.isNull()) {
            entity->setLayer(layerId);
        }
    } else if (!m_currentLayerId.isNull()) {
        entity->setLayer(m_currentLayerId);
    }

    // Set linetype
    if (!m_currentLinetypeId.isNull()) {
        entity->setLinetype(m_currentLinetypeId);
    }

    return true;
}

AcDbObjectId DWGExporter::GetOrCreateLayer(const std::string& layerName, const Color& color) {
    // Check cache first
    auto it = m_layerCache.find(layerName);
    if (it != m_layerCache.end()) {
        return AcDbObjectId(it->second);
    }

    if (!m_layerTable) {
        return AcDbObjectId::kNull;
    }

    // Check if layer exists
    AcString acLayerName(layerName.c_str());
    if (m_layerTable->has(acLayerName)) {
        AcDbObjectId layerId;
        if (m_layerTable->getAt(acLayerName, layerId) == Acad::eOk) {
            m_layerCache[layerName] = layerId.asOldId();
            return layerId;
        }
    }

    // Create new layer
    AcDbLayerTableRecord* layerRecord = new AcDbLayerTableRecord();
    layerRecord->setName(acLayerName);
    layerRecord->setColor(ToAcCmColor(color));

    AcDbObjectId layerId;
    Acad::ErrorStatus es = m_layerTable->add(layerId, layerRecord);
    layerRecord->close();

    if (es == Acad::eOk) {
        m_layerCache[layerName] = layerId.asOldId();
        m_state.layersCreated++;
        return layerId;
    }

    return AcDbObjectId::kNull;
}
#endif

//=======================================================================================
// iModel Element Processing
//=======================================================================================

bool DWGExporter::ProcessIModelElements(const IModelDb& imodel, ProgressCallback progressCallback) {
    if (!m_context) {
        LogError("Export context not set");
        return false;
    }

    // Initialize entity processors
    if (!InitializeEntityProcessors()) {
        LogError("Failed to initialize entity processors");
        return false;
    }

    try {
        // Get elements from iModel (placeholder implementation)
        std::vector<ElementInfo> elements; // = imodel.GetAllElements();

        if (elements.empty()) {
            LogWarning("No elements found in iModel");
            return true;
        }

        m_context->SetTotalElements(elements.size());

        // Group elements by type for efficient processing
        std::unordered_map<ElementType, std::vector<ElementInfo>> elementsByType;
        for (const auto& element : elements) {
            if (CanExportElement(element)) {
                elementsByType[element.type].push_back(element);
            }
        }

        size_t processedCount = 0;
        size_t totalElements = 0;
        for (const auto& [type, typeElements] : elementsByType) {
            totalElements += typeElements.size();
        }

        // Process elements by type
        for (const auto& [elementType, typeElements] : elementsByType) {
            LogInfo("Processing " + std::to_string(typeElements.size()) + " elements of type " +
                    std::to_string(static_cast<int>(elementType)));

            for (const auto& element : typeElements) {
                // Check for cancellation
                if (progressCallback && CheckCancellation(progressCallback)) {
                    LogInfo("Export cancelled by user");
                    return false;
                }

                // Process element using appropriate processor
                if (ProcessElementByType(element)) {
                    processedCount++;
                    m_context->IncrementProcessedCount();
                } else {
                    m_context->IncrementErrorCount();
                    LogWarning("Failed to process element: " + element.id);
                }

                // Update progress
                if (progressCallback) {
                    double percentage = (static_cast<double>(processedCount) / totalElements) * 80.0 + 10.0;
                    std::string operation = "Processing element " + std::to_string(processedCount + 1) +
                                          " of " + std::to_string(totalElements);
                    if (!UpdateProgress(progressCallback, percentage, operation)) {
                        return false;
                    }
                }
            }
        }

        LogInfo("Processed " + std::to_string(processedCount) + " elements");
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception in ProcessIModelElements: " + std::string(e.what()));
        return false;
    }
}

bool DWGExporter::InitializeEntityProcessors() {
    try {
        // Initialize processors for different entity types
        m_entityProcessors.clear();

        // Create line processor
        auto lineProcessor = std::make_unique<DWGLineProcessor>(this);
        m_entityProcessors["Line"] = std::move(lineProcessor);

        // Create circle processor
        auto circleProcessor = std::make_unique<DWGCircleProcessor>(this);
        m_entityProcessors["Circle"] = std::move(circleProcessor);

        // Create text processor
        auto textProcessor = std::make_unique<DWGTextProcessor>(this);
        m_entityProcessors["Text"] = std::move(textProcessor);

        LogInfo("Initialized " + std::to_string(m_entityProcessors.size()) + " entity processors");
        return true;
    }
    catch (const std::exception& e) {
        LogError("Exception initializing entity processors: " + std::string(e.what()));
        return false;
    }
}

bool DWGExporter::ProcessElementByType(const ElementInfo& element) {
    if (!CanExportElement(element)) {
        if (m_context) {
            m_context->IncrementSkippedCount();
        }
        return true; // Skip but don't fail
    }

    try {
        // Determine entity type from element
        std::string entityType = DetermineEntityType(element);

        // Find appropriate processor
        auto it = m_entityProcessors.find(entityType);
        if (it != m_entityProcessors.end()) {
            auto status = it->second->ProcessEntity(element);

            switch (status) {
                case DWGProcessingStatus::Success:
                    return true;
                case DWGProcessingStatus::Skipped:
                    if (m_context) {
                        m_context->IncrementSkippedCount();
                    }
                    return true;
                case DWGProcessingStatus::Failed:
                case DWGProcessingStatus::InvalidGeometry:
                case DWGProcessingStatus::ValidationError:
                case DWGProcessingStatus::ConversionError:
                case DWGProcessingStatus::MemoryError:
                    LogError("Failed to process element " + element.id + " with status: " +
                            std::to_string(static_cast<int>(status)));
                    return false;
                case DWGProcessingStatus::UnsupportedEntity:
                    // Fall through to legacy processing
                    break;
            }
        }

        // Fall back to legacy processing for unsupported entity types
        return ProcessElementLegacy(element);
    }
    catch (const std::exception& e) {
        LogError("Exception processing element " + element.id + ": " + e.what());
        return false;
    }
}

std::string DWGExporter::DetermineEntityType(const ElementInfo& element) const {
    // This is a placeholder implementation
    // In a real implementation, this would analyze the element's geometry
    // and determine the appropriate DWG entity type

    switch (element.type) {
        case ElementType::GeometricElement:
            // For now, default to line for geometric elements
            return "Line";
        case ElementType::SpatialElement:
            return "Circle"; // Placeholder
        case ElementType::PhysicalElement:
            return "Text"; // Placeholder
        default:
            return "Unknown";
    }
}

bool DWGExporter::ProcessElementLegacy(const ElementInfo& element) {
    // Legacy processing method for backward compatibility
    try {
        // Process based on element type
        switch (element.type) {
            case ElementType::GeometricElement:
                return ProcessGeometricElement(element);
            case ElementType::SpatialElement:
                return ProcessSpatialElement(element);
            case ElementType::PhysicalElement:
                return ProcessPhysicalElement(element);
            default:
                LogWarning("Unsupported element type for element: " + element.id);
                if (m_context) {
                    m_context->IncrementSkippedCount();
                }
                return true;
        }
    }
    catch (const std::exception& e) {
        LogError("Exception in legacy processing for element " + element.id + ": " + e.what());
        return false;
    }
}

bool DWGExporter::ProcessElement(const ElementInfo& element) {
    // Redirect to new processing method
    return ProcessElementByType(element);
}

bool DWGExporter::ProcessGeometricElement(const ElementInfo& element) {
    // Extract geometry from element (placeholder)
    // In real implementation, this would use iModelNative APIs

    // For demonstration, create a simple line
    Point3d start(0, 0, 0);
    Point3d end(100, 100, 0);

    if (AddLine(start, end, "Geometry")) {
        m_state.entitiesCreated++;
        return true;
    }

    return false;
}

bool DWGExporter::ProcessSpatialElement(const ElementInfo& element) {
    // Process spatial elements (rooms, spaces, etc.)
    // These might be represented as closed polylines or hatches

    std::vector<Point3d> boundary = {
        Point3d(0, 0, 0),
        Point3d(100, 0, 0),
        Point3d(100, 100, 0),
        Point3d(0, 100, 0)
    };

    if (AddPolyline(boundary, true, "Spaces")) {
        m_state.entitiesCreated++;
        return true;
    }

    return false;
}

bool DWGExporter::ProcessPhysicalElement(const ElementInfo& element) {
    // Process physical elements (walls, columns, etc.)
    // These might be represented as blocks or complex geometry

    // For demonstration, create a rectangle representing a wall
    std::vector<Point3d> wallProfile = {
        Point3d(0, 0, 0),
        Point3d(1000, 0, 0),
        Point3d(1000, 200, 0),
        Point3d(0, 200, 0)
    };

    if (AddPolyline(wallProfile, true, "Walls")) {
        m_state.entitiesCreated++;
        return true;
    }

    return false;
}

//=======================================================================================
// Coordinate System and Units
//=======================================================================================

Point3d DWGExporter::TransformPoint(const Point3d& point) const {
    if (m_context) {
        return m_context->TransformPoint(point);
    }
    return point;
}

Vector3d DWGExporter::TransformVector(const Vector3d& vector) const {
    if (m_context) {
        return m_context->TransformVector(vector);
    }
    return vector;
}

double DWGExporter::TransformLength(double length) const {
    if (m_context) {
        return m_context->ConvertLength(length);
    }
    return length;
}

//=======================================================================================
// Error Handling and Logging
//=======================================================================================

void DWGExporter::LogError(const std::string& message) {
    m_state.errors.push_back(message);
    if (m_context) {
        m_context->AddError("DWGExporter", message);
    }
}

void DWGExporter::LogWarning(const std::string& message) {
    m_state.warnings.push_back(message);
    if (m_context) {
        m_context->AddWarning("DWGExporter", message);
    }
}

void DWGExporter::LogInfo(const std::string& message) {
    // Log to context or console
    if (m_context) {
        m_context->SetCurrentOperation(message);
    }
}

bool DWGExporter::UpdateProgress(ProgressCallback callback, double percentage, const std::string& operation) {
    if (!callback) {
        return true;
    }

    ExportProgress progress;
    progress.percentage = percentage;
    progress.currentOperation = operation;
    progress.processedElements = m_context ? m_context->GetProcessedCount() : 0;
    progress.totalElements = m_context ? m_context->GetTotalElements() : 0;
    progress.status = ExportStatus::Success;

    return callback(progress);
}

bool DWGExporter::CheckCancellation(ProgressCallback callback) {
    if (m_context && m_context->IsCancelled()) {
        return true;
    }

    // Check through progress callback
    ExportProgress progress;
    progress.percentage = 0;
    progress.currentOperation = "Checking cancellation";
    progress.status = ExportStatus::Success;

    return callback ? !callback(progress) : false;
}

//=======================================================================================
// Utility Methods
//=======================================================================================

bool DWGExporter::FileExists(const std::string& path) const {
    return std::filesystem::exists(path);
}

bool DWGExporter::CreateDirectoryIfNeeded(const std::string& path) const {
    try {
        if (!std::filesystem::exists(path)) {
            return std::filesystem::create_directories(path);
        }
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string DWGExporter::SanitizeName(const std::string& name) const {
    std::string sanitized = name;

    // Replace invalid characters
    std::replace_if(sanitized.begin(), sanitized.end(),
        [](char c) { return c == '<' || c == '>' || c == ':' || c == '"' ||
                           c == '|' || c == '?' || c == '*' || c == '/'; }, '_');

    // Ensure name is not empty
    if (sanitized.empty()) {
        sanitized = "Unnamed";
    }

    return sanitized;
}

//=======================================================================================
// DWG Host Application Services Implementation
//=======================================================================================

#ifdef REALDWG_AVAILABLE
DWGHostAppServices::DWGHostAppServices()
    : m_programName("IModelExportFramework")
    , m_productName("iModel Export Framework")
    , m_companyName("Bentley Systems")
{
}

DWGHostAppServices::~DWGHostAppServices() {
}

Acad::ErrorStatus DWGHostAppServices::findFile(
    ACHAR* pcFullPathOut,
    int nBufferLength,
    const ACHAR* pcFilename,
    AcDbDatabase* pDb,
    AcDbHostApplicationServices::FindFileHint hint) {

    // Simple implementation - just copy the filename
    if (pcFilename && pcFullPathOut && nBufferLength > 0) {
        wcsncpy_s(pcFullPathOut, nBufferLength, pcFilename, _TRUNCATE);
        return Acad::eOk;
    }

    return Acad::eFileNotFound;
}

const ACHAR* DWGHostAppServices::program() {
    return m_programName.c_str();
}

const ACHAR* DWGHostAppServices::product() {
    return m_productName.c_str();
}

const ACHAR* DWGHostAppServices::companyName() {
    return m_companyName.c_str();
}

const ACHAR* DWGHostAppServices::prodcode() {
    return L"IMEF";
}

const ACHAR* DWGHostAppServices::releasemarker() {
    return L"1.0";
}

int DWGHostAppServices::versionNumber() {
    return 100; // Version 1.0.0
}
#endif

} // namespace IModelExport
