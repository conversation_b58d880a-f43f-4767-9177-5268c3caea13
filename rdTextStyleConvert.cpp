/*----------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdTextStyleConvert.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

// =======================================================================================
// Opening DWG file:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveDwgTextStylesToDgn ()
    {
    AcDbObjectId                    tableId;
    if ( (tableId = m_pFileHolder->GetDatabase()->textStyleTableId()).isNull() )
        return CantIterateTextStyles;

    AcDbTextStyleTablePointer       acTable (tableId, AcDb::kForRead);
    if (Acad::eOk != acTable.openStatus())
        return CantIterateTextStyles;

    AcDbTextStyleTableIterator*     iterator;
    if (Acad::eOk != acTable->newIterator (iterator))
        return CantIterateTextStyles;

    TextStyleDgnCacheLoaderCollection   textStyleCollection;
    SignedTableIndex*                   textStyleIndex = m_pFileHolder->GetTextStyleIndex();
    for (iterator->start(); !iterator->done(); iterator->step())
        {
        MSElementDescrP         pEntryDescr = NULL;

        AcDbObjectId            textStyleId;
        if (Acad::eOk != iterator->getRecordId (textStyleId))
            continue;

        AcDbTextStyleTableRecordPointer  acTextStyle (textStyleId, AcDb::kForRead);
        if (Acad::eOk != acTextStyle.openStatus())
            continue;        

        DgnTextStylePtr     dgnTextStyle;
        if (RealDwgSuccess == CreateDgnTextStyleFromAcDbTextStyle (dgnTextStyle, acTextStyle))
            {
            textStyleCollection.Add (*dgnTextStyle, this->ElementIdFromObject (acTextStyle));
            // add it to both m_dgnIdToHandleTree and m_originatedInDwgList:
            textStyleIndex->AddEntry ((Int32) dgnTextStyle->GetID(), textStyleId.handle(), true);

            // if this is a dgnlstyle-* from saved from DGN, add it to generated text styles for roundtrip - TFS 243858 for OPPID:
            WStringCR       styleName = dgnTextStyle->GetName ();
            if (acTextStyle->isShapeFile() && styleName.StartsWith(L"dgnlstyle-"))
                this->GetFileHolder().AddGeneratedTextStyle (new RealDwgGeneratedTextStyleItem(styleName.GetWCharCP(), acTextStyle));
            }
        }
    delete iterator;

    textStyleCollection.AddToFile (*GetFile());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateDgnTextStyleFromAcDbTextStyle (DgnTextStylePtr& dgnTextStyle, AcDbTextStyleTableRecord* acTextStyle)
    {
    const ACHAR*    styleName;
    acTextStyle->getName(styleName);
    
    dgnTextStyle = DgnTextStyle::Create (styleName, *this->GetFile());

    if (dgnTextStyle.IsNull() || NULL == acTextStyle)
        {
        DIAGNOSTIC_PRINTF ("Error on null text style!\n");
        return CantCreateTextStyle;
        }

    return  this->SetDgnTextStyleFromAcDbTextStyle(dgnTextStyle, acTextStyle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SetDgnTextStyleFromAcDbTextStyle (DgnTextStylePtr dgnTextStyle, AcDbTextStyleTableRecord* acTextStyle)
    {
    if (dgnTextStyle.IsNull() || NULL == acTextStyle)
        return  NullObject;

    DgnPlatform::TextStyle  styleData;
    UInt32                  fontNo      = 512;
    UInt32                  bigFontNo   = 0;

    this->GetFontIdsFromTextStyle ((int *)&fontNo, (int *)&bigFontNo, acTextStyle);
    this->GetTextStyleDataFromDwg (styleData, acTextStyle, fontNo, bigFontNo, this->GetScaleToDGN());

    dgnTextStyle->AssignLegacyStyleValues (styleData, GetModel());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetTextStyleDataFromDwg
(
DgnPlatform::TextStyle&     styleData,
AcDbTextStyleTableRecord*   acTextStyle,
UInt32                      fontNo,
UInt32                      bigFontNo,
double                      uorScale
)
    {
    if (NULL == acTextStyle)
        return;

    Adesk::Boolean          bold, italic;
    ACHAR*                  typeface;
    Charset                 charset = Charset::kUndefinedCharset;
    FontPitch               pitch = FontPitch::kDefault;
    FontFamily              family = FontFamily::kDoNotCare;

    memset (&styleData, 0, sizeof(DgnPlatform::TextStyle));

    styleData.fontNo        = fontNo;
    styleData.shxBigFont    = bigFontNo;

    styleData.height        = uorScale * acTextStyle->textSize();
    styleData.widthFactor   = acTextStyle->xScale();
    styleData.width         = styleData.height * styleData.widthFactor;

    acTextStyle->font (typeface, bold, italic, charset, pitch, family);
    acutDelString (typeface);

    styleData.flags.bold    = bold;
    styleData.flags.italics = italic;

    if (0.0 != acTextStyle->obliquingAngle ())
        {
        styleData.slant = bsiTrig_getNormalizedAngle (acTextStyle->obliquingAngle ());
        styleData.flags.italics = true;
        }

    if (acTextStyle->isVertical())
        styleData.textDirection |= TXTDIR_VERTICAL;
    else
        styleData.textDirection &= ~TXTDIR_VERTICAL;

    if (0 != (acTextStyle->flagBits() & 0x02))    // "second bit"
        styleData.textDirection |= TXTDIR_BACKWARDS;
    else
        styleData.textDirection &= ~TXTDIR_BACKWARDS;

    if (0 != (acTextStyle->flagBits() & 0x04))    // "third bit"
        styleData.textDirection |= TXTDIR_UPSIDEDOWN;
    else
        styleData.textDirection &= ~TXTDIR_UPSIDEDOWN;

    if (acTextStyle->isShapeFile())
        styleData.flags.acadShapeFile = true;

    // set up some useful defaults.
    if (0.0 == styleData.lineSpacing || styleData.lineSpacing > AUTOCAD_MAXLINESPACING)
        styleData.lineSpacing   = 1.0;

    //    styleData.just - This is set to zero.
    styleData.color                      = COLOR_BYLEVEL;
    styleData.lineLength                 = 0;
    styleData.flags.fixedSpacing         = false;
    styleData.flags.fractions            = true;
    styleData.flags.acadInterCharSpacing = true;

    if ((DgnLineSpacingType)styleData.flags.acadLineSpacingType == DgnLineSpacingType::Exact || (DgnLineSpacingType)styleData.flags.acadLineSpacingType == DgnLineSpacingType::Automatic)
        styleData.flags.acadLineSpacingType = (UInt32) DgnLineSpacingType::AtLeast;

    memset (&styleData.overrideFlags, 0, sizeof (styleData.overrideFlags));
    }


// =======================================================================================
// Saving DGN to DWG

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveDgnTextStylesToDatabase ()
    {
    TextStyleCollection     textStyles (*m_pFileHolder->GetFile());
    SignedTableIndex*       tableIndex  = m_pFileHolder->GetTextStyleIndex();
    ElementIdArrayR         originatedInDwgList = tableIndex->GetOriginatedInDwgList ();

    // if there are any members of the text style tableIndex, then we started with a DWG, and there might have been text styles deleted during the editing session.
    if (this->SavingChanges() && !originatedInDwgList.empty())
        {
        AvlTreeP            currentIdTree   = mdlAvlTree_init (AVLKEY_UINT64);

        // create an avl tree with each currently existing id in it.
        for each (DgnTextStylePtr textStyle in textStyles)
            {
            ElementId       textStyleID = textStyle->GetElementId();
            mdlAvlTree_insertNode (currentIdTree, &textStyleID, sizeof(ElementId));
            }

        // now go through the "originatedInDwgIds" (those that were create from DWG elements when we started) and delete those that are no longer in the "current" list.
        this->DeleteSymbolTableRecords (tableIndex, currentIdTree);
        }

    // now save all the currently existing text styles
    for each (DgnTextStylePtr dgnTextStyle in textStyles)
        {
        this->SaveDgnTextStyleToDatabase (*dgnTextStyle.get());
        }
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetTrueTypeTextStyleFromDGN (AcDbTextStyleTableRecord* pTextStyle, DgnFontCR font, bool bold, bool italics)
    {
    byte        pitch, charSet;
    WStringCR   fontName = font.GetName ();
    font.GetPitchAndCharSet (pitch, charSet);

    if (Acad::eOk != pTextStyle->setFont(fontName.c_str(), bold, italics, static_cast<Charset>(charSet), static_cast<FontPitch>(pitch), FontFamily::kDoNotCare))
        return ERROR;

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//*
* @bsimethod                                                    RayBentley      02/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetShxTextStyleFromDGN (AcDbTextStyleTableRecord* pTextStyle, DgnFontCR font, DgnFontCP shxBigFont, ConvertFromDgnContextR  context)
    {
    WChar     shxFontName[MAXFILELENGTH];
    swprintf (shxFontName, L"%ls.shx", font.GetName().c_str());
    pTextStyle->setFileName (shxFontName);

    WChar     shxBigFontName[MAXFILELENGTH];
    shxBigFontName[0] = 0;
    if (DgnFontType::Rsc == font.GetType ())
        {
        context.GetFileHolder().AddFontToInstallTree (&font);

        if (((DgnRscFontCP) &font)->HasBigFontGlyphs ())
            swprintf (shxBigFontName, L"%sbig.shx", font.GetName().c_str());
        }
    else if (DgnFontType::Shx == font.GetType () && NULL != shxBigFont)
        {
        swprintf (shxBigFontName, L"%ls.shx", shxBigFont->GetName().c_str());
        }

    pTextStyle->setBigFontFileName (shxBigFontName);
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveDgnTextStyleToDatabase (DgnTextStyleR dgnTextStyle)
    {
    // first see if there's an existing AcDbTextStyle.
    AcDbObjectId            existingObjectId = this->ExistingObjectIdFromElementId (dgnTextStyle.GetElementId());
    WStringCR               dgnStyleName = dgnTextStyle.GetName();
    AcString                textStyleName (dgnStyleName.c_str());
    bool                    isAcadShapeFile = false;
    dgnTextStyle.GetProperty (TextStyle_AcadShapeFile, isAcadShapeFile);

    AcDbTextStyleTableRecordPointer     acTextStyle;
    if (existingObjectId.isNull())
        {
        // TR# 152067, 165172 - Dont import xref dependent.
        if (-1 != textStyleName.find (L"|"))
            return RealDwgSuccess;

        // see if we can find it by name.
        AcDbDatabase*               database = m_pFileHolder->GetDatabase();
        AcDbObjectId                ownerId  = database->textStyleTableId();
        AcDbTextStyleTableRecord*   foundStyle;
        if (NULL == (foundStyle = AcDbTextStyleTableRecord::cast (ReuseStandardStyle (textStyleName, ownerId))))
            {
            // didn't find it by name, add it to the database.
            acTextStyle.create ();

            this->DeduplicateTableName (ownerId, textStyleName);
            acTextStyle->setName (textStyleName);

            AcDbTextStyleTablePointer   acTextStyleTable (ownerId, AcDb::kForWrite);
            if (Acad::eOk != acTextStyleTable.openStatus())
                return CantOpenObject;

            AcDbObjectId newObjectId = this->AddRecordToSymbolTable (acTextStyleTable, acTextStyle, dgnTextStyle.GetElementId());
            }
        else
            {
            acTextStyle.acquire (foundStyle);
            }
        }
    else
        {
        acTextStyle.open (existingObjectId, AcDb::kForWrite);
        if (Acad::eOk != acTextStyle.openStatus())
            return CantOpenObject;

        // found existing one. Set the name.
        // don't set the blank name of a text style that is used for a linestyle
        if (!isAcadShapeFile || (0 != dgnStyleName.length()))
            {
            this->ValidateName (textStyleName);
            acTextStyle->setName (textStyleName.kwszPtr());
            }
        }

    // save the test of the text style information to the AcDbTextStyle
    double  slant = 0;
    bool    italics;
    dgnTextStyle.GetProperty (TextStyle_Italics, italics);
    if (italics)
        dgnTextStyle.GetProperty (TextStyle_Slant, slant);

    acTextStyle->setObliquingAngle (bsiTrig_getPositiveNormalizedAngle (slant));

    double height;
    double width;
    dgnTextStyle.GetProperty (TextStyle_Height, height);
    dgnTextStyle.GetProperty (TextStyle_Width, width);
    acTextStyle->setTextSize (this->GetScaleFromDGN() * height);

    if ( (0.0 != height) && (0.0 != width) )
        acTextStyle->setXScale (width / height);
    else
        {
        double widthFactor;
        dgnTextStyle.GetProperty (TextStyle_WidthFactor, widthFactor);
        acTextStyle->setXScale  (0.0 == widthFactor) ? 1.0 : widthFactor;
        }

    // mask of backwards and upsidedown bits.
    Adesk::UInt8    flagBits = acTextStyle->flagBits() & 0xf9;
    bool            backwards;
    dgnTextStyle.GetProperty (TextStyle_Backwards, backwards);
    if (backwards)
        flagBits |= 0x02;

    bool            upsideDown;
    dgnTextStyle.GetProperty (TextStyle_Upsidedown, upsideDown);
    if (upsideDown)
        flagBits |= 0x04;

    acTextStyle->setFlagBits (flagBits);

    // vertical
    bool            vertical;
    dgnTextStyle.GetProperty (TextStyle_Vertical, vertical);
    acTextStyle->setIsVertical (vertical);
    acTextStyle->setIsShapeFile  (isAcadShapeFile);

    DgnFontCP font;
    dgnTextStyle.GetProperty (TextStyle_Font, font);
    if (DgnFontType::TrueType == font->GetType())
        {
        bool    bold;
        dgnTextStyle.GetProperty(TextStyle_Bold, bold);
        if (SUCCESS != SetTrueTypeTextStyleFromDGN(acTextStyle, *font, bold, italics))
            {
            if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_ACAD_TXTSTYLE_WITH_DEFAULT_TTFFONT"))
                {
                SetTrueTypeTextStyleFromDGN(acTextStyle, DgnFontManager::GetDefaultTrueTypeFont(), bold, italics);
                }
            }
        }
    else
        {
        DgnFontCP bigFont = NULL;
        if (BSISUCCESS == dgnTextStyle.GetProperty(TextStyle_ShxBigFont, bigFont) && NULL != bigFont)
            {
            /*-----------------------------------------------------------------------
            Try to handle errors:
            1) When a missing font is substituted with a default font at file opening, 
               it still cannot be resolved here. Do the same logic here at file opening:
               replace the font with default big font.
            2) When a font is successfully resolved, but it's not of a big font, remove it.
            -----------------------------------------------------------------------*/
            if (!bigFont->IsValid())
                bigFont = DgnFontManager::GetDefaultShxBigFont ();
            else if (!bigFont->IsShxBigFont())
                bigFont = NULL;
            }

        RealDwgXDataUtil::RemoveXDataByAppName (acTextStyle, L"ACAD");
        SetShxTextStyleFromDGN (acTextStyle, *font, bigFont, *this);
        }

    RealDwgUtil::SetObjectAnnotative (acTextStyle, m_isDefaultModelAnnotationScaleOn);

    // Add to the index table (if not already there)
    AcDbHandle  dbHandle;
    acTextStyle->getAcDbHandle (dbHandle);

    // Note: In DgnPlatform, dgnTextStyle.GetID() returns a 64-bit ElementID, but it is NOT an ElementID.
    m_pFileHolder->GetTextStyleIndex()->AddEntry ((Int32) dgnTextStyle.GetID(), dbHandle, false);

    return RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------**//**
* These methods are from DgnImportTextStyle.cpp
+---------------+---------------+---------------+---------------+---------------+------*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SetTextDataFromDgn
(
AcDbText*                   pText,
TextParamWideCR             params,
DPoint2dCR                  fontSize,
bool                        backwards,
bool                        upsideDown,
DPoint3dCR                  inOrigin,
DPoint3dCR                  inUserOrigin,
RotMatrixCR                 rMatrix,
ElementHandleCR             elemHandle
)
    {
    // preset entity properties and allow overrides later on as needed
    this->UpdateEntityPropertiesFromElement (pText, elemHandle);

    if ( (0.0 == fontSize.y) || (0.0 == fontSize.x) )
        {
        DIAGNOSTIC_PRINTF ("Ignoring text with degenerate size, ID: %I64d\n", elemHandle.GetElementCP()->ehdr.uniqueId);
        return BSIERROR;
        }

    // TR 112193 if the text has a bad justification then lower left is returned.
    // we now need to use the lower left origin
    AcDb::TextVertMode  vertical;
    AcDb::TextHorzMode  horizontal;
    DPoint3dCP          pUserOrigin = &inUserOrigin;
    bool                hasBigfont = IS_SHX_FONTNUMBER(params.font) && IS_SHX_FONTNUMBER(params.shxBigFont);
    if (SUCCESS != RealDwgUtil::DwgVerticalAndHorizontalAligmentFromDgn (vertical, horizontal, params.just, params.flags.vertical, hasBigfont, params.exFlags.acadFittedText))
        {
        pUserOrigin = &inOrigin;
        }

    pText->setHorizontalMode    (horizontal);
    pText->setVerticalMode      (vertical);
    pText->setHeight            (fabs (fontSize.y) * this->GetScaleFromDGN ());
    pText->setWidthFactor       (0.0 == fontSize.x ? 1.0 : (fabs (fontSize.x / fontSize.y)));
    pText->mirrorInX            (!GetThreeD() && backwards ? true : false);
    pText->mirrorInY            (!GetThreeD() && upsideDown ? true : false);

    AcDbObjectId    textStyleObjectId = this->TextStyleFromDgnTextParams (params);
    pText->setTextStyle (textStyleObjectId);

    DPoint3d            origin, userOrigin;
    this->GetTransformFromDGN().Multiply (origin, inOrigin);
    this->GetTransformFromDGN().Multiply (userOrigin, *pUserOrigin);

    RotMatrix   matrix;
    matrix.InitProduct (this->GetLocalTransform(), rMatrix);

    // Flags must match shapeFile. -Synch them if they do not.
    DVec3d              rX, rY, rZ;
    matrix.getColumns (&rX, &rY, &rZ);

    AcDbTextStyleTableRecordPointer pTextStyle (textStyleObjectId, AcDb::kForRead);

    if (Acad::eOk != pTextStyle.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Error opening text style table! [%ls]\n", acadErrorStatusText(pTextStyle.openStatus()));
        return  BSIERROR;
        }

    Adesk::Boolean textStyleBackwards  = 0 != (pTextStyle->flagBits() & 0x02);    // "second bit"
    Adesk::Boolean textStyleUpsideDown = 0 != (pTextStyle->flagBits() & 0x04);    // "third bit"
    if (pText->isMirroredInX () != textStyleBackwards)
        {
        pText->mirrorInX (textStyleBackwards);
        rX.negate ();
        }

    if (pText->isMirroredInY () != textStyleUpsideDown)
        {
        pText->mirrorInY (textStyleUpsideDown);
        rY.negate ();
        }

    matrix.initFrom2Vectors (&rX, &rY);
    DPoint3d            extrusionDirection, extrusionOrigin;
    double              extrusionAngle;
    RealDwgUtil::ExtractExtrusionAndOrigin (extrusionDirection, extrusionOrigin, extrusionAngle, matrix, origin);

    // get thickness  and extrusion direction (if any)
    double              thickness = 0.0;
    DPoint3d            thicknessDirection;
    if (this->GetEntityThicknessFromElementLinkage(thickness, thicknessDirection, NULL, elemHandle))
        {
        if (thicknessDirection.dotProduct (&extrusionDirection) < 0.0)
            thickness = -thickness;
        }
    pText->setThickness (thickness);

    RotMatrix           extrusionMatrix = RotMatrix::FromIdentity ();
    RealDwgUtil::RotMatrixFromArbitraryGeAxis (extrusionMatrix, RealDwgUtil::GeVector3dFromDPoint3d (extrusionDirection));
    if (!extrusionMatrix.IsIdentity())
        extrusionMatrix.Multiply (extrusionOrigin);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (this->GetSettings().IsZeroZCoordinateEnforced())
        extrusionOrigin.z = 0.0;

    pText->setNormal            (RealDwgUtil::GeVector3dFromDPoint3d (extrusionDirection));      // Must be done first. - both position and alignment point are dependent on origin.
    pText->setPosition          (RealDwgUtil::GePoint3dFromDPoint3d (extrusionOrigin));
    pText->setRotation          (extrusionAngle);
    pText->setAlignmentPoint    (RealDwgUtil::GePoint3dFromDPoint3d (userOrigin));

    if (params.flags.slant)
        {
        // Only if the shapeFile is not italic and the slant angle is 0, but the slant flag is on then go to constant angle.
        // TR: 93349 - Every piece of text with an italic shape file would get the slant flag set (in DwgAbstractText.getTextParamWide)
        // In that case we don't want to apply the fixed scale.
        if (IS_TRUETYPE_FONTNUMBER (params.font) && 0.0 == params.slant)
            {
            Adesk::Boolean  bItalic, bBold;
            ACHAR*          typeface;
            Charset         charset = Charset::kUndefinedCharset;
            FontPitch       pitch = FontPitch::kDefault;
            FontFamily      family = FontFamily::kDoNotCare;

            pTextStyle->font (typeface, bBold, bItalic, charset, pitch, family);
            acutDelString (typeface);

            if (!bItalic)
                pText->setOblique (0.52359877);
            else
                pText->setOblique (params.slant);
            }
        else
            pText->setOblique (params.slant);
        }
     else if (pText->oblique() > TOLERANCE_VectorEqual)
         {
         // turn off oblique angle:
         pText->setOblique (0.0);
         }

    /* the text style could set a colour for the element so we need to override the color on the element */
    if (params.exFlags.color && !this->UseLevelSymbologyOverrides())
        pText->setColor (this->GetColorFromDgn(params.color, pText->colorIndex()));
        
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::TextStyleFromDgnTextParams (TextParamWideCR textParams)
    {
    AcDbObjectId existingStyleObjectId;

    if ( (textParams.flags.textStyle) && (0 != textParams.textStyleId) &&
        ! (existingStyleObjectId = this->ExistingObjectIdFromDBHandle (m_pFileHolder->GetTextStyleIndex ()->GetDBHandle (textParams.textStyleId))).isNull())
        {
        AcDbTextStyleTableRecordPointer pExistingStyle (existingStyleObjectId, AcDb::kForRead);
        if (Acad::eOk != pExistingStyle.openStatus())
            return existingStyleObjectId;

        /*-------------------------------------------------------------------------------
        If the existing text style is of a shape type, replace it with STANDARD for the 
        text.  A shape text style attached to a text in a DXF file causes ACAD to unable
        to open the file.
        -------------------------------------------------------------------------------*/
        if (pExistingStyle->isShapeFile())
            return  acdbSymUtil()->textStyleStandardId(this->GetFileHolder().GetDatabase());

        /* if the vertical is overriden on the element then we need to generate a new text style based on the old one */
        if (!textParams.overridesFromStyle.direction)
            return existingStyleObjectId;

        DgnTextStylePtr   textStyle = DgnTextStyle::GetByID (textParams.textStyleId, *GetFile());
        bool              vertical;
        if (textStyle.IsValid() && (BSISUCCESS == (textStyle->GetProperty (TextStyle_Vertical, vertical))) && (vertical != textParams.flags.vertical) )
            return this->AddTextStyleFromDonor (pExistingStyle, textParams.flags.vertical);
        else
            return existingStyleObjectId;
        }
    else
        {
        return  this->AddTextStyleFromFontNumber (textParams.font, textParams.flags.shxBigFont ? textParams.shxBigFont : 0, textParams.flags.vertical,
                                                  textParams.flags.slant, textParams.exFlags.bold, textParams.slant, m_model);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      04/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddTextStyleFromDonor
(
AcDbTextStyleTableRecord*   pTextStyle,
bool                        bVertical
)
    {
    const ACHAR*    styleName;
    pTextStyle->getName (styleName);

    ACHAR   fullStyleName[2048];
    if (bVertical)
        swprintf (fullStyleName, L"%ls V", styleName);
    else
        swprintf (fullStyleName, L"%ls NV", styleName);

    RealDwgGeneratedTextStyleItem *pItem = m_pFileHolder->GetGeneratedTextStyle (fullStyleName);

    if (NULL == pItem)
        {
        AcDbTextStyleTableRecord*   pNewTextStyle = NULL;
        AcDbObjectId                ownerId       = m_pFileHolder->GetDatabase()->textStyleTableId();
        AcDbTextStyleTablePointer   pTextStyleTable (ownerId, AcDb::kForWrite);

        // if text style already exists in the style table, use it -- it wont just be safe.
        AcDbTextStyleTableRecordPointer     pExistingTextStyle;
        AcDbObjectId                        existingStyleObjectId;
        if (Acad::eOk == pTextStyleTable->getAt (fullStyleName, existingStyleObjectId))
            {
            pExistingTextStyle.open (existingStyleObjectId, AcDb::kForWrite);
            if (Acad::eOk == pExistingTextStyle.openStatus())
                pNewTextStyle = pExistingTextStyle;
            }
        if (NULL == pNewTextStyle)
            {
            pNewTextStyle = new AcDbTextStyleTableRecord();
            pNewTextStyle->setName (fullStyleName);
            pTextStyleTable->add (pNewTextStyle);
            }

        pItem = new RealDwgGeneratedTextStyleItem (fullStyleName, pNewTextStyle);

        pNewTextStyle->setIsVertical (bVertical ? true : false);
        pNewTextStyle->setTextSize (pTextStyle->textSize ());
        pNewTextStyle->setXScale (pTextStyle->xScale ());
        pNewTextStyle->setObliquingAngle (pTextStyle->obliquingAngle ());
        pNewTextStyle->setIsShapeFile (pTextStyle->isShapeFile ());
        pNewTextStyle->setFlagBits (pTextStyle->flagBits());

        ACHAR*          typeface;
        Adesk::Boolean  bold, italic;
        Charset         charset = Charset::kUndefinedCharset;
        FontPitch       pitch = FontPitch::kDefault;
        FontFamily      family = FontFamily::kDoNotCare;

        pTextStyle->font (typeface, bold, italic, charset, pitch, family);

        if ( (NULL == typeface) || (0 == *typeface) )   // If no typeface then must be shx.
            {
            const ACHAR*    fileName;
            const ACHAR*    bigFontFileName;
            pTextStyle->fileName (fileName);
            pTextStyle->bigFontFileName (bigFontFileName);
            pNewTextStyle->setFileName (fileName);
            pNewTextStyle->setBigFontFileName (bigFontFileName);
            }
        else
            {
            pNewTextStyle->setFont (typeface, bold, italic, charset, pitch, family);
            }
        acutDelString (typeface);

        pNewTextStyle->close ();

        m_pFileHolder->AddGeneratedTextStyle (pItem);
        }

    return  pItem->GetTextStyleObjectId ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId         FindExistingTextstyle
(
AcString const&             textStylename,
AcDbTextStyleTableRecord*   pNewTextstyle,
AcDbTextStyleTable*         pTextstyleTable
)
    {
    AcDbObjectId    existingTextStyleId;

    if (pNewTextstyle->isShapeFile())
        {
        /*-------------------------------------------------------------------------------
        If the new textstyle uses a non-text SHX file, we can not find existing text style
        by name because the name of such a style record is blank.  We instead check for
        SHX file name.
        -------------------------------------------------------------------------------*/
        AcDbTextStyleTableIterator* pIter;
        if (Acad::eOk != pTextstyleTable->newIterator (pIter))
            return existingTextStyleId;

        for (pIter->start(); !pIter->done(); pIter->step())
            {
            AcDbObjectId    recordId;
            if (Acad::eOk != pIter->getRecordId (recordId))
                continue;

            AcDbTextStyleTableRecordPointer pTextstyle (recordId, AcDb::kForRead);
            if (Acad::eOk != pTextstyle.openStatus())
                continue;

            if (!pTextstyle->isShapeFile())
                continue;

            const ACHAR*    fileName;
            pTextstyle->fileName (fileName);
            const ACHAR*    newFileName;
            pNewTextstyle->fileName (newFileName);
            if ( 0 == wcsicmp (fileName, newFileName))
                {
                existingTextStyleId = recordId;
                break;
                }
            }
        delete pIter;
        }
    else
        {
        /*-------------------------------------------------------------------------------
        For a textstyle that uses a text SHX file, simple search for an existing style
        using style name from the text style table.
        -------------------------------------------------------------------------------*/
        AcDbObjectId    textStyleId;
        if (Acad::eOk == pTextstyleTable->getAt (textStylename, textStyleId))
            existingTextStyleId = textStyleId;
        }

    return  existingTextStyleId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ValidateTextStyleName (AcString& name)
    {
    // a textstyle name cannot end with a space character TR 325638
    unsigned        numChars = name.length ();
    unsigned        numSpaces = 0;
    const ACHAR*    stringEnd = name.kwszPtr() + numChars - 1;

    for (; numSpaces < numChars && L' ' == *stringEnd; numSpaces++, stringEnd--)
        ;

    if (numSpaces > 0)
        {
        name = name.substr (numChars - numSpaces);
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      04/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddTextStyleFromFontNumber
(
int                         iFontNo,
int                         iBigFontNo,
bool                        bVertical,
bool                        bItalics,
bool                        bBold,
double                      dSlantAngle,
DgnModelRefP                modelRef
)
    {
    // This flag is used to indicate that we have a well defined usage of big font for an shx/big font pair.
    // Other cases where big font is encountered are: the RSC font has glyphs that require a big font and the
    // iFontNo is incorrectly specified as a big font.  These two cases are exceptions and so do not set this
    // flag to true.
    DgnFontNumMapP     fontNumMap = DgnFontManager::GetDgnFontMapP (modelRef);
    BeAssert (nullptr != fontNumMap && L"Found no font map from DgnModelRef!");

    DgnFontP       font             = fontNumMap->GetFontP (iFontNo);
    DgnFontP       bigFont          = fontNumMap->GetFontP (iBigFontNo);
    bool        bUseItalicTTFFont   = false;
    bool        isGivenBigFontValid = (NULL != font && DgnFontType::Shx == font->GetType () && NULL != bigFont && bigFont->IsShxBigFont());

    ACHAR       tmpBuf[100];

    swprintf (tmpBuf, L"%d", iFontNo);

    AcString name (tmpBuf);

    if (isGivenBigFontValid)
        {
        swprintf (tmpBuf, L" %d", iBigFontNo);
        name += tmpBuf;
        }

    if (bVertical)
        name += L" V";

    // if the font is a true type font and the italics is and the angle is 0 the italic font
    // is being used. We need to a synthisize an italic angle for autocad to use
    if (IS_TRUETYPE_FONTNUMBER (iFontNo) && bItalics && dSlantAngle == 0.0)
        {
        bUseItalicTTFFont = true;
        name += L" IF";
        }

    if (bItalics && !bUseItalicTTFFont)
        {
        swprintf (tmpBuf, L" I %.5lf", dSlantAngle);
        name += tmpBuf;
        }

    if (bBold)
        name += L" B";

    // If from a reference file, need to qualify with the reference file name.
    DgnAttachmentP  reference = modelRef->AsDgnAttachmentP ();
    if (NULL != reference)
        {
        WString     refName = reference->GetEffectiveFullFileSpec ();
        if (!refName.empty())
            {
            name += L" ";
            name += refName.c_str ();
            }
        }

    RealDwgGeneratedTextStyleItem *pItem = m_pFileHolder->GetGeneratedTextStyle (name);

    if (NULL == pItem)
        {
        AcDbObjectId                ownerId       = m_pFileHolder->GetDatabase()->textStyleTableId();
        AcDbTextStyleTable*         pTextStyleTable = NULL;

        if (Acad::eOk != acdbOpenObject(pTextStyleTable, ownerId, AcDb::kForWrite))
            {
            // should never hit here, but handle error anyway:
            DIAGNOSTIC_PRINTF ("Error opening text style table - failed adding a textstyle for fontNo %d\n", iFontNo);
            return  AcDbObjectId::kNull;
            }

        AcDbTextStyleTableRecord*   acNewTextStyle = new AcDbTextStyleTableRecord();

        acNewTextStyle->setIsVertical (bVertical);

        WString     fontName, bigFontName;
        if (isGivenBigFontValid)
            bigFontName = bigFont->GetName ();
        if (NULL != font)
            {
            fontName = font->GetName ();

            if (DgnFontType::Rsc == font->GetType ())
                RealDwgUtil::ReplaceInvalidFileNameCharsInNameW (fontName);

            if (DgnFontType::TrueType != font->GetType ())
                {
                acNewTextStyle->setIsShapeFile (false);
                if (DgnFontType::Rsc == font->GetType () && ((DgnRscFontCP) font)->HasBigFontGlyphs ())
                    {
                    AcString bigFontFileName (fontName.c_str ());
                    bigFontFileName += L"big.shx";
                    acNewTextStyle->setBigFontFileName (bigFontFileName);
                    }
                else if (DgnFontType::Shx == font->GetType ())
                    {
                    if (isGivenBigFontValid)
                        {
                        AcString    acFontName (bigFontName.c_str ());
                        acFontName += L".shx";
                        acNewTextStyle->setBigFontFileName (acFontName);
                        }
                    else if ( (NULL != bigFont) && (DgnFontType::Rsc == bigFont->GetType()) && ((DgnRscFontCP)bigFont)->HasBigFontGlyphs())
                        {
                        // handle an RSC font having extended chars requiring big font, or iFontNo being a bigFont.
                        AcString acFontName (fontName.c_str ());
                        acFontName += L".shx";
                        /* It is a SHX file. Determine whether this is a big font file */
                        acNewTextStyle->setBigFontFileName (acFontName);
                        }
                    acNewTextStyle->setIsShapeFile (font->IsSymbolFont());
                    }
                AcString acShxFontName (fontName.c_str ());
                acShxFontName += ".shx";
                acNewTextStyle->setFileName (acShxFontName);
                }
            else // TRUETYPE FONT
                {
                bool        bold = bBold ? true : false, italics = bUseItalicTTFFont ? true : false;

                byte        pitch, charSet;
                font->GetPitchAndCharSet (pitch, charSet);

                acNewTextStyle->setFont (fontName.c_str (), bold, italics, static_cast<Charset>(charSet), static_cast<FontPitch>(pitch), FontFamily::kDoNotCare);
                acNewTextStyle->setIsShapeFile (false);
                }
            }
        else
            {
            font = &DgnFontManager::GetDefaultRscFont ();
            iFontNo = DgnFontManager::GetDefaultRscFontId ();

            if (iFontNo != -1 && iFontNo != 0xFFFF)
                {
                pTextStyleTable->close ();
                delete acNewTextStyle;

                return this->AddTextStyleFromFontNumber (iFontNo, 0, bVertical, bItalics, bBold, dSlantAngle, m_model);
                }
            else
                {
                ASSERT (false);
                }

            fontName = font->GetName ();
            }

        AcString acTextStyleName (fontName.c_str ());
        if (isGivenBigFontValid)
            {
            acTextStyleName += L"-";
            acTextStyleName += AcString (bigFontName.c_str ());
            }

        if (bVertical)
            acTextStyleName += L" V";

        if (bUseItalicTTFFont)
            acTextStyleName += L" IF";

        if (bItalics && !bUseItalicTTFFont)
            {
            swprintf (tmpBuf, L" I %.5lf", dSlantAngle);
            acTextStyleName += tmpBuf;
            }

        // after all, we can not set text style name if it is of a shapefile type, which requires a blank name:
        if (acNewTextStyle->isShapeFile())
            {
            acTextStyleName = AcString(L"");
            }
        else
            {
            WStringCR textStyleNameTemplate = GetSettings().GetTextStyleNameTemplate ();
            if (textStyleNameTemplate.length() > 0)
                {
                WChar combinedName[2048];
                swprintf_s (combinedName, _countof (combinedName), textStyleNameTemplate.c_str(), acTextStyleName.kwszPtr());
                acTextStyleName = combinedName;
                }
            }

        // if text style already exists in the style table, use it
        AcDbTextStyleTableRecordPointer     pExistingTextStyle;
        AcDbObjectId                        existingStyleObjectId = FindExistingTextstyle (acTextStyleName, acNewTextStyle, pTextStyleTable);
        if (!existingStyleObjectId.isNull())
            {
            pExistingTextStyle.open (existingStyleObjectId, AcDb::kForWrite);
            delete acNewTextStyle;
            acNewTextStyle = pExistingTextStyle.object();
            }
        else
            {
            ValidateTextStyleName (acTextStyleName);
            Acad::ErrorStatus es = acNewTextStyle->setName (acTextStyleName);
            if (Acad::eOk != es)
                DIAGNOSTIC_PRINTF ("Failed to set new text style name \"%ls\" [%ls]\n", acTextStyleName.kwszPtr(), acadErrorStatusText(es));
            pTextStyleTable->add (acNewTextStyle);
            }

        acNewTextStyle->setIsVertical (bVertical ? true : false);
        acNewTextStyle->setTextSize (0.0);
        acNewTextStyle->setXScale (1.0);
        acNewTextStyle->setObliquingAngle (dSlantAngle);

        m_pFileHolder->AddGeneratedTextStyle (pItem = new RealDwgGeneratedTextStyleItem (name, acNewTextStyle));

        acNewTextStyle->close ();
        pTextStyleTable->close ();
        }

    if (NULL != font)
        m_pFileHolder->AddFontToInstallTree (font);

    return pItem->GetTextStyleObjectId ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater  02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::DTextFromTextBlock
(
AcDbText*                   pText,
TextBlockR                  textBlock,
ElementHandleR              elemHandle,
bool                        checkIn
)
    {
    if (textBlock.IsEmpty ())
        return EmptyText;

    DPoint3d    origin      = textBlock.GetTextAutoCADOrigin ();
    DPoint3d    userOrigin  = textBlock.GetUserOrigin ();
    RotMatrix   rMatrix     = textBlock.GetOrientation ();

    if (GetThreeD() && textBlock.GetProperties ().IsBackwards())
        {
        RotMatrix   mirrorX = {-1.0, 0, 0, 0, 1.0, 0, 0, 0, 1.0};
        rMatrix.productOf (&rMatrix, &mirrorX);
        }

    if (GetThreeD() && textBlock.GetProperties ().IsUpsideDown())
        {
        RotMatrix   mirrorY = {1, 0, 0, 0, -1.0, 0, 0, 0, 1.0};
        rMatrix.productOf (&rMatrix, &mirrorY);
        }

    ParagraphPropertiesCP   paragraphProperties = textBlock.GetParagraphProperties (0);
    RunPropertiesCP         runProperties = textBlock.GetRunProperties (0);
    if (NULL == paragraphProperties || NULL == runProperties)
        return  MstnElementUnacceptable;

    TextBlockPropertiesCR   textProperties = textBlock.GetProperties (); 
    double                  annoScale = textBlock.GetProperties ().GetAnnotationScale ();

    TextParamWide           sWide;
    paragraphProperties->ToElementData (sWide, textBlock.GetProperties ().GetAnnotationScale());
    runProperties->ToElementData (sWide, textBlock.GetProperties ().GetAnnotationScale());
    
    DPoint2d            scale = runProperties->GetFontSize ();

    // set annotation scale in modelspace
    bool                annotative = false;
    if (textProperties.HasAnnotationScale() && this->CanSaveAnnotationScale())
        annotative = this->AddAnnotationScaleToObject (pText, annoScale, checkIn ? elemHandle.GetElementId() : 0);
    else
        RealDwgUtil::SetObjectAnnotative (pText, false);

    Bentley::WString    markup;
    textBlock.ToDText (markup, GetModel (), !this->GetSettings().ConvertEmptyEDFToSpace());

    /* TR 119390 if we are forcing dtext creation and the font is overridden then reset the textstyle on the element so
        a new textstyle is created for the font */
    bool                missingMainBody = true;
    RunRange            runRange (textBlock);
    for (RunIterator runIter = runRange.begin(); runIter != runRange.end(); ++runIter)
        {
        RunPropertiesCR currProps = (*runIter).GetProperties ();
        if (!currProps.IsSuperScript() && !currProps.IsSubScript())
            missingMainBody = false;

        if (currProps.IsFontOverridden())
            {
            sWide.flags.textStyle = false;
            sWide.textStyleId = 0;
            if (!missingMainBody)
                break;
            }
        }

    /*V7 to V8 file conversion sometime not set the overridden flag related to flags.textstyle and currProps.IsFontOverridden()
    functionality return wrong value*/
    if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_ACAD_TXTSTYLE_WITH_DEFAULT_TTFFONT"))
        {
        if (sWide.flags.textStyle || sWide.textStyleId > 0)
            {
            DgnTextStylePtr   textStyle = DgnTextStyle::GetByID(sWide.textStyleId, *this->GetFile());
            if (textStyle->GetFontNo() != sWide.font)
                {
                sWide.flags.textStyle = false;
                sWide.textStyleId = 0;
                }
            }
        }

    if (scale.y == 0.0 || scale.x == 0.0)
        return  ScaleError;

    if (markup.empty ())
        {
        if (pText->isKindOf (AcDbAttributeDefinition::desc()) || pText->isKindOf (AcDbAttribute::desc()))
            pText->setTextString (L"");
        else
            return EmptyText;
        }

    // a super/sub scripted text without a main body needs re-sized as well as relocated, TFS 701057:
    if (missingMainBody)
        {
        DPoint2d    scriptedSize = DPoint2d::FromZero ();
        DPoint3d    scriptedOffset = DPoint3d::FromZero ();
        runProperties->GetGlyphLayoutSize (scriptedSize, scriptedOffset);
        if (!scriptedSize.IsEqual(DPoint2d::FromZero()))
            {
            scale = scriptedSize;
            rMatrix.Multiply (scriptedOffset);
            origin.Add (scriptedOffset);
            }
        }

    AcString    textString (markup.c_str());
    pText->setTextString (textString);

    this->SetTextDataFromDgn (pText, sWide, scale, textBlock.GetProperties ().IsBackwards(), textBlock.GetProperties ().IsUpsideDown(), origin, userOrigin, rMatrix, elemHandle);

    /* add rsc to tree of fonts to export */
    for (size_t iRun = 0, textParamWideCount = textBlock.GetRunPropertiesCount (); iRun < textParamWideCount; iRun++)
        this->GetFileHolder().AddFontToInstallTree (&textBlock.GetRunProperties(iRun)->GetFont());

    return  RealDwgSuccess;
    }
