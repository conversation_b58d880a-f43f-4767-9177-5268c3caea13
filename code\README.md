# iModelNative Multi-Format Export Framework

## Overview

This framework provides a unified interface for exporting iModel data to multiple CAD and 3D formats:

- **DWG** - AutoCAD format using RealDWG SDK
- **IFC** - Industry Foundation Classes using ODA IFC SDK  
- **DGN** - MicroStation format using ODA DGN SDK
- **OpenUSD** - Universal Scene Description format

## Architecture

```
iModelNative Data
       │
       ▼
┌─────────────────┐
│  Export Manager │
│   (Coordinator) │
└─────────────────┘
       │
       ▼
┌─────────────────────────────────────────────────────┐
│              Format Exporters                      │
├─────────────┬─────────────┬─────────────┬─────────────┤
│ DWG Exporter│ IFC Exporter│ DGN Exporter│ USD Exporter│
│ (RealDWG)   │ (ODA IFC)   │ (ODA DGN)   │ (OpenUSD)   │
└─────────────┴─────────────┴─────────────┴─────────────┘
       │              │              │              │
       ▼              ▼              ▼              ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ .dwg files  │ │ .ifc files  │ │ .dgn files  │ │ .usd files  │
└─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
```

## Directory Structure

```
code/
├── include/                    # Public headers
│   ├── IModelExportManager.h   # Main export interface
│   ├── IExportFormat.h         # Format interface
│   └── ExportTypes.h           # Common types and enums
├── src/                        # Implementation
│   ├── core/                   # Core framework
│   │   ├── ExportManager.cpp   # Main coordinator
│   │   ├── ExportContext.cpp   # Export context management
│   │   └── GeometryConverter.cpp # Common geometry utilities
│   ├── formats/                # Format-specific exporters
│   │   ├── dwg/               # DWG export (RealDWG)
│   │   ├── ifc/               # IFC export (ODA)
│   │   ├── dgn/               # DGN export (ODA)
│   │   └── usd/               # USD export (OpenUSD)
│   └── utils/                  # Utilities
├── examples/                   # Usage examples
├── tests/                      # Unit tests
└── cmake/                      # CMake configuration
```

## Key Features

### Unified Export Interface
- Single API for all export formats
- Consistent parameter handling
- Unified progress reporting
- Common error handling

### Format-Specific Optimization
- Native SDK integration for each format
- Format-specific feature mapping
- Optimized data structures per format
- Custom export options per format

### iModel Integration
- Direct iModel element access
- Efficient geometry extraction
- Metadata preservation
- Relationship handling

### Performance Features
- Multi-threaded export processing
- Memory-efficient streaming
- Progress monitoring with cancellation
- Batch processing support

## Quick Start

```cpp
#include "IModelExportManager.h"

// Create export manager
auto exportManager = IModelExportManager::Create();

// Load iModel
IModelDb imodel = LoadIModel("path/to/model.bim");

// Configure export options
DWGExportOptions dwgOptions;
dwgOptions.outputPath = "output/model.dwg";
dwgOptions.version = DWGExportOptions::DWGVersion::R2021;
dwgOptions.levelOfDetail = ExportLOD::High;
dwgOptions.includeMetadata = true;

// Export with progress callback
auto result = exportManager->ExportToDWG(imodel, dwgOptions, [](const ExportProgress& progress) {
    std::cout << "Progress: " << progress.percentage << "%" << std::endl;
    return true; // Continue export
});

if (result.status == ExportStatus::Success) {
    std::cout << "Export completed: " << result.outputFile << std::endl;
}
```

## Building the Framework

### Prerequisites

- **CMake 3.16+**
- **C++17 compatible compiler**
- **iModelNative SDK** (required)
- **RealDWG SDK** (optional, for DWG export)
- **ODA Platform SDK** (optional, for IFC/DGN export)
- **OpenUSD** (optional, for USD export)

### Build Steps

```bash
# Clone repository
git clone <repository-url>
cd imodel-export-framework

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. \
    -DIMODELNATIVE_ROOT=/path/to/imodelnative \
    -DREALDWG_ROOT=/path/to/realdwg \
    -DODA_ROOT=/path/to/oda \
    -DOPENUSD_ROOT=/path/to/openusd \
    -DENABLE_REALDWG=ON \
    -DENABLE_ODA_IFC=ON \
    -DENABLE_ODA_DGN=ON \
    -DENABLE_OPENUSD=ON

# Build
cmake --build . --config Release

# Install
cmake --install . --prefix /path/to/install
```

## Dependencies

- **iModelNative SDK** - Core iModel access
- **RealDWG SDK** - DWG format support
- **ODA Platform** - IFC and DGN format support
- **OpenUSD** - USD format support
- **CMake 3.16+** - Build system
- **C++17** - Language standard
