#pragma once

#include "../../../include/ExportTypes.h"
#include "../../../core/GeometryProcessor.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/acgi.h>
#endif

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class ExportContext;
class DWGExporter;

//=======================================================================================
// DWG Entity Processing Status
//=======================================================================================

enum class DWGProcessingStatus {
    Success,
    Failed,
    Skipped,
    InvalidGeometry,
    UnsupportedEntity,
    MemoryError,
    ConversionError,
    ValidationError
};

//=======================================================================================
// DWG Entity Validation Result
//=======================================================================================

struct DWGValidationResult {
    bool isValid = false;
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    double complexity = 0.0;
    
    void AddError(const std::string& error) {
        errors.push_back(error);
        isValid = false;
    }
    
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
};

//=======================================================================================
// Base DWG Entity Processor
//=======================================================================================

class DWGEntityProcessor {
public:
    DWGEntityProcessor(DWGExporter* exporter);
    virtual ~DWGEntityProcessor() = default;

    // Main processing interface
    virtual DWGProcessingStatus ProcessEntity(const ElementInfo& element) = 0;
    virtual bool CanProcessEntity(const ElementInfo& element) const = 0;
    virtual std::string GetProcessorName() const = 0;

    // Validation interface
    virtual DWGValidationResult ValidateGeometry(const ElementInfo& element) const;
    virtual bool ValidatePoints(const std::vector<Point3d>& points) const;
    virtual bool ValidateCoordinate(const Point3d& point) const;

    // Error handling
    void SetTolerance(double tolerance) { m_tolerance = tolerance; }
    double GetTolerance() const { return m_tolerance; }

protected:
    // Geometry processing helpers
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    double TransformLength(double length) const;
    double TransformAngle(double angle) const;

    // Coordinate validation and repair
    bool CoerceInvalidElevation(double& elevation) const;
    void ValidatePointArray(std::vector<Point3d>& points) const;
    bool IsValidCoordinate(double value) const;

    // Entity creation helpers
#ifdef REALDWG_AVAILABLE
    bool SetEntityProperties(AcDbEntity* entity, const std::string& layer = "") const;
    bool AddEntityToModelSpace(AcDbEntity* entity) const;
    AcDbObjectId GetOrCreateLayer(const std::string& layerName, const Color& color = Color()) const;
#endif

    // Error reporting
    void LogError(const std::string& message) const;
    void LogWarning(const std::string& message) const;
    void LogInfo(const std::string& message) const;

    // Member variables
    DWGExporter* m_exporter;
    double m_tolerance = 1e-10;
    
    // Statistics
    mutable size_t m_processedCount = 0;
    mutable size_t m_errorCount = 0;
    mutable size_t m_warningCount = 0;
};

//=======================================================================================
// DWG Line Processor
//=======================================================================================

class DWGLineProcessor : public DWGEntityProcessor {
public:
    DWGLineProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGLineProcessor"; }

    // Line-specific processing
    DWGProcessingStatus ProcessLine(const Point3d& start, const Point3d& end, const std::string& layer = "");
    DWGProcessingStatus ProcessRay(const Point3d& basePoint, const Vector3d& direction, const std::string& layer = "");
    DWGProcessingStatus ProcessXLine(const Point3d& basePoint, const Vector3d& direction, const std::string& layer = "");

    // Validation
    DWGValidationResult ValidateGeometry(const ElementInfo& element) const override;
    bool ValidateLineGeometry(const Point3d& start, const Point3d& end) const;

private:
    // Line processing helpers
    bool IsZeroLengthLine(const Point3d& start, const Point3d& end) const;
    void FixLineEndpoints(Point3d& start, Point3d& end) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbLine* CreateDWGLine(const Point3d& start, const Point3d& end) const;
    AcDbRay* CreateDWGRay(const Point3d& basePoint, const Vector3d& direction) const;
    AcDbXline* CreateDWGXLine(const Point3d& basePoint, const Vector3d& direction) const;
    bool SetInfiniteLineLinkage(AcDbEntity* entity, bool infiniteStart, bool infiniteEnd) const;
#endif
};

//=======================================================================================
// DWG Circle Processor
//=======================================================================================

class DWGCircleProcessor : public DWGEntityProcessor {
public:
    DWGCircleProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGCircleProcessor"; }

    // Circle-specific processing
    DWGProcessingStatus ProcessCircle(const Point3d& center, double radius, const Vector3d& normal = Vector3d(0, 0, 1), const std::string& layer = "");
    DWGProcessingStatus ProcessArc(const Point3d& center, double radius, double startAngle, double endAngle, const Vector3d& normal = Vector3d(0, 0, 1), const std::string& layer = "");
    DWGProcessingStatus ProcessEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, const std::string& layer = "");

    // Validation
    DWGValidationResult ValidateGeometry(const ElementInfo& element) const override;
    bool ValidateCircleGeometry(const Point3d& center, double radius) const;
    bool ValidateArcGeometry(const Point3d& center, double radius, double startAngle, double endAngle) const;

private:
    // Circle processing helpers
    bool IsValidRadius(double radius) const;
    bool IsValidAngle(double angle) const;
    void NormalizeAngles(double& startAngle, double& endAngle) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbCircle* CreateDWGCircle(const Point3d& center, double radius, const Vector3d& normal) const;
    AcDbArc* CreateDWGArc(const Point3d& center, double radius, double startAngle, double endAngle, const Vector3d& normal) const;
    AcDbEllipse* CreateDWGEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio) const;
#endif
};

//=======================================================================================
// DWG Text Processor
//=======================================================================================

class DWGTextProcessor : public DWGEntityProcessor {
public:
    DWGTextProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGTextProcessor"; }

    // Text-specific processing
    DWGProcessingStatus ProcessText(const Point3d& position, const std::string& text, double height, double rotation = 0.0, const std::string& layer = "");
    DWGProcessingStatus ProcessMText(const Point3d& position, const std::string& text, double height, double width = 0.0, const std::string& layer = "");

    // Text properties
    struct TextProperties {
        std::string fontName = "Arial";
        double height = 2.5;
        double widthFactor = 1.0;
        double obliqueAngle = 0.0;
        double rotation = 0.0;
        bool isBold = false;
        bool isItalic = false;
        bool isUnderlined = false;
        Color color = Color(1.0f, 1.0f, 1.0f, 1.0f);
        
        enum class Justification {
            Left, Center, Right, Aligned, Middle, Fit
        } justification = Justification::Left;
    };

    // Advanced text processing
    DWGProcessingStatus ProcessTextWithProperties(const Point3d& position, const std::string& text, const TextProperties& properties, const std::string& layer = "");

    // Validation
    DWGValidationResult ValidateGeometry(const ElementInfo& element) const override;
    bool ValidateTextGeometry(const Point3d& position, const std::string& text, double height) const;

private:
    // Text processing helpers
    bool IsValidTextHeight(double height) const;
    bool IsValidTextString(const std::string& text) const;
    std::string SanitizeTextString(const std::string& text) const;
    
    // Text encoding and conversion
    bool ConvertMIFToUnicode(std::string& text) const;
    double GetDisplayRotationAngle(double rotation, bool isAnnotative = false) const;
    void ProcessTextJustification(const TextProperties& properties, Point3d& position) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbText* CreateDWGText(const Point3d& position, const std::string& text, double height, double rotation) const;
    AcDbMText* CreateDWGMText(const Point3d& position, const std::string& text, double height, double width) const;
    bool SetTextProperties(AcDbText* textEntity, const TextProperties& properties) const;
    bool SetMTextProperties(AcDbMText* mtextEntity, const TextProperties& properties) const;
    AcDbObjectId GetOrCreateTextStyle(const std::string& styleName, const TextProperties& properties) const;
#endif

    // Text style cache
    mutable std::unordered_map<std::string, std::string> m_textStyleCache;
};

//=======================================================================================
// DWG Entity Processor Factory
//=======================================================================================

class DWGEntityProcessorFactory {
public:
    static std::unique_ptr<DWGEntityProcessor> CreateProcessor(const std::string& entityType, DWGExporter* exporter);
    static std::vector<std::string> GetSupportedEntityTypes();
    static bool IsEntityTypeSupported(const std::string& entityType);

private:
    static std::unordered_map<std::string, std::function<std::unique_ptr<DWGEntityProcessor>(DWGExporter*)>> s_processorFactories;
    static void InitializeFactories();
};

} // namespace IModelExport
