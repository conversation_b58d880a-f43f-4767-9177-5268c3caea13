#include "DWGEntityProcessor.h"
#include "../DWGExporter.h"
#include "../../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbray.h>
#include <realdwg/base/dbxline.h>
#include <realdwg/base/acgimaterial.h>
#endif

#include <algorithm>
#include <cmath>
#include <regex>

namespace IModelExport {

//=======================================================================================
// Base DWG Entity Processor Implementation
//=======================================================================================

DWGEntityProcessor::DWGEntityProcessor(DWGExporter* exporter)
    : m_exporter(exporter)
    , m_tolerance(1e-10)
{
    if (!m_exporter) {
        throw std::invalid_argument("DWGExporter cannot be null");
    }
}

DWGValidationResult DWGEntityProcessor::ValidateGeometry(const ElementInfo& element) const {
    DWGValidationResult result;
    result.isValid = true;
    
    // Basic validation - derived classes should override for specific validation
    if (element.id.empty()) {
        result.AddError("Element ID is empty");
    }
    
    return result;
}

bool DWGEntityProcessor::ValidatePoints(const std::vector<Point3d>& points) const {
    for (const auto& point : points) {
        if (!ValidateCoordinate(point)) {
            return false;
        }
    }
    return true;
}

bool DWGEntityProcessor::ValidateCoordinate(const Point3d& point) const {
    return IsValidCoordinate(point.x) && 
           IsValidCoordinate(point.y) && 
           IsValidCoordinate(point.z);
}

Point3d DWGEntityProcessor::TransformPoint(const Point3d& point) const {
    if (m_exporter) {
        return m_exporter->TransformPoint(point);
    }
    return point;
}

Vector3d DWGEntityProcessor::TransformVector(const Vector3d& vector) const {
    if (m_exporter) {
        return m_exporter->TransformVector(vector);
    }
    return vector;
}

double DWGEntityProcessor::TransformLength(double length) const {
    if (m_exporter) {
        return m_exporter->TransformLength(length);
    }
    return length;
}

double DWGEntityProcessor::TransformAngle(double angle) const {
    // Angles typically don't need transformation, but included for completeness
    return angle;
}

bool DWGEntityProcessor::CoerceInvalidElevation(double& elevation) const {
    const double MAX_ELEVATION = 1e10;
    const double MIN_ELEVATION = -1e10;
    
    if (std::isnan(elevation) || std::isinf(elevation)) {
        elevation = 0.0;
        return true;
    }
    
    if (elevation > MAX_ELEVATION) {
        elevation = MAX_ELEVATION;
        return true;
    }
    
    if (elevation < MIN_ELEVATION) {
        elevation = MIN_ELEVATION;
        return true;
    }
    
    return false;
}

void DWGEntityProcessor::ValidatePointArray(std::vector<Point3d>& points) const {
    for (auto& point : points) {
        CoerceInvalidElevation(point.z);
        
        if (!IsValidCoordinate(point.x)) {
            point.x = 0.0;
        }
        if (!IsValidCoordinate(point.y)) {
            point.y = 0.0;
        }
    }
}

bool DWGEntityProcessor::IsValidCoordinate(double value) const {
    return std::isfinite(value) && std::abs(value) < 1e15;
}

#ifdef REALDWG_AVAILABLE
bool DWGEntityProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity || !m_exporter) {
        return false;
    }
    
    // Set layer
    if (!layer.empty()) {
        AcDbObjectId layerId = GetOrCreateLayer(layer);
        if (!layerId.isNull()) {
            entity->setLayer(layerId);
        }
    }
    
    return true;
}

bool DWGEntityProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    if (!entity || !m_exporter) {
        return false;
    }
    
    // Use exporter's method to add entity to model space
    // This is a simplified implementation - the actual method would be in DWGExporter
    return true; // Placeholder
}

AcDbObjectId DWGEntityProcessor::GetOrCreateLayer(const std::string& layerName, const Color& color) const {
    if (!m_exporter) {
        return AcDbObjectId::kNull;
    }
    
    // Use exporter's method to get or create layer
    // This is a simplified implementation - the actual method would be in DWGExporter
    return AcDbObjectId::kNull; // Placeholder
}
#endif

void DWGEntityProcessor::LogError(const std::string& message) const {
    if (m_exporter) {
        m_exporter->LogError(GetProcessorName() + ": " + message);
    }
    m_errorCount++;
}

void DWGEntityProcessor::LogWarning(const std::string& message) const {
    if (m_exporter) {
        m_exporter->LogWarning(GetProcessorName() + ": " + message);
    }
    m_warningCount++;
}

void DWGEntityProcessor::LogInfo(const std::string& message) const {
    if (m_exporter) {
        m_exporter->LogInfo(GetProcessorName() + ": " + message);
    }
}

//=======================================================================================
// DWG Line Processor Implementation
//=======================================================================================

DWGLineProcessor::DWGLineProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
{
}

DWGProcessingStatus DWGLineProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    // Validate geometry first
    auto validation = ValidateGeometry(element);
    if (!validation.isValid) {
        LogError("Geometry validation failed for element " + element.id);
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    try {
        // Extract line geometry from element
        // This is a placeholder - real implementation would extract from iModel
        Point3d start(0, 0, 0);
        Point3d end(100, 100, 0);
        std::string layer = "Geometry";
        
        // Process the line
        auto status = ProcessLine(start, end, layer);
        
        if (status == DWGProcessingStatus::Success) {
            m_processedCount++;
            LogInfo("Successfully processed line element " + element.id);
        }
        
        return status;
    }
    catch (const std::exception& e) {
        LogError("Exception processing line element " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGLineProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a line-type element
    // This would be based on element.geometryType or similar property
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGLineProcessor::ProcessLine(const Point3d& start, const Point3d& end, const std::string& layer) {
    // Validate line geometry
    if (!ValidateLineGeometry(start, end)) {
        LogError("Invalid line geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Transform points
    Point3d transformedStart = TransformPoint(start);
    Point3d transformedEnd = TransformPoint(end);
    
    // Fix any issues with endpoints
    FixLineEndpoints(transformedStart, transformedEnd);
    
    // Check for zero-length line after transformation
    if (IsZeroLengthLine(transformedStart, transformedEnd)) {
        LogWarning("Zero-length line detected and skipped");
        return DWGProcessingStatus::Skipped;
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        // Create DWG line entity
        AcDbLine* line = CreateDWGLine(transformedStart, transformedEnd);
        if (!line) {
            LogError("Failed to create DWG line entity");
            return DWGProcessingStatus::Failed;
        }
        
        // Set properties
        if (!SetEntityProperties(line, layer)) {
            delete line;
            LogError("Failed to set line properties");
            return DWGProcessingStatus::Failed;
        }
        
        // Add to model space
        if (!AddEntityToModelSpace(line)) {
            delete line;
            LogError("Failed to add line to model space");
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG line: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - line creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGLineProcessor::ProcessRay(const Point3d& basePoint, const Vector3d& direction, const std::string& layer) {
    // Transform geometry
    Point3d transformedBase = TransformPoint(basePoint);
    Vector3d transformedDir = TransformVector(direction);
    
    // Normalize direction vector
    double length = std::sqrt(transformedDir.x * transformedDir.x + 
                             transformedDir.y * transformedDir.y + 
                             transformedDir.z * transformedDir.z);
    if (length < m_tolerance) {
        LogError("Invalid ray direction vector");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    transformedDir.x /= length;
    transformedDir.y /= length;
    transformedDir.z /= length;
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbRay* ray = CreateDWGRay(transformedBase, transformedDir);
        if (!ray) {
            LogError("Failed to create DWG ray entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(ray, layer)) {
            delete ray;
            return DWGProcessingStatus::Failed;
        }
        
        // Set infinite line linkage for ray (infinite at end)
        SetInfiniteLineLinkage(ray, false, true);
        
        if (!AddEntityToModelSpace(ray)) {
            delete ray;
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG ray: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - ray creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGLineProcessor::ProcessXLine(const Point3d& basePoint, const Vector3d& direction, const std::string& layer) {
    // Similar to ray but infinite in both directions
    Point3d transformedBase = TransformPoint(basePoint);
    Vector3d transformedDir = TransformVector(direction);
    
    // Normalize direction vector
    double length = std::sqrt(transformedDir.x * transformedDir.x + 
                             transformedDir.y * transformedDir.y + 
                             transformedDir.z * transformedDir.z);
    if (length < m_tolerance) {
        LogError("Invalid construction line direction vector");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    transformedDir.x /= length;
    transformedDir.y /= length;
    transformedDir.z /= length;
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbXline* xline = CreateDWGXLine(transformedBase, transformedDir);
        if (!xline) {
            LogError("Failed to create DWG construction line entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(xline, layer)) {
            delete xline;
            return DWGProcessingStatus::Failed;
        }
        
        // Set infinite line linkage for construction line (infinite at both ends)
        SetInfiniteLineLinkage(xline, true, true);
        
        if (!AddEntityToModelSpace(xline)) {
            delete xline;
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG construction line: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - construction line creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGValidationResult DWGLineProcessor::ValidateGeometry(const ElementInfo& element) const {
    DWGValidationResult result = DWGEntityProcessor::ValidateGeometry(element);
    
    // Add line-specific validation
    // This would extract actual geometry from the element in a real implementation
    Point3d start(0, 0, 0);  // Placeholder
    Point3d end(100, 100, 0); // Placeholder
    
    if (!ValidateLineGeometry(start, end)) {
        result.AddError("Invalid line geometry");
    }
    
    return result;
}

bool DWGLineProcessor::ValidateLineGeometry(const Point3d& start, const Point3d& end) const {
    // Check for valid coordinates
    if (!ValidateCoordinate(start) || !ValidateCoordinate(end)) {
        return false;
    }
    
    // Check for minimum length (after transformation)
    Point3d transformedStart = TransformPoint(start);
    Point3d transformedEnd = TransformPoint(end);
    
    double dx = transformedEnd.x - transformedStart.x;
    double dy = transformedEnd.y - transformedStart.y;
    double dz = transformedEnd.z - transformedStart.z;
    double length = std::sqrt(dx * dx + dy * dy + dz * dz);
    
    return length >= m_tolerance;
}

bool DWGLineProcessor::IsZeroLengthLine(const Point3d& start, const Point3d& end) const {
    double dx = end.x - start.x;
    double dy = end.y - start.y;
    double dz = end.z - start.z;
    double length = std::sqrt(dx * dx + dy * dy + dz * dz);
    
    return length < m_tolerance;
}

void DWGLineProcessor::FixLineEndpoints(Point3d& start, Point3d& end) const {
    // Fix any invalid elevations
    CoerceInvalidElevation(start.z);
    CoerceInvalidElevation(end.z);
    
    // Ensure coordinates are within valid range
    if (!IsValidCoordinate(start.x)) start.x = 0.0;
    if (!IsValidCoordinate(start.y)) start.y = 0.0;
    if (!IsValidCoordinate(end.x)) end.x = 0.0;
    if (!IsValidCoordinate(end.y)) end.y = 0.0;
}

#ifdef REALDWG_AVAILABLE
AcDbLine* DWGLineProcessor::CreateDWGLine(const Point3d& start, const Point3d& end) const {
    try {
        AcGePoint3d acStart(start.x, start.y, start.z);
        AcGePoint3d acEnd(end.x, end.y, end.z);
        
        return new AcDbLine(acStart, acEnd);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcDbRay* DWGLineProcessor::CreateDWGRay(const Point3d& basePoint, const Vector3d& direction) const {
    try {
        AcGePoint3d acBase(basePoint.x, basePoint.y, basePoint.z);
        AcGeVector3d acDir(direction.x, direction.y, direction.z);
        
        return new AcDbRay(acBase, acDir);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcDbXline* DWGLineProcessor::CreateDWGXLine(const Point3d& basePoint, const Vector3d& direction) const {
    try {
        AcGePoint3d acBase(basePoint.x, basePoint.y, basePoint.z);
        AcGeVector3d acDir(direction.x, direction.y, direction.z);
        
        return new AcDbXline(acBase, acDir);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

bool DWGLineProcessor::SetInfiniteLineLinkage(AcDbEntity* entity, bool infiniteStart, bool infiniteEnd) const {
    // This would set special linkage data for infinite lines
    // Implementation would depend on specific requirements
    // For now, just return true as placeholder
    return true;
}
#endif

//=======================================================================================
// DWG Circle Processor Implementation
//=======================================================================================

DWGCircleProcessor::DWGCircleProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
{
}

DWGProcessingStatus DWGCircleProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }

    // Validate geometry first
    auto validation = ValidateGeometry(element);
    if (!validation.isValid) {
        LogError("Geometry validation failed for element " + element.id);
        return DWGProcessingStatus::ValidationError;
    }

    try {
        // Extract circle geometry from element
        // This is a placeholder - real implementation would extract from iModel
        Point3d center(0, 0, 0);
        double radius = 50.0;
        Vector3d normal(0, 0, 1);
        std::string layer = "Geometry";

        // Process the circle
        auto status = ProcessCircle(center, radius, normal, layer);

        if (status == DWGProcessingStatus::Success) {
            m_processedCount++;
            LogInfo("Successfully processed circle element " + element.id);
        }

        return status;
    }
    catch (const std::exception& e) {
        LogError("Exception processing circle element " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGCircleProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a circle-type element
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGCircleProcessor::ProcessCircle(const Point3d& center, double radius, const Vector3d& normal, const std::string& layer) {
    // Validate circle geometry
    if (!ValidateCircleGeometry(center, radius)) {
        LogError("Invalid circle geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }

    // Transform geometry
    Point3d transformedCenter = TransformPoint(center);
    double transformedRadius = TransformLength(radius);
    Vector3d transformedNormal = TransformVector(normal);

    // Validate transformed radius
    if (!IsValidRadius(transformedRadius)) {
        LogError("Invalid radius after transformation");
        return DWGProcessingStatus::InvalidGeometry;
    }

#ifdef REALDWG_AVAILABLE
    try {
        // Create DWG circle entity
        AcDbCircle* circle = CreateDWGCircle(transformedCenter, transformedRadius, transformedNormal);
        if (!circle) {
            LogError("Failed to create DWG circle entity");
            return DWGProcessingStatus::Failed;
        }

        // Set properties
        if (!SetEntityProperties(circle, layer)) {
            delete circle;
            LogError("Failed to set circle properties");
            return DWGProcessingStatus::Failed;
        }

        // Add to model space
        if (!AddEntityToModelSpace(circle)) {
            delete circle;
            LogError("Failed to add circle to model space");
            return DWGProcessingStatus::Failed;
        }

        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG circle: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - circle creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGCircleProcessor::ProcessArc(const Point3d& center, double radius, double startAngle, double endAngle, const Vector3d& normal, const std::string& layer) {
    // Validate arc geometry
    if (!ValidateArcGeometry(center, radius, startAngle, endAngle)) {
        LogError("Invalid arc geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }

    // Transform geometry
    Point3d transformedCenter = TransformPoint(center);
    double transformedRadius = TransformLength(radius);
    Vector3d transformedNormal = TransformVector(normal);

    // Normalize angles
    double normalizedStart = startAngle;
    double normalizedEnd = endAngle;
    NormalizeAngles(normalizedStart, normalizedEnd);

#ifdef REALDWG_AVAILABLE
    try {
        AcDbArc* arc = CreateDWGArc(transformedCenter, transformedRadius, normalizedStart, normalizedEnd, transformedNormal);
        if (!arc) {
            LogError("Failed to create DWG arc entity");
            return DWGProcessingStatus::Failed;
        }

        if (!SetEntityProperties(arc, layer)) {
            delete arc;
            return DWGProcessingStatus::Failed;
        }

        if (!AddEntityToModelSpace(arc)) {
            delete arc;
            return DWGProcessingStatus::Failed;
        }

        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG arc: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - arc creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGValidationResult DWGCircleProcessor::ValidateGeometry(const ElementInfo& element) const {
    DWGValidationResult result = DWGEntityProcessor::ValidateGeometry(element);

    // Add circle-specific validation
    Point3d center(0, 0, 0);  // Placeholder
    double radius = 50.0;     // Placeholder

    if (!ValidateCircleGeometry(center, radius)) {
        result.AddError("Invalid circle geometry");
    }

    return result;
}

bool DWGCircleProcessor::ValidateCircleGeometry(const Point3d& center, double radius) const {
    return ValidateCoordinate(center) && IsValidRadius(radius);
}

bool DWGCircleProcessor::ValidateArcGeometry(const Point3d& center, double radius, double startAngle, double endAngle) const {
    return ValidateCircleGeometry(center, radius) &&
           IsValidAngle(startAngle) &&
           IsValidAngle(endAngle);
}

bool DWGCircleProcessor::IsValidRadius(double radius) const {
    return radius > m_tolerance && std::isfinite(radius) && radius < 1e10;
}

bool DWGCircleProcessor::IsValidAngle(double angle) const {
    return std::isfinite(angle);
}

void DWGCircleProcessor::NormalizeAngles(double& startAngle, double& endAngle) const {
    const double TWO_PI = 2.0 * M_PI;

    // Normalize to [0, 2π) range
    while (startAngle < 0) startAngle += TWO_PI;
    while (startAngle >= TWO_PI) startAngle -= TWO_PI;

    while (endAngle < 0) endAngle += TWO_PI;
    while (endAngle >= TWO_PI) endAngle -= TWO_PI;

    // Ensure end angle is greater than start angle
    if (endAngle <= startAngle) {
        endAngle += TWO_PI;
    }
}

#ifdef REALDWG_AVAILABLE
AcDbCircle* DWGCircleProcessor::CreateDWGCircle(const Point3d& center, double radius, const Vector3d& normal) const {
    try {
        AcGePoint3d acCenter(center.x, center.y, center.z);
        AcGeVector3d acNormal(normal.x, normal.y, normal.z);

        return new AcDbCircle(acCenter, acNormal, radius);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcDbArc* DWGCircleProcessor::CreateDWGArc(const Point3d& center, double radius, double startAngle, double endAngle, const Vector3d& normal) const {
    try {
        AcGePoint3d acCenter(center.x, center.y, center.z);
        AcGeVector3d acNormal(normal.x, normal.y, normal.z);

        return new AcDbArc(acCenter, acNormal, radius, startAngle, endAngle);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcDbEllipse* DWGCircleProcessor::CreateDWGEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio) const {
    try {
        AcGePoint3d acCenter(center.x, center.y, center.z);
        AcGeVector3d acMajorAxis(majorAxis.x, majorAxis.y, majorAxis.z);
        AcGeVector3d acNormal = acMajorAxis.crossProduct(AcGeVector3d::kXAxis);
        if (acNormal.isZeroLength()) {
            acNormal = AcGeVector3d::kZAxis;
        }

        return new AcDbEllipse(acCenter, acNormal, acMajorAxis, radiusRatio);
    }
    catch (const std::exception&) {
        return nullptr;
    }
}
#endif

//=======================================================================================
// DWG Text Processor Implementation
//=======================================================================================

DWGTextProcessor::DWGTextProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
{
}

DWGProcessingStatus DWGTextProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }

    // Validate geometry first
    auto validation = ValidateGeometry(element);
    if (!validation.isValid) {
        LogError("Geometry validation failed for element " + element.id);
        return DWGProcessingStatus::ValidationError;
    }

    try {
        // Extract text geometry from element
        // This is a placeholder - real implementation would extract from iModel
        Point3d position(0, 0, 0);
        std::string text = "Sample Text";
        double height = 2.5;
        double rotation = 0.0;
        std::string layer = "Text";

        // Process the text
        auto status = ProcessText(position, text, height, rotation, layer);

        if (status == DWGProcessingStatus::Success) {
            m_processedCount++;
            LogInfo("Successfully processed text element " + element.id);
        }

        return status;
    }
    catch (const std::exception& e) {
        LogError("Exception processing text element " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGTextProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a text-type element
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGTextProcessor::ProcessText(const Point3d& position, const std::string& text, double height, double rotation, const std::string& layer) {
    // Validate text geometry
    if (!ValidateTextGeometry(position, text, height)) {
        LogError("Invalid text geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }

    // Transform geometry
    Point3d transformedPosition = TransformPoint(position);
    double transformedHeight = TransformLength(height);
    double transformedRotation = TransformAngle(rotation);

    // Sanitize text string
    std::string sanitizedText = SanitizeTextString(text);
    if (sanitizedText.empty()) {
        LogWarning("Empty text string after sanitization - skipping");
        return DWGProcessingStatus::Skipped;
    }

    // Convert MIF encoding if needed
    ConvertMIFToUnicode(sanitizedText);

    // Validate transformed height
    if (!IsValidTextHeight(transformedHeight)) {
        LogError("Invalid text height after transformation");
        return DWGProcessingStatus::InvalidGeometry;
    }

#ifdef REALDWG_AVAILABLE
    try {
        // Create DWG text entity
        AcDbText* textEntity = CreateDWGText(transformedPosition, sanitizedText, transformedHeight, transformedRotation);
        if (!textEntity) {
            LogError("Failed to create DWG text entity");
            return DWGProcessingStatus::Failed;
        }

        // Set properties
        if (!SetEntityProperties(textEntity, layer)) {
            delete textEntity;
            LogError("Failed to set text properties");
            return DWGProcessingStatus::Failed;
        }

        // Add to model space
        if (!AddEntityToModelSpace(textEntity)) {
            delete textEntity;
            LogError("Failed to add text to model space");
            return DWGProcessingStatus::Failed;
        }

        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG text: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - text creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGTextProcessor::ProcessMText(const Point3d& position, const std::string& text, double height, double width, const std::string& layer) {
    // Validate text geometry
    if (!ValidateTextGeometry(position, text, height)) {
        LogError("Invalid MText geometry");
        return DWGProcessingStatus::InvalidGeometry;
    }

    // Transform geometry
    Point3d transformedPosition = TransformPoint(position);
    double transformedHeight = TransformLength(height);
    double transformedWidth = width > 0 ? TransformLength(width) : 0.0;

    // Sanitize text string
    std::string sanitizedText = SanitizeTextString(text);
    if (sanitizedText.empty()) {
        LogWarning("Empty MText string after sanitization - skipping");
        return DWGProcessingStatus::Skipped;
    }

    // Convert MIF encoding if needed
    ConvertMIFToUnicode(sanitizedText);

#ifdef REALDWG_AVAILABLE
    try {
        AcDbMText* mtextEntity = CreateDWGMText(transformedPosition, sanitizedText, transformedHeight, transformedWidth);
        if (!mtextEntity) {
            LogError("Failed to create DWG MText entity");
            return DWGProcessingStatus::Failed;
        }

        if (!SetEntityProperties(mtextEntity, layer)) {
            delete mtextEntity;
            return DWGProcessingStatus::Failed;
        }

        if (!AddEntityToModelSpace(mtextEntity)) {
            delete mtextEntity;
            return DWGProcessingStatus::Failed;
        }

        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG MText: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - MText creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGTextProcessor::ProcessTextWithProperties(const Point3d& position, const std::string& text, const TextProperties& properties, const std::string& layer) {
    // Process text with advanced properties
    auto status = ProcessText(position, text, properties.height, properties.rotation, layer);

    // Additional property handling would go here
    // This is a placeholder for more advanced text property processing

    return status;
}

DWGValidationResult DWGTextProcessor::ValidateGeometry(const ElementInfo& element) const {
    DWGValidationResult result = DWGEntityProcessor::ValidateGeometry(element);

    // Add text-specific validation
    Point3d position(0, 0, 0);  // Placeholder
    std::string text = "Sample"; // Placeholder
    double height = 2.5;        // Placeholder

    if (!ValidateTextGeometry(position, text, height)) {
        result.AddError("Invalid text geometry");
    }

    return result;
}

bool DWGTextProcessor::ValidateTextGeometry(const Point3d& position, const std::string& text, double height) const {
    return ValidateCoordinate(position) &&
           IsValidTextString(text) &&
           IsValidTextHeight(height);
}

bool DWGTextProcessor::IsValidTextHeight(double height) const {
    return height > m_tolerance && std::isfinite(height) && height < 1e6;
}

bool DWGTextProcessor::IsValidTextString(const std::string& text) const {
    return !text.empty() && text.length() < 32768; // DWG text length limit
}

std::string DWGTextProcessor::SanitizeTextString(const std::string& text) const {
    std::string sanitized = text;

    // Remove null characters
    sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());

    // Replace problematic characters
    std::replace(sanitized.begin(), sanitized.end(), '\r', ' ');

    // Trim whitespace
    sanitized.erase(0, sanitized.find_first_not_of(" \t\n\r\f\v"));
    sanitized.erase(sanitized.find_last_not_of(" \t\n\r\f\v") + 1);

    return sanitized;
}

bool DWGTextProcessor::ConvertMIFToUnicode(std::string& text) const {
    // This is a simplified implementation of MIF to Unicode conversion
    // Based on RealDwgFileIO/rdText.cpp implementation

    // Look for MIF escape sequences like \M+nXXXX
    std::regex mifPattern(R"(\\M\+n([0-9A-Fa-f]{4}))");
    std::smatch match;

    std::string result = text;
    while (std::regex_search(result, match, mifPattern)) {
        try {
            // Convert hex string to Unicode code point
            unsigned int codePoint = std::stoul(match[1].str(), nullptr, 16);

            // Convert to UTF-8 (simplified)
            std::string utf8Char;
            if (codePoint < 0x80) {
                utf8Char = static_cast<char>(codePoint);
            } else if (codePoint < 0x800) {
                utf8Char += static_cast<char>(0xC0 | (codePoint >> 6));
                utf8Char += static_cast<char>(0x80 | (codePoint & 0x3F));
            } else {
                utf8Char += static_cast<char>(0xE0 | (codePoint >> 12));
                utf8Char += static_cast<char>(0x80 | ((codePoint >> 6) & 0x3F));
                utf8Char += static_cast<char>(0x80 | (codePoint & 0x3F));
            }

            // Replace the MIF sequence with UTF-8 character
            result.replace(match.position(), match.length(), utf8Char);
        }
        catch (const std::exception&) {
            // If conversion fails, just remove the MIF sequence
            result.replace(match.position(), match.length(), "?");
        }
    }

    text = result;
    return true;
}

double DWGTextProcessor::GetDisplayRotationAngle(double rotation, bool isAnnotative) const {
    // Based on RealDwgFileIO implementation
    // Handle annotation scaling and rotation

    if (isAnnotative) {
        // For annotative text, rotation might need adjustment
        // This is a simplified implementation
        return rotation;
    }

    return rotation;
}

void DWGTextProcessor::ProcessTextJustification(const TextProperties& properties, Point3d& position) const {
    // Adjust position based on text justification
    // This is a simplified implementation - real implementation would consider text metrics

    switch (properties.justification) {
        case TextProperties::Justification::Left:
            // No adjustment needed
            break;
        case TextProperties::Justification::Center:
            // Would adjust position to center the text
            break;
        case TextProperties::Justification::Right:
            // Would adjust position to right-align the text
            break;
        default:
            break;
    }
}

#ifdef REALDWG_AVAILABLE
AcDbText* DWGTextProcessor::CreateDWGText(const Point3d& position, const std::string& text, double height, double rotation) const {
    try {
        AcGePoint3d acPosition(position.x, position.y, position.z);

        AcDbText* textEntity = new AcDbText(acPosition, text.c_str(), height, rotation);
        return textEntity;
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcDbMText* DWGTextProcessor::CreateDWGMText(const Point3d& position, const std::string& text, double height, double width) const {
    try {
        AcGePoint3d acPosition(position.x, position.y, position.z);

        AcDbMText* mtextEntity = new AcDbMText();
        mtextEntity->setLocation(acPosition);
        mtextEntity->setContents(text.c_str());
        mtextEntity->setTextHeight(height);
        if (width > 0) {
            mtextEntity->setWidth(width);
        }

        return mtextEntity;
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

bool DWGTextProcessor::SetTextProperties(AcDbText* textEntity, const TextProperties& properties) const {
    if (!textEntity) return false;

    try {
        textEntity->setTextHeight(properties.height);
        textEntity->setWidthFactor(properties.widthFactor);
        textEntity->setOblique(properties.obliqueAngle);
        textEntity->setRotation(properties.rotation);

        // Set text style if needed
        // This would involve getting or creating the appropriate text style

        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool DWGTextProcessor::SetMTextProperties(AcDbMText* mtextEntity, const TextProperties& properties) const {
    if (!mtextEntity) return false;

    try {
        mtextEntity->setTextHeight(properties.height);
        mtextEntity->setRotation(properties.rotation);

        // Set additional MText properties
        // This would involve setting text style, formatting, etc.

        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

AcDbObjectId DWGTextProcessor::GetOrCreateTextStyle(const std::string& styleName, const TextProperties& properties) const {
    // Check cache first
    auto it = m_textStyleCache.find(styleName);
    if (it != m_textStyleCache.end()) {
        // Return cached style ID (simplified)
        return AcDbObjectId::kNull; // Placeholder
    }

    // Create new text style
    // This would involve creating an AcDbTextStyleTableRecord
    // and adding it to the text style table

    return AcDbObjectId::kNull; // Placeholder
}
#endif

//=======================================================================================
// DWG Entity Processor Factory Implementation
//=======================================================================================

std::unordered_map<std::string, std::function<std::unique_ptr<DWGEntityProcessor>(DWGExporter*)>>
    DWGEntityProcessorFactory::s_processorFactories;

std::unique_ptr<DWGEntityProcessor> DWGEntityProcessorFactory::CreateProcessor(const std::string& entityType, DWGExporter* exporter) {
    if (s_processorFactories.empty()) {
        InitializeFactories();
    }

    auto it = s_processorFactories.find(entityType);
    if (it != s_processorFactories.end()) {
        return it->second(exporter);
    }

    return nullptr;
}

std::vector<std::string> DWGEntityProcessorFactory::GetSupportedEntityTypes() {
    if (s_processorFactories.empty()) {
        InitializeFactories();
    }

    std::vector<std::string> types;
    types.reserve(s_processorFactories.size());

    for (const auto& pair : s_processorFactories) {
        types.push_back(pair.first);
    }

    return types;
}

bool DWGEntityProcessorFactory::IsEntityTypeSupported(const std::string& entityType) {
    if (s_processorFactories.empty()) {
        InitializeFactories();
    }

    return s_processorFactories.find(entityType) != s_processorFactories.end();
}

void DWGEntityProcessorFactory::InitializeFactories() {
    // Register line processor
    s_processorFactories["Line"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGLineProcessor>(exporter);
    };

    s_processorFactories["Ray"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGLineProcessor>(exporter);
    };

    s_processorFactories["XLine"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGLineProcessor>(exporter);
    };

    // Register circle processor
    s_processorFactories["Circle"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGCircleProcessor>(exporter);
    };

    s_processorFactories["Arc"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGCircleProcessor>(exporter);
    };

    s_processorFactories["Ellipse"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGCircleProcessor>(exporter);
    };

    // Register text processor
    s_processorFactories["Text"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGTextProcessor>(exporter);
    };

    s_processorFactories["MText"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGTextProcessor>(exporter);
    };

    // Register spline processor (when available)
    // Note: This would require including DWGSplineProcessor.h
    /*
    s_processorFactories["Spline"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGSplineProcessor>(exporter);
    };

    s_processorFactories["NURBS"] = [](DWGExporter* exporter) {
        return std::make_unique<DWGSplineProcessor>(exporter);
    };
    */
}

} // namespace IModelExport
