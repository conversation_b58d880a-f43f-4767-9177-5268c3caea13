#include <gtest/gtest.h>
#include "../../src/formats/dwg/entities/DWGEntityProcessor.h"
#include "../../src/formats/dwg/entities/DWGSplineProcessor.h"
#include "../../src/formats/dwg/DWGExporter.h"
#include "../../include/ExportTypes.h"

using namespace IModelExport;

//=======================================================================================
// Mock DWG Exporter for Testing
//=======================================================================================

class MockDWGExporter : public DWGExporter {
public:
    MockDWGExporter() : DWGExporter() {}
    
    // Override methods for testing
    Point3d TransformPoint(const Point3d& point) const override {
        return point; // No transformation for testing
    }
    
    Vector3d TransformVector(const Vector3d& vector) const override {
        return vector; // No transformation for testing
    }
    
    double TransformLength(double length) const override {
        return length; // No transformation for testing
    }
    
    void LogError(const std::string& message) override {
        m_testErrors.push_back(message);
    }
    
    void LogWarning(const std::string& message) override {
        m_testWarnings.push_back(message);
    }
    
    void LogInfo(const std::string& message) override {
        m_testInfos.push_back(message);
    }
    
    // Test accessors
    const std::vector<std::string>& GetTestErrors() const { return m_testErrors; }
    const std::vector<std::string>& GetTestWarnings() const { return m_testWarnings; }
    const std::vector<std::string>& GetTestInfos() const { return m_testInfos; }
    
    void ClearTestLogs() {
        m_testErrors.clear();
        m_testWarnings.clear();
        m_testInfos.clear();
    }

private:
    std::vector<std::string> m_testErrors;
    std::vector<std::string> m_testWarnings;
    std::vector<std::string> m_testInfos;
};

//=======================================================================================
// Test Fixtures
//=======================================================================================

class DWGEntityProcessorTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_mockExporter = std::make_unique<MockDWGExporter>();
    }
    
    void TearDown() override {
        m_mockExporter.reset();
    }
    
    ElementInfo CreateTestElement(ElementType type, const std::string& id = "test_element") {
        ElementInfo element;
        element.id = id;
        element.type = type;
        return element;
    }
    
    std::unique_ptr<MockDWGExporter> m_mockExporter;
};

//=======================================================================================
// DWG Line Processor Tests
//=======================================================================================

class DWGLineProcessorTest : public DWGEntityProcessorTest {
protected:
    void SetUp() override {
        DWGEntityProcessorTest::SetUp();
        m_lineProcessor = std::make_unique<DWGLineProcessor>(m_mockExporter.get());
    }
    
    std::unique_ptr<DWGLineProcessor> m_lineProcessor;
};

TEST_F(DWGLineProcessorTest, CanProcessGeometricElement) {
    ElementInfo element = CreateTestElement(ElementType::GeometricElement);
    EXPECT_TRUE(m_lineProcessor->CanProcessEntity(element));
}

TEST_F(DWGLineProcessorTest, CannotProcessNonGeometricElement) {
    ElementInfo element = CreateTestElement(ElementType::InformationElement);
    EXPECT_FALSE(m_lineProcessor->CanProcessEntity(element));
}

TEST_F(DWGLineProcessorTest, ValidateLineGeometry) {
    Point3d start(0, 0, 0);
    Point3d end(100, 100, 0);
    
    EXPECT_TRUE(m_lineProcessor->ValidateLineGeometry(start, end));
}

TEST_F(DWGLineProcessorTest, ValidateZeroLengthLine) {
    Point3d start(0, 0, 0);
    Point3d end(0, 0, 0);
    
    EXPECT_FALSE(m_lineProcessor->ValidateLineGeometry(start, end));
}

TEST_F(DWGLineProcessorTest, ProcessValidLine) {
    Point3d start(0, 0, 0);
    Point3d end(100, 100, 0);
    
    auto status = m_lineProcessor->ProcessLine(start, end, "TestLayer");
    
    // Without RealDWG, this should return Skipped
    EXPECT_EQ(status, DWGProcessingStatus::Skipped);
    EXPECT_FALSE(m_mockExporter->GetTestWarnings().empty());
}

TEST_F(DWGLineProcessorTest, ProcessInvalidLine) {
    Point3d start(std::numeric_limits<double>::infinity(), 0, 0);
    Point3d end(100, 100, 0);
    
    auto status = m_lineProcessor->ProcessLine(start, end, "TestLayer");
    
    EXPECT_EQ(status, DWGProcessingStatus::InvalidGeometry);
    EXPECT_FALSE(m_mockExporter->GetTestErrors().empty());
}

//=======================================================================================
// DWG Circle Processor Tests
//=======================================================================================

class DWGCircleProcessorTest : public DWGEntityProcessorTest {
protected:
    void SetUp() override {
        DWGEntityProcessorTest::SetUp();
        m_circleProcessor = std::make_unique<DWGCircleProcessor>(m_mockExporter.get());
    }
    
    std::unique_ptr<DWGCircleProcessor> m_circleProcessor;
};

TEST_F(DWGCircleProcessorTest, ValidateCircleGeometry) {
    Point3d center(0, 0, 0);
    double radius = 50.0;
    
    EXPECT_TRUE(m_circleProcessor->ValidateCircleGeometry(center, radius));
}

TEST_F(DWGCircleProcessorTest, ValidateInvalidRadius) {
    Point3d center(0, 0, 0);
    double radius = -10.0;
    
    EXPECT_FALSE(m_circleProcessor->ValidateCircleGeometry(center, radius));
}

TEST_F(DWGCircleProcessorTest, ValidateZeroRadius) {
    Point3d center(0, 0, 0);
    double radius = 0.0;
    
    EXPECT_FALSE(m_circleProcessor->ValidateCircleGeometry(center, radius));
}

TEST_F(DWGCircleProcessorTest, ProcessValidCircle) {
    Point3d center(0, 0, 0);
    double radius = 50.0;
    Vector3d normal(0, 0, 1);
    
    auto status = m_circleProcessor->ProcessCircle(center, radius, normal, "TestLayer");
    
    // Without RealDWG, this should return Skipped
    EXPECT_EQ(status, DWGProcessingStatus::Skipped);
}

//=======================================================================================
// DWG Text Processor Tests
//=======================================================================================

class DWGTextProcessorTest : public DWGEntityProcessorTest {
protected:
    void SetUp() override {
        DWGEntityProcessorTest::SetUp();
        m_textProcessor = std::make_unique<DWGTextProcessor>(m_mockExporter.get());
    }
    
    std::unique_ptr<DWGTextProcessor> m_textProcessor;
};

TEST_F(DWGTextProcessorTest, ValidateTextGeometry) {
    Point3d position(0, 0, 0);
    std::string text = "Test Text";
    double height = 2.5;
    
    EXPECT_TRUE(m_textProcessor->ValidateTextGeometry(position, text, height));
}

TEST_F(DWGTextProcessorTest, ValidateEmptyText) {
    Point3d position(0, 0, 0);
    std::string text = "";
    double height = 2.5;
    
    EXPECT_FALSE(m_textProcessor->ValidateTextGeometry(position, text, height));
}

TEST_F(DWGTextProcessorTest, ValidateInvalidHeight) {
    Point3d position(0, 0, 0);
    std::string text = "Test Text";
    double height = -1.0;
    
    EXPECT_FALSE(m_textProcessor->ValidateTextGeometry(position, text, height));
}

TEST_F(DWGTextProcessorTest, ProcessValidText) {
    Point3d position(0, 0, 0);
    std::string text = "Test Text";
    double height = 2.5;
    
    auto status = m_textProcessor->ProcessText(position, text, height, 0.0, "TestLayer");
    
    // Without RealDWG, this should return Skipped
    EXPECT_EQ(status, DWGProcessingStatus::Skipped);
}

//=======================================================================================
// DWG Spline Processor Tests
//=======================================================================================

class DWGSplineProcessorTest : public DWGEntityProcessorTest {
protected:
    void SetUp() override {
        DWGEntityProcessorTest::SetUp();
        m_splineProcessor = std::make_unique<DWGSplineProcessor>(m_mockExporter.get());
    }
    
    SplineGeometry CreateTestSpline() {
        SplineGeometry geometry;
        geometry.degree = 3;
        geometry.controlPoints = {
            Point3d(0, 0, 0),
            Point3d(33.33, 33.33, 0),
            Point3d(66.67, 33.33, 0),
            Point3d(100, 0, 0)
        };
        geometry.knots = {0, 0, 0, 0, 1, 1, 1, 1}; // Clamped knot vector
        geometry.isRational = false;
        geometry.isPeriodic = false;
        geometry.isClosed = false;
        return geometry;
    }
    
    std::unique_ptr<DWGSplineProcessor> m_splineProcessor;
};

TEST_F(DWGSplineProcessorTest, ValidateSplineGeometry) {
    SplineGeometry geometry = CreateTestSpline();
    
    auto result = m_splineProcessor->ValidateSplineGeometry(geometry);
    EXPECT_TRUE(result.isValid);
}

TEST_F(DWGSplineProcessorTest, ValidateInvalidDegree) {
    SplineGeometry geometry = CreateTestSpline();
    geometry.degree = 0; // Invalid degree
    
    auto result = m_splineProcessor->ValidateSplineGeometry(geometry);
    EXPECT_FALSE(result.isValid);
    EXPECT_FALSE(result.errors.empty());
}

TEST_F(DWGSplineProcessorTest, ValidateInsufficientControlPoints) {
    SplineGeometry geometry = CreateTestSpline();
    geometry.controlPoints.resize(2); // Insufficient for degree 3
    
    auto result = m_splineProcessor->ValidateSplineGeometry(geometry);
    EXPECT_FALSE(result.isValid);
}

TEST_F(DWGSplineProcessorTest, ValidateKnotVector) {
    std::vector<double> knots = {0, 0, 0, 0, 1, 1, 1, 1};
    int degree = 3;
    int numControlPoints = 4;
    
    EXPECT_TRUE(m_splineProcessor->ValidateKnotVector(knots, degree, numControlPoints));
}

TEST_F(DWGSplineProcessorTest, ValidateInvalidKnotVector) {
    std::vector<double> knots = {0, 0, 0, 1, 1, 1}; // Wrong number of knots
    int degree = 3;
    int numControlPoints = 4;
    
    EXPECT_FALSE(m_splineProcessor->ValidateKnotVector(knots, degree, numControlPoints));
}

//=======================================================================================
// Spline Utility Tests
//=======================================================================================

TEST(SplineUtilsTest, CalculateChordLength) {
    std::vector<Point3d> points = {
        Point3d(0, 0, 0),
        Point3d(10, 0, 0),
        Point3d(10, 10, 0),
        Point3d(0, 10, 0)
    };
    
    double length = SplineUtils::CalculateChordLength(points);
    EXPECT_NEAR(length, 30.0, 1e-6); // 10 + 10 + 10
}

TEST(SplineUtilsTest, ArePointsCollinear) {
    std::vector<Point3d> collinearPoints = {
        Point3d(0, 0, 0),
        Point3d(10, 10, 0),
        Point3d(20, 20, 0)
    };
    
    EXPECT_TRUE(SplineUtils::ArePointsCollinear(collinearPoints, 1e-6));
    
    std::vector<Point3d> nonCollinearPoints = {
        Point3d(0, 0, 0),
        Point3d(10, 10, 0),
        Point3d(20, 15, 0)
    };
    
    EXPECT_FALSE(SplineUtils::ArePointsCollinear(nonCollinearPoints, 1e-6));
}

TEST(SplineUtilsTest, ValidateSplineParameters) {
    EXPECT_TRUE(SplineUtils::ValidateSplineParameters(3, 4, 8)); // degree=3, 4 control points, 8 knots
    EXPECT_FALSE(SplineUtils::ValidateSplineParameters(3, 2, 6)); // Insufficient control points
    EXPECT_FALSE(SplineUtils::ValidateSplineParameters(0, 4, 5)); // Invalid degree
}

//=======================================================================================
// Entity Processor Factory Tests
//=======================================================================================

TEST(DWGEntityProcessorFactoryTest, CreateLineProcessor) {
    MockDWGExporter exporter;
    auto processor = DWGEntityProcessorFactory::CreateProcessor("Line", &exporter);
    
    ASSERT_NE(processor, nullptr);
    EXPECT_EQ(processor->GetProcessorName(), "DWGLineProcessor");
}

TEST(DWGEntityProcessorFactoryTest, CreateCircleProcessor) {
    MockDWGExporter exporter;
    auto processor = DWGEntityProcessorFactory::CreateProcessor("Circle", &exporter);
    
    ASSERT_NE(processor, nullptr);
    EXPECT_EQ(processor->GetProcessorName(), "DWGCircleProcessor");
}

TEST(DWGEntityProcessorFactoryTest, CreateTextProcessor) {
    MockDWGExporter exporter;
    auto processor = DWGEntityProcessorFactory::CreateProcessor("Text", &exporter);
    
    ASSERT_NE(processor, nullptr);
    EXPECT_EQ(processor->GetProcessorName(), "DWGTextProcessor");
}

TEST(DWGEntityProcessorFactoryTest, CreateUnsupportedProcessor) {
    MockDWGExporter exporter;
    auto processor = DWGEntityProcessorFactory::CreateProcessor("UnsupportedType", &exporter);
    
    EXPECT_EQ(processor, nullptr);
}

TEST(DWGEntityProcessorFactoryTest, GetSupportedEntityTypes) {
    auto types = DWGEntityProcessorFactory::GetSupportedEntityTypes();
    
    EXPECT_FALSE(types.empty());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "Line") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "Circle") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "Text") != types.end());
}

TEST(DWGEntityProcessorFactoryTest, IsEntityTypeSupported) {
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Line"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Circle"));
    EXPECT_TRUE(DWGEntityProcessorFactory::IsEntityTypeSupported("Text"));
    EXPECT_FALSE(DWGEntityProcessorFactory::IsEntityTypeSupported("UnsupportedType"));
}
