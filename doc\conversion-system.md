# Conversion System Architecture

## Overview

The conversion system is the heart of the RealDwgFileIO framework, providing bidirectional translation between AutoCAD DWG/DXF and Bentley DGN formats. The system is designed with symmetric conversion contexts and extensible entity mapping.

## Conversion Flow Architecture

```
DWG/DXF File                                    DGN File
     │                                              │
     ▼                                              ▼
┌─────────────┐                              ┌─────────────┐
│  RealDWG    │                              │ DgnPlatform │
│  Database   │                              │   Model     │
└─────────────┘                              └─────────────┘
     │                                              │
     ▼                                              ▼
┌─────────────────────────────────────────────────────────┐
│              FileHolder (Central Hub)                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ ID Mapping  │  │ Symbology   │  │ Model Index │    │
│  │   Tables    │  │   Cache     │  │   Items     │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
     │                                              │
     ▼                                              ▼
┌─────────────┐                              ┌─────────────┐
│ConvertToDgn │◄────── Conversion ──────────►│ConvertFrom  │
│  Context    │        Contexts              │ DgnContext  │
└─────────────┘                              └─────────────┘
     │                                              │
     ▼                                              ▼
┌─────────────────────────────────────────────────────────┐
│                Entity Converters                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ Geometric   │  │  Complex    │  │    3D       │    │
│  │ Entities    │  │ Entities    │  │ Entities    │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
```

## Conversion Contexts

### ConvertToDgnContext (DWG → DGN)

**Primary Responsibilities**:
1. **Entity Iteration**: Traverse AutoCAD database entities
2. **Coordinate Transformation**: Apply DWG to DGN coordinate transforms
3. **Element Creation**: Generate appropriate DGN elements
4. **Symbology Mapping**: Convert AutoCAD symbology to DGN equivalents

**Key Processing Steps**:
```cpp
StatusInt LoadModelFromDatabase(DgnModelP seedModel, RealDwgModelIndexItem* pModelIndexItem)
{
    // 1. Initialize conversion context
    SetupTransformations();
    InitializeSymbologyMappings();
    
    // 2. Iterate through database entities
    AcDbBlockTableRecordIterator* iterator;
    for (iterator->start(); !iterator->done(); iterator->step())
    {
        AcDbEntity* entity;
        iterator->getEntity(entity, AcDb::kForRead);
        
        // 3. Convert entity to DGN element
        MSElementDescrP elementDescr;
        SaveElementToDgn(entity, &elementDescr);
        
        // 4. Add to DGN model
        if (elementDescr)
            elementDescr->AddToModel(seedModel);
    }
    
    // 5. Process views and layouts
    SaveViewGroupsToDgn();
    
    return SUCCESS;
}
```

### ConvertFromDgnContext (DGN → DWG)

**Primary Responsibilities**:
1. **Element Iteration**: Traverse DGN model elements
2. **Coordinate Transformation**: Apply DGN to DWG coordinate transforms  
3. **Entity Creation**: Generate appropriate AutoCAD entities
4. **Database Management**: Insert entities into AutoCAD database

**Key Processing Steps**:
```cpp
StatusInt SaveNonCacheChanges(bool doFullSave, DgnFileP dgnFile)
{
    // 1. Initialize conversion context
    SetupTransformations();
    InitializeSymbologyMappings();
    
    // 2. Iterate through DGN elements
    DgnModel::ElementsCollection elements = m_model->GetElementsCollection();
    for (ElementHandle eh : elements)
    {
        // 3. Convert element to AutoCAD entity
        AcDbObjectId objectId;
        SaveElementToDatabase(eh, &objectId);
    }
    
    // 4. Process views and references
    SaveViewsToDatabase();
    SaveReferencesToDatabase();
    
    return SUCCESS;
}
```

## Entity Conversion Architecture

### Conversion Dispatch System

The framework uses a dispatch system to route entities to appropriate converters:

```cpp
void ConvertToDgnContext::SaveElementToDgn(AcDbEntity* pEntity, MSElementDescrP* ppDescr)
{
    AcRxClass* entityClass = pEntity->isA();
    
    if (entityClass == AcDbLine::desc())
        ConvertLine(pEntity, ppDescr);
    else if (entityClass == AcDbArc::desc())
        ConvertArc(pEntity, ppDescr);
    else if (entityClass == AcDbCircle::desc())
        ConvertCircle(pEntity, ppDescr);
    // ... additional entity types
    else
        ConvertGenericEntity(pEntity, ppDescr); // Extension point
}
```

### Entity Converter Pattern

Each entity type follows a consistent conversion pattern:

```cpp
// Example: Line conversion (rdLine.cpp)
void ConvertToDgnContext::ConvertLine(AcDbLine* acLine, MSElementDescrP* ppDescr)
{
    // 1. Extract geometry
    AcGePoint3d startPoint, endPoint;
    acLine->getStartPoint(startPoint);
    acLine->getEndPoint(endPoint);
    
    // 2. Transform coordinates
    DPoint3d dgnStart, dgnEnd;
    TransformToDgn(startPoint, dgnStart);
    TransformToDgn(endPoint, dgnEnd);
    
    // 3. Create DGN element
    MSElementDescr* lineDescr;
    mdlLine_create(&lineDescr, NULL, &dgnStart, &dgnEnd);
    
    // 4. Apply symbology
    ApplySymbologyToDgnElement(lineDescr, acLine);
    
    *ppDescr = lineDescr;
}
```

## Coordinate Transformation System

### Transformation Pipeline

The framework handles multiple coordinate system transformations:

1. **Unit Conversion**: Between different measurement systems
2. **Scale Transformation**: DWG drawing units to DGN storage units
3. **Origin Translation**: Coordinate system origin differences
4. **Axis Orientation**: Handle different axis conventions

```cpp
class ConvertContext {
    Transform           m_transformToDGN;
    Transform           m_transformFromDGN;
    double              m_scaleToDGN;
    UnitDefinition      m_sourceUnit;
    UnitDefinition      m_targetUnit;
    
    void SetupTransformations() {
        // Calculate scale factors
        m_scaleToDGN = CalculateScaleFactor(m_sourceUnit, m_targetUnit);
        
        // Setup transformation matrices
        m_transformToDGN.InitFromScaleFactors(m_scaleToDGN, m_scaleToDGN, m_scaleToDGN);
        m_transformFromDGN.InverseOf(m_transformToDGN);
    }
};
```

## Symbology Conversion System

### Symbology Mapping Architecture

```
AutoCAD Symbology                    DGN Symbology
┌─────────────┐                     ┌─────────────┐
│   Colors    │ ◄─────────────────► │   Colors    │
│  (ACI/RGB)  │                     │ (Palette)   │
└─────────────┘                     └─────────────┘
┌─────────────┐                     ┌─────────────┐
│ Linetypes   │ ◄─────────────────► │Line Styles  │
│ (LTY files) │                     │(RSC files)  │
└─────────────┘                     └─────────────┘
┌─────────────┐                     ┌─────────────┐
│   Layers    │ ◄─────────────────► │   Levels    │
│ (Properties)│                     │(Properties) │
└─────────────┘                     └─────────────┘
┌─────────────┐                     ┌─────────────┐
│ Materials   │ ◄─────────────────► │ Materials   │
│ (Rendering) │                     │ (Rendering) │
└─────────────┘                     └─────────────┘
```

### Symbology Data Management

```cpp
class DwgSymbologyData {
    // Color management
    bmap<int, ColorDef>                 m_aciColorMap;
    bmap<AcDbObjectId, ColorDef>        m_colorObjectMap;
    
    // Linetype management  
    bmap<AcDbObjectId, LineStyleParams> m_linetypeMap;
    bmap<WString, LineStyleParams>      m_linetypeNameMap;
    
    // Layer management
    bmap<AcDbObjectId, LevelId>         m_layerToLevelMap;
    bmap<WString, LevelId>              m_layerNameToLevelMap;
};
```

## Multi-Threading Architecture

### Parallel Processing Strategy

The framework supports multi-threaded conversion for improved performance:

```cpp
class ConvertToDgnMultiProcessingContext : public ConvertToDgnContext {
    // Thread pool for entity processing
    ThreadPool                  m_threadPool;
    
    // Synchronization primitives
    std::mutex                  m_elementMutex;
    std::condition_variable     m_elementCondition;
    
    // Work queue
    std::queue<AcDbEntity*>     m_entityQueue;
    
    StatusInt ProcessEntitiesInParallel() {
        // Distribute entities across worker threads
        for (int i = 0; i < m_threadPool.size(); ++i) {
            m_threadPool.enqueue([this]() {
                ProcessEntityBatch();
            });
        }
        
        // Wait for completion
        m_threadPool.wait_for_all();
        return SUCCESS;
    }
};
```

## Extension Points

### Custom Entity Conversion

The framework provides extension points for custom entity types:

```cpp
class CustomEntityConverter : public DwgDgnExtension {
public:
    virtual void SaveElementToDgn(AcDbEntity* entity, MSElementDescrP* descr) override {
        // Custom conversion logic
        if (entity->isA() == MyCustomEntity::desc()) {
            ConvertMyCustomEntity(static_cast<MyCustomEntity*>(entity), descr);
        }
    }
    
    virtual void SaveElementToDatabase(ElementHandleCR eh, AcDbObjectId* objectId) override {
        // Custom reverse conversion logic
    }
};
```

### Adapter Registration

```cpp
void RegisterCustomAdapters() {
    MstnInterfaceHelper::Instance().AddAdapter(new CustomEntityConverter());
}
```

## Error Handling and Recovery

### Exception Hierarchy

```cpp
class RealDwgException {
    RealDwgExceptionCode    m_code;
    AcString               m_message;
};

class FileHolderException : public RealDwgException {
    // Database-related errors
};

class DiscardInvalidEntityException : public RealDwgException {
    // Entity conversion errors
};
```

### Graceful Degradation

The conversion system implements graceful degradation:

1. **Entity-level**: Skip invalid entities, continue with others
2. **Property-level**: Use default values for unsupported properties
3. **Symbology-level**: Fall back to standard symbology when custom not available
4. **Progress-level**: Report partial completion status

## Performance Optimization

### Caching Strategies

1. **Symbology Caching**: Cache converted colors, linetypes, and materials
2. **Geometry Caching**: Cache complex geometry conversions
3. **ID Mapping**: Efficient bidirectional ID lookup tables
4. **Model Indexing**: Fast model and layout access

### Memory Management

1. **Streaming**: Process large files without loading everything into memory
2. **Lazy Loading**: Load entities and resources on demand
3. **Resource Cleanup**: Automatic cleanup of temporary objects
4. **Memory Pools**: Efficient allocation for frequently used objects
