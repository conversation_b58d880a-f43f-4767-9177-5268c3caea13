#include "../src/formats/dwg/DWGEntityFactory.h"
#include "../src/formats/dwg/DWGEntityCreator.h"
#include "../src/formats/dwg/DWGEntityProcessor.h"
#include "../src/formats/dwg/DWGEntityTypes.h"
#include "../include/IModelExportManager.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#endif

#include <iostream>
#include <vector>
#include <memory>
#include <chrono>

using namespace IModelExport;

//=======================================================================================
// DWG Complete Entity Processing Example
//=======================================================================================

class DWGCompleteEntitiesExample {
public:
    DWGCompleteEntitiesExample() : m_database(nullptr) {}
    
    bool Initialize() {
#ifdef REALDWG_AVAILABLE
        // Initialize RealDWG
        if (!acdbInitialize()) {
            std::cerr << "Failed to initialize RealDWG" << std::endl;
            return false;
        }
        
        // Create new database
        m_database = new AcDbDatabase(false, true);
        if (!m_database) {
            std::cerr << "Failed to create database" << std::endl;
            return false;
        }
        
        // Initialize entity factory
        m_entityFactory = std::make_shared<DWGEntityFactory>();
        m_entityFactory->SetDatabase(m_database);
        
        // Set up callbacks
        m_entityFactory->SetProgressCallback([this](size_t current, size_t total, const std::string& entity) -> bool {
            std::cout << "Processing entity " << current << "/" << total << ": " << entity << std::endl;
            return true; // Continue processing
        });
        
        m_entityFactory->SetErrorCallback([this](const std::string& error, DWGEntityType type, const std::string& entityId) {
            std::cerr << "Error processing " << DWGEntityTypeRegistry::Instance().GetEntityTypeName(type) 
                      << " (ID: " << entityId << "): " << error << std::endl;
        });
        
        return true;
#else
        std::cerr << "RealDWG not available" << std::endl;
        return false;
#endif
    }
    
    void Cleanup() {
#ifdef REALDWG_AVAILABLE
        if (m_database) {
            delete m_database;
            m_database = nullptr;
        }
        acdbCleanUp();
#endif
    }
    
    bool RunCompleteExample() {
        std::cout << "=== DWG Complete Entity Processing Example ===" << std::endl;
        
        if (!Initialize()) {
            return false;
        }
        
        try {
            // Create all types of entities
            CreateBasic2DEntities();
            CreateTextAndAnnotations();
            CreateDimensions();
            Create3DSolids();
            CreateSurfaces();
            CreateMeshes();
            CreateBlocksAndReferences();
            CreateSpecialEntities();
            
            // Process all entities
            ProcessAllEntities();
            
            // Generate reports
            GenerateReports();
            
            // Save to file
            SaveToFile("complete_entities_example.dwg");
            
            std::cout << "Complete entity example completed successfully!" << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Exception in complete example: " << e.what() << std::endl;
            return false;
        }
    }

private:
    void CreateBasic2DEntities() {
        std::cout << "\n--- Creating Basic 2D Entities ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Lines
        creator->CreateLine(Point3d(0, 0, 0), Point3d(100, 0, 0), "Geometry");
        creator->CreateLine(Point3d(0, 0, 0), Point3d(0, 100, 0), "Geometry");
        creator->CreateLine(Point3d(100, 0, 0), Point3d(100, 100, 0), "Geometry");
        creator->CreateLine(Point3d(0, 100, 0), Point3d(100, 100, 0), "Geometry");
        
        // Circles and arcs
        creator->CreateCircle(Point3d(50, 50, 0), 25, "Geometry");
        creator->CreateArc(Point3d(150, 50, 0), 30, 0, M_PI, "Geometry");
        creator->CreateEllipse(Point3d(250, 50, 0), Vector3d(40, 0, 0), 0.5, "Geometry");
        
        // Polylines
        std::vector<Point3d> polyPoints = {
            Point3d(0, 150, 0), Point3d(50, 200, 0), Point3d(100, 150, 0), Point3d(150, 200, 0)
        };
        creator->CreatePolyline(polyPoints, false, "Geometry");
        
        std::vector<Point3d> poly3DPoints = {
            Point3d(0, 250, 0), Point3d(50, 300, 10), Point3d(100, 250, 20), Point3d(150, 300, 30)
        };
        creator->Create3DPolyline(poly3DPoints, "Geometry");
        
        // Splines
        std::vector<Point3d> splinePoints = {
            Point3d(200, 150, 0), Point3d(250, 200, 0), Point3d(300, 150, 0), Point3d(350, 200, 0)
        };
        creator->CreateSpline(splinePoints, 3, "Geometry");
        
        // Points
        creator->CreatePoint(Point3d(400, 50, 0), "Points");
        creator->CreatePoint(Point3d(450, 50, 0), "Points");
        creator->CreatePoint(Point3d(500, 50, 0), "Points");
        
        // Construction lines
        creator->CreateRay(Point3d(0, 300, 0), Vector3d(1, 1, 0), "Construction");
        creator->CreateXLine(Point3d(100, 300, 0), Vector3d(1, -1, 0), "Construction");
        
        std::cout << "Created basic 2D entities" << std::endl;
    }
    
    void CreateTextAndAnnotations() {
        std::cout << "\n--- Creating Text and Annotations ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Text entities
        creator->CreateText(Point3d(0, 350, 0), "Single Line Text", 10, 0, "Text");
        creator->CreateMText(Point3d(200, 350, 0), "This is multiline text\nwith multiple lines\nand formatting", 150, 8, "Text");
        
        // Attribute definitions
        creator->CreateAttributeDefinition(Point3d(400, 350, 0), "TITLE", "Enter title:", "Default Title", 8, "Attributes");
        creator->CreateAttributeDefinition(Point3d(400, 370, 0), "AUTHOR", "Enter author:", "Default Author", 6, "Attributes");
        
        std::cout << "Created text and annotation entities" << std::endl;
    }
    
    void CreateDimensions() {
        std::cout << "\n--- Creating Dimensions ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Linear dimensions
        creator->CreateAlignedDimension(Point3d(0, 400, 0), Point3d(100, 400, 0), Point3d(50, 420, 0), "Dimensions");
        creator->CreateLinearDimension(Point3d(150, 400, 0), Point3d(250, 450, 0), Point3d(200, 470, 0), 0, "Dimensions");
        creator->CreateOrdinateDimension(Point3d(300, 400, 0), Point3d(350, 420, 0), true, "Dimensions");
        
        // Angular dimensions
        creator->CreateAngularDimension(Point3d(400, 400, 0), Point3d(450, 400, 0), Point3d(450, 450, 0), 
                                       Point3d(400, 450, 0), Point3d(400, 400, 0), Point3d(425, 425, 0), "Dimensions");
        
        // Radial dimensions
        creator->CreateRadialDimension(Point3d(500, 400, 0), Point3d(525, 400, 0), 30, "Dimensions");
        creator->CreateDiametricDimension(Point3d(575, 400, 0), Point3d(625, 400, 0), 30, "Dimensions");
        
        std::cout << "Created dimension entities" << std::endl;
    }
    
    void Create3DSolids() {
        std::cout << "\n--- Creating 3D Solids ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Basic 3D primitives
        creator->CreateBox(Point3d(0, 500, 0), 50, 50, 50, "3D_Solids");
        creator->CreateCylinder(Point3d(100, 525, 0), 25, 50, "3D_Solids");
        creator->CreateSphere(Point3d(200, 525, 0), 25, "3D_Solids");
        creator->CreateCone(Point3d(300, 525, 0), 25, 10, 50, "3D_Solids");
        creator->CreateTorus(Point3d(400, 525, 0), 30, 10, "3D_Solids");
        creator->CreateWedge(Point3d(500, 500, 0), 50, 50, 50, "3D_Solids");
        creator->CreatePyramid(Point3d(600, 525, 0), 25, 5, 50, 6, "3D_Solids");
        
        std::cout << "Created 3D solid entities" << std::endl;
    }
    
    void CreateSurfaces() {
        std::cout << "\n--- Creating Surfaces ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Basic surfaces
        creator->CreatePlaneSurface(Point3d(0, 600, 0), Vector3d(1, 0, 0), Vector3d(0, 1, 0), 100, 100, "Surfaces");
        
        // Procedural surfaces
        std::vector<Point3d> profile = {
            Point3d(150, 600, 0), Point3d(200, 650, 0), Point3d(250, 600, 0)
        };
        creator->CreateExtrudedSurface(profile, Vector3d(0, 0, 1), 50, "Surfaces");
        creator->CreateRevolvedSurface(profile, Point3d(300, 600, 0), Point3d(300, 700, 0), 0, 2 * M_PI, "Surfaces");
        
        std::vector<Point3d> path = {
            Point3d(400, 600, 0), Point3d(450, 650, 10), Point3d(500, 600, 20)
        };
        creator->CreateSweptSurface(profile, path, "Surfaces");
        
        std::cout << "Created surface entities" << std::endl;
    }
    
    void CreateMeshes() {
        std::cout << "\n--- Creating Meshes ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Triangle mesh
        std::vector<Point3d> vertices = {
            Point3d(0, 700, 0), Point3d(50, 700, 0), Point3d(25, 750, 0),
            Point3d(0, 750, 0), Point3d(50, 750, 0), Point3d(25, 700, 25)
        };
        std::vector<int> triangles = {0, 1, 2, 0, 2, 3, 1, 4, 2, 2, 4, 5};
        creator->CreateTriangleMesh(vertices, triangles, "Meshes");
        
        // 3D faces
        creator->Create3DFace(Point3d(100, 700, 0), Point3d(150, 700, 0), Point3d(150, 750, 0), Point3d(100, 750, 0), "Meshes");
        
        std::cout << "Created mesh entities" << std::endl;
    }
    
    void CreateBlocksAndReferences() {
        std::cout << "\n--- Creating Blocks and References ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Block references (assuming blocks exist)
        creator->CreateBlockReference("StandardBlock", Point3d(0, 800, 0), Transform3d::Identity(), "Blocks");
        creator->CreateMInsert("StandardBlock", Point3d(100, 800, 0), 3, 2, 50, 50, "Blocks");
        
        std::cout << "Created block and reference entities" << std::endl;
    }
    
    void CreateSpecialEntities() {
        std::cout << "\n--- Creating Special Entities ---" << std::endl;
        
        auto creator = m_entityFactory->GetEntityCreator();
        
        // Hatch
        std::vector<std::vector<Point3d>> boundaries = {{
            Point3d(0, 900, 0), Point3d(100, 900, 0), Point3d(100, 1000, 0), Point3d(0, 1000, 0)
        }};
        creator->CreateHatch(boundaries, "ANSI31", 1.0, 0, "Hatches");
        
        // Multiline
        std::vector<Point3d> mlinePoints = {
            Point3d(150, 900, 0), Point3d(250, 950, 0), Point3d(350, 900, 0)
        };
        creator->CreateMLine(mlinePoints, "STANDARD", 1.0, "Multilines");
        
        // Lights
        creator->CreatePointLight(Point3d(400, 950, 100), Color(1, 1, 1), 1000, "Lights");
        creator->CreateSpotLight(Point3d(500, 950, 100), Point3d(500, 900, 0), Color(1, 1, 0.8), 800, 30, "Lights");
        
        // Camera
        creator->CreateCamera(Point3d(600, 950, 100), Point3d(300, 500, 0), Vector3d(0, 0, 1), 45, "Cameras");
        
        // Helix
        creator->CreateHelix(Point3d(700, 950, 0), Vector3d(0, 0, 1), 25, 15, 100, 5, true, "Helixes");
        
        std::cout << "Created special entities" << std::endl;
    }
    
    void ProcessAllEntities() {
        std::cout << "\n--- Processing All Entities ---" << std::endl;
        
        DWGProcessingOptions options;
        options.processGeometry = true;
        options.processAttributes = true;
        options.processMaterials = true;
        options.processLayers = true;
        options.enableDetailedLogging = true;
        
        bool success = m_entityFactory->ProcessDatabase(m_database, options);
        if (success) {
            std::cout << "Successfully processed all entities" << std::endl;
        } else {
            std::cerr << "Failed to process some entities" << std::endl;
        }
    }
    
    void GenerateReports() {
        std::cout << "\n--- Generating Reports ---" << std::endl;
        
        const auto& stats = m_entityFactory->GetProcessingStats();
        auto entityCounts = m_entityFactory->GetEntityCounts();
        auto layerCounts = m_entityFactory->GetLayerCounts();
        
        std::cout << "Processing Statistics:" << std::endl;
        std::cout << "  Total entities: " << stats.totalEntities << std::endl;
        std::cout << "  Processed entities: " << stats.processedEntities << std::endl;
        std::cout << "  Skipped entities: " << stats.skippedEntities << std::endl;
        std::cout << "  Error entities: " << stats.errorEntities << std::endl;
        std::cout << "  Processing time: " << stats.processingTimeMs << " ms" << std::endl;
        std::cout << "  Average time per entity: " << stats.averageTimePerEntity << " ms" << std::endl;
        
        std::cout << "\nEntity Counts by Type:" << std::endl;
        for (const auto& pair : entityCounts) {
            std::cout << "  " << DWGEntityTypeRegistry::Instance().GetEntityTypeName(pair.first) 
                      << ": " << pair.second << std::endl;
        }
        
        std::cout << "\nEntity Counts by Layer:" << std::endl;
        for (const auto& pair : layerCounts) {
            std::cout << "  " << pair.first << ": " << pair.second << std::endl;
        }
        
        std::cout << "\nPerformance Metrics:" << std::endl;
        std::cout << "  Total creation time: " << m_entityFactory->GetTotalProcessingTime() << " ms" << std::endl;
        std::cout << "  Average creation time: " << m_entityFactory->GetAverageCreationTime() << " ms" << std::endl;
        std::cout << "  Average processing time: " << m_entityFactory->GetAverageProcessingTime() << " ms" << std::endl;
    }
    
    bool SaveToFile(const std::string& filename) {
        std::cout << "\n--- Saving to File ---" << std::endl;
        
#ifdef REALDWG_AVAILABLE
        if (!m_database) {
            std::cerr << "No database to save" << std::endl;
            return false;
        }
        
        Acad::ErrorStatus es = m_database->saveAs(filename.c_str());
        if (es != Acad::eOk) {
            std::cerr << "Failed to save database: " << es << std::endl;
            return false;
        }
        
        std::cout << "Successfully saved to " << filename << std::endl;
        return true;
#else
        std::cout << "RealDWG not available - cannot save file" << std::endl;
        return false;
#endif
    }

private:
#ifdef REALDWG_AVAILABLE
    AcDbDatabase* m_database;
#else
    void* m_database;
#endif
    std::shared_ptr<DWGEntityFactory> m_entityFactory;
};

//=======================================================================================
// Main Function
//=======================================================================================

int main() {
    std::cout << "DWG Complete Entity Processing Example" << std::endl;
    std::cout << "======================================" << std::endl;
    
    DWGCompleteEntitiesExample example;
    bool success = example.RunCompleteExample();
    
    example.Cleanup();
    
    if (success) {
        std::cout << "\nExample completed successfully!" << std::endl;
        return 0;
    } else {
        std::cerr << "\nExample failed!" << std::endl;
        return 1;
    }
}
