#include "../include/IModelExportManager.h"
#include "../include/ExportTypes.h"
#include "../src/core/ConversionPipeline.h"
#include "../src/core/DataFlowManager.h"
#include "../src/core/ConversionStrategy.h"

#include <iostream>
#include <filesystem>
#include <chrono>
#include <thread>
#include <future>
#include <iomanip>

using namespace IModelExport;

//=======================================================================================
// Advanced Progress Monitoring
//=======================================================================================

class AdvancedProgressMonitor {
private:
    std::chrono::steady_clock::time_point m_startTime;
    std::chrono::steady_clock::time_point m_lastUpdate;
    double m_lastPercentage = 0.0;
    size_t m_totalElements = 0;
    
public:
    void Start(size_t totalElements) {
        m_startTime = m_lastUpdate = std::chrono::steady_clock::now();
        m_totalElements = totalElements;
        m_lastPercentage = 0.0;
        
        std::cout << "\n┌─────────────────────────────────────────────────────────────┐" << std::endl;
        std::cout << "│                    Export Progress Monitor                  │" << std::endl;
        std::cout << "├─────────────────────────────────────────────────────────────┤" << std::endl;
        std::cout << "│ Total Elements: " << std::setw(10) << totalElements << "                              │" << std::endl;
        std::cout << "└─────────────────────────────────────────────────────────────┘" << std::endl;
    }
    
    bool UpdateProgress(const ExportProgress& progress) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastUpdate);
        
        // Update every 500ms to avoid too frequent updates
        if (elapsed.count() > 500 || progress.percentage >= 100.0) {
            auto totalElapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_startTime);
            
            // Calculate ETA
            double remainingPercentage = 100.0 - progress.percentage;
            double eta = 0.0;
            if (progress.percentage > 0.0) {
                eta = (totalElapsed.count() * remainingPercentage) / progress.percentage;
            }
            
            // Calculate speed
            double speed = 0.0;
            if (totalElapsed.count() > 0) {
                speed = static_cast<double>(progress.processedElements) / totalElapsed.count();
            }
            
            // Draw progress bar
            std::cout << "\r";
            std::cout << "Progress: [";
            
            int barWidth = 30;
            int pos = static_cast<int>(barWidth * progress.percentage / 100.0);
            for (int i = 0; i < barWidth; ++i) {
                if (i < pos) std::cout << "█";
                else if (i == pos) std::cout << "▌";
                else std::cout << " ";
            }
            
            std::cout << "] " << std::fixed << std::setprecision(1) << progress.percentage << "% ";
            std::cout << "(" << progress.processedElements << "/" << progress.totalElements << ") ";
            std::cout << "Speed: " << std::fixed << std::setprecision(1) << speed << " elem/s ";
            std::cout << "ETA: " << std::setw(3) << static_cast<int>(eta) << "s ";
            std::cout << "| " << progress.currentOperation;
            std::cout.flush();
            
            m_lastUpdate = now;
            m_lastPercentage = progress.percentage;
        }
        
        // Return false to cancel export (could add user input check here)
        return true;
    }
    
    void Complete() {
        auto endTime = std::chrono::steady_clock::now();
        auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(endTime - m_startTime);
        
        std::cout << std::endl;
        std::cout << "┌─────────────────────────────────────────────────────────────┐" << std::endl;
        std::cout << "│                    Export Completed                        │" << std::endl;
        std::cout << "├─────────────────────────────────────────────────────────────┤" << std::endl;
        std::cout << "│ Total Time: " << std::setw(8) << totalTime.count() << " seconds                        │" << std::endl;
        std::cout << "│ Average Speed: " << std::setw(6) << std::fixed << std::setprecision(1) 
                  << (static_cast<double>(m_totalElements) / totalTime.count()) << " elements/second           │" << std::endl;
        std::cout << "└─────────────────────────────────────────────────────────────┘" << std::endl;
    }
};

//=======================================================================================
// Pipeline-Based Export Example
//=======================================================================================

void PipelineExportExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Pipeline-Based Export Example ===" << std::endl;
    
    try {
        // Create export context
        auto context = std::make_shared<ExportContext>();
        
        // Create conversion pipeline
        auto pipeline = std::make_unique<ConversionPipeline>(context);
        
        // Configure pipeline
        ConversionPipeline::PipelineConfig config;
        config.targetFormat = ExportFormat::DWG;
        config.levelOfDetail = ExportLOD::High;
        config.enableParallelProcessing = true;
        config.maxThreads = std::thread::hardware_concurrency();
        config.batchSize = 100;
        config.enableOptimization = true;
        config.enableValidation = true;
        config.tempDirectory = outputDir + "/temp";
        
        pipeline->SetPipelineConfig(config);
        
        // Set pipeline callbacks
        pipeline->SetStageCallback([](ConversionPipeline::PipelineStage stage, double progress) {
            std::cout << "Pipeline Stage: " << static_cast<int>(stage) << " - " 
                      << std::fixed << std::setprecision(1) << progress << "%" << std::endl;
            return true; // Continue
        });
        
        pipeline->SetProgressCallback([](double progress, const std::string& operation) {
            static auto lastUpdate = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() > 1000) {
                std::cout << "Overall Progress: " << std::fixed << std::setprecision(1) 
                          << progress << "% - " << operation << std::endl;
                lastUpdate = now;
            }
            return true; // Continue
        });
        
        pipeline->SetErrorCallback([](ConversionPipeline::PipelineStage stage, const std::string& error) {
            std::cout << "Pipeline Error at stage " << static_cast<int>(stage) 
                      << ": " << error << std::endl;
        });
        
        // Load iModel (placeholder)
        // IModelDb imodel = LoadIModel(imodelPath);
        
        // Create export options
        DWGExportOptions options;
        options.outputPath = outputDir + "/pipeline_model.dwg";
        options.version = DWGExportOptions::DWGVersion::R2021;
        options.levelOfDetail = ExportLOD::High;
        
        std::cout << "Starting pipeline export..." << std::endl;
        
        // Execute pipeline
        // bool success = pipeline->ExecutePipeline(imodel, options);
        bool success = true; // Mock for demonstration
        
        if (success) {
            auto metrics = pipeline->GetPipelineMetrics();
            std::cout << "\nPipeline Export Completed Successfully!" << std::endl;
            std::cout << "Total Duration: " << metrics.totalDuration << " seconds" << std::endl;
            std::cout << "Elements Processed: " << metrics.elementsProcessed << std::endl;
            std::cout << "Throughput: " << metrics.throughput << " elements/second" << std::endl;
            std::cout << "Peak Memory Usage: " << (metrics.memoryPeakUsage / 1024 / 1024) << " MB" << std::endl;
            
            // Generate detailed report
            std::string report = pipeline->GeneratePipelineReport();
            std::ofstream reportFile(outputDir + "/pipeline_report.txt");
            reportFile << report;
            reportFile.close();
            
            std::cout << "Detailed report saved to: " << outputDir << "/pipeline_report.txt" << std::endl;
        } else {
            std::cout << "Pipeline export failed!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Pipeline export failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Multi-Format Strategy Comparison
//=======================================================================================

void StrategyComparisonExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Multi-Format Strategy Comparison ===" << std::endl;
    
    try {
        // Create export context
        auto context = std::make_shared<ExportContext>();
        
        // Create strategy manager
        auto strategyManager = std::make_unique<ConversionStrategyManager>(context);
        
        // Mock element data for analysis
        std::vector<ElementInfo> elements;
        for (int i = 0; i < 100; ++i) {
            ElementInfo element;
            element.id = "element_" + std::to_string(i);
            element.type = static_cast<ElementType>(i % 3); // Vary element types
            element.classFullName = "TestElement";
            element.userLabel = "Test Element " + std::to_string(i);
            elements.push_back(element);
        }
        
        // Analyze compatibility with different formats
        std::vector<ExportFormat> formats = {
            ExportFormat::DWG,
            ExportFormat::IFC,
            ExportFormat::DGN,
            ExportFormat::USD
        };
        
        std::cout << "\nAnalyzing element compatibility with different formats..." << std::endl;
        std::cout << "┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐" << std::endl;
        std::cout << "│   Format    │ Compatible  │ Avg Quality │ Avg Time(s) │ Recommended │" << std::endl;
        std::cout << "├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤" << std::endl;
        
        for (auto format : formats) {
            size_t compatibleCount = 0;
            double totalQuality = 0.0;
            double totalTime = 0.0;
            
            for (const auto& element : elements) {
                auto compatibility = StrategyUtils::AnalyzeElementCompatibility(element);
                if (compatibility.find(format) != compatibility.end()) {
                    compatibleCount++;
                    totalQuality += StrategyUtils::CalculateConversionQuality(element, format);
                }
                
                auto times = strategyManager->EstimateConversionTimes(element);
                if (times.find(format) != times.end()) {
                    totalTime += times[format];
                }
            }
            
            double avgQuality = compatibleCount > 0 ? totalQuality / compatibleCount : 0.0;
            double avgTime = elements.size() > 0 ? totalTime / elements.size() : 0.0;
            
            std::string formatName = ToString(format);
            bool recommended = (compatibleCount == elements.size() && avgQuality > 0.8);
            
            std::cout << "│ " << std::setw(11) << formatName << " │ "
                      << std::setw(11) << compatibleCount << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(2) << avgQuality << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(3) << avgTime << " │ "
                      << std::setw(11) << (recommended ? "Yes" : "No") << " │" << std::endl;
        }
        
        std::cout << "└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘" << std::endl;
        
        // Get optimal format recommendation
        ExportFormat optimalFormat = strategyManager->RecommendOptimalFormat(elements);
        std::cout << "\nRecommended optimal format: " << ToString(optimalFormat) << std::endl;
        
        // Generate conversion recommendations
        auto recommendations = StrategyUtils::GenerateConversionRecommendations(elements, optimalFormat);
        std::cout << "\nConversion Recommendations:" << std::endl;
        for (const auto& recommendation : recommendations) {
            std::cout << "• " << recommendation << std::endl;
        }
        
        // Compare strategies
        std::string comparison = StrategyUtils::CompareConversionStrategies(elements, formats);
        std::cout << "\nStrategy Comparison Report:" << std::endl;
        std::cout << comparison << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Strategy comparison failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Data Flow Management Example
//=======================================================================================

void DataFlowExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Data Flow Management Example ===" << std::endl;
    
    try {
        // Create export context
        auto context = std::make_shared<ExportContext>();
        
        // Create data flow manager
        auto flowManager = std::make_unique<DataFlowManager>(context);
        
        // Configure data flow
        DataFlowManager::FlowConfig config;
        config.maxQueueSize = 1000;
        config.batchSize = 50;
        config.numWorkerThreads = std::thread::hardware_concurrency();
        config.enableParallelProcessing = true;
        config.enableDataValidation = true;
        config.memoryThreshold = 0.8;
        config.tempDirectory = outputDir + "/temp";
        
        flowManager->SetFlowConfig(config);
        
        // Set flow callbacks
        flowManager->SetStageCallback([](DataFlowManager::FlowStage stage, 
                                        std::shared_ptr<DataFlowManager::DataPacket> packet) {
            static std::unordered_map<DataFlowManager::FlowStage, std::string> stageNames = {
                {DataFlowManager::FlowStage::Input, "Input"},
                {DataFlowManager::FlowStage::Extract, "Extract"},
                {DataFlowManager::FlowStage::Transform, "Transform"},
                {DataFlowManager::FlowStage::Process, "Process"},
                {DataFlowManager::FlowStage::Validate, "Validate"},
                {DataFlowManager::FlowStage::Output, "Output"}
            };
            
            static auto lastLog = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            
            if (std::chrono::duration_cast<std::chrono::seconds>(now - lastLog).count() > 2) {
                std::cout << "Processing packet " << packet->id << " at stage: " 
                          << stageNames[stage] << std::endl;
                lastLog = now;
            }
            
            return true; // Continue processing
        });
        
        flowManager->SetProgressCallback([](double progress, const std::string& operation) {
            static auto lastUpdate = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() > 1000) {
                std::cout << "Flow Progress: " << std::fixed << std::setprecision(1) 
                          << progress << "% - " << operation << std::endl;
                lastUpdate = now;
            }
            return true;
        });
        
        flowManager->SetErrorCallback([](DataFlowManager::FlowStage stage, const std::string& error,
                                        std::shared_ptr<DataFlowManager::DataPacket> packet) {
            std::cout << "Flow Error at stage " << static_cast<int>(stage) 
                      << " for packet " << packet->id << ": " << error << std::endl;
        });
        
        // Load iModel and start flow (placeholder)
        // IModelDb imodel = LoadIModel(imodelPath);
        
        std::cout << "Starting data flow..." << std::endl;
        
        // Start flow
        // bool success = flowManager->StartFlow(imodel, ExportFormat::USD);
        bool success = true; // Mock for demonstration
        
        if (success) {
            // Monitor flow progress
            while (flowManager->IsFlowRunning()) {
                auto stats = flowManager->GetFlowStatistics();
                auto queueSizes = flowManager->GetQueueSizes();
                
                std::cout << "\rFlow Stats - Processed: " << stats.processedPackets 
                          << ", Queued: " << stats.queuedPackets 
                          << ", Throughput: " << std::fixed << std::setprecision(1) << stats.throughput << " pkt/s";
                std::cout.flush();
                
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                
                // Simulate completion after some time
                static int counter = 0;
                if (++counter > 10) {
                    flowManager->StopFlow();
                    break;
                }
            }
            
            std::cout << std::endl;
            
            // Get final statistics
            auto finalStats = flowManager->GetFlowStatistics();
            std::cout << "\nData Flow Completed!" << std::endl;
            std::cout << "Total Packets Processed: " << finalStats.processedPackets << std::endl;
            std::cout << "Error Packets: " << finalStats.errorPackets << std::endl;
            std::cout << "Average Processing Time: " << finalStats.averageProcessingTime << " ms" << std::endl;
            std::cout << "Peak Memory Usage: " << (finalStats.memoryUsage / 1024 / 1024) << " MB" << std::endl;
            
            // Get processed data
            auto outputData = flowManager->GetAllOutputData();
            std::cout << "Output Data Packets: " << outputData.size() << std::endl;
            
        } else {
            std::cout << "Data flow failed to start!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Data flow example failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Performance Benchmarking
//=======================================================================================

void PerformanceBenchmarkExample(const std::string& imodelPath, const std::string& outputDir) {
    std::cout << "\n=== Performance Benchmarking Example ===" << std::endl;
    
    try {
        auto exportManager = IModelExportManager::Create();
        
        // Test different configurations
        std::vector<std::pair<std::string, DWGExportOptions>> configs = {
            {"Single Thread", DWGExportOptions()},
            {"Multi Thread", DWGExportOptions()},
            {"Optimized", DWGExportOptions()},
            {"High Quality", DWGExportOptions()}
        };
        
        // Configure each test
        configs[0].second.enableMultiThreading = false;
        configs[0].second.levelOfDetail = ExportLOD::Medium;
        
        configs[1].second.enableMultiThreading = true;
        configs[1].second.levelOfDetail = ExportLOD::Medium;
        
        configs[2].second.enableMultiThreading = true;
        configs[2].second.levelOfDetail = ExportLOD::Low;
        
        configs[3].second.enableMultiThreading = true;
        configs[3].second.levelOfDetail = ExportLOD::High;
        
        std::cout << "\nRunning performance benchmarks..." << std::endl;
        std::cout << "┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐" << std::endl;
        std::cout << "│ Configuration   │ Time (s)    │ Elements    │ Speed (e/s) │ Memory (MB) │" << std::endl;
        std::cout << "├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤" << std::endl;
        
        for (auto& config : configs) {
            config.second.outputPath = outputDir + "/benchmark_" + config.first + ".dwg";
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // Mock export for demonstration
            std::this_thread::sleep_for(std::chrono::milliseconds(500 + rand() % 1000));
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration<double>(endTime - startTime).count();
            
            // Mock results
            size_t elements = 1000 + rand() % 500;
            double speed = elements / duration;
            size_t memory = 50 + rand() % 100;
            
            std::cout << "│ " << std::setw(15) << config.first << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(2) << duration << " │ "
                      << std::setw(11) << elements << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(1) << speed << " │ "
                      << std::setw(11) << memory << " │" << std::endl;
        }
        
        std::cout << "└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘" << std::endl;
        
        std::cout << "\nBenchmark completed. Results saved to individual files." << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Performance benchmark failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Main Function
//=======================================================================================

int main(int argc, char* argv[]) {
    std::string imodelPath = "sample.bim";
    std::string outputDir = "advanced_output";
    
    if (argc > 1) {
        imodelPath = argv[1];
    }
    if (argc > 2) {
        outputDir = argv[2];
    }
    
    std::cout << "Advanced iModel Export Framework Examples" << std::endl;
    std::cout << "=========================================" << std::endl;
    std::cout << "Input iModel: " << imodelPath << std::endl;
    std::cout << "Output directory: " << outputDir << std::endl;
    
    // Create output directory
    std::filesystem::create_directories(outputDir);
    
    // Run advanced examples
    PipelineExportExample(imodelPath, outputDir + "/pipeline");
    StrategyComparisonExample(imodelPath, outputDir + "/strategy");
    DataFlowExample(imodelPath, outputDir + "/dataflow");
    PerformanceBenchmarkExample(imodelPath, outputDir + "/benchmark");
    
    std::cout << "\nAll advanced examples completed!" << std::endl;
    std::cout << "Check the output directories for detailed results and reports." << std::endl;
    
    return 0;
}
