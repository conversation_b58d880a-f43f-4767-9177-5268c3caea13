/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/filehandler/DwgMstnHost.h $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#include    <Mstn\RealDwg\DwgPlatformHost.h>

BEGIN_BENTLEY_NAMESPACE
namespace DwgFileHandler {


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          01/12
+===============+===============+===============+===============+===============+======*/
class    DwgMstnHost : public Bentley::RealDwg::DwgPlatformHost
{
public:
    // Interfaces from Autodesk's RealDWG hostApp:
    virtual bool        _GetPassword (WCharCP dwgName, FilePasswordOption options, WCharP password, const size_t bufSize) override;
    virtual WCharCP     _GetRegistryProductRootKey () override;
    virtual WCharCP     _Product () override;
    virtual void        _FatalError (WCharCP format, ...) override;

    // interfaces specific to DwgPlatform
    virtual bool        _GetDwgConversionSettings (Bentley::RealDwg::IDwgConversionSettings*& settings) override;
    virtual bool        _GetBigfontInitFile (BeFileNameR fileName) override;
    virtual bool        _GetAcadFontmapFile (BeFileNameR fileName) override;
    virtual void        _ReportMessage (ReportMessageType messageType, ...) override;
    virtual bool        _GetDwgForgroundColor255Name (WStringR colorBookName, WStringR colorName) override;
    virtual bool        _GetDwgDefaultDisplayStyle (WStringR styleName, DisplayStylePtr& style) override;
    virtual bool        _GetTransparencyDisplay () override;
    virtual bool        _GetPersistentTcbForSilentSave (TcbP pTcb) override;
    virtual void        _SetPersistentTcbForSilentSave (TcbCP pTcb) override;
    virtual UInt32      _GetNumberOfScreens () override;
    virtual bool        _CreateProgressMeterSavingChanges (IDgnProgressMeterPtr& outMeter) override;
    virtual void        _LoadCooperativeObjectEnablers () override;
    virtual bool        _AllowUpgradingAecObjects () override;
    virtual bool        _GetMasterDwgFile (AcDbDatabase*& outDwg, DgnFileP* outDgn = nullptr) override;
    virtual void        _SetMasterDwgFile (AcDbDatabase* inDwg, DgnFileP inDgn) override;
    virtual ElementId   _GetActiveViewGroup () override;
    virtual void        _SetActiveViewGroup (ElementId inElementId) override;
    virtual int         _GetMasterFileObjectIdsInCache (AcDbObjectIdArray& objectIds) const override;
    virtual int         _AddMasterFileObjectIdInCache (AcDbObjectId objectId) override;

    DwgMstnHost (RscFileHandle rscHandle);

private:
    RscFileHandle       m_rscFileHandle;
    AcDbDatabase*       m_masterDwgFile;
    DgnFileP            m_masterDgnFile;
    ElementId           m_activeViewgroupId;
    AcDbObjectIdArray   m_masterFileObjectIds;

};  // DwgMstnHost


extern DwgMstnHost*     g_dwgMstnHost;

}   // Ends DwgFileHandler namespace

END_BENTLEY_NAMESPACE
