#include "../../include/IModelExportManager.h"
#include "../../include/IExportFormat.h"
#include "ExportContext.h"
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <algorithm>
#include <sstream>

namespace IModelExport {

//=======================================================================================
// Export Manager Implementation
//=======================================================================================

class ExportManagerImpl : public IModelExportManager {
private:
    std::shared_ptr<ExportContext> m_context;
    std::shared_ptr<IExportFormatFactory> m_factory;
    
    // Threading and concurrency
    mutable std::mutex m_exportsMutex;
    std::unordered_map<std::string, std::shared_ptr<ExportJob>> m_activeExports;
    std::atomic<size_t> m_maxConcurrentExports{4};
    std::atomic<size_t> m_nextExportId{1};
    
    // Configuration
    std::string m_tempDirectory;
    std::atomic<bool> m_loggingEnabled{false};
    std::string m_logFile;
    std::atomic<bool> m_diagnosticsEnabled{false};
    
    // Error tracking
    mutable std::mutex m_errorsMutex;
    std::vector<std::string> m_recentErrors;
    static constexpr size_t MAX_ERROR_HISTORY = 100;

    struct ExportJob {
        std::string id;
        ExportFormat format;
        std::unique_ptr<ExportOptions> options;
        std::future<ExportResult> future;
        std::atomic<bool> cancelled{false};
        ExportProgress progress;
        std::chrono::steady_clock::time_point startTime;
    };

public:
    ExportManagerImpl() {
        m_context = std::make_shared<ExportContext>();
        m_factory = std::make_shared<DefaultExportFormatFactory>();
        
        // Set default temp directory
        #ifdef _WIN32
        m_tempDirectory = std::getenv("TEMP") ? std::getenv("TEMP") : "C:\\Temp";
        #else
        m_tempDirectory = "/tmp";
        #endif
    }

    ExportManagerImpl(std::shared_ptr<IExportFormatFactory> factory) 
        : ExportManagerImpl() {
        m_factory = factory;
    }

    //===================================================================================
    // Configuration
    //===================================================================================

    void SetExportContext(std::shared_ptr<ExportContext> context) override {
        m_context = context;
    }

    std::shared_ptr<ExportContext> GetExportContext() const override {
        return m_context;
    }

    bool IsFormatSupported(ExportFormat format) const override {
        return m_factory->IsFormatSupported(format);
    }

    std::vector<ExportFormat> GetSupportedFormats() const override {
        return m_factory->GetSupportedFormats();
    }

    std::string GetFormatInfo(ExportFormat format) const override {
        auto exporter = m_factory->CreateExporter(format);
        if (!exporter) {
            return "Format not supported";
        }
        
        std::ostringstream info;
        info << "Format: " << exporter->GetFormatName() << "\n";
        info << "Extension: " << exporter->GetFileExtension() << "\n";
        info << "Versions: ";
        auto versions = exporter->GetSupportedVersions();
        for (size_t i = 0; i < versions.size(); ++i) {
            if (i > 0) info << ", ";
            info << versions[i];
        }
        info << "\n";
        info << "Capabilities:\n";
        info << "  Geometry: " << (exporter->SupportsGeometry() ? "Yes" : "No") << "\n";
        info << "  Metadata: " << (exporter->SupportsMetadata() ? "Yes" : "No") << "\n";
        info << "  Materials: " << (exporter->SupportsMaterials() ? "Yes" : "No") << "\n";
        info << "  Textures: " << (exporter->SupportsTextures() ? "Yes" : "No") << "\n";
        info << "  Animations: " << (exporter->SupportsAnimations() ? "Yes" : "No") << "\n";
        info << "  Hierarchy: " << (exporter->SupportsHierarchy() ? "Yes" : "No") << "\n";
        
        return info.str();
    }

    //===================================================================================
    // Synchronous Export Methods
    //===================================================================================

    ExportResult Export(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        ProgressCallback progressCallback) override {
        
        // Validate format support
        if (!IsFormatSupported(format)) {
            ExportResult result;
            result.status = ExportStatus::Error;
            result.errors.push_back("Export format not supported: " + ToString(format));
            LogError(result.errors.back());
            return result;
        }

        // Create exporter
        auto exporter = m_factory->CreateExporter(format);
        if (!exporter) {
            ExportResult result;
            result.status = ExportStatus::Error;
            result.errors.push_back("Failed to create exporter for format: " + ToString(format));
            LogError(result.errors.back());
            return result;
        }

        // Validate options
        std::vector<std::string> validationErrors;
        if (!exporter->ValidateOptions(options, validationErrors)) {
            ExportResult result;
            result.status = ExportStatus::Error;
            result.errors = std::move(validationErrors);
            for (const auto& error : result.errors) {
                LogError(error);
            }
            return result;
        }

        // Set context and perform export
        exporter->SetExportContext(m_context);
        
        auto startTime = std::chrono::steady_clock::now();
        auto result = exporter->Export(imodel, options, progressCallback);
        auto endTime = std::chrono::steady_clock::now();
        
        result.exportTime = std::chrono::duration<double>(endTime - startTime).count();
        
        // Log result
        if (result.status == ExportStatus::Success) {
            LogInfo("Export completed successfully: " + result.outputFile);
        } else {
            LogError("Export failed with status: " + ToString(result.status));
        }

        return result;
    }

    ExportResult ExportToDWG(
        const IModelDb& imodel,
        const DWGExportOptions& options,
        ProgressCallback progressCallback) override {
        return Export(imodel, ExportFormat::DWG, options, progressCallback);
    }

    ExportResult ExportToIFC(
        const IModelDb& imodel,
        const IFCExportOptions& options,
        ProgressCallback progressCallback) override {
        return Export(imodel, ExportFormat::IFC, options, progressCallback);
    }

    ExportResult ExportToDGN(
        const IModelDb& imodel,
        const DGNExportOptions& options,
        ProgressCallback progressCallback) override {
        return Export(imodel, ExportFormat::DGN, options, progressCallback);
    }

    ExportResult ExportToUSD(
        const IModelDb& imodel,
        const USDExportOptions& options,
        ProgressCallback progressCallback) override {
        return Export(imodel, ExportFormat::USD, options, progressCallback);
    }

    //===================================================================================
    // Asynchronous Export Methods
    //===================================================================================

    std::future<ExportResult> ExportAsync(
        const IModelDb& imodel,
        ExportFormat format,
        const ExportOptions& options,
        ProgressCallback progressCallback) override {
        
        return std::async(std::launch::async, [this, &imodel, format, options, progressCallback]() {
            return Export(imodel, format, options, progressCallback);
        });
    }

    std::future<ExportResult> ExportToDWGAsync(
        const IModelDb& imodel,
        const DWGExportOptions& options,
        ProgressCallback progressCallback) override {
        return ExportAsync(imodel, ExportFormat::DWG, options, progressCallback);
    }

    std::future<ExportResult> ExportToIFCAsync(
        const IModelDb& imodel,
        const IFCExportOptions& options,
        ProgressCallback progressCallback) override {
        return ExportAsync(imodel, ExportFormat::IFC, options, progressCallback);
    }

    std::future<ExportResult> ExportToDGNAsync(
        const IModelDb& imodel,
        const DGNExportOptions& options,
        ProgressCallback progressCallback) override {
        return ExportAsync(imodel, ExportFormat::DGN, options, progressCallback);
    }

    std::future<ExportResult> ExportToUSDAsync(
        const IModelDb& imodel,
        const USDExportOptions& options,
        ProgressCallback progressCallback) override {
        return ExportAsync(imodel, ExportFormat::USD, options, progressCallback);
    }

    //===================================================================================
    // Validation and Analysis
    //===================================================================================

    bool ValidateExportOptions(
        ExportFormat format,
        const ExportOptions& options,
        std::vector<std::string>& errors) const override {
        
        if (!IsFormatSupported(format)) {
            errors.push_back("Export format not supported: " + ToString(format));
            return false;
        }

        auto exporter = m_factory->CreateExporter(format);
        if (!exporter) {
            errors.push_back("Failed to create exporter for format: " + ToString(format));
            return false;
        }

        return exporter->ValidateOptions(options, errors);
    }

    bool AnalyzeIModel(
        const IModelDb& imodel,
        ExportFormat format,
        std::vector<std::string>& warnings,
        std::vector<std::string>& errors) const override {
        
        // TODO: Implement iModel analysis
        // This would analyze the iModel content and check for:
        // - Unsupported element types
        // - Complex geometry that might not convert well
        // - Missing required metadata
        // - Performance considerations
        
        return true;
    }

    //===================================================================================
    // Utility Methods
    //===================================================================================

private:
    void LogError(const std::string& error) {
        std::lock_guard<std::mutex> lock(m_errorsMutex);
        m_recentErrors.push_back(error);
        if (m_recentErrors.size() > MAX_ERROR_HISTORY) {
            m_recentErrors.erase(m_recentErrors.begin());
        }
        
        if (m_loggingEnabled && !m_logFile.empty()) {
            // TODO: Write to log file
        }
    }

    void LogInfo(const std::string& info) {
        if (m_loggingEnabled && !m_logFile.empty()) {
            // TODO: Write to log file
        }
    }

    std::string GenerateExportId() {
        return "export_" + std::to_string(m_nextExportId++);
    }
};

//=======================================================================================
// Factory Implementation
//=======================================================================================

std::unique_ptr<IModelExportManager> IModelExportManager::Create() {
    return std::make_unique<ExportManagerImpl>();
}

std::unique_ptr<IModelExportManager> IModelExportManager::Create(
    std::shared_ptr<IExportFormatFactory> factory) {
    return std::make_unique<ExportManagerImpl>(factory);
}

} // namespace IModelExport
