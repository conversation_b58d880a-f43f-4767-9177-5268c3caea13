#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class IModelDb;
class ExportContext;

//=======================================================================================
// Export Format Types
//=======================================================================================

enum class ExportFormat {
    DWG,        // AutoCAD DWG format
    IFC,        // Industry Foundation Classes
    DGN,        // MicroStation DGN format
    USD         // Universal Scene Description
};

enum class ExportLOD {
    Low,        // Basic geometry only
    Medium,     // Geometry + basic properties
    High,       // Full detail with metadata
    Custom      // User-defined level
};

enum class ExportStatus {
    Success,
    Warning,
    Error,
    Cancelled
};

//=======================================================================================
// Export Configuration
//=======================================================================================

struct ExportOptions {
    std::string outputPath;
    ExportLOD levelOfDetail = ExportLOD::Medium;
    bool includeMetadata = true;
    bool includeTextures = true;
    bool includeMaterials = true;
    bool preserveHierarchy = true;
    bool enableMultiThreading = true;
    double geometryTolerance = 1e-6;
    
    // Format-specific options
    std::unordered_map<std::string, std::string> formatOptions;
};

struct DWGExportOptions : ExportOptions {
    enum class DWGVersion {
        R2018, R2021, R2024
    } version = DWGVersion::R2021;
    
    bool exportAsBlocks = true;
    bool preserveLayers = true;
    bool exportDimensions = true;
    std::string templateFile;
};

struct IFCExportOptions : ExportOptions {
    enum class IFCVersion {
        IFC2x3, IFC4, IFC4x3
    } version = IFCVersion::IFC4;
    
    enum class IFCFileFormat {
        STEP, XML, JSON
    } fileFormat = IFCFileFormat::STEP;
    
    bool exportSpaces = true;
    bool exportSystems = true;
    std::string projectInfo;
};

struct DGNExportOptions : ExportOptions {
    enum class DGNVersion {
        V7, V8
    } version = DGNVersion::V8;
    
    bool exportCells = true;
    bool preserveLevels = true;
    std::string seedFile;
};

struct USDExportOptions : ExportOptions {
    enum class USDFormat {
        ASCII,      // .usda
        Binary,     // .usdc  
        Package     // .usdz
    } format = USDFormat::Binary;
    
    bool exportAnimations = false;
    bool exportVariants = true;
    double timeCodesPerSecond = 24.0;
};

//=======================================================================================
// Progress and Error Handling
//=======================================================================================

struct ExportProgress {
    double percentage = 0.0;
    std::string currentOperation;
    size_t processedElements = 0;
    size_t totalElements = 0;
    ExportStatus status = ExportStatus::Success;
};

using ProgressCallback = std::function<bool(const ExportProgress&)>; // Return false to cancel

struct ExportResult {
    ExportStatus status = ExportStatus::Success;
    std::string outputFile;
    std::vector<std::string> warnings;
    std::vector<std::string> errors;
    size_t exportedElements = 0;
    double exportTime = 0.0; // seconds
};

//=======================================================================================
// Geometry Types
//=======================================================================================

struct Point3d {
    double x, y, z;
    Point3d(double x = 0, double y = 0, double z = 0) : x(x), y(y), z(z) {}
};

struct Vector3d {
    double x, y, z;
    Vector3d(double x = 0, double y = 0, double z = 0) : x(x), y(y), z(z) {}
};

struct Transform3d {
    double matrix[4][4];
    Transform3d();
    static Transform3d Identity();
    static Transform3d Translation(const Vector3d& translation);
    static Transform3d Rotation(const Vector3d& axis, double angle);
    static Transform3d Scale(double scale);
};

struct BoundingBox {
    Point3d min, max;
    bool IsValid() const;
    void Extend(const Point3d& point);
    void Extend(const BoundingBox& box);
};

//=======================================================================================
// Material and Appearance
//=======================================================================================

struct Color {
    float r, g, b, a;
    Color(float r = 1.0f, float g = 1.0f, float b = 1.0f, float a = 1.0f) 
        : r(r), g(g), b(b), a(a) {}
};

struct Material {
    std::string name;
    Color diffuseColor;
    Color specularColor;
    float shininess = 0.0f;
    float transparency = 0.0f;
    std::string diffuseTexture;
    std::string normalTexture;
};

//=======================================================================================
// Element Types
//=======================================================================================

enum class ElementType {
    Unknown,
    GeometricElement,
    SpatialElement,
    PhysicalElement,
    FunctionalElement,
    InformationElement,
    DefinitionElement,
    GroupInformationElement,
    Subject,
    Model
};

struct ElementInfo {
    std::string id;
    std::string classFullName;
    ElementType type = ElementType::Unknown;
    std::string userLabel;
    std::string description;
    BoundingBox boundingBox;
    Transform3d placement;
    std::unordered_map<std::string, std::string> properties;
};

//=======================================================================================
// Utility Functions
//=======================================================================================

std::string ToString(ExportFormat format);
std::string ToString(ExportStatus status);
std::string ToString(ExportLOD lod);

ExportFormat ParseExportFormat(const std::string& formatStr);

// File extension helpers
std::string GetFileExtension(ExportFormat format);
std::string GetDefaultFileName(ExportFormat format, const std::string& baseName);

} // namespace IModelExport
