/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdExtractionFiler.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

typedef void (*PFFilerCallback)
(
AcDb::DxfCode       dxfCode,
const void*         pDataToWrite,
void*               userData
);

/*--------------------------------------------------------------------------------------+
|
| This class is used to extract data from an ObjectARX object while converting from DWG
| to DGN. It is needed to get members of an object for which ObjectARX does not provide
| get methods. The caller sets up a callback function, which is called for every DXF
| group in the object. It grabs the data that it wants (by watching for a group code
| or a pattern of group codes) and ignores all the other data. The caller writes the
| callback function, creates an instance of ExtractionFiler (usually as an automatic variable),
| and then calls the ObjectARX object's dxfOut method with this filer.
|
| An example is the retrieval of seed points of an AcDbHatch entity (see rdHatch.cpp).
|
+--------------------------------------------------------------------------------------*/
class ExtractionFiler : public AcDbDxfFiler
{
private:
Acad::ErrorStatus       m_status;
PFFilerCallback         m_callbackFunction;
AcDbDatabase*           m_database;
void*                   m_userData;

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
ExtractionFiler (PFFilerCallback  callback, AcDbDatabase * database, void *userData)
    {
    m_status                = Acad::eOk;
    m_callbackFunction      = callback;
    m_database              = database;
    m_userData              = userData;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus   ExtractFrom (AcDbObjectCP pObject)
    {
    return pObject->dxfOutFields (this);
    }

private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus   Callback (AcDb::DxfCode dxfCode, const void *data)
    {
    m_callbackFunction (dxfCode, data, m_userData);
    return Acad::eOk;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual int                     rewindFiler() override  {return 1;}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  Acad::ErrorStatus      filerStatus() const override  { return m_status; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  AcDb::FilerType        filerType() const override    { return AcDb::kFileFiler; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  AcDbDatabase*          database() const override { return m_database; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                    resetFilerStatus()  { m_status = Acad::eOk; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       setError (Acad::ErrorStatus status, const ACHAR *, ...) override { m_status = status; return status; }
virtual Acad::ErrorStatus       setError(const ACHAR *, ...) override { m_status = Acad::eBadDxfSequence; return m_status; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeObjectId (AcDb::DxfCode code, const AcDbObjectId& id) override
    {
    return Callback (code, (AcDbStub*)id);
    }
virtual Acad::ErrorStatus       writeInt8 (AcDb::DxfCode code, Adesk::Int8 val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeString (AcDb::DxfCode code, const ACHAR* val) override
    {
    return Callback (code, val);
    }

virtual Acad::ErrorStatus       writeString (AcDb::DxfCode code, const AcString& val) override
    {
    return Callback (code, val.kwszPtr());
    }

virtual Acad::ErrorStatus       writeBChunk (AcDb::DxfCode code, const ads_binary& val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeAcDbHandle (AcDb::DxfCode code, const AcDbHandle& val) override
    {
    return Callback (code, &val);
    }

#if RealDwgVersion > 2009
virtual Acad::ErrorStatus       writeInt64 (AcDb::DxfCode code, Adesk::Int64 val)  override
    {
    return Callback (code, &val);
    }
#endif

virtual Acad::ErrorStatus       writeInt32 (AcDb::DxfCode code, Adesk::Int32 val)  override
    {
    return Callback (code, &val);
    }
virtual Acad::ErrorStatus       writeInt16 (AcDb::DxfCode code, Adesk::Int16 val) override
    {
    return Callback (code, &val);
    }

#if RealDwgVersion > 2009
virtual Acad::ErrorStatus       writeUInt64 (AcDb::DxfCode code, Adesk::UInt64 val) override
    {
    return Callback (code, &val);
    }
#endif

virtual Acad::ErrorStatus       writeUInt32 (AcDb::DxfCode code, Adesk::UInt32 val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeUInt16 (AcDb::DxfCode code, Adesk::UInt16 val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeUInt8 (AcDb::DxfCode code, Adesk::UInt8 val) override
    {
    return Callback (code, &val);
    }

#if defined(Adesk_Boolean_is_bool) && RealDwgVersion < 2017
virtual Acad::ErrorStatus       writeInt (AcDb::DxfCode code, int val) override
    {
    return Callback (code, &val);
    }
#endif

virtual Acad::ErrorStatus       writeBoolean (AcDb::DxfCode code, Adesk::Boolean val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeBool (AcDb::DxfCode code, bool val) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeDouble (AcDb::DxfCode code, double val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writePoint2d (AcDb::DxfCode code, const AcGePoint2d& val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writePoint3d (AcDb::DxfCode code, const AcGePoint3d& val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeVector2d (AcDb::DxfCode code, const AcGeVector2d& val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeVector3d (AcDb::DxfCode code, const AcGeVector3d& val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual Acad::ErrorStatus       writeScale3d (AcDb::DxfCode code, const AcGeScale3d& val, int = kDfltPrec) override
    {
    return Callback (code, &val);
    }

virtual bool                    includesDefaultValues() const override {return false;}

};
