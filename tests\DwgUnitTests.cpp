/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/tests/DwgUnitTests.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include "DwgUnitTestsPCH.h"

struct DwgFileOpenFixture : public ::testing::Test
    {
protected:
    DwgFileOpenParams m_defaultParams;
    virtual void SetUp () override
        {
        m_defaultParams.m_useMultiProcess = true;
        m_defaultParams.m_outputDgn.assign (L"C:\\savedfromdwg.dgn");
        m_defaultParams.m_openMode = DgnFileOpenMode::PreferablyReadWrite;
        }
    };

BeFileName CurrentDir ()
    {
    HMODULE hModule = GetModuleHandleW (NULL);
    WCHAR path[MAX_PATH];
    GetModuleFileNameW (hModule, path, MAX_PATH);
    return BeFileName (BeFileName::GetDirectoryName (path).c_str ());
    }

struct EmptyFixture : public ::testing::Test
    {};

TEST_F (EmptyFixture, ChildProcessExists)
    {
    BeFileName exeFileName =
        CurrentDir ()
        .AppendToPath (L"SatToPSProcessing")
        .AppendExtension (L"exe");

    ASSERT_TRUE (BeFileName::DoesPathExist (exeFileName.c_str ()));
    }

TEST_F (DwgFileOpenFixture, Smoke1)
    {
    ASSERT_TRUE (DwgFileOpen (m_defaultParams).OpenDwgFile (BeFileName (L"D:\\Microstation\\Acis to Parasolid\\first.dwg")).IsValid());
    }

TEST_F (DwgFileOpenFixture, lufterplan_isolated2)
    {
    ASSERT_TRUE (DwgFileOpen (m_defaultParams).OpenDwgFile (DwgGlobals::BaseDir ().AppendToPath (L"lufterplan_isolated2").AppendExtension (L"dwg")).IsValid ());
    }

TEST_F (DwgFileOpenFixture, CreatePolyface)
    {
    m_defaultParams.m_outputDgn = BeFileName (L"");
    DgnFilePtr dgnFile = DwgFileOpen (m_defaultParams).OpenDwgFile (DwgGlobals::BaseDir ().AppendToPath (L"acis_solid").AppendExtension (L"dgn"));
    ASSERT_TRUE (dgnFile.IsValid ());

    DgnModelPtr model = dgnFile->LoadModelById (dgnFile->GetLastActiveModelId ());
    ASSERT_TRUE (model.IsValid ());

    PersistentElementRefList* elmList = model->GetGraphicElementsP ();
    ASSERT_TRUE (elmList != nullptr);
    ASSERT_TRUE (!elmList->IsEmpty ());

    ElementHandle eh (*elmList->begin (), model.get ());

    //This variable will be cast to an ACIS ENTITY instance
    void* pData = nullptr;
    UInt32 dataSize;
    ASSERT_EQ (SUCCESS, BrepCellHeaderHandler::ExtractDgnStoreBRepData (&pData, &dataSize, eh));

    Transform bodyTransform = BrepCellHeaderHandler::TransformOfBRepDataEntity (eh);

    IFacetOptionsPtr options = IFacetOptions::Create ();
    options->SetMaxEdgeLength (0.1);
    PolyfaceHeaderPtr polyface = PSolidAcisInterop::PolyfaceFromAcisEntity (pData, dataSize, options, bodyTransform);
    DgnStoreHdrHandler::FreeExtractedData (pData);
    ASSERT_TRUE (polyface.IsValid ());

    EditElementHandle eeh;
    ASSERT_EQ (SUCCESS, MeshHeaderHandler::CreateMeshElement (eeh, nullptr, *polyface, true, *model));
    ASSERT_EQ (SUCCESS, eeh.AddToModel ());

    ASSERT_EQ (
        BSISUCCESS,
        dgnFile->DoSaveTo (
            DwgGlobals::BaseDir ()
            .AppendToPath (L"AcisMeshed")
            .AppendExtension (L"dgn").c_str (), DgnFileFormatType::V8
        )
    );

    //Test the correctness of the output mesh
    ASSERT_GE (polyface->GetPointCount (), 3);

    DRange3d resultRange = polyface->PointRange ();
    DRange3d inputRange;
    DataConvert::ScanRangeToDRange3d (inputRange, eh.GetElementCP ()->hdr.dhdr.range);

    double tolerance = inputRange.DiagonalDistance () * 1.0e-3;
    ASSERT_LT (DVec3d::FromStartEnd (resultRange.low, inputRange.low).Magnitude (), tolerance);
    ASSERT_LT (DVec3d::FromStartEnd (resultRange.high, inputRange.high).Magnitude (), tolerance);
    }

INSTANTIATE_TEST_CASE_P (DwgFileOpenP,
    DwgFileOpenParameterized,
    ::testing::ValuesIn (XmlParamsSingleton::Params ()));

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
int wmain (int argc,wchar_t *argv[])
    {
    PSolidAcisInterop::StartAcisSession ();
    ::testing::InitGoogleTest (&argc, argv);
    int res = RUN_ALL_TESTS ();
    PSolidAcisInterop::TerminateAcisSession ();
    return res;
    }

//All tests can be executed by running %OutRoot%Winx64\Product\DwgExampleHost\DwgUnitTests.exe
