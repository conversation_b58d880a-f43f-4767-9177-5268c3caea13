cmake_minimum_required(VERSION 3.16)

project(IModelExportFramework 
    VERSION 1.0.0
    DESCRIPTION "iModel Multi-Format Export Framework"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /WX)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
endif()

# Find required packages
find_package(Threads REQUIRED)

# Optional SDK detection
option(ENABLE_REALDWG "Enable RealDWG SDK support" OFF)
option(ENABLE_ODA_IFC "Enable ODA IFC SDK support" OFF)
option(ENABLE_ODA_DGN "Enable ODA DGN SDK support" OFF)
option(ENABLE_OPENUSD "Enable OpenUSD support" OFF)
option(BUILD_EXAMPLES "Build example applications" ON)
option(BUILD_TESTS "Build unit tests" ON)

# SDK paths (can be set via command line or environment)
set(REALDWG_ROOT "" CACHE PATH "Path to RealDWG SDK")
set(ODA_ROOT "" CACHE PATH "Path to ODA Platform SDK")
set(OPENUSD_ROOT "" CACHE PATH "Path to OpenUSD installation")
set(IMODELNATIVE_ROOT "" CACHE PATH "Path to iModelNative SDK")

#=======================================================================================
# SDK Configuration
#=======================================================================================

# RealDWG SDK
if(ENABLE_REALDWG AND REALDWG_ROOT)
    find_path(REALDWG_INCLUDE_DIR
        NAMES realdwg/base/adesk.h
        PATHS ${REALDWG_ROOT}/inc
        NO_DEFAULT_PATH
    )
    
    find_library(REALDWG_LIBRARY
        NAMES AcDbCore AcDbCore23
        PATHS ${REALDWG_ROOT}/lib
        NO_DEFAULT_PATH
    )
    
    if(REALDWG_INCLUDE_DIR AND REALDWG_LIBRARY)
        set(REALDWG_FOUND TRUE)
        add_definitions(-DREALDWG_AVAILABLE)
        message(STATUS "RealDWG SDK found: ${REALDWG_ROOT}")
    else()
        message(WARNING "RealDWG SDK not found at: ${REALDWG_ROOT}")
    endif()
endif()

# ODA Platform SDK
if((ENABLE_ODA_IFC OR ENABLE_ODA_DGN) AND ODA_ROOT)
    find_path(ODA_INCLUDE_DIR
        NAMES OdaCommon.h
        PATHS ${ODA_ROOT}/Kernel/Include
        NO_DEFAULT_PATH
    )
    
    if(ODA_INCLUDE_DIR)
        set(ODA_FOUND TRUE)
        message(STATUS "ODA Platform SDK found: ${ODA_ROOT}")
        
        if(ENABLE_ODA_IFC)
            add_definitions(-DODA_IFC_AVAILABLE)
        endif()
        
        if(ENABLE_ODA_DGN)
            add_definitions(-DODA_DGN_AVAILABLE)
        endif()
    else()
        message(WARNING "ODA Platform SDK not found at: ${ODA_ROOT}")
    endif()
endif()

# OpenUSD
if(ENABLE_OPENUSD AND OPENUSD_ROOT)
    find_path(OPENUSD_INCLUDE_DIR
        NAMES pxr/pxr.h
        PATHS ${OPENUSD_ROOT}/include
        NO_DEFAULT_PATH
    )
    
    find_library(OPENUSD_USD_LIBRARY
        NAMES usd
        PATHS ${OPENUSD_ROOT}/lib
        NO_DEFAULT_PATH
    )
    
    if(OPENUSD_INCLUDE_DIR AND OPENUSD_USD_LIBRARY)
        set(OPENUSD_FOUND TRUE)
        add_definitions(-DOPENUSD_AVAILABLE)
        message(STATUS "OpenUSD found: ${OPENUSD_ROOT}")
    else()
        message(WARNING "OpenUSD not found at: ${OPENUSD_ROOT}")
    endif()
endif()

# iModelNative SDK
if(IMODELNATIVE_ROOT)
    find_path(IMODELNATIVE_INCLUDE_DIR
        NAMES iModelNative.h
        PATHS ${IMODELNATIVE_ROOT}/include
        NO_DEFAULT_PATH
    )
    
    if(IMODELNATIVE_INCLUDE_DIR)
        set(IMODELNATIVE_FOUND TRUE)
        add_definitions(-DIMODELNATIVE_AVAILABLE)
        message(STATUS "iModelNative SDK found: ${IMODELNATIVE_ROOT}")
    else()
        message(WARNING "iModelNative SDK not found at: ${IMODELNATIVE_ROOT}")
    endif()
endif()

#=======================================================================================
# Source Files
#=======================================================================================

# Core framework sources
set(CORE_SOURCES
    src/core/ExportManager.cpp
    src/core/ExportContext.cpp
    src/core/ExportFormatFactory.cpp
    src/core/GeometryConverter.cpp
)

# Format-specific sources
set(FORMAT_SOURCES
    src/formats/dwg/DWGExporter.cpp
    src/formats/ifc/IFCExporter.cpp
    src/formats/dgn/DGNExporter.cpp
    src/formats/usd/USDExporter.cpp
)

# Utility sources
set(UTIL_SOURCES
    src/utils/FileUtils.cpp
    src/utils/StringUtils.cpp
    src/utils/MathUtils.cpp
)

# All sources
set(ALL_SOURCES
    ${CORE_SOURCES}
    ${FORMAT_SOURCES}
    ${UTIL_SOURCES}
)

# Public headers
set(PUBLIC_HEADERS
    include/IModelExportManager.h
    include/IExportFormat.h
    include/ExportTypes.h
)

#=======================================================================================
# Library Target
#=======================================================================================

# Create main library
add_library(IModelExportFramework ${ALL_SOURCES} ${PUBLIC_HEADERS})

# Set target properties
set_target_properties(IModelExportFramework PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    PUBLIC_HEADER "${PUBLIC_HEADERS}"
)

# Include directories
target_include_directories(IModelExportFramework
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Link libraries
target_link_libraries(IModelExportFramework
    PUBLIC
        Threads::Threads
)

# SDK-specific linking
if(REALDWG_FOUND)
    target_include_directories(IModelExportFramework PRIVATE ${REALDWG_INCLUDE_DIR})
    target_link_libraries(IModelExportFramework PRIVATE ${REALDWG_LIBRARY})
endif()

if(ODA_FOUND)
    target_include_directories(IModelExportFramework PRIVATE ${ODA_INCLUDE_DIR})
    # Add ODA libraries as needed
endif()

if(OPENUSD_FOUND)
    target_include_directories(IModelExportFramework PRIVATE ${OPENUSD_INCLUDE_DIR})
    target_link_libraries(IModelExportFramework PRIVATE ${OPENUSD_USD_LIBRARY})
endif()

if(IMODELNATIVE_FOUND)
    target_include_directories(IModelExportFramework PRIVATE ${IMODELNATIVE_INCLUDE_DIR})
    # Add iModelNative libraries as needed
endif()

#=======================================================================================
# Examples
#=======================================================================================

if(BUILD_EXAMPLES)
    add_executable(BasicExportExample examples/basic_export.cpp)
    target_link_libraries(BasicExportExample IModelExportFramework)
    
    add_executable(AdvancedExportExample examples/advanced_export.cpp)
    target_link_libraries(AdvancedExportExample IModelExportFramework)
    
    # Set output directory for examples
    set_target_properties(BasicExportExample AdvancedExportExample
        PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/examples
    )
endif()

#=======================================================================================
# Tests
#=======================================================================================

if(BUILD_TESTS)
    enable_testing()
    
    # Find or build test framework
    find_package(GTest QUIET)
    if(NOT GTest_FOUND)
        # Download and build GoogleTest
        include(FetchContent)
        FetchContent_Declare(
            googletest
            URL https://github.com/google/googletest/archive/03597a01ee50ed33e9fd7188ec8e5ef0a4c8bb8c.zip
        )
        FetchContent_MakeAvailable(googletest)
    endif()
    
    # Test sources
    set(TEST_SOURCES
        tests/test_export_manager.cpp
        tests/test_export_formats.cpp
        tests/test_geometry_conversion.cpp
        tests/test_coordinate_transform.cpp
    )
    
    # Create test executable
    add_executable(IModelExportTests ${TEST_SOURCES})
    target_link_libraries(IModelExportTests
        IModelExportFramework
        gtest_main
        gtest
    )
    
    # Register tests
    include(GoogleTest)
    gtest_discover_tests(IModelExportTests)
    
    # Set output directory for tests
    set_target_properties(IModelExportTests
        PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
endif()

#=======================================================================================
# Installation
#=======================================================================================

# Install library
install(TARGETS IModelExportFramework
    EXPORT IModelExportFrameworkTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include/IModelExport
)

# Install headers
install(DIRECTORY include/
    DESTINATION include/IModelExport
    FILES_MATCHING PATTERN "*.h"
)

# Install examples
if(BUILD_EXAMPLES)
    install(TARGETS BasicExportExample AdvancedExportExample
        RUNTIME DESTINATION bin/examples
    )
endif()

# Export targets
install(EXPORT IModelExportFrameworkTargets
    FILE IModelExportFrameworkTargets.cmake
    NAMESPACE IModelExport::
    DESTINATION lib/cmake/IModelExportFramework
)

# Create config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    IModelExportFrameworkConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    cmake/IModelExportFrameworkConfig.cmake.in
    IModelExportFrameworkConfig.cmake
    INSTALL_DESTINATION lib/cmake/IModelExportFramework
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/IModelExportFrameworkConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/IModelExportFrameworkConfigVersion.cmake
    DESTINATION lib/cmake/IModelExportFramework
)

#=======================================================================================
# Package Configuration
#=======================================================================================

set(CPACK_PACKAGE_NAME "IModelExportFramework")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "Bentley Systems")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

if(WIN32)
    set(CPACK_GENERATOR "ZIP;NSIS")
else()
    set(CPACK_GENERATOR "TGZ;DEB;RPM")
endif()

include(CPack)

#=======================================================================================
# Status Summary
#=======================================================================================

message(STATUS "")
message(STATUS "=== iModel Export Framework Configuration ===")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "SDK Support:")
message(STATUS "  RealDWG: ${REALDWG_FOUND}")
message(STATUS "  ODA IFC: ${ENABLE_ODA_IFC}")
message(STATUS "  ODA DGN: ${ENABLE_ODA_DGN}")
message(STATUS "  OpenUSD: ${OPENUSD_FOUND}")
message(STATUS "  iModelNative: ${IMODELNATIVE_FOUND}")
message(STATUS "")
message(STATUS "Build Options:")
message(STATUS "  Examples: ${BUILD_EXAMPLES}")
message(STATUS "  Tests: ${BUILD_TESTS}")
message(STATUS "")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "===============================================")
message(STATUS "")
