/*---------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFileIO.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/
#include    "rDwgInternal.h"
#include    <Mstn\RealDwg\rDwgAPI.h>
#include    <Mstn\RealDwg\DwgPlatformHost.h>
#include    <Mstn\RealDwg\rDwgUtil.h>
#include    <Mstn\RealDwg\rDwgDefs.h>
#include    "rDwgFileIO.h"

#include    <windows.h>

#include    <shlwapi.h> // needed for RealDwg URL stuff.
#include    <wininet.h> // "       "
#include    <share.h>
#include    <io.h>
#include    <fcntl.h>
#include    <stdio.h>
#include    <string>
#include    <stdarg.h>  // variable argument list
#include    <DgnPlatform\Tools\fileutil.h>
#include    <Bentley\DocProperty.h>
#include    <Bentley\BeTextFile.h>
#include    <Bentley\BeTimeUtilities.h>

#include    <realdwg\base\summinfo.h>        // a RealDWG header file

#include    <DgnPlatform\DgnFileIO\dgnitemindex.h>
#include    <DgnPlatform\DgnFile.h>


USING_NAMESPACE_BENTLEY_DGNPLATFORM


BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

/*----------------------------------------------------------------------+
|                                                                       |
|   Static Data                                                         |
|                                                                       |
+----------------------------------------------------------------------*/

static  StatusInt       s_realDwgInitializeStatus                       = -1;
static  bool            s_hostAppInitialized                            = false;

/*=================================================================================**//**
* DwgModelReader methods
* @bsiclass                                                     RayBentley      10/00
+===============+===============+===============+===============+===============+======*/
class           DwgModelReader : public DgnModelReader
{
RealDwgFileIO*      m_fileIO;

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
DwgModelReader
(
DgnModelP           pModel,
RealDwgFileIO*      pFileIO
) : DgnModelReader (pModel)
    {
    m_fileIO       = pFileIO;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
~DwgModelReader () {}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    KeithBentley    10/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt ReadSectionsIntoCache (UInt32* majorVersion, UInt32* minorVersion, DgnModelSections& sections, DgnModelFillContextP context)
    {
    // If the refCount is 0 we're going to have problems (cache will be freed in mdlModelRef_free (TR# 153541).
    BeAssert ((m_dgnModel->GetRefCount() > 0) || (m_fileIO->GetDgnFile()->GetFileRefCount() > 0));

    DgnFileP    fileP                   = m_fileIO->GetDgnFile();
    DgnModelR   dictionaryModel         = fileP->GetDictionaryModel ();
    bool        dictionaryModelReadOnly = dictionaryModel.IsReadOnly();
    bool        dictionaryDirty         = (0 != dictionaryModel.GetChangeCount());

    /*--------------------------------------------------------------------------------------------------------------
    Copy & save the tcb cached by the host as it may be changed for element creation: this is needed because during
    cach filling we may have to change host's TCB for element creation.  The file header is set from DWG file, and
    then it gets copied to host's TCB to ensure correct model element creation.  At the end of cach filling (i.e. end
    of this call), all elements should have been created, we then can restore host's TCB.  The TCB restoring should
    work for all cases that initiate DWG cache filling:

        1) Attach DWG as reference: host's TCB should not be changed by cache filling, TFS 259999.
        2) Place DWG as a cell: host TCB should not be changed.  TFS 250792 (active cell name).
        3) Open DWG file: the session manager will update host's TCB from DgnFile header after cache filling so the
            host's TCB will be once again copied from file header.

    All cases eventually call through this method. Host's TCB will be changed and restored.
    --------------------------------------------------------------------------------------------------------------*/
    TcbP        savedTcb = new Tcb ();
    if (!DwgPlatformHost::Instance()._GetPersistentTcbForSilentSave(savedTcb))
        {
        delete savedTcb;
        savedTcb = nullptr;
        }

    // Fix for TR# 131955 - When DWG is being loaded as cell the dictionary cache may be marked as readonly - this
    // causes failure when we rewrite orphan tag set definitions.
    if (dictionaryModelReadOnly)
        dictionaryModel.SetReadOnly (false);

    FileHolder* fileHolder = m_fileIO->GetDwgFileHolder ();
    assert (NULL != fileHolder);

    RealDwgHostApp::Instance().SetWorkingDatabase (fileHolder->GetDatabase());

    StatusInt status = fileHolder->ReadModelIntoCache (m_dgnModel, m_dgnModel->GetModelId(), DgnModelSections::None != (sections & DgnModelSections::Dictionary));

    // When we call ReadModelIntoCache, it ALWAYS loads both the control and graphic sections into the cache.
    // We need to change sections - that is why it is a reference argument.
    sections = sections | DgnModelSections::Model;

    if (dictionaryModelReadOnly)
        dictionaryModel.SetReadOnly(true);

    // We've just loaded the cache - clear any dirty flags that may have been set during the cache loading process.
    m_dgnModel->ClearAllDirtyFlags();

    // Same thing with model cache - if it was not dirty before opening this cache, clear its dirty flags as well. (TR# 176113).
    if (!dictionaryDirty)
        dictionaryModel.ClearAllDirtyFlags ();

    // restore host's cached tcb:
    if (nullptr != savedTcb)
        {
        DwgPlatformHost::Instance()._SetPersistentTcbForSilentSave (savedTcb);
        delete savedTcb;
        }

    // assert that we have left no open objects.
    RealDwgUtil::AssertNoOpenObjects (fileHolder->GetDatabase());

    if (BSISUCCESS == status && fileHolder->IsFileReadOnlyForPrinting() && !fileP->IsReadOnly() && nullptr != context && context->GetFillDgnAttachment() == nullptr)
        fileP->SetReadOnly (true, nullptr);

    return status;
    }
};

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgFileIO::RealDwgFileIO (DgnFileP pFile, DgnFileFormatType format) : DgnFileIO (pFile)
    {
    // format must be either DgnFileFormatType::DWG or DgnFileFormatType::DXF
    m_format                    = format;
    m_dwgFileHolder             = NULL;
    m_dgnSeedFileName[0]        = 0;
    m_createNewFile             = false;
    m_fileHolderReleased        = false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgFileIO::~RealDwgFileIO ()
    {
    //  If we're going away, make sure opened file is closed.
    CloseDgnFileIO();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
DgnFileFormatType   RealDwgFileIO::GetFormat ()
    {
    return m_format;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::GetModelReader
(
DgnModelReader**            ppStream,
DgnModel*                   pCache
)
    {
    if (NULL == GetDwgFileHolder())
        {
        *ppStream = NULL;
        return BSIERROR;
        }

    *ppStream = new DwgModelReader (pCache, this);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        CantWriteThisVersion (int majorVersion, DgnFileFormatType format)
    {
    if (DwgFileVersion_Unknown == majorVersion)
        return  true;
    else if (DgnFileFormatType::DWG == format)
        return (majorVersion < DwgFileVersion_14);
    else
        return (majorVersion < DwgFileVersion_11);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::LoadFile
(
DesignFileHeader*           pHdr,
bool*                       openedReadonly,
StatusInt*                  rwStatus,
DgnFileOpenMode             openMode,
DgnFileLoadContext*         context
)
    {
    WString     fileNameString = GetFileName();
    WCharCP     fileName       = fileNameString.c_str();
    if (fileNameString.empty())
        return DGNOPEN_STATUS_BadFilename;

    if (m_format == DgnFileFormatType::DXF)
        {
        RealDxfFileType rdwgFileType;
        if (!rdwgFileType.ValidateFile (&pHdr->format, (int*)&pHdr->version, (int*)&pHdr->subVersion, NULL, NULL, fileName))
            return DGNOPEN_STATUS_UnrecognizedFormat;
        }
    else
        {
        RealDwgFileType rdwgFileType;
        if (!rdwgFileType.ValidateFile (&pHdr->format, (int*)&pHdr->version, (int*)&pHdr->subVersion, NULL, NULL, fileName))
            return DGNOPEN_STATUS_UnrecognizedFormat;
        }

    if (IsOpen())
        return DGNOPEN_STATUS_AlreadyOpen;

    StatusInt status;
    if (SUCCESS != (status = FindAndValidateDgnSeedFile()))
        return status;

    if (!Initialize())
        return FILE_OPEN_STATUS_UnableToLoadInterface;

    // DWG file of R13 or DXF file of R10 and earlier releases can not be saved:
    bool forceReadonly;
    if (forceReadonly = ((openMode != DgnFileOpenMode::ReadOnly) && CantWriteThisVersion (pHdr->version, m_format)))
        openMode = DgnFileOpenMode::ReadOnly;

    // create the fileHolder for later use.
    m_dwgFileHolder = new FileHolder ((DgnFileP) GetDgnFile(), fileName);

    // We use the superclass' DgnFileHandle, m_fileHandle, to manage the read/write exclusions for the file.
    // (We can't use it to actually load the file, because there is no RealDWG API that takes a file handle to read the file.)
    // If we want write access to the file, we leave it open for read, excluding any writers.
    // if we want only need read access to the file, we don't need to leave it open at all.
    if (DGNFILE_STATUS_Success != (status = m_fileHandle.TryOpen (fileName, openMode, true, openedReadonly, rwStatus)))
        return  status;

    m_fileHandle.AddRef ();

    // set the last saved time from the DWG file time - TFS 915268.
    ::FILETIME   mtime;
    if (nullptr != pHdr && ::GetFileTime(m_fileHandle.GetHandle(), nullptr, nullptr, &mtime))
        pHdr->lastSaveTime = BeTimeUtilities::ConvertFiletimeToUnixMillisDouble (mtime);

    // if not readonly, we reopen the file readOnly, preventing writers. If readOnly, we close the file.
    if (!*openedReadonly)
        m_fileHandle.ReopenUnlocked (fileName);
    else
        m_fileHandle.Release();

    if (forceReadonly)
        DwgPlatformHost::Instance()._ReportMessage (DwgPlatformHost::REPORTMESSAGE_ReadOnlyForced, fileName);

    return  DGNFILE_STATUS_Success;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::FindAndValidateDgnSeedFile ()
    {
    if (0 != m_dgnSeedFileName[0])
        return SUCCESS;

    WString     seedFileName;
    if (!MstnInterfaceHelper::Instance().GetSettings().GetDgnSeedFile (seedFileName))
        {
        if (SUCCESS != ConfigurationManager::GetVariable (seedFileName, L"MS_TRANSEED"))
            return FILE_OPEN_STATUS_UnableToOpenSeed;
        }

    WChar     seedFile[MAXFILELENGTH];
    RealDwgUtil::TerminatedStringCopy (seedFile, seedFileName.c_str(), _countof(seedFile));
    WCharP    pEndAt = wcschr (seedFile, ';');
    if (pEndAt != NULL)
        *pEndAt = 0;

    BeFileName  foundFile;
    if (SUCCESS != util_findFile (NULL, &foundFile, seedFile, L"MS_SEEDFILES", NULL, 0))
        {
        if ( (SUCCESS != util_findFile (NULL, &foundFile, NULL, L"MS_TRANSEED", NULL, 0)) &&
             (SUCCESS != util_findFile (NULL, &foundFile, L"transeed.dgn", L"MS_SEEDFILES", NULL, 0)) )
            return FILE_OPEN_STATUS_UnableToOpenSeed;
        else
            DwgPlatformHost::Instance()._ReportMessage (DwgPlatformHost::REPORTMESSAGE_DgnSeedFileNotFound, seedFile);
        }

    // Validate the seed file.
    DgnFileFormatType   format;
    if (!dgnFileObj_validateFile (&format, NULL, NULL, NULL, NULL, NULL, foundFile) || (format != DgnFileFormatType::V7 && format != DgnFileFormatType::V8))
        return DWGOPEN_STATUS_InvalidSeed;

    wcscpy (m_dgnSeedFileName, foundFile);

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::InitializeFileHolderFromSeedFile ()
    {
    DgnFileStatus   openStatus = DGNFILE_STATUS_Success;
    DgnDocumentPtr  dgnDocument = DgnDocument::CreateFromFileName (openStatus, m_dgnSeedFileName, NULL, DEFDGNFILE_ID, DgnDocument::FetchMode::Read);
    if (DGNFILE_STATUS_Success != openStatus || !dgnDocument.IsValid())
        return  DWGOPEN_STATUS_CannotOpenSeed;

    DgnFilePtr      dgnSeedFile = DgnFile::Create (*dgnDocument.get(), DgnFileOpenMode::ReadOnly);
    if (!dgnSeedFile.IsValid() || DGNFILE_STATUS_Success != dgnSeedFile->LoadDgnFile(NULL))
        return  DWGOPEN_STATUS_CannotOpenSeed;

    StatusInt       status = BSISUCCESS;
    DgnModelP       seedModelRef = dgnSeedFile->LoadRootModelById (&status, 0, true);
    if (BSISUCCESS != status || NULL == seedModelRef)
        return  DGNFILE_ERROR_NoModel;

    m_dwgFileHolder->InitializeFromSeedModel (seedModelRef);
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**

* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
FileHolder*                 RealDwgFileIO::GetDwgFileHolder ()
    {
    // this method creates the DWG fileholder if necessary. (that is necessary when we have
    //  a reference file from which we are reading additional models. That happens when
    //  you need to determine whether levels are used in all models, when you are merging
    //  references, etc.)
    if (NULL == m_dwgFileHolder)
        {
        m_dwgFileHolder = new FileHolder ((DgnFileP)GetDgnFile(), GetFileName().c_str());
        StatusInt status;
        if (SUCCESS != (status = InitializeFileHolderFromSeedFile()))
            {
            assert (false);
            delete m_dwgFileHolder;
            m_dwgFileHolder = NULL;
            }
        // if this is a reference that we once loaded and subsequently released, we need to reload the Acad database (we already have the DGN model index, need the Acad ModelIndexItems).
        if (m_fileHolderReleased && nullptr != m_dwgFileHolder)
            {
            m_dwgFileHolder->LoadAcadDatabase();
            m_fileHolderReleased = false;
            }
        }
    return m_dwgFileHolder;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::DeleteModel (ModelId modelId)
    {
    if (NULL != GetDwgFileHolder())
        return m_dwgFileHolder->DeleteModel (modelId);

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::CreateDgnModel
(
DgnModelP&                  model,
RealDwgModelIndexItem&      modelItem
)
    {
    model   = this->InstantiateDgnModel (modelItem.GetId());

    StatusInt status;
    if (BSISUCCESS != (status = AddDgnModelToInternalList (model)))
        {
        BeAssert (false);

        delete model;
        model = NULL;
        return status;
        }

    // put the modelInfo into the newly instantiated cache.
    ModelInfoCR     modelInfo = modelItem.GetModelInfo();
    MSElement       modelHeaderElement;
    modelInfo.ToModelElement (modelHeaderElement, NULL, true);
    model->SaveModelHeader(&modelHeaderElement, true);

    // put the frozen layer information in there.
    UInt16                  nFrozenLayers;
    AcDbObjectIdArray       frozenLayers;

    modelItem.GetFrozenLayers (frozenLayers);
    if (0 != (nFrozenLayers = (UInt16)frozenLayers.length()))
        {
        ElementId   *pFrozenLayerIds = (ElementId *) _alloca (nFrozenLayers * sizeof(ElementId));

        // NOTE: This works OK now, because CreateDgnModel is used only when we're going from DWG to DGN, and in that case
        //       the AcDbHandle is the same as the ElementId. What we would like to do is to ask the ConvertContext to do
        //       the conversion, but in this case we don't have one.
        for (UInt16 iLayer=0; iLayer<nFrozenLayers; iLayer++)
            {
            AcDbObjectId    layer = frozenLayers.at (iLayer);
            pFrozenLayerIds[iLayer] = RealDwgUtil::CastDBHandle (layer.handle());
            }

        MSElement       element;
        dgnModel_getLinkageHolderElement (model, &element);
        mdlLinkage_setElementIds (&element, pFrozenLayerIds, nFrozenLayers, DEPENDENCYAPPID_MicroStation, DEPENDENCYAPPVALUE_ReferenceFrozenLevel);
        dgnModel_setLinkageHolderElement (model, &element, true);
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::LoadModelHeaders ()
    {
    if (NULL == GetDwgFileHolder())
        return BSIERROR;

    // For DWG files, this creates a model for every model in the index.
    RealDwgModelIndexItemArray&     modelIndexItems = m_dwgFileHolder->GetModelIndexItems();
    for each (RealDwgModelIndexItemP modelItem in modelIndexItems)
        {
        DgnModelP   newModel;
        StatusInt   status;
        if (SUCCESS != (status = this->CreateDgnModel (newModel, *modelItem)))
            return status;
        }

    return BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::_LoadModelIndex
(
IStorage*                   pRootStore,
ModelIndex*                 pModelIndex
)
    {
    assert (NULL != m_dwgFileHolder);

    // get the information we need from the seed file.
    StatusInt   status;
    if (SUCCESS != (status = InitializeFileHolderFromSeedFile ()))
        return status;

    // load the Autocad database.
    if (SUCCESS != (status = m_dwgFileHolder->LoadAcadDatabase()))
        return status;

    // create the model index.
    pModelIndex->SetVersion (INDEX_VERSION_NUMBER);

    RealDwgModelIndexItemArray&     modelIndexItems = m_dwgFileHolder->GetModelIndexItems();
    for (RealDwgModelIndexItemArray::iterator iModelItem = modelIndexItems.begin(); iModelItem != modelIndexItems.end(); ++iModelItem)
        {
        RealDwgModelIndexItemP     indexItem = *iModelItem;
        msDgnFile_appendToModelIndex ((DgnFileP) GetDgnFile(), indexItem->GetModelInfoCP(), indexItem->GetId());
        }

    // Now that model index is loaded, set the highest modelID.
    FOR_EACH (ModelIndexItemCR indexItem, m_fileObj->GetModelIndex())
        m_fileObj->SetHighestModelID (indexItem.GetModelId());

    // Mark the index as clean at the end of read.
    dgnFileObj_clearModelIndexDirty (m_fileObj);

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt RealDwgFileIO::_CreateCacheAndLoadHeader (DgnModelP& model, ModelId modelIDtoRead)
    {
    if (NULL == GetDwgFileHolder())
        return BSIERROR;

    RealDwgModelIndexItemP  modelItem;
    if (NULL == (modelItem = m_dwgFileHolder->GetModelItemByModelId (modelIDtoRead)))
        return BSIERROR;

    StatusInt   status = this->CreateDgnModel (model, *modelItem);
    if (BSISUCCESS != status)
        return  status;

    /*----------------------------------------------------------------------------------------------------------
    AutoCAD overrides certain system variables from master file to xref file attachments, but our RealDWG fileIO
    loads one file at a time and has no place to track master vs reference files.  We rely on the host to do that.
    When this method is called and the current file is the master file, we send the DWG file data to the host,
    and the host can save it.  Later on we retrieve the data from the host so we can override reference file data.
    ----------------------------------------------------------------------------------------------------------*/
    DgnFileP        dgnFile = m_dwgFileHolder->GetFile ();
    AcDbDatabase*   dwg = m_dwgFileHolder->GetDatabase ();
    if (NULL != dwg && NULL != dgnFile && DgnFilePurpose::MasterFile == dgnFile->GetFilePurpose() && NULL == dgnFile->GetFirstModelRef())
        {
        DwgPlatformHost::Instance()._SetMasterDwgFile (dwg, dgnFile);
        // cache master file viewport ID's, but at this time only if C3D object enablers are loaded
        if (m_dwgFileHolder->IsC3dObjectEnablerLoaded())
            RealDwgUtil::CacheMasterFileViewports (dwg);
        }

    // update default font before processing any model as a WorkSet change can reset the font manager - TFS 200515.
    DgnFontManager::GetManager().Resume();
    m_dwgFileHolder->SetDefaultFont (DgnFontManager::GetDefaultAcadFontP());

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::CloseDgnFileIO
(
)
    {
    // delete DWG/DXF stuff here.
    ReleaseForeignSchema();

    if (m_fileHandle.IsOpen())
        {
        int refcount = m_fileHandle.Release ();
        BeAssert (0 == refcount);
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgFileIO::ReleaseForeignSchema ()
    {
    if (NULL != m_dwgFileHolder)
        {
        if (m_dwgFileHolder->GetDatabase() == RealDwgHostApp::Instance().workingDatabase());
            RealDwgHostApp::Instance().SetWorkingDatabase (NULL);
        delete m_dwgFileHolder;
        m_dwgFileHolder         = NULL;
        m_fileHolderReleased    = true;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDwgFileIO::IsForeignSchemaLoaded ()
    {
    return  NULL != m_dwgFileHolder;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   06/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgFileIO::FileReload (bool beforeReload)
    {
    /*-----------------------------------------------------------------------------------------------------
    When this method was originally implemented to fix TR 271583, a check for (!beforeReload) was used to
    conditionally release the FileHolder.  Maybe at that time it was a right thing to do, but without releasing
    the FileHolder for beforeReload==true, a file reload would result in importing DWG entities from the same
    database that has been opened from prior file open, as documented in TFS's 95151 & 184612.  We want file
    reloading to always release the database such that it will be reloaded with changed data in the file.
    -----------------------------------------------------------------------------------------------------*/
    ReleaseForeignSchema();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2000
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ModelSaveSupported (DgnModelP model)
    {
    // Screen out hidden models (used by animator, applications etc.)
    if (model->GetModelFlag(MODELFLAG_NOT_IN_CELL_LIST) > 0 && model->GetModelFlag(MODELFLAG_NOT_IN_MODEL_LIST) > 0)
        return false;

    switch (model->GetModelType())
        {
        case DgnModelType::Extraction_Deprecated:
            // if a hidden line model somehow got turned into the default model as seen in TFS 129682, we still should save it:
            DgnFileP        dgnFile;
            if (model->IsDictionaryModel() || (nullptr != (dgnFile = model->GetDgnFileP()) && dgnFile->GetDefaultModelId() == model->GetModelId()))
                return  true;
            return false;

        default:
            return true;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::FindAndValidateDwgSeedFile ()
    {
    WString     seedFileName;
    if (!MstnInterfaceHelper::Instance().GetSettings().GetDwgSeedFile (seedFileName))
        {
        if (SUCCESS != ConfigurationManager::GetVariable (seedFileName, L"MS_DWGSEED"))
            return FILE_OPEN_STATUS_UnableToOpenSeed;
        }

    size_t      endAt = seedFileName.find (L';');
    if (endAt > 1)
        seedFileName = seedFileName.substr (0, endAt);

    BeFileName      foundFile;
    if (SUCCESS != util_findFile (NULL, &foundFile, seedFileName.GetWCharCP(), L"MS_SEEDFILES", NULL, 0))
        {
        WString     fileName, fileExt;
        BeFileName::ParseName (NULL, NULL, &fileName, &fileExt, seedFileName.GetWCharCP());
        BeFileName::BuildName (seedFileName, NULL, NULL, fileName.GetWCharCP(), fileExt.GetWCharCP());
        if (SUCCESS != util_findFile (NULL, &foundFile, seedFileName.GetWCharCP(), L"MS_SEEDFILES", NULL, 0))
            return FILE_OPEN_STATUS_UnableToOpenSeed;
        }

    // Validate the seed file.
    RealDwgFileType     dwgFileType;
    if (!dwgFileType.ValidateFile(NULL, NULL, NULL, NULL, NULL, foundFile))
        {
        RealDxfFileType dxfFileType;
        if (!dxfFileType.ValidateFile(NULL, NULL, NULL, NULL, NULL, foundFile))
            return DWGOPEN_STATUS_InvalidSeed;
        }

    wcscpy (m_dwgSeedFileName, foundFile);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::CreateAcadDatabaseFromOpenFile
(
WCharCP                   fileName,
bool                        omitRefPaths
)
    {
    m_fileObj->CreateDgnModelForAllModels();
    m_fileObj->FillAllLoadedModels();

    StatusInt       status;
    if (SUCCESS != (status = FindAndValidateDwgSeedFile ()))
        return status;

    DgnModelP       defaultModel = NULL;
    if (NULL == (defaultModel = m_fileObj->FindLoadedModelById (m_fileObj->GetDefaultModelId())))
        return BSIERROR;

    // create the FileHolder.
    m_dwgFileHolder = new FileHolder ((DgnFileP) m_fileObj, m_dwgSeedFileName, defaultModel);
    if (SUCCESS != (status = m_dwgFileHolder->LoadAcadDatabaseFromSeedDwgFile (defaultModel)))
        return status;

    if (SUCCESS != (status = this->SaveAllModelsToAcadDatabase (fileName, omitRefPaths)))
        return status;

    // set transeed for opening the file back for cases that will not need to create newDgnFile - TFS 371671.
    if (SUCCESS != (status = FindAndValidateDgnSeedFile ()))
        return status;

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::SaveAllModelsToAcadDatabase (WCharCP fileName, bool omitRefPaths)
    {
    int                 maxCount    = dgnFileObj_getModelCount (m_fileObj);
    DgnModelP*          modelList   = (DgnModelP *)_alloca (maxCount * sizeof(DgnModelP));
    int                 modelCount  = 0;

    // create progress meter for saving the master file - a meter saving ref files should have been created at the begining of saving all refs (dgnFileObj_doSaveRefsAs).
    IDgnProgressMeterPtr    progressMeter;
    bool                    meterCreated = DwgPlatformHost::Instance()._CreateProgressMeterSavingChanges (progressMeter);
    if (!meterCreated)
        progressMeter = DgnPlatformLib::GetHost().GetProgressMeter ();
    if (progressMeter.IsValid())
        progressMeter->AddTasks (maxCount);

    DgnAttachmentLoadOptions    loadOptions (true, true, false);

    FOR_EACH (DgnModelP dgnModel, m_fileObj->GetLoadedModelsCollection())
        {
        if (dgnModel->GetModelInfoCP()->GetPropertyFlags().nonIndexed)
            continue;
        if (progressMeter.IsValid())
            progressMeter->SetCurrentTaskDescription (fileName, dgnModel->GetModelName());

        if (BSISUCCESS != dgnModel->ReadAndLoadDgnAttachments(loadOptions))
            {
            DIAGNOSTIC_PRINTF ("Failed loading model %ls\n", dgnModel->GetModelName());
            continue;
            }

        if (progressMeter.IsValid())
            progressMeter->OnTaskComplete ();

        modelList[modelCount++] = dgnModel;
        if (modelCount > maxCount)
            {
            BeAssert (false && L"More models in collection than expected!!");
            break;
            }
        }

    if (modelCount > 0)
        {
        if (progressMeter.IsValid())
            {
            // add dictionary model as a new progress task but do not display its name which may confuse users:
            progressMeter->AddTasks (maxCount + 1);
            progressMeter->SetCurrentTaskDescription (fileName, L".........");
            }

        SaveModelToAcadDatabase (&m_fileObj->GetDictionaryModel(), -1, fileName, omitRefPaths);

        if (progressMeter.IsValid())
            progressMeter->OnTaskComplete ();

        for (int iModel = 0; iModel < modelCount; iModel++)
            {
            if (progressMeter.IsValid())
                progressMeter->SetCurrentTaskDescription (fileName, modelList[iModel]->GetModelName());

            SaveModelToAcadDatabase (modelList[iModel], modelList[iModel]->GetModelId(), fileName, omitRefPaths);

            if (progressMeter.IsValid())
                progressMeter->OnTaskComplete ();
            }
        }

    // This is added for Defect 1126334:PP : Export to DWG Hangs and crashes
    // We need to clear cached static data for next save task.
    if ( nullptr != m_dwgFileHolder )
        m_dwgFileHolder->ClearItemLibrariesMap ();

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
Public StatusInt            RealDwgFileIO::SaveModelToAcadDatabase
(
DgnModelP                   modelRef,
int                         modelId,
WCharCP                     fileName,
bool                        omitRefPaths
)
    {
    assert (NULL != m_dwgFileHolder);
    if (NULL == m_dwgFileHolder)
        return BSIERROR;

    // Once we're done, don't want saveModelChanges to get called again.
    modelRef->ClearAllDirtyFlags();

    if (!ModelSaveSupported (modelRef))
        return SUCCESS;

    DgnModelP               check3dModel = modelRef;
    DgnModelSections        sections  = DgnModelSections::Model;
    if (-1 == modelId)
        {
        // If this is the dictionary model.  Use the cache/modelRef for the default model so that the
        // Units info can be extracted (for shared cells etc). in dictionary model.
        check3dModel    = m_fileObj->FindLoadedModelById (m_fileObj->GetDefaultModelId());
        sections        = DgnModelSections::Dictionary;
        }
    else if (dgnModel_getModelFlag (modelRef, MODELFLAG_NOT_IN_MODEL_LIST) && (DgnModelType::Sheet != dgnModel_getModelType(modelRef)) && (RealDwgUtil::GetDwgModelSpaceId ((DgnFileP)m_fileObj) != modelId) )
        {
        // hidden non-default design models are not indexed in RealDwg::FileHolder.
        return  SUCCESS;
        }

    UInt32 elementCount = modelRef->GetElementCount (sections);
    DependencyManager::ProcessAffected ();

    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
    ConvertFromDgnContext  *context = nullptr;

    if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
        context = new ConvertFromDgnMultiProcessingContext  (modelRef, modelId, m_dwgFileHolder, GetFormat(), settings.GetDwgSaveVersion(), settings.GetDwgSaveUnitMode(), omitRefPaths,
                                    check3dModel->Is3d(), elementCount, settings);
    else
        context = new ConvertFromDgnContext  (modelRef, modelId, m_dwgFileHolder, GetFormat(), settings.GetDwgSaveVersion(), settings.GetDwgSaveUnitMode(), omitRefPaths,
                                    check3dModel->Is3d(), elementCount, settings);

    context->SetOutputFileName (fileName);

    StatusInt   status = DGNMODEL_STATUS_NotFound;

    if (modelId < 0)
        status = context->SaveDictionaryModel ();
    else if (NULL != context->GetModelIndexItem())
        status = context->SaveModelCache ();
    else
        DIAGNOSTIC_PRINTF ("Model with ID=%d in file %ls cannot be saved!\n", modelId, fileName);
    delete context;
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::_OnCreateFile
(
DesignFileHeader*           header,
WCharCP                     fileName,
DgnFileIO*                  donorIO
)
    {
    StatusInt       status = BSIERROR;

    if (IsOpen())
        return  DGNOPEN_STATUS_AlreadyOpen;

    if (donorIO->GetFormat() == DgnFileFormatType::DWG || donorIO->GetFormat() == DgnFileFormatType::DXF)
        {
        RealDwgFileIO*  donorDwgIo = dynamic_cast <RealDwgFileIO*> (donorIO);

        if (NULL == donorDwgIo)
            return status;

        if (NULL == donorDwgIo->GetDwgFileHolder())
            {
            m_dwgFileHolder = new FileHolder ((DgnFileP) donorIO->GetDgnFile(), fileName);
            if (SUCCESS != (status = m_dwgFileHolder->LoadAcadDatabase ()))
                return status;
            }
        else
            {
            // Save donorIO's file holder so it will be used for cache save.  donorIO will be deleted before cache save kicks in.
            m_dwgFileHolder = donorDwgIo->GetDwgFileHolder ();
            donorDwgIo->_SetDwgFileHolder (NULL);
            }
        }
    else
        {
        WString         originalDevice, originalDirectory, newDevice, newDirectory;
        WString         originalFileName = WString (donorIO->GetFileName());

        BeFileName::ParseName (&originalDevice, &originalDirectory, NULL, NULL, originalFileName.GetWCharCP());
        BeFileName::ParseName (&newDevice, &newDirectory, NULL, NULL, fileName);

        // If the input directory is not the same as the master then assume that the references are going to be
        // all in the same directory and therefore the reference path can be omitted.
        bool    omitRefPaths = false;
        if (0 != originalDevice.CompareToI(newDevice) || 0 != originalDirectory.CompareToI(newDirectory))
            omitRefPaths = true;

        if (SUCCESS != (status = CreateAcadDatabaseFromOpenFile (fileName, omitRefPaths)))
            return status;
        }

    m_createNewFile = true;

    // Note... Try to just open the file if it already exists....We're going to rewrite anyway and this way
    // we don't destroy the current contents if a Save As... Fails for some stupid reason.
    if (SUCCESS != (status = m_fileHandle.TryOpen (fileName, DgnFileOpenMode::ReadWrite, false, NULL, NULL)))
        status = m_fileHandle.CreateDgnFile (fileName);

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    KeithBentley    12/00
+---------------+---------------+---------------+---------------+---------------+------*/
static DwgSaveUnitMode      SaveChangesUnitModeFromModelRef (DgnFileP  dgnFile)
    {
    // When we are saving changes during the saveAs process then we need to use the unitsMode derived from the saveAs Settings.
    // Else we are in a DWG and pass DWGSaveUnitMode_StorageUnits to use storage Units (from the DWG file).
    DgnFileFormatType   preSaveAsFormat = dgnFile->GetPreSaveAsFormat();

    if (preSaveAsFormat == DgnFileFormatType::V7 || preSaveAsFormat == DgnFileFormatType::V8)
        return MstnInterfaceHelper::Instance().GetSettings().GetDwgSaveUnitMode();
    else
        return DWGSaveUnitMode_StorageUnits;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    KeithBentley    12/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::SaveNonCacheChangesToDatabase (bool doFullSave)
    {
    // This method is called only when writing a file that we've been working on.
    DgnModelP       defaultModel = NULL;
    if (NULL == (defaultModel = m_fileObj->FindLoadedModelById (m_fileObj->GetDefaultModelId())))
        return BSIERROR;

    bool                    omitReferencePath = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DISALLOWFULLREFPATH");
    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
    ConvertFromDgnContext  *context = nullptr;

    if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
        context = new ConvertFromDgnMultiProcessingContext   (defaultModel, -1, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, SaveChangesUnitModeFromModelRef (m_fileObj), omitReferencePath, defaultModel->Is3d(), 1000, settings);
    else
        context = new ConvertFromDgnContext   (defaultModel, -1, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, SaveChangesUnitModeFromModelRef (m_fileObj), omitReferencePath, defaultModel->Is3d(), 1000, settings);

    StatusInt status = context->SaveNonCacheChanges (doFullSave, (DgnFileP) m_fileObj);
    delete context;
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::SaveModelChangesToDatabase (DgnModelP modelRef, int modelId)
    {
    if (NULL == modelRef || 0 == modelRef->GetChangeCount ())
        return SUCCESS;

    if (NULL == m_dwgFileHolder)
        return SUCCESS;

    StatusInt       status = SUCCESS;
    try
        {
        bool        newModel          = false;
        bool        omitReferencePath = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DISALLOWFULLREFPATH");

        // see if it is a new model.
        if ( (modelId >= 0) && NULL == m_dwgFileHolder->GetModelItemByModelId (modelId))
            {
            if (!ModelSaveSupported (modelRef) || !m_dwgFileHolder->AddModel (modelId, modelRef->GetModelType(), modelRef->GetModelNameCP()))
                {
                modelRef->ClearAllDirtyFlags();
                return SUCCESS;
                }

            newModel = true;
            }

        IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
        ConvertFromDgnContext  *context = nullptr;

        if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
            context = new ConvertFromDgnMultiProcessingContext (modelRef, modelId, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, SaveChangesUnitModeFromModelRef (m_fileObj), omitReferencePath, true, 1000, settings);
        else
            context = new ConvertFromDgnContext (modelRef, modelId, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, SaveChangesUnitModeFromModelRef (m_fileObj), omitReferencePath, true, 1000, settings);

        if (newModel)
            context->ExtractDatabaseVariablesFromDgnHeader (true);

        DgnModel::ElementRefIterator* iterator = new DgnModel::ElementRefIterator();
        iterator->SetModel (modelRef, (-1 == modelId) ? DgnModelSections::Dictionary : DgnModelSections::ControlElements);

        status = context->SaveModelChanges (iterator);

        // should we do this only on SUCCESS?
        modelRef->ClearAllDirtyFlags();
        delete context;
        delete iterator;
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF("Exception caught saving cache changes\n");
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt RealDwgFileIO::_WriteChanges (bool doFullSave, double timestamp, DesignFileHeader* hdr)
    {
    // When this gets called for a file that MicroStation has been editing, we should already have a
    // FileHolder that contains the AutoCAD database. We step through all of our caches, find what we
    // have changed, and update the AutoCAD database to reflect those changes. Then we write the AutoCAD database.
    //
    // This method is also called when we do a "SaveAs" to DWG/DXF from a different file format. In that case,
    // we create the FileHolder (and the AutoCAD database) in the _OnCreateFile method, and we will have no
    // changed elements in our caches.

    // can't do anything without a fileHolder.
    if (NULL == m_dwgFileHolder)
        return BSIERROR;

    // make sure the working database is set.
    AcDbDatabase*   database = m_dwgFileHolder->GetDatabase();

    // can't do anything without a AcDbDatabase.
    if (NULL == database)
        return BSIERROR;

    RealDwgHostApp::Instance().SetWorkingDatabase (database);
    /*-----------------------------------------------------------------------------------
    RealDWG calls newProgressMeter prior to a save or a purge, and then uses the new meter
    for save process.  We need to set the DgnFile at this early stage for our hostApp
    such that both file purge(in post process) and file save(in database write) will have
    a valid DgnFileObj.
    -----------------------------------------------------------------------------------*/
    RealDwgHostApp::Instance().SetDgnFile (m_fileObj);

    // set handseed now to ensure new entities created by DWG save never overlap with the ones in caches.
    UInt64 highestDgnId = (hdr->highestUniqueId + 1);
    UInt64 highestDwgId = RealDwgUtil::CastDBHandle (database->handseed());
    if (highestDwgId < highestDgnId)
        database->setHandseed (AcDbHandle(highestDgnId));

    SaveNonCacheChangesToDatabase (doFullSave);

    /*-----------------------------------------------------------------------------------
    Above process saving non-cache changes calls SaveXRefsAndLayersToDatabase, which may
    in turn load XRefs that are attached to a non-active model, and then unload them at
    the end.  Such loading/unloading XRefs result in working database to be set and reset
    to NULL.  We want to make sure that we still have a valid working database.
    -----------------------------------------------------------------------------------*/
    if (NULL == RealDwgHostApp::Instance().workingDatabase())
        RealDwgHostApp::Instance().SetWorkingDatabase (database);

    DgnModelR   dictionaryModel = m_fileObj->GetDictionaryModel();
    if (dictionaryModel.IsFileImage() && (0 != dictionaryModel.GetChangeCount()))
        SaveModelChangesToDatabase (&dictionaryModel, -1);

    DgnModelP           firstChangedModel = nullptr, firstModel = nullptr;
    ModelIndex const&   modelIndex = m_fileObj->GetModelIndex();

    for (ModelIndex::const_iterator iter = modelIndex.begin(); !iter.IsNull() && iter != modelIndex.end(); ++iter)
        {
        if ((*iter).IsDeleted())
            continue;

        ModelId     modelId = (*iter).GetModelId ();
        DgnModelP   model = m_fileObj->FindLoadedModelById (modelId);
        if (nullptr == model)
            continue;

        if (NULL == firstModel)
            firstModel = model;

        if (model->IsFileImage() && (0 != model->GetChangeCount()))
            {
            if (NULL == firstChangedModel)
                firstChangedModel = model;

            StatusInt   status;
            if (SUCCESS != (status = SaveModelChangesToDatabase (model, modelId)))
                return  status;
            }
        }

    hdr->ClearNeedsCommit ();

    // do the PostSaveToDatabase work.
    if (NULL == firstChangedModel)
        firstChangedModel = firstModel;

    if (NULL != firstChangedModel)
        {
        IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
        ConvertFromDgnContext  *context = nullptr;

        if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
            context = new ConvertFromDgnMultiProcessingContext (firstChangedModel, -1, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, DWGSaveUnitMode_StorageUnits, false, firstChangedModel->Is3d(), 1000, settings);
        else
            context = new ConvertFromDgnContext (firstChangedModel, -1, m_dwgFileHolder, DgnFileFormatType::Invalid, DwgFileVersion_Unknown, DWGSaveUnitMode_StorageUnits, false, firstChangedModel->Is3d(), 1000, settings);
        m_dwgFileHolder->PostSaveToDatabase (*context);
        delete context;
        }
    RealDwgUtil::AssertNoOpenObjects (database);

    // the model index is written simply by adding the model space and paper space's, nothing really to do.
    dgnFileObj_clearModelIndexDirty (m_fileObj);

    // restore the locked layers
    m_dwgFileHolder->RecallLockedLayers ();

    return WriteDatabaseToFile (true);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    MikeSpringer    04/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 InvertThumbnailColors
(
BITMAPINFOHEADER*           imageHeaderP
)
    {
    byte            *clrTableP = (byte *) (imageHeaderP+1);

    // AutoCAD seems to invert black and white.
    for (int iColor = 0; iColor < (int) imageHeaderP->biClrUsed; iColor++, clrTableP += 4)
        {
        if (clrTableP[0] == 0 && clrTableP[1] == 0 && clrTableP[2] == 0)
            clrTableP[0] = clrTableP[1] = clrTableP[2] = 255;
        else if (clrTableP[0] == 255 && clrTableP[1] == 255 && clrTableP[2] == 255)
            clrTableP[0] = clrTableP[1] = clrTableP[2] = 0;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
void            RealDwgFileIO::SaveThumbnail (IThumbnailPropertyValue const& thumbnail)
    {
    /*-----------------------------------------------------------------------------------
    RealDWG expects Windows BITMAPINFO, which is an offsetof the input private struct
    thubmnail.c::Thumbnail::info.
    -----------------------------------------------------------------------------------*/
    static int  s_offsetBITMAPINFO = sizeof(long);

    int         imageBytes = thumbnail.GetImageDataSize ();
    byte const* pImage = thumbnail.GetImageDataCP ();

    if (NULL != m_dwgFileHolder && imageBytes > s_offsetBITMAPINFO)
        {
        pImage     += s_offsetBITMAPINFO;
        imageBytes -= s_offsetBITMAPINFO;

        byte*   imageCopy = (byte *)_alloca (imageBytes);
        memcpy (imageCopy, pImage, imageBytes);

        InvertThumbnailColors ((BITMAPINFOHEADER *)(imageCopy));

        if (SUCCESS != m_dwgFileHolder->SaveThumbnail(imageCopy, imageBytes))
            DIAGNOSTIC_PRINTF ("Error saving thumbnail image\n");
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            RenameFile
(
WStringCR                   oldName,
WStringCR                   newName
)
    {
    ::DeleteFileW (newName.c_str());     // movefile fails if a file already exists with that name

    if (::MoveFileW (oldName.c_str(), newName.c_str()))
        return  SUCCESS;

    return  ::GetLastError();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgFileIO::GetBackupFileName (WStringR backupFileName)
    {
    WString     device, directory, name;

    BeFileName::ParseName (&device, &directory, &name, NULL, GetFileName().GetWCharCP());

    WString     backupDir;
    if ((SUCCESS == ConfigurationManager::GetVariable(backupDir, L"MS_DWG_BACKUP")) && !backupDir.empty() && (0 == _waccess (backupDir.c_str(), 0)))
        BeFileName::ParseName (&device, &directory, NULL, NULL, backupDir.GetWCharCP());

    BeFileName::BuildName (backupFileName, device.GetWCharCP(), directory.GetWCharCP(), name.GetWCharCP(), L"bak");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::WriteDatabaseToFile (bool exportFonts)
    {
    /* -----------------------------------------------------------------------------------------------
        In order to minimize the chance of destroying a valid file while saving, we do the following.

        1)  Save to a temporary file (same name but .tmp extension).
        2)  If the Save is not succesful delete the .tmp file and return error.
        3)  If the Save is Successful:
            a) Rename the current file to backup file (same name ".bak".  This is optional).
            b) Rename the temporary file (with changes) to the original filename.
    -------------------------------------------------------------------------------------------------- */
    if (NULL == m_dwgFileHolder)
        {
        assert (false);
        return BSIERROR;
        }

    // get Progress string
    WString         compactFileName, progressString, formatString;
    BeFileName::BeCompactPath (compactFileName, GetFileName().GetWCharCP(), 45);
    RmgrResource::LoadWString (formatString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_PROGRESS_Writing);
    WString::Sprintf (progressString, formatString.GetWCharCP(), compactFileName.GetWCharCP());

    // get temporary file name.
    WString         tempFileName;
    WString::Sprintf (tempFileName, L"%ls.tmp", GetFileName().c_str());

    StatusInt       status;
    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
    if (SUCCESS == (status = m_dwgFileHolder->WriteFile (tempFileName.c_str(), GetFormat(), settings.GetDxfSavePrecision(), m_createNewFile, progressString.c_str())) && (0 == _waccess (tempFileName.c_str(), 0)) )
        {
        bool        noBackupConfig = ConfigurationManager::IsVariableDefined (L"MS_NO_DWG_BACKUP");

        m_fileHandle.CloseDgnHandle();

        WString     targetFileName = GetFileName();

        if (m_createNewFile)
            {
            /*---------------------------------------------------------------------------
            ACAD/RealDWG requires extension to be .dwg or .dxf.  Multiple extension can
            result in a wrong extension (TR 284445).
            ---------------------------------------------------------------------------*/
            if (SUCCESS == (status = RenameFile(tempFileName, targetFileName)))
                status = m_fileHandle.TryOpen (targetFileName.GetWCharCP(), DgnFileOpenMode::ReadWrite, false, NULL, NULL);
            }
        else
            {
            WString     backupFileName;
            this->GetBackupFileName (backupFileName);
            RenameFile (targetFileName, backupFileName);

            if (SUCCESS == (status = RenameFile (tempFileName, targetFileName)))
                {
                status = m_fileHandle.TryOpen (targetFileName.GetWCharCP(), DgnFileOpenMode::ReadWrite, false, NULL, NULL);
                if (noBackupConfig)
                    ::DeleteFileW (backupFileName.c_str());
                }
            else
                {
                RenameFile (backupFileName, targetFileName);
                }
            }

        if (exportFonts)
            {
            //  At this point,  we can export all the RSC fonts that need to be exported. The following code extracts the location where the new
            //  SHXfonts need to go(from user DWG Save As Options), and then calls SaveRscFonts to export the RSC fonts used in the file to SHX.
            WString     fontPath;
            if (MstnInterfaceHelper::Instance().GetSettings().GetDwgShapeFilePath (fontPath))
                m_dwgFileHolder->ExportRscFonts (fontPath);
            }
        }
    else
        {
        ::DeleteFileW (tempFileName.c_str());
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetCustomSummaryInfo
(
AcDbDatabaseSummaryInfo*    summaryInfo,
const ACHAR*                key,
WStringCR                   newString
)
    {
    ACHAR*      oldString = NULL;

    if (summaryInfo->numCustomInfo() > 0 && Acad::eOk == summaryInfo->getCustomSummaryInfo(key, oldString))
        {
        if (0 == wcscmp(oldString, newString.c_str()))
            return  SUCCESS;

        return Acad::eOk == summaryInfo->setCustomSummaryInfo(key, newString.c_str()) ? SUCCESS : BSIERROR;
        }

    // add a new entry
    return Acad::eOk == summaryInfo->addCustomSummaryInfo(key, newString.c_str()) ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetSummaryStreamProperty
(
AcDbDatabaseSummaryInfo*    summaryInfo,
int                         propertyId,
WStringCR                   stringIn
)
    {
    Acad::ErrorStatus       errorStatus = Acad::eOk;

    switch (propertyId)
        {
        case PIDSI_AUTHOR:
            errorStatus = summaryInfo->setAuthor (stringIn.c_str());        break;
        case PIDSI_TITLE:
            errorStatus = summaryInfo->setTitle (stringIn.c_str());         break;
        case PIDSI_SUBJECT:
            errorStatus = summaryInfo->setSubject (stringIn.c_str());       break;
        case PIDSI_KEYWORDS:
            errorStatus = summaryInfo->setKeywords (stringIn.c_str());      break;
        case PIDSI_COMMENTS:
            errorStatus = summaryInfo->setComments (stringIn.c_str());      break;
        case PIDSI_LASTAUTHOR:
            errorStatus = summaryInfo->setLastSavedBy (stringIn.c_str());   break;

        case PIDSI_EDITTIME:
        case PIDSI_CREATE_DTM:
        case PIDSI_LASTSAVE_DTM:
            /*---------------------------------------------------------------------------
            FUTUREWORK: RealDWG does not allow us to set any date in database.  DwgFiler
            workaround is not desirable in this case because this would mean doing DWGIN
            for entire file!  I was hoping RealDWG save would at least change last saved
            date TDUUPDATE, but it does not.
            ---------------------------------------------------------------------------*/
            return  BSIERROR;

        default:
            return  BSIERROR;
        }

    return  Acad::eOk == errorStatus ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetDocSummaryStreamProperty
(
AcDbDatabaseSummaryInfo*    summaryInfo,
int                         propertyId,
WStringCR                   stringIn
)
    {
    WString     key;

    switch (propertyId)
        {
        case PIDDSI_MANAGER:
            if (SUCCESS == RmgrResource::LoadWString(key, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_FILEPROP_Manager))
                return SetCustomSummaryInfo (summaryInfo, key.c_str(), stringIn);
            break;
        case PIDDSI_CATEGORY:
            if (SUCCESS == RmgrResource::LoadWString(key, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_FILEPROP_Client))
                return SetCustomSummaryInfo (summaryInfo, key.c_str(), stringIn);
            break;
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/14
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt    SetCustomSummaryStreamProperty (AcDbDatabaseSummaryInfo* summaryInfo, WCharCP keyIn, WStringCR stringIn)
    {
    Acad::ErrorStatus   es = Acad::eInvalidInput;

    if (NULL != keyIn && 0 != keyIn[0] && !stringIn.empty())
        {
        // try resetting an existing key
        es = summaryInfo->setCustomSummaryInfo (keyIn, stringIn.c_str());

        // if key not found, add it as a new entry
        if (Acad::eKeyNotFound == es)
            es = summaryInfo->addCustomSummaryInfo (keyIn, stringIn.c_str());
        }

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       RealDwgFileIO::SaveDocumentProperty
(
int*            propID,
WCharCP         name,
int             propType,
int             streamToUse,
const void      *value
)
    {
    if ((NULL != propID && (SUMMARY_STREAM == streamToUse || DOCSUMMARY_STREAM == streamToUse)) ||
        (NULL != name && USERDEFINED_STREAM == streamToUse))
        {
        if (NULL == m_dwgFileHolder)
            return  DGNFILE_ERROR_NotLoaded;

        AcDbDatabaseSummaryInfo*    summaryInfo = NULL;
        if (Acad::eOk != acdbGetSummaryInfo(m_dwgFileHolder->GetDatabase(), summaryInfo))
            return  DGNFILE_ERROR_ReadError;

        StatusInt   status = BSISUCCESS;
        WString     string;

        if (VT_LPWSTR == propType)
            string.assign ((WCharCP)value);
        else if (VT_LPSTR == propType)
            status = BeStringUtilities::CurrentLocaleCharToWChar (string, (CharCP)value);
        else if (VT_BLOB == propType)
            status = RealDwgUtil::BytesToHexString (string, reinterpret_cast<bvector<byte> const*>(value));
        else
            status = DGNFILE_ERROR_BadArg;

        if (m_format == DgnFileFormatType::DXF && string.length () > 255)
            status = DGNFILE_ERROR_BadArg;
            
        if (BSISUCCESS != status)
            {
            delete summaryInfo;
            return  status;
            }

        if (SUMMARY_STREAM == streamToUse)
            status = SetSummaryStreamProperty (summaryInfo, *propID, string);
        else if (DOCSUMMARY_STREAM == streamToUse)
            status = SetDocSummaryStreamProperty (summaryInfo, *propID, string);
        else if (USERDEFINED_STREAM == streamToUse)
            status = SetCustomSummaryStreamProperty (summaryInfo, name, string);
        else
            status = BSIERROR;

        if (SUCCESS == status)
            {
            acdbPutSummaryInfo (summaryInfo);
            m_dwgFileHolder->GetFile()->SetDirty();
            }

        delete summaryInfo;

        return status;
        }

    return  DGNFILE_ERROR_BadArg;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetSummaryStreamProperty
(
void*                           valueOut,
int*                            propertyType,
int                             propertyId,
const AcDbDatabaseSummaryInfo*  summaryInfo
)
    {
    Acad::ErrorStatus   errorStatus = Acad::eOk;
    ACHAR*              string = NULL;

    switch (propertyId)
        {
        case PIDSI_TITLE:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getTitle(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WChar*)valueOut) = 0;
            break;

        case PIDSI_SUBJECT:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getSubject(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WCharP)valueOut) = 0;
            break;

        case PIDSI_AUTHOR:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getAuthor(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WCharP)valueOut) = 0;
            break;

        case PIDSI_KEYWORDS:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getKeywords(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WCharP)valueOut) = 0;
            break;

        case PIDSI_COMMENTS:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getComments(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WCharP)valueOut) = 0;
            break;

        case PIDSI_LASTAUTHOR:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getLastSavedBy(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WCharP)valueOut) = 0;
            break;

        case PIDSI_REVNUMBER:
            *propertyType = VT_LPWSTR;
            if (Acad::eOk == (errorStatus = summaryInfo->getRevisionNumber(string)) && NULL != string)
                wcscpy ((WCharP)valueOut, string);
            else
                *((WChar*)valueOut) = 0;
            break;

        case PIDSI_EDITTIME:
            {
            *propertyType = VT_FILETIME;
            AcDbDate    timeInDwg = summaryInfo->database()->tdindwg ();

            *((Int64 *) valueOut) = RealDwgUtil::FileTimeInt64FromAcDbDate (timeInDwg);
            break;
            }

        case PIDSI_CREATE_DTM:
            {
            /*---------------------------------------------------------------------------
            Use Windows file time stamps for both created & last saved times for these
            reasons:
            1. TDUCREATE & TDUUPDATE can not be saved (see respective part of the save
                code for comments on this).  Autodesk admitted this problem and offered
                no commitment to implement it.
            2. AutoCAD appears to also use Windows time stamps instead of using these
                system variables in FIELDS and File Properties.
            We kept tducreate & tduupdate here in case RealDWG will support and also
            AutoCAD will use them.
            ---------------------------------------------------------------------------*/
            *propertyType = VT_FILETIME;
            const ACHAR*    fileName = NULL;
            if (Acad::eOk != summaryInfo->database()->getFilename(fileName) ||
                SUCCESS != RealDwgUtil::GetFileTimeStamps((FILETIME*)valueOut, NULL, NULL, fileName))
                {
                AcDbDate    timeCreate = summaryInfo->database()->tducreate ();
                RealDwgUtil::FileTimeFromAcDbDate ((FILETIME *)valueOut, timeCreate);
                }
            break;
            }

        case PIDSI_LASTSAVE_DTM:
            {
            *propertyType = VT_FILETIME;
            const ACHAR*    fileName = NULL;
            if (Acad::eOk != summaryInfo->database()->getFilename(fileName) ||
                SUCCESS != RealDwgUtil::GetFileTimeStamps(NULL, NULL, (FILETIME*)valueOut, fileName))
                {
                AcDbDate    timeUpdate = summaryInfo->database()->tduupdate ();
                RealDwgUtil::FileTimeFromAcDbDate ((FILETIME *)valueOut, timeUpdate);
                }
            break;
            }

        default:
            return  BSIERROR;
        }

    if (NULL != string)
        acutDelString (string);

    return  Acad::eOk == errorStatus ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/14
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt    GetCustomSummaryStreamProperty (void* valueOut, int* propertyType, WCharCP key, const AcDbDatabaseSummaryInfo*  summaryInfo)
    {
    if (NULL == key || 0 == key[0])
        return  DGNPLATFORM_STATUS_BadArg;

    *propertyType = VT_LPWSTR;

    ACHAR*          string = NULL;
    StatusInt       status = BSIERROR;

    if (Acad::eOk == summaryInfo->getCustomSummaryInfo(key, string))
        {
        // requested entry exists: allow entry existence query only (null valueOut) or return the requested data.
        status = BSISUCCESS;

        if (0 == _wcsicmp(key, IDgnWorkSetInfo::GetDocumentPropertyKey()))
            {
            // this is the DGN project metadata - convert it from hex back to byte array
            if (nullptr != propertyType)
                *propertyType = VT_BLOB;

            bvector<byte>*  byteArray = reinterpret_cast <bvector<byte> *> (valueOut);

            if (nullptr != byteArray)
                status = RealDwgUtil::HexStringToBytes (byteArray, WString(string));
            }
        else if (nullptr != valueOut)
            {
            // for all other keys, return unicode string as VT_LPWSTR
            wcscpy ((WCharP)valueOut, string);
            }
        acutDelString (string);
        }
    else if (nullptr != valueOut)
        {
        *((WCharP)valueOut) = 0;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetDocSummaryStreamProperty
(
void*                           valueOut,
int*                            propertyType,
int                             propertyId,
const AcDbDatabaseSummaryInfo*  summaryInfo
)
    {
    StatusInt       status = BSIERROR;
    WString         key;

    switch (propertyId)
        {
        case PIDDSI_MANAGER:
            status = RmgrResource::LoadWString(key, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_FILEPROP_Manager);
            break;
        case PIDDSI_CATEGORY:
            status = RmgrResource::LoadWString(key, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_FILEPROP_Client);
            break;
        }

    if (BSISUCCESS == status)
        status = GetCustomSummaryStreamProperty (valueOut, propertyType, key.GetWCharCP(), summaryInfo);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    ReadStringPropertyFrom (void* outValue, int* outPropType, int currentId, int requestedId, bool isUnicode, int handle)
    {
    if (NULL != outPropType)
        *outPropType = VT_LPWSTR;

    // read the size of current property ID
    char        sizeChars[3] = "";
    _read (handle, (void*)sizeChars, 2);

    int         size = 0;
    memcpy (&size, &sizeChars[0], 2);
    if (size < 1 || size >= FILEPROP_MAX_Size)
        {
        _close (handle);
        return  BadData;
        }

    // read the supposedly null terminated content of the property string
    char        buffer[2*FILEPROP_MAX_Size] = "";
    _read (handle, (void*)buffer, isUnicode ? 2*size : size);
    if (currentId == requestedId)
        {
        if (isUnicode)
            BeStringUtilities::Utf16ToWChar ((WCharP)outValue, size, (Utf16CP)buffer);
        else
            BeStringUtilities::CurrentLocaleCharToWChar ((WCharP)outValue, buffer, size);

        if (FILEPROP_ID_Ignore != requestedId)
            _close (handle);

        return  RealDwgSuccess;
        }

    return  NotApplicable;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/14
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    ReadCustomPropertyFrom (void* outValue, int* outPropType, WCharCP requestedKey, bool isUnicode, int handle)
    {
    if (nullptr == requestedKey)
        return  NotApplicable;

    // read number of custom property pairs
    char            sizeChars[3] = "";
    _read (handle, (void*)sizeChars, 2);

    int             numPairs = 0;
    memcpy (&numPairs, &sizeChars[0], 2);
    if (numPairs < 1 || numPairs > FILEPROP_MAX_Size)
        return  NotApplicable;

    WChar           foundKey[1024], foundString[FILEPROP_MAX_Size];
    RealDwgStatus   status = NotApplicable;

    for (int i = 0; i < numPairs; i++)
        {
        // read custom property key
        foundKey[0] = 0;
        status = ReadStringPropertyFrom (&foundKey[0], NULL, FILEPROP_ID_Ignore, FILEPROP_ID_Ignore, isUnicode, handle);
        if (RealDwgSuccess != status)
            return  BadData;

        // read custom property data
        foundString[0] = 0;
        status = ReadStringPropertyFrom (&foundString[0], outPropType, FILEPROP_ID_Ignore, FILEPROP_ID_Ignore, isUnicode, handle);
        if (RealDwgSuccess == status && 0 == _wcsicmp(foundKey, requestedKey))
            {
            // requested entry exists: allow entry existence query only (null outValue) or return the requested data.
            if (0 == _wcsicmp(requestedKey, IDgnWorkSetInfo::GetDocumentPropertyKey()))
                {
                // this is DGN project metadata - convert hex strings back to bytes
                if (nullptr != outPropType)
                    *outPropType = VT_BLOB;

                bvector<byte>*  byteArray = reinterpret_cast <bvector<byte> *> (outValue);

                if (nullptr != byteArray && BSISUCCESS != RealDwgUtil::HexStringToBytes(byteArray, foundString))
                    status = BadData;
                }
            else if (nullptr != outValue)
                {
                // for all other keys, return unicode strings
                wcscpy ((WCharP)outValue, foundString);
                }

            _close (handle);

            return  status;
            }
        }

    // did not find the requested custom proerpty
    return  NotApplicable;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       RealDwgFileIO::ReadDocumentPropertyFromDwg (void* outValue, int* outPropType, int propId, WCharCP key)
    {
    /*------------------------------------------------------------------------------------------------------
    When a DWG xref file is unloaded, the DGN cache still exists and a program can call us to query document
    properties (IModel publishing as an example).  Re-loading DWG database is an overkill for this purpose,
    and this code attempts to directly read the doc property without having to load the file via RealDWG.

    Based on our reverse engineering of DWG files, DocSummary starts at 0x0120 in R2004 and 0x0C80 in R2007.
    In R2013, it appears to be back at 0x0120 again.  But the location of propeties seems to be set at 0x20.
    Each summary property starts by a size of bytes, followed actual property value.  And they are in a fixed
    order:
        Title
        Subject
        Author
        Keywords
        Comments
        Last saved by
        Revision number
        Hyperlink base
        skip 24 bytes for R2004 or 27 bytes for R2007
        number of custom pairs
            number of 1st custom chars
            number of 2nd custom chars
            ...
    ------------------------------------------------------------------------------------------------------*/
    WString     fileName = this->GetFileName ();
    if (fileName.empty())
        return  DGNOPEN_STATUS_BadFilename;

    int         handle = _wsopen (fileName.c_str(), _O_BINARY | _O_RDONLY, _SH_DENYNO);
    if (-1 == handle)
        return  DGNOPEN_STATUS_AccessViolation;

    // Doc properties supported since R2004: it starts doc properties at 0x0120, but still in locale chars
    long        summaryStart = 0x0120;
    char        headerData[8194] = "\0";
    long        headerSize = _read (handle, headerData, sizeof(headerData));
    if (headerSize < summaryStart)
        {
        _close(handle);
        return  DWGOPEN_STATUS_BadFile;
        }

    int         version = RealDwgFileType::AcadVersionFromString (headerData);
    if (version < DwgFileVersion_2004)
        {
        _close(handle);
        return  DGNOPEN_STATUS_VersionMismatch;
        }

    // read the DocProperty starting location at 0x20
    memcpy (&summaryStart, &headerData[32], 4);
    if (headerSize < summaryStart)
        {
        _close(handle);
        return  DWGOPEN_STATUS_BadFile;
        }

    // R2007 uses Unicode
    bool        isUnicode = false;
    if (version >= DwgFileVersion_2007)
        isUnicode = true;

    _lseek (handle, summaryStart, SEEK_SET);

    // read property TITLE
    RealDwgStatus   status = ReadStringPropertyFrom (outValue, outPropType, PIDSI_TITLE, propId, isUnicode, handle);
    if (RealDwgSuccess == status)
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property SUBJECT
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_SUBJECT, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property AUTHOR
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_AUTHOR, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property KEYWORDS
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_KEYWORDS, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property COMMENTS
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_COMMENTS, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property LASTAUTHOR
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_LASTAUTHOR, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // read property REVNUMBER
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, PIDSI_REVNUMBER, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // the last common property is hyperlink base, although not a standard PIDSI property
    if (RealDwgSuccess == (status = ReadStringPropertyFrom(outValue, outPropType, FILEPROP_ID_Ignore, propId, isUnicode, handle)))
        return  BSISUCCESS;
    else if (BadData == status)
        return  DGNFILE_ERROR_ReadError;

    // skip 24 bytes to custom property section:
    if (_lseek(handle, 0x18, SEEK_CUR) > 0L)
        {
        // read custom properties by key
        if (RealDwgSuccess == (status = ReadCustomPropertyFrom(outValue, outPropType, key, isUnicode, handle)))
            return  BSISUCCESS;
        else if (BadData == status)
            return  DGNFILE_ERROR_ReadError;
        // no custom property found
        }

    _close (handle);

    // unsupported property
    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   RealDwgFileIO::GetDocumentProperty
(
int*            propID,
WCharCP         nameP,
int*            propType,
int             streamToUse,
void*           value
)
    {
    if ((NULL != propID && (SUMMARY_STREAM == streamToUse || DOCSUMMARY_STREAM == streamToUse)) ||
        (NULL != nameP && USERDEFINED_STREAM == streamToUse))
        {
        if (NULL == m_dwgFileHolder)
            return  this->ReadDocumentPropertyFromDwg(value, propType, nullptr == propID ? -1 : *propID, nameP);

        AcDbDatabaseSummaryInfo*    summaryInfo = NULL;
        if (Acad::eOk != acdbGetSummaryInfo(m_dwgFileHolder->GetDatabase(), summaryInfo))
            return  DGNFILE_ERROR_ReadError;

        StatusInt   status = BSIERROR;

        if (SUMMARY_STREAM == streamToUse)
            status = GetSummaryStreamProperty (value, propType, *propID, summaryInfo);
        else if (DOCSUMMARY_STREAM == streamToUse)
            status = GetDocSummaryStreamProperty (value, propType, *propID, summaryInfo);
        else if (USERDEFINED_STREAM == streamToUse)
            status = GetCustomSummaryStreamProperty (value, propType, nameP, summaryInfo);

        delete summaryInfo;

        return status;
        }

    return  DGNFILE_ERROR_BadArg;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            RealDwgFileIO::IsOldDwgAecFile ()
    {
    // check if current file contains AEC objects of an old version
    if (NULL != m_dwgFileHolder)
        return m_dwgFileHolder->IsOldVersionAEC ();

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/17
+---------------+---------------+---------------+---------------+---------------+------*/
bool            RealDwgFileIO::_IsReferenceActivationPermitted ()
    {
    // while attching a corrupt DWG file as a read-only reference is allowed, activation for edit is not:
    bool    fileCorrupted = false;
    if (nullptr != m_dwgFileHolder)
        fileCorrupted = m_dwgFileHolder->IsCorruptedDwgFile (m_dwgFileHolder->GetDatabase());
    return  !fileCorrupted;
    }


/*=================================================================================**//**
* RealDwgFileType class
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
DgnFileIOP                  RealDwgFileType::Factory (DgnFileP pFile)
    {
    return new RealDwgFileIO (pFile, DgnFileFormatType::DWG);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDwgFileType::ValidateFile
(
DgnFileFormatType*          pFormat,
int*                        pMajorVersion,
int*                        pMinorVersion,
bool*                       pDefaultModelIs3D,
IThumbnailPropertyValuePtr* ppThumbnail,
WCharCP                     pName
)
    {
    int         handle;
    bool        isValid = false;

    if (-1 != (handle = _wsopen (pName, _O_BINARY | _O_RDONLY, _SH_DENYNO)))
        {
        char        headerData[4096];

        int headerSize = _read (handle, headerData, sizeof(headerData));

        if (isValid = IsDwgFile (pMajorVersion, headerData, headerSize))
            {
            if (NULL != pFormat)
                *pFormat = DgnFileFormatType::DWG;

            if (NULL != pDefaultModelIs3D)
                *pDefaultModelIs3D = true;

            if (NULL != pMinorVersion)
                *pMinorVersion = 0;

            if (NULL != ppThumbnail)
                *ppThumbnail = ReadThumbnail (pName);
            }

        _close (handle);
        }

    return isValid;
    }

struct VersionDefinition
    {
    DwgFileVersion  versionNumber;
    char            string[7];
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
int                         RealDwgFileType::AcadVersionFromString (CharCP pData)
    {
    // When a new version is added here, a new version display string may also be added in init.c::s_versionStrings.
    static  VersionDefinition s_dwgVersions[] =
        {
        {DwgFileVersion_2_5,  "AC1002"},
        {DwgFileVersion_2_6,  "AC1003"},
        {DwgFileVersion_9,    "AC1004"},
        {DwgFileVersion_9,    "AC1005"},
        {DwgFileVersion_10,   "AC1006"},
        {DwgFileVersion_10,   "AC1007"},
        {DwgFileVersion_10,   "AC1008"},
        {DwgFileVersion_11,   "AC1009"},
        {DwgFileVersion_11,   "AC1010"},
        {DwgFileVersion_11,   "AC1011"},
        {DwgFileVersion_13,   "AC1012"},
        {DwgFileVersion_14,   "AC1013"},
        {DwgFileVersion_14,   "AC1014"},
        {DwgFileVersion_2000, "AC1015"},
        {DwgFileVersion_2004, "AC1018"},
        {DwgFileVersion_2007, "AC1021"},
        {DwgFileVersion_2010, "AC1024"},
        {DwgFileVersion_2013, "AC1027"},
        {DwgFileVersion_2018, "AC1032"},
        };


    int     iVersion = 0;
    for (; iVersion < _countof (s_dwgVersions); iVersion++)
        {
        if (0 == memcmp (pData, s_dwgVersions[iVersion].string, 6))
            return s_dwgVersions[iVersion].versionNumber;
        }

    // RealDWG may be able to open versions we do not have in our list.  Just check for "AC1" or "AC2":
    if ('A' == pData[0] && 'C' == pData[1] && ('1' == pData[2] || '2' == pData[2]))
        {
        // looks like a valid DWG, but is it a newer version?
        char    verBuffer[4];
        strncpy (verBuffer, &pData[3], 3);
        verBuffer[3] = 0;

        char*   end = NULL;
        int     verFromData = strtol (verBuffer, &end, 10);
        int     verMax = strtol (&s_dwgVersions[iVersion-1].string[3], &end, 10);

        /*-------------------------------------------------------------------------------
        For a newer version, we'd rather return "VersionInvalid" so the caller will pop up
        the alert dialog box about the unsupported version.  For an older version not listed
        above, but may still open by RealDWG, we'd return "VersionUnknown".
        -------------------------------------------------------------------------------*/
        return verFromData > verMax ? DwgFileVersion_Invalid : DwgFileVersion_Unknown;
        }

    return DwgFileVersion_Invalid;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDwgFileType::IsDwgFile (int* pVersion, CharCP pData, int dataSize)
    {
    int         version;

    if (NULL == pVersion)
        pVersion = &version;

    return (DwgFileVersion_Invalid < (*pVersion = AcadVersionFromString (pData)));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgFileType::GetCapabilities (DgnFileCapabilities*  cap)
    {
    cap->canAutoSave      = false;
    cap->canSaveThumbnail = true;
    }


#if defined (EXTRACT_THUMBNAIL_INDEPENDENTLY_TO_REALDWG)
#include    <sys/stat.h>
#include    <Mstn\MdlApi\msimage.fdf>
#include    <MsjInternal\Ustn\miimage.fdf>
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/12
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt    ConvertPngToBmp (byte*& thumbnailBytes, UInt32& thumbnailSize, byte const* pngBytes, UInt32 pngSize)
    {
    /*-----------------------------------------------------------------------------------
    This is a piece of code which is not in use but worked. If we need to activate this
    code for indenpendent thumbnail extraction, we should try using PNG stream conversion
    instead of swapping it out to a temp file.
    -----------------------------------------------------------------------------------*/
    WChar   tempPngFile[1024] = { 0 };
    if (::GetTempPath(1024, tempPngFile) <= 0)
        return  BSIERROR;

    wcscat (tempPngFile, L"$dwg$thumbnail$.png");

    int     fileHandle = _wopen (tempPngFile, _O_RDWR | _O_CREAT | _O_BINARY, _S_IWRITE);
    if (fileHandle < 0)
        return  BSIERROR;

    _write (fileHandle, pngBytes, pngSize);
    _close (fileHandle);

    // read the PNG file as RGB, then convert it to BMP:
    Point2d     rgbSize;
    byte*       rgbBuffer = NULL;
    StatusInt   status = mdlImage_readFileToRGB (&rgbBuffer, &rgbSize, tempPngFile, IMAGEFILE_PNG, NULL);
    if (BSISUCCESS != status)
        return  status;

    byte    red[256] = { 0 }, green[256] = { 0 }, blue[256] = { 0 };
    int     nColors = 0;

    status = mdlImage_getOptimizedPalette (red, green, blue, &nColors, 256, rgbBuffer, &rgbSize);

    UInt32  totalBytes = rgbSize.x * rgbSize.y;
    if (BSISUCCESS == status && totalBytes > 0)
        {
        BITMAPINFOHEADER    bmpInfoHeader;
        int                 headerSize = sizeof (BITMAPINFOHEADER);

        totalBytes = headerSize + 256 * sizeof(RGBQUAD) + 2 * totalBytes  + 4;
        thumbnailBytes = (byte *) calloc (1, totalBytes);
        if (NULL == thumbnailBytes)
            {
            IMAGELIB_FREE (rgbBuffer);
            return  MDLERR_INSFMEMORY;
            }

        byte*       pBmp = thumbnailBytes;

        // start the image buffer with the CF_DIB signature
        *(UInt32 *) pBmp = CF_DIB;
        pBmp += 4;

        byte*       pImageDataStart = pBmp;

        // build the BMP info header
        memset (&bmpInfoHeader, 0, headerSize);
        bmpInfoHeader.biSize = headerSize;
        bmpInfoHeader.biWidth = rgbSize.x;
        bmpInfoHeader.biHeight = rgbSize.y;
        bmpInfoHeader.biPlanes = 1;
        bmpInfoHeader.biBitCount = 8;
        bmpInfoHeader.biClrUsed = 256;
        bmpInfoHeader.biSizeImage = rgbSize.x * rgbSize.y;
        bmpInfoHeader.biXPelsPerMeter = 1;
        bmpInfoHeader.biYPelsPerMeter = 1;

        memcpy (pBmp, &bmpInfoHeader, headerSize);
        pBmp += headerSize;

        // build the RGBA quads
        for (int i = 0; i < 256; i++)
            {
            if (i < nColors)
                {
                *pBmp = blue[i];
                pBmp++;
                *pBmp = green[i];
                pBmp++;
                *pBmp = red[i];
                pBmp++;
                *pBmp = 0x0;
                pBmp++;
                }
            else
                {
                pBmp += sizeof (RGBQUAD);
                }
            }

        bmpInfoHeader.biSizeImage = (DWORD) (pBmp - pImageDataStart);

        // dither rows
        UInt32      rowSize = (rgbSize.x + 3) & ~0x0003;
        if (rowSize > 0)
            {
            byte*   scanline = (byte*) malloc (3 * rgbSize.x);
            if (NULL != scanline)
                {
                mdlImage_ditherInitialize (nColors, red, green, blue, rgbSize.x);

                byte*   pRed = rgbBuffer + 3 * rgbSize.x * (rgbSize.y - 1);
                for (int row = 0; row < rgbSize.y; row++, pRed -= (3 * rgbSize.x))
                    {
                    byte*   pGreen = pRed + rgbSize.x;
                    byte*   pBlue = pGreen + rgbSize.x;

                    mdlImage_ditherRowWithOffset (scanline, pRed, pGreen, pBlue, rgbSize.x, 1);

                    memcpy (pBmp, scanline, rowSize);

                    pBmp += rowSize;
                    bmpInfoHeader.biSizeImage += rowSize;

                    if (bmpInfoHeader.biSizeImage >= totalBytes)
                        {
                        bmpInfoHeader.biSizeImage = totalBytes;
                        break;
                        }
                    }

                // reset BMP image size
                memcpy (pImageDataStart, &bmpInfoHeader, headerSize);

                mdlImage_ditherCleanup ();
                free (scanline);
                }
            }

        thumbnailSize = (UInt32) (pBmp - pImageDataStart);
        }

    IMAGELIB_FREE (rgbBuffer);

    return  status;
    }

/* This is not right.  What we are seeing is that in files that are signed (DWG 2004) the
    thumbnail is stored differently than in files that are not signed.  Until we can figure
    out how this signature affects the thumbnails, we need to keep ourselves from allocating
    memory for garbage and potentially hanging or crashing the system.  MWS 7/15/03 */
#define MAX_THUMBNAIL_SIZE 570000
#define MAX_IMAGES_PRESENT 50
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    MichaelSpringer 03/01
+---------------+---------------+---------------+---------------+---------------+------*/
IThumbnailPropertyValuePtr RealDwgFileType::ReadThumbnail (int handle)
    {
    /*-----------------------------------------------------------------------------------
    We now extract DWG thumbnail using acdbGetPreviewBitmapFromDwg, but I'm keeping this
    code in case in the future we may need to read the thumbnail dependent to RealDWG.
    -----------------------------------------------------------------------------------*/
    char    buf[30];
    long    picPos, overallSize;

    // Check the version of the file to make sure it supports thumbnails
    _lseek (handle, 0, SEEK_SET);
    _read (handle, buf, 13);

    if (AcadVersionFromString (buf) < DwgFileVersion_13)
        {
        return NULL;
        }

    byte    imagesPresent;
    byte    imageType;
    long    imageStartAddr = 0, imageSize = 0;
    // Get the position in the file of the image(s)
    _read (handle, &picPos, 4);
    _lseek (handle, picPos + 16L, SEEK_SET);

    // Get the overall size of the image(s)
    _read (handle, &overallSize, 4);
    _read (handle, &imagesPresent, 1);

    if ((overallSize > MAX_THUMBNAIL_SIZE) || (imagesPresent > MAX_IMAGES_PRESENT))
        return NULL;

    for (int iImage = 0; iImage < imagesPresent; iImage++)
        {
        _read (handle, &imageType, 1);
        if (imageType == 2 || imageType == 6)
            {
            _read (handle, &imageStartAddr, 4);
            _read (handle, &imageSize, 4);
            }
        else
            {
            _lseek (handle, 8, SEEK_CUR);
            }
        }

    if (0 == imageStartAddr)
        return NULL;

    _lseek (handle, imageStartAddr, SEEK_SET);

    UInt32  thumbnailSize = 0;
    byte*   thumbnailBytes = NULL;
    if (imageType == 6)
        {
        // PNG since R2013
        byte*   pngBytes = (byte *) malloc (imageSize);

        _read (handle, pngBytes, imageSize);

        StatusInt   status = ConvertPngToBmp (thumbnailBytes, thumbnailSize, pngBytes, imageSize);

        free (pngBytes);

        if (BSISUCCESS != status)
            return  NULL;
        }
    else
        {
        // BITMAP since R14
        thumbnailSize = 4 + imageSize;
        thumbnailBytes = (byte *) malloc (thumbnailSize);

        if (NULL == thumbnailBytes)
            return NULL;

        *(UInt32*)thumbnailBytes = CF_DIB;

        _read (handle, thumbnailBytes+4, imageSize);
        }

    InvertThumbnailColors ((BITMAPINFOHEADER *)(thumbnailBytes+4));

    IThumbnailPropertyValuePtr thumbnail = ThumbnailPropertyValue::Create (thumbnailSize, thumbnailBytes);

    free (thumbnailBytes);

    return thumbnail;
    }
#endif  // EXTRACT_THUMBNAIL_INDEPENDENTLY_TO_REALDWG


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/12
+---------------+---------------+---------------+---------------+---------------+------*/
IThumbnailPropertyValuePtr RealDwgFileType::ReadThumbnail (WCharCP fileName)
    {
    // acdbGetPreviewBitmapFromDwg depends on hostApp being initialized:
    if (!s_hostAppInitialized && Acad::eOk == acdbSetHostApplicationServices(&RealDwgHostApp::Instance()))
        s_hostAppInitialized = true;
    if (!s_hostAppInitialized)
        return  NULL;

    HBITMAP     hBitmap = NULL;
    HPALETTE    hPalette = NULL;
    if (!acdbGetPreviewBitmapFromDwg(fileName, &hBitmap, &hPalette))
        return  NULL;

    BITMAP      bitMap;
    if (NULL == ::GetObject(hBitmap, sizeof(BITMAP), &bitMap) || 0 == bitMap.bmWidth || 0 == bitMap.bmHeight || NULL == bitMap.bmBits)
        return  NULL;

    // setup the BMP info header
    BITMAPINFOHEADER    bmpInfoHeader;
    size_t              headerSize = sizeof (BITMAPINFOHEADER);
    size_t              rgbaSize = sizeof (RGBQUAD);

    memset (&bmpInfoHeader, 0, headerSize);
    bmpInfoHeader.biSize = (DWORD)headerSize;
    bmpInfoHeader.biWidth = bitMap.bmWidth;
    bmpInfoHeader.biHeight = bitMap.bmHeight;
    bmpInfoHeader.biPlanes = bitMap.bmPlanes;
    bmpInfoHeader.biBitCount = bitMap.bmBitsPixel;
    bmpInfoHeader.biClrUsed = 1 << bitMap.bmBitsPixel;
    bmpInfoHeader.biSizeImage = ((bitMap.bmWidth * bitMap.bmBitsPixel + 31) / 32) * 4 * bitMap.bmHeight;
    bmpInfoHeader.biXPelsPerMeter = 1;
    bmpInfoHeader.biYPelsPerMeter = 1;

    size_t          thumbnailSize = headerSize + bmpInfoHeader.biClrUsed * rgbaSize + bmpInfoHeader.biSizeImage + 4;
    byte*           thumbnailBuffer = new byte[thumbnailSize];
    byte*           pBmp = thumbnailBuffer;

    // start the thumbnail buffer with the CF_DIB signature
    *(UInt32 *) pBmp = CF_DIB;
    pBmp += 4;

    // add the BMP info header
    memcpy (pBmp, &bmpInfoHeader, headerSize);
    pBmp += headerSize;

    // add RGBA quads of the color table:
    PALETTEENTRY*   paletteEntries = new PALETTEENTRY[bmpInfoHeader.biClrUsed];
    size_t          numEntries = ::GetPaletteEntries (hPalette, 0, bmpInfoHeader.biClrUsed, paletteEntries);
    RGBQUAD         rgba;
    for (size_t i = 0; i < bmpInfoHeader.biClrUsed; i++)
        {
        if (i < numEntries)
            {
            rgba.rgbBlue = paletteEntries[i].peBlue;
            rgba.rgbGreen = paletteEntries[i].peGreen;
            rgba.rgbRed = paletteEntries[i].peRed;
            rgba.rgbReserved = 0;
            }
        else
            {
            memset (&rgba, 0, rgbaSize);
            }
        memcpy (pBmp, &rgba, rgbaSize);
        pBmp += rgbaSize;
        }

    // append BMP image bits:
    memcpy (pBmp, bitMap.bmBits, bmpInfoHeader.biSizeImage);

    IThumbnailPropertyValuePtr thumbnail = ThumbnailPropertyValue::Create ((UInt32)thumbnailSize, thumbnailBuffer);

    delete paletteEntries;
    delete thumbnailBuffer;

    return  thumbnail;
    }


/*=================================================================================**//**
* RealDxfFileType class
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
DgnFileIOP                  RealDxfFileType::Factory (DgnFileP pFile)
    {
    return new RealDwgFileIO (pFile, DgnFileFormatType::DXF);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Public void                 RealDxfFileType::GetCapabilities (DgnFileCapabilities* cap)
    {
    cap->canAutoSave      = false;
    cap->canSaveThumbnail = true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDxfFileType::IsDxfFile (int* pVersion, CharCP pData, int dataSize)
    {
    if (dataSize < 50)
        return  false;

    /*-----------------------------------------------------------------------------------
    A fully qualified DXF file starts with
        0
    SECTION
        2
    HEADER
        9
    $ACADVER
        1
    AC10XX
        ...
    where AC10XX is the same string format as in DWG.  We check for "$ACADVER" followed
    by "AC10".  We do not want to read any file that happens to contain the string "AC10".
    -----------------------------------------------------------------------------------*/
    CharCP      asciiString;
    if (NULL != (asciiString = strstr(pData, "$ACADVER")) && NULL != (asciiString = strstr(asciiString, "AC10")))
        {
        if (NULL != pVersion)
            *pVersion = RealDwgFileType::AcadVersionFromString (asciiString);
        return  true;
        }

    // We want to open a DXF file with no or an incomplete header section, ideally also check for ENTITIES section.
    // Unfortunately some crapy DXF files may have large data prior reaching to ENTITIES section, and there is no point to open entire file. TFS#346565.
    if (nullptr != (asciiString = strstr(pData, "SECTION")))
        {
        // since this is a headerless DXF file, treat it as the lowest DXF version we support.
        if (NULL != pVersion)
            *pVersion = 0;
        return true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDxfFileType::IsBinaryDxfFile (int* pVersion, CharCP pData, int dataSize)
    {
    if (0 == strncmp (pData, "AutoCAD Binary DXF", 18))
        {
        if (NULL != pVersion)
            {
            CharCP        versionAt = &pData[54];
            *pVersion = RealDwgFileType::AcadVersionFromString (versionAt);
            }
        return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDxfFileType::ValidateFile
(
DgnFileFormatType*          pFormat,
int*                        pMajorVersion,
int*                        pMinorVersion,
bool*                       pDefaultModelIs3D,
IThumbnailPropertyValuePtr* ppThumbnail,
WCharCP                     pName
)
    {
    int         handle;
    bool        isValid = false;
    WString     extension;

    // The half-witted DXF format doesn't include a header string so it is very difficult to
    // tell if a file actually is a DXF - add Extension test (04/2005) to avoid mistaking
    // files for DXF unless they have extension - RBB.

    BeFileName::ParseName (NULL, NULL, NULL, &extension, pName);
    if (0 != _wcsicmp (extension.c_str(), L"dxf") && 0 != _wcsicmp (extension.c_str(), L"dxb"))
        return false;

    if (-1 == (handle = _wsopen (pName, _O_BINARY | _O_RDONLY, _SH_DENYNO)))
        return false;

    char       headerData[4097];

    // verify that this is really a DXF file. Read the first 4096 bytes
    int     headerSize = _read (handle, headerData, sizeof(headerData)-1);

    // make sure null terminated.
    headerData[headerSize] = 0;

    if (isValid = (IsDxfFile (pMajorVersion, headerData, headerSize) || IsBinaryDxfFile (pMajorVersion, headerData, headerSize)))
        {
        if (NULL != pFormat)
            *pFormat = DgnFileFormatType::DXF;

        if (NULL != pDefaultModelIs3D)
            *pDefaultModelIs3D = true;

        if (NULL != pMinorVersion)
            *pMinorVersion = 0;

        if (NULL != ppThumbnail)
#if defined (EXTRACT_THUMBNAIL_INDEPENDENTLY_TO_REALDWG)
            *ppThumbnail = RealDwgFileType::ReadThumbnail (handle);
#else
            *ppThumbnail = RealDwgFileType::ReadThumbnail (pName);
#endif
        }

    _close (handle);

    return isValid;
    }


/*=================================================================================**//**
* RealDwgProgressMeter
* @bsiclass                                                     RayBentley      01/2003
+===============+===============+===============+===============+===============+======*/
class RealDwgProgressMeter : public AcDbHostApplicationProgressMeter
    {
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                setLimit (int max) override
    {
    // Our global progress meter is currently indeterminate, so not bothering to track determinate progress data.
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                start (const ACHAR *displayString = NULL) override
    {
    // The operation owning us in charge of creating the progress meter, and it may or may not actually display in its own right (based on a timeout), so don't do anything explicitly here.
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                stop () override
    {
    // The operation owning us in charge of creating the progress meter, and it may or may not actually display in its own right (based on a timeout), so don't do anything explicitly here.
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                meterProgress () override
    {
    IDgnProgressMeterP progressMeter = DgnPlatformLib::GetHost ().GetProgressMeter ();
    if (progressMeter)
        progressMeter->UpdateTaskProgress ();
    }

};  // RealDwgProgressMeter


typedef std::pair <Bentley::WString, Bentley::WString>  T_StringPair;
typedef std::pair <AcDbDatabase*, COleDocument*>        T_DatabaseDocumentPair;


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgHostApp::RealDwgHostApp()
    {
    m_file                      = NULL;
    m_doNothingProgressMeter    = new AcDbHostApplicationProgressMeter();
    m_isRunningOn64BitOS        = false;
    m_oleClientItem             = NULL;
    m_messageList               = NULL;
    BOOL        isWow64Process;
    if (0 != IsWow64Process(GetCurrentProcess(),&isWow64Process) && isWow64Process)
        m_isRunningOn64BitOS    = true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgHostApp::~RealDwgHostApp()
    {
    m_localToUrl.clear();
    m_databaseToOleDocument.clear();
    m_displayString.clear();
    m_messageList = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::findFile (WCHAR* pcFullPathOut, int nBufferLength, const WCHAR* pcFilename, AcDbDatabase* pDb, AcDbHostApplicationServices::FindFileHint hint)
    {
    DwgPlatformHost::AcadFileType   findHint = (DwgPlatformHost::AcadFileType) hint;

    return RealDwgSuccess == DwgPlatformHost::Instance()._FindFile(pcFullPathOut, nBufferLength, pcFilename, pDb, findHint) ? Acad::eOk : Acad::eFileNotFound;
    }


// These two functions return the full path to the root folder where roamable/local
// customizable files were installed. Note that the user may have reconfigured
// the location of some the customizable files using the Options Dialog
// therefore these functions should not be used to locate customizable files.
// To locate customizable files either use the findFile function or the
// appropriate system variable for the given file type.
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::getRoamableRootFolder (const WCHAR*& folder)
    {
    Acad::ErrorStatus ret = Acad::eOk;
    static WCHAR buf[MAX_PATH] = L"\0"; //MDI SAFE
    if (buf[0]==0)
        {
        if (GetModuleFileName (NULL, buf, MAX_PATH) != 0)
            ret = Acad::eRegistryAccessError;
        }
    folder = buf;
    return ret;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::getLocalRootFolder (const WCHAR*& folder)
    {
    Acad::ErrorStatus ret = Acad::eOk;
    static WCHAR buf[MAX_PATH] = L"\0"; //MDI SAFE
    if (buf[0]==0)
        {
        if (GetModuleFileName(NULL, buf, MAX_PATH) != 0)
            ret = Acad::eRegistryAccessError;
        }
    folder = buf;
    return ret;
    }

//URL related services
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Boolean              RealDwgHostApp::isURL (const WCHAR* pszURL) const
    {
    return TRUE == PathIsURL (pszURL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Boolean              RealDwgHostApp::isRemoteFile (const ACHAR* pszLocalFile, ACHAR* pszURL, size_t urlLen) const
    {
    T_StringMap::const_iterator value;
    if (m_localToUrl.end() != (value = m_localToUrl.find (pszLocalFile)))
        {
        if (urlLen > 0)
            wcsncpy (pszURL, value->second.GetWCharCP(), urlLen);
        return true;
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::getRemoteFile (const ACHAR* pszURL, ACHAR* pszLocalFile, size_t localLen, Adesk::Boolean bIgnoreCache) const
    {
    DWORD err = ERROR_FILE_NOT_FOUND;
    if (!bIgnoreCache)
        {
        DWORD size = 0;
        if (GetUrlCacheEntryInfo (pszURL,NULL,&size))
            return Acad::eInetFileGenericError; //this shouldn't succeed
        err = GetLastError();
        if (err == ERROR_INSUFFICIENT_BUFFER)
            {
            INTERNET_CACHE_ENTRY_INFO* pCacheEntry = (INTERNET_CACHE_ENTRY_INFO*)malloc(size);
            if (GetUrlCacheEntryInfo(pszURL,pCacheEntry,&size))
                {
                size_t      size2copy = min(size, localLen);
                if (size2copy > 0)
                    {
                    wcsncpy (pszLocalFile, pCacheEntry->lpszLocalFileName, size2copy);
                    m_localToUrl.insert (T_StringPair (pszLocalFile, pszURL));
                    }
                free(pCacheEntry);
                return Acad::eInetOk;
                }
            err = GetLastError();
            }
        }
    if (err == ERROR_FILE_NOT_FOUND)
        {
        if (SUCCEEDED (URLDownloadToCacheFile(NULL,pszURL,pszLocalFile,(DWORD)localLen,0,NULL)))
            {
            m_localToUrl.insert (T_StringPair (pszLocalFile, pszURL));
            return Acad::eInetOk;
            }
        }
    return Acad::eInetFileGenericError;
    }

#if defined (DEBUG_OLE)
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ShowOleClientItems (COleDocument* document, CharP comment, COleClientItem* returned)
    {
    printf ("%hs 0x%08x\n", comment, document);

    POSITION        position;
    COleClientItem  *clientItem;
    for (position = document->GetStartPosition(), clientItem = document->GetNextClientItem (position); NULL != clientItem; clientItem = document->GetNextClientItem (position))
        {
        CString strType;
        if (clientItem->GetItemState() != COleClientItem::emptyState)
            {
            clientItem->GetUserType (USERCLASSTYPE_FULL, strType);
            printf ("ClientItem %ls\n", (LPCTSTR)strType);
            }
        }

    if (NULL != returned)
        printf ("returning 0x%08x\n");
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::getNewOleClientItem (COleClientItem*& pItem)
    {
    if (NULL == m_oleClientItem)
        {
        COleDocument*   document    = RealDwgHostApp::Instance().GetWorkingOleDocument();

        if (NULL == document)
            {
            BeAssert (false && L"Cannot get COleDocument!");
            return Acad::eOk;
            }

        pItem = new COleClientItem (document);

#if defined (DEBUG_OLE)
        ShowOleClientItems (document, "In getNewOleClientItem document", pItem);
#endif

        return Acad::eOk;
        // return __super::getNewOleClientItem (pItem);
        }

    // return the one we created (in the FromElement method of the AcDbOle2Frame DGNExtension) and set using SetOleClientItem below.
    pItem = m_oleClientItem;
    m_oleClientItem = NULL;
    return Acad::eOk;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SetWorkingDatabase (AcDbDatabase* database)
    {
#if defined (DEBUG_WORKINGDATABASE)
    // this is here just so we can set a breakpoint on it.
    const ACHAR* fileName;
    if (NULL == database)
        printf ("Setting Working database to NULL\n");
    else if (Acad::eOk == database->getFilename (fileName))
        printf ("Setting Working database to 0x%08x, %ls\n", database, fileName);
    else
        printf ("Setting Working database is 0x%08x\n", database);
#endif

    setWorkingDatabase (database);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SetOleClientItem (COleClientItem* pItem)
    {
    m_oleClientItem = pItem;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
COleDocument*               RealDwgHostApp::GetWorkingOleDocument ()
    {
    AcDbDatabase* currentDatabase;

    if (NULL == (currentDatabase = workingDatabase()))
        return NULL;

#if defined (DEBUG_OLE)
    const ACHAR* fileName;
    if (Acad::eOk == currentDatabase->getFilename (fileName))
        printf ("Working database is 0x%08x, %ls\n", currentDatabase, fileName);
    else
        printf ("Working database is 0x%08x\n", currentDatabase);
#endif

    T_OleDocumentMap::const_iterator    value;
    COleDocument*                       document;
    if (m_databaseToOleDocument.end() != (value = m_databaseToOleDocument.find (currentDatabase)))
        {
        document = value->second;
        }
    else
        {
        document = new COleDocument();
        m_databaseToOleDocument.insert (T_DatabaseDocumentPair (currentDatabase, document));
        }

    return document;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::DatabaseDeleted (AcDbDatabase *database)
    {
    T_OleDocumentMap::iterator    value;
    if (m_databaseToOleDocument.end() != (value = m_databaseToOleDocument.find (database)))
        {
        COleDocument* document = value->second;
        m_databaseToOleDocument.erase (value);
#if defined (DEBUG_OLE)
        ShowOleClientItems (document, "Before Deleting document", NULL);
#endif
        delete document;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   04/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           RealDwgHostApp::serializeOleItem(COleClientItem* pItem, CArchive* archive)
    {
    // this is called when there's a newly created AcDbOle2Frame object getting saved to the AcDbDatabase.
    try
        {
        // Attempting the initialization on a failed OLE client item may cause an access violation(like ERROR_OPEN_FAILED from TR 299727).
        if (S_OK != pItem->GetLastStatus())
            return  Acad::eInetUnknownError;

        pItem->Serialize (*archive);
        }
    catch (COleException* e)
        {
        DIAGNOSTIC_PRINTF ("COleException is thrown from COleClientItem::Serialization(). SCODE = %x\n", e->m_sc);
        e->Delete ();
        return  Acad::eInetGenericException;
        }
    catch (CArchiveException* e)
        {
        DIAGNOSTIC_PRINTF ("CArchiveException is thrown from COleClientItem::Serialization(). SCODE = %x, File = <%ls>\n", e->m_cause, e->m_strFileName);
        e->Delete ();

        return  Acad::eInetGenericException;
        }

    return Acad::eOk;
    }

// make sure you implement getAlternateFontName. In case your findFile implementation
// fails to find a font you should return a font name here that is guaranteed to exist.
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
const WCHAR *               RealDwgHostApp::getAlternateFontName() const
    {
    return  DwgPlatformHost::Instance()._GetAlternateFontName();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbProgressMeterP          RealDwgHostApp::newProgressMeter()
    {
    // I thought this would get called when we loaded a dwg file, but that does not appear to be the case. Instead they use the progress meter you set with setWorkingProgressMeter.
    return new RealDwgProgressMeter ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbProgressMeterP          RealDwgHostApp::NewProgressMeter (DgnFileP dgnFile, double startFraction, double endFraction)
    {
    return new RealDwgProgressMeter ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SetProgressMeterLimits (AcDbProgressMeterP progressMeter, double lowFraction, double highFraction, int approxNumObjects)
    {
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SetDoNothingProgressMeter ()
    {
    setWorkingProgressMeter (m_doNothingProgressMeter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::displayChar (ACHAR c) const
    {
    if (CHAR_LineFeed == c || 0 == c)
        {
        // end of a full message collection - display what we have collected:
        if (!m_displayString.empty())
            this->DisplayAndFlushMessage ();
        }
    else
        {
        // collect characters:
        m_displayString += (WChar)c;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::DisplayAndFlushMessage (const ACHAR* messageIn) const
    {
    WString     stringToUse;
    if (NULL != messageIn)
        {
        // Display incoming string
        stringToUse.assign ((WCharCP)messageIn);

        // AEC OE's seem to append LineFeed & CR chars in their messages(TR 297306), remove these:
        for (Bentley::WString::iterator rIter = stringToUse.end() - 1; rIter != stringToUse.begin(); rIter--)
            {
            if (CHAR_LineFeed == *rIter || 0x0D == *rIter)
                stringToUse.erase (rIter);
            else
                break;
            }
        }
    else
        {
        // Display collected string
        stringToUse = m_displayString;
        }

    // display message to Message Center, or save it to message list:
    if (NULL == m_messageList)
        NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Info, stringToUse.GetWCharCP(), NULL, 0, OutputMessageAlert::None));
    else
        m_messageList->push_back (stringToUse.GetWCharCP());

    // Flush collected string, if used:
    if (NULL == messageIn)
        m_displayString.clear ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SaveOrSendStringToMessageCenter
(
const ACHAR*                stringIn,
size_t                      lengthIn
) const
    {
    if (NULL == stringIn || 0 == *stringIn || lengthIn <= 0)
        return;

    Bentley::WString        newString ((WCharCP)stringIn, lengthIn);
    size_t                  lineFeedAt = Bentley::WString::npos;

    /*-----------------------------------------------------------------------------------
    Find each and every LINEFEED in the input string, truncate and combine it with
    previously saved strings, then display them.  Leave the ending LINEFEED as the default
    process outside of the loop.
    -----------------------------------------------------------------------------------*/
    while (Bentley::WString::npos != (lineFeedAt = newString.find(CHAR_LineFeed)) && lineFeedAt < (lengthIn-1))
        {
        Bentley::WString    endingString;

        if (lineFeedAt > 0)
            {
            endingString = newString.substr (0, lineFeedAt);
            m_displayString += endingString.substr (0, lineFeedAt);
            }

        if (!m_displayString.empty())
            this->DisplayAndFlushMessage ();

        lineFeedAt++;

        lengthIn -= lineFeedAt;

        newString = newString.substr (lineFeedAt, lengthIn);
        }

    m_displayString += newString.substr (0, lengthIn);

    if (CHAR_LineFeed == newString.at(lengthIn-1) || 0 == newString.at(lengthIn-1))
        {
        // end of a full message collection - display what we have collected:
        this->DisplayAndFlushMessage ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsStringAcceptable (const ACHAR* string, int length)
    {
    if (nullptr == string || 0 == *string)
        return  false;

    // skip a string containing only white spaces:
    size_t      nChars = wcslen (string);
    for (int i = 0; i < nChars; i++)
        {
        if (!iswspace(string[i]))
            return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::displayString (const ACHAR* string, int length) const
    {
    if (length < (int)wcslen(string) || !m_displayString.empty())
        {
        // collect pieces of message strings sent from RealDWG:
        this->SaveOrSendStringToMessageCenter (string, length);
        }
    else if (IsStringAcceptable(string, length))
        {
        // display the full message:
        this->DisplayAndFlushMessage (string);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
const ACHAR*                RealDwgHostApp::getEnv (const ACHAR* var)
    {
    // I just want to see if this is called.
    // assert (false);
    return __super::getEnv (var);
    }

#if RealDwgVersion < 2017
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
LCID                        RealDwgHostApp::getRegistryProductLCID ()
    {
    return DwgPlatformHost::Instance()._GetRegistryProductLCID ();
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
#if RealDwgVersion < 2013
const ACHAR*                RealDwgHostApp::getRegistryProductRootKey ()
#else
const ACHAR*                RealDwgHostApp::getMachineRegistryProductRootKey ()
#endif
    {
#if defined (COMMENT)
    RealDWG loads Object Enablers (OEs) by looking at the following registry keys:
      HKCU\Software\<ProductRootKey>\Application\<OEName>\Loader
      HKLM\Software\<ProductRootKey>\Application\<OEName>\Loader
      HKCU\Software\ObjectDBX\<Version>\Application\<OEName>\Loader
      HKLM\Software\ObjectDBX\<Version>\Application\<OEName>\Loader

    Our observation is that Autodesk installs all the OEs that they write in HKLM\Software\Autodesk\AutoCAD\<Version>\<BuildNum>\Application\
    Other OE developer seem to install them in HKLM\Software\ObjectDBX\<Version>\Application\
    Where <Version> is something like R17.2, and <BuildNum> is something like <ACAD-7001:409> - see FindAutoCADRegistryRootKey.

    Here is how we take advantage of that:
     On a 32-bit operating system:
       If user has AutoCAD installed, we return the AutoCAD registry root key from this method. That makes MicroStation use the Autodesk OEs.
       If user does not have AutoCAD installed, we return SOFTWARE\ObjectDBX\<Version> as the root key.
     On a 64-bit operating system:
       Autodesk only allows installation of 64-bit AutoCAD on 64-bit operating system.
       When we try to find the AutoCAD installation, we fail to find it, because the SOFTWARE\Wow6432Node doesn't have the AcadLocation key (see FindAutoCADRegistryRootKey comment)
       So in that case we again return SOFTWARE\Autodesk\ObjectDBX\<Version>. The Wow6432Node seems to contain the 32-bit OE's that are installed, and the non-Wow6432Node
         entry contains the 64-bit OE's that are installed.

    Regardless of whether we're on a 64- or 32-bit OS, we do not have our own root key, because we don't want MicroStation-specific OE's.

    When we install MicroStation, we set SOFTWARE\Autodesk\ObjectDBX\<Version>\Application\<enablersTheyGiveUsToShip>\LOADER to point to our
    installation of the OE's that Autodesk supplies with RealDWG.

    The net result is that if 32-bit AutoCAD is installed, we will find their versions first. Otherwise, we find our versions.

    Note:
     RealDWG tries to load the OE's when we call AcDbDatabase::readDwgFile.
     It will ONLY try to load the object enablers if it finds an entry in ProductRootKey\\Applications - that's where the mapping from OE name to file name occurs.
     When it tries to load the object enabler, it first calls RealDwgHostApp::findFile with the entry that is in the Application\<AppName>\LOADER key.
#endif
    const ACHAR*    rootKey = DwgPlatformHost::Instance()._GetRegistryProductRootKey();
    if (NULL != rootKey && 0 != rootKey[0])
        return  rootKey;

#if RealDwgVersion < 2013
    return __super::getRegistryProductRootKey ();
#else
    return __super::getMachineRegistryProductRootKey ();
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
#if RealDwgVersion >= 2013
const ACHAR*                RealDwgHostApp::getUserRegistryProductRootKey ()
    {
    return  this->getMachineRegistryProductRootKey();
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
const ACHAR*                RealDwgHostApp::product()
    {
    return DwgPlatformHost::Instance()._Product ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Boolean              RealDwgHostApp::readyToDisplayMessages()
    {
    return Adesk::kTrue;
    }


#define MSG_INSECURE_ENVIRONMENT_SHORT      534

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        RealDwgHostApp::getPassword
(
const ACHAR*        dwgName,
PasswordOptions     options,
wchar_t*            password,
const size_t        bufSize
)
    {
    DwgPlatformHost::FilePasswordOption     passwordOption = (DwgPlatformHost::FilePasswordOption) options;
    return  DwgPlatformHost::Instance()._GetPassword(dwgName, passwordOption, password, bufSize);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::fatalError
(
const ACHAR*                format,
...
)
    {
    va_list  varArgs;

    va_start (varArgs, format);
    DwgPlatformHost::Instance()._FatalError (format, varArgs);
    va_end (varArgs);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::alert
(
const ACHAR*                message
) const
    {
    DwgPlatformHost::Instance()._Alert (message);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/17
+---------------+---------------+---------------+---------------+---------------+------*/
bool    RealDwgHostApp::notifyCorruptDrawingFoundOnOpen (AcDbObjectId id, Acad::ErrorStatus es)
    {
    WString fmtString;
    if (SUCCESS == RmgrResource::LoadWString(fmtString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOErrors, REALDWGMESSAGE_CorruptionDetected))
        {
        UInt64  handle = RealDwgUtil::CastDBHandle (id.handle());
        const ACHAR*    className = id.objectClass()->name ();
        if (nullptr == className || 0 == className[0])
            className = L"?";

        WString msg;
        WString::Sprintf (msg, fmtString.c_str(), className, handle, acadErrorStatusText(es));
        NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Warning, msg.c_str(), nullptr, 0, OutputMessageAlert::None));
        }

    /*-----------------------------------------------------------------------------------
    Return true to allow reading the file, then followed by AcDbDatabase::needsRecovery 
    which actually checks for file corruption that is comparable to what ACAD does.

    The default implementation returns false, which causes fatalError to be called!
    -----------------------------------------------------------------------------------*/
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgHostApp::SetDgnFile (DgnFileP file)
    {
    m_file = file;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgHostAppR     RealDwgHostApp::Instance()
    {
    if (NULL == s_instance)
        s_instance = new RealDwgHostApp();

    return *s_instance;
    }


RealDwgHostAppP     RealDwgHostApp::s_instance;


// implementations of RealDwg-required members of AcRxObject.
ACRX_NO_CONS_DEFINE_MEMBERS (ToDgnExtension, AcRxObject);

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgFileIO::AddBigfontCodepageConfiguration ()
    {
    BeFileName fileName;
    // Add codepage from ACAD's bigfont.ini file to our font configuration.
    if (!DwgPlatformHost::Instance()._GetBigfontInitFile(fileName))
        return;

    BeFileStatus    status = BeFileStatus::UnknownError;
    BeTextFilePtr   textFile = BeTextFile::Open (status, fileName.GetWCharCP(), TextFileOpenType::Read, TextFileOptions::None);
    if (BeFileStatus::Success != status || !textFile.IsValid())
        return;

    WString         str;
    while (TextFileReadStatus::Success == textFile->GetLine(str))
        {
        // trim spaces from line
        WString::size_type  pos1 = str.find_first_not_of(L" \t");
        WString::size_type  pos2 = str.find_last_not_of(L" \t");
        str = str.substr(pos1 == WString::npos ? 0 : pos1, pos2 == WString::npos ? str.length() - 1 : pos2 - pos1 + 1);

        // strip out comments
        pos1 = str.find_first_of (L'#');
        str = str.substr (0, pos1);
        if (0 != str.size ())
            {
            UInt32                  cp = 0;
            WString::size_type      semi = str.find_first_of (L';');
            if (0 != semi)
                {
                WString val = str.substr (semi+1);
                swscanf (val.c_str(), L"%d", &cp);
                str.erase (semi);
                }

            LangCodePage codepage;
            switch (cp)
                {
                case 1: codepage = LangCodePage::Japanese;               break;
                case 2: codepage = LangCodePage::Traditional_Chinese;    break;
                case 3: codepage = LangCodePage::Korean;                 break;
                case 4: codepage = LangCodePage::Johab;                  break;
                case 5: codepage = LangCodePage::Simplified_Chinese;     break;
                default:
                    continue;
                }

            FontSpecialChars                special;
            bvector<DgnFontNamedSymbolPtr>  namedSymbols;
            DgnFontManager::GetManager().AddShxFontConfig (BeFileName(str.c_str()), false, codepage, special, namedSymbols);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
static long     GetValidLocaleId ()
    {
    // default to English
    long        lcid = 0x0409;

#if RealDwgVersion >= 2013
    // find this host app
    static WChar    langDir[MAX_PATH] = {0};
    static WChar    moduleName[] = L"RealDwgFileIO.dll";
    if (GetModuleFileName(GetModuleHandle(moduleName), langDir, _countof(langDir)) == 0)
        return  lcid;

    // strip off dll file name
    int             dirEnd = (int) (wcslen(langDir) - wcslen(moduleName));
    if (dirEnd > 0)
        langDir[dirEnd] = 0;

    // append the sub-folder from the product LCID (system locale by default)
    lcid = (long)DwgPlatformHost::Instance()._GetRegistryProductLCID ();
    switch (lcid)
        {
        case 0x0804: wcscat (langDir, L"zh-CN");      break;  // Simplified Chinese
        case 0x0404: wcscat (langDir, L"zh-TW");      break;  // Chinese Traditional
        case 0x0405: wcscat (langDir, L"cs-CZ");      break;  // Czech
        case 0x0407: wcscat (langDir, L"de-DE");      break;  // German
        case 0x040A: wcscat (langDir, L"es-ES");      break;  // Spanish
        case 0x040C: wcscat (langDir, L"fr-FR");      break;  // French
        case 0x040E: wcscat (langDir, L"hu-HU");      break;  // Hungarian
        case 0x0410: wcscat (langDir, L"it-IT");      break;  // Italian
        case 0x0411: wcscat (langDir, L"ja-JP");      break;  // Japanese
        case 0x0412: wcscat (langDir, L"ko-KR");      break;  // Korean
        case 0x0415: wcscat (langDir, L"pl-PL");      break;  // Polish
        case 0x0416: wcscat (langDir, L"pt-BR");      break;  // Portuguese (Brazil)
        case 0x0419: wcscat (langDir, L"ru-RU");      break;  // Russian
        default:
            lcid = 0x0409;
        }

    // if the locale sub-folder exists, assume it to be valid; otherwise default to English:
    if (0x0409 != lcid && -1 == _waccess(langDir, 0))
        lcid = 0x0409;
#endif

    return  lcid;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/16
+---------------+---------------+---------------+---------------+---------------+------*/
static void     LoadDBXByFileName (WStringCR dbxName)
    {
    if (dbxName.empty())
        return;

    AcString    fileName (dbxName.c_str());

    // drop quotes at the beginning and the end of the file name:
    fileName.trimLeft (L'\"');
    fileName.trimRight (L'\"');
        
    // must have a .dbx extension
    AcString    ext = fileName.substr (fileName.length() - 4, -1);
    if (0 == ext.compareNoCase(L".dbx"))
        {
        WString     shortName = BeFileName::GetFileNameAndExtension (dbxName.GetWCharCP());
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Info, REALDWGMESSAGECENTER_LoadingUserDBX, false, shortName.GetWCharCP(), dbxName.GetWCharCP());
        acrxLoadModule (fileName.kwszPtr(), true);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/16
+---------------+---------------+---------------+---------------+---------------+------*/
static void     LoadUserObjectDBXModules ()
    {
    /*-----------------------------------------------------------------------------------
    Allow users/app developers to load DBX modules at the startup.  This may be needed 
    because apparently not all object enablers demand load DBX's.  A case in point, AT&T.
    -----------------------------------------------------------------------------------*/
    WString     dbxList;
    if (BSISUCCESS != ConfigurationManager::GetVariable(dbxList, L"MS_DWG_OBJECTDBX"))
        return;

    dbxList.Trim ();
    if (dbxList.empty())
        return;

    size_t      foundAt = WString::npos;
    while (WString::npos != (foundAt = dbxList.find(L';')))
        {
        // load this DBX
        LoadDBXByFileName (dbxList.substr(0, foundAt));
        // move to next DBX
        dbxList = dbxList.substr (foundAt + 1, WString::npos);
        }

    // last file, or the only file in the list, that does not end up with a ;
    if (!dbxList.empty())
        LoadDBXByFileName (dbxList);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                RealDwgFileIO::Initialize ()
    {
    if (s_realDwgInitializeStatus < 0)
        {
        // initialize RealDwg.
        // set up our HostApplications
        Acad::ErrorStatus   status = Acad::eOk;
        if (!s_hostAppInitialized && Acad::eOk != (status = acdbSetHostApplicationServices (&RealDwgHostApp::Instance())))
            s_realDwgInitializeStatus = 0;
        s_hostAppInitialized = Acad::eOk == status;

        // get a valid locale ID
        long    lcid = GetValidLocaleId ();

        // validate RealDWG setup with the locale
        if (Acad::eOk != (status = acdbValidateSetup (lcid)))
            s_realDwgInitializeStatus = 0;

        // set up our protocol extension.
        if (RealDwgSuccess != ToDgnExtension::Initialize() || RealDwgSuccess != ToDwgExtension::Initialize())
            s_realDwgInitializeStatus = 0;

        AddBigfontCodepageConfiguration ();

#if defined (TESTING)
        AcRxDynamicLinker*  pDynLinker      = AcRxDynamicLinker::cast(acrxSysRegistry()->at(ACRX_DYNAMIC_LINKER));
        // Poke around in AcRxDynamicLinker
        const ACHAR*        productKey      = pDynLinker->ProductKey();
        const Adesk::UInt32 productLCID     = pDynLinker->ProductLcid();
        const ACHAR*        acadProductKey  = acrxProductKey();
#endif

        // load MicroStation Extensions
        DwgPlatformHost::Instance()._LoadCooperativeObjectEnablers ();

        // load ObjectDBX modules specified by user (eg. AT&T)
        LoadUserObjectDBXModules ();

        // everything OK.
        s_realDwgInitializeStatus = 1;
        }

    return 0 != s_realDwgInitializeStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        MstnInterface::RegisterRealDwgDgnAdapter (IRealDwgDgnAdapter* adapter)
    {
    MstnInterfaceHelper::Instance().AddAdapter (adapter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
MstnInterfaceHelper::MstnInterfaceHelper ()
    {
    m_dwgConversionSettings = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        MstnInterfaceHelper::AddAdapter (IRealDwgDgnAdapter* adapter)
    {
    m_adapters.push_back (adapter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        MstnInterfaceHelper::LoadDgnProtocolExtensions ()
    {
    // call all the sourceFunctions - they will register protocol extensions if necessary.
    bvector<IRealDwgDgnAdapter*>::iterator iterator;
    for (iterator = m_adapters.begin(); iterator != m_adapters.end(); iterator++)
        {
        IRealDwgDgnAdapter *adapter = *iterator;
        adapter->AddProtocolExtension ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
IDwgConversionSettings&     MstnInterfaceHelper::GetSettings()
    {
    if (NULL == m_dwgConversionSettings && !DwgPlatformHost::Instance()._GetDwgConversionSettings(m_dwgConversionSettings))
        {
        m_dwgConversionSettings = ConvertContext::GetDefaultConversionSettings();
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_DwgSettingsNotFound, false, NULL, NULL);
        }

    return *m_dwgConversionSettings;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
MstnInterfaceHelper&        MstnInterfaceHelper::Instance ()
    {
    if (NULL == s_instance)
        s_instance = new MstnInterfaceHelper();
    return *s_instance;
    }

MstnInterfaceHelper*    MstnInterfaceHelper::s_instance;


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            dwgFileIO_addFileTypes ()
    {
    // allow a non-MicroStation DwgPlatformHost to add DWG/DXF file type on DgnFileIO
    DgnFileTypeRegistry::AddFileType (new RealDwgFileType());
    DgnFileTypeRegistry::AddFileType (new RealDxfFileType());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RDWG_EXPORTED DgnFileTypeP      dwgFileIO_getFileType (int fileType)
    {
    // "Options" for DWG...
    fileTypeRegistry_setOptionsCommand (DgnFileFormatType::DWG, L"dwgsettings", "openDialog");
    fileTypeRegistry_setOptionsCommand (DgnFileFormatType::DXF, L"dwgsettings", "openDialog");

    if (DgnFileFormatType::DWG == (DgnFileFormatType)fileType)
        return new RealDwgFileType ();
    else if (DgnFileFormatType::DXF == (DgnFileFormatType)fileType)
        return new RealDxfFileType ();

    return NULL;
    }

}   // end RealDwg namespace

END_BENTLEY_NAMESPACE


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/17
+---------------+---------------+---------------+---------------+---------------+------*/
extern "C" RDWG_EXPORTED DgnFileTypeP   v8FileIOImplementer_getType (int fileFormat)
    {
    return Bentley::RealDwg::dwgFileIO_getFileType (fileFormat);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
extern "C" RDWG_EXPORTED StatusInt      realDwg_getRealDwgVersion (WCharP versionString, int* versionNumber)
    {
    StatusInt   status = BSISUCCESS;
    int         currentVersion = 2009;
#if RealDwgVersion == 2009
    currentVersion = 2009;
#elif RealDwgVersion == 2010
    currentVersion = 2010;
#elif RealDwgVersion == 2011
    currentVersion = 2011;
#elif RealDwgVersion == 2012
    currentVersion = 2012;
#elif RealDwgVersion == 2013
    currentVersion = 2013;
#elif RealDwgVersion == 2014
    currentVersion = 2014;
#elif RealDwgVersion == 2015
    currentVersion = 2015;
#elif RealDwgVersion == 2016
    currentVersion = 2016;
#elif RealDwgVersion == 2017
    currentVersion = 2017;
#elif RealDwgVersion == 2018
    currentVersion = 2018;
#elif RealDwgVersion == 2019
    currentVersion = 2019;
#elif RealDwgVersion == 2021
    currentVersion = 2021;
#elif RealDwgVersion == 2022
    currentVersion = 2022;
#elif RealDwgVersion == 2023
    currentVersion = 2023;
#else
    status = ERROR;
    assert (false);
    return  status;
#endif
    if (NULL != versionNumber)
        *versionNumber = currentVersion;
    if (NULL != versionString)
        {
        WString     rscString;
        status = RmgrResource::LoadWString (rscString, Bentley::RealDwg::DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_USING_VERSION);
        if (SUCCESS == status)
            swprintf (versionString, rscString.c_str(), currentVersion);
        }
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
extern "C" RDWG_EXPORTED bool   realDwg_isFileFormatNewerThan (int olderVersion)
    {
    int     currentVersion = 2009;

    if (BSISUCCESS == realDwg_getRealDwgVersion (nullptr, &currentVersion))
        {
        if (currentVersion <= 2023)
            return  olderVersion < 2018;
        if (currentVersion <= 2022)
            return  olderVersion < 2018;
        else if (currentVersion <= 2021)
            return  olderVersion < 2018;
        else if (currentVersion <= 2019)
            return  olderVersion < 2018;
        else if (currentVersion <= 2017 && currentVersion >= 2013)
            return  olderVersion < 2013;
        else if (currentVersion <= 2012 && currentVersion >= 2010)
            return  olderVersion < 2010;
        else if (currentVersion <= 2009 && currentVersion >= 2007)
            return  olderVersion < 2007;
        }

    return  false;
    }


