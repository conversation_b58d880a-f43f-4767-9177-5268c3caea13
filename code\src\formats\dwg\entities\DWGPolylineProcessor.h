#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbpolyface.h>
#endif

namespace IModelExport {

//=======================================================================================
// Polyline Geometry Data Structures
//=======================================================================================

struct PolylineVertex {
    Point3d position;
    double bulge = 0.0;        // Arc bulge factor
    double startWidth = 0.0;   // Starting width
    double endWidth = 0.0;     // Ending width
    int vertexFlags = 0;       // Vertex flags
    Vector3d tangent = Vector3d(0, 0, 0);  // Tangent direction
    
    // Mesh-specific data
    int meshM = 0;             // M direction index
    int meshN = 0;             // N direction index
    
    bool hasArc() const { return std::abs(bulge) > 1e-10; }
    bool hasWidth() const { return startWidth > 1e-10 || endWidth > 1e-10; }
};

struct PolylineGeometry {
    std::vector<PolylineVertex> vertices;
    bool isClosed = false;
    bool is3D = false;
    bool hasWidth = false;
    bool hasArcs = false;
    
    // Polyline properties
    double constantWidth = 0.0;
    double elevation = 0.0;
    Vector3d normal = Vector3d(0, 0, 1);
    
    // Mesh properties (for polygon mesh)
    int meshM = 0;             // M direction size
    int meshN = 0;             // N direction size
    bool isMeshClosed = false;
    bool isPolyfaceMesh = false;
    
    // Smoothing and fitting
    int smoothType = 0;        // Smooth surface type
    int surfaceDensityM = 0;   // M direction density
    int surfaceDensityN = 0;   // N direction density
    
    // Validation helpers
    bool IsValid() const;
    bool HasValidVertices() const;
    bool IsSimplePolyline() const;
    bool IsPolygonMesh() const;
    bool IsPolyfaceMesh() const;
};

//=======================================================================================
// Polyline Validation Results
//=======================================================================================

struct PolylineValidationResult : public DWGValidationResult {
    bool hasValidVertices = false;
    bool hasValidBulges = false;
    bool hasValidWidths = false;
    bool hasValidMeshData = false;
    int invalidVertexCount = 0;
    int invalidBulgeCount = 0;
    double totalLength = 0.0;
    
    void AddPolylineError(const std::string& error) {
        AddError("Polyline: " + error);
    }
    
    void AddPolylineWarning(const std::string& warning) {
        AddWarning("Polyline: " + warning);
    }
};

//=======================================================================================
// DWG Polyline Processor
//=======================================================================================

class DWGPolylineProcessor : public DWGEntityProcessor {
public:
    DWGPolylineProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGPolylineProcessor"; }

    // Polyline-specific processing methods
    DWGProcessingStatus ProcessPolyline(const PolylineGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus Process2DPolyline(const PolylineGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus Process3DPolyline(const PolylineGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPolygonMesh(const PolylineGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPolyfaceMesh(const PolylineGeometry& geometry, const std::string& layer = "");

    // Validation methods
    PolylineValidationResult ValidatePolylineGeometry(const PolylineGeometry& geometry) const;
    bool ValidateVertices(const std::vector<PolylineVertex>& vertices) const;
    bool ValidateBulges(const std::vector<PolylineVertex>& vertices) const;
    bool ValidateWidths(const std::vector<PolylineVertex>& vertices) const;
    bool ValidateMeshData(const PolylineGeometry& geometry) const;

    // Polyline processing and repair methods
    bool RepairPolylineGeometry(PolylineGeometry& geometry) const;
    bool SimplifyPolyline(PolylineGeometry& geometry, double tolerance = 1e-6) const;
    bool OptimizeVertices(std::vector<PolylineVertex>& vertices) const;
    bool ValidateAndFixBulges(std::vector<PolylineVertex>& vertices) const;

    // Conversion methods
    bool ConvertToSimplePolyline(const PolylineGeometry& complex, PolylineGeometry& simple, double tolerance = 0.01) const;
    bool ConvertArcsToLines(PolylineGeometry& geometry, double tolerance = 0.01) const;
    bool ConvertToMesh(const PolylineGeometry& polyline, PolylineGeometry& mesh) const;

    // Arc processing (based on RealDwgFileIO bulge handling)
    bool ProcessArcSegment(const PolylineVertex& start, const PolylineVertex& end, std::vector<Point3d>& arcPoints) const;
    double CalculateArcRadius(const Point3d& start, const Point3d& end, double bulge) const;
    Point3d CalculateArcCenter(const Point3d& start, const Point3d& end, double bulge) const;
    bool ValidateBulgeValue(double bulge) const;

private:
    // Vertex processing (based on RealDwgFileIO vertex handling)
    bool ProcessVertexData(const PolylineVertex& vertex, bool is3D) const;
    bool ValidateVertexPosition(const Point3d& position) const;
    bool ValidateVertexWidth(double startWidth, double endWidth) const;
    void FixVertexData(PolylineVertex& vertex) const;
    
    // Width processing
    bool ProcessVariableWidth(const std::vector<PolylineVertex>& vertices) const;
    bool HasVariableWidth(const std::vector<PolylineVertex>& vertices) const;
    void OptimizeWidthData(std::vector<PolylineVertex>& vertices) const;
    
    // Mesh processing
    bool ValidateMeshDimensions(int meshM, int meshN) const;
    bool ValidateMeshVertexCount(const std::vector<PolylineVertex>& vertices, int meshM, int meshN) const;
    bool ProcessMeshSurface(const PolylineGeometry& geometry) const;
    
    // Geometric analysis
    double CalculatePolylineLength(const PolylineGeometry& geometry) const;
    double CalculateSegmentLength(const PolylineVertex& start, const PolylineVertex& end) const;
    bool IsPolylineClosed(const PolylineGeometry& geometry, double tolerance = 1e-6) const;
    bool HasSelfIntersections(const PolylineGeometry& geometry) const;
    
    // Transformation and coordinate processing
    void TransformPolylineGeometry(PolylineGeometry& geometry) const;
    void ValidateAndFixVertices(std::vector<PolylineVertex>& vertices) const;
    void NormalizePolylineData(PolylineGeometry& geometry) const;
    
    // Optimization algorithms
    bool RemoveRedundantVertices(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6) const;
    bool SimplifyArcs(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6) const;
    bool MergeCollinearSegments(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbPolyline* CreateDWGPolyline(const PolylineGeometry& geometry) const;
    AcDb2dPolyline* CreateDWG2DPolyline(const PolylineGeometry& geometry) const;
    AcDb3dPolyline* CreateDWG3DPolyline(const PolylineGeometry& geometry) const;
    AcDbPolygonMesh* CreateDWGPolygonMesh(const PolylineGeometry& geometry) const;
    AcDbPolyFaceMesh* CreateDWGPolyfaceMesh(const PolylineGeometry& geometry) const;
    
    // Vertex creation helpers
    bool SetPolylineVertex(AcDbPolyline* polyline, int index, const PolylineVertex& vertex) const;
    bool Set2DPolylineVertex(AcDb2dPolyline* polyline, const PolylineVertex& vertex) const;
    bool Set3DPolylineVertex(AcDb3dPolyline* polyline, const PolylineVertex& vertex) const;
    
    // Property setting helpers
    bool SetPolylineProperties(AcDbPolyline* polyline, const PolylineGeometry& geometry) const;
    bool SetMeshProperties(AcDbPolygonMesh* mesh, const PolylineGeometry& geometry) const;
    bool SetPolyfaceProperties(AcDbPolyFaceMesh* polyface, const PolylineGeometry& geometry) const;
    
    // Bulge and arc processing
    bool SetVertexBulge(AcDbPolyline* polyline, int index, double bulge) const;
    bool SetVertexWidth(AcDbPolyline* polyline, int index, double startWidth, double endWidth) const;
    
    // Error handling for RealDWG operations
    bool HandlePolylineCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedPolylines = 0;
    mutable size_t m_repairedPolylines = 0;
    mutable size_t m_simplifiedPolylines = 0;
    mutable size_t m_convertedToMesh = 0;
    mutable size_t m_processedArcs = 0;
    
    // Configuration
    double m_vertexTolerance = 1e-10;
    double m_bulgeTolerance = 1e-10;
    double m_widthTolerance = 1e-10;
    double m_simplificationTolerance = 1e-6;
    int m_maxVertices = 32767;  // DWG limit
    bool m_enableAutoRepair = true;
    bool m_enableSimplification = true;
    bool m_enableOptimization = true;
};

//=======================================================================================
// Polyline Utility Functions
//=======================================================================================

class PolylineUtils {
public:
    // Bulge calculations
    static double CalculateBulge(const Point3d& start, const Point3d& end, const Point3d& arcPoint);
    static Point3d CalculateArcMidpoint(const Point3d& start, const Point3d& end, double bulge);
    static double BulgeToAngle(double bulge);
    static double AngleToBulge(double angle);
    
    // Arc geometry
    static bool CalculateArcGeometry(const Point3d& start, const Point3d& end, double bulge,
                                    Point3d& center, double& radius, double& startAngle, double& endAngle);
    static std::vector<Point3d> TesselateArc(const Point3d& start, const Point3d& end, double bulge, int segments = 16);
    
    // Polyline analysis
    static double CalculateArea(const PolylineGeometry& geometry);
    static Point3d CalculateCentroid(const PolylineGeometry& geometry);
    static bool IsClockwise(const PolylineGeometry& geometry);
    static bool IsConvex(const PolylineGeometry& geometry);
    
    // Validation utilities
    static bool IsValidBulge(double bulge);
    static bool IsValidWidth(double width);
    static bool IsValidMeshSize(int meshM, int meshN);
    
    // Conversion utilities
    static bool ConvertToCounterClockwise(PolylineGeometry& geometry);
    static bool ReversePolyline(PolylineGeometry& geometry);
    static bool ClosePolyline(PolylineGeometry& geometry, double tolerance = 1e-6);
    
    // Optimization utilities
    static bool RemoveDuplicateVertices(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6);
    static bool SimplifyDouglasPeucker(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6);
    static bool OptimizeArcSegments(std::vector<PolylineVertex>& vertices, double tolerance = 1e-6);
};

} // namespace IModelExport
