/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdCircle.cpp $
|
|  $Copyright: (c) 2012 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtCircle : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbCircle*     pCircle = AcDbCircle::cast (acObject);
    AcGeVector3d    normal = pCircle->normal ();
    AcGePoint3d     center = pCircle->center ();

    return context.CreateElementFromArcParams (outElement, &normal, center, NULL, pCircle->radius(), pCircle->radius(),
                                               0.0, msGeomConst_2pi, pCircle->thickness(), true, pCircle);
    }

};
