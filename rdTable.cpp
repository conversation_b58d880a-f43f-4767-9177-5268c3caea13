/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdTable.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/


// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          12/13
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtTable : public ToDgnExtension
{
    mutable AcDbTable*              m_acTable;
    mutable TextTable*              m_textTable;
    mutable ConvertToDgnContext*    m_toDgnContext;
    mutable DwgContextForTextBlock* m_contextForTextBlock;
    mutable RotMatrix               m_textTableMatrix;
    mutable DPoint3d                m_textTableOrigin;
    mutable UInt32                  m_numFieldCells;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    m_acTable = AcDbTable::cast (acObject);
    if (NULL == m_acTable)
        return  NullObject;

    m_textTable = NULL;
    m_toDgnContext = &context;
    m_contextForTextBlock = new DwgContextForTextBlock (&context, NULL, m_acTable);
    if (NULL == m_contextForTextBlock)
        return  OutOfMemoryError;

    // get table origin:
    RealDwgUtil::DPoint3dFromGePoint3d (m_textTableOrigin, m_acTable->position());
    m_toDgnContext->GetTransformToDGN().Multiply (m_textTableOrigin);

    /*------------------------------------------------------------------------------------------------------
    Set table definition matrix:
    Although AcDbTable is a sub-class of AcDbBlockReference, it does not appear to support annotation scale. 
    The table size appears to be done by height and width.  Ignore scale for now.
    ------------------------------------------------------------------------------------------------------*/
    DVec3d      xAxis, zAxis;
    RealDwgUtil::DVec3dFromGeVector3d (xAxis, m_acTable->direction());
    RealDwgUtil::DVec3dFromGeVector3d (zAxis, m_acTable->normal());
    xAxis.Normalize ();
    zAxis.Normalize ();
    DVec3d      yAxis = DVec3d::FromCrossProduct (zAxis, xAxis);
    
    m_textTableMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);
    m_numFieldCells = 0;
    
    RealDwgStatus   status = this->CreateTextTableElement (outElement);

    if (RealDwgSuccess == status)
        context.ElementHeaderFromEntity (outElement, m_acTable);
    else
        status = this->DropTableToBlock (outElement);

    delete m_contextForTextBlock;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateTextTableElement (EditElementHandleR outElement) const
    {
    int             numColumns = m_acTable->numColumns ();
    int             numRows = m_acTable->numRows ();
    if (0 == numColumns && 0 == numRows)
        return  CantCreateTextTable;

    AcDbObjectId    textstyleId = m_acTable->textStyle (AcDb::kTitleRow);
    if (!textstyleId.isValid() || !textstyleId.objectClass()->isDerivedFrom(AcDbTextStyleTableRecord::desc()))
        textstyleId = acdbSymUtil()->textStyleStandardId (m_acTable->database());

    // really the index of text style!?
    ElementId       dgnTextstyleId = m_toDgnContext->GetFileHolder().GetTextStyleIndex()->GetDgnId (textstyleId.handle());
    double          backupTextHeight = this->ComputeBackupTextHeight (numRows);

    TextTablePtr    textTable = TextTable::Create (numRows, numColumns, dgnTextstyleId, backupTextHeight, *m_toDgnContext->GetModel());
    if (!textTable.IsValid() || NULL == (m_textTable = textTable.get()))
        return  CantCreateTextTable;

    // try to break table to sub tables - return to drop the table if the break failed.
    if (RealDwgSuccess != this->BreakTextTableToSubTables())
        return  CantCreateTextTable;

    double          scaleToDgn = m_toDgnContext->GetScaleToDGN ();
    DPoint3d        topLeftPoint = m_textTableOrigin;
    bool            isTopDown = AcDb::kTtoB == m_acTable->flowDirection();
    if (!isTopDown)
        {
        // table flows from bottom to top
        DVec3d      bottom2top = DVec3d::From (0.0, m_acTable->height() * scaleToDgn, 0.0);
        m_textTableMatrix.Multiply (bottom2top);
        topLeftPoint.Add (bottom2top);
        }

    textTable->SetOrigin (topLeftPoint);
    textTable->SetRotation (m_textTableMatrix);
    textTable->SetDefaultColumnWidth (scaleToDgn * m_acTable->columnWidth(0));
    textTable->SetDefaultRowHeight (scaleToDgn * m_acTable->rowHeight(0));
    textTable->SetDefaultLineColor (m_toDgnContext->GetDgnColor(m_acTable->entityColor()));
    textTable->SetDefaultLineWeight (m_toDgnContext->GetDgnWeight(m_acTable->lineWeight()));
    textTable->SetDefaultLineStyle (m_toDgnContext->GetDgnStyle(m_acTable->linetypeId()));

    // set column width and row height before adding contents in cells
    this->SetColumnWidthAndRowHeight (numColumns, numRows, isTopDown);

    // create and add contents in cells
    if (RealDwgSuccess != this->SetCellContents(numColumns, numRows, isTopDown))
        return  CantCreateTextTable;

    if (BSISUCCESS != TextTableHandler::CreateTextTableElement(outElement, *m_textTable))
        return  CantCreateTextTable;

    // since we already have set default symbology for the text table, don't duplicate the effort:
    int     mask = HEADERRESTOREMASK_All & ~HEADERRESTOREMASK_Color & ~HEADERRESTOREMASK_Weight & ~HEADERRESTOREMASK_Style;
    m_toDgnContext->ElementHeaderFromEntity (outElement, m_acTable, mask);

    if (m_numFieldCells > 0)
        m_toDgnContext->AddPostProcessObject (m_acTable);

    // add xGraphics to display linestyle - TFS 88692
    TextTableHandler*   textTableHandler = dynamic_cast <TextTableHandler*> (&outElement.GetHandler());
    if (nullptr != textTableHandler)
        textTableHandler->ValidatePresentation (outElement);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
double          ComputeBackupTextHeight (int numRows) const
    {
    /*--------------------------------------------------------------------------------------------------------------
    TextTable constructor requires a non-zero backup text height which will be used to determine minimum row height,
    as well as minimum column width.  The minimum height and width will prevent TextTableRow::SetHeight and 
    TextTableColumn::SetWidth to successfully set the values we want if our values are greater than the minimum values.
    Therefore we want to find a backup text height that is smaller enough such that we will be able to set row height
    and column width later on.
    --------------------------------------------------------------------------------------------------------------*/
    double      backupTextHeight = m_toDgnContext->GetDatabase()->textsize ();

    for (int row = 0; row < numRows; row++)
        {
        double  minimumRowHeight = m_acTable->minimumRowHeight (row);

        if (minimumRowHeight < backupTextHeight)
            backupTextHeight = minimumRowHeight;
        }

    // invert the minimum row height calculation in TextTable constructor using the larger of hard coded margins:
    backupTextHeight -= 0.8 * backupTextHeight;

    backupTextHeight *= m_toDgnContext->GetScaleToDGN ();

    return  backupTextHeight;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetColumnWidthAndRowHeight (int numColumns, int numRows, bool isTopDown) const
    {
    double      scaleToDgn = m_toDgnContext->GetScaleToDGN ();

    // set width for columns
    for (int column = 0; column < numColumns; column++)
        {
        TextTableColumnP    tableColumn = m_textTable->GetColumn (column);

        if (NULL != tableColumn)
            {
            double          columnWidth = m_acTable->columnWidth(column) * scaleToDgn;
            tableColumn->SetWidth (columnWidth);
            tableColumn->SetWidthLock (true);
            }
        }

    // set height for rows
    for (int row = 0; row < numRows; row++)
        {
        double          rowHeight = m_acTable->rowHeight(row) * scaleToDgn;
        UInt32          dgnRow = isTopDown ? row : numRows - row - 1;
        TextTableRowP   tableRow = m_textTable->GetRow (dgnRow);

        if (NULL == tableRow)
            continue;


        // set cell style
        TableHeaderFooterType   cellStyle = TableHeaderFooterType::Body;
        switch (m_acTable->rowType(row))
            {
            case AcDb::kTitleRow:   cellStyle = TableHeaderFooterType::Title;    break;
            case AcDb::kHeaderRow:  cellStyle = TableHeaderFooterType::Header;   break;
            default:
            case AcDb::kDataRow:    cellStyle = TableHeaderFooterType::Body;     break;
            }

        tableRow->SetHeaderFooterType (cellStyle);
        double          minRowHeight = m_acTable->minimumRowHeight(row) * scaleToDgn;
        tableRow->SetFixedMinimumRowHeight(minRowHeight <= rowHeight ? minRowHeight : rowHeight);
       /* call SetFixedMinimumRowHeight before SetHeight to persist TEXTTABLE_ROW_PROP_FixedMinimumHeight property in TextTableInstanceHolder for each dwg table row.
        minimum row height value returned by TextTableRow::GetMinimumHeight  is greater than rowHeight which result in increase in row height for each dwg table row.*/
        tableRow->SetHeight(rowHeight);
        tableRow->SetHeightLock(true);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetCellContents (int numColumns, int numRows, bool isTopDown) const
    {
    // visit all effective table cells and conver them to DGN text table cells
    for (int column = 0; column < numColumns; column++)
        {
        double              widthBefore = 0.0;
        TextTableColumnP    tableColumn = m_textTable->GetColumn (column);
        if (NULL != tableColumn)
            widthBefore = tableColumn->GetWidth ();

        // revert row indices if the DWG table flows bottom up
        int     row = isTopDown ? 0 : numRows - 1;

        for (; isTopDown ? (row < numRows) : (row > -1); isTopDown ? row++ : row--)
            {
            /*-----------------------------------------------------------------------------------------------
            We want to merge cells prior to setting the content of a merged cell to ensure the effective cell
            size to be correct.  After cells merged, we proceed to setting the content of the min cell which
            has the content for merged cells, and ignore all interior cells.
            -----------------------------------------------------------------------------------------------*/
            TableCellIndex      cellIndex;
            if (!this->MergeCells(cellIndex, row, column, numRows, isTopDown))
                continue;

            // get the effective DGN cell based on table flow direction (i.e. top-down or bottom-up):
            UInt32          dgnRow = isTopDown ? row : numRows - row - 1;
            TextTableRowP   tableRow = m_textTable->GetRow (dgnRow);
            if (NULL == tableRow)
                return  MstnElementUnacceptable;

            double          heightBefore = tableRow->GetHeight ();
            TextTableCellP  tableCell = m_textTable->GetCell (TableCellIndex(dgnRow, column));
            if (NULL == tableCell)
                return  MstnElementUnacceptable;

            // set the cell margins before setting the content as otherwise the content may change the cell size
            this->SetTableCellMargins (tableCell, row, column);

            if (!m_acTable->isEmpty(row, column))
                {
                switch (m_acTable->cellType(row, column))
                    {
                    case AcDb::kTextCell:               // single text cell
                        this->SetTableCellFromTextCell (tableCell, row, column, 0, m_acTable->textString(row, column, 0));
                        break;
                    case AcDb::kBlockCell:              // single block cell
                        this->DropBlockCellToBlock (row, column, 0);
                        break;
                    case AcDb::kMultipleContentCell:    // cell with multiple contents
                        this->ProcessMultipleContents (tableCell, row, column);
                        break;
                    default:
                        return  CantCreateTextTable;
                    }
                }

            this->SetTableCellSymbology (tableCell, cellIndex, row, column, isTopDown);

            // reset rot height if it has been changed possibly due to margin changes:
            double          heightAfter = tableRow->GetHeight ();
            if (fabs(heightBefore - heightAfter) > TOLERANCE_UORPointEqual)
                tableRow->SetHeight (heightBefore);
            }

        // try to reset column width if it has been changed most likely due to a tight wordwrapping in a cell.
        if (NULL != tableColumn)
            {
            double      widthAfter = tableColumn->GetWidth ();
            if (fabs(widthBefore - widthAfter) > TEXT_WORDWRAP_ADJUST)
                tableColumn->SetWidth (widthBefore);
            }
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ProcessMultipleContents (TextTableCellP tableCell, UInt32 row, UInt32 column) const
    {
    RealDwgStatus   status = BadData;
    UInt32          numContents = m_acTable->numContents (row, column);

    for (UInt32 content = 0; content < numContents; content++)
        {
        AcDb::CellContentType   contentType = m_acTable->contentType (row, column, content);

        switch (contentType)
            {
            case AcDb::kCellContentTypeValue:
                status = this->SetTableCellFromAcValueCell (tableCell, row, column, content);
                break;
            case AcDb::kCellContentTypeField:
                status = this->SetTableCellFromFieldsCell (tableCell, row, column, content);
                break;
            case AcDb::kCellContentTypeBlock:
                status = this->DropBlockCellToBlock (row, column, content);
                break;
            case AcDb::kCellContentTypeUnknown:
            default:
                break;
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTableCellFromTextCell (TextTableCellP tableCell, UInt32 row, UInt32 column, UInt32 content, const AcString& acString) const
    {
    if (acString.isEmpty())
        return  EmptyText;

    TextBlockPtr    textBlock = TextBlock::Create (*m_toDgnContext->GetModel());
    if (!textBlock.IsValid())
        return  OutOfMemoryError;

    // DWG table is non-annotative - clear up annotation scale set from the constructor
    TextBlockProperties     textblockProperties = textBlock->GetProperties ();
    if (textblockProperties.HasAnnotationScale())
        {
        textblockProperties.ClearAnnotationScale ();
        textBlock->SetProperties (textblockProperties);
        }

    DVec2d          sizeBefore = tableCell->GetSize ();

    RealDwgStatus   status = this->SetTextBlockFromTextCell (textBlock.get(), row, column, content, acString);
    if (RealDwgSuccess != status || textBlock->IsEmpty())
        return  status;

    // invert TextTableHandler's adding the text descender to bottom margin - do this before setting the content:
    this->AdjustVerticalMarginsFromDescender (tableCell, row, column, content, textBlock.get());

    tableCell->SetOrientation (this->GetTextTableCellOrientation(row, column, content, textBlock.get()));
    tableCell->SetAlignment (this->GetTextTableCellAlignment(row, column));
    tableCell->SetTextBlock (*textBlock.get());

    /*---------------------------------------------------------------------------------------------------------
    If the text table cell size is changed by setting the content above, fix it by changing wordwarpping length
    and cell margins.
    ---------------------------------------------------------------------------------------------------------*/
    DVec2d                  sizeAfter = tableCell->GetSize ();
    TableCellMarginValues   margins = tableCell->GetMargins ();

    if (!sizeBefore.IsEqual(sizeAfter, margins.m_left))
        this->AdjustMarginsFromWordWrapping (tableCell, row, column, content, textBlock.get());

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTextBlockFromTextCell (TextBlock* textBlock, UInt32 row, UInt32 column, UInt32 content, const AcString& acString) const
    {
    if (acString.isEmpty())
        return  EmptyText;

    // do not set origin - text should be fitted in to the cell with margins and justifications
    DPoint3d        origin = DPoint3d::FromZero();

    // only rotate text by angle - text plane should be defined by text able element
    double          angle = m_acTable->rotation (row, column, content);
    RotMatrix       rotation;
    rotation.InitFromAxisAndRotationAngle (2, angle);

    double          textHeight = m_acTable->textHeight (row, column, content);
    DPoint2d        textSize = DPoint2d::From (textHeight, textHeight);
    textSize.Scale (m_toDgnContext->GetScaleToDGN());

    AcDbObjectId                    objectId = m_acTable->textStyle (row, column, content);
    AcDbTextStyleTableRecordPointer acTextstyle (objectId, AcDb::kForRead);
    DgnTextStylePtr                 dgnTextstyle = DgnTextStyle::GetByID(m_toDgnContext->GetFileHolder().GetTextStyleIndex()->GetDgnId(objectId.handle()), *m_toDgnContext->GetFile());

    DgnModelP               model = m_toDgnContext->GetModel ();
    TextBlockPropertiesPtr  textblockProperties;
    ParagraphPropertiesPtr  paragraphProperties;
    RunPropertiesPtr        runProperties;

    if (Acad::eOk == acTextstyle.openStatus() && dgnTextstyle.IsValid())
        {
        textblockProperties = TextBlockProperties::Create (*dgnTextstyle, *model);
        paragraphProperties = ParagraphProperties::Create (*dgnTextstyle, *model);
        runProperties = RunProperties::Create (*dgnTextstyle, *model);
        textSize.x *= acTextstyle->xScale ();
        }
    else
        {
        textblockProperties = TextBlockProperties::Create (*model);
        paragraphProperties = ParagraphProperties::Create (*model);
        runProperties = RunProperties::Create (DgnFontManager::GetDefaultTrueTypeFont (), textSize, *model);
        }

    if (textblockProperties->HasAnnotationScale())
        textblockProperties->ClearAnnotationScale ();

    // disable backwards & upside down
    textblockProperties->SetIsBackwards (false);
    textblockProperties->SetIsUpsideDown (false);
    // disable vertical for TTF
    if (DgnFontType::TrueType == runProperties->GetFont().GetType())
        textblockProperties->SetIsVertical (false);

    paragraphProperties->SetJustification (this->GetTextJustification(row, column));
    
    runProperties->SetFontSize (textSize);

    // set word wrap for the text
    double          wordWrapwidth = this->GetWordWrapWidth (row, column, angle, textblockProperties->IsVertical());
    textblockProperties->SetDocumentWidth(wordWrapwidth * textblockProperties->GetAnnotationScale());

    UInt32          contentColor = this->GetContentColor (row, column, content);
    if (!runProperties->HasColor() || runProperties->GetColor() != contentColor)
        runProperties->SetColor (contentColor);

    textBlock->SetType (TEXTBLOCK_TYPE_DwgMText);
    textBlock->SetOrientation (rotation);
    textBlock->SetUserOrigin (origin);
    textBlock->SetProperties (*textblockProperties);
    textBlock->SetParagraphPropertiesForAdd (*paragraphProperties);
    textBlock->SetRunPropertiesForAdd (*runProperties);
    textBlock->SetTextNodeProperties (runProperties.get ());

    textBlock->FromMText ((WCharCP)acString.kwszPtr (), *m_contextForTextBlock, m_toDgnContext->GetScaleToDGN());

    textBlock->PerformLayout ();

    AcDbSmartObjectPointer<AcDbField>   acField(m_acTable->fieldId(row, column, content), AcDb::kForRead);
    if (Acad::eOk == acField.openStatus())
        m_numFieldCells++;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
TableCellOrientation    GetTextTableCellOrientation (UInt32 row, UInt32 column, UInt32 content, TextBlockCP textBlock) const
    {
    if (textBlock->GetProperties().IsVertical())
        return TableCellOrientation::Vertical;

    // currently TextTableCell overrides TextBlock rotation by 4 orthogonal rotations.  Pending change of that in the future.
    double  angle = Angle::AdjustToSweep (m_acTable->rotation(row, column, content), 0, Angle::TwoPi());

    if (Angle::IsNearZero(angle))
        return TableCellOrientation::Horizontal;
    else if (Angle::NearlyEqual(angle, Angle::PiOver2()))
        return TableCellOrientation::Rotate270;             // shouldn't this be 90 degrees instead?
    else if (Angle::NearlyEqual(angle, 1.5 * Angle::Pi()))
        return TableCellOrientation::Rotate90;              // shouldn't this be 270 degrees instead?

    // all other rotations are currently ignored
    return TableCellOrientation::Horizontal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            AdjustMarginsFromWordWrapping (TextTableCellP tableCell, UInt32 row, UInt32 column, UInt32 content, TextBlockP textBlock) const
    {
    /*--------------------------------------------------------------------------------------------------------
    Setting a text content into a cell can change cell size and one such a cause is due to the word wrapping in
    TextBlock.  A small wrapping length is required to make it wrap the text correctly.  We do this by shrinking
    the horizontal margins of the table cell.
    --------------------------------------------------------------------------------------------------------*/
    // add a few UOR's to wordwrapping length
    TextBlockProperties textblockProps = textBlock->GetProperties ();
    double              wordwrapWidth = textblockProps.GetDocumentWidth() + TEXT_WORDWRAP_ADJUST;

    textblockProps.SetDocumentWidth (wordwrapWidth);
    textBlock->SetProperties (textblockProps);
    textBlock->PerformLayout ();

    // compensate the additinal wordwrapping UOR's by shorterning the margins
    TableCellMarginValues   margins = tableCell->GetMargins ();
    double                  halfMargin = 0.5 * TEXT_WORDWRAP_ADJUST;
    if (ISTEXTROTATIONVERTICAL(m_acTable->rotation(row, column, content)) != textblockProps.IsVertical())
        {
        margins.m_top -= halfMargin;
        if (margins.m_top < 0.0)
            margins.m_top = 0.0;

        margins.m_bottom -= halfMargin;
        if (margins.m_bottom < 0.0)
            margins.m_bottom = 0.0;
        }
    else
        {
        margins.m_left -= halfMargin;
        if (margins.m_left < 0.0)
            margins.m_left = 0.0;

        margins.m_right -= halfMargin;
        if (margins.m_right < 0.0)
            margins.m_right = 0.0;
        }

    tableCell->SetMargins (margins);
    tableCell->SetTextBlock (*textBlock);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            AdjustVerticalMarginsFromDescender (TextTableCellP tableCell, UInt32 row, UInt32 column, UInt32 content, TextBlockCP textBlock) const
    {
    /*--------------------------------------------------------------------------------------------------------------
    When it comes to cell height calculation, our TextTable always adds a text descender from the last line of the
    text, regardless whether any of the letters actually has a descender.  That is, the bottom margin of TextTable 
    always starts from the baseline. If the last line of the text does not have a descender, one gets added in table 
    cell fitting calculation.
    
    This is different than what ACAD does: its cell fitting calculation is based on actual/effective text.  If the 
    last line of text has a descender, the bottom margin starts from the bottom/descender line of the text.  If the
    text does not have a descender, it starts from the baseline.
    
    To make ACAD's text to be able to fit into our cell, we subtract the decender from bottom margin if the text does 
    not have a descender, i.e. we revert the calculation of TextBlockHolder::_GetEffectiveMarginValues.
    ---------------------------------------------------------------------------------------------------------------*/
    // FUTUREWORK: ignore vertical texts for now - it shall go with the element handler.
    if (ISTEXTROTATIONVERTICAL(m_acTable->rotation(row, column, content)) != textBlock->GetProperties().IsVertical())
        return;

    LineCP      lastLine = textBlock->End().GetCurrentLineCP ();
    if (NULL == lastLine)
        return;

    // subtract max descender from bottom margin and reset it to table cell
    double      maxDescender = lastLine->GetMaxDescender ();
    if (maxDescender > TOLERANCE_UORPointEqual)
        {
        TableCellMarginValues   margins = tableCell->GetMargins ();

        margins.m_bottom -= maxDescender;

        if (margins.m_bottom < 0.0)
            margins.m_bottom = 0.0;

        // TextBlockHolder::ComputeDescenderAdjustment also adjusts top margin - TFS620652.
        margins.m_top -= maxDescender;
        if (margins.m_top < 0.0)
            margins.m_top = 0.0;

        tableCell->SetMargins (margins);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetEffectiveCellWidth (int row, int column, bool isVertical) const
    {
    double      effectiveWidth = 0.0;
    int         rowFrom = 0, rowTo = 0, columnFrom = 0, columnTo = 0;

    if (m_acTable->isMergedCell(row, column, &rowFrom, &rowTo, &columnFrom, &columnTo))
        {
        // use accumulative width of merged cells
        if (isVertical)
            {
            for (int r = rowFrom; r <= rowTo; r++)
                effectiveWidth += m_acTable->rowHeight (r);
            }
        else
            {
            for (int c = columnFrom; c <= columnTo; c++)
                effectiveWidth += m_acTable->columnWidth (c);
            }
        }
    else
        {
        // not a merged cell, use current column width
        effectiveWidth = isVertical ? m_acTable->rowHeight(row) : m_acTable->columnWidth(column);
        }

    return  effectiveWidth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetWordWrapWidth (int row, int column, double angle, bool runVertical) const
    {
    bool        rotateVertical = ISTEXTROTATIONVERTICAL(angle);
    bool        isVertical = rotateVertical != runVertical;
    double      wordwrapWidth = this->GetEffectiveCellWidth (row, column, isVertical);
    double      totalMargin = 0.0;

#ifdef FUTUREWORK_AUTODESK_FIX_MARGINS
    if (isVertical)
        totalMargin = m_acTable->margin(row, column, AcDb::kCellMarginTop) + m_acTable->margin(row, column, AcDb::kCellMarginBottom);
    else
        totalMargin = m_acTable->margin(row, column, AcDb::kCellMarginLeft) + m_acTable->margin(row, column, AcDb::kCellMarginRight);
#else
    if (isVertical)
        totalMargin = 2.0 * m_acTable->margin(row, column, AcDb::kCellMarginTop);
    else
        {
        if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ACCOUNT_TABLEMERGEDCELLS_MARGIN_FOR_TEXTWRAP"))
            {
            int rowFrom = 0, rowTo = 0, columnFrom = 0, columnTo = 0;
            if (m_acTable->isMergedCell(row, column, &rowFrom, &rowTo, &columnFrom, &columnTo))
                {
                for (int cellColIdx = columnFrom; cellColIdx <= columnTo; cellColIdx++)
                    {
                    totalMargin += m_acTable->margin(row, cellColIdx, AcDb::kCellMarginLeft);
                    totalMargin += m_acTable->margin(row, cellColIdx, AcDb::kCellMarginRight);
                    }
                }
            else
                {
                totalMargin = m_acTable->margin(row, column, AcDb::kCellMarginLeft);
                totalMargin += m_acTable->margin(row, column, AcDb::kCellMarginRight);
                }
            }
        else
            totalMargin = 2.0 * m_acTable->margin(row, column, AcDb::kCellMarginLeft);
        }
#endif

    // word wrapping width is cell width less cell margins:
    wordwrapWidth -= totalMargin;

    wordwrapWidth *= m_toDgnContext->GetScaleToDGN ();

    return  wordwrapWidth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
TextElementJustification    GetTextJustification (UInt32 row, UInt32 column) const
    {
    switch (m_acTable->alignment(row, column))
        {
        case AcDb::kTopLeft:        return  TextElementJustification::LeftTop;
        case AcDb::kTopCenter:      return  TextElementJustification::CenterTop;
        case AcDb::kTopRight:       return  TextElementJustification::RightTop;
        case AcDb::kMiddleLeft:     return  TextElementJustification::LeftMiddle;
        case AcDb::kMiddleCenter:   return  TextElementJustification::CenterMiddle;
        case AcDb::kMiddleRight:    return  TextElementJustification::RightMiddle;
        case AcDb::kBottomLeft:     return  TextElementJustification::LeftDescender;
        case AcDb::kBottomCenter:   return  TextElementJustification::CenterDescender;
        case AcDb::kBottomRight:    return  TextElementJustification::RightDescender;
        }
    return  TextElementJustification::LeftTop;
    }
            
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
TableCellAlignment      GetTextTableCellAlignment (UInt32 row, UInt32 column) const
    {
    switch (m_acTable->alignment(row, column))
        {
        case AcDb::kTopLeft:        return  TableCellAlignment::LeftTop;
        case AcDb::kTopCenter:      return  TableCellAlignment::CenterTop;
        case AcDb::kTopRight:       return  TableCellAlignment::RightTop;
        case AcDb::kMiddleLeft:     return  TableCellAlignment::LeftMiddle;
        case AcDb::kMiddleCenter:   return  TableCellAlignment::CenterMiddle;
        case AcDb::kMiddleRight:    return  TableCellAlignment::RightMiddle;
        case AcDb::kBottomLeft:     return  TableCellAlignment::LeftBottom;
        case AcDb::kBottomCenter:   return  TableCellAlignment::CenterBottom;
        case AcDb::kBottomRight:    return  TableCellAlignment::RightBottom;
        }
    return  TableCellAlignment::LeftTop;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTableCellFromAcValueCell (TextTableCellP tableCell, int row, int column, int content) const
    {
    AcValue::DataType   dataType;
    AcValue::UnitType   unitType;
    Acad::ErrorStatus   es = m_acTable->getDataType (row, column, content, dataType, unitType);
    if (Acad::eOk != es)
        return  BadData;

    AcValue             acValue = m_acTable->value (row, column, content);
    if (!acValue.isValid())
        return  BadData;

    switch (dataType)
        {
        case AcValue::kString:
            return this->SetTableCellFromTextCell (tableCell, row, column, content, m_acTable->textString(row, column, content));

        case AcValue::kLong:
        case AcValue::kDouble:
        case AcValue::kDate:
        case AcValue::kPoint:
        case AcValue::k3dPoint:
        case AcValue::kObjectId:
        case AcValue::kBuffer:
        case AcValue::kResbuf:
        case AcValue::kGeneral:
        case AcValue::kColor:
        default:
            // should we ever expect these types in a table?
            return  BadData;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTableCellFromFieldsCell (TextTableCellP tableCell, int row, int column, int content) const
    {
    // just set display string now - will create and convert fields in the post process.
    AcString    displayString = m_acTable->textString (row, column, content);
    if (displayString.isEmpty())
        return  EmptyText;

    return  this->SetTableCellFromTextCell (tableCell, row, column, content, displayString);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTableCellSymbology (TextTableCellP tableCell, TableCellIndexCR cellIndex, int row, int column, bool isTopDown) const
    {
    TableSymbologyValuesPtr     tableSymbology = TableSymbologyValues::Create ();
    if (!tableSymbology.IsValid())
        return  OutOfMemoryError;

    // currently DGN only has outer gride lines
    bool        isVisible = AcDb::kVisible == m_acTable->gridVisibility(row, column, AcDb::kOuterGridLineTypes) ||
                            AcDb::kVisible == m_acTable->gridVisibility(row, column, AcDb::kInnerGridLineTypes);
    tableSymbology->SetLineVisible (isVisible);

    if (isVisible)
        {
        TableSymbologyValuesR   symbRef = *tableSymbology.get ();
        bvector<TableCellIndex> cellList;

        cellList.push_back (cellIndex);

        this->GetTableSymbologyFromGridLine (symbRef, isTopDown ? AcDb::kHorzTop : AcDb::kHorzBottom, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::Top, cellList);

        this->GetTableSymbologyFromGridLine (symbRef, isTopDown ? AcDb::kHorzBottom : AcDb::kHorzTop, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::Bottom, cellList);

        this->GetTableSymbologyFromGridLine (symbRef, AcDb::kVertLeft, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::Left, cellList);

        this->GetTableSymbologyFromGridLine (symbRef, AcDb::kVertRight, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::Right, cellList);

        this->GetTableSymbologyFromGridLine (symbRef, AcDb::kHorzInside, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::InteriorHorizontal, cellList);

        this->GetTableSymbologyFromGridLine (symbRef, AcDb::kVertInside, row, column);
        m_textTable->SetEdgeSymbology (symbRef, TableCellListEdges::InteriorVertical, cellList);
        }
    
    if (!m_acTable->isBackgroundColorNone(row, column))
        {
        UInt32  fillColor = m_toDgnContext->GetDgnColor (m_acTable->backgroundColor(row, column));

        tableSymbology->SetFillVisible (true);
        tableSymbology->SetFillColor (fillColor);
        }

    tableCell->SetFillSymbology (*tableSymbology.get());

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetTableSymbologyFromGridLine (TableSymbologyValuesR tableSymbology, AcDb::GridLineType gridline, int row, int column) const
    {
    UInt32  dgnColor  = m_toDgnContext->GetDgnColor (m_acTable->gridColor(row, column, gridline));
    UInt32  dgnWeight = m_toDgnContext->GetDgnWeight (m_acTable->gridLineWeight(row, column, gridline));
    Int32   dgnStyle  = m_toDgnContext->GetDgnStyle (m_acTable->gridLinetype(row, column, gridline));
    bool    isVisible = AcDb::kVisible == m_acTable->gridVisibility(row, column, gridline);

    tableSymbology.SetLineColor (dgnColor);
    tableSymbology.SetLineWeight (dgnWeight);
    tableSymbology.SetLineStyle (dgnStyle);
    tableSymbology.SetLineVisible (isVisible);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetTableCellMargins (TextTableCellP tableCell, UInt32 row, UInt32 column) const
    {
    TableCellMarginValues   margins;
    double                  scale = m_toDgnContext->GetScaleToDGN ();

    margins.m_top = m_acTable->margin(row, column, AcDb::kCellMarginTop) * scale;
    margins.m_bottom = m_acTable->margin(row, column, AcDb::kCellMarginBottom) * scale;
    margins.m_left = m_acTable->margin(row, column, AcDb::kCellMarginLeft) * scale;
    margins.m_right = m_acTable->margin(row, column, AcDb::kCellMarginRight) * scale;

#ifndef FUTUREWORK_AUTODESK_FIX_MARGINS
    /*----------------------------------------------------------------------------------------------------------
    As of RealDWG 2015, AcDbTable::margin does not consistently return correct right & bottom margins.  Based on
    our tests, top and left margins appear to match what's reported by ACAD as "vertical" and "horizontal" margins.
    We have sought a confirmation from Adesk but so far have failed to obtain anything from them.  For now, just
    use top and left margins as vertical and horizontal margins until either we figure this out or Adesk fixes it.
    ----------------------------------------------------------------------------------------------------------*/
    margins.m_bottom = margins.m_top;
    margins.m_right = margins.m_left;
#endif

    tableCell->SetMargins (margins);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          GetContentColor (int row, int column, int content) const
    {
    // if the content color is BYBLOCK, use table entity color
    AcCmColor       acColor = m_acTable->contentColor (row, column, content);
    if (acColor.isByBlock())
        acColor = m_acTable->color ();

    return m_toDgnContext->GetDgnColor (acColor);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            MergeCells (TableCellIndexR cellIndex, int row, int column, int totalRows, bool isTopDown) const
    {
    // if this is single cell (not merged), proceed as if it is merged
    TableCellRange      mergeRange;
    if (!m_acTable->isMergedCell(row, column, (int*)&mergeRange.minRow, (int*)&mergeRange.maxRow, (int*)&mergeRange.minColumn, (int*)&mergeRange.maxColumn))
        {
        UInt32          dgnRow = isTopDown ? row : totalRows - row - 1;
        cellIndex = TableCellIndex (dgnRow, column);
        return  true;
        }

    // this is a merged cell, but we only merge cells from the "min" cell.  All other cells should be ignored:
    if (row != mergeRange.minRow || column != mergeRange.minColumn)
        return  false;

    // get number of rows and columns to be merged
    UInt32              nRowsMerged = mergeRange.maxRow - mergeRange.minRow + 1;
    UInt32              nColumnsMerged = mergeRange.maxColumn - mergeRange.minColumn + 1;

    if (!isTopDown)
        {
        // table rows flow from bottom to top, swap the row indices
        int             reversedMin = totalRows - mergeRange.maxRow - 1;
        mergeRange.maxRow = totalRows - mergeRange.minRow - 1;
        mergeRange.minRow = reversedMin;
        }

    cellIndex = TableCellIndex (mergeRange.minRow, mergeRange.minColumn);

    if (BSISUCCESS != m_textTable->MergeCells(cellIndex, nRowsMerged, nColumnsMerged))
        DIAGNOSTIC_PRINTF ("Failed to merge cells from rows [%d->%d] and columns[%d %d]!\n", mergeRange.minRow, mergeRange.maxRow, mergeRange.minColumn, mergeRange.maxColumn);

    // we have merged cells starting from the min cell - return to continue processing this cell.
    return  true;
    }
            
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropBlockCellToBlock (UInt32 row, Int32 column, UInt32 content) const
    {
    AcDbObjectId                blockId = m_acTable->blockTableRecordId (row, column, content);
    AcDbBlockTableRecordPointer block (blockId, AcDb::kForRead);
    if (Acad::eOk != block.openStatus())
        return  CantOpenObject;

    AcString        blockName;
    if (Acad::eOk != block->getName(blockName))
        blockName.assign (L"");

    DPoint3d        origin = DPoint3d::FromZero ();
    RealDwgUtil::DPoint3dFromGePoint3d (origin, m_acTable->attachmentPoint(row, column, content));
    m_toDgnContext->GetTransformToDGN().Multiply (origin);

    double          scale = m_acTable->scale (row, column, content);
    RotMatrix       scaleMatrix, rotMatrix;
    scaleMatrix.InitFromScaleFactors (scale, scale, scale);
    rotMatrix.InitFromAxisAndRotationAngle (2, m_acTable->blockRotation(row, column));
    rotMatrix.InitProduct (rotMatrix, scaleMatrix);

    Transform       extrusionTransform;
    if (NULL != RealDwgUtil::GetExtrusionTransform(extrusionTransform, m_acTable->normal(), 0.0))
        {
        RotMatrix   extrusionMatrix;
        extrusionTransform.GetMatrix (extrusionMatrix);
        rotMatrix.InitProduct (extrusionMatrix, rotMatrix);
        }

    EditElementHandle   sharedCellEeh;
    SharedCellHandler::CreateSharedCellElement (sharedCellEeh, NULL, blockName, &origin, &rotMatrix, NULL, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel());
    if (!sharedCellEeh.IsValid())
        return  CantCreateCell;

    SharedCellHandler::SetDefinitionID (sharedCellEeh, m_toDgnContext->ElementIdFromObjectId(blockId));

    MSElementP          scElem = sharedCellEeh.GetElementP ();
    scElem->ehdr.level = m_toDgnContext->GetDgnLevel (m_acTable->layerId());
    scElem->hdr.dhdr.symb.color = m_toDgnContext->GetDgnColor (m_acTable->contentColor(row, column));

    SharedCellHandler::CreateSharedCellComplete (sharedCellEeh);

    m_toDgnContext->LoadElementIntoCache (sharedCellEeh);

    // create and attach tags to the shared cell instance, if any
    if (sharedCellEeh.IsValid())
        this->CreateTags (row, column, content, rotMatrix, sharedCellEeh, block);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateTags (int row, int column, int content, RotMatrixCR matrix, ElementHandleCR target, AcDbBlockTableRecord* block) const
    {
    // extract attribute definitions from the block
    AcDbBlockTableRecordIterator    *iterator = NULL;
    if (Acad::eOk != block->newIterator(iterator))
        return  BlockIteratorFailure;
    
    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId    attdefId;
        if (Acad::eOk != iterator->getEntityId(attdefId))
            continue;

        AcDbAttributeDefinitionPointer  attdef (attdefId, AcDb::kForRead);
        if (Acad::eOk != attdef.openStatus())
            continue;

        ACHAR*          tagValue = NULL;
        if (Acad::eOk == m_acTable->getBlockAttributeValue(row, column, content, attdefId, tagValue) && NULL != tagValue)
            {
            // get the attdef's origin, which will act as the tag offset
            DPoint3d    origin;
            RealDwgUtil::DPoint3dFromGePoint3d (origin, attdef->position());
            matrix.Multiply (origin);
            m_toDgnContext->GetTransformToDGN().Multiply (origin);

            // we should have already created an invisible tag in the shared cell def, from the attribute definition.
            ElementId   tagIdInCelldef = m_toDgnContext->ElementIdFromObjectId (attdefId);

            this->CreateAndAttachTagTo (target, matrix, origin, tagIdInCelldef, tagValue);

            acutDelString (tagValue);
            }
        }

    delete iterator;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateAndAttachTagTo (ElementHandleCR target, RotMatrixCR matrix, DPoint3dCR origin, ElementId tagIdInCelldef, ACHAR const* tagValue) const
    {
    DgnModelP           model = m_toDgnContext->GetModel ();
    if (NULL == model)
        return  BadModel;

    // get the tag element previously saved in the shared cell def and make a copy of it as a new tag instance
    ElementHandle       tagInCelldef(tagIdInCelldef, &m_toDgnContext->GetFile()->GetDictionaryModel());
    if (!tagInCelldef.IsValid())
        return  CantAccessMstnElement;

    TagElementHandler*  tagHandler = dynamic_cast<TagElementHandler*> (&tagInCelldef.GetHandler());
    if (NULL == tagHandler)
        return  BadElementHandler;

    EditElementHandle   newTag (tagInCelldef.GetElementCP(), model);

    if (newTag.IsValid() &&
        BSISUCCESS == TagElementHandler::SetAssociation(newTag, target.GetElementRef(), *model) &&
        BSISUCCESS == TagElementHandler::SetAttributeValue(newTag, DgnTagValue(tagValue)) &&
        BSISUCCESS == TagElementHandler::SetVisibility(newTag, false, true))
        {
        // transform the new tag element
        TransformInfo   transInfo (Transform::From(matrix));
        tagHandler->ApplyTransform (newTag, transInfo);

        // set the offset from block origin to attdef origin
        TagElementHandler::SetOffset (newTag, origin);

        // copy symbology from shared cell element
        MSElementP      newElm = newTag.GetElementP ();
        newElm->ehdr.level = target.GetElementCP()->ehdr.level;
        newElm->hdr.dhdr.symb = target.GetElementCP()->hdr.dhdr.symb;
        newElm->ehdr.uniqueId = 0;

        tagHandler->ValidateElementRange (newTag, true);

        // add the tag to file
        m_toDgnContext->LoadElementIntoCache (newTag);

        return  RealDwgSuccess;
        }

    return  CantCreateTag;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   BreakTextTableToSubTables () const
    {
    AcDb::TableBreakOption          breakOptions = m_acTable->breakOption ();
    if (AcDb::kTableBreakNone == breakOptions || 0 == (AcDb::kTableBreakEnableBreaking & breakOptions))
        {
        m_textTable->SetBreakType (TableBreakType::None);
        return  RealDwgSuccess;
        }
    // ACAD only breaks table by rows, never by columns.
    m_textTable->SetBreakType (TableBreakType::Horizontal);

    TableBreakPosition              breakPosition = TableBreakPosition::Manual;
    switch (m_acTable->breakFlowDirection())
        {
        case AcDb::kTableBreakFlowDownOrUp:
            // DGN text table currently does not flow from bottom to top
            if (AcDb::kBtoT == m_acTable->flowDirection())
                return  CantCreateTextTable;
            breakPosition = TableBreakPosition::Below;
            break;

        case AcDb::kTableBreakFlowLeft:
            breakPosition = TableBreakPosition::Left;
            break;

        case AcDb::kTableBreakFlowRight:
        default:
            breakPosition = TableBreakPosition::Right;
            break;
        }

    m_textTable->SetBreakPosition (breakPosition);

    double      breakLength = m_acTable->breakHeight(0) * m_toDgnContext->GetScaleToDGN();

    m_textTable->SetBreakLength (breakLength);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropTableToBlock (EditElementHandleR outElement) const
    {
    AcDbBlockReference*     acBlockReference = AcDbBlockReference::cast (m_acTable);

    if (NULL != acBlockReference)
        {
        ToDgnExtBlockReference  blockRef2Dgn;
        return  blockRef2Dgn.ToElement (acBlockReference, outElement, *m_toDgnContext);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    // retrieve the text table element from DGN
    EditElementHandle   oldElement(context.ElementIdFromObject(acObject), context.GetModel());
    if (!oldElement.IsValid())
        return  CantAccessMstnElement;

    TextTableHandler*   textTableHandler = dynamic_cast <TextTableHandler*> (&oldElement.GetHandler());
    if (NULL == textTableHandler)
        return  BadElementHandler;

    TextTablePtr        textTable = textTableHandler->GetTextTableForElement (oldElement);
    if (!textTable.IsValid() || NULL == (m_textTable = textTable.get()))
        return  MstnElementUnacceptable;

    m_acTable = AcDbTable::cast (acObject);
    if (NULL == m_acTable)
        return  NullObject;

    m_toDgnContext = &context;

    int         numColumns = m_acTable->numColumns ();
    int         numRows = m_acTable->numRows ();
    int         cellsUpdated = 0;
    bool        isTopDown = AcDb::kTtoB == m_acTable->flowDirection();

    // update fields in each and every cell of the text table
    for (int column = 0; column < numColumns; column++)
        {
        int     row = isTopDown ? 0 : numRows - 1;
        for (; isTopDown ? (row < numRows) : (row > -1); isTopDown ? row++ : row--)
            {
            // invert DGN table row index if DWG table flows from bottom up:
            UInt32      dgnRow = isTopDown ? row : numRows - row - 1;

            if (!m_acTable->isEmpty(row, column))
                {
                switch (m_acTable->cellType(row, column))
                    {
                    case AcDb::kTextCell:               // single text cell
                        if (RealDwgSuccess == this->UpdateFieldsForCell(row, column, dgnRow, 0))
                            cellsUpdated++;
                        break;
                    case AcDb::kMultipleContentCell:    // cell with multiple contents
                        if (RealDwgSuccess == this->UpdateFieldsForMultipleContents(row, column, dgnRow))
                            cellsUpdated++;
                        break;
                    }
                }
            }
        }

    if (0 == cellsUpdated)
        return  CantCreateFields;

    // save the text table back to DGN file
    EditElementHandle   newElement;
    StatusInt           status = TextTableHandler::CreateTextTableElement (newElement, *m_textTable);

    if (BSISUCCESS == status)
        status = newElement.ReplaceInModel (oldElement.GetElementRef());
    
    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   UpdateFieldsForMultipleContents (int row, int column, UInt32 dgnRow) const
    {
    UInt32      numContents = m_acTable->numContents (row, column);

    for (UInt32 content = 0; content < numContents; content++)
        {
        if (AcDb::kCellContentTypeField == m_acTable->contentType(row, column, content))
            this->UpdateFieldsForCell (row, column, dgnRow, content);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   UpdateFieldsForCell (int row, int column, UInt32 dgnRow, int content) const
    {
    AcDbSmartObjectPointer<AcDbField>   acField(m_acTable->fieldId(row, column, content), AcDb::kForRead);
    if (Acad::eOk != acField.openStatus())
        return  CantOpenObject;

    AcString    originalString = m_acTable->textString (row, column, content);
    if (originalString.isEmpty())
        return  EmptyText;

    AcString    fieldString;
    m_toDgnContext->GetFieldsString (fieldString, acField, originalString);

    if (fieldString.isEmpty())
        return  EmptyText;

    TextTableCellP      tableCell = m_textTable->GetCell (TableCellIndex(dgnRow, column));
    if (NULL == tableCell)
        return  BadData;

    TextBlockCP         oldTextBlock = tableCell->GetTextBlock ();
    if (NULL == oldTextBlock)
        return  BadData;

    TextBlockPtr        textBlock = TextBlock::Create (*oldTextBlock);
    if (!textBlock.IsValid())
        return  OutOfMemoryError;

    DwgContextForTextBlock*     tbContext = new DwgContextForTextBlock (m_toDgnContext, NULL, m_acTable);
    if (NULL == tbContext)
        return  OutOfMemoryError;

    textBlock->Clear ();
    textBlock->FromMText ((WCharCP)fieldString.kwszPtr (), *tbContext, m_toDgnContext->GetScaleToDGN());

    delete tbContext;

    if (textBlock->ConvertFieldsFromDwg())
        {
        tableCell->SetTextBlock (*textBlock);
        return  RealDwgSuccess;
        }

    DIAGNOSTIC_PRINTF ("Failed converting DWG field:\"%ls\" in table cell[%d,%d]\n", originalString.kwszPtr(), row, column);

    return  CantCreateFields;
    }

};  // ToDgnExtTable




/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          12/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtTextTable : public ToDwgExtension
{
    mutable TextTableCP                 m_textTable;
    mutable AcDbTable*                  m_acTable;
    mutable ConvertFromDgnContext*      m_fromDgnContext;
    mutable DVec3d                      m_xDirection;
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          inElement,          // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    TextTableHandler*   textTableHandler = dynamic_cast <TextTableHandler*> (&inElement.GetHandler());
    if (NULL == textTableHandler)
        return  BadElementHandler;

    TextTablePtr        textTable = textTableHandler->GetTextTableForElement (inElement);
    if (!textTable.IsValid())
        return  MstnElementUnacceptable;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbTable::desc());

    m_textTable = textTable.get ();
    m_acTable = AcDbTable::cast (acObject);
    m_fromDgnContext = &context;
    
    RealDwgStatus       status = CantCreateTextTable;
    if (NULL != m_acTable)
        status = this->SetAcDbTableFromTextTable (inElement);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbTableFromTextTable (ElementHandleCR inElement) const
    {
    UInt32              numRows = m_textTable->GetRowCount ();
    UInt32              numColumns = m_textTable->GetColumnCount ();
    if (0 == numRows && 0 == numColumns)
        return  MstnElementUnacceptable;
    
    // create DWG table with cells of numRows x numColumns x 1 numContents
    Acad::ErrorStatus   es = m_acTable->setSize (numRows, numColumns);
    if (Acad::eOk != es)
        {
        DIAGNOSTIC_PRINTF ("Error resizing text table by %d x %d\n", numColumns, numRows);
        return  CantCreateTextTable;
        }

    // set the default table style if the table does not already have one:
    this->SetTableStyle ();

    // set default text style
    UInt32              dgnstyleId = (UInt32) m_textTable->GetTextStyleId (TextTableRegion::Body);
    AcDbObjectId        textstyleId = m_fromDgnContext->ExistingObjectIdFromElementId (m_fromDgnContext->GetFileHolder().GetTextStyleIndex()->GetDBHandle(dgnstyleId));
    if (textstyleId.isValid())
        es = m_acTable->setTextStyle (textstyleId, AcDb::kAllRowTypes);

    // DGN text table currently only flows top-down - do this before setting the origin:
    if (Acad::eOk != (es = m_acTable->setFlowDirection(AcDb::kTtoB)))
        return  CantCreateTextTable;

    // set origin
    DPoint3d            origin = m_textTable->GetOrigin ();
    m_fromDgnContext->GetTransformFromDGN().Multiply (origin);
    if (Acad::eOk != (es = m_acTable->setPosition(RealDwgUtil::GePoint3dFromDPoint3d(origin))))
        return  CantCreateTextTable;

    RotMatrix           matrix;
    matrix.InitProduct (m_fromDgnContext->GetLocalTransform(), m_textTable->GetRotation());
    DVec3d              yAxis, zAxis;
    matrix.GetColumns (m_xDirection, yAxis, zAxis);
    m_xDirection.Normalize ();
    zAxis.Normalize ();

    // set normal & x-direction
    if (Acad::eOk != m_acTable->setNormal(RealDwgUtil::GeVector3dFromDVec3d(zAxis)) || Acad::eOk != m_acTable->setDirection(RealDwgUtil::GeVector3dFromDVec3d(m_xDirection)))
        return  CantCreateTextTable;

    /*--------------------------------------------------------------------------------------------------------------
    Work around a fields creation glitch: if a table cell has a text field in it, at the time of adding the table
    entity into database, AcDbTable attempts to create AcDbField objects which gets added into table's *T block table
    record, where the mtext and fields are held.  When we add the table entity, we reset handseed in an attempt to
    persist our element ID in DWG.  That may have prevented AcDbTable from successfully adding more objects into 
    database due to handseed conflicts to existing object ID's in database.  To work around this problem in RealDWG,
    we have to make the AcDbTable entity database resident prior to adding text fields.
    --------------------------------------------------------------------------------------------------------------*/
    if (!m_acTable->objectId().isValid())
        m_fromDgnContext->AddEntityToCurrentBlock (m_acTable, inElement.GetElementId());

    double      scaleToDwg = m_fromDgnContext->GetScaleFromDGN ();

    // set default text height
    double      defaultRowHeight = m_textTable->GetDefaultRowHeight() * scaleToDwg;
    m_acTable->setTextHeight (defaultRowHeight, AcDb::kAllRowTypes);

    // set default cell margins
    TableCellMarginValues   defaultMargins = m_textTable->GetDefaultMargins ();
    this->SetAcTableCellMargins (-1, -1, defaultMargins);

    /*---------------------------------------------------------------------------------------------------------
    We need to set cell tyles, but AcDbTable can only set cell tyle by name, so we have to get cell style names
    from DWG table style.  It appears that ACAD requires Title, Header and Data to be the first 3 cell entries 
    in a table style, so we make an assumption that the name array indices match these default cell styles.
    ---------------------------------------------------------------------------------------------------------*/
    AcStringArray       cellStylenames;
    this->GetCellStyleNames (cellStylenames);

    // before we start processing cells, clear all DWG merges so we can count on those merged from DGN
    if (numRows > 1 || numColumns > 1)
        es = m_acTable->unmergeCells (0, numRows - 1, 0, numColumns - 1);

    for (UInt32 column = 0; column < numColumns; column++)
        {
        TextTableColumnCP   tableColumn = m_textTable->GetColumn (column);
        if (NULL == tableColumn)
            continue;

        double              columnWidth = tableColumn->GetWidth() * scaleToDwg;
        if (Acad::eOk != (es = m_acTable->setColumnWidth(column, columnWidth)))
            continue;

        for (UInt32 row = 0; row < numRows; row++)
            {
            TextTableRowCP  tableRow = m_textTable->GetRow (row);
            if (NULL == tableRow)
                continue;

            if (0 == column)
                {
                // set row height when process the first column
                double      rowHeight = tableRow->GetHeight() * scaleToDwg;
                es = m_acTable->setRowHeight (row, rowHeight);
                if (Acad::eOk != es)
                    DIAGNOSTIC_PRINTF ("Error setting row height of text table cell at (%d, %d) in text table\n", row, column);
                }

            // clear cell overrides
            es = m_acTable->removeAllOverrides (row, column);
            // clear all contents from the cell
            es = m_acTable->deleteContent (row, column);
            // if it is an interior cell from a previous merge, ignore it
            if (m_acTable->isMergedCell(row, column))
                continue;

            // set the cell style
            this->SetCellStyleByName (row, column, tableRow->GetHeaderFooterType(), cellStylenames);

            if (RealDwgSuccess != this->SetAcTextCellFromTextTableCell(row, column))
                DIAGNOSTIC_PRINTF ("Failed setting text table cell at (%d, %d) in text table\n", row, column);
            }
        }

    if (Acad::eOk != (es = m_acTable->generateLayout()) || Acad::eOk != (es = m_acTable->recomputeTableBlock()))
        return  CantCreateTextTable;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcTextCellFromTextTableCell (UInt32 row, UInt32 column) const
    {
    TextTableCellCP     tableCell = m_textTable->GetCell (TableCellIndex(row, column));
    if (NULL == tableCell)
        {
        DIAGNOSTIC_PRINTF ("Null text table cell at (%d, %d) in text table\n", row, column);
        return  MstnElementUnacceptable;
        }

    // if this is the first cell that is merged with others, merge DWG table cells now and we will skip merged cells later.
    this->MergeCellsStartingFrom (row, column, tableCell);

    // set scale
    Acad::ErrorStatus   es = m_acTable->setScale (row, column, 0, 1.0);

    // set alignment - somehow we have to post-add an override as otherwise it does not save correctly as of R2014.
    es = m_acTable->setAlignment (row, column, this->GetAcTableCellAlignment(tableCell));
    es = m_acTable->setOverride (row, column, -1, AcDb::kCellPropAlignment);
    
    // we currently only have text cell type
    RealDwgStatus       status = this->SetAcTextCellFromTextBlock (row, column, tableCell);
    if (RealDwgSuccess != status)
        return  status;

    // get & set margins
    TableCellMarginValues   margins = tableCell->GetMargins ();
    this->SetAcTableCellMargins (row, column, margins);

    // get cell fill symbology & set background fill
    TableSymbologyValuesPtr     fillSymbology = TableSymbologyValues::Create ();
    if (fillSymbology.IsValid())
        {
        tableCell->GetFillSymbology (*fillSymbology.get());

        bool    isFilled = fillSymbology->HasFillVisible() && fillSymbology->GetFillVisible() && fillSymbology->HasFillColor();

        es = m_acTable->setBackgroundColorNone (row, column, !isFilled);

        if (isFilled)
            es = m_acTable->setBackgroundColor (row, column, m_fromDgnContext->GetColorFromDgn(fillSymbology->GetFillColor(), DWG_COLOR_ByBlock));
        }
        
    // get & set grid lines
    this->SetAcGridLineFromCellEdge (row, column, tableCell, TableCellListEdges::Top, AcDb::kHorzTop);
    this->SetAcGridLineFromCellEdge (row, column, tableCell, TableCellListEdges::Bottom, AcDb::kHorzBottom);
    this->SetAcGridLineFromCellEdge (row, column, tableCell, TableCellListEdges::Left, AcDb::kVertLeft);
    this->SetAcGridLineFromCellEdge (row, column, tableCell, TableCellListEdges::Right, AcDb::kVertRight);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetAcTableCellMargins (UInt32 row, UInt32 column, TableCellMarginValues const& margins) const
    {
    double  scaleToDwg = m_fromDgnContext->GetScaleFromDGN ();

    m_acTable->setMargin (row, column, AcDb::kCellMarginTop, margins.m_top * scaleToDwg);
    m_acTable->setMargin (row, column, AcDb::kCellMarginBottom, margins.m_bottom * scaleToDwg);
    m_acTable->setMargin (row, column, AcDb::kCellMarginLeft, margins.m_left * scaleToDwg);
    m_acTable->setMargin (row, column, AcDb::kCellMarginRight, margins.m_right * scaleToDwg);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcTextCellFromTextBlock (UInt32 row, UInt32 column, TextTableCellCP tableCell) const
    {
    // set text cell type
    Acad::ErrorStatus   es = m_acTable->setCellType (row, column, AcDb::kTextCell);

    // get TextBlock for the cell
    TextBlockCP         cellTextBlock = tableCell->GetTextBlock ();
    // if empty, will create an empty TextBlock - TFS 456075:
    if (nullptr == cellTextBlock || cellTextBlock->IsEmpty())
        return this->SetAcTextCellForEmptyCell(row, column, tableCell);

    // create a working TextBlock for write
    TextBlockPtr    textBlock = TextBlock::Create (*cellTextBlock);
    if (!textBlock.IsValid())
        return  OutOfMemoryError;

    textBlock->ConvertToMTextCompatibleTextBlock ();

    // get & set text style
    FileHolder&         fileholder = m_fromDgnContext->GetFileHolder ();
    DgnTextStylePtr     textstyle = textBlock->GetTextStyle ();
    AcDbObjectId        dwgStyleId;
    if (textstyle.IsValid() && 
        (dwgStyleId = fileholder.GetTextStyleIndex()->GetObjectId((Int32)textstyle->GetID(), fileholder.GetDatabase())).isValid())
        es = m_acTable->setTextStyle (row, column, dwgStyleId);

    // make sure rsc fonts eventually get exported
    size_t          numRuns = textBlock->GetRunPropertiesCount ();
    for (size_t i = 0; i < numRuns; i++)
        fileholder.AddFontToInstallTree (&textBlock->GetRunProperties(i)->GetFont());

    double          textHeight = fabs (m_fromDgnContext->GetTextSizeFromTextBlock(NULL, *textBlock));
    textHeight *= m_fromDgnContext->GetScaleFromDGN ();

    RotMatrix       matrix;
    matrix.InitProduct (m_fromDgnContext->GetLocalTransform(), textBlock->GetOrientation());

    DVec3d          xAxis;
    matrix.GetColumn (xAxis, 0);

    AcString        contentString;
    this->GetContentStringFromTextBlock (contentString, textBlock.get(), row, column);

    es = m_acTable->setTextHeight (row, column, -1, textHeight);
    es = m_acTable->setRotation (row, column, -1, m_xDirection.AngleTo(xAxis));

    // calling the other method, setTextString(row, column, nContent, string) resulted in unevaluated fields in the string!
    es = m_acTable->setTextString (row, column, contentString.kwszPtr());

    return  Acad::eOk == es ? RealDwgSuccess : CantCreateText;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/16
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcTextCellForEmptyCell (UInt32 row, UInt32 column, TextTableCellCP tableCell) const
    {
    // set text height
    double      textHeight = tableCell->GetTextSize().y * m_fromDgnContext->GetScaleFromDGN();
    if (Acad::eOk != m_acTable->setTextHeight(row, column, -1, textHeight) || Acad::eOk != m_acTable->setTextString (row, column, L""))
        return  CantCreateText;

    // still have to set text style - the prior default setting does not seem to propogate into data cells!
    UInt32          dgnstyleId = (UInt32) m_textTable->GetTextStyleId (TextTableRegion::Body);
    AcDbObjectId    textstyleId = m_fromDgnContext->ExistingObjectIdFromElementId (m_fromDgnContext->GetFileHolder().GetTextStyleIndex()->GetDBHandle(dgnstyleId));
    if (textstyleId.isValid())
         m_acTable->setTextStyle (row, column, textstyleId);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
AcDb::CellAlignment     GetAcTableCellAlignment (TextTableCellCP dgnTableCell) const
    {
    switch (dgnTableCell->GetAlignment())
        {
        case TableCellAlignment::LeftTop:            return AcDb::kTopLeft;
        case TableCellAlignment::CenterTop:          return AcDb::kTopCenter;
        case TableCellAlignment::RightTop:           return AcDb::kTopRight;
        case TableCellAlignment::LeftMiddle:         return AcDb::kMiddleLeft;
        case TableCellAlignment::CenterMiddle:       return AcDb::kMiddleCenter;
        case TableCellAlignment::RightMiddle:        return AcDb::kMiddleRight;
        case TableCellAlignment::LeftBottom:         return AcDb::kBottomLeft;
        case TableCellAlignment::CenterBottom:       return AcDb::kBottomCenter;
        case TableCellAlignment::RightBottom:        return AcDb::kBottomRight;
        }
    return  AcDb::kTopLeft;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetAcGridLineFromCellEdge (UInt32 row, UInt32 column, TextTableCellCP dgnTableCell, TableCellListEdges dgnCellEdge, AcDb::GridLineType acGridLine) const
    {
    bvector<TableSymbologyValuesPtr>    edgeSymbology;
    dgnTableCell->GetEdgeSymbology (edgeSymbology, dgnCellEdge);

    // set grid edge visibility
    bool        isVisible = true;
    if (edgeSymbology[0]->HasLineVisible())
        isVisible = edgeSymbology[0]->GetLineVisible ();

    Acad::ErrorStatus   es = m_acTable->setGridVisibility (row, column, acGridLine, isVisible ? AcDb::kVisible : AcDb::kInvisible);

    // set grid edge color
    UInt32      edgeColor = COLOR_BYCELL;
    if (edgeSymbology[0]->HasLineColor())
        edgeColor = edgeSymbology[0]->GetLineColor ();

    es = m_acTable->setGridColor (row, column, acGridLine, m_fromDgnContext->GetColorFromDgn(edgeColor, DWG_COLOR_ByBlock));

    // set grid edge weight
    UInt32      edgeWeight = WEIGHT_BYCELL;
    if (edgeSymbology[0]->HasLineWeight())
        edgeWeight = edgeSymbology[0]->GetLineWeight ();

    es = m_acTable->setGridLineWeight (row, column, acGridLine, m_fromDgnContext->GetLineWeightFromDgn(edgeWeight, AcDb::kLnWtByBlock));
    
    // set grid edge linetype
    Int32       edgeLinestyle = STYLE_BYCELL;
    if (edgeSymbology[0]->HasLineStyle())
        edgeLinestyle = edgeSymbology[0]->GetLineStyle ();

    es = m_acTable->setGridLinetype (row, column, acGridLine, m_fromDgnContext->GetLineTypeFromDgn(edgeLinestyle));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetContentStringFromTextBlock (AcString& contentString, TextBlockCP textBlock, UInt32 row, UInt32 column) const
    {
    DwgContextForTextBlock      mapper(NULL, m_fromDgnContext, m_acTable);
    double                      scale = 1.0 / m_fromDgnContext->GetScaleFromDGN();
    bool                        emptyEdf2Space = m_fromDgnContext->GetSettings().ConvertEmptyEDFToSpace ();
    WString                     textString;

    textBlock->ToMText (textString, m_fromDgnContext->GetModel(), &mapper, scale, true, emptyEdf2Space);

    // remove the start color markup with content color override
    int                         colorIndex = DWG_COLOR_ByBlock;
    size_t                      endAt = 0;
    if (1 == swscanf(textString.c_str(), L"\\C%d;", &colorIndex) && WString::npos != (endAt = textString.find(L';', 3)))
        {
        AcCmColor               cmColor;
        cmColor.setColorIndex ((Adesk::UInt16)colorIndex);

        if (Acad::eOk == m_acTable->setContentColor(row, column, -1, cmColor))
            textString = textString.substr (endAt + 1);
        }
    else
        {
        RunPropertiesCR runProperties = textBlock->GetRunPropertiesForAdd();

        if (runProperties.HasColor())
            {
            UInt32 color = runProperties.GetColor();
            m_acTable->setContentColor(row, column, -1, m_fromDgnContext->GetColorFromDgn(color, DWG_COLOR_ByBlock));
            }
        }

    contentString.assign (textString.c_str());

    m_fromDgnContext->CreateFieldsInContentString (contentString);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            MergeCellsStartingFrom (UInt32 row, UInt32 column, TextTableCellCP dgnTableCell) const
    {
    UInt32      mergedRows = dgnTableCell->GetRowSpan ();
    UInt32      mergedColumns = dgnTableCell->GetColumnSpan ();

    if (mergedRows <= 1 && mergedColumns <= 1)
        return  false;

    Acad::ErrorStatus   es = m_acTable->enableMergeAll (row, column, true);
    
    es = m_acTable->mergeCells (row, row + mergedRows - 1, column, column + mergedColumns - 1);

    return  Acad::eOk == es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTableStyle () const
    {
    // if the table already has a style, keep it
    if (m_acTable->tableStyle().isValid())
        return  RealDwgSuccess;

    // otherwise set the first valid style from the table style dictionary
    AcDbDictionary*             tableStyles = NULL;
    if (Acad::eOk != m_fromDgnContext->GetFileHolder().GetDatabase()->getTableStyleDictionary(tableStyles, AcDb::kForRead))
        return  NullObjectId;

    AcDbDictionaryIterator*     iterator = tableStyles->newIterator();

    tableStyles->close ();

    for (; !iterator->done(); iterator->next())
        {
        AcDbTableStylePointer   tableStyle (iterator->objectId(), AcDb::kForRead);
        if (Acad::eOk == tableStyle.openStatus() && Acad::eOk == m_acTable->setTableStyle(tableStyle->objectId()))
            return  RealDwgSuccess;
        }
    
    return  CantOpenObject;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   GetCellStyleNames (AcStringArray& cellStylenames) const
    {
    AcDbTableStylePointer   tablestyle(m_acTable->tableStyle(), AcDb::kForRead);

    if (Acad::eOk != tablestyle.openStatus() || tablestyle->getCellStyles(cellStylenames) < 3)
        {
        // make up a default list
        cellStylenames.removeAll ();
        cellStylenames.append (AcString(L"_DATA"));
        cellStylenames.append (AcString(L"_HEADER"));
        cellStylenames.append (AcString(L"_TITLE"));
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetCellStyleByName (int row, int column, TableHeaderFooterType rowType, const AcStringArray& cellStylenames) const
    {
    TableCellStyle      cellStyle = TableCellStyle_Data;

    switch (rowType)
        {
        case TableHeaderFooterType::Title:   cellStyle = TableCellStyle_Title;   break;
        case TableHeaderFooterType::Header:
        case TableHeaderFooterType::Footer:  cellStyle = TableCellStyle_Header;  break;
        case TableHeaderFooterType::Body:    cellStyle = TableCellStyle_Data;    break;
        }

    Acad::ErrorStatus   es = m_acTable->setCellStyle (row, column, cellStylenames.at(cellStyle).kwszPtr());

    return  Acad::eOk == es ? RealDwgSuccess : BadData;
    }

};  // ToDwgExtTextTable
