#include <gtest/gtest.h>
#include "../src/formats/dwg/DWGExporter.h"
#include "../src/formats/dwg/DWGConverter.h"
#include "../src/formats/dwg/DWGGeometryProcessor.h"
#include "../src/formats/dwg/DWGMaterialManager.h"
#include "../src/core/ExportContext.h"

using namespace IModelExport;

//=======================================================================================
// Test Fixtures
//=======================================================================================

class DWGExporterTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_context = std::make_shared<ExportContext>();
        m_exporter = std::make_unique<DWGExporter>();
        m_exporter->SetExportContext(m_context);
    }

    void TearDown() override {
        m_exporter.reset();
        m_context.reset();
    }

    std::shared_ptr<ExportContext> m_context;
    std::unique_ptr<DWGExporter> m_exporter;
};

class DWGConverterTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_context = std::make_shared<ExportContext>();
        m_converter = std::make_unique<DWGConverter>(m_context);
    }

    void TearDown() override {
        m_converter.reset();
        m_context.reset();
    }

    std::shared_ptr<ExportContext> m_context;
    std::unique_ptr<DWGConverter> m_converter;
};

class DWGGeometryProcessorTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_processor = std::make_unique<DWGGeometryProcessor>();
    }

    void TearDown() override {
        m_processor.reset();
    }

    std::unique_ptr<DWGGeometryProcessor> m_processor;
};

class DWGMaterialManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_materialManager = std::make_unique<DWGMaterialManager>();
    }

    void TearDown() override {
        m_materialManager.reset();
    }

    std::unique_ptr<DWGMaterialManager> m_materialManager;
};

//=======================================================================================
// DWG Exporter Tests
//=======================================================================================

TEST_F(DWGExporterTest, CreateExporter) {
    ASSERT_NE(m_exporter, nullptr);
    EXPECT_EQ(m_exporter->GetFormat(), ExportFormat::DWG);
    EXPECT_EQ(m_exporter->GetFormatName(), "AutoCAD DWG");
    EXPECT_EQ(m_exporter->GetFileExtension(), ".dwg");
}

TEST_F(DWGExporterTest, GetSupportedVersions) {
    auto versions = m_exporter->GetSupportedVersions();
    EXPECT_GE(versions.size(), 3);
    
    bool hasR2018 = std::find(versions.begin(), versions.end(), "R2018") != versions.end();
    bool hasR2021 = std::find(versions.begin(), versions.end(), "R2021") != versions.end();
    bool hasR2024 = std::find(versions.begin(), versions.end(), "R2024") != versions.end();
    
    EXPECT_TRUE(hasR2018);
    EXPECT_TRUE(hasR2021);
    EXPECT_TRUE(hasR2024);
}

TEST_F(DWGExporterTest, ValidateExportOptions) {
    // Test valid options
    DWGExportOptions validOptions;
    validOptions.outputPath = "test.dwg";
    validOptions.version = DWGExportOptions::DWGVersion::R2021;
    validOptions.geometryTolerance = 1e-6;
    
    std::vector<std::string> errors;
    bool isValid = m_exporter->ValidateOptions(validOptions, errors);
    EXPECT_TRUE(isValid);
    EXPECT_TRUE(errors.empty());
    
    // Test invalid options
    DWGExportOptions invalidOptions;
    invalidOptions.outputPath = ""; // Empty path should be invalid
    invalidOptions.geometryTolerance = -1.0; // Negative tolerance should be invalid
    
    errors.clear();
    isValid = m_exporter->ValidateOptions(invalidOptions, errors);
    EXPECT_FALSE(isValid);
    EXPECT_FALSE(errors.empty());
}

TEST_F(DWGExporterTest, CanExportElement) {
    // Test geometric element (should be exportable)
    ElementInfo geometricElement;
    geometricElement.type = ElementType::GeometricElement;
    EXPECT_TRUE(m_exporter->CanExportElement(geometricElement));
    
    // Test spatial element (should be exportable)
    ElementInfo spatialElement;
    spatialElement.type = ElementType::SpatialElement;
    EXPECT_TRUE(m_exporter->CanExportElement(spatialElement));
    
    // Test physical element (should be exportable)
    ElementInfo physicalElement;
    physicalElement.type = ElementType::PhysicalElement;
    EXPECT_TRUE(m_exporter->CanExportElement(physicalElement));
    
    // Test functional element (should not be exportable)
    ElementInfo functionalElement;
    functionalElement.type = ElementType::FunctionalElement;
    EXPECT_FALSE(m_exporter->CanExportElement(functionalElement));
    
    // Test information element (should not be exportable)
    ElementInfo informationElement;
    informationElement.type = ElementType::InformationElement;
    EXPECT_FALSE(m_exporter->CanExportElement(informationElement));
}

TEST_F(DWGExporterTest, SetDWGVersion) {
    EXPECT_TRUE(m_exporter->SetDWGVersion(DWGExportOptions::DWGVersion::R2018));
    EXPECT_TRUE(m_exporter->SetDWGVersion(DWGExportOptions::DWGVersion::R2021));
    EXPECT_TRUE(m_exporter->SetDWGVersion(DWGExportOptions::DWGVersion::R2024));
}

TEST_F(DWGExporterTest, CreateLayer) {
    // Test creating a basic layer
    EXPECT_TRUE(m_exporter->CreateLayer("TestLayer", Color(1.0f, 0.0f, 0.0f, 1.0f)));
    
    // Test creating multiple layers
    EXPECT_TRUE(m_exporter->CreateLayer("Walls", Color(0.8f, 0.8f, 0.8f, 1.0f)));
    EXPECT_TRUE(m_exporter->CreateLayer("Columns", Color(0.6f, 0.6f, 0.8f, 1.0f)));
    EXPECT_TRUE(m_exporter->CreateLayer("Beams", Color(0.9f, 0.7f, 0.5f, 1.0f)));
    
    // Test creating layer with same name (should succeed - layer already exists)
    EXPECT_TRUE(m_exporter->CreateLayer("TestLayer", Color(0.0f, 1.0f, 0.0f, 1.0f)));
}

TEST_F(DWGExporterTest, CreateBlock) {
    // Test creating a basic block
    EXPECT_TRUE(m_exporter->CreateBlock("TestBlock"));
    
    // Test creating multiple blocks
    EXPECT_TRUE(m_exporter->CreateBlock("WallBlock"));
    EXPECT_TRUE(m_exporter->CreateBlock("ColumnBlock"));
    EXPECT_TRUE(m_exporter->CreateBlock("BeamBlock"));
    
    // Test creating block with same name (should succeed - block already exists)
    EXPECT_TRUE(m_exporter->CreateBlock("TestBlock"));
}

//=======================================================================================
// DWG Converter Tests
//=======================================================================================

TEST_F(DWGConverterTest, CreateConverter) {
    ASSERT_NE(m_converter, nullptr);
    EXPECT_NE(m_converter->GetConversionStatistics().totalElements, SIZE_MAX); // Should be initialized
}

TEST_F(DWGConverterTest, ConvertElement) {
    // Create test element
    ElementInfo element;
    element.id = "test_element_001";
    element.type = ElementType::GeometricElement;
    element.classFullName = "TestGeometricElement";
    element.userLabel = "Test Element";
    element.description = "A test element for unit testing";
    
    // Test conversion
    bool success = m_converter->ConvertElement(element);
    EXPECT_TRUE(success);
    
    // Check statistics
    auto stats = m_converter->GetConversionStatistics();
    EXPECT_GT(stats.convertedElements, 0);
}

TEST_F(DWGConverterTest, ConvertElementBatch) {
    // Create test elements
    std::vector<ElementInfo> elements;
    for (int i = 0; i < 10; ++i) {
        ElementInfo element;
        element.id = "batch_element_" + std::to_string(i);
        element.type = ElementType::PhysicalElement;
        element.classFullName = "TestPhysicalElement";
        element.userLabel = "Batch Element " + std::to_string(i);
        elements.push_back(element);
    }
    
    // Test batch conversion
    bool success = m_converter->ConvertElementBatch(elements);
    EXPECT_TRUE(success);
    
    // Check statistics
    auto stats = m_converter->GetConversionStatistics();
    EXPECT_EQ(stats.convertedElements, elements.size());
}

TEST_F(DWGConverterTest, EnableBatchMode) {
    EXPECT_TRUE(m_converter->EnableBatchMode(50));
    EXPECT_TRUE(m_converter->EnableBatchMode(100));
    EXPECT_TRUE(m_converter->EnableBatchMode(1));
}

TEST_F(DWGConverterTest, EnableParallelProcessing) {
    EXPECT_TRUE(m_converter->EnableParallelProcessing(1));
    EXPECT_TRUE(m_converter->EnableParallelProcessing(4));
    EXPECT_TRUE(m_converter->EnableParallelProcessing(0)); // Should use default
}

TEST_F(DWGConverterTest, ConversionStatistics) {
    // Get initial statistics
    auto initialStats = m_converter->GetConversionStatistics();
    EXPECT_EQ(initialStats.totalElements, 0);
    EXPECT_EQ(initialStats.convertedElements, 0);
    EXPECT_EQ(initialStats.errorElements, 0);
    
    // Convert some elements
    ElementInfo element;
    element.id = "stats_test_element";
    element.type = ElementType::GeometricElement;
    element.classFullName = "StatsTestElement";
    
    m_converter->ConvertElement(element);
    
    // Check updated statistics
    auto updatedStats = m_converter->GetConversionStatistics();
    EXPECT_GT(updatedStats.convertedElements, initialStats.convertedElements);
}

//=======================================================================================
// DWG Geometry Processor Tests
//=======================================================================================

TEST_F(DWGGeometryProcessorTest, CreateProcessor) {
    ASSERT_NE(m_processor, nullptr);
}

TEST_F(DWGGeometryProcessorTest, TransformGeometry) {
    // Create test geometry
    GeometryData geometry;
    geometry.type = GeometryData::Type::Line;
    geometry.points = {Point3d(0, 0, 0), Point3d(1, 1, 1)};
    
    // Create transformation
    Transform3d transform = Transform3d::Translation(Vector3d(10, 20, 30));
    
    // Test transformation
    bool success = m_processor->TransformGeometry(geometry, transform);
    EXPECT_TRUE(success);
    
    // Check transformed points
    EXPECT_NEAR(geometry.points[0].x, 10.0, 1e-6);
    EXPECT_NEAR(geometry.points[0].y, 20.0, 1e-6);
    EXPECT_NEAR(geometry.points[0].z, 30.0, 1e-6);
    
    EXPECT_NEAR(geometry.points[1].x, 11.0, 1e-6);
    EXPECT_NEAR(geometry.points[1].y, 21.0, 1e-6);
    EXPECT_NEAR(geometry.points[1].z, 31.0, 1e-6);
}

TEST_F(DWGGeometryProcessorTest, ValidateForDWG) {
    // Test valid geometry
    GeometryData validGeometry;
    validGeometry.type = GeometryData::Type::Line;
    validGeometry.points = {Point3d(0, 0, 0), Point3d(1, 1, 1)};
    
    std::vector<std::string> issues;
    bool isValid = m_processor->ValidateForDWG(validGeometry, issues);
    EXPECT_TRUE(isValid);
    EXPECT_TRUE(issues.empty());
    
    // Test invalid geometry (no points)
    GeometryData invalidGeometry;
    invalidGeometry.type = GeometryData::Type::Line;
    invalidGeometry.points.clear();
    
    issues.clear();
    isValid = m_processor->ValidateForDWG(invalidGeometry, issues);
    EXPECT_FALSE(isValid);
    EXPECT_FALSE(issues.empty());
}

TEST_F(DWGGeometryProcessorTest, OptimizeForDWG) {
    // Create geometry with duplicate points
    GeometryData geometry;
    geometry.type = GeometryData::Type::Polyline;
    geometry.points = {
        Point3d(0, 0, 0),
        Point3d(0, 0, 0), // Duplicate
        Point3d(1, 1, 1),
        Point3d(1, 1, 1), // Duplicate
        Point3d(2, 2, 2)
    };
    
    size_t originalPointCount = geometry.points.size();
    
    // Test optimization
    bool success = m_processor->OptimizeForDWG(geometry);
    EXPECT_TRUE(success);
    
    // Should have fewer points after optimization
    EXPECT_LT(geometry.points.size(), originalPointCount);
}

//=======================================================================================
// DWG Material Manager Tests
//=======================================================================================

TEST_F(DWGMaterialManagerTest, CreateMaterialManager) {
    ASSERT_NE(m_materialManager, nullptr);
}

TEST_F(DWGMaterialManagerTest, RegisterDWGMaterial) {
    // Create test material
    Material material;
    material.name = "Test_Material";
    material.diffuseColor = Color(1.0f, 0.0f, 0.0f, 1.0f);
    material.specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    material.shininess = 0.5f;
    material.transparency = 0.0f;
    
    // Test registration
    std::string materialId;
    bool success = m_materialManager->RegisterDWGMaterial(material, materialId);
    EXPECT_TRUE(success);
    EXPECT_FALSE(materialId.empty());
    
    // Test registering same material again
    std::string materialId2;
    success = m_materialManager->RegisterDWGMaterial(material, materialId2);
    EXPECT_TRUE(success);
    // Should return same ID for same material
    EXPECT_EQ(materialId, materialId2);
}

TEST_F(DWGMaterialManagerTest, SetMaterialCategory) {
    // Create and register material
    Material material;
    material.name = "Concrete_Material";
    material.diffuseColor = Color(0.7f, 0.7f, 0.7f, 1.0f);
    
    std::string materialId;
    bool success = m_materialManager->RegisterDWGMaterial(material, materialId);
    ASSERT_TRUE(success);
    
    // Test setting category
    success = m_materialManager->SetMaterialCategory(materialId, DWGMaterialManager::MaterialCategory::Concrete);
    EXPECT_TRUE(success);
    
    // Test getting category
    auto category = m_materialManager->GetMaterialCategory(materialId);
    EXPECT_EQ(category, DWGMaterialManager::MaterialCategory::Concrete);
}

TEST_F(DWGMaterialManagerTest, MaterialTags) {
    // Create and register material
    Material material;
    material.name = "Steel_Material";
    material.diffuseColor = Color(0.6f, 0.6f, 0.7f, 1.0f);
    
    std::string materialId;
    bool success = m_materialManager->RegisterDWGMaterial(material, materialId);
    ASSERT_TRUE(success);
    
    // Test adding tags
    EXPECT_TRUE(m_materialManager->AddMaterialTag(materialId, "structural"));
    EXPECT_TRUE(m_materialManager->AddMaterialTag(materialId, "metal"));
    EXPECT_TRUE(m_materialManager->AddMaterialTag(materialId, "durable"));
    
    // Test getting tags
    auto tags = m_materialManager->GetMaterialTags(materialId);
    EXPECT_EQ(tags.size(), 3);
    EXPECT_TRUE(std::find(tags.begin(), tags.end(), "structural") != tags.end());
    EXPECT_TRUE(std::find(tags.begin(), tags.end(), "metal") != tags.end());
    EXPECT_TRUE(std::find(tags.begin(), tags.end(), "durable") != tags.end());
    
    // Test finding materials by tag
    auto materialsWithStructuralTag = m_materialManager->FindMaterialsByTag("structural");
    EXPECT_FALSE(materialsWithStructuralTag.empty());
    EXPECT_TRUE(std::find(materialsWithStructuralTag.begin(), materialsWithStructuralTag.end(), materialId) != materialsWithStructuralTag.end());
}

TEST_F(DWGMaterialManagerTest, ValidateMaterial) {
    // Test valid material
    Material validMaterial;
    validMaterial.name = "Valid_Material";
    validMaterial.diffuseColor = Color(0.5f, 0.5f, 0.5f, 1.0f);
    validMaterial.shininess = 0.5f;
    validMaterial.transparency = 0.0f;
    
    std::vector<std::string> issues;
    bool isValid = m_materialManager->ValidateMaterial(validMaterial, issues);
    EXPECT_TRUE(isValid);
    EXPECT_TRUE(issues.empty());
    
    // Test invalid material (empty name)
    Material invalidMaterial;
    invalidMaterial.name = ""; // Empty name should be invalid
    invalidMaterial.diffuseColor = Color(0.5f, 0.5f, 0.5f, 1.0f);
    
    issues.clear();
    isValid = m_materialManager->ValidateMaterial(invalidMaterial, issues);
    EXPECT_FALSE(isValid);
    EXPECT_FALSE(issues.empty());
}

TEST_F(DWGMaterialManagerTest, MaterialStatistics) {
    // Get initial statistics
    auto initialStats = m_materialManager->GetMaterialStatistics();
    EXPECT_EQ(initialStats.totalMaterials, 0);
    
    // Register some materials
    std::vector<Material> materials;
    for (int i = 0; i < 5; ++i) {
        Material material;
        material.name = "Material_" + std::to_string(i);
        material.diffuseColor = Color(0.5f, 0.5f, 0.5f, 1.0f);
        materials.push_back(material);
        
        std::string materialId;
        m_materialManager->RegisterDWGMaterial(material, materialId);
    }
    
    // Check updated statistics
    auto updatedStats = m_materialManager->GetMaterialStatistics();
    EXPECT_EQ(updatedStats.totalMaterials, 5);
}

//=======================================================================================
// Integration Tests
//=======================================================================================

TEST_F(DWGExporterTest, EndToEndExportTest) {
    // Create export options
    DWGExportOptions options;
    options.outputPath = "test_output.dwg";
    options.version = DWGExportOptions::DWGVersion::R2021;
    options.levelOfDetail = ExportLOD::Medium;
    options.includeMetadata = true;
    options.geometryTolerance = 1e-6;
    
    // Validate options
    std::vector<std::string> errors;
    bool optionsValid = m_exporter->ValidateOptions(options, errors);
    EXPECT_TRUE(optionsValid);
    
    // Initialize export
    bool initialized = m_exporter->InitializeExport(options);
    EXPECT_TRUE(initialized);
    
    // Create some geometry
    EXPECT_TRUE(m_exporter->CreateLayer("TestLayer", Color(1.0f, 0.0f, 0.0f, 1.0f)));
    EXPECT_TRUE(m_exporter->AddLine(Point3d(0, 0, 0), Point3d(100, 100, 0), "TestLayer"));
    EXPECT_TRUE(m_exporter->AddCircle(Point3d(50, 50, 0), 25, "TestLayer"));
    
    // Finalize export
    bool finalized = m_exporter->FinalizeExport();
    EXPECT_TRUE(finalized);
}

//=======================================================================================
// Performance Tests
//=======================================================================================

TEST_F(DWGConverterTest, PerformanceTest) {
    // Test conversion performance with a large number of elements
    const size_t numElements = 1000;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::vector<ElementInfo> elements;
    for (size_t i = 0; i < numElements; ++i) {
        ElementInfo element;
        element.id = "perf_element_" + std::to_string(i);
        element.type = ElementType::GeometricElement;
        element.classFullName = "PerformanceTestElement";
        element.userLabel = "Performance Element " + std::to_string(i);
        elements.push_back(element);
    }
    
    bool success = m_converter->ConvertElementBatch(elements);
    EXPECT_TRUE(success);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    // Verify all elements were processed
    auto stats = m_converter->GetConversionStatistics();
    EXPECT_EQ(stats.convertedElements, numElements);
    
    // Performance should be reasonable (less than 10 seconds for 1000 elements)
    EXPECT_LT(duration.count(), 10000);
    
    std::cout << "Converted " << numElements << " elements in " << duration.count() << " ms" << std::endl;
    std::cout << "Average: " << (static_cast<double>(duration.count()) / numElements) << " ms per element" << std::endl;
}

//=======================================================================================
// Error Handling Tests
//=======================================================================================

TEST_F(DWGExporterTest, ErrorHandlingTest) {
    // Test with invalid export options
    DWGExportOptions invalidOptions;
    invalidOptions.outputPath = ""; // Invalid empty path
    invalidOptions.geometryTolerance = -1.0; // Invalid negative tolerance
    
    std::vector<std::string> errors;
    bool isValid = m_exporter->ValidateOptions(invalidOptions, errors);
    EXPECT_FALSE(isValid);
    EXPECT_FALSE(errors.empty());
    
    // Should contain specific error messages
    bool hasPathError = false;
    bool hasToleranceError = false;
    
    for (const auto& error : errors) {
        if (error.find("path") != std::string::npos) {
            hasPathError = true;
        }
        if (error.find("tolerance") != std::string::npos) {
            hasToleranceError = true;
        }
    }
    
    EXPECT_TRUE(hasPathError);
    EXPECT_TRUE(hasToleranceError);
}

//=======================================================================================
// Main Test Runner
//=======================================================================================

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "Running DWG Export Framework Tests..." << std::endl;
    std::cout << "=====================================" << std::endl;
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "\nDWG test execution completed." << std::endl;
    
    return result;
}
