#include "../src/formats/dwg/DWGExporter.h"
#include "../src/formats/dwg/DWGConverter.h"
#include "../src/formats/dwg/DWGGeometryProcessor.h"
#include "../src/formats/dwg/DWGMaterialManager.h"
#include "../include/IModelExportManager.h"
#include "../include/ExportTypes.h"

#include <iostream>
#include <filesystem>
#include <chrono>
#include <iomanip>
#include <fstream>

using namespace IModelExport;

//=======================================================================================
// Advanced DWG Export Progress Monitor
//=======================================================================================

class DWGProgressMonitor {
private:
    std::chrono::steady_clock::time_point m_startTime;
    std::chrono::steady_clock::time_point m_lastUpdate;
    size_t m_totalElements = 0;
    size_t m_processedElements = 0;
    
public:
    void Start(size_t totalElements) {
        m_startTime = m_lastUpdate = std::chrono::steady_clock::now();
        m_totalElements = totalElements;
        m_processedElements = 0;
        
        std::cout << "\n┌─────────────────────────────────────────────────────────────┐" << std::endl;
        std::cout << "│                    DWG Export Progress                     │" << std::endl;
        std::cout << "├─────────────────────────────────────────────────────────────┤" << std::endl;
        std::cout << "│ Total Elements: " << std::setw(10) << totalElements << "                              │" << std::endl;
        std::cout << "└─────────────────────────────────────────────────────────────┘" << std::endl;
    }
    
    bool UpdateProgress(const ExportProgress& progress) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastUpdate);
        
        // Update every 500ms
        if (elapsed.count() > 500 || progress.percentage >= 100.0) {
            auto totalElapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_startTime);
            
            // Calculate ETA
            double eta = 0.0;
            if (progress.percentage > 0.0) {
                eta = (totalElapsed.count() * (100.0 - progress.percentage)) / progress.percentage;
            }
            
            // Calculate speed
            double speed = 0.0;
            if (totalElapsed.count() > 0) {
                speed = static_cast<double>(progress.processedElements) / totalElapsed.count();
            }
            
            // Draw progress bar
            std::cout << "\r";
            std::cout << "Progress: [";
            
            int barWidth = 30;
            int pos = static_cast<int>(barWidth * progress.percentage / 100.0);
            for (int i = 0; i < barWidth; ++i) {
                if (i < pos) std::cout << "█";
                else if (i == pos) std::cout << "▌";
                else std::cout << " ";
            }
            
            std::cout << "] " << std::fixed << std::setprecision(1) << progress.percentage << "% ";
            std::cout << "(" << progress.processedElements << "/" << progress.totalElements << ") ";
            std::cout << "Speed: " << std::fixed << std::setprecision(1) << speed << " elem/s ";
            std::cout << "ETA: " << std::setw(3) << static_cast<int>(eta) << "s ";
            std::cout << "| " << progress.currentOperation.substr(0, 20);
            std::cout.flush();
            
            m_lastUpdate = now;
            m_processedElements = progress.processedElements;
        }
        
        return true; // Continue export
    }
    
    void Complete(const ExportResult& result) {
        auto endTime = std::chrono::steady_clock::now();
        auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(endTime - m_startTime);
        
        std::cout << std::endl;
        std::cout << "┌─────────────────────────────────────────────────────────────┐" << std::endl;
        std::cout << "│                    DWG Export Completed                    │" << std::endl;
        std::cout << "├─────────────────────────────────────────────────────────────┤" << std::endl;
        std::cout << "│ Status: " << std::setw(12) << ToString(result.status) << "                              │" << std::endl;
        std::cout << "│ Total Time: " << std::setw(8) << totalTime.count() << " seconds                        │" << std::endl;
        std::cout << "│ Elements Exported: " << std::setw(6) << result.exportedElements << "                           │" << std::endl;
        std::cout << "│ Average Speed: " << std::setw(6) << std::fixed << std::setprecision(1) 
                  << (static_cast<double>(result.exportedElements) / totalTime.count()) << " elements/second           │" << std::endl;
        std::cout << "│ Output File: " << std::setw(40) << result.outputFile.substr(0, 40) << " │" << std::endl;
        std::cout << "└─────────────────────────────────────────────────────────────┘" << std::endl;
        
        if (!result.warnings.empty()) {
            std::cout << "\nWarnings (" << result.warnings.size() << "):" << std::endl;
            for (const auto& warning : result.warnings) {
                std::cout << "  ⚠ " << warning << std::endl;
            }
        }
        
        if (!result.errors.empty()) {
            std::cout << "\nErrors (" << result.errors.size() << "):" << std::endl;
            for (const auto& error : result.errors) {
                std::cout << "  ✗ " << error << std::endl;
            }
        }
    }
};

//=======================================================================================
// Basic DWG Export Example
//=======================================================================================

void BasicDWGExportExample(const std::string& outputDir) {
    std::cout << "\n=== Basic DWG Export Example ===" << std::endl;
    
    try {
        // Create DWG exporter
        auto exporter = std::make_unique<DWGExporter>();
        
        // Create export context
        auto context = std::make_shared<ExportContext>();
        exporter->SetExportContext(context);
        
        // Configure export options
        DWGExportOptions options;
        options.outputPath = outputDir + "/basic_model.dwg";
        options.version = DWGExportOptions::DWGVersion::R2021;
        options.levelOfDetail = ExportLOD::Medium;
        options.includeMetadata = true;
        options.preserveLayers = true;
        options.exportAsBlocks = false;
        options.geometryTolerance = 1e-6;
        
        // Validate options
        std::vector<std::string> errors;
        if (!exporter->ValidateOptions(options, errors)) {
            std::cout << "Export options validation failed:" << std::endl;
            for (const auto& error : errors) {
                std::cout << "  - " << error << std::endl;
            }
            return;
        }
        
        // Initialize export
        if (!exporter->InitializeExport(options)) {
            std::cout << "Failed to initialize DWG export" << std::endl;
            return;
        }
        
        // Create some basic geometry
        std::cout << "Creating basic geometry..." << std::endl;
        
        // Create layers
        exporter->CreateLayer("Walls", Color(0.8f, 0.8f, 0.8f, 1.0f));
        exporter->CreateLayer("Columns", Color(0.6f, 0.6f, 0.8f, 1.0f));
        exporter->CreateLayer("Beams", Color(0.9f, 0.7f, 0.5f, 1.0f));
        exporter->CreateLayer("Text", Color(1.0f, 1.0f, 0.0f, 1.0f));
        
        // Add walls (rectangles)
        std::vector<Point3d> wall1 = {
            Point3d(0, 0, 0), Point3d(5000, 0, 0), 
            Point3d(5000, 200, 0), Point3d(0, 200, 0)
        };
        exporter->AddPolyline(wall1, true, "Walls");
        
        std::vector<Point3d> wall2 = {
            Point3d(0, 0, 0), Point3d(0, 3000, 0), 
            Point3d(200, 3000, 0), Point3d(200, 0, 0)
        };
        exporter->AddPolyline(wall2, true, "Walls");
        
        // Add columns (circles)
        exporter->AddCircle(Point3d(1000, 1000, 0), 150, "Columns");
        exporter->AddCircle(Point3d(4000, 1000, 0), 150, "Columns");
        exporter->AddCircle(Point3d(1000, 2500, 0), 150, "Columns");
        exporter->AddCircle(Point3d(4000, 2500, 0), 150, "Columns");
        
        // Add beams (lines)
        exporter->AddLine(Point3d(1000, 1000, 3000), Point3d(4000, 1000, 3000), "Beams");
        exporter->AddLine(Point3d(1000, 2500, 3000), Point3d(4000, 2500, 3000), "Beams");
        exporter->AddLine(Point3d(1000, 1000, 3000), Point3d(1000, 2500, 3000), "Beams");
        exporter->AddLine(Point3d(4000, 1000, 3000), Point3d(4000, 2500, 3000), "Beams");
        
        // Add text labels
        exporter->AddText(Point3d(2500, 1750, 0), "Building Plan", 200, "Text");
        exporter->AddText(Point3d(500, 500, 0), "Wall", 100, "Text");
        exporter->AddText(Point3d(1000, 800, 0), "Column", 100, "Text");
        
        // Finalize export
        if (!exporter->FinalizeExport()) {
            std::cout << "Failed to finalize DWG export" << std::endl;
            return;
        }
        
        std::cout << "Basic DWG export completed successfully!" << std::endl;
        std::cout << "Output file: " << options.outputPath << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Basic DWG export failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Advanced DWG Conversion Example
//=======================================================================================

void AdvancedDWGConversionExample(const std::string& outputDir) {
    std::cout << "\n=== Advanced DWG Conversion Example ===" << std::endl;
    
    try {
        // Create export context
        auto context = std::make_shared<ExportContext>();
        
        // Create DWG converter
        auto converter = std::make_unique<DWGConverter>(context);
        
        // Configure conversion options
        DWGExportOptions options;
        options.outputPath = outputDir + "/advanced_model.dwg";
        options.version = DWGExportOptions::DWGVersion::R2021;
        options.levelOfDetail = ExportLOD::High;
        options.includeMetadata = true;
        options.preserveLayers = true;
        options.exportAsBlocks = true;
        options.exportDimensions = true;
        options.geometryTolerance = 1e-6;
        options.enableMultiThreading = true;
        
        // Enable advanced features
        converter->EnableParallelProcessing(4);
        converter->EnableBatchMode(50);
        
        // Create sample building elements
        std::vector<ElementInfo> elements;
        
        // Create wall elements
        for (int i = 0; i < 10; ++i) {
            ElementInfo wall;
            wall.id = "wall_" + std::to_string(i);
            wall.type = ElementType::PhysicalElement;
            wall.classFullName = "Wall";
            wall.userLabel = "Wall " + std::to_string(i + 1);
            wall.description = "Structural wall element";
            wall.properties["Material"] = "Concrete";
            wall.properties["Thickness"] = "200";
            wall.properties["Height"] = "3000";
            elements.push_back(wall);
        }
        
        // Create column elements
        for (int i = 0; i < 8; ++i) {
            ElementInfo column;
            column.id = "column_" + std::to_string(i);
            column.type = ElementType::PhysicalElement;
            column.classFullName = "Column";
            column.userLabel = "Column " + std::to_string(i + 1);
            column.description = "Structural column element";
            column.properties["Material"] = "Steel";
            column.properties["Profile"] = "HEB300";
            column.properties["Height"] = "3000";
            elements.push_back(column);
        }
        
        // Create beam elements
        for (int i = 0; i < 12; ++i) {
            ElementInfo beam;
            beam.id = "beam_" + std::to_string(i);
            beam.type = ElementType::PhysicalElement;
            beam.classFullName = "Beam";
            beam.userLabel = "Beam " + std::to_string(i + 1);
            beam.description = "Structural beam element";
            beam.properties["Material"] = "Steel";
            beam.properties["Profile"] = "IPE200";
            beam.properties["Length"] = "5000";
            elements.push_back(beam);
        }
        
        std::cout << "Converting " << elements.size() << " elements..." << std::endl;
        
        // Setup progress monitoring
        DWGProgressMonitor monitor;
        monitor.Start(elements.size());
        
        // Mock iModel (in real implementation, this would be actual iModel data)
        // IModelDb imodel = LoadIModel("sample.bim");
        
        // Perform conversion
        bool success = converter->ConvertElementBatch(elements);
        
        if (success) {
            // Get conversion statistics
            auto stats = converter->GetConversionStatistics();
            
            std::cout << "\nAdvanced DWG conversion completed!" << std::endl;
            std::cout << "Conversion Statistics:" << std::endl;
            std::cout << "  Total Elements: " << stats.totalElements << std::endl;
            std::cout << "  Converted: " << stats.convertedElements << std::endl;
            std::cout << "  Skipped: " << stats.skippedElements << std::endl;
            std::cout << "  Errors: " << stats.errorElements << std::endl;
            std::cout << "  Total Time: " << std::fixed << std::setprecision(2) << stats.totalConversionTime << " seconds" << std::endl;
            std::cout << "  Average Time per Element: " << std::fixed << std::setprecision(4) << stats.averageElementTime << " seconds" << std::endl;
            
            // Generate detailed report
            std::string report = converter->GenerateConversionReport();
            std::string reportPath = outputDir + "/conversion_report.txt";
            std::ofstream reportFile(reportPath);
            reportFile << report;
            reportFile.close();
            
            std::cout << "Detailed report saved to: " << reportPath << std::endl;
            
        } else {
            std::cout << "Advanced DWG conversion failed!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Advanced DWG conversion failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// DWG Material Management Example
//=======================================================================================

void DWGMaterialExample(const std::string& outputDir) {
    std::cout << "\n=== DWG Material Management Example ===" << std::endl;
    
    try {
        // Create material manager
        auto materialManager = std::make_unique<DWGMaterialManager>();
        
        // Create sample materials
        std::vector<Material> materials;
        
        // Concrete material
        Material concrete;
        concrete.name = "Concrete_C30";
        concrete.diffuseColor = Color(0.7f, 0.7f, 0.7f, 1.0f);
        concrete.specularColor = Color(0.2f, 0.2f, 0.2f, 1.0f);
        concrete.shininess = 0.1f;
        concrete.transparency = 0.0f;
        materials.push_back(concrete);
        
        // Steel material
        Material steel;
        steel.name = "Steel_S355";
        steel.diffuseColor = Color(0.6f, 0.6f, 0.7f, 1.0f);
        steel.specularColor = Color(0.8f, 0.8f, 0.9f, 1.0f);
        steel.shininess = 0.8f;
        steel.transparency = 0.0f;
        materials.push_back(steel);
        
        // Glass material
        Material glass;
        glass.name = "Glass_Clear";
        glass.diffuseColor = Color(0.9f, 0.9f, 1.0f, 0.3f);
        glass.specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
        glass.shininess = 0.9f;
        glass.transparency = 0.7f;
        materials.push_back(glass);
        
        // Wood material
        Material wood;
        wood.name = "Wood_Oak";
        wood.diffuseColor = Color(0.8f, 0.6f, 0.4f, 1.0f);
        wood.specularColor = Color(0.3f, 0.2f, 0.1f, 1.0f);
        wood.shininess = 0.3f;
        wood.transparency = 0.0f;
        wood.diffuseTexture = "textures/oak_diffuse.jpg";
        wood.normalTexture = "textures/oak_normal.jpg";
        materials.push_back(wood);
        
        std::cout << "Registering " << materials.size() << " materials..." << std::endl;
        
        // Register materials
        std::vector<std::string> materialIds;
        for (const auto& material : materials) {
            std::string materialId;
            if (materialManager->RegisterDWGMaterial(material, materialId)) {
                materialIds.push_back(materialId);
                std::cout << "  ✓ Registered: " << material.name << " (ID: " << materialId << ")" << std::endl;
                
                // Set material category
                if (material.name.find("Concrete") != std::string::npos) {
                    materialManager->SetMaterialCategory(materialId, DWGMaterialManager::MaterialCategory::Concrete);
                } else if (material.name.find("Steel") != std::string::npos) {
                    materialManager->SetMaterialCategory(materialId, DWGMaterialManager::MaterialCategory::Metal);
                } else if (material.name.find("Glass") != std::string::npos) {
                    materialManager->SetMaterialCategory(materialId, DWGMaterialManager::MaterialCategory::Glass);
                } else if (material.name.find("Wood") != std::string::npos) {
                    materialManager->SetMaterialCategory(materialId, DWGMaterialManager::MaterialCategory::Wood);
                }
                
                // Add tags
                materialManager->AddMaterialTag(materialId, "structural");
                if (material.transparency > 0.0f) {
                    materialManager->AddMaterialTag(materialId, "transparent");
                }
                if (!material.diffuseTexture.empty()) {
                    materialManager->AddMaterialTag(materialId, "textured");
                }
                
            } else {
                std::cout << "  ✗ Failed to register: " << material.name << std::endl;
            }
        }
        
        // Get material statistics
        auto stats = materialManager->GetMaterialStatistics();
        std::cout << "\nMaterial Statistics:" << std::endl;
        std::cout << "  Total Materials: " << stats.totalMaterials << std::endl;
        std::cout << "  Total Textures: " << stats.totalTextures << std::endl;
        std::cout << "  Materials with Textures: " << stats.materialsWithTextures << std::endl;
        std::cout << "  Materials with Transparency: " << stats.materialsWithTransparency << std::endl;
        std::cout << "  Average Complexity: " << std::fixed << std::setprecision(2) << stats.averageComplexity << std::endl;
        
        // Generate material report
        std::string report = materialManager->GenerateMaterialReport();
        std::string reportPath = outputDir + "/material_report.txt";
        std::ofstream reportFile(reportPath);
        reportFile << report;
        reportFile.close();
        
        std::cout << "Material report saved to: " << reportPath << std::endl;
        
        // Save material library
        std::string libraryPath = outputDir + "/material_library.xml";
        if (materialManager->SaveMaterialLibrary(libraryPath)) {
            std::cout << "Material library saved to: " << libraryPath << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "DWG material example failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Performance Benchmark Example
//=======================================================================================

void DWGPerformanceBenchmark(const std::string& outputDir) {
    std::cout << "\n=== DWG Performance Benchmark ===" << std::endl;
    
    try {
        // Test different configurations
        std::vector<std::pair<std::string, DWGExportOptions>> configs = {
            {"Single_Thread_Low", DWGExportOptions()},
            {"Single_Thread_High", DWGExportOptions()},
            {"Multi_Thread_Low", DWGExportOptions()},
            {"Multi_Thread_High", DWGExportOptions()}
        };
        
        // Configure each test
        configs[0].second.enableMultiThreading = false;
        configs[0].second.levelOfDetail = ExportLOD::Low;
        
        configs[1].second.enableMultiThreading = false;
        configs[1].second.levelOfDetail = ExportLOD::High;
        
        configs[2].second.enableMultiThreading = true;
        configs[2].second.levelOfDetail = ExportLOD::Low;
        
        configs[3].second.enableMultiThreading = true;
        configs[3].second.levelOfDetail = ExportLOD::High;
        
        std::cout << "\nRunning DWG performance benchmarks..." << std::endl;
        std::cout << "┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐" << std::endl;
        std::cout << "│ Configuration   │ Time (s)    │ Elements    │ Speed (e/s) │ Memory (MB) │" << std::endl;
        std::cout << "├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤" << std::endl;
        
        for (auto& config : configs) {
            config.second.outputPath = outputDir + "/benchmark_" + config.first + ".dwg";
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // Create export context and converter
            auto context = std::make_shared<ExportContext>();
            auto converter = std::make_unique<DWGConverter>(context);
            
            // Create test elements
            std::vector<ElementInfo> elements;
            for (int i = 0; i < 1000; ++i) {
                ElementInfo element;
                element.id = "benchmark_element_" + std::to_string(i);
                element.type = static_cast<ElementType>(i % 3);
                element.classFullName = "BenchmarkElement";
                element.userLabel = "Benchmark Element " + std::to_string(i);
                elements.push_back(element);
            }
            
            // Perform conversion
            bool success = converter->ConvertElementBatch(elements);
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration<double>(endTime - startTime).count();
            
            // Get results
            size_t elementCount = elements.size();
            double speed = success ? (elementCount / duration) : 0.0;
            size_t memory = 50 + (rand() % 100); // Mock memory usage
            
            std::cout << "│ " << std::setw(15) << config.first << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(2) << duration << " │ "
                      << std::setw(11) << elementCount << " │ "
                      << std::setw(11) << std::fixed << std::setprecision(1) << speed << " │ "
                      << std::setw(11) << memory << " │" << std::endl;
        }
        
        std::cout << "└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘" << std::endl;
        
        std::cout << "\nDWG performance benchmark completed." << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "DWG performance benchmark failed with exception: " << e.what() << std::endl;
    }
}

//=======================================================================================
// Main Function
//=======================================================================================

int main(int argc, char* argv[]) {
    std::string outputDir = "dwg_export_output";
    
    if (argc > 1) {
        outputDir = argv[1];
    }
    
    std::cout << "DWG Export Framework Examples" << std::endl;
    std::cout << "=============================" << std::endl;
    std::cout << "Output directory: " << outputDir << std::endl;
    
    // Create output directory
    std::filesystem::create_directories(outputDir);
    
    // Run DWG export examples
    BasicDWGExportExample(outputDir + "/basic");
    AdvancedDWGConversionExample(outputDir + "/advanced");
    DWGMaterialExample(outputDir + "/materials");
    DWGPerformanceBenchmark(outputDir + "/benchmark");
    
    std::cout << "\nAll DWG export examples completed!" << std::endl;
    std::cout << "Check the output directories for DWG files and reports." << std::endl;
    
    return 0;
}
