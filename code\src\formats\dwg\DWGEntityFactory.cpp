#include "DWGEntityFactory.h"
#include "../../core/ExportContext.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbline.h>
#include <realdwg/base/dbcircle.h>
#include <realdwg/base/dbarc.h>
#include <realdwg/base/dbellipse.h>
#include <realdwg/base/dbspline.h>
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbtext.h>
#include <realdwg/base/dbmtext.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbhatch.h>
#include <realdwg/base/dbsolid.h>
#include <realdwg/base/dbtrace.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/db3dface.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbmesh.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbxref.h>
#include <realdwg/base/dbmline.h>
#include <realdwg/base/dbray.h>
#include <realdwg/base/dbxline.h>
#include <realdwg/base/dbpoint.h>
#include <realdwg/base/dbshape.h>
#include <realdwg/base/dbimage.h>
#include <realdwg/base/dbole2frame.h>
#include <realdwg/base/dbwipeout.h>
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbgroup.h>
#include <realdwg/base/dblight.h>
#include <realdwg/base/dbcamera.h>
#include <realdwg/base/dbhelix.h>
#include <realdwg/base/dbgeodata.h>
#include <realdwg/base/dbnamedpath.h>
#include <realdwg/base/dbmotionpath.h>
#include <realdwg/base/dbsection.h>
#include <realdwg/base/dbsurface.h>
#include <realdwg/base/dbnurbsurface.h>
#include <realdwg/base/dbplanesurface.h>
#include <realdwg/base/dbextrsurf.h>
#include <realdwg/base/dbrevsurf.h>
#include <realdwg/base/dbswepsurf.h>
#include <realdwg/base/dbloftsurf.h>
#include <realdwg/base/dbblendsurf.h>
#include <realdwg/base/dbnetsurf.h>
#include <realdwg/base/dbpatchsurf.h>
#include <realdwg/base/dboffsetsurf.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbtolerance.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbunderlayref.h>
#include <realdwg/base/dbpdfref.h>
#include <realdwg/base/dbdwfref.h>
#include <realdwg/base/dbdgnref.h>
#include <realdwg/base/dbpointcloud.h>
#include <realdwg/base/dbpointcloudex.h>
#include <realdwg/base/dbgeopositionmarker.h>
#include <realdwg/base/dbsun.h>
#include <realdwg/base/dbsky.h>
#include <realdwg/base/dbrenderenvironment.h>
#include <realdwg/base/dbrenderentry.h>
#include <realdwg/base/dbrenderglobal.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/dbmentalrayrendersettings.h>
#include <realdwg/base/dbrapidrtrendersettings.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbviewtable.h>
#include <realdwg/base/dbucstable.h>
#include <realdwg/base/dbvptable.h>
#include <realdwg/base/dbappid.h>
#include <realdwg/base/dbspatialfilter.h>
#include <realdwg/base/dblayerfilter.h>
#include <realdwg/base/dbindex.h>
#include <realdwg/base/dbspatialindex.h>
#include <realdwg/base/dblayerindex.h>
#include <realdwg/base/dbcolor.h>
#include <realdwg/base/dbtrans.h>
#include <realdwg/base/dbproxy.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/base/dbeval.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbacis.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#endif

#include <iostream>
#include <chrono>
#include <algorithm>

namespace IModelExport {

//=======================================================================================
// DWGEntityTypeRegistry Implementation
//=======================================================================================

DWGEntityTypeRegistry& DWGEntityTypeRegistry::Instance() {
    static DWGEntityTypeRegistry instance;
    return instance;
}

DWGEntityTypeRegistry::DWGEntityTypeRegistry() {
    InitializeEntityTypeMap();
    InitializeEntityCategories();
    InitializeEntityCapabilities();
}

void DWGEntityTypeRegistry::InitializeEntityTypeMap() {
    // Initialize type to name mapping
    m_typeToName[DWGEntityType::Line] = "Line";
    m_typeToName[DWGEntityType::Point] = "Point";
    m_typeToName[DWGEntityType::Circle] = "Circle";
    m_typeToName[DWGEntityType::Arc] = "Arc";
    m_typeToName[DWGEntityType::Ellipse] = "Ellipse";
    m_typeToName[DWGEntityType::Polyline] = "Polyline";
    m_typeToName[DWGEntityType::LWPolyline] = "LWPolyline";
    m_typeToName[DWGEntityType::Polyline2D] = "Polyline2D";
    m_typeToName[DWGEntityType::Polyline3D] = "Polyline3D";
    m_typeToName[DWGEntityType::Spline] = "Spline";
    m_typeToName[DWGEntityType::Ray] = "Ray";
    m_typeToName[DWGEntityType::XLine] = "XLine";
    m_typeToName[DWGEntityType::Text] = "Text";
    m_typeToName[DWGEntityType::MText] = "MText";
    m_typeToName[DWGEntityType::AttributeDefinition] = "AttributeDefinition";
    m_typeToName[DWGEntityType::Attribute] = "Attribute";
    m_typeToName[DWGEntityType::AlignedDimension] = "AlignedDimension";
    m_typeToName[DWGEntityType::AngularDimension] = "AngularDimension";
    m_typeToName[DWGEntityType::DiametricDimension] = "DiametricDimension";
    m_typeToName[DWGEntityType::LinearDimension] = "LinearDimension";
    m_typeToName[DWGEntityType::OrdinateDimension] = "OrdinateDimension";
    m_typeToName[DWGEntityType::RadialDimension] = "RadialDimension";
    m_typeToName[DWGEntityType::RadialDimensionLarge] = "RadialDimensionLarge";
    m_typeToName[DWGEntityType::ArcDimension] = "ArcDimension";
    m_typeToName[DWGEntityType::Leader] = "Leader";
    m_typeToName[DWGEntityType::MLeader] = "MLeader";
    m_typeToName[DWGEntityType::Hatch] = "Hatch";
    m_typeToName[DWGEntityType::Gradient] = "Gradient";
    m_typeToName[DWGEntityType::Solid] = "Solid";
    m_typeToName[DWGEntityType::Trace] = "Trace";
    m_typeToName[DWGEntityType::Face3D] = "Face3D";
    m_typeToName[DWGEntityType::PolyFaceMesh] = "PolyFaceMesh";
    m_typeToName[DWGEntityType::PolygonMesh] = "PolygonMesh";
    m_typeToName[DWGEntityType::SubDMesh] = "SubDMesh";
    m_typeToName[DWGEntityType::Solid3D] = "Solid3D";
    m_typeToName[DWGEntityType::Region] = "Region";
    m_typeToName[DWGEntityType::Body] = "Body";
    m_typeToName[DWGEntityType::Surface] = "Surface";
    m_typeToName[DWGEntityType::NurbSurface] = "NurbSurface";
    m_typeToName[DWGEntityType::PlaneSurface] = "PlaneSurface";
    m_typeToName[DWGEntityType::ExtrudedSurface] = "ExtrudedSurface";
    m_typeToName[DWGEntityType::RevolvedSurface] = "RevolvedSurface";
    m_typeToName[DWGEntityType::SweptSurface] = "SweptSurface";
    m_typeToName[DWGEntityType::LoftedSurface] = "LoftedSurface";
    m_typeToName[DWGEntityType::BlendSurface] = "BlendSurface";
    m_typeToName[DWGEntityType::NetworkSurface] = "NetworkSurface";
    m_typeToName[DWGEntityType::PatchSurface] = "PatchSurface";
    m_typeToName[DWGEntityType::OffsetSurface] = "OffsetSurface";
    m_typeToName[DWGEntityType::Mesh] = "Mesh";
    m_typeToName[DWGEntityType::BlockReference] = "BlockReference";
    m_typeToName[DWGEntityType::MInsert] = "MInsert";
    m_typeToName[DWGEntityType::XRef] = "XRef";
    m_typeToName[DWGEntityType::MLine] = "MLine";
    m_typeToName[DWGEntityType::Shape] = "Shape";
    m_typeToName[DWGEntityType::RasterImage] = "RasterImage";
    m_typeToName[DWGEntityType::WipeOut] = "WipeOut";
    m_typeToName[DWGEntityType::OLE2Frame] = "OLE2Frame";
    m_typeToName[DWGEntityType::Viewport] = "Viewport";
    m_typeToName[DWGEntityType::Table] = "Table";
    m_typeToName[DWGEntityType::Field] = "Field";
    m_typeToName[DWGEntityType::Group] = "Group";
    m_typeToName[DWGEntityType::Light] = "Light";
    m_typeToName[DWGEntityType::WebLight] = "WebLight";
    m_typeToName[DWGEntityType::DistantLight] = "DistantLight";
    m_typeToName[DWGEntityType::PointLight] = "PointLight";
    m_typeToName[DWGEntityType::SpotLight] = "SpotLight";
    m_typeToName[DWGEntityType::Camera] = "Camera";
    m_typeToName[DWGEntityType::Helix] = "Helix";
    m_typeToName[DWGEntityType::GeoData] = "GeoData";
    m_typeToName[DWGEntityType::GeoPositionMarker] = "GeoPositionMarker";
    m_typeToName[DWGEntityType::NamedPath] = "NamedPath";
    m_typeToName[DWGEntityType::MotionPath] = "MotionPath";
    m_typeToName[DWGEntityType::Section] = "Section";
    m_typeToName[DWGEntityType::Tolerance] = "Tolerance";
    m_typeToName[DWGEntityType::FCF] = "FCF";
    m_typeToName[DWGEntityType::ProxyEntity] = "ProxyEntity";
    m_typeToName[DWGEntityType::UnderlayReference] = "UnderlayReference";
    m_typeToName[DWGEntityType::DwfReference] = "DwfReference";
    m_typeToName[DWGEntityType::DgnReference] = "DgnReference";
    m_typeToName[DWGEntityType::PdfReference] = "PdfReference";
    m_typeToName[DWGEntityType::PointCloud] = "PointCloud";
    m_typeToName[DWGEntityType::PointCloudEx] = "PointCloudEx";
    m_typeToName[DWGEntityType::Sun] = "Sun";
    m_typeToName[DWGEntityType::Sky] = "Sky";
    m_typeToName[DWGEntityType::RenderEnvironment] = "RenderEnvironment";
    m_typeToName[DWGEntityType::RenderEntry] = "RenderEntry";
    m_typeToName[DWGEntityType::RenderGlobal] = "RenderGlobal";
    m_typeToName[DWGEntityType::Material] = "Material";
    m_typeToName[DWGEntityType::VisualStyle] = "VisualStyle";
    m_typeToName[DWGEntityType::MentalRayRenderSettings] = "MentalRayRenderSettings";
    m_typeToName[DWGEntityType::RapidRTRenderSettings] = "RapidRTRenderSettings";
    m_typeToName[DWGEntityType::Layout] = "Layout";
    m_typeToName[DWGEntityType::PlotSettings] = "PlotSettings";
    m_typeToName[DWGEntityType::Dictionary] = "Dictionary";
    m_typeToName[DWGEntityType::XRecord] = "XRecord";
    m_typeToName[DWGEntityType::LayerTableRecord] = "LayerTableRecord";
    m_typeToName[DWGEntityType::LinetypeTableRecord] = "LinetypeTableRecord";
    m_typeToName[DWGEntityType::TextStyleTableRecord] = "TextStyleTableRecord";
    m_typeToName[DWGEntityType::DimStyleTableRecord] = "DimStyleTableRecord";
    m_typeToName[DWGEntityType::BlockTableRecord] = "BlockTableRecord";
    m_typeToName[DWGEntityType::ViewTableRecord] = "ViewTableRecord";
    m_typeToName[DWGEntityType::UCSTableRecord] = "UCSTableRecord";
    m_typeToName[DWGEntityType::ViewportTableRecord] = "ViewportTableRecord";
    m_typeToName[DWGEntityType::RegAppTableRecord] = "RegAppTableRecord";
    m_typeToName[DWGEntityType::MLineStyleTableRecord] = "MLineStyleTableRecord";
    m_typeToName[DWGEntityType::SpatialFilter] = "SpatialFilter";
    m_typeToName[DWGEntityType::LayerFilter] = "LayerFilter";
    m_typeToName[DWGEntityType::SpatialIndex] = "SpatialIndex";
    m_typeToName[DWGEntityType::LayerIndex] = "LayerIndex";
    m_typeToName[DWGEntityType::DynamicBlockReference] = "DynamicBlockReference";
    m_typeToName[DWGEntityType::EvalGraph] = "EvalGraph";
    m_typeToName[DWGEntityType::AssocNetwork] = "AssocNetwork";
    m_typeToName[DWGEntityType::AssocAction] = "AssocAction";
    m_typeToName[DWGEntityType::AssocActionBody] = "AssocActionBody";
    m_typeToName[DWGEntityType::AssocActionParam] = "AssocActionParam";
    m_typeToName[DWGEntityType::AssocDependency] = "AssocDependency";
    m_typeToName[DWGEntityType::AssocGeomDependency] = "AssocGeomDependency";
    m_typeToName[DWGEntityType::AssocValueDependency] = "AssocValueDependency";
    m_typeToName[DWGEntityType::AssocVariable] = "AssocVariable";
    m_typeToName[DWGEntityType::CustomEntity] = "CustomEntity";
    m_typeToName[DWGEntityType::Unknown] = "Unknown";

#ifdef REALDWG_AVAILABLE
    // Initialize class name to type mapping
    m_classNameToType["AcDbLine"] = DWGEntityType::Line;
    m_classNameToType["AcDbPoint"] = DWGEntityType::Point;
    m_classNameToType["AcDbCircle"] = DWGEntityType::Circle;
    m_classNameToType["AcDbArc"] = DWGEntityType::Arc;
    m_classNameToType["AcDbEllipse"] = DWGEntityType::Ellipse;
    m_classNameToType["AcDbPolyline"] = DWGEntityType::Polyline;
    m_classNameToType["AcDb2dPolyline"] = DWGEntityType::Polyline2D;
    m_classNameToType["AcDb3dPolyline"] = DWGEntityType::Polyline3D;
    m_classNameToType["AcDbSpline"] = DWGEntityType::Spline;
    m_classNameToType["AcDbRay"] = DWGEntityType::Ray;
    m_classNameToType["AcDbXline"] = DWGEntityType::XLine;
    m_classNameToType["AcDbText"] = DWGEntityType::Text;
    m_classNameToType["AcDbMText"] = DWGEntityType::MText;
    m_classNameToType["AcDbAttributeDefinition"] = DWGEntityType::AttributeDefinition;
    m_classNameToType["AcDbAttribute"] = DWGEntityType::Attribute;
    m_classNameToType["AcDbAlignedDimension"] = DWGEntityType::AlignedDimension;
    m_classNameToType["AcDbAngularDimension"] = DWGEntityType::AngularDimension;
    m_classNameToType["AcDbDiametricDimension"] = DWGEntityType::DiametricDimension;
    m_classNameToType["AcDbRotatedDimension"] = DWGEntityType::LinearDimension;
    m_classNameToType["AcDbOrdinateDimension"] = DWGEntityType::OrdinateDimension;
    m_classNameToType["AcDbRadialDimension"] = DWGEntityType::RadialDimension;
    m_classNameToType["AcDbRadialDimensionLarge"] = DWGEntityType::RadialDimensionLarge;
    m_classNameToType["AcDbArcDimension"] = DWGEntityType::ArcDimension;
    m_classNameToType["AcDbLeader"] = DWGEntityType::Leader;
    m_classNameToType["AcDbMLeader"] = DWGEntityType::MLeader;
    m_classNameToType["AcDbHatch"] = DWGEntityType::Hatch;
    m_classNameToType["AcDbSolid"] = DWGEntityType::Solid;
    m_classNameToType["AcDbTrace"] = DWGEntityType::Trace;
    m_classNameToType["AcDb3dFace"] = DWGEntityType::Face3D;
    m_classNameToType["AcDbPolyFaceMesh"] = DWGEntityType::PolyFaceMesh;
    m_classNameToType["AcDbPolygonMesh"] = DWGEntityType::PolygonMesh;
    m_classNameToType["AcDbSubDMesh"] = DWGEntityType::SubDMesh;
    m_classNameToType["AcDb3dSolid"] = DWGEntityType::Solid3D;
    m_classNameToType["AcDbRegion"] = DWGEntityType::Region;
    m_classNameToType["AcDbBody"] = DWGEntityType::Body;
    m_classNameToType["AcDbSurface"] = DWGEntityType::Surface;
    m_classNameToType["AcDbNurbSurface"] = DWGEntityType::NurbSurface;
    m_classNameToType["AcDbPlaneSurface"] = DWGEntityType::PlaneSurface;
    m_classNameToType["AcDbExtrudedSurface"] = DWGEntityType::ExtrudedSurface;
    m_classNameToType["AcDbRevolvedSurface"] = DWGEntityType::RevolvedSurface;
    m_classNameToType["AcDbSweptSurface"] = DWGEntityType::SweptSurface;
    m_classNameToType["AcDbLoftedSurface"] = DWGEntityType::LoftedSurface;
    m_classNameToType["AcDbBlendSurface"] = DWGEntityType::BlendSurface;
    m_classNameToType["AcDbNetworkSurface"] = DWGEntityType::NetworkSurface;
    m_classNameToType["AcDbPatchSurface"] = DWGEntityType::PatchSurface;
    m_classNameToType["AcDbOffsetSurface"] = DWGEntityType::OffsetSurface;
    m_classNameToType["AcDbMesh"] = DWGEntityType::Mesh;
    m_classNameToType["AcDbBlockReference"] = DWGEntityType::BlockReference;
    m_classNameToType["AcDbMInsert"] = DWGEntityType::MInsert;
    m_classNameToType["AcDbMline"] = DWGEntityType::MLine;
    m_classNameToType["AcDbShape"] = DWGEntityType::Shape;
    m_classNameToType["AcDbRasterImage"] = DWGEntityType::RasterImage;
    m_classNameToType["AcDbWipeout"] = DWGEntityType::WipeOut;
    m_classNameToType["AcDbOle2Frame"] = DWGEntityType::OLE2Frame;
    m_classNameToType["AcDbViewport"] = DWGEntityType::Viewport;
    m_classNameToType["AcDbTable"] = DWGEntityType::Table;
    m_classNameToType["AcDbField"] = DWGEntityType::Field;
    m_classNameToType["AcDbGroup"] = DWGEntityType::Group;
    m_classNameToType["AcDbLight"] = DWGEntityType::Light;
    m_classNameToType["AcDbCamera"] = DWGEntityType::Camera;
    m_classNameToType["AcDbHelix"] = DWGEntityType::Helix;
    m_classNameToType["AcDbGeoData"] = DWGEntityType::GeoData;
    m_classNameToType["AcDbGeoPositionMarker"] = DWGEntityType::GeoPositionMarker;
    m_classNameToType["AcDbNamedPath"] = DWGEntityType::NamedPath;
    m_classNameToType["AcDbMotionPath"] = DWGEntityType::MotionPath;
    m_classNameToType["AcDbSection"] = DWGEntityType::Section;
    m_classNameToType["AcDbTolerance"] = DWGEntityType::Tolerance;
    m_classNameToType["AcDbFcf"] = DWGEntityType::FCF;
    m_classNameToType["AcDbProxyEntity"] = DWGEntityType::ProxyEntity;
    m_classNameToType["AcDbUnderlayReference"] = DWGEntityType::UnderlayReference;
    m_classNameToType["AcDbDwfReference"] = DWGEntityType::DwfReference;
    m_classNameToType["AcDbDgnReference"] = DWGEntityType::DgnReference;
    m_classNameToType["AcDbPdfReference"] = DWGEntityType::PdfReference;
    m_classNameToType["AcDbPointCloud"] = DWGEntityType::PointCloud;
    m_classNameToType["AcDbPointCloudEx"] = DWGEntityType::PointCloudEx;

    // Initialize type to class name mapping (reverse)
    for (const auto& pair : m_classNameToType) {
        m_typeToClassName[pair.second] = pair.first;
    }
#endif
}

void DWGEntityTypeRegistry::InitializeEntityCategories() {
    // Geometric entities
    m_geometricEntities[DWGEntityType::Line] = true;
    m_geometricEntities[DWGEntityType::Point] = true;
    m_geometricEntities[DWGEntityType::Circle] = true;
    m_geometricEntities[DWGEntityType::Arc] = true;
    m_geometricEntities[DWGEntityType::Ellipse] = true;
    m_geometricEntities[DWGEntityType::Polyline] = true;
    m_geometricEntities[DWGEntityType::LWPolyline] = true;
    m_geometricEntities[DWGEntityType::Polyline2D] = true;
    m_geometricEntities[DWGEntityType::Polyline3D] = true;
    m_geometricEntities[DWGEntityType::Spline] = true;
    m_geometricEntities[DWGEntityType::Ray] = true;
    m_geometricEntities[DWGEntityType::XLine] = true;
    m_geometricEntities[DWGEntityType::Helix] = true;

    // Annotation entities
    m_annotationEntities[DWGEntityType::Text] = true;
    m_annotationEntities[DWGEntityType::MText] = true;
    m_annotationEntities[DWGEntityType::AttributeDefinition] = true;
    m_annotationEntities[DWGEntityType::Attribute] = true;
    m_annotationEntities[DWGEntityType::AlignedDimension] = true;
    m_annotationEntities[DWGEntityType::AngularDimension] = true;
    m_annotationEntities[DWGEntityType::DiametricDimension] = true;
    m_annotationEntities[DWGEntityType::LinearDimension] = true;
    m_annotationEntities[DWGEntityType::OrdinateDimension] = true;
    m_annotationEntities[DWGEntityType::RadialDimension] = true;
    m_annotationEntities[DWGEntityType::RadialDimensionLarge] = true;
    m_annotationEntities[DWGEntityType::ArcDimension] = true;
    m_annotationEntities[DWGEntityType::Leader] = true;
    m_annotationEntities[DWGEntityType::MLeader] = true;
    m_annotationEntities[DWGEntityType::Tolerance] = true;
    m_annotationEntities[DWGEntityType::FCF] = true;

    // 3D entities
    m_3dEntities[DWGEntityType::Polyline3D] = true;
    m_3dEntities[DWGEntityType::Face3D] = true;
    m_3dEntities[DWGEntityType::PolyFaceMesh] = true;
    m_3dEntities[DWGEntityType::PolygonMesh] = true;
    m_3dEntities[DWGEntityType::SubDMesh] = true;
    m_3dEntities[DWGEntityType::Solid3D] = true;
    m_3dEntities[DWGEntityType::Region] = true;
    m_3dEntities[DWGEntityType::Body] = true;
    m_3dEntities[DWGEntityType::Mesh] = true;
    m_3dEntities[DWGEntityType::Helix] = true;

    // Surface entities
    m_surfaceEntities[DWGEntityType::Surface] = true;
    m_surfaceEntities[DWGEntityType::NurbSurface] = true;
    m_surfaceEntities[DWGEntityType::PlaneSurface] = true;
    m_surfaceEntities[DWGEntityType::ExtrudedSurface] = true;
    m_surfaceEntities[DWGEntityType::RevolvedSurface] = true;
    m_surfaceEntities[DWGEntityType::SweptSurface] = true;
    m_surfaceEntities[DWGEntityType::LoftedSurface] = true;
    m_surfaceEntities[DWGEntityType::BlendSurface] = true;
    m_surfaceEntities[DWGEntityType::NetworkSurface] = true;
    m_surfaceEntities[DWGEntityType::PatchSurface] = true;
    m_surfaceEntities[DWGEntityType::OffsetSurface] = true;

    // Solid entities
    m_solidEntities[DWGEntityType::Solid3D] = true;
    m_solidEntities[DWGEntityType::Region] = true;
    m_solidEntities[DWGEntityType::Body] = true;

    // Mesh entities
    m_meshEntities[DWGEntityType::Face3D] = true;
    m_meshEntities[DWGEntityType::PolyFaceMesh] = true;
    m_meshEntities[DWGEntityType::PolygonMesh] = true;
    m_meshEntities[DWGEntityType::SubDMesh] = true;
    m_meshEntities[DWGEntityType::Mesh] = true;

    // Block entities
    m_blockEntities[DWGEntityType::BlockReference] = true;
    m_blockEntities[DWGEntityType::MInsert] = true;
    m_blockEntities[DWGEntityType::XRef] = true;
    m_blockEntities[DWGEntityType::DynamicBlockReference] = true;

    // Light entities
    m_lightEntities[DWGEntityType::Light] = true;
    m_lightEntities[DWGEntityType::WebLight] = true;
    m_lightEntities[DWGEntityType::DistantLight] = true;
    m_lightEntities[DWGEntityType::PointLight] = true;
    m_lightEntities[DWGEntityType::SpotLight] = true;
    m_lightEntities[DWGEntityType::Sun] = true;

    // Rendering entities
    m_renderingEntities[DWGEntityType::Material] = true;
    m_renderingEntities[DWGEntityType::VisualStyle] = true;
    m_renderingEntities[DWGEntityType::RenderEnvironment] = true;
    m_renderingEntities[DWGEntityType::RenderEntry] = true;
    m_renderingEntities[DWGEntityType::RenderGlobal] = true;
    m_renderingEntities[DWGEntityType::MentalRayRenderSettings] = true;
    m_renderingEntities[DWGEntityType::RapidRTRenderSettings] = true;
    m_renderingEntities[DWGEntityType::Sky] = true;
}

void DWGEntityTypeRegistry::InitializeEntityCapabilities() {
    // Initialize all entity types to support basic capabilities by default
    for (const auto& pair : m_typeToName) {
        DWGEntityType type = pair.first;

        // Most entities support layers, colors, linetypes, etc.
        m_supportsLayers[type] = true;
        m_supportsColors[type] = true;
        m_supportsLinetypes[type] = true;
        m_supportsLineWeights[type] = true;
        m_supportsTransparency[type] = true;
        m_supportsVisibility[type] = true;

        // Default to not supporting attributes and materials
        m_supportsAttributes[type] = false;
        m_supportsMaterials[type] = false;
        m_requiresSpecialHandling[type] = false;
    }

    // Entities that support attributes
    m_supportsAttributes[DWGEntityType::BlockReference] = true;
    m_supportsAttributes[DWGEntityType::MInsert] = true;
    m_supportsAttributes[DWGEntityType::AttributeDefinition] = true;
    m_supportsAttributes[DWGEntityType::Attribute] = true;

    // Entities that support materials
    m_supportsMaterials[DWGEntityType::Solid3D] = true;
    m_supportsMaterials[DWGEntityType::Region] = true;
    m_supportsMaterials[DWGEntityType::Body] = true;
    m_supportsMaterials[DWGEntityType::Surface] = true;
    m_supportsMaterials[DWGEntityType::NurbSurface] = true;
    m_supportsMaterials[DWGEntityType::PlaneSurface] = true;
    m_supportsMaterials[DWGEntityType::ExtrudedSurface] = true;
    m_supportsMaterials[DWGEntityType::RevolvedSurface] = true;
    m_supportsMaterials[DWGEntityType::SweptSurface] = true;
    m_supportsMaterials[DWGEntityType::LoftedSurface] = true;
    m_supportsMaterials[DWGEntityType::BlendSurface] = true;
    m_supportsMaterials[DWGEntityType::NetworkSurface] = true;
    m_supportsMaterials[DWGEntityType::PatchSurface] = true;
    m_supportsMaterials[DWGEntityType::OffsetSurface] = true;
    m_supportsMaterials[DWGEntityType::Mesh] = true;
    m_supportsMaterials[DWGEntityType::Face3D] = true;
    m_supportsMaterials[DWGEntityType::PolyFaceMesh] = true;
    m_supportsMaterials[DWGEntityType::PolygonMesh] = true;
    m_supportsMaterials[DWGEntityType::SubDMesh] = true;

    // Entities that require special handling
    m_requiresSpecialHandling[DWGEntityType::BlockReference] = true;
    m_requiresSpecialHandling[DWGEntityType::MInsert] = true;
    m_requiresSpecialHandling[DWGEntityType::XRef] = true;
    m_requiresSpecialHandling[DWGEntityType::DynamicBlockReference] = true;
    m_requiresSpecialHandling[DWGEntityType::Hatch] = true;
    m_requiresSpecialHandling[DWGEntityType::Gradient] = true;
    m_requiresSpecialHandling[DWGEntityType::RasterImage] = true;
    m_requiresSpecialHandling[DWGEntityType::OLE2Frame] = true;
    m_requiresSpecialHandling[DWGEntityType::Viewport] = true;
    m_requiresSpecialHandling[DWGEntityType::Table] = true;
    m_requiresSpecialHandling[DWGEntityType::Field] = true;
    m_requiresSpecialHandling[DWGEntityType::Group] = true;
    m_requiresSpecialHandling[DWGEntityType::ProxyEntity] = true;
    m_requiresSpecialHandling[DWGEntityType::UnderlayReference] = true;
    m_requiresSpecialHandling[DWGEntityType::DwfReference] = true;
    m_requiresSpecialHandling[DWGEntityType::DgnReference] = true;
    m_requiresSpecialHandling[DWGEntityType::PdfReference] = true;
    m_requiresSpecialHandling[DWGEntityType::PointCloud] = true;
    m_requiresSpecialHandling[DWGEntityType::PointCloudEx] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocNetwork] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocAction] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocActionBody] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocActionParam] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocDependency] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocGeomDependency] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocValueDependency] = true;
    m_requiresSpecialHandling[DWGEntityType::AssocVariable] = true;

    // Entities that don't support standard properties
    m_supportsLinetypes[DWGEntityType::RasterImage] = false;
    m_supportsLinetypes[DWGEntityType::OLE2Frame] = false;
    m_supportsLinetypes[DWGEntityType::PointCloud] = false;
    m_supportsLinetypes[DWGEntityType::PointCloudEx] = false;

    m_supportsLineWeights[DWGEntityType::RasterImage] = false;
    m_supportsLineWeights[DWGEntityType::OLE2Frame] = false;
    m_supportsLineWeights[DWGEntityType::PointCloud] = false;
    m_supportsLineWeights[DWGEntityType::PointCloudEx] = false;
    m_supportsLineWeights[DWGEntityType::Light] = false;
    m_supportsLineWeights[DWGEntityType::Camera] = false;
}

DWGEntityType DWGEntityTypeRegistry::GetEntityType(void* entity) const {
#ifdef REALDWG_AVAILABLE
    if (!entity) return DWGEntityType::Unknown;

    AcDbEntity* acEntity = static_cast<AcDbEntity*>(entity);
    AcRxClass* entityClass = acEntity->isA();
    if (!entityClass) return DWGEntityType::Unknown;

    const ACHAR* className = entityClass->name();
    if (!className) return DWGEntityType::Unknown;

    std::string classNameStr;
#ifdef UNICODE
    // Convert wide string to narrow string
    int len = WideCharToMultiByte(CP_UTF8, 0, className, -1, nullptr, 0, nullptr, nullptr);
    if (len > 0) {
        classNameStr.resize(len - 1);
        WideCharToMultiByte(CP_UTF8, 0, className, -1, &classNameStr[0], len, nullptr, nullptr);
    }
#else
    classNameStr = className;
#endif

    auto it = m_classNameToType.find(classNameStr);
    if (it != m_classNameToType.end()) {
        return it->second;
    }

    return DWGEntityType::Unknown;
#else
    return DWGEntityType::Unknown;
#endif
}

std::string DWGEntityTypeRegistry::GetEntityTypeName(DWGEntityType type) const {
    auto it = m_typeToName.find(type);
    if (it != m_typeToName.end()) {
        return it->second;
    }
    return "Unknown";
}

std::string DWGEntityTypeRegistry::GetEntityClassName(DWGEntityType type) const {
#ifdef REALDWG_AVAILABLE
    auto it = m_typeToClassName.find(type);
    if (it != m_typeToClassName.end()) {
        return it->second;
    }
#endif
    return "";
}

} // namespace IModelExport
