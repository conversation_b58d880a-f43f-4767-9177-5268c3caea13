/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/RTextDbx/RText.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// warning C4100: 'pkt': unreferenced formal parameter in dbxEntryPoint.h(109)
// warning C4201: nonstandard extension used: nameless struct/union in dbhandle.h(107)
#pragma warning(disable: 4100 4201)

#include <windows.h>
#include <objbase.h>
#include <Realdwg\Base\rxregsvc.h>
#include <Realdwg\Base\dbapserv.h>
#include <Realdwg\Base\dbxEntryPoint.h>
#include <Realdwg\Base\dbproxy.h>
#include <Realdwg\Base\dbobjptr.h>
#include "RText.h"

static RTextCollection      s_rtextCollector;

extern "C" 
{
// locally defined entry point invoked by Rx.
AcRx::AppRetCode RTEXT_EXPORT acrxEntryPoint(AcRx::AppMsgCode msg, void* pkt);
}

ACRX_DXF_DEFINE_MEMBERS(RText, AcDbEntity, AcDb::kDHL_CURRENT, AcDb::kMReleaseCurrent, AcDbProxyEntity::kAllAllowedBits, "RTEXT", "RTEXT");

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
RText::Data::Data() : m_angle(0), m_height(0), m_flags(0)
    {
    m_position.set (0,0,0);
    m_normal.set (0,0,0);
    m_contents.setEmpty ();
    m_textstyleName.setEmpty ();
    m_textstyleId.setNull ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus RText::dwgInFields(AcDbDwgFiler* filer)
    {
    assertWriteEnabled ();
    auto es = AcDbEntity::dwgInFields (filer);
    if (es != Acad::eOk)
        return es;

    auto pos = filer->tell ();

    RText::Data data;
    filer->readPoint3d (&data.m_position);
    filer->readVector3d (&data.m_normal);
    filer->readDouble (&data.m_angle);
    filer->readDouble (&data.m_height);
    filer->readHardPointerId (&data.m_textstyleId);
    filer->readInt16 (&data.m_flags);
    filer->readString (data.m_contents);

    if (filer->filerStatus() == Acad::eOk && !data.m_contents.isEmpty())
        {
        RTextEntry  rtext(this->objectId(), data);
        s_rtextCollector.insert (rtext);
        }

    filer->seek (pos, 0);

    return Acad::eMakeMeProxy;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus RText::dxfInFields(AcDbDxfFiler* filer)
    {
    /*-----------------------------------------------------------------------------------
    Below implementation works, but we need an object ID or handle for the RTEXT.  According to Art
    from Autodesk, the object is not set resident in db through dxfInFields.  His workaround is to
    implement AcDbDatabaseReactor::objectAppended, reasoning that when this method returns eMakeMeProxy
    the caller will delete this object and add a proxy object into db.  My tests have shown negative
    results: objectAppended is never called.  Method objectOpenedForModify may or may not be called.
    Untill we have figured out a way to cache the data with a unique key there is no point to bother
    extracting the data now.
    -----------------------------------------------------------------------------------*/
#ifdef HAVE_OBJECT_ID
    assertWriteEnabled ();
    auto es = AcDbEntity::dxfInFields (filer);
    if (es != Acad::eOk)
        return es;

    if (!filer->atSubclassData(desc()->name()))
        return filer->filerStatus();

    RText::Data data;
    struct resbuf rb;

    while (!filer->atEOF())
        {
        if (filer->readItem(&rb) != Acad::eOk)
            break;
        switch (rb.restype)
            {
            case AcDb::kDxfXCoord:
                data.m_position.set (rb.resval.rpoint[0], rb.resval.rpoint[1], rb.resval.rpoint[2]);
                break;
            case AcDb::kDxfAngle:
                data.m_angle = rb.resval.rreal;
                break;
            case AcDb::kDxfTxtSize:
                data.m_height = rb.resval.rreal;
                break;
            case AcDb::kDxfText:
                data.m_contents.assign (rb.resval.rstring);
                break;
            case AcDb::kDxfNormalX:
                data.m_normal.set (rb.resval.rpoint[0], rb.resval.rpoint[1], rb.resval.rpoint[2]);
                break;
            case AcDb::kDxfInt16:
                // 1/0 = expression/file name
                // 2/0 = inline mtext sequences enabled/disabled
                data.m_flags = rb.resval.rint;
                break;
            case AcDb::kDxfHardPointerId:
                // currently dxf filer does not seem to reach here, but would be nice to see it does in the future!
                ::acdbGetObjectId (data.m_textstyleId, rb.resval.rlname);
                break;
            case AcDb::kDxfTextStyleName:
                data.m_textstyleName.assign (rb.resval.rstring);
                break;
            default:
                break;
            }
        }
    
    if (filer->filerStatus() == Acad::eOk && !data.m_contents.isEmpty())
        {
        RTextEntry  rtext(this->objectId(), data);
        s_rtextCollector.insert (rtext);
        }
#endif

    return Acad::eMakeMeProxy;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
AcRx::AppRetCode RTEXT_EXPORT acrxEntryPoint(AcRx::AppMsgCode msg, void* pkt)
    {
    switch (msg)
        {
        case AcRx::kInitAppMsg:
            ::acrxUnlockApplication (pkt);     // Try to allow unloading
            ::acrxRegisterAppMDIAware (pkt);
            RText::rxInit ();
            // Register a service using the class name.
            if (!::acrxServiceIsRegistered(L"RText"))
                ::acrxRegisterService (L"RText");
            ::acrxBuildClassHierarchy();
            break;

        case AcRx::kUnloadAppMsg:
            // Unregister the service
            AcRxObject *obj = ::acrxServiceDictionary->remove(L"Rtext");
            if (obj != NULL)
                    delete obj;
            ::deleteAcRxClass (RText::desc());
            ::acrxBuildClassHierarchy ();
            break;
        }
    return AcRx::kRetOK;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
void InitializeRTextCollection ()
    {
    s_rtextCollector.clear ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
RTextCollection& GetRTextCollection ()
    {
    return s_rtextCollector;
    }
