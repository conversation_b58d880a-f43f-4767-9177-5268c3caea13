/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSection.cpp $
|
|  $Copyright: (c) 2013 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          04/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSectionClip : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    DIAGNOSTIC_PRINTF ("SectionClip element not implemented yet!\n");
    return  RealDwgIgnoreElement;
    }
};      // ToDwgExtSectionClip
