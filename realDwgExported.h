/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/realDwgExported.h $
|
|  $Copyright: (c) 2010 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#include    <Mstn\RealDWG\rDwgApi.h>

BEGIN_EXTERN_C
RDWG_EXPORTED AcDbDatabase*     realDwg_getDatabase (DgnFileP fileObj);

RDWG_EXPORTED StatusInt         realDwg_getIntegerSystemVariable (::Int32& valueOut, const AcDbDatabase* database, const char* varName);
RDWG_EXPORTED StatusInt         realDwg_getBooleanSystemVariable (bool& valueOut, const AcDbDatabase* database, const char* varName);
RDWG_EXPORTED StatusInt         realDwg_getDoubleSystemVariable (double& valueOut, const AcDbDatabase* database, const char* varName);
RDWG_EXPORTED StatusInt         realDwg_getPointSystemVariable (::DPoint3d& valueOut, const AcDbDatabase* database, const char* varName);
RDWG_EXPORTED StatusInt         realDwg_getStringSystemVariable (Bentley::WString& valueOut, const AcDbDatabase* database, const char* varName);

RDWG_EXPORTED StatusInt         realDwg_setIntegerSystemVariable (AcDbDatabase* database, const char* varName, ::Int32 valueIn);
RDWG_EXPORTED StatusInt         realDwg_setBooleanSystemVariable (AcDbDatabase* database, const char* varName, bool valueIn);
RDWG_EXPORTED StatusInt         realDwg_setDoubleSystemVariable (AcDbDatabase* database, const char* varName, double valueIn);
RDWG_EXPORTED StatusInt         realDwg_setPointSystemVariable (AcDbDatabase* database, const char* varName, const ::DPoint3d& value);
RDWG_EXPORTED StatusInt         realDwg_setStringSystemVariable (AcDbDatabase* database, const char* varName, const Bentley::WString& valueIn);
END_EXTERN_C
