#pragma once

#include "../../../include/IExportFormat.h"
#include "../../../include/ExportTypes.h"
#include "../../core/ExportContext.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbobjptr.h>
#endif

#include <memory>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class IModelDb;

//=======================================================================================
// DWG Exporter Implementation using RealDWG SDK
//=======================================================================================

class DWGExporter : public IDWGExporter {
public:
    DWGExporter();
    virtual ~DWGExporter();

    //===================================================================================
    // IExportFormat Interface
    //===================================================================================

    ExportFormat GetFormat() const override { return ExportFormat::DWG; }
    std::string GetFormatName() const override { return "AutoCAD DWG"; }
    std::string GetFileExtension() const override { return ".dwg"; }
    std::vector<std::string> GetSupportedVersions() const override;

    bool SupportsGeometry() const override { return true; }
    bool SupportsMetadata() const override { return true; }
    bool SupportsMaterials() const override { return true; }
    bool SupportsTextures() const override { return true; }
    bool SupportsAnimations() const override { return false; }
    bool SupportsHierarchy() const override { return true; }

    ExportResult Export(
        const IModelDb& imodel,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) override;

    bool ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const override;
    bool CanExportElement(const ElementInfo& element) const override;

    void SetExportContext(std::shared_ptr<ExportContext> context) override;
    std::shared_ptr<ExportContext> GetExportContext() const override;

    //===================================================================================
    // Advanced Export Capabilities
    //===================================================================================

    // Batch export support
    ExportResult ExportBatch(
        const std::vector<ElementInfo>& elements,
        const DWGExportOptions& options,
        ProgressCallback progressCallback = nullptr);

    // Streaming export for large datasets
    bool StartStreamingExport(const DWGExportOptions& options);
    bool AddElementToStream(const ElementInfo& element);
    bool FinalizeStreamingExport();

    // Export statistics and analysis
    struct ExportStatistics {
        size_t totalElements = 0;
        size_t exportedElements = 0;
        size_t skippedElements = 0;
        size_t errorElements = 0;
        std::unordered_map<std::string, size_t> entityTypeCounts;
        std::unordered_map<std::string, size_t> layerCounts;
        double totalExportTime = 0.0;
        double averageElementTime = 0.0;
        size_t memoryUsage = 0;
    };

    ExportStatistics GetExportStatistics() const;

    //===================================================================================
    // IDWGExporter Interface
    //===================================================================================

    bool SetDWGVersion(DWGExportOptions::DWGVersion version) override;
    bool LoadTemplate(const std::string& templatePath) override;
    bool CreateLayer(const std::string& layerName, const Color& color) override;
    bool CreateBlock(const std::string& blockName) override;

    bool AddLine(const Point3d& start, const Point3d& end, const std::string& layer = "") override;
    bool AddCircle(const Point3d& center, double radius, const std::string& layer = "") override;
    bool AddText(const Point3d& position, const std::string& text, double height, const std::string& layer = "") override;
    bool AddPolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "") override;

    //===================================================================================
    // Extended Entity Creation Methods
    //===================================================================================

    // Advanced geometric entities
    bool AddArc(const Point3d& center, double radius, double startAngle, double endAngle, const std::string& layer = "");
    bool AddEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, const std::string& layer = "");
    bool AddSpline(const std::vector<Point3d>& controlPoints, int degree = 3, const std::string& layer = "");
    bool AddHatch(const std::vector<std::vector<Point3d>>& boundaries, const std::string& pattern, const std::string& layer = "");

    // 3D entities
    bool Add3DFace(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4, const std::string& layer = "");
    bool AddSolid(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces, const std::string& layer = "");
    bool AddMesh(const std::vector<Point3d>& vertices, const std::vector<int>& indices, const std::string& layer = "");

    // Annotation entities
    bool AddDimension(const Point3d& start, const Point3d& end, const Point3d& textPos, const std::string& layer = "");
    bool AddLeader(const std::vector<Point3d>& points, const std::string& text, const std::string& layer = "");
    bool AddMText(const Point3d& position, const std::string& text, double width, double height, const std::string& layer = "");

    // Block and reference entities
    bool AddBlockReference(const std::string& blockName, const Point3d& position, const Transform3d& transform, const std::string& layer = "");
    bool AddXRef(const std::string& fileName, const Point3d& position, const Transform3d& transform, const std::string& layer = "");

    // Complex entities
    bool AddRegion(const std::vector<std::vector<Point3d>>& boundaries, const std::string& layer = "");
    bool AddBody(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces, const std::string& layer = "");

    //===================================================================================
    // Advanced Layer and Block Management
    //===================================================================================

    // Layer management
    bool SetLayerProperties(const std::string& layerName, const Color& color, const std::string& lineType, double lineWeight);
    bool SetLayerVisibility(const std::string& layerName, bool visible);
    bool SetLayerLocked(const std::string& layerName, bool locked);
    bool SetLayerFrozen(const std::string& layerName, bool frozen);
    bool DeleteLayer(const std::string& layerName);
    std::vector<std::string> GetAllLayers() const;

    // Block management
    bool StartBlockDefinition(const std::string& blockName, const Point3d& basePoint);
    bool EndBlockDefinition();
    bool AddEntityToCurrentBlock(const std::string& entityType, const std::vector<Point3d>& points);
    bool DeleteBlock(const std::string& blockName);
    std::vector<std::string> GetAllBlocks() const;

    // Advanced block operations
    bool ExplodeBlock(const std::string& blockRefId);
    bool UpdateBlockDefinition(const std::string& blockName);
    bool CreateNestedBlock(const std::string& parentBlock, const std::string& childBlock, const Transform3d& transform);

protected:
    bool InitializeExport(const ExportOptions& options) override;
    bool FinalizeExport() override;
    void CleanupExport() override;

private:
    //===================================================================================
    // RealDWG Integration
    //===================================================================================

#ifdef REALDWG_AVAILABLE
    // RealDWG objects
    AcDbDatabase* m_database;
    AcDbHostApplicationServices* m_hostApp;

    // Symbol tables
    AcDbLayerTable* m_layerTable;
    AcDbBlockTable* m_blockTable;
    AcDbTextStyleTable* m_textStyleTable;
    AcDbLinetypeTable* m_linetypeTable;
    AcDbDimStyleTable* m_dimStyleTable;
    AcDbViewportTable* m_viewportTable;
    AcDbUCSTable* m_ucsTable;
    AcDbViewTable* m_viewTable;
    AcDbRegAppTable* m_regAppTable;

    // Current drawing state
    AcDbObjectId m_currentLayerId;
    AcDbObjectId m_currentTextStyleId;
    AcDbObjectId m_currentLinetypeId;
    AcDbObjectId m_currentDimStyleId;
    AcDbObjectId m_currentBlockId;

    // Advanced state management
    struct DrawingState {
        AcDbObjectId layerId;
        AcDbObjectId textStyleId;
        AcDbObjectId linetypeId;
        AcDbObjectId dimStyleId;
        AcCmColor color;
        double lineWeight;
        double lineTypeScale;
        AcDb::LineWeight entityLineWeight;
        AcDb::Visibility visibility;
    };

    DrawingState m_currentState;
    std::stack<DrawingState> m_stateStack;

    // Block definition state
    bool m_inBlockDefinition;
    AcDbBlockTableRecord* m_currentBlockRecord;
    
    // Helper methods
    bool InitializeRealDWG();
    void CleanupRealDWG();
    bool CreateDefaultLayers();
    bool CreateDefaultTextStyles();
    bool CreateDefaultLinetypes();
    
    // Conversion helpers
    AcGePoint3d ToAcGePoint3d(const Point3d& point) const;
    AcGeVector3d ToAcGeVector3d(const Vector3d& vector) const;
    AcCmColor ToAcCmColor(const Color& color) const;
    
    // Entity creation helpers
    AcDbObjectId AddEntityToModelSpace(AcDbEntity* entity);
    AcDbObjectId AddEntityToBlock(AcDbEntity* entity, const std::string& blockName);
    bool SetEntityProperties(AcDbEntity* entity, const std::string& layer);
    
    // Layer management
    AcDbObjectId GetOrCreateLayer(const std::string& layerName, const Color& color = Color());
    bool SetCurrentLayer(const std::string& layerName);
    
    // Block management
    AcDbObjectId GetOrCreateBlock(const std::string& blockName);
    bool SetCurrentBlock(const std::string& blockName);
#endif

    //===================================================================================
    // Export State Management
    //===================================================================================

    struct ExportState {
        std::string outputPath;
        DWGExportOptions::DWGVersion version;
        bool initialized;
        bool finalized;
        
        // Statistics
        size_t entitiesCreated;
        size_t layersCreated;
        size_t blocksCreated;
        
        // Error tracking
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };

    ExportState m_state;
    std::shared_ptr<ExportContext> m_context;
    
    // Layer and block caching
    std::unordered_map<std::string, std::string> m_layerCache;  // name -> objectId
    std::unordered_map<std::string, std::string> m_blockCache;  // name -> objectId
    
    //===================================================================================
    // iModel Element Processing
    //===================================================================================

    // Main processing methods
    bool ProcessIModelElements(const IModelDb& imodel, ProgressCallback progressCallback);
    bool ProcessElement(const ElementInfo& element);
    
    // Element type processors
    bool ProcessGeometricElement(const ElementInfo& element);
    bool ProcessSpatialElement(const ElementInfo& element);
    bool ProcessPhysicalElement(const ElementInfo& element);
    bool ProcessAnnotationElement(const ElementInfo& element);
    
    // Geometry processors
    bool ProcessLineGeometry(const ElementInfo& element);
    bool ProcessArcGeometry(const ElementInfo& element);
    bool ProcessPolylineGeometry(const ElementInfo& element);
    bool ProcessSolidGeometry(const ElementInfo& element);
    bool ProcessSurfaceGeometry(const ElementInfo& element);
    bool ProcessMeshGeometry(const ElementInfo& element);
    
    // Property processors
    bool ProcessElementProperties(const ElementInfo& element);
    bool ProcessMaterialProperties(const ElementInfo& element);
    bool ProcessDisplayProperties(const ElementInfo& element);
    
    //===================================================================================
    // Coordinate System and Units
    //===================================================================================

    // Coordinate transformation
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    double TransformLength(double length) const;
    
    // Units conversion
    bool SetupUnitsConversion(const IModelDb& imodel);
    double GetUnitsScale() const;
    
    //===================================================================================
    // Error Handling and Logging
    //===================================================================================

    void LogError(const std::string& message);
    void LogWarning(const std::string& message);
    void LogInfo(const std::string& message);
    
    bool HandleError(const std::string& elementId, const std::string& error);
    bool ShouldContinueOnError() const;
    
    //===================================================================================
    // Progress Reporting
    //===================================================================================

    bool UpdateProgress(ProgressCallback callback, double percentage, const std::string& operation);
    bool CheckCancellation(ProgressCallback callback);
    
    //===================================================================================
    // Utility Methods
    //===================================================================================

    std::string GenerateUniqueLayerName(const std::string& baseName) const;
    std::string GenerateUniqueBlockName(const std::string& baseName) const;
    std::string SanitizeName(const std::string& name) const;
    
    bool FileExists(const std::string& path) const;
    bool CreateDirectoryIfNeeded(const std::string& path) const;
    std::string GetTemporaryFileName() const;
};

//=======================================================================================
// DWG Host Application Services
//=======================================================================================

#ifdef REALDWG_AVAILABLE
class DWGHostAppServices : public AcDbHostApplicationServices {
public:
    DWGHostAppServices();
    virtual ~DWGHostAppServices();

    // Required overrides
    virtual Acad::ErrorStatus findFile(
        ACHAR* pcFullPathOut, 
        int nBufferLength,
        const ACHAR* pcFilename,
        AcDbDatabase* pDb = nullptr,
        AcDbHostApplicationServices::FindFileHint hint = kDefault) override;

    virtual const ACHAR* program() override;
    virtual const ACHAR* product() override;
    virtual const ACHAR* companyName() override;
    virtual const ACHAR* prodcode() override;
    virtual const ACHAR* releasemarker() override;
    virtual int versionNumber() override;

private:
    std::string m_programName;
    std::string m_productName;
    std::string m_companyName;
};
#endif

} // namespace IModelExport
