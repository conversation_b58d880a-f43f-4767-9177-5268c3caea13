/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdFace.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtFace : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbFace*           acFace = AcDbFace::cast (acObject);
    DPoint3d            points[5];
    int                 nPoints;
    bool                invisibleEdgeFound = false;

    for (int iPoint=0; iPoint<4; iPoint++)
        {
        AcGePoint3d         acPoint;

        acFace->getVertexAt (iPoint, acPoint);
        RealDwgUtil::DPoint3dFromGePoint3d (points[iPoint], acPoint);
        }

    // starting with last edge, check for nontrivial invisible edges
    for (int iPoint=3, jPoint=0; jPoint<4; iPoint=jPoint++)
        {
        Adesk::Boolean  isVisible;
        if (Acad::eOk != acFace->isEdgeVisibleAt (iPoint, isVisible))
            return InvalidFacePointCount;

        if (!points[iPoint].isEqual (&points[jPoint]) && !isVisible)
            {
            invisibleEdgeFound = true;
            break;
            }
        }

    nPoints = points[2].isEqual (&points[3]) ? 3 : 4;

    // if a nontrivial invisible edge is found, make a polyface-style mesh element instead
    if (invisibleEdgeFound)
        {
        int indices[5];

        // Fill index array based on hide flags
        for (int iPoint = 0; iPoint < nPoints; iPoint++)
            {
            Adesk::Boolean  isVisible = false;
            acFace->isEdgeVisibleAt (iPoint, isVisible);

            indices[iPoint] = isVisible ? (iPoint+1) : -(iPoint+1);
            }

        // ACAD triangles are quads with v[2]=v[3] (the edge is trivial).
        // Hence, the last edge of a triangle is actually (v[3],v[0]) and its hide
        // flag is the 8 bit, not the 4 bit, which controls the trivial edge.
        if (nPoints == 3)
            {
            Adesk::Boolean  isVisible = false;
            acFace->isEdgeVisibleAt (3, isVisible);

            indices[2] = isVisible ? 3 : -3;
            }

        // Create mesh consisting of 1 quad (or "triangle") w/ invisible edge flags
        context.GetTransformToDGN().Multiply (points, nPoints);

        PolyfaceHeaderPtr   dgnPolyface = PolyfaceHeader::CreateVariableSizeIndexed ();
        if (!dgnPolyface.IsValid())
            return  CantCreateMesh;

        dgnPolyface->Point().Append (points, nPoints);

        // append a trailing 0 index for the face, as we are not using a blocked index array here
        indices[nPoints++] = 0;
        dgnPolyface->PointIndex().Append (indices, nPoints);
        if (SUCCESS != MeshHeaderHandler::CreateMeshElement(outElement, NULL, *dgnPolyface, context.GetThreeD(), *context.GetModel()))
            return  CantCreateMesh;
        }
    else
        {
        points[nPoints++] = points[0];
        if (RealDwgSuccess != context.CreateElementFromVertices(outElement, points, nPoints, true, context.GetTransformToDGN()))
            return  CantCreateMesh;
        }

    context.ElementHeaderFromEntity (outElement, acFace);

    return RealDwgSuccess;
    }

};  // ToDgnExtFace
