/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFileIO.h $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

///For dealing with import/exporting functions.
#include <DgnPlatform\DgnFileIO\dgnfileio.h>
#include <RmgrTools\Tools\mdlResource.h>

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
struct RealDwgFileType : public DgnPlatform::DgnFileType
    {
                                RealDwgFileType () : DgnFileType (DgnPlatform::DgnFileFormatType::DWG) {}
    DgnFileIOP                  Factory (DgnFileP pFile) override;
//    virtual int                 GetFormat () override  { return DgnFileFormatType::DWG; }
    virtual bool                ValidateFile (DgnFileFormatType *pFormat, int *pMajorVersion, int *pMinorVersion, bool *pDefaultModelIs3D, IThumbnailPropertyValuePtr*, const WChar *pName) override;
    virtual void                GetCapabilities (DgnPlatform::DgnFileCapabilities *cap) override;

    static bool                 IsDwgFile (int *pVersion, const char *pData, int dataSize);
    static int                  AcadVersionFromString (const char *pData);
    static IThumbnailPropertyValuePtr ReadThumbnail (WCharCP fileName);
    };

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
struct RealDxfFileType : public DgnPlatform::DgnFileType
    {
                                RealDxfFileType () : DgnFileType (DgnPlatform::DgnFileFormatType::DXF) {}
    DgnFileIOP                  Factory (DgnFileP pFile) override;
    virtual bool                ValidateFile (DgnFileFormatType *pFormat, int *pMajorVersion, int *pMinorVersion, bool *pDefaultModelIs3D, IThumbnailPropertyValuePtr*, const WChar *pName) override;
    virtual void                GetCapabilities (DgnPlatform::DgnFileCapabilities *cap) override;

    static bool                 IsDxfFile (int *pVersion, const char *pData, int dataSize);
    static bool                 IsBinaryDxfFile (int *pVersion, const char *pData, int dataSize);
    };

class   FileHolder;
struct  RealDwgModelIndexItem;

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
class RealDwgFileIO : public DgnPlatform::DgnFileIO
    {
private:
    DgnFileFormatType           m_format;
    FileHolder*                 m_dwgFileHolder;
    WChar                       m_dgnSeedFileName[DGNPLATFORM_RESOURCE_MAXFILELENGTH];
    WChar                       m_dwgSeedFileName[DGNPLATFORM_RESOURCE_MAXFILELENGTH];
    bool                        m_createNewFile;
    bool                        m_fileHolderReleased;

    // Methods from DgnFileIO that we override
protected:
    virtual bool                _CanExclusiveLock () override                       { return true; }
    virtual StatusInt           _OnCreateFile (DgnPlatform::DesignFileHeader* pHdr, WChar const* pFileName, DgnFileIO* pDonor) override;
    virtual StatusInt           _LoadModelIndex (IStorage* pRootStore, DgnPlatform::ModelIndex* pModelIndex) override;

    // methods specific to RealDwgFileIO
    void                        _SetDwgFileHolder (FileHolder* newFileHolder) { m_dwgFileHolder = newFileHolder; }

public:
                                RealDwgFileIO (DgnFileP pFile, DgnFileFormatType format);
    virtual                     ~RealDwgFileIO ();

    virtual DgnFileFormatType   GetFormat () override;        
    virtual StatusInt           GetModelReader (DgnPlatform::DgnModelReader** ppStream, DgnModelP pCache) override;
    virtual StatusInt           _CreateCacheAndLoadHeader (DgnModelP& cache, DgnPlatform::ModelId modelIDtoRead) override;
    virtual StatusInt           LoadFile (DgnPlatform::DesignFileHeader* pHdr, bool* openedReadonly, StatusInt* rwStatus, DgnPlatform::DgnFileOpenMode openMode, DgnFileLoadContextP) override;
    virtual StatusInt           DeleteModel (DgnPlatform::ModelId modelID) override;
    virtual StatusInt           LoadModelHeaders () override;
    virtual StatusInt           SaveDocumentProperty (int* propID, WCharCP nameP, int propType, int streamToUse, const void *value) override;
    virtual StatusInt           GetDocumentProperty (int* propIDP, WCharCP nameP, int* propType, int streamToUse, void* value) override;

    virtual StatusInt           CloseDgnFileIO () override;
    virtual void                FileReload (bool beforeReload) override;
    virtual void                ReleaseForeignSchema () override;
    virtual bool                IsForeignSchemaLoaded () override;

    // We use DgnFileIO's FileHandle/_ObtainExclusiveLock/_ReleaseExclusiveLock mechansism for maintaining the OS view of file read/write locks.
    virtual StatusInt           _WriteChanges (bool doFullSave, double timestamp, DgnPlatform::DesignFileHeader* hdr) override;
    virtual void                SaveThumbnail (IThumbnailPropertyValue const& thumbnail) override;
    virtual bool                IsOldDwgAecFile () override;  // SessionMgr checks for old version AEC file
    virtual bool                _IsReferenceActivationPermitted () override;


    // methods specific to RealDwgFileIO
    FileHolder*                 GetDwgFileHolder();
    StatusInt                   InitializeFileHolderFromSeedFile();
    StatusInt                   SaveNonCacheChangesToDatabase (bool fullSave);
    StatusInt                   SaveModelChangesToDatabase (DgnModelP pCache, int modelId);
    void                        GetBackupFileName (WStringR backupFilename);
    StatusInt                   WriteDatabaseToFile (bool exportFonts);

    StatusInt                   CreateAcadDatabaseFromOpenFile (WCharCP fileName, bool omitRefPaths);
    StatusInt                   SaveAllModelsToAcadDatabase (WCharCP fileName, bool omitRefPaths);
    StatusInt                   SaveModelToAcadDatabase (DgnModelP modelRef, int modelId, WCharCP fileName, bool omitRefPaths);

    static bool                 Initialize();
    static void                 AddBigfontCodepageConfiguration ();
    static RealDwgStatus        ConvertMdlStatusToRealDwgStatus (StatusInt mdlStatus)   { return (RealDwgStatus) mdlStatus; }    // MDL uses different range, can just cast

private:
    StatusInt                   FindAndValidateDgnSeedFile();
    StatusInt                   FindAndValidateDwgSeedFile();
    StatusInt                   CreateDgnModel (DgnModelP& model, RealDwgModelIndexItem& modelItem);
    StatusInt                   ReadDocumentPropertyFromDwg (void* outValue, int* outPropType, int propId, WCharCP key);
    };

}   // Ends RealDwg namespace

END_BENTLEY_NAMESPACE

