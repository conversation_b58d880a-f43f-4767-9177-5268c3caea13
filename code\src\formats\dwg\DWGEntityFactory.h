#pragma once

#include "DWGEntityTypes.h"
#include "DWGEntityCreator.h"
#include "DWGEntityProcessor.h"
#include "../../core/GeometryProcessor.h"
#include "../../../include/ExportTypes.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbents.h>
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dbdictionary.h>
#include <realdwg/base/dbxrecord.h>
#include <realdwg/base/dbapserv.h>
#include <realdwg/base/dbfiler.h>
#include <realdwg/base/dbsymutl.h>
#include <realdwg/base/dbboiler.h>
#include <realdwg/base/dbmstyle.h>
#include <realdwg/base/dbdimdata.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/base/dbassoc.h>
#include <realdwg/base/dbassocaction.h>
#include <realdwg/base/dbassocnetwork.h>
#include <realdwg/base/dbassocgeom.h>
#include <realdwg/base/dbassocperssubentmanager.h>
#include <realdwg/base/dbassocvariable.h>
#include <realdwg/base/dbassocvaluedepdency.h>
#include <realdwg/base/dbassocgeomdependency.h>
#endif

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>

namespace IModelExport {

//=======================================================================================
// DWG Entity Type Registry - Complete AutoCAD Entity Type Mapping
//=======================================================================================

class DWGEntityTypeRegistry {
public:
    static DWGEntityTypeRegistry& Instance();

    // Entity type detection
    DWGEntityType GetEntityType(void* entity) const;
    std::string GetEntityTypeName(DWGEntityType type) const;
    std::string GetEntityClassName(DWGEntityType type) const;
    
    // Entity type information
    bool IsGeometricEntity(DWGEntityType type) const;
    bool IsAnnotationEntity(DWGEntityType type) const;
    bool Is3DEntity(DWGEntityType type) const;
    bool IsSurfaceEntity(DWGEntityType type) const;
    bool IsSolidEntity(DWGEntityType type) const;
    bool IsMeshEntity(DWGEntityType type) const;
    bool IsBlockEntity(DWGEntityType type) const;
    bool IsLightEntity(DWGEntityType type) const;
    bool IsRenderingEntity(DWGEntityType type) const;
    
    // Entity categories
    std::vector<DWGEntityType> GetGeometricEntityTypes() const;
    std::vector<DWGEntityType> GetAnnotationEntityTypes() const;
    std::vector<DWGEntityType> Get3DEntityTypes() const;
    std::vector<DWGEntityType> GetSurfaceEntityTypes() const;
    std::vector<DWGEntityType> GetSolidEntityTypes() const;
    std::vector<DWGEntityType> GetMeshEntityTypes() const;
    std::vector<DWGEntityType> GetBlockEntityTypes() const;
    std::vector<DWGEntityType> GetLightEntityTypes() const;
    std::vector<DWGEntityType> GetRenderingEntityTypes() const;
    std::vector<DWGEntityType> GetAllEntityTypes() const;
    
    // Entity validation
    bool IsValidEntityType(DWGEntityType type) const;
    bool IsEntityTypeSupported(DWGEntityType type) const;
    
    // Entity creation information
    bool RequiresSpecialHandling(DWGEntityType type) const;
    bool SupportsAttributes(DWGEntityType type) const;
    bool SupportsMaterials(DWGEntityType type) const;
    bool SupportsLayers(DWGEntityType type) const;
    bool SupportsLinetypes(DWGEntityType type) const;
    bool SupportsColors(DWGEntityType type) const;
    bool SupportsLineWeights(DWGEntityType type) const;
    bool SupportsTransparency(DWGEntityType type) const;
    bool SupportsVisibility(DWGEntityType type) const;

private:
    DWGEntityTypeRegistry();
    void InitializeEntityTypeMap();
    void InitializeEntityCategories();
    void InitializeEntityCapabilities();

#ifdef REALDWG_AVAILABLE
    std::unordered_map<std::string, DWGEntityType> m_classNameToType;
    std::unordered_map<DWGEntityType, std::string> m_typeToClassName;
#endif
    
    std::unordered_map<DWGEntityType, std::string> m_typeToName;
    std::unordered_map<DWGEntityType, bool> m_geometricEntities;
    std::unordered_map<DWGEntityType, bool> m_annotationEntities;
    std::unordered_map<DWGEntityType, bool> m_3dEntities;
    std::unordered_map<DWGEntityType, bool> m_surfaceEntities;
    std::unordered_map<DWGEntityType, bool> m_solidEntities;
    std::unordered_map<DWGEntityType, bool> m_meshEntities;
    std::unordered_map<DWGEntityType, bool> m_blockEntities;
    std::unordered_map<DWGEntityType, bool> m_lightEntities;
    std::unordered_map<DWGEntityType, bool> m_renderingEntities;
    
    std::unordered_map<DWGEntityType, bool> m_supportsAttributes;
    std::unordered_map<DWGEntityType, bool> m_supportsMaterials;
    std::unordered_map<DWGEntityType, bool> m_supportsLayers;
    std::unordered_map<DWGEntityType, bool> m_supportsLinetypes;
    std::unordered_map<DWGEntityType, bool> m_supportsColors;
    std::unordered_map<DWGEntityType, bool> m_supportsLineWeights;
    std::unordered_map<DWGEntityType, bool> m_supportsTransparency;
    std::unordered_map<DWGEntityType, bool> m_supportsVisibility;
    std::unordered_map<DWGEntityType, bool> m_requiresSpecialHandling;
};

//=======================================================================================
// DWG Entity Factory - Comprehensive DWG Entity Management
//=======================================================================================

class DWGEntityFactory {
public:
    DWGEntityFactory();
    ~DWGEntityFactory();

    //===================================================================================
    // Factory Configuration
    //===================================================================================

    void SetDatabase(void* database);
    void* GetDatabase() const;
    
    void SetGeometryProcessor(std::shared_ptr<GeometryProcessor> processor);
    std::shared_ptr<GeometryProcessor> GetGeometryProcessor() const;
    
    void SetOutputFormat(ExportFormat format);
    ExportFormat GetOutputFormat() const;

    //===================================================================================
    // Entity Creation Interface
    //===================================================================================

    std::shared_ptr<DWGEntityCreator> GetEntityCreator();
    std::shared_ptr<DWGEntityProcessor> GetEntityProcessor();
    
    // Create entities by type
    bool CreateEntity(DWGEntityType type, const std::vector<Point3d>& points, 
                     const std::unordered_map<std::string, std::string>& properties = {});
    
    // Process entities by type
    bool ProcessEntity(void* entity, DWGEntityType type = DWGEntityType::Unknown);
    bool ProcessEntities(const std::vector<void*>& entities);
    
    // Batch operations
    bool ProcessDatabase(void* database, const DWGProcessingOptions& options = DWGProcessingOptions());
    bool ProcessModelSpace(void* database, const DWGProcessingOptions& options = DWGProcessingOptions());
    bool ProcessPaperSpace(void* database, const std::string& layoutName, 
                          const DWGProcessingOptions& options = DWGProcessingOptions());

    //===================================================================================
    // Entity Type Specific Creation (High-Level Interface)
    //===================================================================================

    // Basic 2D geometry
    bool CreateLine(const Point3d& start, const Point3d& end, const std::string& layer = "");
    bool CreateCircle(const Point3d& center, double radius, const std::string& layer = "");
    bool CreateArc(const Point3d& center, double radius, double startAngle, double endAngle, const std::string& layer = "");
    bool CreatePolyline(const std::vector<Point3d>& points, bool closed = false, const std::string& layer = "");
    bool CreateSpline(const std::vector<Point3d>& controlPoints, int degree = 3, const std::string& layer = "");
    
    // Text and annotations
    bool CreateText(const Point3d& position, const std::string& text, double height, const std::string& layer = "");
    bool CreateMText(const Point3d& position, const std::string& text, double width, double height, const std::string& layer = "");
    
    // Dimensions
    bool CreateLinearDimension(const Point3d& start, const Point3d& end, const Point3d& textPos, const std::string& layer = "");
    bool CreateRadialDimension(const Point3d& center, const Point3d& chordPoint, double leaderLength, const std::string& layer = "");
    
    // 3D solids
    bool CreateBox(const Point3d& corner, double length, double width, double height, const std::string& layer = "");
    bool CreateCylinder(const Point3d& center, double radius, double height, const std::string& layer = "");
    bool CreateSphere(const Point3d& center, double radius, const std::string& layer = "");
    
    // Surfaces
    bool CreatePlaneSurface(const Point3d& corner, const Vector3d& uVector, const Vector3d& vVector,
                           double uLength, double vLength, const std::string& layer = "");
    
    // Meshes
    bool CreateMesh(const std::vector<Point3d>& vertices, const std::vector<int>& indices, const std::string& layer = "");
    
    // Blocks and references
    bool CreateBlockReference(const std::string& blockName, const Point3d& position, const std::string& layer = "");
    
    // Images and OLE
    bool CreateRasterImage(const std::string& imagePath, const Point3d& position, 
                          double width, double height, const std::string& layer = "");

    //===================================================================================
    // Entity Conversion and Translation
    //===================================================================================

    // Convert between entity types
    bool ConvertEntity(void* sourceEntity, DWGEntityType targetType, void** targetEntity);
    
    // Translate entities between coordinate systems
    bool TransformEntity(void* entity, const Transform3d& transform);
    bool TransformEntities(const std::vector<void*>& entities, const Transform3d& transform);
    
    // Copy and clone entities
    void* CloneEntity(void* entity);
    std::vector<void*> CloneEntities(const std::vector<void*>& entities);

    //===================================================================================
    // Entity Analysis and Information
    //===================================================================================

    // Entity properties
    DWGEntityType GetEntityType(void* entity) const;
    std::string GetEntityTypeName(void* entity) const;
    std::string GetEntityLayer(void* entity) const;
    Color GetEntityColor(void* entity) const;
    std::string GetEntityLinetype(void* entity) const;
    double GetEntityLineWeight(void* entity) const;
    
    // Entity geometry
    BoundingBox GetEntityBounds(void* entity) const;
    Point3d GetEntityCentroid(void* entity) const;
    double GetEntityArea(void* entity) const;
    double GetEntityLength(void* entity) const;
    double GetEntityVolume(void* entity) const;
    
    // Entity validation
    bool IsEntityValid(void* entity) const;
    bool IsEntityVisible(void* entity) const;
    bool IsEntityLocked(void* entity) const;
    bool IsEntityFrozen(void* entity) const;
    
    // Entity relationships
    std::vector<void*> GetEntityDependencies(void* entity) const;
    std::vector<void*> GetEntityReferences(void* entity) const;
    void* GetEntityOwner(void* entity) const;

    //===================================================================================
    // Statistics and Reporting
    //===================================================================================

    const DWGProcessingStats& GetProcessingStats() const;
    void ResetProcessingStats();
    
    std::unordered_map<DWGEntityType, size_t> GetEntityCounts() const;
    std::unordered_map<std::string, size_t> GetLayerCounts() const;
    std::unordered_map<std::string, size_t> GetMaterialCounts() const;
    
    size_t GetTotalEntitiesCreated() const;
    size_t GetTotalEntitiesProcessed() const;
    
    // Performance metrics
    double GetAverageCreationTime() const;
    double GetAverageProcessingTime() const;
    double GetTotalProcessingTime() const;

    //===================================================================================
    // Error Handling and Logging
    //===================================================================================

    void SetProgressCallback(DWGEntityProcessor::ProgressCallback callback);
    void SetErrorCallback(DWGEntityProcessor::ErrorCallback callback);
    void ClearCallbacks();
    
    std::vector<std::string> GetLastErrors() const;
    std::vector<std::string> GetLastWarnings() const;
    void ClearErrorLog();

private:
    //===================================================================================
    // Internal State
    //===================================================================================

    std::shared_ptr<DWGEntityCreator> m_entityCreator;
    std::shared_ptr<DWGEntityProcessor> m_entityProcessor;
    std::shared_ptr<GeometryProcessor> m_geometryProcessor;
    
    void* m_database;
    ExportFormat m_outputFormat;
    
    // Error tracking
    std::vector<std::string> m_lastErrors;
    std::vector<std::string> m_lastWarnings;
    
    // Performance tracking
    mutable std::chrono::high_resolution_clock::time_point m_lastOperationStart;
    mutable double m_totalCreationTime;
    mutable double m_totalProcessingTime;
    mutable size_t m_creationOperations;
    mutable size_t m_processingOperations;

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    void InitializeComponents();
    void LogError(const std::string& error);
    void LogWarning(const std::string& warning);
    
    void StartTiming() const;
    void EndTiming(bool isCreation) const;
    
    bool ValidateDatabase() const;
    bool ValidateEntity(void* entity) const;
    bool ValidatePoints(const std::vector<Point3d>& points, DWGEntityType type) const;
    
#ifdef REALDWG_AVAILABLE
    // RealDWG specific helpers
    AcDbDatabase* GetAcDbDatabase() const;
    AcDbEntity* GetAcDbEntity(void* entity) const;
    bool IsAcDbEntityValid(AcDbEntity* entity) const;
#endif
};

//=======================================================================================
// DWG Entity Factory Manager - Singleton for Global Access
//=======================================================================================

class DWGEntityFactoryManager {
public:
    static DWGEntityFactoryManager& Instance();
    
    std::shared_ptr<DWGEntityFactory> GetFactory(const std::string& name = "default");
    std::shared_ptr<DWGEntityFactory> CreateFactory(const std::string& name);
    void RemoveFactory(const std::string& name);
    void ClearFactories();
    
    std::vector<std::string> GetFactoryNames() const;
    size_t GetFactoryCount() const;

private:
    DWGEntityFactoryManager() = default;
    std::unordered_map<std::string, std::shared_ptr<DWGEntityFactory>> m_factories;
};

} // namespace IModelExport
