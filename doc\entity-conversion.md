# Entity Conversion Mappings

## Overview

The RealDwgFileIO framework provides comprehensive entity conversion between AutoCAD and MicroStation formats. Each entity type has dedicated conversion logic that handles geometry, properties, and symbology mapping.

## Entity Conversion Matrix

| AutoCAD Entity | DGN Element | Converter File | Complexity |
|----------------|-------------|----------------|------------|
| AcDbLine | Line Element | rdLine.cpp | Simple |
| AcDbArc | Arc Element | rdArc.cpp | Simple |
| AcDbCircle | Ellipse Element | rdCircle.cpp | Simple |
| AcDbEllipse | Ellipse Element | rdEllipse.cpp | Medium |
| AcDbPolyline | LineString/Shape | rdPolyline.cpp | Medium |
| AcDb2dPolyline | Complex LineString | rd2dPolyline.cpp | Medium |
| AcDb3dPolyline | 3D LineString | rd3dPolyline.cpp | Medium |
| AcDbSpline | B-spline Curve | rdSpline.cpp | Complex |
| AcDbText | Text Element | rdText.cpp | Medium |
| AcDbMText | Text Node | rdMText.cpp | Complex |
| AcDbHatch | Pattern/Hatch | rdHatch.cpp | Complex |
| AcDbDimension | Dimension Element | rdDimension.cpp | Complex |
| AcDbBlockReference | Cell Element | rdBlockReference.cpp | Complex |
| AcDb3dSolid | Solid Element | rdSolid.cpp | Complex |
| AcDbSurface | Surface Element | rdSurface.cpp | Complex |
| AcDbSubDMesh | Mesh Element | rdSubDMesh.cpp | Complex |

## Geometric Entity Conversions

### Linear Entities

#### Line Conversion (rdLine.cpp)
```cpp
// DWG → DGN
void ConvertToDgnContext::ConvertLine(AcDbLine* acLine, MSElementDescrP* ppDescr)
{
    AcGePoint3d startPoint, endPoint;
    acLine->getStartPoint(startPoint);
    acLine->getEndPoint(endPoint);
    
    DPoint3d dgnStart, dgnEnd;
    RealDwgUtil::DPoint3dFromGePoint3d(dgnStart, startPoint);
    RealDwgUtil::DPoint3dFromGePoint3d(dgnEnd, endPoint);
    
    // Apply coordinate transformation
    GetTransformToDGN().Multiply(dgnStart);
    GetTransformToDGN().Multiply(dgnEnd);
    
    // Create DGN line element
    mdlLine_create(ppDescr, NULL, &dgnStart, &dgnEnd);
    
    // Apply symbology
    ApplySymbologyToDgnElement(*ppDescr, acLine);
}

// DGN → DWG  
void ConvertFromDgnContext::ConvertLine(MSElementDescrCP pDescr, AcDbObjectId* outId)
{
    LineElement* lineElem = (LineElement*)pDescr;
    DPoint3d start = lineElem->start;
    DPoint3d end = lineElem->end;
    
    // Apply coordinate transformation
    GetTransformFromDGN().multiply(&start, &start, 1);
    GetTransformFromDGN().multiply(&end, &end, 1);
    
    // Create AutoCAD line
    AcDbLine* acLine = new AcDbLine(
        RealDwgUtil::GePoint3dFromDPoint3d(start),
        RealDwgUtil::GePoint3dFromDPoint3d(end)
    );
    
    // Apply symbology and add to database
    ApplySymbologyToAcadEntity(acLine, pDescr);
    AddEntityToDatabase(acLine, outId);
}
```

#### Arc Conversion (rdArc.cpp)
```cpp
void ConvertToDgnContext::ConvertArc(AcDbArc* acArc, MSElementDescrP* ppDescr)
{
    // Extract arc parameters
    AcGePoint3d center = acArc->center();
    double radius = acArc->radius();
    double startAngle = acArc->startAngle();
    double endAngle = acArc->endAngle();
    AcGeVector3d normal = acArc->normal();
    
    // Transform to DGN coordinates
    DPoint3d dgnCenter;
    RealDwgUtil::DPoint3dFromGePoint3d(dgnCenter, center);
    GetTransformToDGN().Multiply(dgnCenter);
    
    double dgnRadius = radius * GetScaleToDGN();
    
    // Handle coordinate system orientation
    RotMatrix rMatrix;
    RealDwgUtil::RotMatrixFromArbitraryAxis(rMatrix, normal);
    
    // Create DGN arc element
    mdlArc_create(ppDescr, NULL, &dgnCenter, dgnRadius, dgnRadius, 
                  &rMatrix, startAngle, endAngle);
    
    ApplySymbologyToDgnElement(*ppDescr, acArc);
}
```

### Complex Geometric Entities

#### Polyline Conversion (rdPolyline.cpp)
Handles various polyline types with different vertex data:

```cpp
void ConvertToDgnContext::ConvertPolyline(AcDbPolyline* acPoly, MSElementDescrP* ppDescr)
{
    int numVertices = acPoly->numVerts();
    DPoint3d* vertices = new DPoint3d[numVertices];
    
    // Extract vertices and bulge factors
    for (int i = 0; i < numVertices; i++) {
        AcGePoint3d vertex;
        acPoly->getPointAt(i, vertex);
        
        RealDwgUtil::DPoint3dFromGePoint3d(vertices[i], vertex);
        GetTransformToDGN().Multiply(vertices[i]);
        
        // Handle bulge factors for arc segments
        double bulge = acPoly->getBulgeAt(i);
        if (fabs(bulge) > TOLERANCE_VectorEqual) {
            // Convert to arc segment
            ConvertPolylineArcSegment(i, bulge, vertices);
        }
    }
    
    // Create appropriate DGN element based on properties
    if (acPoly->isClosed()) {
        mdlShape_create(ppDescr, NULL, vertices, numVertices);
    } else {
        mdlLineString_create(ppDescr, NULL, vertices, numVertices);
    }
    
    delete[] vertices;
    ApplySymbologyToDgnElement(*ppDescr, acPoly);
}
```

#### Spline Conversion (rdSpline.cpp)
Handles NURBS curve conversion:

```cpp
void ConvertToDgnContext::ConvertSpline(AcDbSpline* acSpline, MSElementDescrP* ppDescr)
{
    // Extract NURBS data
    int degree;
    Adesk::Boolean rational, closed, periodic;
    AcGePoint3dArray controlPoints;
    AcGeDoubleArray knots, weights;
    
    acSpline->getNurbsData(degree, rational, closed, periodic,
                          controlPoints, knots, weights);
    
    // Convert to DGN B-spline curve
    MSBsplineCurve bsplineCurve;
    bsplineCurve.params.order = degree + 1;
    bsplineCurve.params.numPoles = controlPoints.length();
    bsplineCurve.params.numKnots = knots.length();
    
    // Allocate and fill control points
    bsplineCurve.poles = (DPoint3d*)dlmSystem_calloc(
        bsplineCurve.params.numPoles, sizeof(DPoint3d));
    
    for (int i = 0; i < controlPoints.length(); i++) {
        RealDwgUtil::DPoint3dFromGePoint3d(bsplineCurve.poles[i], 
                                          controlPoints[i]);
        GetTransformToDGN().Multiply(bsplineCurve.poles[i]);
    }
    
    // Convert knot vector and weights
    ConvertKnotVector(knots, &bsplineCurve);
    ConvertWeights(weights, rational, &bsplineCurve);
    
    // Create DGN B-spline element
    mdlBspline_createCurve(ppDescr, NULL, &bsplineCurve);
    
    ApplySymbologyToDgnElement(*ppDescr, acSpline);
}
```

## Text Entity Conversions

### Simple Text (rdText.cpp)
```cpp
void ConvertToDgnContext::ConvertText(AcDbText* acText, MSElementDescrP* ppDescr)
{
    // Extract text properties
    AcString textString = acText->textString();
    AcGePoint3d position = acText->position();
    double height = acText->height();
    double rotation = acText->rotation();
    
    // Convert to DGN coordinates
    DPoint3d dgnPosition;
    RealDwgUtil::DPoint3dFromGePoint3d(dgnPosition, position);
    GetTransformToDGN().Multiply(dgnPosition);
    
    double dgnHeight = height * GetScaleToDGN();
    
    // Create DGN text element
    WString wideText = WString(textString.kwszPtr());
    mdlText_create(ppDescr, NULL, wideText.GetWCharCP(), 
                   &dgnPosition, dgnHeight, rotation);
    
    // Apply text style and symbology
    ApplyTextStyleToDgnElement(*ppDescr, acText);
    ApplySymbologyToDgnElement(*ppDescr, acText);
}
```

### Multiline Text (rdMText.cpp)
More complex text handling with formatting:

```cpp
void ConvertToDgnContext::ConvertMText(AcDbMText* acMText, MSElementDescrP* ppDescr)
{
    // Extract formatted text content
    AcString contents = acMText->contents();
    AcGePoint3d location = acMText->location();
    double textHeight = acMText->textHeight();
    double width = acMText->width();
    
    // Parse formatting codes
    TextFormatInfo formatInfo;
    ParseMTextFormatting(contents, formatInfo);
    
    // Create DGN text node
    TextNodeElement textNode;
    InitializeTextNode(&textNode, location, textHeight, width);
    
    // Add formatted text runs
    for (auto& run : formatInfo.textRuns) {
        AddTextRunToNode(&textNode, run);
    }
    
    // Convert to element descriptor
    ConvertTextNodeToDescriptor(&textNode, ppDescr);
    
    ApplySymbologyToDgnElement(*ppDescr, acMText);
}
```

## Complex Entity Conversions

### Block Reference (rdBlockReference.cpp)
```cpp
void ConvertToDgnContext::ConvertBlockReference(AcDbBlockReference* blockRef, 
                                               MSElementDescrP* ppDescr)
{
    // Get block definition
    AcDbObjectId blockId = blockRef->blockTableRecord();
    AcDbBlockTableRecordPointer blockRecord(blockId, AcDb::kForRead);
    
    // Convert block definition to cell definition if needed
    ElementId cellDefId = GetOrCreateCellDefinition(blockRecord);
    
    // Extract insertion parameters
    AcGePoint3d insertionPoint = blockRef->position();
    AcGeScale3d scaleFactors = blockRef->scaleFactors();
    double rotation = blockRef->rotation();
    
    // Transform to DGN coordinates
    DPoint3d dgnInsertion;
    RealDwgUtil::DPoint3dFromGePoint3d(dgnInsertion, insertionPoint);
    GetTransformToDGN().Multiply(dgnInsertion);
    
    // Create DGN cell element
    Transform cellTransform;
    BuildCellTransform(&cellTransform, dgnInsertion, scaleFactors, rotation);
    
    mdlCell_create(ppDescr, NULL, cellDefId, &dgnInsertion, &cellTransform);
    
    // Handle attribute references
    ConvertAttributeReferences(blockRef, *ppDescr);
    
    ApplySymbologyToDgnElement(*ppDescr, blockRef);
}
```

### Hatch Patterns (rdHatch.cpp)
```cpp
void ConvertToDgnContext::ConvertHatch(AcDbHatch* acHatch, MSElementDescrP* ppDescr)
{
    // Determine hatch type
    AcDbHatch::HatchStyle hatchStyle = acHatch->hatchStyle();
    
    if (hatchStyle == AcDbHatch::kNormal) {
        // Pattern hatch
        ConvertPatternHatch(acHatch, ppDescr);
    } else {
        // Solid fill
        ConvertSolidHatch(acHatch, ppDescr);
    }
}

void ConvertToDgnContext::ConvertPatternHatch(AcDbHatch* acHatch, 
                                             MSElementDescrP* ppDescr)
{
    // Extract boundary loops
    int numLoops = acHatch->numLoops();
    for (int i = 0; i < numLoops; i++) {
        AcDbHatch::LoopType loopType = acHatch->loopTypeAt(i);
        
        if (loopType & AcDbHatch::kPolyline) {
            ConvertPolylineLoop(acHatch, i, ppDescr);
        } else {
            ConvertEdgeLoop(acHatch, i, ppDescr);
        }
    }
    
    // Apply pattern definition
    ApplyHatchPattern(acHatch, *ppDescr);
}
```

## 3D Entity Conversions

### Solid Entities (rdSolid.cpp)
```cpp
void ConvertToDgnContext::ConvertSolid(AcDb3dSolid* acSolid, MSElementDescrP* ppDescr)
{
    // Extract ACIS data
    void* asmBody = nullptr;
    if (Acad::eOk == acSolid->getASMBody(asmBody)) {
        // Convert ACIS to Parasolid
        ConvertAcisToParasolid(asmBody, ppDescr);
    } else {
        // Fallback: tessellate to mesh
        TessellateSolidToMesh(acSolid, ppDescr);
    }
    
    ApplySymbologyToDgnElement(*ppDescr, acSolid);
}
```

## Conversion Utilities

### Coordinate Transformation
```cpp
class CoordinateTransformer {
    Transform m_toDgn;
    Transform m_fromDgn;
    double m_scale;
    
public:
    void TransformPoint(const AcGePoint3d& acPoint, DPoint3d& dgnPoint) {
        RealDwgUtil::DPoint3dFromGePoint3d(dgnPoint, acPoint);
        m_toDgn.Multiply(dgnPoint);
    }
    
    void TransformVector(const AcGeVector3d& acVector, DVec3d& dgnVector) {
        RealDwgUtil::DVec3dFromGeVector3d(dgnVector, acVector);
        m_toDgn.MultiplyMatrixOnly(dgnVector);
    }
};
```

### Symbology Application
```cpp
void ApplySymbologyToDgnElement(MSElementDescrP descr, AcDbEntity* entity) {
    // Color conversion
    AcCmColor acColor = entity->color();
    ColorDef dgnColor = ConvertColor(acColor);
    mdlElement_setColor(descr, dgnColor.GetColorIndex());
    
    // Linetype conversion
    AcDbObjectId linetypeId = entity->linetypeId();
    LineStyleParams lineStyle = ConvertLinetype(linetypeId);
    mdlElement_setLineStyle(descr, lineStyle.GetStyleId());
    
    // Layer/Level conversion
    AcDbObjectId layerId = entity->layerId();
    LevelId levelId = ConvertLayer(layerId);
    mdlElement_setLevel(descr, levelId);
}
```
