#pragma once

#include "../../include/ExportTypes.h"
#include "ElementProcessor.h"
#include "GeometryProcessor.h"
#include <memory>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>

namespace IModelExport {

// Forward declarations
class ExportContext;
class IModelDb;

//=======================================================================================
// Data Flow Manager - Orchestrates data flow through the export pipeline
//=======================================================================================

class DataFlowManager {
public:
    DataFlowManager(std::shared_ptr<ExportContext> context);
    ~DataFlowManager();

    //===================================================================================
    // Data Flow Configuration
    //===================================================================================

    // Flow stages
    enum class FlowStage {
        Input,          // Data input from iModel
        Extract,        // Extract geometry and properties
        Transform,      // Apply transformations
        Process,        // Process for target format
        Validate,       // Validate processed data
        Output,         // Output to target format
        Complete        // Flow complete
    };

    // Data flow configuration
    struct FlowConfig {
        size_t maxQueueSize = 1000;
        size_t batchSize = 100;
        size_t numWorkerThreads = 4;
        bool enableParallelProcessing = true;
        bool enableDataValidation = true;
        bool enableProgressReporting = true;
        double memoryThreshold = 0.8; // 80% of available memory
        std::string tempDirectory;
    };

    void SetFlowConfig(const FlowConfig& config);
    FlowConfig GetFlowConfig() const;

    //===================================================================================
    // Data Containers
    //===================================================================================

    // Data packet for flow processing
    struct DataPacket {
        std::string id;
        FlowStage stage;
        ElementInfo element;
        std::vector<GeometryData> geometries;
        std::vector<Material> materials;
        std::unordered_map<std::string, std::string> properties;
        Transform3d transform;
        std::chrono::system_clock::time_point timestamp;
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
        bool processed = false;
    };

    // Data queue for each stage
    using DataQueue = std::queue<std::shared_ptr<DataPacket>>;

    //===================================================================================
    // Flow Execution
    //===================================================================================

    // Start data flow
    bool StartFlow(const IModelDb& imodel, ExportFormat targetFormat);
    
    // Stop data flow
    void StopFlow();
    
    // Pause data flow
    void PauseFlow();
    
    // Resume data flow
    void ResumeFlow();
    
    // Check if flow is running
    bool IsFlowRunning() const;
    
    // Check if flow is paused
    bool IsFlowPaused() const;

    //===================================================================================
    // Data Input/Output
    //===================================================================================

    // Add data packet to input queue
    bool AddInputData(std::shared_ptr<DataPacket> packet);
    
    // Get processed data from output queue
    std::shared_ptr<DataPacket> GetOutputData();
    
    // Get all processed data
    std::vector<std::shared_ptr<DataPacket>> GetAllOutputData();
    
    // Clear all queues
    void ClearAllQueues();

    //===================================================================================
    // Flow Monitoring
    //===================================================================================

    // Flow statistics
    struct FlowStatistics {
        size_t totalPackets = 0;
        size_t processedPackets = 0;
        size_t errorPackets = 0;
        size_t queuedPackets = 0;
        double throughput = 0.0; // packets per second
        double averageProcessingTime = 0.0;
        size_t memoryUsage = 0;
        std::chrono::system_clock::time_point startTime;
        std::chrono::system_clock::time_point lastUpdateTime;
    };

    FlowStatistics GetFlowStatistics() const;
    
    // Get queue sizes
    std::unordered_map<FlowStage, size_t> GetQueueSizes() const;
    
    // Get processing progress
    double GetProcessingProgress() const;

    //===================================================================================
    // Flow Callbacks
    //===================================================================================

    // Callback types
    using StageCallback = std::function<bool(FlowStage, std::shared_ptr<DataPacket>)>;
    using ProgressCallback = std::function<bool(double, const std::string&)>;
    using ErrorCallback = std::function<void(FlowStage, const std::string&, std::shared_ptr<DataPacket>)>;

    void SetStageCallback(StageCallback callback);
    void SetProgressCallback(ProgressCallback callback);
    void SetErrorCallback(ErrorCallback callback);

    //===================================================================================
    // Memory Management
    //===================================================================================

    // Check memory usage
    size_t GetMemoryUsage() const;
    
    // Check if memory threshold exceeded
    bool IsMemoryThresholdExceeded() const;
    
    // Trigger memory cleanup
    void TriggerMemoryCleanup();
    
    // Set memory limit
    void SetMemoryLimit(size_t limitBytes);

    //===================================================================================
    // Error Handling
    //===================================================================================

    // Error recovery strategies
    enum class ErrorRecovery {
        Skip,           // Skip problematic packet
        Retry,          // Retry processing
        Fallback,       // Use fallback processing
        Abort           // Abort entire flow
    };

    // Handle processing error
    bool HandleProcessingError(std::shared_ptr<DataPacket> packet, const std::string& error,
                              ErrorRecovery strategy = ErrorRecovery::Skip);
    
    // Get error statistics
    std::unordered_map<FlowStage, size_t> GetErrorStatistics() const;

private:
    //===================================================================================
    // Internal Data Structures
    //===================================================================================

    std::shared_ptr<ExportContext> m_context;
    std::shared_ptr<ElementProcessor> m_elementProcessor;
    std::shared_ptr<GeometryProcessor> m_geometryProcessor;

    // Flow configuration
    FlowConfig m_config;
    ExportFormat m_targetFormat;

    // Flow state
    std::atomic<bool> m_flowRunning{false};
    std::atomic<bool> m_flowPaused{false};
    std::atomic<bool> m_stopRequested{false};

    // Data queues for each stage
    std::unordered_map<FlowStage, DataQueue> m_queues;
    mutable std::unordered_map<FlowStage, std::mutex> m_queueMutexes;
    std::unordered_map<FlowStage, std::condition_variable> m_queueConditions;

    // Worker threads
    std::vector<std::thread> m_workerThreads;
    std::mutex m_threadMutex;

    // Statistics
    mutable std::mutex m_statsMutex;
    FlowStatistics m_statistics;
    std::unordered_map<FlowStage, size_t> m_errorCounts;

    // Callbacks
    StageCallback m_stageCallback;
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;

    // Memory management
    std::atomic<size_t> m_memoryLimit{1024 * 1024 * 1024}; // 1GB default
    std::atomic<size_t> m_currentMemoryUsage{0};

    //===================================================================================
    // Worker Thread Functions
    //===================================================================================

    // Main worker thread function
    void WorkerThreadFunction(FlowStage stage);
    
    // Stage-specific processing functions
    void ProcessInputStage();
    void ProcessExtractStage();
    void ProcessTransformStage();
    void ProcessProcessStage();
    void ProcessValidateStage();
    void ProcessOutputStage();

    //===================================================================================
    // Data Processing Methods
    //===================================================================================

    // Process data packet at specific stage
    bool ProcessDataPacket(std::shared_ptr<DataPacket> packet, FlowStage stage);
    
    // Extract data from element
    bool ExtractElementData(std::shared_ptr<DataPacket> packet);
    
    // Transform data
    bool TransformElementData(std::shared_ptr<DataPacket> packet);
    
    // Process data for target format
    bool ProcessForTargetFormat(std::shared_ptr<DataPacket> packet);
    
    // Validate processed data
    bool ValidateProcessedData(std::shared_ptr<DataPacket> packet);
    
    // Output data to target format
    bool OutputToTargetFormat(std::shared_ptr<DataPacket> packet);

    //===================================================================================
    // Queue Management
    //===================================================================================

    // Add packet to queue
    bool AddToQueue(FlowStage stage, std::shared_ptr<DataPacket> packet);
    
    // Get packet from queue
    std::shared_ptr<DataPacket> GetFromQueue(FlowStage stage);
    
    // Check if queue is empty
    bool IsQueueEmpty(FlowStage stage) const;
    
    // Get queue size
    size_t GetQueueSize(FlowStage stage) const;
    
    // Clear specific queue
    void ClearQueue(FlowStage stage);

    //===================================================================================
    // Flow Control
    //===================================================================================

    // Initialize worker threads
    void InitializeWorkerThreads();
    
    // Shutdown worker threads
    void ShutdownWorkerThreads();
    
    // Wait for all queues to be empty
    void WaitForCompletion();
    
    // Check for pause/stop conditions
    bool ShouldPause() const;
    bool ShouldStop() const;

    //===================================================================================
    // Statistics and Monitoring
    //===================================================================================

    // Update statistics
    void UpdateStatistics();
    
    // Calculate throughput
    double CalculateThroughput() const;
    
    // Update memory usage
    void UpdateMemoryUsage();
    
    // Report progress
    bool ReportProgress();

    //===================================================================================
    // Utility Methods
    //===================================================================================

    // Generate unique packet ID
    std::string GeneratePacketId();
    
    // Get stage name
    std::string GetStageName(FlowStage stage) const;
    
    // Get next stage
    FlowStage GetNextStage(FlowStage currentStage) const;
    
    // Check if stage is final
    bool IsFinalStage(FlowStage stage) const;
    
    // Log flow event
    void LogFlowEvent(FlowStage stage, const std::string& message);
    
    // Cleanup resources
    void CleanupResources();
};

//=======================================================================================
// Data Flow Utilities
//=======================================================================================

namespace DataFlowUtils {
    
    // Create optimized flow configuration
    DataFlowManager::FlowConfig CreateOptimizedFlowConfig(
        const IModelDb& imodel, ExportFormat targetFormat);
    
    // Calculate optimal queue sizes
    std::unordered_map<DataFlowManager::FlowStage, size_t> CalculateOptimalQueueSizes(
        size_t totalElements, size_t availableMemory);
    
    // Calculate optimal thread count
    size_t CalculateOptimalThreadCount(size_t availableCores, size_t queueSizes);
    
    // Estimate memory usage per packet
    size_t EstimatePacketMemoryUsage(const DataFlowManager::DataPacket& packet);
    
    // Analyze flow bottlenecks
    std::vector<std::string> AnalyzeFlowBottlenecks(
        const DataFlowManager::FlowStatistics& stats,
        const std::unordered_map<DataFlowManager::FlowStage, size_t>& queueSizes);
    
    // Generate flow performance report
    std::string GenerateFlowPerformanceReport(
        const DataFlowManager::FlowStatistics& stats);
    
    // Optimize flow configuration based on performance
    DataFlowManager::FlowConfig OptimizeFlowConfiguration(
        const DataFlowManager::FlowConfig& currentConfig,
        const DataFlowManager::FlowStatistics& stats);
}

} // namespace IModelExport
