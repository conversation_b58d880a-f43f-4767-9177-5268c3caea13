#pragma once

#include "../../../include/IExportFormat.h"
#include "../../../include/ExportTypes.h"
#include "../../core/ExportContext.h"

// ODA IFC SDK includes
#ifdef ODA_IFC_AVAILABLE
#include <IfcCore/IfcModel.h>
#include <IfcCore/IfcBuilder.h>
#include <IfcCore/IfcGUID.h>
#include <IfcCore/IfcGeomServer.h>
#include <Ifc4/Ifc4.h>
#include <Ifc4/Ifc4Entities.h>
#endif

#include <memory>
#include <unordered_map>

namespace IModelExport {

// Forward declarations
class IModelDb;

//=======================================================================================
// IFC Exporter Implementation using ODA IFC SDK
//=======================================================================================

class IFCExporter : public IIFCExporter {
public:
    IFCExporter();
    virtual ~IFCExporter();

    //===================================================================================
    // IExportFormat Interface
    //===================================================================================

    ExportFormat GetFormat() const override { return ExportFormat::IFC; }
    std::string GetFormatName() const override { return "Industry Foundation Classes"; }
    std::string GetFileExtension() const override { return ".ifc"; }
    std::vector<std::string> GetSupportedVersions() const override;

    bool SupportsGeometry() const override { return true; }
    bool SupportsMetadata() const override { return true; }
    bool SupportsMaterials() const override { return true; }
    bool SupportsTextures() const override { return false; }
    bool SupportsAnimations() const override { return false; }
    bool SupportsHierarchy() const override { return true; }

    ExportResult Export(
        const IModelDb& imodel,
        const ExportOptions& options,
        ProgressCallback progressCallback = nullptr) override;

    bool ValidateOptions(const ExportOptions& options, std::vector<std::string>& errors) const override;
    bool CanExportElement(const ElementInfo& element) const override;

    void SetExportContext(std::shared_ptr<ExportContext> context) override;
    std::shared_ptr<ExportContext> GetExportContext() const override;

    //===================================================================================
    // IIFCExporter Interface
    //===================================================================================

    bool SetIFCVersion(IFCExportOptions::IFCVersion version) override;
    bool SetProjectInfo(const std::string& projectName, const std::string& description) override;
    bool CreateSite(const std::string& siteName) override;
    bool CreateBuilding(const std::string& buildingName, const std::string& siteId) override;
    bool CreateBuildingStorey(const std::string& storeyName, const std::string& buildingId, double elevation) override;

    bool AddWall(const std::string& wallId, const std::vector<Point3d>& profile, double height, const std::string& storeyId) override;
    bool AddSlab(const std::string& slabId, const std::vector<Point3d>& boundary, double thickness, const std::string& storeyId) override;
    bool AddColumn(const std::string& columnId, const Point3d& position, double height, double width, double depth, const std::string& storeyId) override;
    bool AddBeam(const std::string& beamId, const Point3d& start, const Point3d& end, double width, double height, const std::string& storeyId) override;

protected:
    bool InitializeExport(const ExportOptions& options) override;
    bool FinalizeExport() override;
    void CleanupExport() override;

private:
    //===================================================================================
    // ODA IFC Integration
    //===================================================================================

#ifdef ODA_IFC_AVAILABLE
    // IFC model and builder
    OdIfc::OdIfcModelPtr m_ifcModel;
    OdIfc::OdIfcBuilderPtr m_ifcBuilder;
    
    // IFC entities
    OdIfc::OdIfcEntityPtr m_project;
    OdIfc::OdIfcEntityPtr m_site;
    OdIfc::OdIfcEntityPtr m_building;
    std::unordered_map<std::string, OdIfc::OdIfcEntityPtr> m_buildingStoreys;
    
    // Geometry server
    OdIfc::OdIfcGeomServerPtr m_geomServer;
    
    // Helper methods
    bool InitializeODAIFC();
    void CleanupODAIFC();
    bool CreateProjectStructure();
    bool SetupUnitsAndContext();
    
    // Entity creation helpers
    OdIfc::OdIfcEntityPtr CreateIfcGloballyUniqueId();
    OdIfc::OdIfcEntityPtr CreateIfcOwnerHistory();
    OdIfc::OdIfcEntityPtr CreateIfcLocalPlacement(const Point3d& origin, const Vector3d& xAxis = Vector3d(1,0,0), const Vector3d& zAxis = Vector3d(0,0,1));
    OdIfc::OdIfcEntityPtr CreateIfcCartesianPoint(const Point3d& point);
    OdIfc::OdIfcEntityPtr CreateIfcDirection(const Vector3d& direction);
    OdIfc::OdIfcEntityPtr CreateIfcAxis2Placement3D(const Point3d& origin, const Vector3d& zAxis, const Vector3d& xAxis);
    
    // Geometry creation helpers
    OdIfc::OdIfcEntityPtr CreateIfcExtrudedAreaSolid(const std::vector<Point3d>& profile, const Vector3d& direction, double depth);
    OdIfc::OdIfcEntityPtr CreateIfcArbitraryClosedProfileDef(const std::vector<Point3d>& points);
    OdIfc::OdIfcEntityPtr CreateIfcPolyline(const std::vector<Point3d>& points);
    OdIfc::OdIfcEntityPtr CreateIfcRectangleProfileDef(double width, double height);
    
    // Material creation
    OdIfc::OdIfcEntityPtr CreateIfcMaterial(const std::string& name);
    OdIfc::OdIfcEntityPtr CreateIfcMaterialDefinitionRepresentation(const Material& material);
    
    // Conversion helpers
    std::string GenerateGUID();
    std::string ToIfcLabel(const std::string& text);
    std::string ToIfcText(const std::string& text);
    double ToIfcLengthMeasure(double length);
#endif

    //===================================================================================
    // Export State Management
    //===================================================================================

    struct ExportState {
        std::string outputPath;
        IFCExportOptions::IFCVersion version;
        IFCExportOptions::IFCFileFormat fileFormat;
        std::string projectName;
        std::string projectDescription;
        bool initialized;
        bool finalized;
        
        // Statistics
        size_t entitiesCreated;
        size_t wallsCreated;
        size_t slabsCreated;
        size_t columnsCreated;
        size_t beamsCreated;
        
        // Error tracking
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };

    ExportState m_state;
    std::shared_ptr<ExportContext> m_context;
    
    // Entity caching
    std::unordered_map<std::string, std::string> m_entityCache;  // iModel ID -> IFC GUID
    std::unordered_map<std::string, std::string> m_materialCache; // material name -> IFC entity ID
    
    //===================================================================================
    // iModel Element Processing
    //===================================================================================

    // Main processing methods
    bool ProcessIModelElements(const IModelDb& imodel, ProgressCallback progressCallback);
    bool ProcessElement(const ElementInfo& element);
    
    // Element type processors
    bool ProcessBuildingElement(const ElementInfo& element);
    bool ProcessSpatialElement(const ElementInfo& element);
    bool ProcessStructuralElement(const ElementInfo& element);
    bool ProcessMEPElement(const ElementInfo& element);
    
    // Building element processors
    bool ProcessWallElement(const ElementInfo& element);
    bool ProcessSlabElement(const ElementInfo& element);
    bool ProcessColumnElement(const ElementInfo& element);
    bool ProcessBeamElement(const ElementInfo& element);
    bool ProcessDoorElement(const ElementInfo& element);
    bool ProcessWindowElement(const ElementInfo& element);
    bool ProcessStairElement(const ElementInfo& element);
    bool ProcessRoofElement(const ElementInfo& element);
    
    // Spatial element processors
    bool ProcessSpaceElement(const ElementInfo& element);
    bool ProcessZoneElement(const ElementInfo& element);
    bool ProcessBuildingStoreyElement(const ElementInfo& element);
    
    // Property processors
    bool ProcessElementProperties(const ElementInfo& element);
    bool ProcessMaterialProperties(const ElementInfo& element);
    bool ProcessQuantityProperties(const ElementInfo& element);
    
    //===================================================================================
    // IFC Hierarchy Management
    //===================================================================================

    // Spatial hierarchy
    bool CreateSpatialHierarchy(const IModelDb& imodel);
    bool AssignElementToStorey(const std::string& elementId, const std::string& storeyId);
    bool CreateIfcRelContainedInSpatialStructure(const std::string& spatialElementId, const std::vector<std::string>& containedElementIds);
    
    // Relationship management
    bool CreateIfcRelAggregates(const std::string& relatingObjectId, const std::vector<std::string>& relatedObjectIds);
    bool CreateIfcRelDefinesByProperties(const std::string& relatedObjectId, const std::string& propertySetId);
    bool CreateIfcRelAssociatesMaterial(const std::string& relatedObjectId, const std::string& materialId);
    
    //===================================================================================
    // Coordinate System and Units
    //===================================================================================

    // Coordinate transformation
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    double TransformLength(double length) const;
    
    // Units setup
    bool SetupIfcUnits();
    bool CreateIfcSIUnit(const std::string& unitType, const std::string& prefix = "");
    bool CreateIfcDimensionalExponents();
    
    //===================================================================================
    // Property Set Management
    //===================================================================================

    // Property set creation
    std::string CreateIfcPropertySet(const std::string& name, const std::unordered_map<std::string, std::string>& properties);
    std::string CreateIfcPropertySingleValue(const std::string& name, const std::string& value, const std::string& unit = "");
    std::string CreateIfcQuantitySet(const std::string& name, const std::unordered_map<std::string, double>& quantities);
    std::string CreateIfcQuantityLength(const std::string& name, double value);
    std::string CreateIfcQuantityArea(const std::string& name, double value);
    std::string CreateIfcQuantityVolume(const std::string& name, double value);
    
    //===================================================================================
    // Error Handling and Logging
    //===================================================================================

    void LogError(const std::string& message);
    void LogWarning(const std::string& message);
    void LogInfo(const std::string& message);
    
    bool HandleError(const std::string& elementId, const std::string& error);
    bool ShouldContinueOnError() const;
    
    //===================================================================================
    // Progress Reporting
    //===================================================================================

    bool UpdateProgress(ProgressCallback callback, double percentage, const std::string& operation);
    bool CheckCancellation(ProgressCallback callback);
    
    //===================================================================================
    // Utility Methods
    //===================================================================================

    std::string SanitizeIfcName(const std::string& name) const;
    bool FileExists(const std::string& path) const;
    bool CreateDirectoryIfNeeded(const std::string& path) const;
    std::string GetTemporaryFileName() const;
    
    // IFC validation
    bool ValidateIfcModel() const;
    bool ValidateIfcEntity(const std::string& entityId) const;
};

} // namespace IModelExport
