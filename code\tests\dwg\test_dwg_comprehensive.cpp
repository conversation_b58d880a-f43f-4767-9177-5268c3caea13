#include <gtest/gtest.h>
#include "../../src/formats/dwg/DWGExporter.h"
#include "../../src/formats/dwg/entities/DWGPolylineProcessor.h"
#include "../../src/formats/dwg/entities/DWGHatchProcessor.h"
#include "../../src/formats/dwg/geometry/DWGGeometryProcessor.h"
#include "../../src/formats/dwg/styles/DWGStyleManager.h"
#include "../../src/formats/dwg/diagnostics/DWGDiagnostics.h"

using namespace IModelExport;

//=======================================================================================
// Comprehensive DWG System Tests
//=======================================================================================

class DWGComprehensiveTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_exporter = std::make_unique<DWGExporter>();
        m_geometryProcessor = std::make_unique<DWGGeometryProcessor>();
        m_styleManager = std::make_unique<DWGStyleManager>();
        m_diagnostics = std::make_unique<DWGDiagnostics>();
        
        // Configure diagnostics
        m_diagnostics->SetLogLevel(DiagnosticLevel::Info);
        m_diagnostics->EnablePerformanceMonitoring(true);
        m_diagnostics->SetConsoleOutput(false); // Disable for tests
    }
    
    void TearDown() override {
        m_exporter.reset();
        m_geometryProcessor.reset();
        m_styleManager.reset();
        m_diagnostics.reset();
    }
    
    std::unique_ptr<DWGExporter> m_exporter;
    std::unique_ptr<DWGGeometryProcessor> m_geometryProcessor;
    std::unique_ptr<DWGStyleManager> m_styleManager;
    std::unique_ptr<DWGDiagnostics> m_diagnostics;
};

//=======================================================================================
// Polyline Processor Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, PolylineProcessorBasicFunctionality) {
    DWGPolylineProcessor processor(m_exporter.get());
    
    // Create test polyline geometry
    PolylineGeometry geometry;
    geometry.vertices = {
        {{0, 0, 0}, 0.0, 0.0, 0.0},
        {{100, 0, 0}, 0.5, 0.0, 0.0},  // Arc segment
        {{100, 100, 0}, 0.0, 0.0, 0.0},
        {{0, 100, 0}, 0.0, 0.0, 0.0}
    };
    geometry.isClosed = true;
    geometry.hasArcs = true;
    
    // Validate geometry
    auto validation = processor.ValidatePolylineGeometry(geometry);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidVertices);
    EXPECT_TRUE(validation.hasValidBulges);
    
    // Process polyline
    auto status = processor.ProcessPolyline(geometry, "TestLayer");
    EXPECT_EQ(status, DWGProcessingStatus::Skipped); // Without RealDWG
}

TEST_F(DWGComprehensiveTest, PolylineArcCalculations) {
    DWGPolylineProcessor processor(m_exporter.get());
    
    Point3d start(0, 0, 0);
    Point3d end(100, 0, 0);
    double bulge = 0.5;
    
    // Test arc calculations
    double radius = processor.CalculateArcRadius(start, end, bulge);
    EXPECT_GT(radius, 0.0);
    
    Point3d center = processor.CalculateArcCenter(start, end, bulge);
    EXPECT_TRUE(processor.ValidateBulgeValue(bulge));
    
    // Test arc tessellation
    std::vector<Point3d> arcPoints;
    EXPECT_TRUE(processor.ProcessArcSegment(
        {{start, bulge, 0.0, 0.0}}, 
        {{end, 0.0, 0.0, 0.0}}, 
        arcPoints
    ));
}

//=======================================================================================
// Hatch Processor Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, HatchProcessorBasicFunctionality) {
    DWGHatchProcessor processor(m_exporter.get());
    
    // Create test hatch geometry
    HatchGeometry geometry;
    
    // Create rectangular boundary
    HatchBoundary boundary;
    boundary.type = HatchBoundary::Type::Polyline;
    boundary.vertices = {
        Point3d(0, 0, 0),
        Point3d(100, 0, 0),
        Point3d(100, 100, 0),
        Point3d(0, 100, 0)
    };
    boundary.isClosed = true;
    boundary.isOuter = true;
    
    geometry.boundaries.push_back(boundary);
    geometry.pattern = HatchPattern::CreateSolid();
    geometry.fillColor = Color(0.5f, 0.5f, 0.5f, 1.0f);
    geometry.isSolid = true;
    
    // Validate geometry
    auto validation = processor.ValidateHatchGeometry(geometry);
    EXPECT_TRUE(validation.isValid);
    EXPECT_TRUE(validation.hasValidBoundaries);
    
    // Process hatch
    auto status = processor.ProcessHatch(geometry, "HatchLayer");
    EXPECT_EQ(status, DWGProcessingStatus::Skipped); // Without RealDWG
}

TEST_F(DWGComprehensiveTest, HatchPatternCreation) {
    // Test predefined patterns
    auto solid = HatchPattern::CreateSolid();
    EXPECT_TRUE(solid.IsValid());
    EXPECT_TRUE(solid.IsSolid());
    
    auto ansi31 = HatchPattern::CreateAnsi31();
    EXPECT_TRUE(ansi31.IsValid());
    EXPECT_FALSE(ansi31.IsSolid());
    EXPECT_FALSE(ansi31.lines.empty());
    
    // Test pattern utilities
    EXPECT_TRUE(HatchUtils::IsStandardPattern("SOLID"));
    EXPECT_TRUE(HatchUtils::IsStandardPattern("ANSI31"));
    EXPECT_FALSE(HatchUtils::IsStandardPattern("CUSTOM"));
    
    auto standardNames = HatchUtils::GetStandardPatternNames();
    EXPECT_FALSE(standardNames.empty());
}

//=======================================================================================
// Geometry Processor Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, GeometryProcessorValidation) {
    // Test coordinate validation
    Point3d validPoint(10.0, 20.0, 30.0);
    EXPECT_TRUE(m_geometryProcessor->ValidatePoint(validPoint));
    
    Point3d invalidPoint(std::numeric_limits<double>::infinity(), 20.0, 30.0);
    EXPECT_FALSE(m_geometryProcessor->ValidatePoint(invalidPoint));
    
    // Test point repair
    Point3d repairedPoint = m_geometryProcessor->RepairPoint(invalidPoint);
    EXPECT_TRUE(m_geometryProcessor->ValidatePoint(repairedPoint));
    
    // Test distance calculations
    Point3d p1(0, 0, 0);
    Point3d p2(3, 4, 0);
    double distance = m_geometryProcessor->DistancePointToPoint(p1, p2);
    EXPECT_NEAR(distance, 5.0, 1e-6);
    
    // Test angle calculations
    Vector3d v1(1, 0, 0);
    Vector3d v2(0, 1, 0);
    double angle = m_geometryProcessor->AngleBetweenVectors(v1, v2);
    EXPECT_NEAR(angle, M_PI / 2.0, 1e-6);
}

TEST_F(DWGComprehensiveTest, GeometryProcessorTransformation) {
    // Test transformation setup
    Transform3D translation = Transform3D::Translation(Vector3d(10, 20, 30));
    m_geometryProcessor->SetTransform(translation);
    
    Point3d original(0, 0, 0);
    Point3d transformed = m_geometryProcessor->TransformPoint(original);
    
    EXPECT_NEAR(transformed.x, 10.0, 1e-6);
    EXPECT_NEAR(transformed.y, 20.0, 1e-6);
    EXPECT_NEAR(transformed.z, 30.0, 1e-6);
    
    // Test vector transformation
    Vector3d originalVector(1, 0, 0);
    Vector3d transformedVector = m_geometryProcessor->TransformVector(originalVector);
    EXPECT_NEAR(transformedVector.x, 1.0, 1e-6);
    EXPECT_NEAR(transformedVector.y, 0.0, 1e-6);
    EXPECT_NEAR(transformedVector.z, 0.0, 1e-6);
}

//=======================================================================================
// Style Manager Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, StyleManagerBasicOperations) {
    // Test layer creation
    LayerStyle layer;
    layer.name = "TestLayer";
    layer.color = Color(1.0f, 0.0f, 0.0f, 1.0f);
    layer.lineTypeName = "Continuous";
    layer.lineWeight = 0.25;
    
    auto result = m_styleManager->CreateLayer(layer);
    EXPECT_TRUE(result.success);
    
    // Test layer retrieval
    LayerStyle retrievedLayer;
    auto getResult = m_styleManager->GetLayer("TestLayer", retrievedLayer);
    EXPECT_TRUE(getResult.success);
    EXPECT_EQ(retrievedLayer.name, "TestLayer");
    
    // Test linetype creation
    LineTypeStyle lineType = LineTypeStyle::CreateDashed();
    auto lineTypeResult = m_styleManager->CreateLineType(lineType);
    EXPECT_TRUE(lineTypeResult.success);
    
    // Test text style creation
    TextStyle textStyle;
    textStyle.name = "TestTextStyle";
    textStyle.fontName = "Arial";
    textStyle.height = 2.5;
    
    auto textStyleResult = m_styleManager->CreateTextStyle(textStyle);
    EXPECT_TRUE(textStyleResult.success);
}

TEST_F(DWGComprehensiveTest, StyleManagerValidation) {
    // Test style validation
    LayerStyle invalidLayer;
    invalidLayer.name = ""; // Invalid empty name
    
    auto result = m_styleManager->CreateLayer(invalidLayer);
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.errors.empty());
    
    // Test style repair
    LayerStyle repairableLayer;
    repairableLayer.name = "Invalid/Name*With?Special<Characters>";
    repairableLayer.color = Color(-1.0f, 2.0f, 0.5f, 1.0f); // Invalid color values
    
    EXPECT_TRUE(m_styleManager->RepairLayer(repairableLayer));
    EXPECT_NE(repairableLayer.name, "Invalid/Name*With?Special<Characters>");
    EXPECT_TRUE(repairableLayer.color.r >= 0.0f && repairableLayer.color.r <= 1.0f);
}

//=======================================================================================
// Diagnostics Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, DiagnosticsBasicFunctionality) {
    // Test message logging
    m_diagnostics->LogInfo("Test info message", "TestSource");
    m_diagnostics->LogWarning("Test warning message", "TestSource");
    m_diagnostics->LogError("Test error message", "TestSource");
    
    EXPECT_EQ(m_diagnostics->GetMessageCount(), 3);
    EXPECT_TRUE(m_diagnostics->HasWarnings());
    EXPECT_TRUE(m_diagnostics->HasErrors());
    
    // Test message filtering
    auto errorMessages = m_diagnostics->GetMessages(DiagnosticLevel::Error);
    EXPECT_EQ(errorMessages.size(), 1);
    EXPECT_EQ(errorMessages[0].message, "Test error message");
    
    auto sourceMessages = m_diagnostics->GetMessages("TestSource");
    EXPECT_EQ(sourceMessages.size(), 3);
}

TEST_F(DWGComprehensiveTest, DiagnosticsPerformanceMonitoring) {
    // Test performance timing
    {
        auto timer = m_diagnostics->StartTimer("TestOperation");
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        // Timer destructor will record the timing
    }
    
    // Test element tracking
    m_diagnostics->RecordElementProcessed(true);
    m_diagnostics->RecordElementProcessed(false);
    m_diagnostics->RecordElementSkipped();
    
    auto metrics = m_diagnostics->GetPerformanceMetrics();
    EXPECT_EQ(metrics.elementsProcessed, 2);
    EXPECT_EQ(metrics.elementsSuccessful, 1);
    EXPECT_EQ(metrics.elementsFailed, 1);
    EXPECT_EQ(metrics.elementsSkipped, 1);
}

//=======================================================================================
// Integration Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, IntegratedProcessingWorkflow) {
    // Test complete processing workflow
    m_diagnostics->StartSession("IntegrationTest");
    
    // Create and process various entities
    DWGLineProcessor lineProcessor(m_exporter.get());
    DWGCircleProcessor circleProcessor(m_exporter.get());
    DWGTextProcessor textProcessor(m_exporter.get());
    
    // Process line
    auto lineStatus = lineProcessor.ProcessLine(
        Point3d(0, 0, 0), Point3d(100, 100, 0), "GeometryLayer"
    );
    m_diagnostics->RecordElementProcessed(lineStatus == DWGProcessingStatus::Success);
    
    // Process circle
    auto circleStatus = circleProcessor.ProcessCircle(
        Point3d(50, 50, 0), 25.0, Vector3d(0, 0, 1), "GeometryLayer"
    );
    m_diagnostics->RecordElementProcessed(circleStatus == DWGProcessingStatus::Success);
    
    // Process text
    auto textStatus = textProcessor.ProcessText(
        Point3d(10, 10, 0), "Test Text", 2.5, 0.0, "TextLayer"
    );
    m_diagnostics->RecordElementProcessed(textStatus == DWGProcessingStatus::Success);
    
    m_diagnostics->EndSession();
    
    // Verify processing results
    auto metrics = m_diagnostics->GetPerformanceMetrics();
    EXPECT_EQ(metrics.elementsProcessed, 3);
    
    // Generate report
    std::string report = m_diagnostics->GenerateReport();
    EXPECT_FALSE(report.empty());
}

TEST_F(DWGComprehensiveTest, ErrorHandlingAndRecovery) {
    // Test error handling with invalid geometry
    DWGLineProcessor processor(m_exporter.get());
    
    // Invalid line (same start and end points)
    auto status = processor.ProcessLine(
        Point3d(0, 0, 0), Point3d(0, 0, 0), "TestLayer"
    );
    EXPECT_EQ(status, DWGProcessingStatus::InvalidGeometry);
    
    // Invalid coordinates
    status = processor.ProcessLine(
        Point3d(std::numeric_limits<double>::infinity(), 0, 0),
        Point3d(100, 100, 0), "TestLayer"
    );
    EXPECT_EQ(status, DWGProcessingStatus::InvalidGeometry);
    
    // Test geometry repair
    std::vector<Point3d> invalidPoints = {
        Point3d(std::numeric_limits<double>::infinity(), 0, 0),
        Point3d(100, 100, 0),
        Point3d(200, std::numeric_limits<double>::quiet_NaN(), 0)
    };
    
    m_geometryProcessor->RepairPoints(invalidPoints);
    
    for (const auto& point : invalidPoints) {
        EXPECT_TRUE(m_geometryProcessor->ValidatePoint(point));
    }
}

//=======================================================================================
// Performance Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, PerformanceBenchmark) {
    const int numElements = 1000;
    
    m_diagnostics->StartSession("PerformanceBenchmark");
    auto sessionTimer = m_diagnostics->StartTimer("TotalProcessing");
    
    DWGLineProcessor processor(m_exporter.get());
    
    for (int i = 0; i < numElements; ++i) {
        Point3d start(i * 10.0, 0, 0);
        Point3d end(i * 10.0 + 10.0, 10.0, 0);
        
        auto timer = m_diagnostics->StartTimer("ElementProcessing");
        auto status = processor.ProcessLine(start, end, "BenchmarkLayer");
        
        m_diagnostics->RecordElementProcessed(status == DWGProcessingStatus::Success || 
                                            status == DWGProcessingStatus::Skipped);
    }
    
    sessionTimer->Stop();
    m_diagnostics->EndSession();
    
    auto metrics = m_diagnostics->GetPerformanceMetrics();
    EXPECT_EQ(metrics.elementsProcessed, numElements);
    
    double avgTime = metrics.averageProcessingTimePerElement();
    EXPECT_GT(avgTime, 0.0);
    
    // Performance should be reasonable (less than 1ms per element for simple lines)
    EXPECT_LT(avgTime, 1.0); // milliseconds
}

//=======================================================================================
// Memory Management Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, MemoryManagement) {
    // Test memory usage tracking
    m_diagnostics->RecordMemoryUsage();
    size_t initialMemory = m_diagnostics->GetCurrentMemoryUsage();
    
    // Create many objects to increase memory usage
    std::vector<std::unique_ptr<DWGEntityProcessor>> processors;
    for (int i = 0; i < 100; ++i) {
        processors.push_back(std::make_unique<DWGLineProcessor>(m_exporter.get()));
    }
    
    m_diagnostics->RecordMemoryUsage();
    size_t peakMemory = m_diagnostics->GetPeakMemoryUsage();
    
    // Clear objects
    processors.clear();
    
    // Memory usage should have increased and then decreased
    EXPECT_GE(peakMemory, initialMemory);
}

//=======================================================================================
// Utility Function Tests
//=======================================================================================

TEST_F(DWGComprehensiveTest, UtilityFunctions) {
    // Test polyline utilities
    std::vector<Point3d> collinearPoints = {
        Point3d(0, 0, 0),
        Point3d(10, 10, 0),
        Point3d(20, 20, 0)
    };
    EXPECT_TRUE(PolylineUtils::ArePointsCollinear(collinearPoints));
    
    std::vector<Point3d> rectanglePoints = {
        Point3d(0, 0, 0),
        Point3d(10, 0, 0),
        Point3d(10, 10, 0),
        Point3d(0, 10, 0)
    };
    double area = PolylineUtils::CalculateArea(rectanglePoints);
    EXPECT_NEAR(area, 100.0, 1e-6);
    
    // Test hatch utilities
    EXPECT_TRUE(HatchUtils::IsValidBoundary(HatchBoundary()));
    
    // Test spline utilities
    EXPECT_TRUE(SplineUtils::ValidateSplineParameters(3, 4, 8));
    EXPECT_FALSE(SplineUtils::ValidateSplineParameters(3, 2, 6));
}
