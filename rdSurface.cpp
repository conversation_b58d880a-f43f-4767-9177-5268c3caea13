/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSurface.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtSurface : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendControlElementToCell
(
EditElementHandleR          cellElement,
AcDbEntity*                 pEntity,
StandardSurfaceXAttrID      controlType,
ConvertToDgnContextR        context
)
    {
    if (NULL == pEntity)
        return;

    EditElementHandle       controlElem;
    if (RealDwgSuccess != ACRX_X_CALL (pEntity, ToDgnExtension)->ToElement (pEntity, controlElem, context))
        return;

    MSElementDescrP         elmdscr = controlElem.GetElementDescrP ();
    if (NULL == elmdscr)
        return;

    elmdscr->el.ehdr.uniqueId = context.GetAndIncrementNextId ();

    StandardSurfaceBaseHandler::SetControlTypeForElement (controlElem, controlType);

    if (BSISUCCESS == NormalCellHeaderHandler::AddChildElement(cellElement, controlElem))
        {
        // can't add an invisible profile to a cell, so do it post addition:
        if (NULL != elmdscr)
            elmdscr->el.hdr.dhdr.props.b.invisible = true;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetSweepParameters
(
SweepParameters&            sweepParameters,
AcDbSweepOptions&           sweepOptions
)
    {
    sweepParameters.SetAlign ((AlignOption) sweepOptions.align());
    sweepParameters.SetAlignAngle (sweepOptions.alignAngle());
    sweepParameters.SetDraftAngle (sweepOptions.draftAngle());
    sweepParameters.SetTwistAngle (sweepOptions.twistAngle());
    sweepParameters.SetScale (sweepOptions.scaleFactor());
    sweepParameters.SetUseBasePoint (sweepOptions.basePoint() != AcGePoint3d(0,0,0));
    sweepParameters.SetIsBanked (sweepOptions.bank());

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetExtrudeParameters
(
ExtrudeParameters&          extrudeParameters,
AcDbExtrudedSurface*        pExtrudedSurface
)
    {
    DVec3d      vector;
    RealDwgUtil::DVec3dFromGeVector3d (vector, pExtrudedSurface->getSweepVec());
    extrudeParameters.SetSweepVector (vector);

    AcDbSweepOptions    sweepOptions;

    pExtrudedSurface->getSweepOptions (sweepOptions);

    SweepParameters     sweepParams;
    GetSweepParameters (sweepParams, sweepOptions);

    extrudeParameters.SetSweepParameters (sweepParams);

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetRevolveParameters
(
RevolveParameters&          pRevolveParameters,
AcDbRevolvedSurface*        pRevolvedSurface
)
    {
    DVec3d      axis;
    RealDwgUtil::DVec3dFromGeVector3d  (axis, pRevolvedSurface->getAxisVec());
    pRevolveParameters.SetAxis (axis);

    AcDbRevolveOptions  revolveOptions;

    pRevolvedSurface->getRevolveOptions (revolveOptions);

    RevolveFlags        revolveFlags;

    revolveFlags.SetIsCloseToAxis (revolveOptions.closeToAxis());

    pRevolveParameters.SetFlags (revolveFlags);
    pRevolveParameters.SetDraftAngle (revolveOptions.draftAngle());
    pRevolveParameters.SetTwistAngle (revolveOptions.twistAngle());
    pRevolveParameters.SetRevolvedAngle (pRevolvedSurface->getRevolveAngle());
    pRevolveParameters.SetStartAngle (pRevolvedSurface->getStartAngle());

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetLoftParameters
(
LoftParameters&             pLoftParameters,
AcDbLoftOptions&            pLoftOptions
)
    {
    pLoftParameters.SetStartDraftAngle (pLoftOptions.draftStart());
    pLoftParameters.SetEndDraftAngle (pLoftOptions.draftEnd());
    pLoftParameters.SetStartDraftMagnitude (pLoftOptions.draftStartMag());
    pLoftParameters.SetEndDraftMagnitude (pLoftOptions.draftEndMag());
    pLoftParameters.SetNormalOption ((LoftNormalOption) pLoftOptions.normal());

    LoftFlags   flags;

    flags.SetAlign (pLoftOptions.alignDirection());
    flags.SetClosed (pLoftOptions.closed());
    flags.SetTwisted (!pLoftOptions.noTwist());
    flags.SetArcLengthParam (pLoftOptions.arcLengthParam());

    pLoftParameters.SetFlags (flags);

    return  SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddSweptSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbSweptSurface*           pSweptSurface,
ConvertToDgnContext&        context
)
    {
    SweepParameters     sweepParameters;
    AcDbSweepOptions    sweepOptions;

    sweepParameters._Init ();
    sweepParameters._SetUIsoLines (pSweptSurface->uIsolineDensity());
    sweepParameters._SetVIsoLines (pSweptSurface->vIsolineDensity());

    pSweptSurface->getSweepOptions (sweepOptions);

    GetSweepParameters (sweepParameters, sweepOptions);

    AcDbEntity* pEntity = pSweptSurface->getSweepEntity ();
    if (NULL != pEntity)
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfaceSweepCurve, context);

    pEntity = pSweptSurface->getPathEntity ();
    if (NULL != pEntity)
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfacePathCurve, context);

    return  sweepParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddRevolvedSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbRevolvedSurface*        pRevolvedSurface,
ConvertToDgnContext&        context
)
    {
    RevolveParameters   revolveParameters;

    revolveParameters._Init ();
    revolveParameters._SetUIsoLines (pRevolvedSurface->uIsolineDensity());
    revolveParameters._SetVIsoLines (pRevolvedSurface->vIsolineDensity());

    GetRevolveParameters (revolveParameters, pRevolvedSurface);

    AcDbEntity*     pEntity = pRevolvedSurface->getRevolveEntity ();
    if (NULL != pEntity)
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfaceRevolveCurve, context);

    return  revolveParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddExtrudedSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbExtrudedSurface*        pExtrudedSurface,
ConvertToDgnContext&        context
)
    {
    ExtrudeParameters   extrudeParameters;

    extrudeParameters._Init ();
    extrudeParameters._SetUIsoLines (pExtrudedSurface->uIsolineDensity());
    extrudeParameters._SetVIsoLines (pExtrudedSurface->vIsolineDensity());

    GetExtrudeParameters (extrudeParameters, pExtrudedSurface);

    AcDbEntity* pEntity = pExtrudedSurface->getSweepEntity ();
    if (NULL !=pEntity)
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfaceSweepCurve, context);

    return  extrudeParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddLoftedSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbLoftedSurface*          pLoftedSurface,
ConvertToDgnContext&        context
)
    {
    LoftParameters      loftParameters;
    AcDbLoftOptions     loftOptions;

    loftParameters._Init();
    loftParameters._SetUIsoLines (pLoftedSurface->uIsolineDensity());
    loftParameters._SetVIsoLines (pLoftedSurface->vIsolineDensity());

    pLoftedSurface->getLoftOptions (loftOptions);

    GetLoftParameters (loftParameters, loftOptions);

    AcDbEntity*         pEntity;

    for (int iXSection = 0; iXSection < pLoftedSurface->numCrossSections(); iXSection++)
        {
        pEntity = pLoftedSurface->getCrossSection (iXSection);
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfaceCrossSection, context);
        }

    for (int iGuide = 0; iGuide < pLoftedSurface->numGuideCurves(); iGuide++)
        {
        pEntity = pLoftedSurface->getGuideCurve (iGuide);
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfaceGuideCurve, context);
        }

    if (0 == pLoftedSurface->numCrossSections())
        {
        pEntity = pLoftedSurface->getPathEntity ();
        AppendControlElementToCell (elemHandle, pEntity, XATTRID_StandardSurfacePathCurve, context);
        }

    return  loftParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddPlaneSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbPlaneSurface*           pPlaneSurface
)
    {
    PlaneParameters     planeParameters;

    planeParameters._Init ();
    planeParameters._SetUIsoLines (pPlaneSurface->uIsolineDensity());
    planeParameters._SetVIsoLines (pPlaneSurface->vIsolineDensity());

    return  planeParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddDefaultSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbSurface*                pSurface
)
    {
    DefaultParameters       defaultParameters;

    defaultParameters._Init();
    defaultParameters._SetUIsoLines (pSurface->uIsolineDensity());
    defaultParameters._SetVIsoLines (pSurface->vIsolineDensity());

    return  defaultParameters._AddToElement (elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddSurfaceParameters
(
EditElementHandleR          elemHandle,
AcDbSurface*                pSurface,
ConvertToDgnContext&        context
)
    {
    StatusInt       status = SUCCESS;

    AcDbSweptSurface*       pSwept;
    AcDbRevolvedSurface*    pRevolved;
    AcDbExtrudedSurface*    pExtruded;
    AcDbLoftedSurface*      pLofted;
    AcDbPlaneSurface*       pPlane;

    if (NULL != (pSwept = AcDbSweptSurface::cast (pSurface)))
        {
        status = AddSweptSurfaceParameters (elemHandle, pSwept, context);
        }
    else if (NULL != (pRevolved = AcDbRevolvedSurface::cast (pSurface)))
        {
        status = AddRevolvedSurfaceParameters (elemHandle, pRevolved, context);
        }
    else if (NULL != (pExtruded = AcDbExtrudedSurface::cast (pSurface)))
        {
        status = AddExtrudedSurfaceParameters (elemHandle, pExtruded, context);
        }
    else if (NULL != (pLofted = AcDbLoftedSurface::cast (pSurface)))
        {
        status = AddLoftedSurfaceParameters (elemHandle, pLofted, context);
        }
    else if (NULL != (pPlane = AcDbPlaneSurface::cast (pSurface)))
        {
        status = AddPlaneSurfaceParameters (elemHandle, pPlane);
        }
    else
        {
        status = AddDefaultSurfaceParameters (elemHandle, pSurface);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/07
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetCellTransformFromSurface
(
DPoint3dP                   pOrigin,
RotMatrixP                  pMatrix,
AcDbSurface*                pSurface,
ConvertToDgnContext&        context
)
    {
    pOrigin->zero ();
    pMatrix->initIdentity ();

    AcDbSweptSurface*       pSwept;
    AcDbRevolvedSurface*    pRevolved;
    AcDbExtrudedSurface*    pExtruded;
    if (NULL != (pSwept = AcDbSweptSurface::cast (pSurface)))
        {
        AcDbSweepOptions    sweepOptions;

        pSwept->getSweepOptions (sweepOptions);

        // set cell origin to be the base point
        RealDwgUtil::DPoint3dFromGePoint3d (*pOrigin, sweepOptions.basePoint());
        }
    else if (NULL != (pRevolved = AcDbRevolvedSurface::cast (pSurface)))
        {
        // set cell origin to be the axis point
        RealDwgUtil::DPoint3dFromGePoint3d (*pOrigin, pRevolved->getAxisPnt());
        }
    else if (NULL != (pExtruded = AcDbExtrudedSurface::cast (pSurface)))
        {
        AcDbSweepOptions        sweepOptions;

        pExtruded->getSweepOptions(sweepOptions);

        AcGePoint3d     basePoint = sweepOptions.basePoint ();
        AcGeMatrix3d    matrix;
        if (sweepOptions.getSweepEntityTransform(matrix))
            basePoint.transformBy (matrix);

        // set cell origin to be the base point
        RealDwgUtil::DPoint3dFromGePoint3d (*pOrigin, basePoint);
        }
    else
        {
        return;
        }

    context.GetTransformToDGN().Multiply (*pOrigin);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 CannotSupportDefaultSurface
(
AcDbSurface*                pSurface
)
    {
    /*-----------------------------------------------------------------------------------
    We currently cannot edit any DWG surface yet.  A problem becomes more obvious when
    we save a DGN surface to DWG.  the acisIn method turns them to be default surfaces.
    Consequently we cannot edit these surfaces anymore.  To workaround this obvious issue
    we drop default surface to ACIS body so our SmartSolid tools will continue to work.
    -----------------------------------------------------------------------------------*/
    return  !pSurface->isKindOf(AcDbSweptSurface::desc()) &&
            !pSurface->isKindOf(AcDbRevolvedSurface::desc()) &&
            !pSurface->isKindOf(AcDbExtrudedSurface::desc()) &&
            !pSurface->isKindOf(AcDbLoftedSurface::desc()) &&
            !pSurface->isKindOf(AcDbPlaneSurface::desc());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   06/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        IsNativeAutoCADSurface (AcRxClass *desc) const
    {
    if (desc == AcDbSweptSurface::desc())
        return true;

    if (desc == AcDbRevolvedSurface::desc())
        return true;

    if (desc == AcDbExtrudedSurface::desc())
        return true;

    if (desc == AcDbLoftedSurface::desc())
        return true;

    if (desc == AcDbPlaneSurface::desc())
        return true;

    if (desc == AcDbSurface::desc())
        return true;

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/18
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateShapeElement (EditElementHandleR outElement, AcDbEntityP region, ConvertToDgnContextR context) const
    {
    if (nullptr == region)
        return  NullObject;

    AcDbVoidPtrArray    entities;
    auto es = region->explode (entities);
    if (Acad::eOk != es || entities.isEmpty())
        return  EntityError;

    auto numPoints = entities.length ();

    RealDwgStatus   status = RealDwgSuccess;
    bvector<DPoint3d>   points(numPoints);
    for (int i = 0; i < numPoints; i++)
        {
        auto line = static_cast <AcDbLine*> (entities[i]);
        if (nullptr == line)
            status = DwgObjectUnsupported;

        if (status == RealDwgSuccess)
            RealDwgUtil::DPoint3dFromGePoint3d (points[i], line->startPoint());

        delete entities[i];
        }

    if (status != RealDwgSuccess)
        {
        DIAGNOSTIC_PRINTF ("Plane surface is dropped to unsupported entities!");
        return  status;
        }

    context.GetTransformToDGN().Multiply (&points.front(), numPoints);

    status = context.CreateElementFromVertices(outElement, &points.front(), numPoints, true);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/18
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropPlaneSurface (EditElementHandleR outElement, AcDbPlaneSurface* planeSurface, ConvertToDgnContextR context) const
    {
    // work around a plane surface being world drawn to too many primitives - TFS 923312:
    if (nullptr == planeSurface)
        return  NullObject;

    AcArray<AcDbEntity*>    regions;
    Acad::ErrorStatus   es = planeSurface->convertToRegion (regions);
    if (Acad::eOk != es || regions.isEmpty())
        return  EntityError;

    outElement.Invalidate ();

    RealDwgStatus   status = RealDwgSuccess;
    MSElementDescrP firstElmdscr = nullptr;

    for (int i = 0; i < regions.length(); i++)
        {
        EditElementHandle   eeh;
        if (RealDwgSuccess == status && RealDwgSuccess == this->CreateShapeElement(eeh, regions[i], context))
            {
            if (nullptr == firstElmdscr)
                {
                // normally a plane surface corresponds to one region only.
                firstElmdscr = eeh.ExtractElementDescr ();
                }
            else
                {
                // more than 1 region get dropped from this surface - wrap them in a cell:
                if (!outElement.IsValid())
                    {
                    auto name = planeSurface->isA()->name();
                    NormalCellHeaderHandler::CreateOrphanCellElement (outElement, name, context.GetThreeD(), *context.GetModel());

                    EditElementHandle   firstEeh(firstElmdscr, true, false);
                    if (BSISUCCESS != NormalCellHeaderHandler::AddChildElement(outElement, firstEeh))
                        {
                        // error out but do not break out here - we must free all regions.
                        DIAGNOSTIC_PRINTF ("Failed adding regions to dropped Plane Surface cell, ID=%I64d!\n", context.ElementIdFromObject(planeSurface));
                        status = CantCreateSurface;
                        }
                    }

                if (RealDwgSuccess == status && BSISUCCESS != NormalCellHeaderHandler::AddChildElement(outElement, eeh))
                    {
                    DIAGNOSTIC_PRINTF ("Failed adding regions to dropped Plane Surface cell, ID=%I64d!\n", context.ElementIdFromObject(planeSurface));
                    status = CantCreateSurface;
                    }
                }
            }
        delete regions[i];
        }

    if (RealDwgSuccess != status)
        return  status;

    // we should have either a shape or a cell element:
    if (nullptr == firstElmdscr && !outElement.IsValid())
        return  MstnElementUnacceptable;

    if (outElement.IsValid())
        NormalCellHeaderHandler::AddChildComplete (outElement);
    else
        outElement.SetElementDescr (firstElmdscr, true, false);

    context.ElementHeaderFromEntity (outElement, planeSurface);

    // dropped header element should be locked
    outElement.GetElementP()->hdr.ehdr.locked = true;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToElement
(
AcDbObjectP                 acObject,
EditElementHandleR          outElement,
ConvertToDgnContextR        context
) const override
    {
    // MS_DWG_BREP_AS_PROXY unconditionally creates a proxy.
    if (context.DropBrepAsProxy())
        {
        auto planeSurface = AcDbPlaneSurface::cast (acObject);
        if (planeSurface != nullptr && RealDwgSuccess == this->DropPlaneSurface(outElement, planeSurface, context))
            return  RealDwgSuccess;
        return context.WorldDrawToElements (AcDbEntity::cast(acObject), outElement);
        }

    AcDbSurface*            acSurface = AcDbSurface::cast (acObject);

    /*-----------------------------------------------------------------------------------
    Extract ACIS data from the surface object via copyFrom.  AcDbSurface::getBody works
    but it is an internal method which does not seem to allow deletion of the newly created
    body entity.
    -----------------------------------------------------------------------------------*/
    AcDbBody*               acBody = new AcDbBody ();
    acBody->copyFrom (acSurface);
    acBody->setPropertiesFrom (acSurface);

    EditElementHandle       surfaceElem;
    RealDwgStatus           status = CantCreateAcisElement;
    if (!context.UseAcisOutForBrep())
        status = context.CreateElementFromBrep (surfaceElem, AcDbEntity::cast(acSurface));

    if (RealDwgSuccess != status)
        {
        if (CantCreateAcisElement != status)
            DIAGNOSTIC_PRINTF ("Failed Brep conversion for SURFACE(%I64d) - try converting it via ACISOUT!\n", context.ElementIdFromObject(acObject));
        status = context.CreateElementFromAcisOut (surfaceElem, AcDbEntity::cast(acBody), true);
        }

    delete acBody;

    if (RealDwgSuccess != status)
        {
        // if we don't get an element from Acis, then it might be a subclass of AcDb3dSolid
        //  (that happens with the PipeDesigner Object Enabler). Try WorldDraw it instead.
        if (!IsNativeAutoCADSurface(acObject->desc()) && ConversionInChildrenProc != status && WorldDrawFailed != status)
            return context.WorldDrawToElements (AcDbEntity::cast(acObject), outElement);

        return ConversionInChildrenProc == status ? status : DwgObjectUnsupported;
        }

    if (CannotSupportDefaultSurface(acSurface))
        {
        // drop standard surface to smart surface:
        outElement.Duplicate (surfaceElem);
        context.ElementHeaderFromEntity (outElement, acSurface);
        return  RealDwgSuccess;
        }

    /*-----------------------------------------------------------------------------------
    Wrap ACIS in a proxy cell, and add parameters to cell in the postProcess
    -----------------------------------------------------------------------------------*/
    const ACHAR*        name = acSurface->isA()->name();

    DPoint3d            origin;
    RotMatrix           matrix;

    GetCellTransformFromSurface (&origin, &matrix, acSurface, context);

    NormalCellHeaderHandler::CreateCellElement (outElement, name, origin, matrix, context.GetThreeD(), *context.GetModel());

    context.ElementHeaderFromEntity (outElement, acSurface);
    if (SUCCESS != NormalCellHeaderHandler::AddChildElement(outElement, surfaceElem))
        {
        DIAGNOSTIC_PRINTF ("Failed adding SmartSurface element to Standard Surface cell, ID=%I64d!\n", context.ElementIdFromObject(acObject));
        return  CantCreateSurface;
        }

    AddSurfaceParameters (outElement, acSurface, context);

    NormalCellHeaderHandler::AddChildComplete (outElement);

    return  RealDwgSuccess;
    }

};  // ToDgnExtSurface



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtStandardSurfaceBase : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbSurface::desc());
    AcDbSurface*        acSurface = AcDbSurface::cast (acObject);
    if (NULL == acSurface)
        return  NullObject;

    AcGeMatrix3d        geMatrix = AcGeMatrix3d::kIdentity;
    RealDwgStatus       status = this->ExtractMatrixFromStandardSurface (geMatrix, elemHandle, context);
    if (RealDwgSuccess == status)
        {
        DefaultParameters   defaultParameters;
        if (BSISUCCESS != defaultParameters._ExtractFromElement(elemHandle) ||
            BSISUCCESS != ExtractNonStandardSurface(acSurface, defaultParameters, elemHandle, context))
            status = MstnElementUnacceptable;
        }

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractMatrixFromStandardSurface (AcGeMatrix3d& geMatrix, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    RotMatrix       rMatrix;
    DPoint3d        origin;

    RealDwgStatus   status = context.ExtractTransformFromCell (origin, rMatrix, inElement);
    if (RealDwgSuccess != status)
        return  status;

    if (origin.maxAbs() >= TOLERANCE_UORPointEqual || !rMatrix.isIdentity())
        {
        context.GetTransformFromDGN().Multiply (origin);

        DVec3d      xAxis, yAxis, zAxis;
        rMatrix.GetColumns (xAxis, yAxis, zAxis);

        geMatrix.setCoordSystem (RealDwgUtil::GePoint3dFromDPoint3d(origin), RealDwgUtil::GeVector3dFromDVec3d(xAxis),
                                 RealDwgUtil::GeVector3dFromDVec3d(yAxis), RealDwgUtil::GeVector3dFromDVec3d(zAxis));
        }
    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveStandardSurfaceElementToAcis (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    bool            isHeaderBrep = BrepCellHeaderHandler::IsBRepDataValid (inElement);
    ChildElemIter   firstChild (inElement);
    HandlerR        handler = isHeaderBrep ? inElement.GetHandler() : firstChild.GetHandler();

    ToDwgExtension* toDwg = ToDwgExtension::Cast (handler);
    if (NULL != toDwg)
        return toDwg->ToObject (isHeaderBrep ? inElement : firstChild, acObject, existingObject, context);

    return  MstnElementUnacceptable;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertSurfaceToAcis (AcDbObjectP& acObject, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    /*-------------------------------------------------------------------------------
    Creating from surface parameters failed.  Try copy ACIS data from element.
    Ideally, we'd rather call AcDbSurface::setBody for this, but it causes database
    writing to fail.
    The input element may be our cell containing a smart element but it may also be
    a smart element that gets dropped.
    -------------------------------------------------------------------------------*/
    ChildElemIter   firstChild (inElement);
    AcDbObjectP     newObject = NULL;
    RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, BrepCellHeaderHandler::IsBRepDataValid(inElement) ? inElement : firstChild);
    if (ReplacedObjectType == status && NULL != newObject)
        {
        if (acObject->objectId().isValid())
            {
            if (Acad::eOk != acObject->handOverTo(newObject))
                acObject->erase ();
            }
        else if (acObject->isNewObject())
            {
            delete acObject;
            }
        acObject = newObject;
        status = RealDwgSuccess;
        }
    else if (RealDwgSuccess != status && ReplacedObjectType != status && WorldDrawFailed != status)
        {
        DropGeometry    dropSolids (DropGeometry::OPTION_Solids);
        dropSolids.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

        status = context.DropElementToDwg (acObject, NULL, BrepCellHeaderHandler::IsBRepDataValid(inElement) ? inElement : firstChild, dropSolids);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
void            ExtractBaseSurface (AcDbSurface* acSurface, SurfaceParametersBase& baseParameters) const
    {
    acSurface->setUIsolineDensity (baseParameters._GetUIsoLines());
    acSurface->setVIsolineDensity (baseParameters._GetVIsoLines());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
void            ExtractSweepOptions (AcDbSweepOptions& sweep, SweepParameters& sweepParameters) const
    {
    sweep.setAlign ((AcDbSweepOptions::AlignOption) sweepParameters.GetAlign());
    sweep.setAlignAngle (sweepParameters.GetAlignAngle());
    sweep.setDraftAngle (sweepParameters.GetDraftAngle());
    sweep.setTwistAngle (sweepParameters.GetTwistAngle());
    sweep.setScaleFactor (sweepParameters.GetScale());
    sweep.setBank (sweepParameters.IsBanked());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
int             ExtractControlElements
(
AcArray<AcDbEntity*>*       pCrossSections,
AcArray<AcDbEntity*>*       pGuideCurves,
AcDbEntity**                ppPathCurve,
AcDbEntity**                ppSweepCurve,
AcDbEntity**                ppRevolveCurve,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    StandardSurfaceXAttrID  xAttrId = XATTRID_StandardSurfaceInvalid;
    int                     nCntrlEnts = 0;

    // loop through cell to find control curves, if there is any
    for (ChildElemIter child(cellElem, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
        {
        if (CELL_HEADER_ELM != child.GetElementType() && SUCCESS == StandardSurfaceBaseHandler::GetControlTypeFromElement(&xAttrId, child))
            {
            AcDbObject*         acObject = NULL;
            RealDwgStatus       status = context.CreateObjectFromElement (acObject, child);

            AcDbEntity*         acEntity = AcDbEntity::cast (acObject);
            if (RealDwgSuccess != status || NULL == acEntity)
                {
                DIAGNOSTIC_PRINTF ("Failed creating control element: %I64d, in standard surface %I64d\n", child.GetElementId(), cellElem.GetElementId());
                if (NULL != acObject)
                    {
                    if (acObject->isNewObject())
                        delete acObject;
                    else
                        acObject->erase ();
                    }
                continue;
                }

            nCntrlEnts++;

            switch (xAttrId)
                {
                case XATTRID_StandardSurfaceCrossSection:
                    if (NULL != pCrossSections)
                        pCrossSections->append (acEntity);
                    break;
                case XATTRID_StandardSurfaceGuideCurve:
                    if (NULL != pGuideCurves)
                        pGuideCurves->append (acEntity);
                    break;
                case XATTRID_StandardSurfacePathCurve:
                    if (NULL != ppPathCurve)
                        *ppPathCurve = acEntity;
                    break;
                case XATTRID_StandardSurfaceSweepCurve:
                    if (NULL != ppSweepCurve)
                        *ppSweepCurve = acEntity;
                    break;
                case XATTRID_StandardSurfaceRevolveCurve:
                    if (NULL != ppRevolveCurve)
                        *ppRevolveCurve = acEntity;
                    break;

                default:
                    nCntrlEnts--;
                    DIAGNOSTIC_PRINTF ("Unknown control element type: %d, in standard surface element %I64d\n", xAttrId, cellElem.GetElementCP()->ehdr.uniqueId);
                }
            }
        }

    return  nCntrlEnts;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractNonStandardSurface
(
AcDbSurface*                acSurface,
DefaultParameters&          defaultParameters,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    /*-----------------------------------------------------------------------------------
    Below logic is what I'd like to have to create a none standard surface with just ACIS
    data in it.  But it causes an error in modeler which seems to attempt to delete a
    pointer that it did not allocate.  Since setBody() is not published, I do not know
    what modelerBody should be.  Maybe it has to be allocated by modeler.  Who knows.
    Before we can figure that out so we can do it the right way, we just return an
    error to the caller where it will let acisIn to create a whatever ACIS entity acisIn
    will give to us.


    ExtractBaseSurface (acSurface, defaultParameters);

    ElementHandle      smartSolid (cellElem.GetElementDescrCP()->h.firstElem, false, false);
    AcDbBody*       body = new AcDbBody ();

    StatusInt       status = context.ConvertSolidElementToAcis (body, smartSolid);
    if (SUCCESS == status)
        {
        body->close ();

        Acad::ErrorStatus   es = acSurface->setBody (body);
        if (Acad::eOk != es)
            status = BSIERROR;
        }

    if (SUCCESS != status)
        delete body;

    -----------------------------------------------------------------------------------*/
    return  NotApplicable;
    }

};  // ToDwgExtStandardSurfaceBase

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSweptSurface : public ToDwgExtStandardSurfaceBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbSweptSurface::desc());
    AcDbSweptSurface*   acSweptSurface = AcDbSweptSurface::cast (acObject);
    if (NULL == acSweptSurface)
        return  NullObject;

    AcGeMatrix3d        geMatrix = AcGeMatrix3d::kIdentity;
    RealDwgStatus       status = this->ExtractMatrixFromStandardSurface (geMatrix, elemHandle, context);
    if (RealDwgSuccess == status)
        {
        SweepParameters     sweepParameters;
        if (BSISUCCESS != sweepParameters._ExtractFromElement(elemHandle) ||
            BSISUCCESS != this->ExtractSweptSurface(acSweptSurface, sweepParameters, geMatrix, elemHandle, context))
            status = MstnElementUnacceptable;
        }

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ExtractSweptSurface
(
AcDbSweptSurface*       acSweptSurace,
SweepParameters&        sweepParameters,
AcGeMatrix3d&           pCellMatrix,
ElementHandleCR         cellElem,
ConvertFromDgnContext&  context
) const
    {
    AcDbSweepOptions    sweepOptions;

    this->ExtractSweepOptions (sweepOptions, sweepParameters);

    AcGePoint3d         basePoint(0,0,0);
    if (sweepParameters.IsBasePointUsed())
        {
        AcGeVector3d    xAxis, yAxis, zAxis;
        pCellMatrix.getCoordSystem (basePoint, xAxis, yAxis, zAxis);
        }

    sweepOptions.setBasePoint (basePoint);

    AcGeMatrix3d        geMatrix;

    // update path entity transformation
    sweepOptions.getPathEntityTransform (geMatrix);
    sweepOptions.setPathEntityTransform (pCellMatrix.postMultBy(geMatrix));

    // update sweeping entity transformation
    sweepOptions.getSweepEntityTransform (geMatrix);
    sweepOptions.setSweepEntityTransform (pCellMatrix.preMultBy(geMatrix));

    this->ExtractBaseSurface (acSweptSurace, sweepParameters);

    AcDbEntity*         acPathCurve = NULL;
    AcDbEntity*         acSweepCurve = NULL;
    if (this->ExtractControlElements(NULL, NULL, &acPathCurve, &acSweepCurve, NULL, cellElem, context) > 0)
        {
        Acad::ErrorStatus es = acSweptSurace->createSweptSurface (acSweepCurve, acPathCurve, sweepOptions);

        if (NULL != acPathCurve && acPathCurve->objectId().isValid())
            acPathCurve->close ();
        if (NULL != acSweepCurve && acSweepCurve->objectId().isValid())
            acSweepCurve->close ();

        if (Acad::eOk == es)
            return  RealDwgSuccess;
        else
            DIAGNOSTIC_PRINTF ("Error creating swept surface ID=%I64d: %ls\n", cellElem.GetElementRef()->GetElementId(), acadErrorStatusText(es));
        }

    return  MstnElementUnacceptable;
    }

};  // ToDwgExtSweptSurface

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtExtrudedSurface : public ToDwgExtStandardSurfaceBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbExtrudedSurface::desc());
    AcDbExtrudedSurface*   acExtrudedSurface = AcDbExtrudedSurface::cast (acObject);
    if (NULL == acExtrudedSurface)
        return  NullObject;

    AcGeMatrix3d        geMatrix = AcGeMatrix3d::kIdentity;
    RealDwgStatus       status = this->ExtractMatrixFromStandardSurface (geMatrix, elemHandle, context);
    if (RealDwgSuccess == status)
        {
        ExtrudeParameters   extrudeParameters;
        if (BSISUCCESS != extrudeParameters._ExtractFromElement(elemHandle) ||
            BSISUCCESS != this->ExtractExtrudedSurface(acExtrudedSurface, extrudeParameters, geMatrix, elemHandle, context))
            status = MstnElementUnacceptable;
        }

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus        ExtractExtrudedSurface
(
AcDbExtrudedSurface*        acExtrudedSurface,
ExtrudeParameters&          extrudedParameters,
AcGeMatrix3d&               cellMatrix,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    SweepParameters     sweepParams;
    extrudedParameters.GetSweepParameters (sweepParams);

    AcDbSweepOptions    sweepOptions;
    this->ExtractSweepOptions (sweepOptions, sweepParams);

    sweepOptions.setSweepEntityTransform (cellMatrix);

    AcGeVector3d        sweepVector = RealDwgUtil::GeVector3dFromDVec3d (extrudedParameters.GetSweepVector());

    this->ExtractBaseSurface (acExtrudedSurface, extrudedParameters);

    AcDbEntity*         acSweepCurve = NULL;
    if (this->ExtractControlElements(NULL, NULL, NULL, &acSweepCurve, NULL, cellElem, context) > 0)
        {
        Acad::ErrorStatus   es = acExtrudedSurface->createExtrudedSurface (acSweepCurve, sweepVector, sweepOptions);

        if (NULL != acSweepCurve && acSweepCurve->objectId().isValid())
            acSweepCurve->close ();

        return Acad::eOk == Acad::eOk ? RealDwgSuccess : CantCreateSurface;
        }

    return  MstnElementUnacceptable;
    }

};  // ToDwgExtExtrudedSurface

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtRevolvedSurface : public ToDwgExtStandardSurfaceBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbRevolvedSurface::desc());
    AcDbRevolvedSurface*   acRevolvedSurface = AcDbRevolvedSurface::cast (acObject);
    if (NULL == acRevolvedSurface)
        return  NullObject;

    AcGeMatrix3d        geMatrix = AcGeMatrix3d::kIdentity;
    RealDwgStatus       status = this->ExtractMatrixFromStandardSurface (geMatrix, elemHandle, context);
    if (RealDwgSuccess == status)
        {
        RevolveParameters   revolveParameters;
        if (BSISUCCESS != revolveParameters._ExtractFromElement(elemHandle) ||
            BSISUCCESS != this->ExtractRevolvedSurface(acRevolvedSurface, revolveParameters, geMatrix, elemHandle, context))
            status = MstnElementUnacceptable;
        }

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ExtractRevolvedSurface
(
AcDbRevolvedSurface*        acRevolvedSurface,
RevolveParameters&          revolveParameters,
AcGeMatrix3d&               cellMatrix,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    AcGePoint3d         acisPoint;
    AcGeVector3d        xAxis, yAxis, zAxis;

    cellMatrix.getCoordSystem (acisPoint, xAxis, yAxis, zAxis);

    AcGeVector3d        axis = RealDwgUtil::GeVector3dFromDPoint3d (revolveParameters.GetAxis());
    if (axis.isZeroLength())
        {
        axis.set (0, 0, 1);
        DIAGNOSTIC_PRINTF ("Zero revolving axis normal on revolved surface %I64d\n", cellElem.GetElementCP()->ehdr.uniqueId);
        }

    AcDbRevolveOptions  revolveOptions;

    acRevolvedSurface->getRevolveOptions (revolveOptions);

    revolveOptions.setCloseToAxis (revolveParameters.GetFlags().GetIsCloseToAxis());
    revolveOptions.setDraftAngle (revolveParameters.GetDraftAngle());
    revolveOptions.setTwistAngle (revolveParameters.GetTwistAngle());

    this->ExtractBaseSurface (acRevolvedSurface, revolveParameters);

    AcDbEntity*         acRevolvedCurve = NULL;
    if (this->ExtractControlElements(NULL, NULL, NULL, NULL, &acRevolvedCurve, cellElem, context) > 0)
        {
        Acad::ErrorStatus   es = acRevolvedSurface->createRevolvedSurface (acRevolvedCurve, acisPoint, axis, revolveParameters.GetRevolvedAngle(), revolveParameters.GetStartAngle(), revolveOptions);

        if (NULL != acRevolvedCurve && acRevolvedCurve->objectId().isValid())
            acRevolvedCurve->close ();

        return  Acad::eOk == es ? RealDwgSuccess : CantCreateSurface;
        }

    return  MstnElementUnacceptable;
    }

};  // ToDwgExtRevolvedSurface

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtLoftedSurface : public ToDwgExtStandardSurfaceBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbLoftedSurface::desc());
    AcDbLoftedSurface*   acLoftedSurface = AcDbLoftedSurface::cast (acObject);
    if (NULL == acLoftedSurface)
        return  NullObject;

    RealDwgStatus   status;
    LoftParameters  loftParameters;
    if (BSISUCCESS == loftParameters._ExtractFromElement(elemHandle) &&
        BSISUCCESS == this->ExtractLoftedSurface(acLoftedSurface, loftParameters, elemHandle, context))
        status = RealDwgSuccess;
    else
        status = MstnElementUnacceptable;

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractLoftedSurface
(
AcDbLoftedSurface*          acLoftedSurface,
LoftParameters&             loftParameters,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    AcDbLoftOptions     loftOptions;

    acLoftedSurface->getLoftOptions (loftOptions);

    loftOptions.setDraftStart (loftParameters.GetStartDraftAngle());
    loftOptions.setDraftEnd (loftParameters.GetEndDraftAngle());
    loftOptions.setDraftStartMag (loftParameters.GetStartDraftMagnitude());
    loftOptions.setDraftEndMag (loftParameters.GetEndDraftMagnitude());
    loftOptions.setNormal ((AcDbLoftOptions::NormalOption) loftParameters.GetNormalOption());
    loftOptions.setAlignDirection (loftParameters.GetFlags().GetAlign());
    loftOptions.setClosed (loftParameters.GetFlags().GetClosed());
    loftOptions.setNoTwist (!loftParameters.GetFlags().GetTwisted());
    loftOptions.setArcLengthParam (loftParameters.GetFlags().GetArcLengthParam());

    this->ExtractBaseSurface (acLoftedSurface, loftParameters);

    AcArray<AcDbEntity*>    crossSections, guideCurves;
    AcDbEntity*             acPathCurve = NULL;

    if (this->ExtractControlElements(&crossSections, &guideCurves, &acPathCurve, NULL, NULL, cellElem, context) > 0)
        {
        Acad::ErrorStatus   es = acLoftedSurface->createLoftedSurface(crossSections, guideCurves, acPathCurve, loftOptions);

        for (int i = 0; i < crossSections.length(); i++)
            {
            AcDbEntityP     entity = crossSections.at (i);
            if (NULL != entity && entity->objectId().isValid())
                entity->close ();
            }
        if (NULL != acPathCurve && acPathCurve->objectId().isValid())
            acPathCurve->close ();

        return Acad::eOk == es ? RealDwgSuccess : CantCreateSurface;
        }

    return  MstnElementUnacceptable;
    }

};  // ToDwgExtLoftedSurface

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/13
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtPlaneSurface : public ToDwgExtStandardSurfaceBase
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetTargetVersion() < DwgFileVersion_2007)
        return this->SaveStandardSurfaceElementToAcis (acObject, existingObject, elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbPlaneSurface::desc());
    AcDbPlaneSurface*   acPlaneSurface = AcDbPlaneSurface::cast (acObject);
    if (NULL == acPlaneSurface)
        return  NullObject;

    RealDwgStatus       status;
    PlaneParameters     planeParameters;
    if (BSISUCCESS == planeParameters._ExtractFromElement(elemHandle) &&
        BSISUCCESS == this->ExtractPlaneSurface(acPlaneSurface, planeParameters, elemHandle, context))
        status = RealDwgSuccess;
    else
        status = MstnElementUnacceptable;

    if (RealDwgSuccess != status)
        status = this->ConvertSurfaceToAcis (acObject, elemHandle, context);

    if (RealDwgSuccess == status && NULL != acObject)
        context.UpdateEntityPropertiesFromElement (AcDbEntity::cast(acObject), elemHandle);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/07
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractPlaneSurface
(
AcDbPlaneSurface*           acPlaneSurface,
PlaneParameters&            planeParameters,
ElementHandleCR             cellElem,
ConvertFromDgnContext&      context
) const
    {
    this->ExtractBaseSurface (acPlaneSurface, planeParameters);

    ElementHandle       smartSolid (cellElem.GetElementDescrCP()->h.firstElem, false, false);
    AcDbRegion*         region = new AcDbRegion ();
    AcDbObjectP         newObject = region;
    AcDbObjectP         replacingObject = NULL;

    RealDwgStatus      status = context.ConvertSolidElementToAcis (replacingObject, newObject, smartSolid);
    if (RealDwgSuccess == status)
        {
        region->close ();

        if (Acad::eOk != acPlaneSurface->createFromRegion(region))
            status = CantCreateSurface;
        }
    else if (ReplacedObjectType == status && NULL != replacingObject)
        {
        // if we can't create a REGION, we can't create a plane surface - clean up and return.
        if (replacingObject->objectId().isValid())
            replacingObject->close ();
        else
            delete  replacingObject;

        status = CantCreateSurface;
        }

    delete region;

    return  status;
    }

};  // ToDwgExtPlaneSurface
