#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>
#include <unordered_map>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbminsert.h>
#include <realdwg/base/dbblktbl.h>
#include <realdwg/base/dbblkref.h>
#include <realdwg/base/dbattdef.h>
#include <realdwg/base/dbattrib.h>
#include <realdwg/base/dbdynblk.h>
#include <realdwg/ge/gematrix3d.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// Block Data Structures (Based on RealDwgFileIO rdBlock.cpp and rdBlockReference.cpp)
//=======================================================================================

struct AttributeDefinition {
    std::string tag;                    // Attribute tag
    std::string prompt;                 // Attribute prompt
    std::string defaultValue;           // Default value
    Point3d position;                   // Text position
    double height = 2.5;                // Text height
    double rotation = 0.0;              // Text rotation
    double widthFactor = 1.0;           // Width factor
    double obliqueAngle = 0.0;          // Oblique angle
    std::string textStyleName = "Standard"; // Text style
    
    // Attribute flags
    bool isInvisible = false;           // Invisible attribute
    bool isConstant = false;            // Constant attribute
    bool isVerify = false;              // Verify attribute
    bool isPreset = false;              // Preset attribute
    bool isLockPosition = false;        // Lock position
    
    // Text alignment
    enum class Alignment {
        Left, Center, Right, Aligned, Middle, Fit
    } alignment = Alignment::Left;
    
    Point3d alignmentPoint;             // Alignment point
    
    // Text properties
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string layer = "0";
    
    bool IsValid() const;
};

struct AttributeInstance {
    std::string tag;                    // Attribute tag
    std::string value;                  // Attribute value
    Point3d position;                   // Text position
    double height = 2.5;                // Text height
    double rotation = 0.0;              // Text rotation
    double widthFactor = 1.0;           // Width factor
    double obliqueAngle = 0.0;          // Oblique angle
    std::string textStyleName = "Standard"; // Text style
    
    // Attribute properties
    bool isInvisible = false;           // Invisible attribute
    bool isLockPosition = false;        // Lock position
    
    // Text alignment
    AttributeDefinition::Alignment alignment = AttributeDefinition::Alignment::Left;
    Point3d alignmentPoint;             // Alignment point
    
    // Text properties
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string layer = "0";
    
    bool IsValid() const;
};

struct BlockDefinition {
    std::string name;                   // Block name
    std::string description;            // Block description
    Point3d basePoint;                  // Block base point
    
    // Block properties
    bool isAnonymous = false;           // Anonymous block
    bool hasAttributes = false;         // Has attribute definitions
    bool isXRef = false;                // External reference
    bool isOverlay = false;             // Overlay XRef
    bool isFromExternalReference = false; // From XRef
    bool isFromOverlayReference = false;  // From overlay
    bool isDependent = false;           // Dependent on XRef
    bool isResolved = true;             // XRef resolved
    
    // XRef properties
    std::string xrefPath;               // XRef file path
    bool isUnloaded = false;            // XRef unloaded
    
    // Block entities (simplified - would contain actual entity data)
    struct BlockEntity {
        std::string entityType;         // Entity type name
        std::vector<Point3d> geometry;  // Simplified geometry
        std::string layer = "0";        // Entity layer
        Color color = Color(1.0f, 1.0f, 1.0f, 1.0f); // Entity color
        std::unordered_map<std::string, std::string> properties; // Additional properties
    };
    
    std::vector<BlockEntity> entities;  // Block entities
    std::vector<AttributeDefinition> attributeDefinitions; // Attribute definitions
    
    // Block units and scaling
    enum class Units {
        Unitless, Inches, Feet, Miles, Millimeters, Centimeters, Meters, Kilometers,
        Microinches, Mils, Yards, Angstroms, Nanometers, Microns, Decimeters,
        Decameters, Hectometers, Gigameters, AstronomicalUnits, LightYears, Parsecs
    } units = Units::Unitless;
    
    double unitScale = 1.0;             // Unit scale factor
    bool explodable = true;             // Block can be exploded
    
    bool IsValid() const;
    bool HasGeometry() const { return !entities.empty(); }
    size_t GetEntityCount() const { return entities.size(); }
    size_t GetAttributeCount() const { return attributeDefinitions.size(); }
};

struct BlockReference {
    std::string blockName;              // Referenced block name
    Point3d position;                   // Insertion point
    Vector3d scale = Vector3d(1, 1, 1); // Scale factors
    double rotation = 0.0;              // Rotation angle
    Vector3d normal = Vector3d(0, 0, 1); // Normal vector
    
    // Transformation matrix (alternative to position/scale/rotation)
    bool useTransformMatrix = false;
    double transformMatrix[4][4];       // 4x4 transformation matrix
    
    // Attribute instances
    std::vector<AttributeInstance> attributes;
    
    // Block reference properties
    std::string layer = "0";
    Color color = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string lineType = "Continuous";
    double lineWeight = 0.25;
    double transparency = 0.0;
    bool isVisible = true;
    
    // Array properties (for MInsert)
    bool isArrayInsert = false;         // Multiple insert
    int rowCount = 1;                   // Number of rows
    int columnCount = 1;                // Number of columns
    double rowSpacing = 0.0;            // Row spacing
    double columnSpacing = 0.0;         // Column spacing
    
    // Dynamic block properties
    bool isDynamicBlock = false;        // Dynamic block
    std::unordered_map<std::string, double> dynamicProperties; // Dynamic properties
    
    bool IsValid() const;
    bool HasAttributes() const { return !attributes.empty(); }
    bool IsArrayInsert() const { return isArrayInsert && (rowCount > 1 || columnCount > 1); }
    size_t GetAttributeCount() const { return attributes.size(); }
};

//=======================================================================================
// Block Validation Results
//=======================================================================================

struct BlockValidationResult : public DWGValidationResult {
    bool hasValidName = false;
    bool hasValidGeometry = false;
    bool hasValidAttributes = false;
    bool hasValidBasePoint = false;
    bool hasValidXRefPath = false;
    int entityCount = 0;
    int attributeCount = 0;
    
    void AddBlockError(const std::string& error) {
        AddError("Block: " + error);
    }
    
    void AddBlockWarning(const std::string& warning) {
        AddWarning("Block: " + warning);
    }
};

struct BlockReferenceValidationResult : public DWGValidationResult {
    bool hasValidBlockName = false;
    bool hasValidPosition = false;
    bool hasValidScale = false;
    bool hasValidRotation = false;
    bool hasValidAttributes = false;
    bool blockExists = false;
    
    void AddBlockRefError(const std::string& error) {
        AddError("BlockReference: " + error);
    }
    
    void AddBlockRefWarning(const std::string& warning) {
        AddWarning("BlockReference: " + warning);
    }
};

//=======================================================================================
// DWG Block Processor (Based on RealDwgFileIO rdBlock.cpp and rdBlockReference.cpp)
//=======================================================================================

class DWGBlockProcessor : public DWGEntityProcessor {
public:
    DWGBlockProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGBlockProcessor"; }

    // Block processing methods
    DWGProcessingStatus ProcessBlockDefinition(const BlockDefinition& definition);
    DWGProcessingStatus ProcessBlockReference(const BlockReference& reference, const std::string& layer = "");
    DWGProcessingStatus ProcessMInsertBlock(const BlockReference& reference, const std::string& layer = "");
    DWGProcessingStatus ProcessXRefBlock(const BlockDefinition& definition);

    // Attribute processing
    DWGProcessingStatus ProcessAttributeDefinition(const AttributeDefinition& attDef, const std::string& blockName);
    DWGProcessingStatus ProcessAttributeInstance(const AttributeInstance& attribute, const std::string& blockName);
    
    // Block management
    bool CreateBlockDefinition(const BlockDefinition& definition);
    bool UpdateBlockDefinition(const BlockDefinition& definition);
    bool DeleteBlockDefinition(const std::string& blockName);
    bool BlockExists(const std::string& blockName) const;
    
    // Block library management
    bool LoadBlockLibrary(const std::string& libraryPath);
    bool SaveBlockLibrary(const std::string& libraryPath) const;
    std::vector<std::string> GetAvailableBlocks() const;
    BlockDefinition GetBlockDefinition(const std::string& blockName) const;

    // Validation methods
    BlockValidationResult ValidateBlockDefinition(const BlockDefinition& definition) const;
    BlockReferenceValidationResult ValidateBlockReference(const BlockReference& reference) const;
    bool ValidateBlockName(const std::string& name) const;
    bool ValidateAttributeDefinition(const AttributeDefinition& attDef) const;
    bool ValidateAttributeInstance(const AttributeInstance& attribute) const;
    bool ValidateXRefPath(const std::string& path) const;

    // Block transformation and geometry
    std::vector<Point3d> TransformBlockGeometry(const BlockDefinition& definition, const BlockReference& reference) const;
    Point3d TransformPoint(const Point3d& point, const BlockReference& reference) const;
    Vector3d TransformVector(const Vector3d& vector, const BlockReference& reference) const;
    BoundingBox3D CalculateBlockBounds(const BlockDefinition& definition) const;
    BoundingBox3D CalculateReferenceBounds(const BlockReference& reference) const;

    // Attribute processing helpers
    bool ProcessBlockAttributes(const BlockReference& reference) const;
    bool ValidateAttributeValues(const BlockReference& reference) const;
    std::string GetAttributeValue(const BlockReference& reference, const std::string& tag) const;
    bool SetAttributeValue(BlockReference& reference, const std::string& tag, const std::string& value) const;

    // Block optimization and repair
    bool RepairBlockDefinition(BlockDefinition& definition) const;
    bool RepairBlockReference(BlockReference& reference) const;
    bool OptimizeBlockGeometry(BlockDefinition& definition) const;
    bool SimplifyBlockGeometry(BlockDefinition& definition, double tolerance = 1e-6) const;

    // XRef management
    bool LoadXRef(const std::string& path, const std::string& blockName);
    bool UnloadXRef(const std::string& blockName);
    bool ReloadXRef(const std::string& blockName);
    bool BindXRef(const std::string& blockName, bool insertBind = false);
    std::vector<std::string> GetXRefList() const;
    bool IsXRefLoaded(const std::string& blockName) const;

private:
    // Block definition management
    std::unordered_map<std::string, BlockDefinition> m_blockDefinitions;
    std::unordered_map<std::string, std::string> m_xrefPaths;
    std::unordered_map<std::string, bool> m_xrefLoadStatus;
    
    // Block name validation and sanitization
    bool IsValidBlockName(const std::string& name) const;
    std::string SanitizeBlockName(const std::string& name) const;
    std::string GenerateUniqueBlockName(const std::string& baseName) const;
    bool IsReservedBlockName(const std::string& name) const;
    
    // Geometry processing helpers
    bool ProcessBlockEntity(const BlockDefinition::BlockEntity& entity, const std::string& blockName) const;
    bool ValidateBlockGeometry(const BlockDefinition& definition) const;
    bool CalculateBlockBasePoint(BlockDefinition& definition) const;
    
    // Transformation helpers
    void CalculateTransformMatrix(const BlockReference& reference, double matrix[4][4]) const;
    Point3d ApplyTransformMatrix(const Point3d& point, const double matrix[4][4]) const;
    Vector3d ApplyTransformMatrix(const Vector3d& vector, const double matrix[4][4]) const;
    
    // Attribute processing helpers
    bool ProcessAttributeDefinitions(const std::vector<AttributeDefinition>& attDefs, const std::string& blockName) const;
    bool ValidateAttributeTag(const std::string& tag) const;
    bool ValidateAttributeValue(const std::string& value) const;
    Point3d CalculateAttributePosition(const AttributeDefinition& attDef, const BlockReference& reference) const;
    
    // XRef processing helpers
    bool LoadXRefFile(const std::string& path, BlockDefinition& definition);
    bool ProcessXRefDependencies(const BlockDefinition& definition);
    bool ValidateXRefIntegrity(const std::string& blockName) const;
    std::string ResolveXRefPath(const std::string& relativePath) const;
    
    // Array insert processing
    bool ProcessArrayInsert(const BlockReference& reference) const;
    std::vector<Point3d> CalculateArrayPositions(const BlockReference& reference) const;
    bool ValidateArrayParameters(const BlockReference& reference) const;
    
    // Dynamic block processing
    bool ProcessDynamicBlock(const BlockReference& reference) const;
    bool ValidateDynamicProperties(const BlockReference& reference) const;
    bool ApplyDynamicProperties(const BlockReference& reference) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbBlockTableRecord* CreateDWGBlockDefinition(const BlockDefinition& definition) const;
    AcDbBlockReference* CreateDWGBlockReference(const BlockReference& reference) const;
    AcDbMInsertBlock* CreateDWGMInsertBlock(const BlockReference& reference) const;
    AcDbAttributeDefinition* CreateDWGAttributeDefinition(const AttributeDefinition& attDef) const;
    AcDbAttribute* CreateDWGAttribute(const AttributeInstance& attribute) const;
    
    // Block table management
    bool AddBlockToTable(AcDbBlockTableRecord* blockRecord) const;
    AcDbObjectId GetBlockTableRecordId(const std::string& blockName) const;
    bool BlockExistsInTable(const std::string& blockName) const;
    
    // XRef management
    AcDbObjectId AttachXRef(const std::string& path, const std::string& blockName) const;
    bool DetachXRef(const std::string& blockName) const;
    bool ReloadXRefRecord(const std::string& blockName) const;
    
    // Property setting helpers
    bool SetBlockReferenceProperties(AcDbBlockReference* blockRef, const BlockReference& reference) const;
    bool SetAttributeProperties(AcDbAttribute* attribute, const AttributeInstance& attInst) const;
    bool SetBlockDefinitionProperties(AcDbBlockTableRecord* blockRecord, const BlockDefinition& definition) const;
    
    // Error handling for RealDWG operations
    bool HandleBlockCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedBlockDefinitions = 0;
    mutable size_t m_processedBlockReferences = 0;
    mutable size_t m_processedAttributes = 0;
    mutable size_t m_processedXRefs = 0;
    mutable size_t m_repairedBlocks = 0;
    
    // Configuration
    double m_blockTolerance = 1e-6;
    double m_attributeTolerance = 1e-3;
    bool m_enableBlockOptimization = true;
    bool m_enableAttributeValidation = true;
    bool m_enableXRefValidation = true;
    bool m_autoRepairBlocks = true;
    int m_maxBlockNestingLevel = 10;
    size_t m_maxBlockEntities = 10000;
    
    // Block library paths
    std::vector<std::string> m_blockLibraryPaths;
    std::string m_defaultBlockLibrary;
};

} // namespace IModelExport
