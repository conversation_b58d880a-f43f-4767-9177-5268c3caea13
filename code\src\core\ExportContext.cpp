#include "ExportContext.h"
#include "GeometryConverter.h"
#include "MaterialManager.h"
#include "TextureManager.h"

#include <algorithm>
#include <thread>

namespace IModelExport {

//=======================================================================================
// ExportContext Implementation
//=======================================================================================

ExportContext::ExportContext() 
    : m_coordinateTransform(Transform3d::Identity())
    , m_unitsScale(1.0)
    , m_geometryTolerance(1e-6)
    , m_angleTolerance(1e-6)
    , m_errorHandling(ErrorHandling::SkipOnError)
{
    // Initialize default components
    m_geometryConverter = std::make_shared<GeometryConverter>();
    m_materialManager = std::make_shared<MaterialManager>();
    m_textureManager = std::make_shared<TextureManager>();
}

ExportContext::~ExportContext() {
    ClearCache();
}

//=======================================================================================
// Geometry Conversion
//=======================================================================================

std::shared_ptr<GeometryConverter> ExportContext::GetGeometryConverter() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_geometryConverter;
}

void ExportContext::SetGeometryConverter(std::shared_ptr<GeometryConverter> converter) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_geometryConverter = converter;
}

void ExportContext::SetCoordinateTransform(const Transform3d& transform) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_coordinateTransform = transform;
}

Transform3d ExportContext::GetCoordinateTransform() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_coordinateTransform;
}

Point3d ExportContext::TransformPoint(const Point3d& point) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_geometryConverter->TransformPoint(point, m_coordinateTransform);
}

Vector3d ExportContext::TransformVector(const Vector3d& vector) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_geometryConverter->TransformVector(vector, m_coordinateTransform);
}

void ExportContext::SetUnitsConversion(double sourceUnitsPerMeter, double targetUnitsPerMeter) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    if (sourceUnitsPerMeter > 0.0 && targetUnitsPerMeter > 0.0) {
        m_unitsScale = targetUnitsPerMeter / sourceUnitsPerMeter;
    }
}

double ExportContext::GetUnitsScale() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_unitsScale;
}

double ExportContext::ConvertLength(double length) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return length * m_unitsScale;
}

//=======================================================================================
// Material and Texture Management
//=======================================================================================

std::shared_ptr<MaterialManager> ExportContext::GetMaterialManager() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_materialManager;
}

void ExportContext::SetMaterialManager(std::shared_ptr<MaterialManager> manager) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_materialManager = manager;
}

std::shared_ptr<TextureManager> ExportContext::GetTextureManager() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_textureManager;
}

void ExportContext::SetTextureManager(std::shared_ptr<TextureManager> manager) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_textureManager = manager;
}

std::string ExportContext::RegisterMaterial(const Material& material) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_materialManager->RegisterMaterial(material);
}

bool ExportContext::GetMaterial(const std::string& materialId, Material& material) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_materialManager->GetMaterial(materialId, material);
}

std::vector<std::string> ExportContext::GetAllMaterialIds() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_materialManager->GetAllMaterialIds();
}

//=======================================================================================
// Element Tracking and Mapping
//=======================================================================================

void ExportContext::MapElementId(const std::string& imodelId, const std::string& exportId) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_elementIdMap[imodelId] = exportId;
}

bool ExportContext::GetMappedElementId(const std::string& imodelId, std::string& exportId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementIdMap.find(imodelId);
    if (it != m_elementIdMap.end()) {
        exportId = it->second;
        return true;
    }
    return false;
}

std::unordered_map<std::string, std::string> ExportContext::GetAllElementMappings() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_elementIdMap;
}

void ExportContext::MarkElementProcessed(const std::string& imodelId) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_processedElements.insert(imodelId);
}

bool ExportContext::IsElementProcessed(const std::string& imodelId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_processedElements.find(imodelId) != m_processedElements.end();
}

void ExportContext::ClearProcessedElements() {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_processedElements.clear();
}

//=======================================================================================
// Element Statistics
//=======================================================================================

void ExportContext::IncrementProcessedCount() {
    m_processedCount++;
}

void ExportContext::IncrementSkippedCount() {
    m_skippedCount++;
}

void ExportContext::IncrementErrorCount() {
    m_errorCount++;
}

size_t ExportContext::GetProcessedCount() const {
    return m_processedCount.load();
}

size_t ExportContext::GetSkippedCount() const {
    return m_skippedCount.load();
}

size_t ExportContext::GetErrorCount() const {
    return m_errorCount.load();
}

void ExportContext::ResetStatistics() {
    m_processedCount = 0;
    m_skippedCount = 0;
    m_errorCount = 0;
}

//=======================================================================================
// Hierarchy and Relationships
//=======================================================================================

void ExportContext::SetElementParent(const std::string& childId, const std::string& parentId) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_elementParents[childId] = parentId;
    m_elementChildren[parentId].push_back(childId);
}

bool ExportContext::GetElementParent(const std::string& childId, std::string& parentId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementParents.find(childId);
    if (it != m_elementParents.end()) {
        parentId = it->second;
        return true;
    }
    return false;
}

std::vector<std::string> ExportContext::GetElementChildren(const std::string& parentId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementChildren.find(parentId);
    if (it != m_elementChildren.end()) {
        return it->second;
    }
    return {};
}

void ExportContext::AddElementToGroup(const std::string& groupName, const std::string& elementId) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_elementGroups[groupName].push_back(elementId);
}

std::vector<std::string> ExportContext::GetGroupElements(const std::string& groupName) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementGroups.find(groupName);
    if (it != m_elementGroups.end()) {
        return it->second;
    }
    return {};
}

std::vector<std::string> ExportContext::GetAllGroups() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    std::vector<std::string> groups;
    for (const auto& pair : m_elementGroups) {
        groups.push_back(pair.first);
    }
    return groups;
}

//=======================================================================================
// Progress and Status
//=======================================================================================

void ExportContext::SetTotalElements(size_t total) {
    m_totalElements = total;
}

size_t ExportContext::GetTotalElements() const {
    return m_totalElements.load();
}

double ExportContext::GetProgressPercentage() const {
    size_t total = m_totalElements.load();
    size_t processed = m_processedCount.load();
    
    if (total == 0) {
        return 0.0;
    }
    
    return (static_cast<double>(processed) / total) * 100.0;
}

void ExportContext::SetCurrentOperation(const std::string& operation) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_currentOperation = operation;
}

std::string ExportContext::GetCurrentOperation() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_currentOperation;
}

void ExportContext::SetCancelled(bool cancelled) {
    m_cancelled = cancelled;
}

bool ExportContext::IsCancelled() const {
    return m_cancelled.load();
}

//=======================================================================================
// Error and Warning Management
//=======================================================================================

void ExportContext::AddError(const std::string& elementId, const std::string& error) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_errors.push_back(error);
    m_elementErrors[elementId].push_back(error);
    IncrementErrorCount();
}

void ExportContext::AddWarning(const std::string& elementId, const std::string& warning) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_warnings.push_back(warning);
    m_elementWarnings[elementId].push_back(warning);
}

std::vector<std::string> ExportContext::GetErrors() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_errors;
}

std::vector<std::string> ExportContext::GetWarnings() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_warnings;
}

std::vector<std::string> ExportContext::GetElementErrors(const std::string& elementId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementErrors.find(elementId);
    if (it != m_elementErrors.end()) {
        return it->second;
    }
    return {};
}

std::vector<std::string> ExportContext::GetElementWarnings(const std::string& elementId) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_elementWarnings.find(elementId);
    if (it != m_elementWarnings.end()) {
        return it->second;
    }
    return {};
}

void ExportContext::SetErrorHandling(ErrorHandling strategy) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_errorHandling = strategy;
}

ExportContext::ErrorHandling ExportContext::GetErrorHandling() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_errorHandling;
}

//=======================================================================================
// Caching and Performance
//=======================================================================================

void ExportContext::ClearCache() {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_cache.clear();
}

size_t ExportContext::GetCacheSize() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_cache.size();
}

void ExportContext::SetMemoryLimit(size_t limitBytes) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_memoryLimit = limitBytes;
}

size_t ExportContext::GetMemoryLimit() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_memoryLimit;
}

size_t ExportContext::GetMemoryUsage() const {
    // Simplified memory usage calculation
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_cache.size() * sizeof(void*) + 
           m_elementIdMap.size() * 64 + // Approximate string overhead
           m_processedElements.size() * 32;
}

void ExportContext::TrimMemory() {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    
    size_t currentUsage = GetMemoryUsage();
    if (currentUsage > m_memoryLimit) {
        // Clear least recently used cache entries
        // For simplicity, clear all cache
        m_cache.clear();
    }
}

//=======================================================================================
// Configuration and Settings
//=======================================================================================

void ExportContext::SetSetting(const std::string& key, const std::string& value) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_settings[key] = value;
}

std::string ExportContext::GetSetting(const std::string& key, const std::string& defaultValue) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_settings.find(key);
    if (it != m_settings.end()) {
        return it->second;
    }
    return defaultValue;
}

bool ExportContext::HasSetting(const std::string& key) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_settings.find(key) != m_settings.end();
}

void ExportContext::RemoveSetting(const std::string& key) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_settings.erase(key);
}

void ExportContext::SetGeometryTolerance(double tolerance) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_geometryTolerance = tolerance;
}

double ExportContext::GetGeometryTolerance() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_geometryTolerance;
}

void ExportContext::SetAngleTolerance(double tolerance) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_angleTolerance = tolerance;
}

double ExportContext::GetAngleTolerance() const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    return m_angleTolerance;
}

//=======================================================================================
// Threading Support
//=======================================================================================

void ExportContext::Lock() const {
    m_mutex.lock();
}

void ExportContext::Unlock() const {
    m_mutex.unlock();
}

bool ExportContext::TryLock() const {
    return m_mutex.try_lock();
}

void ExportContext::SetThreadLocalData(const std::string& key, std::shared_ptr<void> data) {
    m_threadLocalData[key] = data;
}

std::shared_ptr<void> ExportContext::GetThreadLocalData(const std::string& key) const {
    auto it = m_threadLocalData.find(key);
    if (it != m_threadLocalData.end()) {
        return it->second;
    }
    return nullptr;
}

} // namespace IModelExport
