#include "DWGDimensionProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <sstream>
#include <iomanip>

namespace IModelExport {

//=======================================================================================
// DimensionGeometry Implementation
//=======================================================================================

bool DimensionGeometry::IsValid() const {
    // Validate definition points
    if (!std::isfinite(defPoint1.x) || !std::isfinite(defPoint1.y) || !std::isfinite(defPoint1.z) ||
        !std::isfinite(defPoint2.x) || !std::isfinite(defPoint2.y) || !std::isfinite(defPoint2.z) ||
        !std::isfinite(textPosition.x) || !std::isfinite(textPosition.y) || !std::isfinite(textPosition.z) ||
        !std::isfinite(dimLinePoint.x) || !std::isfinite(dimLinePoint.y) || !std::isfinite(dimLinePoint.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(textRotation) || !std::isfinite(textHeight) || !std::isfinite(arrowSize) ||
        !std::isfinite(extLineOffset) || !std::isfinite(extLineExtend) || !std::isfinite(dimLineExtend) ||
        !std::isfinite(textGap) || !std::isfinite(measuredValue) || !std::isfinite(linearScale) ||
        !std::isfinite(roundValue)) {
        return false;
    }
    
    // Validate positive values
    if (textHeight <= 0.0 || arrowSize <= 0.0 || linearScale <= 0.0) {
        return false;
    }
    
    // Type-specific validation
    switch (type) {
        case Type::Angular:
            return std::isfinite(angular.angle) && 
                   std::isfinite(angular.vertex.x) && std::isfinite(angular.vertex.y) && std::isfinite(angular.vertex.z) &&
                   std::isfinite(angular.point1.x) && std::isfinite(angular.point1.y) && std::isfinite(angular.point1.z) &&
                   std::isfinite(angular.point2.x) && std::isfinite(angular.point2.y) && std::isfinite(angular.point2.z);
        case Type::Radial:
            return std::isfinite(radial.center.x) && std::isfinite(radial.center.y) && std::isfinite(radial.center.z) &&
                   std::isfinite(radial.radius) && radial.radius > 0.0;
        case Type::Diametric:
            return std::isfinite(diametric.center.x) && std::isfinite(diametric.center.y) && std::isfinite(diametric.center.z) &&
                   std::isfinite(diametric.diameter) && diametric.diameter > 0.0;
        default:
            return true;
    }
}

double DimensionGeometry::CalculateMeasuredValue() const {
    switch (type) {
        case Type::Linear:
        case Type::Aligned: {
            double dx = defPoint2.x - defPoint1.x;
            double dy = defPoint2.y - defPoint1.y;
            double dz = defPoint2.z - defPoint1.z;
            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }
        
        case Type::Angular: {
            // Calculate angle between two vectors
            Vector3d v1(angular.point1.x - angular.vertex.x, angular.point1.y - angular.vertex.y, angular.point1.z - angular.vertex.z);
            Vector3d v2(angular.point2.x - angular.vertex.x, angular.point2.y - angular.vertex.y, angular.point2.z - angular.vertex.z);
            
            double dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
            double len1 = std::sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
            double len2 = std::sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);
            
            if (len1 > 1e-10 && len2 > 1e-10) {
                double cosAngle = dot / (len1 * len2);
                cosAngle = std::max(-1.0, std::min(1.0, cosAngle)); // Clamp to valid range
                return std::acos(cosAngle);
            }
            return 0.0;
        }
        
        case Type::Radial:
            return radial.radius;
            
        case Type::Diametric:
            return diametric.diameter;
            
        default:
            return measuredValue;
    }
}

std::string DimensionGeometry::FormatDimensionText() const {
    if (!dimensionText.empty()) {
        return prefix + dimensionText + suffix;
    }
    
    double value = CalculateMeasuredValue() * linearScale;
    
    // Apply rounding if specified
    if (roundValue > 0.0) {
        value = std::round(value / roundValue) * roundValue;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision);
    
    switch (type) {
        case Type::Angular:
            // Convert radians to degrees for display
            oss << (value * 180.0 / M_PI) << "°";
            break;
            
        default:
            oss << value;
            if (showUnits && !units.empty()) {
                oss << units;
            }
            break;
    }
    
    return prefix + oss.str() + suffix;
}

//=======================================================================================
// LeaderGeometry Implementation
//=======================================================================================

bool LeaderGeometry::IsValid() const {
    if (vertices.size() < 2) {
        return false;
    }
    
    // Validate all vertices
    for (const auto& vertex : vertices) {
        if (!std::isfinite(vertex.x) || !std::isfinite(vertex.y) || !std::isfinite(vertex.z)) {
            return false;
        }
    }
    
    // Validate arrow and text points
    if (!std::isfinite(arrowPoint.x) || !std::isfinite(arrowPoint.y) || !std::isfinite(arrowPoint.z) ||
        !std::isfinite(textPoint.x) || !std::isfinite(textPoint.y) || !std::isfinite(textPoint.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(arrowSize) || !std::isfinite(textHeight) || !std::isfinite(textWidth) ||
        arrowSize <= 0.0 || textHeight <= 0.0) {
        return false;
    }
    
    return true;
}

double LeaderGeometry::CalculateLength() const {
    if (vertices.size() < 2) {
        return 0.0;
    }
    
    double totalLength = 0.0;
    for (size_t i = 1; i < vertices.size(); ++i) {
        double dx = vertices[i].x - vertices[i-1].x;
        double dy = vertices[i].y - vertices[i-1].y;
        double dz = vertices[i].z - vertices[i-1].z;
        totalLength += std::sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    return totalLength;
}

//=======================================================================================
// MLeaderGeometry Implementation
//=======================================================================================

bool MLeaderGeometry::IsValid() const {
    if (leaderLines.empty()) {
        return false;
    }
    
    // Validate leader lines
    for (const auto& line : leaderLines) {
        if (line.vertices.size() < 2) {
            return false;
        }
        
        for (const auto& vertex : line.vertices) {
            if (!std::isfinite(vertex.x) || !std::isfinite(vertex.y) || !std::isfinite(vertex.z)) {
                return false;
            }
        }
        
        if (!std::isfinite(line.arrowPoint.x) || !std::isfinite(line.arrowPoint.y) || !std::isfinite(line.arrowPoint.z)) {
            return false;
        }
    }
    
    // Validate content locations
    if (!std::isfinite(textLocation.x) || !std::isfinite(textLocation.y) || !std::isfinite(textLocation.z) ||
        !std::isfinite(blockLocation.x) || !std::isfinite(blockLocation.y) || !std::isfinite(blockLocation.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(textHeight) || !std::isfinite(blockScale) || !std::isfinite(blockRotation) ||
        !std::isfinite(landingGap) || !std::isfinite(doglegLength) || !std::isfinite(arrowSize) ||
        textHeight <= 0.0 || blockScale <= 0.0 || arrowSize <= 0.0) {
        return false;
    }
    
    return true;
}

//=======================================================================================
// ToleranceGeometry Implementation
//=======================================================================================

bool ToleranceGeometry::IsValid() const {
    // Validate position and direction
    if (!std::isfinite(position.x) || !std::isfinite(position.y) || !std::isfinite(position.z) ||
        !std::isfinite(direction.x) || !std::isfinite(direction.y) || !std::isfinite(direction.z) ||
        !std::isfinite(normal.x) || !std::isfinite(normal.y) || !std::isfinite(normal.z)) {
        return false;
    }
    
    // Validate numeric properties
    if (!std::isfinite(tolerance1) || !std::isfinite(tolerance2) || !std::isfinite(textHeight) ||
        textHeight <= 0.0) {
        return false;
    }
    
    // Validate that we have some tolerance content
    if (toleranceText.empty() && symbol.empty()) {
        return false;
    }
    
    return true;
}

std::string ToleranceGeometry::FormatToleranceText() const {
    if (!toleranceText.empty()) {
        return toleranceText;
    }
    
    std::ostringstream oss;
    
    // Add geometric tolerance symbol
    if (!symbol.empty()) {
        oss << symbol << " ";
    }
    
    // Add tolerance values
    if (tolerance1 != 0.0) {
        oss << "±" << tolerance1;
    }
    
    if (tolerance2 != 0.0) {
        oss << " ±" << tolerance2;
    }
    
    // Add material condition
    if (!materialCondition.empty()) {
        oss << " " << materialCondition;
    }
    
    // Add datum references
    if (!datum1.empty()) {
        oss << " | " << datum1;
    }
    if (!datum2.empty()) {
        oss << " | " << datum2;
    }
    if (!datum3.empty()) {
        oss << " | " << datum3;
    }
    
    return oss.str();
}

//=======================================================================================
// DWGDimensionProcessor Implementation
//=======================================================================================

DWGDimensionProcessor::DWGDimensionProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_dimensionTolerance(1e-6)
    , m_textTolerance(1e-3)
    , m_angleTolerance(1e-8)
    , m_enableAutoTextPlacement(true)
    , m_enableDimensionRepair(true)
    , m_enableTextFormatting(true)
    , m_enableAssociativity(false)
{
}

DWGProcessingStatus DWGDimensionProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract dimension geometry from element
        // For demonstration, we'll create a sample linear dimension
        
        if (element.type == ElementType::AnnotationElement) {
            DimensionGeometry dimension;
            dimension.type = DimensionGeometry::Type::Linear;
            dimension.defPoint1 = Point3d(0, 0, 0);
            dimension.defPoint2 = Point3d(100, 0, 0);
            dimension.dimLinePoint = Point3d(50, 20, 0);
            dimension.textPosition = Point3d(50, 20, 0);
            dimension.textHeight = 2.5;
            dimension.arrowSize = 2.5;
            
            return ProcessDimension(dimension, "Dimensions");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing dimension entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGDimensionProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::AnnotationElement; // Simplified check
}

DWGProcessingStatus DWGDimensionProcessor::ProcessDimension(const DimensionGeometry& geometry, const std::string& layer) {
    // Validate geometry
    auto validation = ValidateDimensionGeometry(geometry);
    if (!validation.isValid) {
        LogError("Dimension geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Process based on dimension type
    switch (geometry.type) {
        case DimensionGeometry::Type::Linear:
            return ProcessLinearDimension(geometry, layer);
        case DimensionGeometry::Type::Aligned:
            return ProcessAlignedDimension(geometry, layer);
        case DimensionGeometry::Type::Angular:
            return ProcessAngularDimension(geometry, layer);
        case DimensionGeometry::Type::Radial:
            return ProcessRadialDimension(geometry, layer);
        case DimensionGeometry::Type::Diametric:
            return ProcessDiametricDimension(geometry, layer);
        case DimensionGeometry::Type::Ordinate:
            return ProcessOrdinateDimension(geometry, layer);
        case DimensionGeometry::Type::Arc:
            return ProcessArcDimension(geometry, layer);
        default:
            LogError("Unsupported dimension type");
            return DWGProcessingStatus::UnsupportedEntity;
    }
}

DWGProcessingStatus DWGDimensionProcessor::ProcessLinearDimension(const DimensionGeometry& geometry, const std::string& layer) {
    // Transform geometry
    DimensionGeometry transformedGeometry = geometry;
    transformedGeometry.defPoint1 = TransformPoint(transformedGeometry.defPoint1);
    transformedGeometry.defPoint2 = TransformPoint(transformedGeometry.defPoint2);
    transformedGeometry.dimLinePoint = TransformPoint(transformedGeometry.dimLinePoint);
    transformedGeometry.textPosition = TransformPoint(transformedGeometry.textPosition);
    
    // Calculate measured value
    transformedGeometry.measuredValue = CalculateLinearDistance(transformedGeometry.defPoint1, transformedGeometry.defPoint2);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbAlignedDimension* dimension = CreateDWGAlignedDimension(transformedGeometry);
        if (!dimension) {
            LogError("Failed to create DWG linear dimension entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetDimensionProperties(dimension, transformedGeometry)) {
            delete dimension;
            LogError("Failed to set linear dimension properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(dimension, layer)) {
            delete dimension;
            LogError("Failed to set dimension entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(dimension)) {
            delete dimension;
            LogError("Failed to add linear dimension to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedDimensions++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG linear dimension: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - linear dimension creation skipped");
    m_processedDimensions++;
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGDimensionProcessor::ProcessLeader(const LeaderGeometry& geometry, const std::string& layer) {
    // Validate geometry
    auto validation = ValidateLeaderGeometry(geometry);
    if (!validation.isValid) {
        LogError("Leader geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    LeaderGeometry transformedGeometry = geometry;
    for (auto& vertex : transformedGeometry.vertices) {
        vertex = TransformPoint(vertex);
    }
    transformedGeometry.arrowPoint = TransformPoint(transformedGeometry.arrowPoint);
    transformedGeometry.textPoint = TransformPoint(transformedGeometry.textPoint);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbLeader* leader = CreateDWGLeader(transformedGeometry);
        if (!leader) {
            LogError("Failed to create DWG leader entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetLeaderProperties(leader, transformedGeometry)) {
            delete leader;
            LogError("Failed to set leader properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(leader, layer)) {
            delete leader;
            LogError("Failed to set leader entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(leader)) {
            delete leader;
            LogError("Failed to add leader to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedLeaders++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG leader: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - leader creation skipped");
    m_processedLeaders++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

DimensionValidationResult DWGDimensionProcessor::ValidateDimensionGeometry(const DimensionGeometry& geometry) const {
    DimensionValidationResult result;
    result.isValid = true;
    
    // Validate points
    result.hasValidPoints = ValidateDimensionPoints(geometry);
    if (!result.hasValidPoints) {
        result.AddDimensionError("Invalid dimension points");
    }
    
    // Validate text
    result.hasValidText = ValidateDimensionText(geometry.dimensionText);
    if (!result.hasValidText) {
        result.AddDimensionWarning("Invalid dimension text");
    }
    
    // Validate style
    result.hasValidStyle = ValidateDimensionStyle(geometry.dimStyleName);
    if (!result.hasValidStyle) {
        result.AddDimensionWarning("Invalid dimension style: " + geometry.dimStyleName);
    }
    
    // Calculate measured value
    result.measuredValue = geometry.CalculateMeasuredValue();
    result.hasValidMeasurement = result.measuredValue > m_dimensionTolerance;
    if (!result.hasValidMeasurement) {
        result.AddDimensionError("Invalid measured value: " + std::to_string(result.measuredValue));
    }
    
    return result;
}

LeaderValidationResult DWGDimensionProcessor::ValidateLeaderGeometry(const LeaderGeometry& geometry) const {
    LeaderValidationResult result;
    result.isValid = true;
    
    // Validate vertices
    result.hasValidVertices = ValidateLeaderVertices(geometry.vertices);
    if (!result.hasValidVertices) {
        result.AddLeaderError("Invalid leader vertices");
    }
    
    // Validate text
    result.hasValidText = !geometry.text.empty();
    if (!result.hasValidText) {
        result.AddLeaderWarning("Empty leader text");
    }
    
    // Validate arrow
    result.hasValidArrow = ValidateArrowBlock(geometry.arrowName);
    if (!result.hasValidArrow) {
        result.AddLeaderWarning("Invalid arrow block: " + geometry.arrowName);
    }
    
    // Calculate length
    result.leaderLength = geometry.CalculateLength();
    if (result.leaderLength < m_dimensionTolerance) {
        result.AddLeaderError("Leader length too small: " + std::to_string(result.leaderLength));
        result.isValid = false;
    }
    
    return result;
}

bool DWGDimensionProcessor::ValidateDimensionPoints(const DimensionGeometry& geometry) const {
    // Check that definition points are not coincident
    double distance = CalculateLinearDistance(geometry.defPoint1, geometry.defPoint2);
    if (distance < m_dimensionTolerance) {
        return false;
    }
    
    // Check that text position is valid
    if (!std::isfinite(geometry.textPosition.x) || !std::isfinite(geometry.textPosition.y) || !std::isfinite(geometry.textPosition.z)) {
        return false;
    }
    
    return true;
}

bool DWGDimensionProcessor::ValidateLeaderVertices(const std::vector<Point3d>& vertices) const {
    if (vertices.size() < 2) {
        return false;
    }
    
    // Check that consecutive vertices are not coincident
    for (size_t i = 1; i < vertices.size(); ++i) {
        double distance = CalculateLinearDistance(vertices[i-1], vertices[i]);
        if (distance < m_dimensionTolerance) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// Calculation Methods
//=======================================================================================

double DWGDimensionProcessor::CalculateLinearDistance(const Point3d& p1, const Point3d& p2) const {
    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;
    double dz = p2.z - p1.z;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

double DWGDimensionProcessor::CalculateAngularMeasurement(const Point3d& vertex, const Point3d& p1, const Point3d& p2) const {
    Vector3d v1(p1.x - vertex.x, p1.y - vertex.y, p1.z - vertex.z);
    Vector3d v2(p2.x - vertex.x, p2.y - vertex.y, p2.z - vertex.z);
    
    double dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
    double len1 = std::sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
    double len2 = std::sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);
    
    if (len1 > 1e-10 && len2 > 1e-10) {
        double cosAngle = dot / (len1 * len2);
        cosAngle = std::max(-1.0, std::min(1.0, cosAngle)); // Clamp to valid range
        return std::acos(cosAngle);
    }
    
    return 0.0;
}

std::string DWGDimensionProcessor::FormatDimensionValue(double value, int precision, const std::string& units) const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    
    if (!units.empty()) {
        oss << units;
    }
    
    return oss.str();
}

//=======================================================================================
// Helper Methods
//=======================================================================================

bool DWGDimensionProcessor::ValidateDimensionText(const std::string& text) const {
    // Allow empty text (will use measured value)
    return true;
}

bool DWGDimensionProcessor::ValidateDimensionStyle(const std::string& styleName) const {
    // For now, just check that it's not empty
    return !styleName.empty();
}

bool DWGDimensionProcessor::ValidateArrowBlock(const std::string& arrowName) const {
    // Check for standard arrow names
    static const std::vector<std::string> standardArrows = {
        "_ClosedFilled", "_Closed", "_Dot", "_ArchTick", "_Oblique", "_Open", "_Origin", "_Origin2", "_Open90", "_Open30"
    };
    
    return std::find(standardArrows.begin(), standardArrows.end(), arrowName) != standardArrows.end();
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbAlignedDimension* DWGDimensionProcessor::CreateDWGAlignedDimension(const DimensionGeometry& geometry) const {
    try {
        AcGePoint3d pt1(geometry.defPoint1.x, geometry.defPoint1.y, geometry.defPoint1.z);
        AcGePoint3d pt2(geometry.defPoint2.x, geometry.defPoint2.y, geometry.defPoint2.z);
        AcGePoint3d dimLine(geometry.dimLinePoint.x, geometry.dimLinePoint.y, geometry.dimLinePoint.z);
        
        AcDbAlignedDimension* dimension = new AcDbAlignedDimension(pt1, pt2, dimLine);
        
        return dimension;
    }
    catch (...) {
        return nullptr;
    }
}

AcDbLeader* DWGDimensionProcessor::CreateDWGLeader(const LeaderGeometry& geometry) const {
    try {
        AcDbLeader* leader = new AcDbLeader();
        
        // Add vertices
        for (const auto& vertex : geometry.vertices) {
            AcGePoint3d pt(vertex.x, vertex.y, vertex.z);
            leader->appendVertex(pt);
        }
        
        // Set properties
        leader->setHasArrowHead(geometry.hasArrowHead);
        leader->setHasHookLine(geometry.hasHookLine);
        
        return leader;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGDimensionProcessor::SetDimensionProperties(AcDbDimension* dimension, const DimensionGeometry& geometry) const {
    if (!dimension) {
        return false;
    }
    
    try {
        // Set dimension text
        if (!geometry.dimensionText.empty()) {
            dimension->setDimensionText(geometry.dimensionText.c_str());
        }
        
        // Set text height
        dimension->setDimtxt(geometry.textHeight);
        
        // Set arrow size
        dimension->setDimasz(geometry.arrowSize);
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGDimensionProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGDimensionProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
