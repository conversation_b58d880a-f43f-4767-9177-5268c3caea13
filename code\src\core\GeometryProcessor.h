#pragma once

#include "../../include/ExportTypes.h"
#include <vector>
#include <memory>
#include <functional>

namespace IModelExport {

// Forward declarations
class ExportContext;

//=======================================================================================
// Geometry Data Structures
//=======================================================================================

struct GeometryData {
    enum class Type {
        Point,
        Line,
        Arc,
        Circle,
        Ellipse,
        Polyline,
        Spline,
        Surface,
        Solid,
        Mesh,
        Text,
        Dimension,
        Hatch,
        Block,
        Unknown
    };

    Type type = Type::Unknown;
    std::vector<Point3d> points;
    std::vector<Vector3d> normals;
    std::vector<Point3d> textureCoords;
    std::vector<int> indices;
    std::vector<int> faceVertexCounts;
    BoundingBox boundingBox;
    Transform3d transform;
    std::string materialId;
    std::unordered_map<std::string, std::string> properties;
};

struct CurveData {
    enum class CurveType {
        Line,
        Arc,
        Circle,
        Ellipse,
        Spline,
        Polyline,
        CompositeCurve
    };

    CurveType type = CurveType::Line;
    std::vector<Point3d> controlPoints;
    std::vector<double> knots;
    std::vector<double> weights;
    int degree = 1;
    bool closed = false;
    bool rational = false;
    double startParameter = 0.0;
    double endParameter = 1.0;
};

struct SurfaceData {
    enum class SurfaceType {
        Plane,
        Cylinder,
        Cone,
        Sphere,
        Torus,
        BSpline,
        NURBS,
        Mesh
    };

    SurfaceType type = SurfaceType::Plane;
    std::vector<std::vector<Point3d>> controlGrid;
    std::vector<double> uKnots;
    std::vector<double> vKnots;
    std::vector<std::vector<double>> weights;
    int uDegree = 1;
    int vDegree = 1;
    bool uClosed = false;
    bool vClosed = false;
    bool rational = false;
};

struct SolidData {
    enum class SolidType {
        Box,
        Cylinder,
        Cone,
        Sphere,
        Torus,
        Extrusion,
        Revolution,
        Sweep,
        Boolean,
        BRep
    };

    SolidType type = SolidType::Box;
    std::vector<SurfaceData> faces;
    std::vector<CurveData> edges;
    std::vector<Point3d> vertices;
    std::vector<std::vector<int>> faceLoops;
    std::vector<std::vector<int>> edgeLoops;
    bool isClosed = true;
};

//=======================================================================================
// Geometry Processor - Handles geometry analysis, conversion and optimization
//=======================================================================================

class GeometryProcessor {
public:
    GeometryProcessor();
    ~GeometryProcessor();

    //===================================================================================
    // Geometry Analysis
    //===================================================================================

    // Analyze geometry and determine type
    GeometryData::Type AnalyzeGeometry(const std::vector<Point3d>& points, 
                                      const std::vector<int>& indices = {}) const;
    
    // Calculate geometry properties
    struct GeometryProperties {
        double length = 0.0;
        double area = 0.0;
        double volume = 0.0;
        Point3d centroid;
        BoundingBox boundingBox;
        bool isClosed = false;
        bool isPlanar = false;
        bool isConvex = false;
    };
    
    GeometryProperties AnalyzeGeometryProperties(const GeometryData& geometry) const;
    
    // Check geometry validity
    bool ValidateGeometry(const GeometryData& geometry, std::vector<std::string>& errors) const;
    
    // Detect geometry degeneracies
    std::vector<std::string> DetectDegeneracies(const GeometryData& geometry) const;

    //===================================================================================
    // Geometry Conversion
    //===================================================================================

    // Convert between geometry types
    bool ConvertGeometry(const GeometryData& source, GeometryData::Type targetType, 
                        GeometryData& result) const;
    
    // Tessellate curves to polylines
    std::vector<Point3d> TessellateCurve(const CurveData& curve, double tolerance) const;
    
    // Tessellate surfaces to meshes
    GeometryData TessellateSurface(const SurfaceData& surface, double tolerance) const;
    
    // Convert solids to meshes
    GeometryData ConvertSolidToMesh(const SolidData& solid, double tolerance) const;
    
    // Simplify geometry
    GeometryData SimplifyGeometry(const GeometryData& geometry, double tolerance) const;

    //===================================================================================
    // Curve Processing
    //===================================================================================

    // Fit curve to points
    CurveData FitCurveToPoints(const std::vector<Point3d>& points, int degree = 3) const;
    
    // Split curve at parameter
    std::pair<CurveData, CurveData> SplitCurve(const CurveData& curve, double parameter) const;
    
    // Join curves
    CurveData JoinCurves(const std::vector<CurveData>& curves, double tolerance) const;
    
    // Offset curve
    CurveData OffsetCurve(const CurveData& curve, double distance, const Vector3d& direction) const;
    
    // Calculate curve length
    double CalculateCurveLength(const CurveData& curve) const;
    
    // Evaluate curve at parameter
    Point3d EvaluateCurve(const CurveData& curve, double parameter) const;
    
    // Find curve parameter for point
    double FindCurveParameter(const CurveData& curve, const Point3d& point, double tolerance) const;

    //===================================================================================
    // Surface Processing
    //===================================================================================

    // Fit surface to points
    SurfaceData FitSurfaceToPoints(const std::vector<std::vector<Point3d>>& points, 
                                  int uDegree = 3, int vDegree = 3) const;
    
    // Trim surface with curves
    SurfaceData TrimSurface(const SurfaceData& surface, const std::vector<CurveData>& boundaries) const;
    
    // Offset surface
    SurfaceData OffsetSurface(const SurfaceData& surface, double distance) const;
    
    // Calculate surface area
    double CalculateSurfaceArea(const SurfaceData& surface) const;
    
    // Evaluate surface at parameters
    Point3d EvaluateSurface(const SurfaceData& surface, double u, double v) const;
    
    // Calculate surface normal
    Vector3d CalculateSurfaceNormal(const SurfaceData& surface, double u, double v) const;

    //===================================================================================
    // Solid Processing
    //===================================================================================

    // Boolean operations
    enum class BooleanOperation {
        Union,
        Intersection,
        Difference,
        SymmetricDifference
    };
    
    SolidData PerformBooleanOperation(const SolidData& solid1, const SolidData& solid2, 
                                     BooleanOperation operation) const;
    
    // Extrude profile to create solid
    SolidData ExtrudeProfile(const std::vector<CurveData>& profile, const Vector3d& direction, 
                            double distance) const;
    
    // Revolve profile to create solid
    SolidData RevolveProfile(const std::vector<CurveData>& profile, const Point3d& axisPoint, 
                            const Vector3d& axisDirection, double angle) const;
    
    // Calculate solid volume
    double CalculateSolidVolume(const SolidData& solid) const;
    
    // Check if point is inside solid
    bool IsPointInsideSolid(const SolidData& solid, const Point3d& point) const;

    //===================================================================================
    // Mesh Processing
    //===================================================================================

    // Generate mesh from geometry
    GeometryData GenerateMesh(const GeometryData& geometry, double tolerance) const;
    
    // Optimize mesh (reduce vertices, improve quality)
    GeometryData OptimizeMesh(const GeometryData& mesh) const;
    
    // Smooth mesh
    GeometryData SmoothMesh(const GeometryData& mesh, int iterations = 1) const;
    
    // Decimate mesh (reduce triangle count)
    GeometryData DecimateMesh(const GeometryData& mesh, double reductionFactor) const;
    
    // Calculate mesh normals
    std::vector<Vector3d> CalculateMeshNormals(const GeometryData& mesh) const;
    
    // Validate mesh topology
    bool ValidateMeshTopology(const GeometryData& mesh, std::vector<std::string>& errors) const;

    //===================================================================================
    // Geometry Transformation
    //===================================================================================

    // Transform geometry
    GeometryData TransformGeometry(const GeometryData& geometry, const Transform3d& transform) const;
    
    // Scale geometry
    GeometryData ScaleGeometry(const GeometryData& geometry, double scale) const;
    
    // Rotate geometry
    GeometryData RotateGeometry(const GeometryData& geometry, const Vector3d& axis, double angle) const;
    
    // Translate geometry
    GeometryData TranslateGeometry(const GeometryData& geometry, const Vector3d& translation) const;
    
    // Mirror geometry
    GeometryData MirrorGeometry(const GeometryData& geometry, const Point3d& planePoint, 
                               const Vector3d& planeNormal) const;

    //===================================================================================
    // Geometry Optimization
    //===================================================================================

    // Remove duplicate points
    std::vector<Point3d> RemoveDuplicatePoints(const std::vector<Point3d>& points, 
                                              double tolerance) const;
    
    // Merge colinear segments
    std::vector<Point3d> MergeColinearSegments(const std::vector<Point3d>& points, 
                                              double angleTolerance) const;
    
    // Optimize curve representation
    CurveData OptimizeCurve(const CurveData& curve) const;
    
    // Optimize surface representation
    SurfaceData OptimizeSurface(const SurfaceData& surface) const;
    
    // Reduce geometry complexity
    GeometryData ReduceComplexity(const GeometryData& geometry, double tolerance) const;

    //===================================================================================
    // Format-Specific Processing
    //===================================================================================

    // Prepare geometry for DWG export
    GeometryData PrepareForDWG(const GeometryData& geometry, std::shared_ptr<ExportContext> context) const;
    
    // Prepare geometry for IFC export
    GeometryData PrepareForIFC(const GeometryData& geometry, std::shared_ptr<ExportContext> context) const;
    
    // Prepare geometry for DGN export
    GeometryData PrepareForDGN(const GeometryData& geometry, std::shared_ptr<ExportContext> context) const;
    
    // Prepare geometry for USD export
    GeometryData PrepareForUSD(const GeometryData& geometry, std::shared_ptr<ExportContext> context) const;

    //===================================================================================
    // Utility Functions
    //===================================================================================

    // Calculate distance between geometries
    double CalculateDistance(const GeometryData& geom1, const GeometryData& geom2) const;
    
    // Check geometry intersection
    bool CheckIntersection(const GeometryData& geom1, const GeometryData& geom2) const;
    
    // Find closest point on geometry
    Point3d FindClosestPoint(const GeometryData& geometry, const Point3d& point) const;
    
    // Project point onto geometry
    Point3d ProjectPointOntoGeometry(const GeometryData& geometry, const Point3d& point) const;

private:
    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Curve fitting algorithms
    CurveData FitBSplineCurve(const std::vector<Point3d>& points, int degree) const;
    CurveData FitNURBSCurve(const std::vector<Point3d>& points, int degree) const;
    
    // Surface fitting algorithms
    SurfaceData FitBSplineSurface(const std::vector<std::vector<Point3d>>& points, 
                                 int uDegree, int vDegree) const;
    
    // Tessellation algorithms
    std::vector<Point3d> TessellateArc(const Point3d& center, double radius, 
                                      double startAngle, double endAngle, double tolerance) const;
    std::vector<Point3d> TessellateSpline(const CurveData& spline, double tolerance) const;
    
    // Mesh generation algorithms
    GeometryData GenerateTriangleMesh(const std::vector<Point3d>& points, 
                                     const std::vector<int>& indices) const;
    GeometryData GenerateQuadMesh(const std::vector<std::vector<Point3d>>& grid) const;
    
    // Optimization algorithms
    std::vector<Point3d> DouglasPeuckerSimplify(const std::vector<Point3d>& points, 
                                               double tolerance) const;
    GeometryData QuadricErrorMetricDecimation(const GeometryData& mesh, 
                                             double reductionFactor) const;
    
    // Validation helpers
    bool ValidatePointArray(const std::vector<Point3d>& points, std::vector<std::string>& errors) const;
    bool ValidateIndexArray(const std::vector<int>& indices, size_t pointCount, 
                           std::vector<std::string>& errors) const;
    bool ValidateCurveData(const CurveData& curve, std::vector<std::string>& errors) const;
    bool ValidateSurfaceData(const SurfaceData& surface, std::vector<std::string>& errors) const;
};

//=======================================================================================
// Geometry Utility Functions
//=======================================================================================

namespace GeometryUtils {
    
    // Create basic geometries
    GeometryData CreateLine(const Point3d& start, const Point3d& end);
    GeometryData CreateCircle(const Point3d& center, double radius, const Vector3d& normal = Vector3d(0,0,1));
    GeometryData CreateRectangle(const Point3d& corner1, const Point3d& corner2);
    GeometryData CreateBox(const Point3d& min, const Point3d& max);
    GeometryData CreateSphere(const Point3d& center, double radius);
    GeometryData CreateCylinder(const Point3d& base, const Vector3d& axis, double radius, double height);
    
    // Geometry queries
    bool IsPointOnLine(const Point3d& point, const Point3d& lineStart, const Point3d& lineEnd, double tolerance = 1e-6);
    bool IsPointInTriangle(const Point3d& point, const Point3d& a, const Point3d& b, const Point3d& c);
    bool IsPointInPolygon(const Point3d& point, const std::vector<Point3d>& polygon);
    
    // Geometric calculations
    double CalculateTriangleArea(const Point3d& a, const Point3d& b, const Point3d& c);
    double CalculatePolygonArea(const std::vector<Point3d>& polygon);
    Point3d CalculateTriangleCentroid(const Point3d& a, const Point3d& b, const Point3d& c);
    Point3d CalculatePolygonCentroid(const std::vector<Point3d>& polygon);
    
    // Intersection tests
    bool LineLineIntersection(const Point3d& line1Start, const Point3d& line1End,
                             const Point3d& line2Start, const Point3d& line2End,
                             Point3d& intersection);
    bool LinePlaneIntersection(const Point3d& lineStart, const Point3d& lineEnd,
                              const Point3d& planePoint, const Vector3d& planeNormal,
                              Point3d& intersection);
    bool RayTriangleIntersection(const Point3d& rayOrigin, const Vector3d& rayDirection,
                                const Point3d& a, const Point3d& b, const Point3d& c,
                                Point3d& intersection);
}

} // namespace IModelExport
