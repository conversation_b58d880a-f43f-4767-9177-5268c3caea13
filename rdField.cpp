/*----------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdField.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

static WCharP s_fieldFormatString = L"%%<\\_FldIdx %d>%%";
static WCharP s_objectIdFieldCode = L"Object(%<\\_ObjId ";

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendDependentToSpecificationString
(
AcString&       specificationString,
ElementId       elementId
)
    {
    WChar       tag[256], idString[256];

    // for now, just add one element id:
    swprintf (tag, L"Element%d", 0);
    swprintf (idString, L"%I64x", elementId);

    AcString    id(idString);
    AcString    dependentsString = RealDwgUtil::FormatXMLTag (tag, RealDwgUtil::FormatXMLTag (L"ID", id));

    specificationString += RealDwgUtil::FormatXMLTag (L"Dependents", dependentsString);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId         GetTargetObjectIdFromFieldCode
(
AcDbField*          pField
)
    {
    AcDbObjectId    objectId = AcDbObjectId::kNull;
    const ACHAR*    evalId = pField->evaluatorId ();
    if (NULL == evalId || 0 != _wcsicmp(evalId, L"AcObjProp"))
        return  objectId;
    
    /*-----------------------------------------------------------------------------------
    Try getting the object ID from field code which is a text expression string for an
    object field, for example "Object(%<\_ObjId -3137832>%).Length" without the options.  
    The value stored in the text string is actually an integer of the object's address 
    pointed to by the object ID.
    -----------------------------------------------------------------------------------*/
    AcString        fieldCode = pField->getFieldCode (AcDbField::FieldCodeFlag(AcDbField::kChildObjectReference | AcDbField::kStripOptions));
    if (0 == fieldCode.find(s_objectIdFieldCode))
        {
        // extract the integer number from the string
        AcString    idValue = fieldCode.substr ((int)wcslen(s_objectIdFieldCode), -1);
        if (!idValue.isEmpty())
            {
            idValue = idValue.substr (0, idValue.find(L">%"));

            // convert it to object ID:
            __int64 idStub = idValue.asDeci64 ();
            objectId = AcDbObjectId ((AcDbStub*)idStub);
            }
        }

    return  objectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcString            ConvertToDgnContext::FormatFieldSpecificationXML (AcString& fieldString, AcDbField* pChild)
    {
    // The specification string consists of the XML tags for access, format and value.
    AcString        evaluatorString (pChild->evaluatorId());
    AcString        formatString (pChild->getFormat());
    AcString        accessString;

    AcValue         data;
    const ACHAR*    propertyName = NULL;
    if (Acad::eOk == pChild->getData(L"ObjectPropertyName", &data) && data.get(propertyName))
        {
        accessString = AcString (propertyName);
        }
    else
        {
        AcString    tempString = pChild->getFieldCode (AcDbField::FieldCodeFlag(AcDbField::kStripOptions));

        int         dotIndex    = tempString.find (L'.');
        if (dotIndex >= 0)
            accessString = tempString.substr (dotIndex + 1, tempString.length() - dotIndex + 1);
        else
            accessString = tempString;
        }

    AcString        expressionString = RealDwgUtil::FormatXMLTag (L"AC_Evaluator", evaluatorString) + RealDwgUtil::FormatXMLTag (L"AC_Access", accessString);

    if (formatString.length() > 0)
        expressionString = expressionString + RealDwgUtil::FormatXMLTag (L"AC_Format", formatString);

    AcString        specificationString = RealDwgUtil::FormatXMLTag (L"Evaluator", AcString (L"DwgProxy")) + RealDwgUtil::FormatXMLTag (L"Expression", expressionString);

    AcFdFieldValue  value;
    pChild->getValue (value);
    if (value.isValid())
        specificationString = specificationString + RealDwgUtil::FormatXMLTag (L"Value", RealDwgUtil::XMLFromFieldValue(value));

    /*-----------------------------------------------------------------------------------
    NEEDS VERIFICATION: is it ever possible for a child field to have more than one
    target objects (i.e. DXF group codes 97 and 331's)?

    RealDWG does not have a way to access the object ID list.  Below method appears to be
    the only way to access one target object ID.  Maybe one child field can really only
    have one target.  That seems to make sense.  I have tried and failed to create
    multiple targets in a child field in AutoCAD 2009.
    -----------------------------------------------------------------------------------*/
    if (Acad::eOk == pChild->getData(L"ObjectPropertyId", &data))
        {
        AcDbObjectId    objectId;
        if (data.get (objectId))
            AppendDependentToSpecificationString (specificationString, this->ElementIdFromObjectId(objectId));
        }
    else
        {
        AcDbObjectId    objectId = GetTargetObjectIdFromFieldCode (pChild);
        if (objectId.isValid())
            AppendDependentToSpecificationString (specificationString, this->ElementIdFromObjectId(objectId));
        }

    return specificationString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ExtractEvaluatedFieldFromText
(
AcString&                   evaluatedField,     // <=
int                         iField,
AcString&                   stringWithFieldCode,
AcString&                   originalText
)
    {
    int         fieldIndex;
    AcString    codeString;

    if (iField > 0)
        return BSIERROR;           // We currently don't try to extract evaluated fields from text with more than one field.
                                // This is fragile enough trying to extract one - and hopefully it is not needed often.

    codeString.format (s_fieldFormatString, iField);

    if (0 <= (fieldIndex = stringWithFieldCode.find (codeString)))
        {
        AcString        prefix  = stringWithFieldCode.substr (0, fieldIndex);

        if ( (0 == fieldIndex) || (0 == originalText.find (prefix.kwszPtr())) )
            {
            int     prefixAndFieldLength = fieldIndex + codeString.length();
            int     suffixLength         = stringWithFieldCode.length() - prefixAndFieldLength;

            if (suffixLength > 0)
                {
                AcString    suffix = stringWithFieldCode.substr (prefixAndFieldLength, suffixLength);

                int originalTextSuffixIndex;
                AcString    tempString = originalText.substr (fieldIndex);
                if (0 <= (originalTextSuffixIndex = tempString.find (suffix.kwszPtr())) )
                    {
                    evaluatedField = tempString.substr (0, originalTextSuffixIndex);
                    return SUCCESS;
                    }
                }
            else
                {
                evaluatedField = originalText.substr (0, fieldIndex);
                return SUCCESS;
                }
            }
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetFieldsString
(
AcString&                   textString,
AcDbField*                  pField,
AcString&                   originalString
)
    {
    AcDbField::FieldCodeFlag    nFlags = AcDbField::FieldCodeFlag (AcDbField::kFieldCode | AcDbField::kObjectReference);
    textString.assign (pField->getFieldCode (nFlags));

    for (int iField = 0; iField < pField->childCount(); iField++)
        {
        AcDbField*          pChild;

        if (Acad::eOk != pField->getChild (iField, pChild, AcDb::kForRead))
            continue;

        nFlags = AcDbField::FieldCodeFlag (AcDbField::kFieldCode | AcDbField::kAddMarkers);
        AcString            fieldString         = pChild->getFieldCode (nFlags);
        nFlags = AcDbField::kEvaluatedText;
        AcString            evaluatedString     = pChild->getFieldCode (nFlags);

        // The evaluated string should contain the field value in most cases - but we have found cases where they are empty
        // (TR#  157442
        if (evaluatedString.isEmpty() &&
            SUCCESS != ExtractEvaluatedFieldFromText (evaluatedString, iField, textString, originalString))
            {
            textString = originalString;
            break;
            }

        AcString            specificationString;
        specificationString = FormatFieldSpecificationXML (fieldString, pChild);

        AcString            replacementString = L"%" + RealDwgUtil::FormatXMLTag (L"Field", RealDwgUtil::FormatXMLTag (L"Specification", specificationString)  +
                                                                               RealDwgUtil::FormatXMLTag (L"Result", evaluatedString)) + L"%";

        // replace the ACAD field code in mtext with our field code:
        int                 fieldStart = textString.find (L"%<\\_FldId");
        if (fieldStart >= 0 || (fieldStart = textString.find(L"%<\\_FldPtr")) >= 0)
            {
            AcString        startString = textString.substr (0, fieldStart);
            AcString        endString = textString.substr (fieldStart, textString.length());
            int             fieldEnd = endString.find (L">%");
            if (fieldEnd >= 0 && (unsigned)(fieldEnd+2) < endString.length())
                endString = endString.substr (fieldEnd+2, endString.length());
            else
                endString.assign (L"");

            textString = startString + replacementString + endString;
            }

        pChild->close ();
        }
    }



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Ray.Bentley     02/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ParseXMLTag
(
AcString&                   field,
WCharCP*                  ppNext,
WCharCP                   pInput,
WCharCP                   pTag
)
    {
    WChar         prefix[1024], suffix[1024];
    WCharCP       pStart;
    WCharCP       pEnd;

    swprintf (prefix, L"<%ls>", pTag);
    swprintf (suffix, L"</%ls>", pTag);

    if (NULL != (pStart = wcsstr (pInput, prefix)) &&
        NULL != (pEnd = wcsstr (pStart, suffix)))
        {
        pStart += wcslen (prefix);
        int         length = pEnd - pStart;

        // isolate the part of the string we want.
        WChar *tmp = (WChar*)_alloca ((length+1) * sizeof WChar);
        RealDwgUtil::TerminatedStringCopy (tmp, pStart, length+1);

        field.assign (tmp);

        if (NULL != ppNext)
            *ppNext = pEnd + wcslen (suffix);

        return SUCCESS;
        }
    return BSIERROR;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Ray.Bentley     02/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ParseXMLStringTag
(
AcString&                   output,
AcString&                   input,
WCharCP                   pTag
)
    {
    AcString        tmpString;
    StatusInt       status;

    if (SUCCESS == (status = ParseXMLTag (tmpString, NULL, input, pTag)))
        status = ParseXMLTag (output, NULL, tmpString, L"String");

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/05
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ParseUnknownXMLTag
(
AcString&                   tag,
AcString&                   value,
AcString&                   input
)
    {
    int                     tagStart, tagEnd;

    if ((tagStart = input.find ('<')) >= 0 &&
        (tagEnd   = input.find ('>')) >= 0)
        {
        tagStart++;

        tag = input.substr (tagStart, tagEnd - tagStart);

        return ParseXMLTag (value, NULL, input, tag);
        }
    return BSIERROR;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExtractFieldValueFromXML
(
AcFdFieldValue&             fieldValue,
AcString&                   input,
AcString&                   accessString,          // Use this to identify type of value (for now).
ConvertFromDgnContext&      context
)
    {
    AcString            tag, value;
    if (SUCCESS == ParseUnknownXMLTag (tag, value, input))
        {
        if (tag == AcString (L"Double"))
            {
            double      dValue;

            if (1 == swscanf (value, L"%lf", &dValue))
                {
                if (accessString == AcString (L"Area"))
                    {
                    dValue *= context.GetScaleFromDGN() * context.GetScaleFromDGN();
                    }
                else if (accessString == AcString (L"Length") || accessString == AcString (L"Circumference"))
                    {
                    dValue *= context.GetScaleFromDGN();
                    }

                fieldValue = AcFdFieldValue (dValue);
                return SUCCESS;
                }
            }
        else if (tag == AcString (L"DateTime"))
            {
            __time64_t  dateValue;

            if (1 == swscanf (value, L"%I64d", &dateValue))
                {
#pragma warning(disable:4244)
                dateValue = (__time64_t) osTime_cvtNTFileTimeToMillis (dateValue)/1000.0;
#pragma warning(default:4244)

#ifdef REALDWG_DIAGNOTICS
                char    timeString[64];
                errno_t errorCode = _ctime64_s (timeString, sizeof(timeString), &dateValue);
                if (0 != errorCode)
                    DIAGNOSTIC_PRINTF ("Invalid field time value <%I64d>.  Error code: %d\n", dateValue, errorCode);
#endif

                fieldValue = AcFdFieldValue (dateValue);
                return SUCCESS;
                }
            }
        else if (tag == AcString (L"Int64"))                         // Used for file size - but no analogue in AutoCAD.
            {
            long        intValue;

            if (1 == swscanf (value, L"%d", &intValue))
                fieldValue = AcFdFieldValue (intValue);
            }
        else
            {
            fieldValue = AcFdFieldValue (value);
            }

        }

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2005
+---------------+---------------+---------------+---------------+---------------+------*/
AcFdFieldValue              GetOrAddFieldData
(
AcDbField*                  pField,
WCharCP                   pKeyChars
)
    {
    AcFdFieldValue          value;
    AcString                key (pKeyChars);

    if (!pField->getData(key, &value))
        pField->setData (key, &value);

    return value;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  ExtractObjectIdsFromXML
(
AcDbField*                  pField,
AcString&                   specification,
ConvertFromDgnContext&      context
)
    {
    WChar const*          pCurr = specification;
    for (int i=0; true; i++)
        {
        WChar             tag[256];
        ElementId           elementId = 0;
        AcDbObjectId        objectId;
        AcString            idValueString, idString;

        swprintf (tag, L"Element%d", i);

        if (SUCCESS == ParseXMLTag (idValueString, &pCurr, pCurr, tag) &&
            SUCCESS == ParseXMLTag (idString, NULL, idValueString, L"ID"))
            {
            if (1 == swscanf (idString, L"%I64d", &elementId) &&
                ! (objectId = context.ExistingObjectIdFromElementId (elementId)).isNull())
                {
                AcValue     data;
                data.set (objectId);
                if (Acad::eOk != pField->setData (L"ObjectPropertyId", &data))
                    {
                    DIAGNOSTIC_PRINTF ("Cannot set ObjectPropertyId %I64x for field.\n", elementId);
                    }

                // with RealDWG we can not set multiple 331's!
                return  i+1;
                }

            /*---------------------------------------------------------------------------
            We are in post-process so we should have the target object in database. If we
            don't have one, we have ignored it and therefore we should remove the field
            from the mtext; otherwise it will become an invalid field.
            ---------------------------------------------------------------------------*/
            if (elementId > 0 && objectId.isNull())
                return  -1;
            }
        else
            break;
        }

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             ObjectIdStringFromXML
(
const AcString&             specification,
ConvertFromDgnContext&      context
)
    {
    AcString                fieldIdString = AcString(L" ") + AcString(s_objectIdFieldCode);
    
    WCharCP                 pCurr = specification;
    for (int i=0; true; i++)
        {
        WChar               tag[256];
        ElementId           elementId;
        AcDbObjectId        objectId;
        AcString            idValueString, idString;

        swprintf (tag, L"Element%d", i);

        if (SUCCESS == ParseXMLTag (idValueString, &pCurr, pCurr, tag) &&
            SUCCESS == ParseXMLTag (idString, NULL, idValueString, L"ID"))
            {
            if (1 != swscanf (idString, L"%I64d", &elementId))
                break;

            objectId = context.ExistingObjectIdFromElementId (elementId);

            // if the target element is not saved yet, save it now and get its object ID
            if (objectId.isNull() && RealDwgSuccess != context.PreSaveElementToDatabase(&objectId, elementId))
                break;

            if (objectId.isValid())
                {
                /*-----------------------------------------------------------------------
                The object ID value in a text string, as opposed to be in a field object,
                expresses the object address the ID points to, in an integer format.
                Therefore it must be platform dependent.  We employ the format specifier
                "I" for ptrdiff_t in swprintf to convert the object address to a 32-bit
                or a 64-bit integer depending on which platform we are on.
                -----------------------------------------------------------------------*/
                ACHAR       idValue[256];
                AcDbStub*   objAddress = (AcDbStub*)objectId;
                swprintf (idValue, L"%I64d", (UInt64)objAddress);
                fieldIdString += AcString(idValue) + AcString(L">%).");
                // with RealDWG we can not set multiple 331's!
                return  fieldIdString;
                }
            break;
            }
        }

    return  fieldIdString + AcString(L"0");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 RemoveMarkupPrefixes
(
AcString&                   fieldValue
)
    {
    int                     numChars = fieldValue.length ();
    const ACHAR*            valueString = fieldValue.kwszPtr ();
    int                     startAt = 0;

    while (startAt < numChars && L'{' == valueString[startAt])
        {
        startAt++;
        }

    if (startAt + 2 > numChars)
        return;

    if (L'\\' == valueString[startAt] && L'\\' != valueString[startAt+1])
        {
        int         lastFound = fieldValue.findRev (L';');

        if (lastFound >= 0)
            {
            while (lastFound+1 < numChars && L'}' == valueString[lastFound+1])
                lastFound++;

            if (++lastFound < numChars)
                fieldValue = fieldValue.substr (lastFound, -1);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 RemoveExtraBackSlashes
(
AcString&                   fieldValue
)
    {
    int     foundAt = fieldValue.find (L"\\\\");

    if (foundAt > -1)
        {
        AcString    nextString = fieldValue.substr (foundAt+1, -1);

        RemoveExtraBackSlashes (nextString);

        fieldValue = fieldValue.substr(foundAt) + nextString;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        FixUpParentFieldData
(
AcDbField*                  pParentField,
const AcDbFieldArray&       childFields,
const AcString&             parentFieldCode
)
    {
    RecordingFiler  filer (30 + childFields.length());
    filer.RecordData (pParentField);

#if defined (REALDWG_FILER_DEBUG)
    /*-----------------------------------------------------------------------------------
    RealDWG 2009 is unable to add child fields to a parent field(setFieldCode returns eOk
    but failed to append children).  Due to this bug RealDWG 2009 cannot even create any
    field at all.  We workaround this bug by appending child fields with DWGIN filling.
    Here is the RecordingFiler's dump list from a parent field:

     0: kDwgSoftPointerId, ffd034a0 fd6340ff AcDbDictionary
     1: kDwgUInt32,    1 (0x1)
     2: kDwgSoftPointerId, ffd034a0 fd6340ff AcDbDictionary
     3: kDwgHardOwnershipId, NULL
     4: kDwgString(ACHAR) _text
     5: kDwgString(ACHAR) area =  sq. m                    <= parent field code
     6: kDwgInt32,    0 (0x0)                              <= number of child fields
     7: kDwgInt32,    0 (0x0)
     8: kDwgInt32,   63 (0x3f)
     9: kDwgInt32,    0 (0x0)
    10: kDwgInt32,   13 (0xd)
    11: kDwgInt32,    2 (0x2)
    12: kDwgInt32,    0 (0x0)
    13: kDwgString(ACHAR)
    14: kDwgInt32,    3 (0x3)
    15: kDwgInt32,    0 (0x0)
    16: kDwgInt32,    0 (0x0)
    17: kDwgString(ACHAR)
    18: kDwgString(ACHAR)
    19: kDwgString(ACHAR)
    20: kDwgInt32,    0 (0x0)
    21: kDwgInt32,    1 (0x1)
    22: kDwgString(ACHAR) ACFD_FIELDTEXT_CHECKSUM
    23: kDwgInt32,    2 (0x2)
    24: kDwgInt32,    2 (0x2)
    25: kDwgReal 11016.000000
    26: kDwgInt32,    0 (0x0)
    27: kDwgString(ACHAR)
    28: kDwgString(ACHAR)

    We need to reset and/or insert child field ID pointer entries:

     5: kDwgString(ACHAR) area = %<\_FldIdx 0>% sq. m       <= parent field code
     6: kDwgInt32,    1 (0x1)                               <= number of child fields
     7: kDwgHardOwnershipId, ffd034b0 fd6340ef AcDbField    <= pointer to the 1st child field
     ...

    -----------------------------------------------------------------------------------*/
    filer.DumpList ("Parent AcDbField from DWGOUT filing:");
#endif

    FilerDataList&      dataList = filer.GetDataList ();

    // entry #5 is a string type since R2017 - TFS 569776.
    StringFilerData*    fieldCodeString = nullptr;
    ACharFilerData*     fieldCodeChars = dynamic_cast <ACharFilerData *> (dataList[5]);
    if (nullptr != fieldCodeChars)
        fieldCodeChars->SetValue (parentFieldCode);
    else if (nullptr != (fieldCodeString = dynamic_cast<StringFilerData *>(dataList[5])))
        fieldCodeString->SetValue (parentFieldCode);
    else
        return BadDataSequence;

    Int32FilerData*     numChildrenData = dynamic_cast <Int32FilerData *> (dataList[6]);
    if (NULL == numChildrenData)
        return BadDataSequence;

    Int32               oldNumChildren = numChildrenData->GetValue ();
    Int32               newNumChildren = childFields.length ();

    numChildrenData->SetValue (newNumChildren);

    for (Int32 i = 0; i < newNumChildren; i++)
        {
        UInt32          dataIndex = 7 + i;
        AcDbObjectId    childObjId = childFields.at(i)->objectId ();

        if (i < oldNumChildren)
            {
            HardOwnershipIdFilerData*   idData = dynamic_cast <HardOwnershipIdFilerData *> (dataList[dataIndex]);
            idData->SetValue (childObjId);
            }
        else
            {
            HardOwnershipIdFilerData*   idData = new HardOwnershipIdFilerData (childObjId);
            filer.InsertEntryAt (dataIndex, idData);
            }
        }

    Acad::ErrorStatus   es = filer.PlaybackData (pParentField);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (30 + childFields.length());
        check.RecordData (pParentField);
        check.DumpList ("Parent AcDbField after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    FixUpChildFieldData
(
AcDbField*              pChildField,
const AcString&         result
)
    {
    RecordingFiler  filer (40);
    filer.RecordData (pChildField);

#if defined (REALDWG_FILER_DEBUG)
    /*-----------------------------------------------------------------------------------
    RealDWG 2009 failed to evaluate child field (showing result as ####).  So we employ
    RecordingFiler to workaround that by filling evaluated and cache resultant strings,
    as well as resetting field's state.  Here is a dump list from a child field:

     0: kDwgSoftPointerId, ffe01eb8 fd26f8e9 AcDbField
     1: kDwgUInt32,    0 (0x0)
     2: kDwgHardOwnershipId, NULL
     3: kDwgString(ACHAR) AcObjProp
     4: kDwgString(ACHAR) \AcObjProp Object(%<\_ObjIdx 0>%).Center
     5: kDwgInt32,    0 (0x0)                                   <= number child fields
     ... may have child field objects
     6: kDwgInt32,    1 (0x1)                                   <= number of target objects
     7: kDwgSoftPointerId, ffe01e98 fd26f8c9 AcDbArc
     ... may have more target objects
     8: kDwgInt32,   63 (0x3f)                                  <= evaluation option
     9: kDwgInt32,    0 (0x0)                                   <= filling option
    10: kDwgInt32,   59 (0x3b)                                  <= field state
    11: kDwgInt32,    2 (0x2)                                   <= evaluation status
    12: kDwgInt32,    0 (0x0)                                   <= error code
    13: kDwgString(ACHAR)                                       <= evaluation error message
    14: kDwgInt32,    0 (0x0)                                   <= AcValue: flags?? 0x0 defaults to parge AcValue; 0x1 no data present & must verify other types.
    15: kDwgInt32,   32 (0x20)                                  <= AcValue: value type, k3dPoint in this example
    16: kDwgUInt32,   24 (0x18)                                 <= AcValue: size for kString, kDate, kPoint, and k3dPoint. No entry for other types.
    17: Byte, 24 bytes                                          <= AcValue: value by data type
    18: kDwgInt32,    0 (0x0)                                   <= AcValue: unit type
    19: kDwgString(ACHAR)                                       <= AcValue: string format
    20: kDwgString(ACHAR)                                       <= AcValue: string value
    21: kDwgString(ACHAR) 25.161756, 23.432430, 0.000000        <= text string value
    22: kDwgInt32,   30 (0x1e)                                  <= number of chars in above text string
    23: kDwgInt32,    2 (0x2)                                   <= number of data sets in this field, ObjPropId & ObjPropName in this example. Read them in AcValue's.
    24: kDwgString(ACHAR) ObjectPropertyId
    25: kDwgInt32,    2 (0x2)
    26: kDwgInt32,   64 (0x40)
    27: kDwgSoftPointerId, ffe01e98 fd26f8c9 AcDbArc
    28: kDwgInt32,    0 (0x0)
    29: kDwgString(ACHAR)
    30: kDwgString(ACHAR)
    31: kDwgString(ACHAR) ObjectPropertyName
    32: kDwgInt32,    0 (0x0)
    33: kDwgInt32,    4 (0x4)
    34: kDwgUInt32,   14 (0xe)
    35: Byte, 14 bytes
    36: kDwgInt32,    0 (0x0)
    37: kDwgString(ACHAR)
    38: kDwgString(ACHAR)
    -----------------------------------------------------------------------------------*/
    filer.DumpList ("Child field from DWGOUT filing:");
#endif

    FilerDataList&      dataList = filer.GetDataList ();
    // seek the index for state value: count through start of target objects:
    UInt32  index = 5 + pChildField->childCount() + 1;

    Int32FilerData*     intData = dynamic_cast <Int32FilerData *> (dataList[6]);
    if (NULL == intData)
        return BadDataSequence;

    // continue to count through number of target objects, index of evaluation option, index of filling option and arrive at field state:
    index += intData->GetValue() + 3;

    // Set field state at the index found above:
    intData = dynamic_cast <Int32FilerData *> (dataList[index]);
    if (NULL == intData)
        return BadDataSequence;

    int state = (AcDbField::kInitialized | AcDbField::kCompiled | AcDbField::kEvaluated | AcDbField::kHasCache);
    if (NULL != pChildField->getFormat())
        state |= AcDbField::kHasFormattedString;

    intData->SetValue (state);

    // Move index to AcValue type
    index += 4;
    // Move index through AcValue:
    if (RealDwgSuccess != dataList.GetAcValueAt(NULL, index))
        return BadDataSequence;
    // Restore the index moved by GetAcValueAt back to the string value (20th in above example)
    index--;

    // Set the evaluated string:
    ACharFilerData*     resultChars = dynamic_cast <ACharFilerData *> (dataList[index++]);
    if (NULL == resultChars)
        return BadDataSequence;

    resultChars->SetValue (result);

    StringFilerData*    resultString = nullptr;

    // Set the cache string at next entry (21th in above example)
    if (nullptr == (resultChars  = dynamic_cast <ACharFilerData *> (dataList[index])) &&
        nullptr == (resultString = dynamic_cast <StringFilerData *> (dataList[index])))
        return BadDataSequence;
    else
        index++;

    if (nullptr != resultChars)
        resultChars->SetValue (result);
    else if (nullptr != resultString)
        resultString->SetValue (result);
    else
        return BadDataSequence;

    // set number of chars for the above cache string
    intData = dynamic_cast <Int32FilerData *> (dataList[index++]);
    if (NULL == intData)
        return BadDataSequence;

    intData->SetValue (result.length());

    Acad::ErrorStatus   es = filer.PlaybackData (pChildField);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (30);
        check.RecordData (pChildField);
        check.DumpList ("Child field from DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::CreateFieldsInContentString(AcString& contentString)
    {
    AcString    fieldToken;
    AcString    outString = contentString;
    WCharCP     pCurr = contentString.kwszPtr ();
    UInt32      numFields = 0;

    while (SUCCESS == ParseXMLTag(fieldToken, &pCurr, pCurr, L"Field"))
        {
        AcString        specification, acEvaluator, result (L"");

        if (SUCCESS == ParseXMLTag (result, NULL, fieldToken, L"Result") &&
            SUCCESS == ParseXMLTag (specification, NULL, fieldToken, L"Specification") &&
            SUCCESS == ParseXMLStringTag (acEvaluator, specification, L"AC_Evaluator"))
            {
            AcString    formatString, valueString, accessString;

            if (SUCCESS != ParseXMLStringTag (formatString,  specification, L"AC_Format"))
                formatString.assign (L"");

            bool        isObjectProperty = 0 == wcsncmp (acEvaluator, L"AcObj", 5),
                        isVariable       = 0 == wcsncmp (acEvaluator, L"AcVar", 5);

            if ((isObjectProperty || isVariable) && SUCCESS == ParseXMLStringTag (accessString, specification, L"AC_Access"))
                {
                AcString    childFieldCode = AcString(L"\\") + acEvaluator;

                if (isObjectProperty)
                    childFieldCode += ObjectIdStringFromXML(specification, *this);
                else
                    childFieldCode += AcString(L" ");

                childFieldCode += accessString;
                if (!formatString.isEmpty())
                    childFieldCode += AcString(L" \\f \"") + formatString + AcString (L"\"");

                childFieldCode = AcString(L"%<") + childFieldCode + AcString(L">%");

                AcString    taggedFieldToken = RealDwgUtil::FormatXMLTag (L"Field", fieldToken);

                RealDwgUtil::ReplaceStringInAcString (outString, taggedFieldToken, childFieldCode);
                numFields++;
                }
            }
        }

    if (numFields > 0)
        {
        contentString = outString;
        return  RealDwgSuccess;
        }
    
    return  CantCreateFields;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsBlankFieldValue (const AcString& value)
    {
    // check if field contains nothing but "####".
    const ACHAR*    result = value.kwszPtr ();
    for (unsigned i = 0; i < value.length(); i++)
        {
        if (L'#' != result[i])
            return  false;
        }
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2005
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ExtractFields
(
AcDbObject*                 pObject,
const ACHAR*                oldString,
AcString&                   contentString
)
    {
    Int32           iField;
    AcString        originalString  = contentString;
    WCharCP         pCurr = originalString.kwszPtr();
    AcString        parentFieldCode (contentString);
    AcString        fieldToken;
    AcDbField*      pParentField = NULL;
    AcDbDatabase*   pDatabase = m_pFileHolder->GetDatabase();
    AcDbFieldArray  childArray;

    for (iField = 0; SUCCESS == ParseXMLTag (fieldToken, &pCurr, pCurr, L"Field");)
        {
        AcString            specification, acEvaluator, result (L"");

        // First make sure that this tag can be saved to DWG (i.e. it has an ECEvaluator specified).
        if (SUCCESS == ParseXMLTag (result, NULL, fieldToken, L"Result") &&
            SUCCESS == ParseXMLTag (specification, NULL, fieldToken, L"Specification") &&
            SUCCESS == ParseXMLStringTag (acEvaluator, specification, L"AC_Evaluator"))
            {
            AcDbField*              pChildField = NULL;

            if (0 == iField)
                {
                /*-----------------------------------------------------------------------
                Workaround a likely RealDWG 2009 bug: getField failed to open a field for
                write when the source object is already opened for write, returning an
                error kKeyNotFound.
                -----------------------------------------------------------------------*/
                Acad::ErrorStatus   es = pObject->downgradeOpen ();
                es = pObject->getField (L"TEXT", pParentField, AcDb::kForWrite);
                pObject->upgradeOpen ();

                if (Acad::eOk != es)
                    {
                    if (pObject->hasFields())
                        pObject->removeField (L"TEXT");

                    pParentField = new AcDbField ();

                    pParentField->setOwnerId (pObject->objectId());
                    pParentField->setEvaluatorId (L"_text");
                    pParentField->setEvaluationOption (AcDbField::kAutomatic);
                    pParentField->setFilingOption ((AcDbField::FilingOption)0);

                    AcDbObjectId        newId;
                    if (Acad::eOk != pObject->setField (L"TEXT", pParentField, newId))
                        {
                        DIAGNOSTIC_PRINTF ("Failed adding parent field %I64x to database\n", this->ElementIdFromObjectId(pObject->objectId()));
                        delete pParentField;
                        return;
                        }
                    }
                }

            bool    isNewChild = false;
            if (pParentField->childCount() > iField)
                {
                if (Acad::eOk != pParentField->getChild (iField, pChildField, AcDb::kForWrite))
                    {
                    DIAGNOSTIC_PRINTF ("Failed getting child field %d for %I64x\n", iField, this->ElementIdFromObjectId(pObject->objectId()));
                    continue;
                    }
                }
            else
                {
                pChildField = new AcDbField ();
                if (Acad::eOk != pDatabase->addAcDbObject(pChildField))
                    {
                    DIAGNOSTIC_PRINTF ("Failed adding child field %d for %I64x to database\n", iField, this->ElementIdFromObjectId(pObject->objectId()));
                    continue;
                    }

                pChildField->setOwnerId (pParentField->objectId());
                pChildField->setEvaluatorId (acEvaluator);
                pChildField->setEvaluationOption (AcDbField::kAutomatic);
                pChildField->setFilingOption ((AcDbField::FilingOption)0);

                isNewChild = true;
                }

            childArray.append (pChildField);

            // Need to review this logic in the future: what if results are the same but field codes are different?
            if (result != pChildField->getValue() || (isNewChild && IsBlankFieldValue(result)))
                {
                AcString            formatString, valueString, accessString;

                if (SUCCESS != ParseXMLStringTag (formatString,  specification, L"AC_Format"))
                    formatString.assign (L"");

                pChildField->setFormat (formatString);

                AcFdFieldValue      value;
                bool                isObjectProperty = 0 == wcsncmp (acEvaluator, L"AcObj", 5),
                                    isVariable       = 0 == wcsncmp (acEvaluator, L"AcVar", 5);

                if ((isObjectProperty || isVariable) &&
                    SUCCESS == ParseXMLStringTag (accessString, specification, L"AC_Access"))
                    {
                    AcString        childFieldCode;

                    if (SUCCESS == ParseXMLTag (valueString, NULL, specification, L"Value") &&
                        SUCCESS == ExtractFieldValueFromXML (value, valueString, accessString, *this))
                        pChildField->setData (L"ACFD_FIELD_VALUE", &value);

                    if (isObjectProperty)
                        {
                        // try setting the target object ID for this child field:
                        if (ExtractObjectIdsFromXML(pChildField, specification, *this) < 0)
                            {
                            // target object has been ignored by DWG save, ignore this field:
                            childArray.removeLast ();
                            pChildField->erase ();
                            continue;
                            }

                        childFieldCode = AcString (L"\\") + acEvaluator + AcString (L" Object(%%<\\_ObjIdx 0>%%).");
                        }
                    else
                        {
                        childFieldCode = AcString (L"\\") + acEvaluator + AcString (L" ");
                        }

                    value.set (accessString);
                    pChildField->setData (isObjectProperty ? L"ObjectPropertyName": L"Variable", &value);

                    childFieldCode = childFieldCode + accessString;
                    if (!formatString.isEmpty())
                        childFieldCode = childFieldCode + AcString(L" \\f \"") + formatString + AcString (L"\"");

                    Acad::ErrorStatus   es = pChildField->setFieldCode (childFieldCode);
                    if (Acad::eOk != es)
                        DIAGNOSTIC_PRINTF ("Error saving field. <%ls>\n", acadErrorStatusText(es));
                    }

                RemoveMarkupPrefixes (result);

                if (isVariable && L"Filename" == accessString)
                    RemoveExtraBackSlashes (result);

                FixUpChildFieldData (pChildField, result);

                int     numEval = 0;
                Acad::ErrorStatus   es = pChildField->evaluate (pChildField->evaluationOption(), pDatabase, &numEval);
                }

            AcString        fieldMarker;
            fieldMarker.format (L"%%<\\_FldIdx %d>%%", iField++);

            AcString        taggedFieldToken = RealDwgUtil::FormatXMLTag (L"Field", fieldToken);
            RealDwgUtil::ReplaceStringInAcString (parentFieldCode, taggedFieldToken, fieldMarker);

            if (NULL != pChildField)
                pChildField->close ();
            }
        }

    if (0 == iField)
        {
        if (pObject->hasFields())
            pObject->removeField (L"TEXT");
        }
    else
        {
        const ACHAR*        pChar = oldString;
        double              checkSum = 0;

        for (int j=1; '\0' != *pChar; pChar++, j++)
            checkSum += (double) *pChar * (double) j;

        AcFdFieldValue      checkSumValue(checkSum);
        pParentField->setData (L"ACFD_FIELDTEXT_CHECKSUM", &checkSumValue);

        AcDbFieldArray*     pCopyArray = childArray.isEmpty() ? NULL : new AcDbFieldArray(childArray);

        AcDbField::FieldCodeFlag    nFlags = AcDbField::FieldCodeFlag(AcDbField::kTextField | AcDbField::kPreserveFields);
        Acad::ErrorStatus           es = pParentField->setFieldCode (parentFieldCode, nFlags, pCopyArray);
        if (Acad::eOk != es)
            DIAGNOSTIC_PRINTF ("Error saving field. <%ls>\n", acadErrorStatusText(es));

        if (NULL != pCopyArray)
            delete pCopyArray;

        if (!childArray.isEmpty())
            FixUpParentFieldData (pParentField, childArray, parentFieldCode);

        int     numEvaluated = 0;
        es = pParentField->evaluate (pParentField->evaluationOption(), pDatabase, &numEvaluated);

#if defined (REALDWG_DIAGNOSTICS_VERBOSE)
        RealDwgUtil::DumpField ("New Field", pParentField);
#endif
        }

    if (NULL != pParentField)
        {
        if (pObject->hasFields())
            pParentField->close ();
        else
            pParentField->erase ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2005
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::ExtractFieldContents
(
AcString&                   contentString
)
    {
    int                 iField;
    AcString            fieldToken, copyString = contentString;
    WChar const*      pCurr = copyString;                     // Fix for TR# 198913 - Work on copy or multiple fields will fail.

    for (iField = 0; SUCCESS == ParseXMLTag (fieldToken, &pCurr, pCurr, L"Field"); iField++)
        {
        AcString        result;

        // First make sure that this tag can be saved to DWG (i.e. it has an AEvaluator specified).
        if (SUCCESS == ParseXMLTag (result, NULL, fieldToken, L"Result"))
            {
            AcString        taggedFieldToken = RealDwgUtil::FormatXMLTag(L"Field", fieldToken);
            RealDwgUtil::ReplaceStringInAcString (contentString, taggedFieldToken, result);
            }
        }

    return iField > 0;
    }


