/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdArc.cpp $
|
|  $Copyright: (c) 2012 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtArc : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbArc*        pArc = AcDbArc::cast (acObject);
    double          sweepAngle = pArc->endAngle() - pArc->startAngle();

    if (!Angle::IsFullCircle(sweepAngle))
        sweepAngle = Angle::AdjustToSweep (sweepAngle, 0, msGeomConst_2pi);

    AcGeVector3d    normal = pArc->normal ();
    AcGePoint3d     center = pArc->center ();

    return context.CreateElementFromArcParams (outElement, &normal, center, NULL, pArc->radius(), pArc->radius(),
                                               pArc->startAngle(), sweepAngle, pArc->thickness(), false, pArc);
    }

};  // ToDgnExtArc
