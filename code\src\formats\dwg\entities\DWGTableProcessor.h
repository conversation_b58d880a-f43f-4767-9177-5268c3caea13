#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>
#include <unordered_map>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbtable.h>
#include <realdwg/base/dbfield.h>
#include <realdwg/base/dbfieldvalue.h>
#include <realdwg/base/dbtablestyle.h>
#include <realdwg/base/dbcellstyle.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// Table and Field Data Structures (Based on RealDwgFileIO rdTable.cpp and rdField.cpp)
//=======================================================================================

struct CellStyle {
    // Text properties
    std::string textStyleName = "Standard";
    double textHeight = 2.5;
    Color textColor = Color(0.0f, 0.0f, 0.0f, 1.0f);
    double textRotation = 0.0;
    
    // Text alignment
    enum class Alignment {
        TopLeft, TopCenter, TopRight,
        MiddleLeft, MiddleCenter, MiddleRight,
        BottomLeft, BottomCenter, BottomRight
    } alignment = Alignment::MiddleCenter;
    
    // Cell borders
    struct Border {
        bool isVisible = true;
        Color color = Color(0.0f, 0.0f, 0.0f, 1.0f);
        std::string lineType = "Continuous";
        double lineWeight = 0.25;
        
        bool IsValid() const {
            return lineWeight >= 0.0;
        }
    };
    
    Border topBorder;
    Border bottomBorder;
    Border leftBorder;
    Border rightBorder;
    
    // Cell background
    bool hasBackground = false;
    Color backgroundColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    
    // Cell margins
    double leftMargin = 1.5;
    double rightMargin = 1.5;
    double topMargin = 1.5;
    double bottomMargin = 1.5;
    
    // Data format
    enum class DataType {
        Text, Number, Currency, Date, Percentage, Angle, Point, General
    } dataType = DataType::General;
    
    std::string formatString;           // Format string for data
    int precision = 2;                  // Decimal precision
    std::string unitString;             // Unit string
    
    bool IsValid() const;
};

struct TableCell {
    std::string text;                   // Cell text content
    std::string formula;                // Cell formula (if any)
    
    // Cell properties
    CellStyle style;                    // Cell style
    bool isLocked = false;              // Cell is locked
    bool isAutoScale = true;            // Auto-scale text
    
    // Cell span
    int rowSpan = 1;                    // Number of rows spanned
    int columnSpan = 1;                 // Number of columns spanned
    
    // Cell type
    enum class Type {
        Text,           // Text cell
        Block,          // Block reference cell
        Formula,        // Formula cell
        Field           // Field cell
    } type = Type::Text;
    
    // Block reference (for block cells)
    std::string blockName;
    double blockScale = 1.0;
    double blockRotation = 0.0;
    std::unordered_map<std::string, std::string> blockAttributes;
    
    // Field reference (for field cells)
    std::string fieldCode;
    std::string fieldFormat;
    
    bool IsValid() const;
    bool IsSpanned() const { return rowSpan > 1 || columnSpan > 1; }
    bool HasFormula() const { return !formula.empty(); }
    bool HasField() const { return type == Type::Field && !fieldCode.empty(); }
};

struct TableGeometry {
    Point3d insertionPoint;             // Table insertion point
    Vector3d direction = Vector3d(1, 0, 0); // Table direction
    Vector3d normal = Vector3d(0, 0, 1);    // Table normal
    
    // Table dimensions
    int numRows = 1;                    // Number of rows
    int numColumns = 1;                 // Number of columns
    std::vector<double> rowHeights;     // Row heights
    std::vector<double> columnWidths;   // Column widths
    
    // Table cells
    std::vector<std::vector<TableCell>> cells; // Table cells [row][column]
    
    // Table style
    std::string tableStyleName = "Standard";
    
    // Table properties
    enum class FlowDirection {
        Down, Up
    } flowDirection = FlowDirection::Down;
    
    bool hasTitle = false;              // Table has title row
    bool hasHeader = false;             // Table has header row
    int titleRowCount = 0;              // Number of title rows
    int headerRowCount = 0;             // Number of header rows
    
    // Table breaks
    bool enableBreaking = false;        // Enable table breaking
    double breakHeight = 0.0;           // Break height
    std::vector<int> breakRows;         // Manual break rows
    
    // Table regeneration
    bool suppressTitle = false;         // Suppress title in continuation
    bool suppressHeader = false;        // Suppress header in continuation
    
    bool IsValid() const;
    bool HasValidDimensions() const;
    TableCell& GetCell(int row, int column);
    const TableCell& GetCell(int row, int column) const;
    double CalculateTableWidth() const;
    double CalculateTableHeight() const;
};

struct FieldGeometry {
    enum class Type {
        Text,           // Text field
        Date,           // Date field
        FileName,       // File name field
        FileSize,       // File size field
        SaveDate,       // Save date field
        PlotDate,       // Plot date field
        SheetSet,       // Sheet set field
        Custom,         // Custom field
        Formula,        // Formula field
        Hyperlink,      // Hyperlink field
        Object          // Object field
    } type = Type::Text;
    
    std::string fieldCode;              // Field code
    std::string formatString;           // Format string
    std::string evaluatedText;          // Evaluated text
    
    // Field properties
    bool isLocked = false;              // Field is locked
    bool isAutoUpdate = true;           // Auto-update field
    
    // Field context
    std::string objectId;               // Associated object ID
    std::string propertyName;           // Property name for object fields
    
    // Field evaluation
    enum class State {
        NotEvaluated,   // Not evaluated
        Evaluated,      // Successfully evaluated
        Error,          // Evaluation error
        NotApplicable   // Not applicable
    } state = State::NotEvaluated;
    
    std::string errorMessage;           // Error message if evaluation failed
    
    bool IsValid() const;
    bool NeedsEvaluation() const;
    std::string GetDisplayText() const;
};

//=======================================================================================
// Table and Field Validation Results
//=======================================================================================

struct TableValidationResult : public DWGValidationResult {
    bool hasValidDimensions = false;
    bool hasValidCells = false;
    bool hasValidStyle = false;
    bool hasValidPosition = false;
    int invalidCellCount = 0;
    int emptyRowCount = 0;
    int emptyColumnCount = 0;
    
    void AddTableError(const std::string& error) {
        AddError("Table: " + error);
    }
    
    void AddTableWarning(const std::string& warning) {
        AddWarning("Table: " + warning);
    }
};

struct FieldValidationResult : public DWGValidationResult {
    bool hasValidCode = false;
    bool hasValidFormat = false;
    bool canEvaluate = false;
    FieldGeometry::State evaluationState = FieldGeometry::State::NotEvaluated;
    
    void AddFieldError(const std::string& error) {
        AddError("Field: " + error);
    }
    
    void AddFieldWarning(const std::string& warning) {
        AddWarning("Field: " + warning);
    }
};

//=======================================================================================
// DWG Table and Field Processor (Based on RealDwgFileIO rdTable.cpp and rdField.cpp)
//=======================================================================================

class DWGTableProcessor : public DWGEntityProcessor {
public:
    DWGTableProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGTableProcessor"; }

    // Table and field processing methods
    DWGProcessingStatus ProcessTable(const TableGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessField(const FieldGeometry& geometry, const std::string& context = "");

    // Table processing helpers
    DWGProcessingStatus ProcessTableCells(const TableGeometry& geometry) const;
    DWGProcessingStatus ProcessTableStyle(const TableGeometry& geometry) const;
    DWGProcessingStatus ProcessTableBreaks(const TableGeometry& geometry) const;

    // Cell processing
    bool ProcessTableCell(const TableCell& cell, int row, int column, const TableGeometry& table) const;
    bool ProcessCellText(const TableCell& cell, int row, int column) const;
    bool ProcessCellBlock(const TableCell& cell, int row, int column) const;
    bool ProcessCellFormula(const TableCell& cell, int row, int column) const;
    bool ProcessCellField(const TableCell& cell, int row, int column) const;

    // Field processing
    bool EvaluateField(FieldGeometry& field) const;
    std::string FormatFieldValue(const FieldGeometry& field, const std::string& value) const;
    bool UpdateFieldText(FieldGeometry& field) const;

    // Validation methods
    TableValidationResult ValidateTableGeometry(const TableGeometry& geometry) const;
    FieldValidationResult ValidateFieldGeometry(const FieldGeometry& geometry) const;
    bool ValidateTableDimensions(const TableGeometry& geometry) const;
    bool ValidateTableCells(const TableGeometry& geometry) const;
    bool ValidateCellStyle(const CellStyle& style) const;
    bool ValidateFieldCode(const std::string& fieldCode) const;

    // Table geometry calculations
    Point3d CalculateCellPosition(int row, int column, const TableGeometry& geometry) const;
    BoundingBox3D CalculateCellBounds(int row, int column, const TableGeometry& geometry) const;
    BoundingBox3D CalculateTableBounds(const TableGeometry& geometry) const;
    double CalculateRowHeight(int row, const TableGeometry& geometry) const;
    double CalculateColumnWidth(int column, const TableGeometry& geometry) const;

    // Table optimization and repair
    bool RepairTableGeometry(TableGeometry& geometry) const;
    bool OptimizeTableLayout(TableGeometry& geometry) const;
    bool AutoSizeColumns(TableGeometry& geometry) const;
    bool AutoSizeRows(TableGeometry& geometry) const;
    bool ValidateAndFixCellSpans(TableGeometry& geometry) const;

    // Field evaluation and formatting
    std::string EvaluateTextField(const FieldGeometry& field) const;
    std::string EvaluateDateField(const FieldGeometry& field) const;
    std::string EvaluateFileField(const FieldGeometry& field) const;
    std::string EvaluateCustomField(const FieldGeometry& field) const;
    std::string EvaluateFormulaField(const FieldGeometry& field) const;
    std::string EvaluateObjectField(const FieldGeometry& field) const;

    // Table style management
    bool CreateTableStyle(const std::string& styleName, const CellStyle& defaultCellStyle);
    bool GetTableStyle(const std::string& styleName, CellStyle& style) const;
    std::vector<std::string> GetAvailableTableStyles() const;
    bool ValidateTableStyle(const std::string& styleName) const;

private:
    // Table processing helpers
    bool ProcessTableStructure(const TableGeometry& geometry) const;
    bool ProcessTableBorders(const TableGeometry& geometry) const;
    bool ProcessTableText(const TableGeometry& geometry) const;
    bool ValidateTableStructure(const TableGeometry& geometry) const;
    
    // Cell processing helpers
    bool ValidateCellPosition(int row, int column, const TableGeometry& geometry) const;
    bool ValidateCellSpan(const TableCell& cell, int row, int column, const TableGeometry& geometry) const;
    Point3d CalculateCellTextPosition(const TableCell& cell, int row, int column, const TableGeometry& geometry) const;
    
    // Cell style processing
    bool ProcessCellBorders(const CellStyle& style, int row, int column) const;
    bool ProcessCellBackground(const CellStyle& style, int row, int column) const;
    bool ProcessCellTextStyle(const CellStyle& style, int row, int column) const;
    
    // Field processing helpers
    bool ValidateFieldContext(const FieldGeometry& field) const;
    bool ParseFieldCode(const std::string& fieldCode, std::string& fieldType, std::unordered_map<std::string, std::string>& parameters) const;
    std::string ApplyFieldFormat(const std::string& value, const std::string& format, FieldGeometry::Type type) const;
    
    // Formula evaluation
    bool EvaluateFormula(const std::string& formula, std::string& result) const;
    bool ValidateFormulaExpression(const std::string& formula) const;
    std::string ParseFormulaReferences(const std::string& formula, const TableGeometry& table) const;
    
    // Date and time formatting
    std::string FormatDate(const std::string& format) const;
    std::string FormatTime(const std::string& format) const;
    std::string FormatDateTime(const std::string& format) const;
    
    // File information
    std::string GetFileName() const;
    std::string GetFileSize() const;
    std::string GetSaveDate() const;
    std::string GetPlotDate() const;
    
    // Geometric calculations
    void TransformTableGeometry(TableGeometry& geometry) const;
    Point3d TransformTablePoint(const Point3d& point, const TableGeometry& geometry) const;
    Vector3d GetTableXDirection(const TableGeometry& geometry) const;
    Vector3d GetTableYDirection(const TableGeometry& geometry) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbTable* CreateDWGTable(const TableGeometry& geometry) const;
    AcDbField* CreateDWGField(const FieldGeometry& geometry) const;
    
    // Table creation helpers
    bool SetTableDimensions(AcDbTable* table, const TableGeometry& geometry) const;
    bool SetTableCells(AcDbTable* table, const TableGeometry& geometry) const;
    bool SetTableStyle(AcDbTable* table, const std::string& styleName) const;
    
    // Cell creation helpers
    bool SetCellText(AcDbTable* table, int row, int column, const TableCell& cell) const;
    bool SetCellStyle(AcDbTable* table, int row, int column, const CellStyle& style) const;
    bool SetCellBlock(AcDbTable* table, int row, int column, const TableCell& cell) const;
    bool SetCellField(AcDbTable* table, int row, int column, const TableCell& cell) const;
    
    // Field creation helpers
    bool SetFieldCode(AcDbField* field, const FieldGeometry& geometry) const;
    bool SetFieldFormat(AcDbField* field, const FieldGeometry& geometry) const;
    bool EvaluateFieldValue(AcDbField* field) const;
    
    // Property setting helpers
    bool SetTableProperties(AcDbTable* table, const TableGeometry& geometry) const;
    bool SetFieldProperties(AcDbField* field, const FieldGeometry& geometry) const;
    
    // Error handling for RealDWG operations
    bool HandleTableCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Table style storage
    std::unordered_map<std::string, CellStyle> m_tableStyles;
    
    // Field evaluation cache
    mutable std::unordered_map<std::string, std::string> m_fieldCache;
    
    // Statistics and debugging
    mutable size_t m_processedTables = 0;
    mutable size_t m_processedFields = 0;
    mutable size_t m_processedCells = 0;
    mutable size_t m_evaluatedFields = 0;
    mutable size_t m_repairedTables = 0;
    
    // Configuration
    double m_tableTolerance = 1e-6;
    double m_cellTolerance = 1e-3;
    bool m_enableTableOptimization = true;
    bool m_enableFieldEvaluation = true;
    bool m_enableFormulaEvaluation = true;
    bool m_autoRepairTables = true;
    int m_maxTableRows = 1000;
    int m_maxTableColumns = 100;
    double m_minCellSize = 1.0;
    double m_maxCellSize = 1000.0;
};

} // namespace IModelExport
