#pragma once

#include "../../include/ExportTypes.h"
#include "ElementProcessor.h"
#include "GeometryProcessor.h"
#include "MaterialManager.h"
#include <memory>
#include <functional>
#include <thread>
#include <future>

namespace IModelExport {

// Forward declarations
class ExportContext;
class IModelDb;

//=======================================================================================
// Conversion Pipeline - Orchestrates the entire conversion process
//=======================================================================================

class ConversionPipeline {
public:
    ConversionPipeline(std::shared_ptr<ExportContext> context);
    ~ConversionPipeline();

    //===================================================================================
    // Pipeline Configuration
    //===================================================================================

    // Pipeline stages
    enum class PipelineStage {
        Initialize,         // Initialize pipeline and validate inputs
        Analyze,           // Analyze iModel structure and content
        Plan,              // Plan conversion strategy
        Extract,           // Extract data from iModel
        Transform,         // Transform coordinates and units
        Convert,           // Convert to target format
        Validate,          // Validate converted data
        Optimize,          // Optimize output
        Finalize,          // Finalize and cleanup
        Complete           // Pipeline complete
    };

    // Pipeline configuration
    struct PipelineConfig {
        ExportFormat targetFormat;
        ExportLOD levelOfDetail;
        bool enableParallelProcessing;
        size_t maxThreads;
        size_t batchSize;
        double geometryTolerance;
        bool enableOptimization;
        bool enableValidation;
        bool enableProgressReporting;
        std::string tempDirectory;
    };

    void SetPipelineConfig(const PipelineConfig& config);
    PipelineConfig GetPipelineConfig() const;

    //===================================================================================
    // Pipeline Execution
    //===================================================================================

    // Execute complete pipeline
    bool ExecutePipeline(const IModelDb& imodel, const ExportOptions& options);
    
    // Execute pipeline asynchronously
    std::future<bool> ExecutePipelineAsync(const IModelDb& imodel, const ExportOptions& options);
    
    // Execute specific pipeline stage
    bool ExecuteStage(PipelineStage stage);
    
    // Resume pipeline from specific stage
    bool ResumePipeline(PipelineStage fromStage);

    //===================================================================================
    // Pipeline Monitoring and Control
    //===================================================================================

    // Pipeline callbacks
    using StageCallback = std::function<bool(PipelineStage, double)>;
    using ProgressCallback = std::function<bool(double, const std::string&)>;
    using ErrorCallback = std::function<void(PipelineStage, const std::string&)>;

    void SetStageCallback(StageCallback callback);
    void SetProgressCallback(ProgressCallback callback);
    void SetErrorCallback(ErrorCallback callback);

    // Pipeline status
    enum class PipelineStatus {
        NotStarted,
        Running,
        Paused,
        Completed,
        Failed,
        Cancelled
    };

    PipelineStatus GetStatus() const;
    PipelineStage GetCurrentStage() const;
    double GetOverallProgress() const;
    double GetStageProgress() const;

    // Pipeline control
    void PausePipeline();
    void ResumePipeline();
    void CancelPipeline();
    bool IsPipelineCancelled() const;

    //===================================================================================
    // Data Flow Management
    //===================================================================================

    // Data containers for pipeline stages
    struct PipelineData {
        // Input data
        const IModelDb* imodel;
        ExportOptions options;
        
        // Analysis results
        std::vector<ElementInfo> elements;
        std::unordered_map<ElementType, size_t> elementCounts;
        BoundingBox modelBounds;
        std::string sourceUnits;
        Transform3d coordinateTransform;
        
        // Extracted data
        std::vector<GeometryData> geometries;
        std::vector<Material> materials;
        std::unordered_map<std::string, std::string> properties;
        std::unordered_map<std::string, std::vector<std::string>> hierarchies;
        
        // Converted data
        std::vector<std::shared_ptr<void>> convertedEntities;
        std::unordered_map<std::string, std::string> entityMappings;
        
        // Output data
        std::string outputPath;
        std::vector<std::string> outputFiles;
        size_t outputSize;
    };

    // Access pipeline data
    const PipelineData& GetPipelineData() const;
    
    // Set custom data processors
    using DataProcessor = std::function<bool(PipelineData&)>;
    void SetCustomDataProcessor(PipelineStage stage, DataProcessor processor);

    //===================================================================================
    // Pipeline Analytics and Reporting
    //===================================================================================

    // Pipeline metrics
    struct PipelineMetrics {
        std::chrono::system_clock::time_point startTime;
        std::chrono::system_clock::time_point endTime;
        double totalDuration;
        std::unordered_map<PipelineStage, double> stageDurations;
        size_t elementsProcessed;
        size_t elementsSkipped;
        size_t elementsWithErrors;
        size_t memoryPeakUsage;
        double throughput; // elements per second
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };

    PipelineMetrics GetPipelineMetrics() const;
    
    // Generate detailed report
    std::string GeneratePipelineReport() const;
    
    // Export metrics to file
    bool ExportMetrics(const std::string& filePath) const;

    //===================================================================================
    // Error Handling and Recovery
    //===================================================================================

    // Error recovery strategies
    enum class RecoveryStrategy {
        Retry,              // Retry current stage
        Skip,               // Skip current stage
        Fallback,           // Use fallback processing
        Rollback,           // Rollback to previous stage
        Abort               // Abort pipeline
    };

    // Handle pipeline error
    bool HandlePipelineError(PipelineStage stage, const std::string& error, 
                            RecoveryStrategy strategy = RecoveryStrategy::Retry);
    
    // Validate pipeline state
    bool ValidatePipelineState(std::vector<std::string>& errors) const;
    
    // Create pipeline checkpoint
    bool CreateCheckpoint(const std::string& checkpointPath);
    
    // Restore from checkpoint
    bool RestoreFromCheckpoint(const std::string& checkpointPath);

    //===================================================================================
    // Pipeline Optimization
    //===================================================================================

    // Optimize pipeline configuration
    PipelineConfig OptimizePipelineConfig(const IModelDb& imodel, 
                                         const ExportOptions& options) const;
    
    // Analyze pipeline bottlenecks
    std::vector<std::string> AnalyzePipelineBottlenecks() const;
    
    // Suggest pipeline improvements
    std::vector<std::string> SuggestPipelineImprovements() const;
    
    // Benchmark pipeline performance
    struct BenchmarkResults {
        double baselineTime;
        double optimizedTime;
        double improvementFactor;
        std::unordered_map<std::string, double> optimizationImpacts;
    };
    
    BenchmarkResults BenchmarkPipeline(const IModelDb& imodel, 
                                      const ExportOptions& options) const;

private:
    //===================================================================================
    // Internal Pipeline Implementation
    //===================================================================================

    std::shared_ptr<ExportContext> m_context;
    std::shared_ptr<ElementProcessor> m_elementProcessor;
    std::shared_ptr<GeometryProcessor> m_geometryProcessor;
    std::shared_ptr<MaterialManager> m_materialManager;

    // Pipeline state
    PipelineConfig m_config;
    PipelineData m_data;
    PipelineMetrics m_metrics;
    std::atomic<PipelineStatus> m_status{PipelineStatus::NotStarted};
    std::atomic<PipelineStage> m_currentStage{PipelineStage::Initialize};
    std::atomic<bool> m_cancelled{false};
    std::atomic<bool> m_paused{false};

    // Callbacks
    StageCallback m_stageCallback;
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;

    // Custom processors
    std::unordered_map<PipelineStage, DataProcessor> m_customProcessors;

    // Threading
    mutable std::mutex m_pipelineMutex;
    std::condition_variable m_pauseCondition;
    std::future<bool> m_pipelineFuture;

    //===================================================================================
    // Stage Implementation Methods
    //===================================================================================

    // Initialize pipeline
    bool InitializePipeline();
    
    // Analyze iModel
    bool AnalyzeIModel();
    
    // Plan conversion strategy
    bool PlanConversion();
    
    // Extract data from iModel
    bool ExtractData();
    
    // Transform coordinates and units
    bool TransformData();
    
    // Convert to target format
    bool ConvertData();
    
    // Validate converted data
    bool ValidateData();
    
    // Optimize output
    bool OptimizeOutput();
    
    // Finalize pipeline
    bool FinalizePipeline();

    //===================================================================================
    // Analysis Methods
    //===================================================================================

    // Analyze iModel structure
    bool AnalyzeModelStructure();
    
    // Analyze element distribution
    bool AnalyzeElementDistribution();
    
    // Analyze geometry complexity
    bool AnalyzeGeometryComplexity();
    
    // Analyze material usage
    bool AnalyzeMaterialUsage();
    
    // Estimate processing requirements
    bool EstimateProcessingRequirements();

    //===================================================================================
    // Planning Methods
    //===================================================================================

    // Plan processing strategy
    bool PlanProcessingStrategy();
    
    // Plan memory usage
    bool PlanMemoryUsage();
    
    // Plan threading strategy
    bool PlanThreadingStrategy();
    
    // Plan output organization
    bool PlanOutputOrganization();

    //===================================================================================
    // Extraction Methods
    //===================================================================================

    // Extract elements
    bool ExtractElements();
    
    // Extract geometry
    bool ExtractGeometry();
    
    // Extract materials
    bool ExtractMaterials();
    
    // Extract properties
    bool ExtractProperties();
    
    // Extract hierarchies
    bool ExtractHierarchies();

    //===================================================================================
    // Transformation Methods
    //===================================================================================

    // Transform coordinates
    bool TransformCoordinates();
    
    // Convert units
    bool ConvertUnits();
    
    // Apply coordinate system transformations
    bool ApplyCoordinateTransformations();
    
    // Validate transformations
    bool ValidateTransformations();

    //===================================================================================
    // Conversion Methods
    //===================================================================================

    // Convert elements to target format
    bool ConvertElements();
    
    // Convert geometry to target format
    bool ConvertGeometry();
    
    // Convert materials to target format
    bool ConvertMaterials();
    
    // Convert properties to target format
    bool ConvertProperties();
    
    // Build target format structure
    bool BuildTargetStructure();

    //===================================================================================
    // Validation Methods
    //===================================================================================

    // Validate converted geometry
    bool ValidateConvertedGeometry();
    
    // Validate converted materials
    bool ValidateConvertedMaterials();
    
    // Validate converted properties
    bool ValidateConvertedProperties();
    
    // Validate output structure
    bool ValidateOutputStructure();

    //===================================================================================
    // Optimization Methods
    //===================================================================================

    // Optimize geometry
    bool OptimizeGeometry();
    
    // Optimize materials
    bool OptimizeMaterials();
    
    // Optimize file structure
    bool OptimizeFileStructure();
    
    // Compress output
    bool CompressOutput();

    //===================================================================================
    // Utility Methods
    //===================================================================================

    // Update stage progress
    bool UpdateStageProgress(double percentage, const std::string& operation);
    
    // Update overall progress
    bool UpdateOverallProgress();
    
    // Check for cancellation
    bool CheckCancellation();
    
    // Wait for resume if paused
    void WaitForResume();
    
    // Log pipeline event
    void LogPipelineEvent(PipelineStage stage, const std::string& message);
    
    // Calculate stage weight for progress calculation
    double GetStageWeight(PipelineStage stage) const;
    
    // Get stage name
    std::string GetStageName(PipelineStage stage) const;
    
    // Cleanup pipeline resources
    void CleanupPipelineResources();
};

//=======================================================================================
// Pipeline Utilities
//=======================================================================================

namespace PipelineUtils {
    
    // Create optimized pipeline configuration
    ConversionPipeline::PipelineConfig CreateOptimizedConfig(
        ExportFormat targetFormat, const IModelDb& imodel);
    
    // Estimate pipeline duration
    double EstimatePipelineDuration(const ConversionPipeline::PipelineConfig& config,
                                   const IModelDb& imodel);
    
    // Calculate optimal thread count
    size_t CalculateOptimalThreadCount(const IModelDb& imodel, size_t availableCores);
    
    // Calculate optimal batch size
    size_t CalculateOptimalBatchSize(const IModelDb& imodel, size_t availableMemory);
    
    // Analyze pipeline performance
    std::vector<std::string> AnalyzePipelinePerformance(
        const ConversionPipeline::PipelineMetrics& metrics);
    
    // Generate performance recommendations
    std::vector<std::string> GeneratePerformanceRecommendations(
        const ConversionPipeline::PipelineMetrics& metrics);
}

} // namespace IModelExport
