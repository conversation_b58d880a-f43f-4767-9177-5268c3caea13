/*------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgBaseContext.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
// implementation of DwgSettings

#define LINEWEIGHT_PER_MM           50.0
#define DEFAULT_LINEWEIGHT_SCALE    0.275
#define MAX_DGN_EXPORT_ITEMS        2000
#define MAX_OrphanTags_PerSet       100

static UShort  s_standardDWGWeights[] = {0, 5, 9, 13, 15, 18, 20, 25, 30, 35, 40, 50, 53, 60, 70, 80, 90, 100, 106, 120, 140, 158, 200, 211 };

static byte s_dwgColors[256][3] =
        {
        { 255, 255, 255 },      // 255
        { 255, 255, 255 },      // 0
        { 255,   0,   0 },      // 1
        { 255, 255,   0 },      // 2
        {   0, 255,   0 },      // 3
        {   0, 255, 255 },      // 4
        {   0,   0, 255 },      // 5
        { 255,   0, 255 },      // 6
        { 255, 255, 255 },      // 7
        { 128, 128, 128 },      // 8
        { 192, 192, 192 },      // 9
        { 255,   0,   0 },      // 10
        { 255, 127, 127 },      // 11
        { 204,   0,   0 },      // 12
        { 204, 102, 102 },      // 13
        { 153,   0,   0 },      // 14
        { 153,  76,  76 },      // 15
        { 127,   0,   0 },      // 16
        { 127,  63,  63 },      // 17
        {  76,   0,   0 },      // 18
        {  76,  38,  38 },      // 19
        { 255,  63,   0 },      // 20
        { 255, 159, 127 },      // 21
        { 204,  51,   0 },      // 22
        { 204, 127, 102 },      // 23
        { 153,  38,   0 },      // 24
        { 153,  95,  76 },      // 25
        { 127,  31,   0 },      // 26
        { 127,  79,  63 },      // 27
        {  76,  19,   0 },      // 28
        {  76,  49,  38 },      // 29
        { 255, 127,   0 },      // 30
        { 255, 191, 127 },      // 31
        { 204, 102,   0 },      // 32
        { 204, 153, 102 },      // 33
        { 153,  76,   0 },      // 34
        { 153, 114,  76 },      // 35
        { 127,  63,   0 },      // 36
        { 127,  95,  63 },      // 37
        {  76,  38,   0 },      // 38
        {  76,  57,  38 },      // 39
        { 255, 191,   0 },      // 40
        { 255, 223, 127 },      // 41
        { 204, 153,   0 },      // 42
        { 204, 178, 102 },      // 43
        { 153, 114,   0 },      // 44
        { 153, 133,  76 },      // 45
        { 127,  95,   0 },      // 46
        { 127, 111,  63 },      // 47
        {  76,  57,   0 },      // 48
        {  76,  66,  38 },      // 49
        { 255, 255,   0 },      // 50
        { 255, 255, 127 },      // 51
        { 204, 204,   0 },      // 52
        { 204, 204, 102 },      // 53
        { 153, 153,   0 },      // 54
        { 153, 153,  76 },      // 55
        { 127, 127,   0 },      // 56
        { 127, 127,  63 },      // 57
        {  76,  76,   0 },      // 58
        {  76, 76,   38 },      // 59
        { 191, 255,   0 },      // 60
        { 223, 255, 127 },      // 61
        { 153, 204,   0 },      // 62
        { 178, 204, 102 },      // 63
        { 114, 153,   0 },      // 64
        { 133, 153,  76 },      // 65
        {  95, 127,   0 },      // 66
        { 111, 127,  63 },      // 67
        {  51,  76,   0 },      // 68
        {  66,  76,  38 },      // 69
        { 127, 255,   0 },      // 70
        { 191, 255, 127 },      // 71
        { 102, 204,   0 },      // 72
        { 153, 204, 102 },      // 73
        {  76, 153,   0 },      // 74
        { 114, 153,  76 },      // 75
        {  63, 127,   0 },      // 76
        {  95, 127,  63 },      // 77
        {  38,  76,   0 },      // 78
        {  57,  76,  38 },      // 79
        {  63, 255,   0 },      // 80
        { 159, 255, 107 },      // 81
        {  51, 204,   0 },      // 82
        { 127, 204, 102 },      // 83
        {  38, 153,   0 },      // 84
        {  95, 153,  76 },      // 85
        {  31, 127,   0 },      // 86
        {  79, 127,  63 },      // 87
        {  19,  76,   0 },      // 88
        {  47,  76,  38 },      // 89
        {   0, 255,   0 },      // 90
        { 127, 255, 127 },      // 91
        {   0, 204,   0 },      // 92
        { 102, 204, 102 },      // 93
        {   0, 153,   0 },      // 94
        {  76, 153,  76 },      // 95
        {   0, 127,   0 },      // 96
        {  63, 127,  63 },      // 97
        {   0,  76,   0 },      // 98
        {  38,  76,  38 },      // 99
        {   0, 255,  63 },      // 100
        { 127, 255, 159 },      // 101
        {   0, 204,  51 },      // 102
        { 102, 204, 127 },      // 103
        {   0, 153,  38 },      // 104
        {  76, 153,  95 },      // 105
        {   0, 127,  31 },      // 106
        {  63, 127,  31 },      // 107
        {   0,  76,  19 },      // 108
        {  38,  76,  47 },      // 109
        {   0, 255, 127 },      // 110
        { 127, 255, 191 },      // 111
        {   0, 204, 102 },      // 112
        { 102, 204, 153 },      // 113
        {   0, 153,  76 },      // 114
        {  76, 153, 114 },      // 115
        {   0, 127,  63 },      // 116
        {  63, 127,  95 },      // 117
        {   0,  76,  38 },      // 118
        {  38,  76,  57 },      // 119
        {   0, 255, 191 },      // 120
        { 127, 255, 223 },      // 121
        {   0, 204, 153 },      // 122
        { 102, 204, 178 },      // 123
        {   0, 153, 114 },      // 124
        {  76, 153, 133 },      // 125
        {   0, 127,  95 },      // 126
        {  63, 127, 111 },      // 127
        {   0,  76,  57 },      // 128
        {  38,  76,  66 },      // 129
        {   0, 255, 255 },      // 130
        { 127, 255, 255 },      // 131
        {   0, 204, 204 },      // 132
        { 102, 204, 204 },      // 133
        {   0, 153, 153 },      // 134
        {  76, 153, 153 },      // 135
        {   0, 127, 127 },      // 136
        {  63, 127, 127 },      // 137
        {   0,  76,  76 },      // 138
        {  38,  76,  76 },      // 139
        {   0, 191, 255 },      // 140
        { 127, 223, 255 },      // 141
        {   0, 153, 204 },      // 142
        { 102, 178, 204 },      // 143
        {   0, 114, 153 },      // 144
        {  76, 133, 153 },      // 145
        {   0,  95, 127 },      // 146
        {  63, 111, 127 },      // 147
        {   0,  57,  76 },      // 148
        {  38,  66,  76 },      // 149
        {   0, 127, 255 },      // 150
        { 127, 191, 255 },      // 151
        {   0, 102, 204 },      // 152
        { 102, 153, 204 },      // 153
        {   0,  76, 153 },      // 154
        {  76, 114, 153 },      // 155
        {   0,  63, 127 },      // 156
        {  63,  95, 127 },      // 157
        {   0,  38,  76 },      // 158
        {  38,  57,  76 },      // 159
        {   0,  63, 255 },      // 160
        { 127, 159, 255 },      // 161
        {   0,  51, 204 },      // 162
        { 102, 127, 204 },      // 163
        {   0,  38, 153 },      // 164
        {  76,  95, 153 },      // 165
        {   0,  31, 127 },      // 166
        {  63,  79, 127 },      // 167
        {   0,  19,  76 },      // 168
        {  38,  47,  76 },      // 169
        {   0,   0, 255 },      // 170
        { 127, 127, 255 },      // 171
        {   0,   0, 204 },      // 172
        { 102, 102, 204 },      // 173
        {   0,   0, 153 },      // 174
        {  76,  76, 153 },      // 175
        {   0,   0, 127 },      // 176
        {  63,  63, 127 },      // 177
        {   0,   0,  76 },      // 178
        {  38,  38,  76 },      // 179
        {  63,   0, 255 },      // 180
        { 159, 127, 255 },      // 181
        {  51,   0, 204 },      // 182
        { 127, 102, 204 },      // 183
        {  38,   0, 153 },      // 184
        {  95,  76, 153 },      // 185
        {  31,   0, 127 },      // 186
        {  79,  63, 127 },      // 187
        {  19,   0,  76 },      // 188
        {  47,  38,  76 },      // 189
        { 127,   0, 255 },      // 190
        { 191, 127, 255 },      // 191
        { 102,   0, 204 },      // 192
        { 153, 102, 204 },      // 193
        {  76,   0, 153 },      // 194
        { 114,  76, 153 },      // 195
        {  63,   0, 127 },      // 196
        {  95,  63, 127 },      // 197
        {  38,   0,  76 },      // 198
        {  57,  38,  76 },      // 199
        { 191,   0, 255 },      // 200
        { 223, 127, 255 },      // 201
        { 153,   0, 204 },      // 202
        { 178, 102, 204 },      // 203
        { 114,   0, 153 },      // 204
        { 133,  76, 153 },      // 205
        {  95,   0, 127 },      // 206
        { 111,  63, 127 },      // 207
        {  57,   0,  76 },      // 208
        {  66,  38,  76 },      // 209
        { 255,   0, 255 },      // 210
        { 255, 127, 255 },      // 211
        { 204,   0, 204 },      // 212
        { 204, 102, 204 },      // 213
        { 153,   0, 153 },      // 214
        { 153,  76, 153 },      // 215
        { 127,   0, 127 },      // 216
        { 127,  63, 127 },      // 217
        {  76,   0,  76 },      // 218
        {  76,  38,  76 },      // 219
        { 255,   0, 191 },      // 220
        { 255, 127, 223 },      // 221
        { 204,   0, 153 },      // 222
        { 204, 102, 178 },      // 223
        { 204, 102, 114 },      // 224
        { 153,  76, 133 },      // 225
        { 127,   0,  95 },      // 226
        { 127,  63, 111 },      // 227
        {  76,   0,  57 },      // 228
        {  76,  38,  66 },      // 229
        { 255,   0, 127 },      // 230
        { 255, 127, 191 },      // 231
        { 204,   0, 102 },      // 232
        { 204, 102, 153 },      // 233
        { 153,   0,  76 },      // 234
        { 153,  76, 114 },      // 235
        { 127,   0,  63 },      // 236
        { 127,  63,  95 },      // 237
        {  76,   0,  38 },      // 238
        {  76,  38,  57 },      // 239
        { 255,   0,  63 },      // 240
        { 255, 127, 159 },      // 241
        { 204,   0,  51 },      // 242
        { 204, 102, 127 },      // 243
        { 153,   0,  38 },      // 244
        { 153,  76,  95 },      // 245
        { 127,   0,  31 },      // 246
        { 127,  63,  79 },      // 247
        {  76,   0,  19 },      // 248
        {  76,  38,  47 },      // 249
        {  51,  51,  51 },      // 250
        {  91,  91,  91 },      // 251
        { 132, 132, 132 },      // 252
        { 173, 173, 173 },      // 253
        { 214, 214, 214 },      // 254
        };

class           DefaultDwgConversionSettings : public IDwgConversionSettings
    {
    private:
    WString             m_textStyleNameTemplate;
    WString             m_insertLayerName;
    WString             m_dictionaryWildcardFilters;
    DwgFileVersion      m_saveVersion;
public:
    DefaultDwgConversionSettings () : m_textStyleNameTemplate (L"Style-$s") , m_saveVersion (DwgFileVersion_Max) {}

    // used when opening DWGs
    virtual int                     GetCustomObjectDisplayView() const override                 { return  0; }
    virtual ObjectDisplayMode       GetCustomObjectDisplayMode() const override                 { return  CustomObjectDisplay_Default; }
    virtual ProxyShowMode           GetProxyShowMode() const override                           { return  ProxyObject_Show; }
    virtual bool                    CreateDGNMaterials() const override                         { return  true;  }
    virtual bool                    CreateDGNLights() const override                            { return  true;  }
    virtual bool                    GraphicGroupAttributes() const override                     { return  false; }
    virtual bool                    SetAxisLockFromOrthoMode() const override                   { return  false; }
    virtual bool                    OpenModelSpaceAs2d() const override                         { return  false; }
    virtual bool                    OpenPaperSpaceAs2d() const override                         { return  false; }
    virtual bool                    DiscardInvalidEntities() const override                     { return  false; }
    virtual bool                    HyperlinkAsEngineeringLink() const override                 { return  false; }
    virtual bool                    AttributesAsTags() const override                           { return  true; }
    virtual bool                    DisallowLogicalNameFromXRefBlockNames() const override      { return  false; }
    virtual bool                    PreserveSeedOrigin () const override                        { return  false;  }
    virtual bool                    OpenPDFAsVector() const override                            { return  true; }

    virtual double                  GetLineCodeScale () const override                          { return  0.0; }
    virtual double                  GetLineWeightScale () const override                        { return  0.0; }
    virtual bool                    AllowPsolidAcisInteropLogging () const override             { return  false; }
    virtual ColumnTextDropMode      GetColumnTextDropMode () const override                     { return  DropColumnText_Auto; }
    virtual bool                    GetDefaultXrefTreatAsElement () const override              { return  false; }

    virtual void                    GetSheetBackgroundColor (int& red, int& green, int& blue) const override    { red = green = blue = 255; }
    virtual void                    GetDesignBackgroundColor (int& red, int& green, int& blue) const override   { red = green = blue = 0; }

    // used for both opening and saving DWG's
    virtual bool                    MapVPortLocateLockToDisplayUnlocked() const override        { return  false; }
    virtual bool                    IgnoreXData() const override                                { return  false; }

    // used when saving to DWG
    virtual bool                    SetViewportLayerFromClipElement() const override            { return  false; }
    virtual ViewportFreezeMode      GetViewportFreezeMode () const override                     { return  VPFreeze_ViewportsAndGlobal; }
    virtual bool                    AllowLeaderHooklineToBeAdded() const override               { return  false; }
    virtual bool                    IsZeroZCoordinateEnforced() const override                  { return  false; }
    virtual bool                    CreateDWGMaterials() const override                         { return  true;  }
    virtual bool                    CreateDWGLights() const override                            { return  true;  }
    virtual bool                    CreateExtrusionsFromProjectedSolids() const override        { return  false; }
    virtual bool                    CreateSingleBlockFromDuplicateCells() const override        { return  true;  }
    virtual bool                    SaveApplicationData() const override                        { return  false; }
    virtual bool                    SaveMicroStationSettings() const override                   { return  false; }
    virtual bool                    AllowScaledBlocksFromCells() const override                 { return  true;  }
    virtual bool                    CreateOverlaysForReferenceAttachments() const override      { return  false; }
    virtual bool                    DropUnsupportedLineStyles() const override                  { return  false; }
    virtual bool                    DropUnsupportedAreaPatterns() const override                { return  true;  }
    virtual bool                    DropUnsupportedDimensions() const override                  { return  false; }
    virtual bool                    SetUCSFromCurrentACS() const override                       { return  true;  }
    virtual SaveReferencePathMode   GetSaveReferencePathMode() const override                   { return  SaveReferencePath_WhenSameDirectory; }
    virtual bool                    SaveFrontBackClip() const override                          { return  false; }
    virtual bool                    SaveSheetsToSeparateFiles() const override                  { return  false; }
    virtual bool                    CreateTrueColorFromDgnIndices() const override              { return  false; }
    virtual bool                    DisallowSaveDimensionSettings() const override              { return  false; }
    virtual bool                    DisallowBlockNameFromTriForma() const override              { return  false; }
    virtual bool                    CreateBlocksFromTriForma () const override                  { return  false; }
    virtual bool                    ForcePositiveExtrusionForClockwiseArcs() const override     { return  false; }
    virtual bool                    CreatePolylinesFromSplines() const override                 { return  false; }
    virtual bool                    CreateBlockDefinitionsOnLayer0() const override             { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockColor() const override     { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockStyle() const override     { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockWeight() const override    { return  false; }
    virtual bool                    ConvertEmptyEDFToSpace() const override                     { return  false; }

    virtual bool                    OverrideExistingLinetypeDefinitions() const override        { return  false; }
    virtual bool                    EnableDimensionRoundoff() const override                    { return  false; }
    virtual bool                    UniformlyScaleBlocks () const override                      { return  false; }
    virtual bool                    SaveBlockUnitsFromFileUnits () const override               { return  false; }

    virtual bool                    DropNestedCells() const override                            { return  false; }
    virtual bool                    DropTriformaCells() const override                          { return  false; }
    virtual bool                    AllowFarRefDependency() const override                      { return  false; }
    virtual bool                    DropLabelLines() const override                             { return  false; }
    virtual bool                    DisableV7RefClipRotation() const override                   { return  false; }
    virtual bool                    SaveRasterToSharedCell() const override                     { return  false; } 
    virtual bool                    CopyRasterToOutputFolder() const override                   { return  false; } 
    virtual bool                    IsImageFileFormatSupported (WCharCP pi_schemeTypeName, DgnPlatform::ImageFileFormat  imageFileType) const override {return true;} 

    virtual bool                    UseLevelSymbologyOverrides () const override                { return  false; } 
    virtual bool                    ConvertReferences () const override                         { return  false; }
    virtual int                     GetLevelDisplayView () const override                       { return  0; }

    virtual UInt32                  GetMaxOrphanTagsPerSet() const override                     { return  MAX_OrphanTags_PerSet; }
    virtual UInt32                  GetMaxDictionaryItems() const override                      { return  MAX_DGN_EXPORT_ITEMS; }
    virtual WStringCR               GetDictionaryWildcardFilters() const override               { return  m_dictionaryWildcardFilters; }


    virtual ConstructionMapping     GetConstructionClassMapping() const override                { return Construction_Layer; }
    virtual PatternMapping          GetPatternClassMapping() const override                     { return Pattern_Ignore; }
    virtual LinearPatternedMapping  GetLinearPatternedClassMapping() const override             { return LinearPatterned_Omit; }

    virtual double                  GetPolyfaceAngleTolerance () const override                 { return 0.5; }


    virtual LineStringMapping       GetLineStringMapping (bool planar) const override
        {
        if (planar)
            return LineString_LWPolyline;
        else
            return LineString_3DPolyline;
        }

    virtual SolidSurfaceMapping     GetSolidSurfaceMapping (bool isFlatSurfaceOrSolid) const override
        {
        if (isFlatSurfaceOrSolid)
            return SolidSurface_Acis;
        else
            return SolidSurface_Polyface;
        }

    virtual ClosedMapping           GetComplexShapeMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Region : Closed_Polyline);
        }

    virtual ClosedMapping           GetTriOrQuadMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_SolidOrFace;
        else
            return (threeD ? Closed_SolidOrFace : Closed_Polyline);
        }
    virtual ClosedMapping           GetPolygonMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Polyline : Closed_Polyline);
        }
    virtual ClosedMapping           GetGroupedHoleMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Region: Closed_Region);
        }

    virtual DwgSaveUnitMode         GetDwgSaveUnitMode () const override    { return DWGSaveUnitMode_SeedFileMasterUnits; }
    virtual int                     GetDxfSavePrecision () const override   { return 6; }

    virtual DwgFileVersion          GetDwgSaveVersion () const override     { return m_saveVersion; }
    virtual void                    SetDwgSaveVersion (DwgFileVersion newVersion) override  { m_saveVersion = newVersion; }

    virtual int                     GetDwgWeightFromDgnWeight (int dgnWeight) const override
        {
        if (dgnWeight < 0)
            return 0;
        else if (dgnWeight > MAX_LINEWEIGHTS)
            return s_standardDWGWeights[_countof (s_standardDWGWeights)-1];

        double  dgnWidthInMM = dgnWeight * DEFAULT_LINEWEIGHT_SCALE;
        return GetDwgWeightFromWidthInMM (dgnWidthInMM);
        }

    virtual int                     GetDwgWeightFromWidthInMM (double dgnWidthInMM) const override
        {
        return  s_standardDWGWeights[StandardIndexFromDwgWeight (dgnWidthInMM * LINEWEIGHT_PER_MM)];
        }

    virtual bool                    GetDwgSeedFile (WStringR seedFileName) const override
        {
        return false;
        }

    virtual bool                    GetDgnSeedFile (WStringR seedFileName) const override
        {
        return false;
        }

    virtual bool                    GetDwgShapeFilePath (WStringR shapeFilePath) const override
        {
        // don't convert fonts.
        return false;
        }

    virtual bool                    GetDefaultDwgLineWeight (int& value) const override
        {
        // don't set it.
        return false;
        }
        
    virtual WStringCR               GetTextStyleNameTemplate () const override
        {
        return m_textStyleNameTemplate;
        }

    virtual WStringCR               GetInsertLayerName () const override
        {
        return m_insertLayerName;
        }

    virtual DwgOpenUnitMode         GetDwgOpenUnitMode (WCharCP fileName, DwgLinearUnits currentLinearUnits, StandardUnit currentDesignCenterUnits, DwgFileVersion version) const override
        {
        // Note: This implementation never returns any of the DwgOpenUnitMode enumerations - it always returns an Standard Unit.
        //       Other implementations may return either DWGOpenUnitMode_SeedFileMasterUnits or DWGOpenUnitMode_SeedFileSubUnits.
        
        // if lunits are Engineering or Architectural, use Inches.
        if ( (DWGLinearUnit_Engineering == currentLinearUnits) || (DWGLinearUnit_Architectural == currentLinearUnits) )
            return static_cast <DwgOpenUnitMode> (StandardUnit::EnglishInches);
        else
            {
            // see if the design center units are meaningful.
            if (StandardUnit::None != currentDesignCenterUnits)
                return static_cast <DwgOpenUnitMode> (currentDesignCenterUnits);
            else
                return static_cast <DwgOpenUnitMode> (StandardUnit::MetricMeters);
            }
        }

    virtual int                     GetDgnWeightFromDwgWeight (int dwgWeight) const override
        {
        int         dgnWeight = (int) (0.5 + ((double) dwgWeight / (LINEWEIGHT_PER_MM * DEFAULT_LINEWEIGHT_SCALE)));
        return dgnWeight >= MAX_LINEWEIGHTS ? MAX_LINEWEIGHTS-1 : dgnWeight;
        }

    virtual int GetDwgFileCodePage () const override
        {
        return 1252;
        }

    virtual DwgSaveNonDefaultModelMode  GetNonDefaultModelMode () const override
        {
        return NonDefaultModels_SeparateFiles;
        }

    virtual void    GetDisplayColorTable (byte colorTable[768], DwgColorTableQuery queryMode) const override
        {
        // default R2004 color table
        memcpy (colorTable, s_dwgColors, 768);
        }

    virtual bool    GetMergeVisibleEdgeSettings (HLineSettings& outSettings) const override
        {
        return  false;
        }

    int     StandardIndexFromDwgWeight (double dwgWeightInMM) const
        {
        // AutoCAD only understands lineweights with certain values...
        for (int index=1; index < _countof (s_standardDWGWeights); index++)
            {
            if (s_standardDWGWeights[index] > dwgWeightInMM)
                {
                double      low = s_standardDWGWeights[index-1], high = s_standardDWGWeights[index];

                return  ((dwgWeightInMM - low) < (high - dwgWeightInMM) ? index-1 : index);
                }
            }
        return _countof (s_standardDWGWeights)-1;
        }

    };  // DefaultDwgConversionSettings

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
IDwgConversionSettings*         ConvertContext::GetDefaultConversionSettings ()
    {
    return new DefaultDwgConversionSettings();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BarryBentley      01/2000
* These methods are not inlined so we can export them.
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertContext::GetThreeD () const                      { return m_threeD; }
DgnModelP                   ConvertContext::GetModel () const                       { return m_model; }
DgnFileP                    ConvertContext::GetFile() const                         { return m_dgnFile; }
TransformCR                 ConvertContext::GetTransformToDGN () const              { return m_transformToDGN; }
TransformCR                 ConvertContext::GetTransformFromDGN () const            { return m_transformFromDGN; }
TransformCR                 ConvertContext::GetTransformToModelFromDGN () const     { return m_modelTransformFromDGN; }
TransformCR                 ConvertContext::GetTransformFromModelToDGN () const     { return m_modelTransformToDGN; }
TransformCR                 ConvertContext::GetLocalTransform () const              { return m_localTransform; }
double                      ConvertContext::GetStorageUnitSolidExtent () const      { return m_storageUnitSolidExtent; }
FileHolder&                 ConvertContext::GetFileHolder() const                   { return *m_pFileHolder; }
AcDbObjectId                ConvertContext::GetCurrentBlockId () const              { return m_currentBlockId; }
void                        ConvertContext::SetCurrentBlockId (AcDbObjectId currentBlockId) { m_currentBlockId = currentBlockId; }
DgnPlatform::StandardUnit   ConvertContext::GetStandardTargetUnits () const         { return m_standardTargetUnits; }
DgnPlatform::UnitDefinition ConvertContext::GetTargetUnits () const                 { return m_targetUnit; }
IDwgConversionSettings&     ConvertContext::GetSettings () const                    { return m_dwgSettings; }
AcDbDatabaseP               ConvertContext::GetDatabase() const                     { return m_pFileHolder->GetDatabase(); }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertContext::ConvertContext (IDwgConversionSettings& conversionSettings) : m_dwgSettings (conversionSettings) {}
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertContext::~ConvertContext ()
    {
    mdlAvlTree_free (&m_pPostProcessIdTree, NULLFUNC, NULL);

    if (NULL != m_annotationScale1vs1)
        {
        delete m_annotationScale1vs1;
        m_annotationScale1vs1 = NULL;
        }

    if (!m_acisOutputFilePath.empty() && !::DeleteFileW(m_acisOutputFilePath.c_str()))
        {
#ifdef REALDWG_DIAGNOSTICS
        if (BeFileName::DoesPathExist(m_acisOutputFilePath.c_str()))
            printf ("Failed deleting the temporary ACISout file %ls\n", m_acisOutputFilePath.c_str());
#endif
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbAnnotationScale*     ExtractAnnotationScale1vs1 (AcDbDatabase* database)
    {
    AcDbAnnotationScale*        annoScale1vs1 = NULL;
    AcDbObjectContextManager*   contextManager = database->objectContextManager ();

    if (NULL != contextManager)
        {
        AcDbObjectContextCollection* const  contextCollection = contextManager->contextCollection (ACDB_ANNOTATIONSCALES_COLLECTION);

        if (NULL != contextCollection)
            {
            AcDbObjectContextCollectionIterator*    pIter = contextCollection->newIterator ();

            // find 1:1 - should be the first entry.
            for (pIter->start(); !pIter->done(); pIter->next())
                {
                AcDbObjectContext   *objectContext = NULL;

                if (Acad::eOk == pIter->getContext(objectContext))
                    {
                    AcDbAnnotationScale*    annoScale = (AcDbAnnotationScale*)objectContext;
                    double                  scale = 0;
                    if (NULL != annoScale && Acad::eOk == annoScale->getScale(scale) && fabs(scale - 1.0) < TOLERANCE_ZeroScale)
                        {
                        annoScale1vs1 = annoScale;
                        break;
                        }

                    delete objectContext;
                    }
                }
            }
        }

    return  annoScale1vs1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertContext::Initialize
(
DgnModelP                   model,
FileHolderP                 pFileHolder,
RealDwgModelIndexItemP      pModelIndexItem,
DwgOpenUnitMode             unitMode
)
    {
    m_model             = model;
    m_threeD            = NULL == model ? false : model->Is3d();
    m_pFileHolder       = pFileHolder;
    m_nextViewIndex     = 0;
    m_pModelIndexItem   = pModelIndexItem;
    m_localTransform.initIdentity ();
    m_dgnFile           = model->GetDgnFileP();

    assert (NULL != m_dgnFile);

    if (NULL != m_pModelIndexItem)
        m_currentBlockId    = m_pModelIndexItem->GetBlockTableRecordId();
    else
        m_currentBlockId.setNull();

    m_microStationDictionaryId.setNull ();

    switch (this->GetSettings().GetCustomObjectDisplayMode())
        {
        case CustomObjectDisplay_Standard:         m_worldDrawRegenType = kAcGiStandardDisplay;        break;
        case CustomObjectDisplay_Rendered:         m_worldDrawRegenType = kAcGiShadedDisplay;          break;
        case CustomObjectDisplay_Hidden:           m_worldDrawRegenType = kAcGiHideOrShadeCommand;     break;
        case CustomObjectDisplay_AsProxyGraphics:  m_worldDrawRegenType = kAcGiSaveWorldDrawForProxy;  break;
        // default to invalid regeneration type such that it will be set from active viewport:
        case CustomObjectDisplay_Default:
        default:
            m_worldDrawRegenType = eAcGiRegenTypeInvalid;
        }

    this->InitializeDgnTransforms (m_model, unitMode);

    m_pPostProcessIdTree = mdlAvlTree_init (AVLKEY_ELEMENTID);

    if (!DwgPlatformHost::Instance()._GetDwgForgroundColor255Name(m_specialDwgColorBookName, m_dwgForegroundColor255Name))
        {
        m_specialDwgColorBookName.clear ();
        m_dwgForegroundColor255Name.clear ();
        }

    // temporary ACIS output file name:
    WChar       tmpDir[MAX_PATH] = { 0 };
    int         nChars = ::GetTempPathW (MAX_PATH, tmpDir);  
    BeAssert (nChars > 0);

    m_acisOutputFilePath.empty ();
    if (nChars > 0)
        {
        WChar   tmpFile[MAX_PATH] = { 0 };
        if (wsprintf(tmpFile, L"$acis$out%d", ::GetCurrentProcessId()) < 1)
            wcscpy (tmpFile, L"$acis$out$tmp");

        m_acisOutputFilePath = WString(tmpDir) + WString(tmpFile) + WString(L".sat");
        }

    // allow ACIS conversion only if ACIS InterOp exists:
    m_canConvertAcis = NULL != GetModuleHandle(L"SPAXInterop.dll") || BSISUCCESS == util_findFile(NULL, NULL, L"SPAXInterop.dll", L"MS_LIBRARY_PATH", NULL, 0);

    // Try to copy annotation scale 1:1 context:
    m_annotationScale1vs1 = ExtractAnnotationScale1vs1 (pFileHolder->GetDatabase());

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  01/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertContext::InitializeDgnTransforms (DgnModelP modelRef, DwgOpenUnitMode unitMode)
    {
    ModelInfoCR     modelInfo = modelRef->GetModelInfo();

    GetTransformToDGNFromModelInfo (&m_transformToDGN, NULL, &m_targetUnit, modelInfo, m_model, unitMode);
    m_standardTargetUnits = m_targetUnit.IsStandardUnit();
    m_masterUnit = modelInfo.m_masterUnit;

    if (!m_masterUnit.IsValid())
        m_masterUnit.GetStandardUnit (StandardUnit::MetricMeters);

    m_storageUnitSolidExtent = 1000.0 * fabs (modelInfo.m_uorPerStorage);
    m_transformFromDGN.inverseOf (&m_transformToDGN);

    // Save the original transforms.
    m_modelTransformFromDGN = m_transformFromDGN;
    m_modelTransformToDGN   = m_transformToDGN;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertContext::ElementIdFromObject (AcDbObjectCP acObject) const
    {
    // when converting from DWG to DGN, ElementId == AcDbHandle
    AcDbHandle  dbHandle;
    acObject->getAcDbHandle (dbHandle);
    return RealDwgUtil::CastDBHandle (dbHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertContext::ElementIdFromObjectId (const AcDbObjectId& objectId) const
    {
    // when converting from DWG to DGN, ElementId == AcDbHandle
    return RealDwgUtil::CastDBHandle (objectId.handle());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertContext::ElementIdFromDBHandle (const AcDbHandle& dbHandle) const
    {
    // when converting from DWG to DGN, ElementId == AcDbHandle
    return RealDwgUtil::CastDBHandle (dbHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbHandle                  ConvertContext::DBHandleFromElementId (ElementId elementId) const
    {
    // when converting from DWG to DGN, ElementId == AcDbHandle
    return AcDbHandle (elementId);
    }

/*---------------------------------------------------------------------------------**//**
* Internal to MicroStation DGN we store the breps as a scaled down version of the
* body in master units.  The scale depends on three factors, the subpermas setting,
* the uorpersub setting and finally the actual brep scaling.  While we can get
* the first two from the elementContext->transformToDgn() call, the third factor
* can be obtained form the kernel through APIs.  Use these three to create a
* transform and return it
*
* @param        elementContext  => input element context
* @param        invert          => invert or not
* @param        pTransform      <= output transform
* @return       pTransform      <= output transform
* @bsimethod    DwgAcisData                             VenkatKalyan    09/00
+---------------+---------------+---------------+---------------+---------------+------*/
Transform*                  ConvertContext::GetTransformForACISBodies
(
bool                        invert,
Transform*                  pTransform
)
    {
    Transform       transform = this->GetTransformToModelFromDGN();

    // Get the kernel scale
    double                  kernelScale = dgnModel_getEffectiveSolidScale (m_model);
    RotMatrix               scaleMatrix;

    // KernelScale is from Kernel Units to DGN.  To Get Kernel to Model Multiply (Dgn to Model X Kernel to DGN).
    scaleMatrix.initFromScaleFactors (kernelScale, kernelScale, kernelScale);
    transform.productOf (&transform, &scaleMatrix);

    if (invert)
        transform.inverseOf(&transform);

    *pTransform = transform;
    return  pTransform;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                ReadOutIsMasterSubUnits
(
DgnModelP       modelRef
)
    {
    if (nullptr == modelRef)
        return  false;

    ModelInfoCR modelInfo = modelRef->GetDgnModelP()->GetModelInfo();

    return DgnUnitFormat::MUSU == modelInfo.GetLinearUnitMode();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  07/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertContext::GetTransformToDGNFromModelInfo
(
TransformP                  pTransformToDGN,        // <= transform
double*                     pScaleToDGN,            // <= scale to DGN.
UnitDefinitionP             pTargetUnits,           // <= target units.
ModelInfoCR                 modelInfo,              // => model Info.
DgnModelP                   modelRef,
DwgOpenUnitMode             unitMode
)
    {
    UnitDefinition      targetUnits;
    if (NULL == pTargetUnits)
        pTargetUnits = &targetUnits;

    // Special case for Feet-Inches Models.  Always use Architectural/Engineering (inches) For these.
    if ( ((unitMode == DWGOpenUnitMode_SeedFileMasterUnits) || (unitMode == DWGOpenUnitMode_SeedFileSubUnits)) &&
        ReadOutIsMasterSubUnits (modelRef) &&
        (modelInfo.m_masterUnit.IsStandardUnit() == StandardUnit::EnglishFeet) && (modelInfo.m_subUnit.IsStandardUnit() == StandardUnit::EnglishInches) )
        {
        *pTargetUnits = modelInfo.m_subUnit;
        }
    else
        {
        if (DWGOpenUnitMode_SeedFileMasterUnits == unitMode)
            *pTargetUnits = modelInfo.m_masterUnit;

        else if (DWGOpenUnitMode_SeedFileSubUnits == unitMode)
            *pTargetUnits = modelInfo.m_subUnit;

        else if (DWGOpenUnitMode_LegacyDwgAttachment == unitMode)
            *pTargetUnits = modelInfo.m_storageUnit;

        else if (DWGOpenUnitMode_StorageUnits == unitMode)      // this value is passed in when initializing ConverToDgnContext.
            *pTargetUnits = modelInfo.m_storageUnit;

        else
            {
            *pTargetUnits = UnitDefinition::GetStandardUnit (static_cast <StandardUnit> (unitMode));

            if (!pTargetUnits->IsValid())
                {
                // -1 denotes "not yet set", see a valid case called from SetAcDbViewportFromSelfReference
                *pTargetUnits = modelInfo.m_storageUnit;
                }
            }
        }

    double      uorPerTarget = fabs (modelInfo.m_uorPerStorage);
    if (!pTargetUnits->IsEqual (modelInfo.m_storageUnit))
        {
        double conversionFactor;
        modelInfo.m_storageUnit.GetConversionFactorFrom (conversionFactor, *pTargetUnits);
        uorPerTarget *= conversionFactor;
        }

    if (NULL != pScaleToDGN)
        *pScaleToDGN = uorPerTarget;

    RotMatrix       rotationToDGN;
    rotationToDGN.initFromScaleFactors (uorPerTarget, uorPerTarget, uorPerTarget);

    DPoint3d        translationToDGN;
    translationToDGN.init (modelInfo.m_globalOrigin.x, modelInfo.m_globalOrigin.y, modelInfo.m_flags.modelIs3D ? modelInfo.m_globalOrigin.z : 0.0);

    if (NULL != pTransformToDGN)
        pTransformToDGN->initFrom (&rotationToDGN, &translationToDGN);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
double                      ConvertContext::GetScaleToDGN () const
    {
    DPoint3d  column;
    m_transformToDGN.getMatrixColumn (&column, 0);
    return column.magnitude();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
double                      ConvertContext::GetScaleFromDGN () const
    {
    double      scaleToDGN;

    return 0.0 == (scaleToDGN = this->GetScaleToDGN()) ? 1.0 : 1.0 / scaleToDGN;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  01/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertContext::PushTransform
(
TransformP                  pCurrentToDGN,
TransformP                  pCurrentFromDGN,
TransformP                  pCurrentLocalTransform,
TransformCP                 pPushTransform
)
    {
    Transform               inverse;

    *pCurrentToDGN   = m_transformToDGN;
    *pCurrentFromDGN = m_transformFromDGN;
    *pCurrentLocalTransform = m_localTransform;

    m_localTransform.productOf (pPushTransform, &m_localTransform);
    m_transformFromDGN.productOf (pPushTransform, &m_transformFromDGN);

    inverse.inverseOf (pPushTransform);
    m_transformToDGN.productOf (&m_transformToDGN, &inverse);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  01/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertContext::ClearTransformTranslation
(
TransformP                  pCurrentToDGN,
TransformP                  pCurrentFromDGN,
TransformP                  pCurrentLocalTransform
)
    {
    *pCurrentToDGN   = m_transformToDGN;
    *pCurrentFromDGN = m_transformFromDGN;
    *pCurrentLocalTransform = m_localTransform;

    DPoint3d zero;
    zero.zero();
    m_localTransform.setTranslation (&zero);
    m_transformFromDGN.setTranslation (&zero);
    m_transformToDGN.setTranslation (&zero);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  01/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertContext::PopTransform
(
TransformP                  pTransformToDGN,
TransformP                  pTransformFromDGN,
TransformP                  pLocalTransform
)
    {
    m_transformToDGN   = *pTransformToDGN;
    m_transformFromDGN = *pTransformFromDGN;
    m_localTransform   = *pLocalTransform;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
double                      ConvertContext::GetScaleFromMasterUnits () const
    {
    double  conversionFactor;
    m_targetUnit.GetConversionFactorFrom (conversionFactor, m_masterUnit);
    return conversionFactor;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
LangCodePage                    ConvertContext::GetAnsiCodePage () const
    {
    /*-----------------------------------------------------------------------------------
    RealDWG does not allow access to DWGCODEPAGE stored in file header.  This may not be
    a problem in DWG files of R2007 and later which is Unicode based.  The MIF in mtext
    can be handled locally.  For an earlier version of DWG, this issue is supposedly
    taken care of by AcDbDatabase::readDwgFile with the codepage conversion option. As a
    result we always get Unicoded strings.

    A problem is with writing a DWG file to R2004 or an earlier version.  Now we cannot
    set this file based codepage anymore.  When we are in a OS locale that is differnt
    than the one in the file we are writing to, we end up with a wrong DWGCODEPAGE that
    may cause R2004 or other applications to incorrect use the codepage.
    -----------------------------------------------------------------------------------*/
    return static_cast<LangCodePage>(RealDwgXDataUtil::GetAnsiCodePageFromDwgCodePage (RealDwgHostApp::Instance().getSystemCodePage()));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertContext::GetMicroStationDictionaryId (bool createIfNotFound)
    {
    if (m_microStationDictionaryId.isValid())
        return  m_microStationDictionaryId;

    AcDbDictionaryPointer   pMainDictionary (m_pFileHolder->GetDatabase()->namedObjectsDictionaryId(), createIfNotFound ? AcDb::kForWrite : AcDb::kForRead);
    if (Acad::eOk != pMainDictionary.openStatus())
        return  AcDbObjectId::kNull;

    if (Acad::eOk == pMainDictionary->getAt (StringConstants::MainDictionaryItem_MicroStation, m_microStationDictionaryId))
        return  m_microStationDictionaryId;

    // the MicroStation dictionary does not exist - create a new one per request
    if (createIfNotFound)
        {
        AcDbDictionary*     pNewDictionary = new AcDbDictionary();

        if (Acad::eOk == pMainDictionary->setAt (StringConstants::MainDictionaryItem_MicroStation, pNewDictionary, m_microStationDictionaryId))
            {
            pNewDictionary->close ();
            return  m_microStationDictionaryId;
            }

        delete pNewDictionary;
        }

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static DisplayPathCP         DisplayPathFromSubEntPath
(
AcDbFullSubentPath&         subEntPath,
ConvertContext&             context
)
    {
    const AcDbObjectIdArray&  objectIds = subEntPath.objectIds();

    if (1 == objectIds.length())
        {
        DgnModelP       model = context.GetModel ();
        ElementRefP     elementRef;

        if (NULL != model && NULL != (elementRef = model->FindByElementId(context.ElementIdFromObjectId(objectIds[0]))))
            {
            DisplayPathP    path = new DisplayPath (elementRef, context.GetModel());

            path->AddRef ();

            return  path;
            }
        }
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            FindClosestIntersectionIndex
(
int*                        pCloseIndex,
ElementHandleCR             eh1,
ElementHandleCR             eh2,
DPoint3dCP                  pClosePoint
)
    {
    DPoint3dArray       point1;
    DPoint3dArray       point2;
    if (SUCCESS == ElementUtil::GetIntersections (&point1, &point2, eh1, eh2, NULL, NULL, true) && point1.size() > 0)
        {
        double      minDistance = DBL_MAX;

        for (size_t iIntersect = 0; iIntersect < point1.size (); iIntersect++)
            {
            double  thisDistance;
            if ((thisDistance = pClosePoint->Distance (point1[iIntersect])) < minDistance)
                {
                minDistance = thisDistance;
                *pCloseIndex = (int)iIntersect;
                }
            }
        return SUCCESS;
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertContext::AssocPointFromSnapPointRef
(
AssocPoint*                 pAssocPoint,
AcDbPointRef const&         pointRef,
int                         pointIndex,
ElementHandleCR             dimElement
)
    {
    StatusInt               status          = BSIERROR;
    DgnModelP               modelRef = this->GetModel();
    AcDbFullSubentPath      mainEntity, intersectEntity;

    AcDbOsnapPointRef const&    snapPointRef = static_cast <AcDbOsnapPointRef const&> (pointRef);
    if (Acad::eOk != snapPointRef.getIdPath(mainEntity) || Acad::eOk != snapPointRef.getIntIdPath(intersectEntity))
        return  BSIERROR;

    Adesk::GsMarker         gsMarker        = mainEntity.subentId().index ();

#if defined (_WIN64)
    // Note Adesk::GsMarker is typedef'ed to IntPtr. We don't think it will ever go beyond 32 bits, but here's where we need to find out.
    if (gsMarker & 0xffffffff00000000)
        DIAGNOSTIC_PRINTF ("Unexpected subentId=%I64d\n", gsMarker);
#endif
    int                     subEntIndex = (int) gsMarker;
    if (subEntIndex < 0 || subEntIndex > 0xFF)
        subEntIndex = 0;

    // get first snap element
    DisplayPathCP           pDisplayPath = DisplayPathFromSubEntPath (mainEntity, *this);
    if (NULL == pDisplayPath)
        return  status;
    EditElementHandle       cursorEeh (pDisplayPath->GetCursorElem(), modelRef);
    if (!cursorEeh.IsValid())
        {
        delete pDisplayPath;
        return  status;
        }

    // get 2nd snap element if exists
    DisplayPathCP           pDisplayPath2 = NULL;
    EditElementHandle       cursorEeh2;
    if (snapPointRef.osnapType() == AcDbPointRef::kOsnapInt)
        {
        pDisplayPath2 = DisplayPathFromSubEntPath (intersectEntity, *this);
        if (NULL != pDisplayPath2)
            cursorEeh2.SetElementRef (pDisplayPath2->GetCursorElem(), modelRef);
        }

    if (snapPointRef.osnapType() != AcDbPointRef::kOsnapInt || cursorEeh2.IsValid())
        {
        CurveVectorPtr      curveVector = ICurvePathQuery::ElementToCurveVector (cursorEeh);
        if (!curveVector.IsValid())
            return  status;

        int                 intersectIndex = 0;
        DPoint3d            center, closePoint;
        double              start, sweep, r1, r2;
        RotMatrix           rMatrix;

        double              oSnapParam = snapPointRef.nearPointParam ();

        DPoint3dArray       points;
        MSBsplineCurveCP    curve = NULL;

        if (RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(points, *curveVector))
            {
            int             nPoints = (int)points.size ();

            if (subEntIndex > 0)
                subEntIndex--;

            switch (snapPointRef.osnapType())
                {
                case AcDbPointRef::kOsnapStart:
                    AssociativePoint::InitKeypoint (*pAssocPoint, subEntIndex, nPoints, 0, 1);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapEnd:
                    AssociativePoint::InitKeypoint (*pAssocPoint, subEntIndex, nPoints, 1, 1);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapMid:
                    AssociativePoint::InitKeypoint (*pAssocPoint, subEntIndex, nPoints, 1, 2);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapNear:
                    if (subEntIndex < nPoints)
                        {
                        DPoint3d        locatePoint;
                        locatePoint.interpolate (&points[subEntIndex], oSnapParam, &points[subEntIndex+1]);

                        ElementHandle   eh(pDisplayPath->GetPathElem(-1), modelRef);
                        if (!eh.IsValid())
                            break;

                        DPoint3d        testPoint = locatePoint;
                        Transform       pathTransform;
                        if (SUCCESS == assoc_getPathTransformToParent (&pathTransform, pDisplayPath, modelRef))
                            pathTransform.MultiplyTranspose (testPoint);

                        /*--------------------------------------------------------------------------------------
                        If the locate point is too far away from the expected dim def point, AssocPoint will make
                        an attempt to correct it after the file is opened, setting off a chain reaction: the dependency 
                        manager will remap assoc ID's.  The model will thus be set dirty.  A dirty model will not 
                        be emptied on a ref file reload (see DgnModel::Empty).  From there on, each time a 
                        DgnFile::Reload is called, DWG elements get appended into the model that still has the 
                        original elements remained.  It appears that not forcing ref file reloading to be done by 
                        design to prevent intentional changes from being thrown away on a ref file reload.  So we 
                        try to attach the root problem by fixing the snap bad point as a case in TFS 95151.  If 
                        the dim def point falls between the two assoc ref points, replace the locate point with 
                        the dim point; otherwise simply reject the bad point.
                        --------------------------------------------------------------------------------------*/
                        MSElementCP     elem = dimElement.GetElementCP ();
                        if (nullptr != elem)
                            {
                            double      tolerance = elem->dim.geom.witOffset;
                            DPoint3d    defPoint = elem->dim.GetPoint (pointIndex);
                            if (!testPoint.IsEqual(defPoint, tolerance))
                                {
                                // if the dimension def point falls beteen the two points, replace locate point with the def point:
                                if (defPoint.IsVectorInSmallerSector(points[subEntIndex], points[subEntIndex+1]))
                                    testPoint = defPoint;
                                else
                                    break;  // reject the bad point
                                }
                            }

                        double          ratio = 0.0;
                        DVec3d          segDir = DVec3d::FromStartEnd (points[subEntIndex], points[subEntIndex+1]);
                        double          segDist = segDir.Normalize ();
                        if (segDist > TOLERANCE_Relative)
                            {
                            DVec3d      locVec = DVec3d::FromStartEnd (points[subEntIndex], testPoint);
                            double      pointDist = segDir.DotProduct (locVec);

                            ratio = pointDist / segDist;
                            }

                        if (MULTILINE_ELM == eh.GetElementType ())
                            AssociativePoint::InitMline (*pAssocPoint, subEntIndex, nPoints, 0, ratio, false);
                        else
                            AssociativePoint::InitProjection (*pAssocPoint, subEntIndex, nPoints, ratio);

                        status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);

                        break;
                        }

                case AcDbPointRef::kOsnapInt:
                    {
                    /*--------------------------------------------------------------------------------
                    From RealDWG doc - AcDbSubentId::index() returns a subentity index. There is no rule 
                    as to how this index value must be determined, but it is often the graphics system 
                    marker for the subentity that the AcDbSubentId object is identifying.
                    --------------------------------------------------------------------------------*/
                    Adesk::GsMarker     intersectGsMarker = intersectEntity.subentId().index();
#if defined (_WIN64)
                    // Note Adesk::GsMarker is typedef'ed to IntPtr. We don't think it will ever go beyond 32 bits, but here's where we need to find out.
                    if (intersectGsMarker & 0xffffffff00000000)
                        DIAGNOSTIC_PRINTF ("Unexpected subentId=%I64d\n", gsMarker);
#endif
                    int             intersectSubEntIndex = (int) intersectGsMarker;
                    int             nPoints2 = LineStringUtil::GetApparentCount (cursorEeh2);

                    if (nPoints2 > 0)
                        {
                        if (intersectSubEntIndex < 0 || intersectSubEntIndex > 0xFF || intersectSubEntIndex > nPoints2)
                            intersectSubEntIndex = 0;
                        else if (intersectSubEntIndex > 0)
                            intersectSubEntIndex--;

                        intersectIndex = 0;
                        }
                    else
                        {
                        closePoint.interpolate (&points[subEntIndex], oSnapParam, &points[subEntIndex+1]);

                        if (SUCCESS != FindClosestIntersectionIndex (&intersectIndex, cursorEeh, cursorEeh2, &closePoint))
                            DIAGNOSTIC_PRINTF ("Error finding intersection index for a snap point!\n");
                        }
                    // the segment index must be smaller than the vertex index - TFS 883379, 1010378
                    if (intersectSubEntIndex > (nPoints2 - 2))
                        intersectSubEntIndex = nPoints2 - 2;
                    if (subEntIndex > (nPoints - 2))
                        subEntIndex = nPoints - 2;

                    AssociativePoint::InitIntersection (*pAssocPoint, intersectIndex, subEntIndex, intersectSubEntIndex, nPoints, nPoints2);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath2, modelRef, true, 1);
                    }
                }
            }
        else if (SUCCESS == ArcHandler::Extract(NULL, NULL, &start, &sweep, &r1, &r2, &rMatrix, NULL, &center, cursorEeh))
            {
            switch (snapPointRef.osnapType())
                {
                case AcDbPointRef::kOsnapStart:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_START, start);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapEnd:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_END, start + sweep);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapMid:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_ANGLE, start + sweep/2);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapCen:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_CENTER, 0.0);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapQuad:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_ANGLE, oSnapParam);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapNear:
                    AssociativePoint::InitArc (*pAssocPoint, AssociativePoint::ARC_ANGLE, oSnapParam);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapInt:
                    {
                    Adesk::GsMarker     intersectGsMarker = intersectEntity.subentId().index();
#if defined (_WIN64)
                    // Note Adesk::GsMarker is typedef'ed to IntPtr. We don't think it will ever go beyond 32 bits, but here's where we need to find out.
                    if (intersectGsMarker & 0xffffffff00000000)
                        DIAGNOSTIC_PRINTF ("Unexpected subentId=%I64d\n", gsMarker);
#endif
                    int             intersectSubEntIndex = (int) intersectGsMarker;
                    DEllipse3d      dEllipse;
                    int             nPoints2 = LineStringUtil::GetApparentCount (cursorEeh2);

                    dEllipse.initFromScaledRotMatrix (&center, &rMatrix, r1, r2, start, sweep);
                    dEllipse.evaluate (&closePoint, NULL, NULL, oSnapParam);

                    if (intersectSubEntIndex > 0 && nPoints2 > 0)
                        intersectSubEntIndex--;
                    else if (intersectSubEntIndex < 0)
                        intersectSubEntIndex = 0;

                    if (SUCCESS == FindClosestIntersectionIndex (&intersectIndex, cursorEeh, cursorEeh2, &closePoint))
                        {
                        AssociativePoint::InitIntersection (*pAssocPoint, intersectIndex, 0, intersectSubEntIndex, 0, nPoints2);
                        status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                        status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath2, modelRef, true, 1);
                        }

                    break;
                    }
                }
            }
        else if (NULL != (curve = curveVector->front()->GetBsplineCurveCP()))
            {
            size_t  order = curve->GetOrder ();
            int     numKnots = (int)(curve->IsClosed() ? curve->params.numPoles + 2*order - 1 : curve->params.numPoles + order);

            switch (snapPointRef.osnapType())
                {
                case AcDbPointRef::kOsnapStart:
                    AssociativePoint::InitBCurve (*pAssocPoint, curve->GetKnot(order - 1));
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapEnd:
                    AssociativePoint::InitBCurve (*pAssocPoint, curve->GetKnot(numKnots - order));
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapMid:
                    AssociativePoint::InitBCurve (*pAssocPoint, (curve->GetKnot(order - 1) + curve->GetKnot(numKnots - order)) / 2.0);
                    status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                    break;

                case AcDbPointRef::kOsnapInt:
                case AcDbPointRef::kOsnapNear:
                    {
                    double      u, actualLength;
                    double      arcLen = oSnapParam * this->GetScaleToDGN();

                    // AutoDumbasses store length, not parameter for interpolated splines....
                    if (BSPLINE_CONSTRUCTION_INTERPOLATION == cursorEeh.GetElementCP()->bspline_curve.flags.construct_type)
                        {
                        curve->FractionAtSignedDistance (0.0, arcLen, u, actualLength);
                        curve->FractionToPoint (closePoint, u);
                        }
                    else
                        u = oSnapParam;

                    if (AcDbPointRef::kOsnapNear == snapPointRef.osnapType())
                        {
                        AssociativePoint::InitBCurve (*pAssocPoint, u);
                        status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                        }
                    else
                        {
                        Adesk::GsMarker     intersectGsMarker = intersectEntity.subentId().index();
#if defined (_WIN64)
                        // Note Adesk::GsMarker is typedef'ed to IntPtr. We don't think it will ever go beyond 32 bits, but here's where we need to find out.
                        if (intersectGsMarker & 0xffffffff00000000)
                            DIAGNOSTIC_PRINTF ("Unexpected subentId=%I64d\n", gsMarker);
#endif
                        int                 intersectSubEntIndex = (int) intersectGsMarker;
                        int                 nPoints2 = LineStringUtil::GetApparentCount (cursorEeh2);

                        if (intersectSubEntIndex > 0 && nPoints2 > 0)
                            intersectSubEntIndex--;
                        else if (intersectSubEntIndex < 0)
                            intersectSubEntIndex = 0;

                        bspcurv_evaluateCurvePoint (&closePoint, NULL, curve, u);

                        if (SUCCESS == FindClosestIntersectionIndex (&intersectIndex, cursorEeh, cursorEeh2, &closePoint))
                            {
                            AssociativePoint::InitIntersection (*pAssocPoint, intersectIndex, 0, intersectSubEntIndex, 0, nPoints2);
                            status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath, modelRef, true, 0);
                            status = AssociativePoint::SetRoot (*pAssocPoint, pDisplayPath2, modelRef, true, 1);
                            }
                        }
                    break;
                    }
                }
            }
        }

    delete pDisplayPath2;
    delete pDisplayPath;

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      ConvertContext::GetDgnWeight (AcDb::LineWeight  lineWeight) const
    {
    switch (lineWeight)
        {
        case AcDb::kLnWtByLwDefault:
            return GetSettings().GetDgnWeightFromDwgWeight (RealDwgHostApp::Instance().workingAppSysvars()->lwdefault());

        case AcDb::kLnWtByLayer:
            return WEIGHT_BYLEVEL;

        case AcDb::kLnWtByBlock:
            return WEIGHT_BYCELL;

        default:
            return GetSettings().GetDgnWeightFromDwgWeight ((int) lineWeight);
        }
    }



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/03
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertContext::ReferenceAttachmentIdFromEntity (AcDbEntity* pEntity)
    {
    try
        {
        if (NULL == pEntity)
            return 0;

        AcDbBlockReference*         pBlockRef;
        AcDbViewport*               pViewport;

        if (NULL != (pBlockRef = AcDbBlockReference::cast (pEntity)))
            {
            if (pBlockRef->blockTableRecord().isNull())
                return 0;

            AcDbBlockTableRecordPointer pBlock (pBlockRef->blockTableRecord(), AcDb::kForRead);
            if (Acad::eOk != pBlock.openStatus())
                return 0;

            if (pBlock->isFromExternalReference())
                return this->ElementIdFromObject (pEntity);
            }
        else if (NULL != (pViewport = AcDbViewport::cast (pEntity)))
            {
            AcDbBlockTableRecordPointer pBlock (m_currentBlockId, AcDb::kForRead);
            if (Acad::eOk != pBlock.openStatus())
                return 0;

            AcDbObjectId    layoutId;
            if ( (layoutId = pBlock->getLayoutId()).isNull() )
                 return this->ElementIdFromObject (pEntity);

            AcDbLayoutPointer               pLayout (layoutId, AcDb::kForRead);
            if (Acad::eOk != pLayout.openStatus())
                 return this->ElementIdFromObject (pEntity);

            AcDbObjectIdArray               viewports = pLayout->getViewportArray();
            if ( (viewports.length() <= 0) || viewports[0] != pViewport->objectId())
                 return this->ElementIdFromObject (pEntity);

            return 0;
            }
        }
    catch   (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught in ConvertContext::ReferenceAttachementIdFromEntity\n");
        }

    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetPaperOriginCallback
(
AcDb::DxfCode               dxfGroupCode,
const void*                 dataIn,
void*                       userArg
)
    {
    DPoint2d*    paperOrigin = (DPoint2d*)userArg;
    // from AutoCAD 2009 DXF Reference
    if (148 == dxfGroupCode)
        paperOrigin->x = *((double*) dataIn);
    else if (149 == dxfGroupCode)
        paperOrigin->y = *((double*) dataIn);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetPaperOrigin (DPoint2dR paperOrigin, AcDbLayout* pLayout)
    {
    ExtractionFiler filer (GetPaperOriginCallback, pLayout->database(), &paperOrigin);
    filer.ExtractFrom (pLayout);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2004
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus       ConvertContext::CalculateSheetPropertiesFromLayout
(
DPoint2dR           sheetOrigin,
DPoint2dR           sheetSize,
double&             plotScale,
double&             plotAngle,
UnitDefinitionR     paperUnits,
DPoint2dR           minMargin,
DPoint2dR           maxMargin,
AcDbLayout*         pLayout
)
    {
    /*--------------------------------------------------------------------------------------------------------------
    This method sets DGN's sheet & paper parameters from DWG's layout & paper parameters.  Major differences between
    the two that impact our calculation of sheet & paper properties include:

    1) ACAD has 4 paper rotations: 0, 90, 180 and 270 degrees, representing 4 combinations of Portrait & Landscape.
       Our SheetDef's sheet rotation is a true rotation, i.e. the origins actually get transformed by the rotation matrix.
    2) ACAD always keeps origin at lower-left corner regardless what rotation it has.
    3) ACAD paper offset is an inverted value: a positive offset value needs to become negative.

    Based on these facts, we calculate sheet and paper origins from the layout origin at lower-left corner, then we
    swap the sheet's width and height for the rotations of 90 and 270 degrees.

    Andrew Edge has changed SheetDef API's to ignore paper parameters such that we can set PlotScale from DWG plot scale
    and PaperRotation from DWG plot orienation without having to set them separately.  They should be printer parameters
    anyway and should only be derived from sheet parameters.  PlotScale and PaperRotation are primarily used to round
    trip back to DWG.
    --------------------------------------------------------------------------------------------------------------*/
    // first, get effective layout scale:
    double          layoutScale = 0.0;
    if (pLayout->useStandardScale())
        pLayout->getStdScale(layoutScale);

    if (layoutScale < TOLERANCE_AnnotationScale || !pLayout->useStandardScale())
        {
        double       drawingUnitsNumerator, drawingUnitsDenominator;

        pLayout->getCustomPrintScale (drawingUnitsNumerator, drawingUnitsDenominator);
        layoutScale = (0.0 == drawingUnitsDenominator) ? 0.0 : drawingUnitsNumerator / drawingUnitsDenominator;
        }

    // use inverted plot scale
    layoutScale = layoutScale < TOLERANCE_AnnotationScale ? 1.0 : 1.0 / layoutScale;

    // now we are ready to calculate the sheet size & origin based on lower-left corner of the DWG plot setup:
    static DPoint2d zeroPoint = {0.0, 0.0};
    DPoint2d        paperSize;

    // get paper size and margins, in mm:
    paperSize.Zero ();
    minMargin.Zero ();
    maxMargin.Zero ();
    if (Acad::eOk != pLayout->getPlotPaperSize(paperSize.x, paperSize.y) || Acad::eOk != pLayout->getPlotPaperMargins(minMargin.x, minMargin.y, maxMargin.x, maxMargin.y))
        return  BadData;

    // subtract margins from the paper size to get the sheet size, and scale it to customer size:
    sheetSize.DifferenceOf (paperSize, minMargin);
    sheetSize.Subtract (maxMargin);
    sheetSize.Scale (layoutScale);

    // extract the reference point in mm to which the plot origin is relative (e.g. EXTMIN for plot type of Extents):
    DPoint2d        basePoint = zeroPoint;
    GetPaperOrigin (basePoint, pLayout);

    // get the plot origin which is the plot offset user has specified in the Page Setup:
    DPoint2d        plotOffset = zeroPoint;
    pLayout->getPlotOrigin (plotOffset.x, plotOffset.y);

    // map ACAD's Portrait & Lanscape rotation to SheetDef rotation, swap x & y as necessary.
    double          rotationAngle = 0.0;
    bool            swapXY = false;
    switch (pLayout->plotRotation())
        {
        case AcDbPlotSettings::k0degrees:
            plotAngle = 0.0;
            break;

        case AcDbPlotSettings::k90degrees:
            plotAngle = msGeomConst_piOver2;
            swapXY = true;
            break;

        case AcDbPlotSettings::k180degrees:
            plotAngle = msGeomConst_pi;
            break;

        case AcDbPlotSettings::k270degrees:
            plotAngle = msGeomConst_pi + msGeomConst_piOver2;
            swapXY = true;
            break;
        }

    // swap the reference point for a 90- or a 270-degrees of rotation - TFS 150745.
    if (swapXY && !basePoint.IsEqual(zeroPoint))
        basePoint.Init (basePoint.y, basePoint.x);

    // the sheet origin is the plot origin in the layout coordinate system, and in mm.
    plotOffset.Scale (layoutScale);
    sheetOrigin.SumOf (basePoint, plotOffset);
    // the paper origin goes negative from layout origin
    sheetOrigin.Negate (sheetOrigin);

    // carry out width & height swapping of the sheet for a 90- or a 270-degrees of rotation:
    if (swapXY)
        {
        if (!sheetSize.IsEqual(zeroPoint))
            sheetSize.Init (sheetSize.y, sheetSize.x);
        if (!sheetOrigin.IsEqual(zeroPoint))
            sheetOrigin.Init (sheetOrigin.y, sheetOrigin.x);
        if (!minMargin.IsEqual(zeroPoint))
            minMargin.Init (minMargin.y, minMargin.x);
        if (!maxMargin.IsEqual(zeroPoint))
            maxMargin.Init (maxMargin.y, maxMargin.x);
        }

    switch (pLayout->plotPaperUnits())
        {
        case    AcDbPlotSettings::kInches:
            paperUnits = UnitDefinition::GetStandardUnit (StandardUnit::EnglishInches);
            break;

        case    AcDbPlotSettings::kMillimeters:
            paperUnits = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);
            break;

        case    AcDbPlotSettings::kPixels:
        default:
            paperUnits = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);
            break;
        }

    double          sheetToPaper = 1.0;
    paperUnits.GetConversionFactorFrom (sheetToPaper, m_targetUnit);

    plotScale = sheetToPaper * layoutScale;

    UnitDefinition  mmUnits = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);

    double          mmToTarget = 1.0;
    m_targetUnit.GetConversionFactorFrom (mmToTarget, mmUnits);

    double          mmToDGN = mmToTarget * this->GetScaleToDGN();

    sheetToPaper *= mmToDGN;

    sheetSize.Scale (sheetToPaper);
    sheetOrigin.Scale (sheetToPaper);

    // SheetDef appears to expect scaled margins
    minMargin.Scale (sheetToPaper * layoutScale);
    maxMargin.Scale (sheetToPaper * layoutScale);

    return RealDwgSuccess;
    }

