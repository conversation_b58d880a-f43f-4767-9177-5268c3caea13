#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/db3dsolid.h>
#include <realdwg/base/dbsurf.h>
#include <realdwg/base/dbregion.h>
#include <realdwg/base/dbbody.h>
#include <realdwg/base/dbface.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/base/dbsubdmesh.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/geplane.h>
#include <realdwg/ge/gesurface.h>
#include <realdwg/ge/genurb3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// 3D Geometry Data Structures (Based on RealDwgFileIO)
//=======================================================================================

struct Face3DGeometry {
    std::vector<Point3d> vertices;      // Face vertices (3 or 4 points)
    Vector3d normal;                    // Face normal
    bool isVisible = true;              // Face visibility
    int edgeFlags = 0;                  // Edge visibility flags
    
    bool IsTriangle() const { return vertices.size() == 3; }
    bool IsQuad() const { return vertices.size() == 4; }
    bool IsValid() const;
    Vector3d CalculateNormal() const;
    double CalculateArea() const;
};

struct MeshGeometry {
    enum class Type {
        PolygonMesh,        // Regular M×N mesh
        PolyFaceMesh,       // Irregular mesh with faces
        SubDMesh            // Subdivision surface mesh
    };
    
    Type type = Type::PolygonMesh;
    std::vector<Point3d> vertices;
    std::vector<std::vector<int>> faces;    // Face vertex indices
    std::vector<Vector3d> normals;          // Vertex normals
    std::vector<Point3d> textureCoords;     // Texture coordinates
    
    // Polygon mesh specific
    int meshM = 0;                      // M direction size
    int meshN = 0;                      // N direction size
    bool isMClosed = false;             // M direction closed
    bool isNClosed = false;             // N direction closed
    int smoothType = 0;                 // Surface smoothing type
    int surfaceDensityM = 0;            // M direction density
    int surfaceDensityN = 0;            // N direction density
    
    // SubD mesh specific
    int subdivisionLevel = 0;           // Subdivision level
    bool isSmooth = true;               // Smooth subdivision
    
    bool IsValid() const;
    bool HasValidTopology() const;
    size_t GetFaceCount() const { return faces.size(); }
    size_t GetVertexCount() const { return vertices.size(); }
};

struct SurfaceGeometry {
    enum class Type {
        Plane,              // Planar surface
        Cylinder,           // Cylindrical surface
        Cone,               // Conical surface
        Sphere,             // Spherical surface
        Torus,              // Toroidal surface
        Nurbs,              // NURBS surface
        Extruded,           // Extruded surface
        Revolved,           // Revolved surface
        Swept,              // Swept surface
        Lofted              // Lofted surface
    };
    
    Type type = Type::Plane;
    
    // Common properties
    Point3d origin;
    Vector3d uDirection;
    Vector3d vDirection;
    Vector3d normal;
    
    // Parametric bounds
    double uMin = 0.0, uMax = 1.0;
    double vMin = 0.0, vMax = 1.0;
    
    // Type-specific data
    union {
        struct {
            double radius;
            double height;
        } cylinder;
        
        struct {
            double radius;
        } sphere;
        
        struct {
            double majorRadius;
            double minorRadius;
        } torus;
        
        struct {
            double baseRadius;
            double topRadius;
            double height;
        } cone;
    };
    
    // NURBS surface data
    struct NurbsData {
        int degreeU = 3, degreeV = 3;
        std::vector<double> knotsU, knotsV;
        std::vector<std::vector<Point3d>> controlPoints;
        std::vector<std::vector<double>> weights;
        bool isRationalU = false, isRationalV = false;
        bool isPeriodicU = false, isPeriodicV = false;
    } nurbsData;
    
    // Profile and path for swept/extruded surfaces
    std::vector<Point3d> profile;
    std::vector<Point3d> path;
    Vector3d extrusionDirection;
    Vector3d revolutionAxis;
    double revolutionAngle = 0.0;
    
    bool IsValid() const;
    bool IsPlanar() const { return type == Type::Plane; }
    bool IsParametric() const;
    Point3d EvaluatePoint(double u, double v) const;
    Vector3d EvaluateNormal(double u, double v) const;
};

struct Solid3DGeometry {
    enum class Type {
        Box,                // Rectangular box
        Cylinder,           // Cylindrical solid
        Cone,               // Conical solid
        Sphere,             // Spherical solid
        Torus,              // Toroidal solid
        Wedge,              // Wedge solid
        Pyramid,            // Pyramidal solid
        Extrusion,          // Extruded solid
        Revolution,         // Revolved solid
        Boolean,            // Boolean operation result
        Imported,           // Imported solid (ACIS/SAT)
        Custom              // Custom solid
    };
    
    Type type = Type::Box;
    
    // Bounding information
    Point3d center;
    Vector3d dimensions;    // Width, height, depth
    
    // Transformation
    Vector3d xAxis = Vector3d(1, 0, 0);
    Vector3d yAxis = Vector3d(0, 1, 0);
    Vector3d zAxis = Vector3d(0, 0, 1);
    
    // Type-specific parameters
    union {
        struct {
            double width, height, depth;
        } box;
        
        struct {
            double radius, height;
        } cylinder;
        
        struct {
            double radius;
        } sphere;
        
        struct {
            double majorRadius, minorRadius;
        } torus;
        
        struct {
            double baseRadius, topRadius, height;
        } cone;
    };
    
    // Profile for extrusion/revolution
    std::vector<Point3d> profile;
    Vector3d extrusionDirection;
    double extrusionHeight = 0.0;
    Vector3d revolutionAxis;
    double revolutionAngle = 0.0;
    
    // Boolean operation data
    enum class BooleanType { Union, Subtract, Intersect };
    BooleanType booleanType = BooleanType::Union;
    std::vector<Solid3DGeometry> operands;
    
    // ACIS/SAT data
    std::string acisData;
    std::string satData;
    
    bool IsValid() const;
    bool IsPrimitive() const;
    double CalculateVolume() const;
    double CalculateSurfaceArea() const;
};

//=======================================================================================
// 3D Entity Validation Results
//=======================================================================================

struct Mesh3DValidationResult : public DWGValidationResult {
    bool hasValidVertices = false;
    bool hasValidFaces = false;
    bool hasValidNormals = false;
    bool hasValidTopology = false;
    bool hasValidTextureCoords = false;
    int invalidVertexCount = 0;
    int invalidFaceCount = 0;
    int degenerateFaceCount = 0;
    
    void AddMeshError(const std::string& error) {
        AddError("Mesh: " + error);
    }
    
    void AddMeshWarning(const std::string& warning) {
        AddWarning("Mesh: " + warning);
    }
};

struct Surface3DValidationResult : public DWGValidationResult {
    bool hasValidGeometry = false;
    bool hasValidParametrization = false;
    bool hasValidBounds = false;
    bool hasValidNurbs = false;
    double surfaceArea = 0.0;
    
    void AddSurfaceError(const std::string& error) {
        AddError("Surface: " + error);
    }
    
    void AddSurfaceWarning(const std::string& warning) {
        AddWarning("Surface: " + warning);
    }
};

struct Solid3DValidationResult : public DWGValidationResult {
    bool hasValidGeometry = false;
    bool hasValidVolume = false;
    bool hasValidAcisData = false;
    bool isClosed = false;
    bool isManifold = false;
    double volume = 0.0;
    double surfaceArea = 0.0;
    
    void AddSolidError(const std::string& error) {
        AddError("Solid: " + error);
    }
    
    void AddSolidWarning(const std::string& warning) {
        AddWarning("Solid: " + warning);
    }
};

//=======================================================================================
// DWG 3D Entity Processor
//=======================================================================================

class DWG3DEntityProcessor : public DWGEntityProcessor {
public:
    DWG3DEntityProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWG3DEntityProcessor"; }

    // 3D entity processing methods
    DWGProcessingStatus ProcessFace3D(const Face3DGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessMesh(const MeshGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessSurface(const SurfaceGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessSolid3D(const Solid3DGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessRegion(const std::vector<Point3d>& boundary, const std::string& layer = "");

    // Specific mesh processing
    DWGProcessingStatus ProcessPolygonMesh(const MeshGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessPolyFaceMesh(const MeshGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessSubDMesh(const MeshGeometry& geometry, const std::string& layer = "");

    // Specific surface processing
    DWGProcessingStatus ProcessPlaneSurface(const SurfaceGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessNurbsSurface(const SurfaceGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessExtrudedSurface(const SurfaceGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessRevolvedSurface(const SurfaceGeometry& geometry, const std::string& layer = "");

    // Validation methods
    Mesh3DValidationResult ValidateMeshGeometry(const MeshGeometry& geometry) const;
    Surface3DValidationResult ValidateSurfaceGeometry(const SurfaceGeometry& geometry) const;
    Solid3DValidationResult ValidateSolid3DGeometry(const Solid3DGeometry& geometry) const;
    
    bool ValidateFace3D(const Face3DGeometry& face) const;
    bool ValidateMeshTopology(const MeshGeometry& geometry) const;
    bool ValidateNurbsSurface(const SurfaceGeometry::NurbsData& nurbs) const;

    // Geometry repair and optimization
    bool RepairMeshGeometry(MeshGeometry& geometry) const;
    bool RepairSurfaceGeometry(SurfaceGeometry& geometry) const;
    bool RepairSolid3DGeometry(Solid3DGeometry& geometry) const;
    
    bool OptimizeMesh(MeshGeometry& geometry) const;
    bool SimplifyMesh(MeshGeometry& geometry, double tolerance = 1e-6) const;
    bool WeldVertices(MeshGeometry& geometry, double tolerance = 1e-6) const;
    bool RemoveDegenerateFaces(MeshGeometry& geometry) const;

    // Conversion methods
    bool ConvertToTriangles(MeshGeometry& geometry) const;
    bool ConvertToQuads(MeshGeometry& geometry) const;
    bool TessellateSurface(const SurfaceGeometry& surface, MeshGeometry& mesh, int uSegments = 32, int vSegments = 32) const;
    bool ExtractSurfaceFromSolid(const Solid3DGeometry& solid, std::vector<SurfaceGeometry>& surfaces) const;

private:
    // Face processing helpers
    bool ProcessFaceVertices(const std::vector<Point3d>& vertices, bool isVisible = true) const;
    bool ValidateFaceVertices(const std::vector<Point3d>& vertices) const;
    Vector3d CalculateFaceNormal(const std::vector<Point3d>& vertices) const;
    bool IsFaceDegenerate(const std::vector<Point3d>& vertices, double tolerance = 1e-6) const;
    
    // Mesh processing helpers
    bool ValidateMeshConnectivity(const MeshGeometry& geometry) const;
    bool CalculateMeshNormals(MeshGeometry& geometry) const;
    bool ValidateMeshDimensions(int meshM, int meshN) const;
    bool ProcessMeshSmoothness(const MeshGeometry& geometry) const;
    
    // Surface processing helpers
    bool ValidateSurfaceBounds(const SurfaceGeometry& geometry) const;
    bool ValidateParametricSurface(const SurfaceGeometry& geometry) const;
    Point3d EvaluateSurfacePoint(const SurfaceGeometry& geometry, double u, double v) const;
    Vector3d EvaluateSurfaceNormal(const SurfaceGeometry& geometry, double u, double v) const;
    
    // Solid processing helpers
    bool ValidateSolidVolume(const Solid3DGeometry& geometry) const;
    bool ProcessAcisData(const std::string& acisData) const;
    bool ProcessSatData(const std::string& satData) const;
    bool ValidateBooleanOperation(const Solid3DGeometry& geometry) const;
    
    // Geometric calculations
    double CalculateMeshVolume(const MeshGeometry& geometry) const;
    double CalculateSurfaceArea(const SurfaceGeometry& geometry) const;
    bool IsManifoldMesh(const MeshGeometry& geometry) const;
    bool IsClosedMesh(const MeshGeometry& geometry) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbFace* CreateDWGFace3D(const Face3DGeometry& geometry) const;
    AcDbPolygonMesh* CreateDWGPolygonMesh(const MeshGeometry& geometry) const;
    AcDbPolyFaceMesh* CreateDWGPolyFaceMesh(const MeshGeometry& geometry) const;
    AcDbSubDMesh* CreateDWGSubDMesh(const MeshGeometry& geometry) const;
    AcDbSurface* CreateDWGSurface(const SurfaceGeometry& geometry) const;
    AcDb3dSolid* CreateDWGSolid3D(const Solid3DGeometry& geometry) const;
    AcDbRegion* CreateDWGRegion(const std::vector<Point3d>& boundary) const;
    
    // Surface creation helpers
    AcDbPlaneSurface* CreateDWGPlaneSurface(const SurfaceGeometry& geometry) const;
    AcDbNurbSurface* CreateDWGNurbsSurface(const SurfaceGeometry& geometry) const;
    AcDbExtrudedSurface* CreateDWGExtrudedSurface(const SurfaceGeometry& geometry) const;
    AcDbRevolvedSurface* CreateDWGRevolvedSurface(const SurfaceGeometry& geometry) const;
    
    // Property setting helpers
    bool SetMeshProperties(AcDbPolygonMesh* mesh, const MeshGeometry& geometry) const;
    bool SetSurfaceProperties(AcDbSurface* surface, const SurfaceGeometry& geometry) const;
    bool SetSolidProperties(AcDb3dSolid* solid, const Solid3DGeometry& geometry) const;
    
    // Error handling for RealDWG operations
    bool Handle3DCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedFaces = 0;
    mutable size_t m_processedMeshes = 0;
    mutable size_t m_processedSurfaces = 0;
    mutable size_t m_processedSolids = 0;
    mutable size_t m_repairedGeometry = 0;
    mutable size_t m_optimizedMeshes = 0;
    
    // Configuration
    double m_meshTolerance = 1e-6;
    double m_surfaceTolerance = 1e-8;
    double m_solidTolerance = 1e-10;
    int m_maxMeshVertices = 1000000;
    int m_maxMeshFaces = 2000000;
    bool m_enableMeshOptimization = true;
    bool m_enableSurfaceTessellation = true;
    bool m_enableSolidValidation = true;
};

} // namespace IModelExport
