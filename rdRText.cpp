/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdRText.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    "rDwgInternal.h"

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/19
+===============+===============+===============+===============+===============+======*/
struct RTextDataConverter
{
private:
    AcDbEntityP     m_sourceEntity;
    ConvertToDgnContextR    m_toDgnContext;

public:
    RTextDataConverter (AcDbEntityP proxy, ConvertToDgnContextR c) : m_sourceEntity(proxy), m_toDgnContext(c) {}
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
bool    FindRTextDataFromCache (RText::Data& rtext)
    {
    auto& rtexts = ::GetRTextCollection ();
    auto found = rtexts.find (m_sourceEntity->objectId());
    if (found != rtexts.end())
        {
        rtext = found->second;
        return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
void FindTextStyleByName (AcDbObjectId& textstyleId, AcString const& name, AcDbDatabaseP dwg)
    {
    if (!name.isEmpty() && dwg != nullptr)
        {
        AcDbTextStyleTablePointer   tstyleTable(dwg->textStyleTableId(), AcDb::kForRead);
        if (tstyleTable.openStatus() == Acad::eOk)
            {
            if (tstyleTable->getIdAt(name.kwszPtr(), textstyleId) != Acad::eOk)
                textstyleId = AcDbObjectId::kNull;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
TextBlockPtr    CreateTextBlock (RText::Data const& rtext)
    {
    auto dgnModel = m_toDgnContext.GetModel ();
    auto dgnFile = m_toDgnContext.GetFile ();
    if (dgnModel == nullptr || dgnFile == nullptr)
        return  nullptr;

    auto textBlock = TextBlock::Create (*dgnModel);
    if (!textBlock.IsValid())
        return  nullptr;

    // DXF only has textstyle name, not an ID
    AcDbObjectId    textstyleId = rtext.m_textstyleId;
    if (!textstyleId.isValid())
        FindTextStyleByName (textstyleId, rtext.m_textstyleName, m_toDgnContext.GetDatabase());

    double  widthScale = 1.0;
    DgnTextStylePtr dgnTextStyle;
    AcDbTextStyleTableRecordPointer dwgTextStyle(textstyleId, AcDb::kForRead);
    if (dwgTextStyle.openStatus() == Acad::eOk)
        {
        dgnTextStyle = DgnTextStyle::GetByID (m_toDgnContext.GetFileHolder().GetTextStyleIndex()->GetDgnId(textstyleId.handle()), *dgnFile);
        if (dwgTextStyle->xScale() > TOLERANCE_ZeroScale)
            widthScale = dwgTextStyle->xScale ();
        }

    TextSizeParam   textSize;
    DPoint3d        origin;
    RotMatrix       matrix;
    m_toDgnContext.GetDgnTextTransformFromDwg(&textSize, &origin, &matrix, rtext.m_normal, rtext.m_position, rtext.m_angle, rtext.m_height, widthScale);

    DPoint2d    fontSize = DPoint2d::From(textSize.size.width, textSize.size.height);

    TextBlockPropertiesPtr  tbProps;
    ParagraphPropertiesPtr  paraProps;
    RunPropertiesPtr        runProps;
    if (dgnTextStyle.IsValid())
        {
        tbProps     = TextBlockProperties::Create (*dgnTextStyle, *dgnModel);
        paraProps   = ParagraphProperties::Create (*dgnTextStyle, *dgnModel);
        runProps    = RunProperties::Create (*dgnTextStyle, *dgnModel);
        runProps->SetFontSize (fontSize);
        }
    else
        {
        tbProps     = TextBlockProperties::Create (*dgnModel);
        paraProps   = ParagraphProperties::Create (*dgnModel);
        runProps    = RunProperties::Create (DgnFontManager::GetDefaultTrueTypeFont(), fontSize, *dgnModel);
        }

    paraProps->SetJustification (TextElementJustification::LeftTop);

    textBlock->SetProperties (*tbProps);
    textBlock->SetParagraphPropertiesForAdd (*paraProps);
    textBlock->SetRunPropertiesForAdd (*runProps);
    textBlock->SetUserOrigin (origin);
    textBlock->SetOrientation (matrix);

    return  textBlock;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus AddDieselExpression (TextBlockR textBlock, WStringCR diesel)
    {
    //Get the xref list
    WString xRefList = L"";

    xRefList = diesel;
    bvector<XrefPair> xRefPair = m_toDgnContext.GetXRefBlockPath();
    //seperator for diesel exp e.g. $(xrefs,8)###blockname:path;blockname:path;
    if (xRefPair.size() > 0)
        xRefList += L"###";

    for (bpair<WString, WString> pair : xRefPair)
        {
        //WString block = pair.first;
        //WString path = pair.second;
        xRefList += pair.first;
        xRefList += L":";
        xRefList += pair.second;
        xRefList += L";";
        }
    textBlock.AppendText(xRefList.GetWCharCP());

    return  textBlock.IsEmpty() ? BSIERROR : BSISUCCESS;
    }
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus AddStringsFromTextFile (TextBlockR textBlock, WStringCR fileName)
    {
    BeFileStatus    status = BeFileStatus::UnknownError;
    auto textFile = BeTextFile::Open (status, fileName.GetWCharCP(), TextFileOpenType::Read, TextFileOptions::None);
    if (BeFileStatus::Success != status || !textFile.IsValid())
        return  BSIERROR;

    // read the first line
    WString line;
    if (TextFileReadStatus::Success != textFile->GetLine(line))
        return  BSIERROR;

    if (line.empty())
        textBlock.AppendLineBreak ();
    else
        textBlock.AppendText (line.GetWCharCP());

    // read rest of the file
    while (TextFileReadStatus::Success == textFile->GetLine(line))
        {
        textBlock.AppendLineBreak ();
        if (!line.empty())
            textBlock.AppendText (line.GetWCharCP());
        }

    return  textBlock.IsEmpty() ? BSIERROR : BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateElement (EditElementHandleR outElement, TextBlockR textBlock)
    {
    // create text element from TextBlock
    auto result = TextHandlerBase::CreateElement (outElement, nullptr, textBlock);
    if (TEXTBLOCK_TO_ELEMENT_RESULT_Empty == result)
        {
        DIAGNOSTIC_PRINTF ("Ignoring empty rtext %I64d...\n", m_toDgnContext.ElementIdFromObject(m_sourceEntity));
        return  EmptyText;
        }
    if (!outElement.IsValid())
        return  CantCreateText;

    outElement.GetElementP()->hdr.ehdr.locked = true;

    m_toDgnContext.ElementHeaderFromEntity (outElement, m_sourceEntity);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                      Sandip Ichake  5/2020
+---------------+---------------+---------------+---------------+---------------+------*/
void    SetxRefList()
    {
    AcDbDatabaseP   masterDwg = nullptr;
    DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg);

    if (masterDwg == nullptr)
        return;

    bvector<XrefPair> xRefPairData;
    AcDbBlockTablePointer       pBlockTable(masterDwg->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != pBlockTable.openStatus())
        return;

    AcDbBlockTableIterator*     pBlkIter = nullptr;
    if (Acad::eOk != pBlockTable->newIterator(pBlkIter))
        {
        pBlockTable.close();
        return;
        }

    for (pBlkIter->start(); !pBlkIter->done(); pBlkIter->step())
        {
        AcDbObjectId                blockId;
        if (Acad::eOk != pBlkIter->getRecordId(blockId))
            continue;

        AcDbBlockTableRecordPointer     pBlock(blockId, AcDb::kForRead);
        if (pBlock->isFromExternalReference())
            {
            // path name
            ACHAR           *pathName;
            pBlock->pathName(pathName);
            WString strTempPath(pathName);
            //block name
            const ACHAR*    blockName;
            pBlock->getName(blockName);
            WString strTempBlock(blockName);

            xRefPairData.push_back(XrefPair(strTempBlock, strTempPath));
            //const ACHAR           *fileName;
            //AcDbDatabase *pDb = pBlock->xrefDatabase(false);
            //pDb->getFilename(fileName);
            //delete fileName;

            //printf("XRef within Block - %ls -  %ls \n", blockName, pathName);
            }
        pBlock.close();

        }
    m_toDgnContext.SetXRefBlockPath(xRefPairData);
    delete pBlkIter;
    pBlockTable.close();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert (EditElementHandleR outElement)
    {
    RText::Data rtext;
    if (!this->FindRTextDataFromCache(rtext))
        return  NotApplicable;

    WString contents(rtext.m_contents.kwszPtr());
    contents.Trim ();
    if (contents.empty())
        return  EmptyText;

    if (rtext.IsTextFile() && !BeFileName::DoesPathExist(contents.GetWCharCP()))
        return  FileNotFound;

    auto textBlock = this->CreateTextBlock (rtext);
    if (!textBlock.IsValid())
        return  CantCreateText;

    BentleyStatus   status = BSIERROR;
    if (rtext.IsDieselExpression())
        {
        //get the xrefs list only for xrefs expression
        WString temp = contents;
        temp.ToLower();
        if (temp.StartsWith(L"$(xrefs"))
            SetxRefList();
        status = this->AddDieselExpression(*textBlock, contents);
        }
    else
        status = this->AddStringsFromTextFile (*textBlock, contents);
    if (status != BSISUCCESS)
        return  CantCreateText;
                    
    textBlock->PerformLayout ();

    return  this->CreateElement(outElement, *textBlock);
    }
};  // RTextConverter

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/19
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CreateElementFromRText (EditElementHandleR outElement, AcDbEntityP acEntity)
    {
    if (acEntity == nullptr)
        return  NullObject;

    RTextDataConverter  factory(acEntity, *this);
    return  factory.Convert(outElement);
    }
