#include <gtest/gtest.h>
#include "../include/IModelExportManager.h"
#include "../include/ExportTypes.h"
#include "../src/core/ExportContext.h"
#include "../src/core/GeometryProcessor.h"
#include "../src/core/MaterialManager.h"
#include "../src/core/ConversionPipeline.h"

using namespace IModelExport;

//=======================================================================================
// Test Fixtures
//=======================================================================================

class ExportFrameworkTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_context = std::make_shared<ExportContext>();
        m_exportManager = IModelExportManager::Create();
    }

    void TearDown() override {
        m_context.reset();
        m_exportManager.reset();
    }

    std::shared_ptr<ExportContext> m_context;
    std::unique_ptr<IModelExportManager> m_exportManager;
};

class GeometryProcessorTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_processor = std::make_unique<GeometryProcessor>();
    }

    void TearDown() override {
        m_processor.reset();
    }

    std::unique_ptr<GeometryProcessor> m_processor;
};

class MaterialManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_materialManager = std::make_unique<MaterialManager>();
    }

    void TearDown() override {
        m_materialManager.reset();
    }

    std::unique_ptr<MaterialManager> m_materialManager;
};

//=======================================================================================
// Export Manager Tests
//=======================================================================================

TEST_F(ExportFrameworkTest, CreateExportManager) {
    ASSERT_NE(m_exportManager, nullptr);
    EXPECT_TRUE(m_exportManager->IsFormatSupported(ExportFormat::DWG));
    EXPECT_TRUE(m_exportManager->IsFormatSupported(ExportFormat::IFC));
    EXPECT_TRUE(m_exportManager->IsFormatSupported(ExportFormat::DGN));
    EXPECT_TRUE(m_exportManager->IsFormatSupported(ExportFormat::USD));
}

TEST_F(ExportFrameworkTest, GetSupportedFormats) {
    auto formats = m_exportManager->GetSupportedFormats();
    EXPECT_GE(formats.size(), 4);
    
    bool hasDWG = std::find(formats.begin(), formats.end(), ExportFormat::DWG) != formats.end();
    bool hasIFC = std::find(formats.begin(), formats.end(), ExportFormat::IFC) != formats.end();
    bool hasDGN = std::find(formats.begin(), formats.end(), ExportFormat::DGN) != formats.end();
    bool hasUSD = std::find(formats.begin(), formats.end(), ExportFormat::USD) != formats.end();
    
    EXPECT_TRUE(hasDWG);
    EXPECT_TRUE(hasIFC);
    EXPECT_TRUE(hasDGN);
    EXPECT_TRUE(hasUSD);
}

TEST_F(ExportFrameworkTest, ValidateExportOptions) {
    // Test DWG options validation
    DWGExportOptions dwgOptions;
    dwgOptions.outputPath = "test.dwg";
    dwgOptions.version = DWGExportOptions::DWGVersion::R2021;
    dwgOptions.geometryTolerance = 1e-6;
    
    std::vector<std::string> errors;
    bool isValid = m_exportManager->ValidateExportOptions(ExportFormat::DWG, dwgOptions, errors);
    EXPECT_TRUE(isValid);
    EXPECT_TRUE(errors.empty());
    
    // Test invalid options
    DWGExportOptions invalidOptions;
    invalidOptions.outputPath = ""; // Empty path should be invalid
    invalidOptions.geometryTolerance = -1.0; // Negative tolerance should be invalid
    
    errors.clear();
    isValid = m_exportManager->ValidateExportOptions(ExportFormat::DWG, invalidOptions, errors);
    EXPECT_FALSE(isValid);
    EXPECT_FALSE(errors.empty());
}

//=======================================================================================
// Export Context Tests
//=======================================================================================

TEST_F(ExportFrameworkTest, ExportContextBasicOperations) {
    ASSERT_NE(m_context, nullptr);
    
    // Test coordinate transformation
    Transform3d transform = Transform3d::Translation(Vector3d(10, 20, 30));
    m_context->SetCoordinateTransform(transform);
    
    Point3d originalPoint(0, 0, 0);
    Point3d transformedPoint = m_context->TransformPoint(originalPoint);
    
    EXPECT_NEAR(transformedPoint.x, 10.0, 1e-6);
    EXPECT_NEAR(transformedPoint.y, 20.0, 1e-6);
    EXPECT_NEAR(transformedPoint.z, 30.0, 1e-6);
    
    // Test units conversion
    m_context->SetUnitsConversion(1.0, 1000.0); // meters to millimeters
    double convertedLength = m_context->ConvertLength(1.0);
    EXPECT_NEAR(convertedLength, 1000.0, 1e-6);
}

TEST_F(ExportFrameworkTest, ExportContextElementTracking) {
    // Test element ID mapping
    std::string imodelId = "element_123";
    std::string exportId = "dwg_entity_456";
    
    m_context->MapElementId(imodelId, exportId);
    
    std::string retrievedId;
    bool found = m_context->GetMappedElementId(imodelId, retrievedId);
    EXPECT_TRUE(found);
    EXPECT_EQ(retrievedId, exportId);
    
    // Test element processing tracking
    m_context->MarkElementProcessed(imodelId);
    EXPECT_TRUE(m_context->IsElementProcessed(imodelId));
    EXPECT_FALSE(m_context->IsElementProcessed("non_existent_element"));
    
    // Test statistics
    m_context->IncrementProcessedCount();
    m_context->IncrementProcessedCount();
    m_context->IncrementSkippedCount();
    
    EXPECT_EQ(m_context->GetProcessedCount(), 2);
    EXPECT_EQ(m_context->GetSkippedCount(), 1);
    EXPECT_EQ(m_context->GetErrorCount(), 0);
}

TEST_F(ExportFrameworkTest, ExportContextHierarchy) {
    // Test parent-child relationships
    std::string parentId = "parent_element";
    std::string childId1 = "child_element_1";
    std::string childId2 = "child_element_2";
    
    m_context->SetElementParent(childId1, parentId);
    m_context->SetElementParent(childId2, parentId);
    
    std::string retrievedParent;
    bool hasParent = m_context->GetElementParent(childId1, retrievedParent);
    EXPECT_TRUE(hasParent);
    EXPECT_EQ(retrievedParent, parentId);
    
    auto children = m_context->GetElementChildren(parentId);
    EXPECT_EQ(children.size(), 2);
    EXPECT_TRUE(std::find(children.begin(), children.end(), childId1) != children.end());
    EXPECT_TRUE(std::find(children.begin(), children.end(), childId2) != children.end());
    
    // Test element grouping
    std::string groupName = "test_group";
    m_context->AddElementToGroup(groupName, childId1);
    m_context->AddElementToGroup(groupName, childId2);
    
    auto groupElements = m_context->GetGroupElements(groupName);
    EXPECT_EQ(groupElements.size(), 2);
    
    auto allGroups = m_context->GetAllGroups();
    EXPECT_TRUE(std::find(allGroups.begin(), allGroups.end(), groupName) != allGroups.end());
}

//=======================================================================================
// Geometry Processor Tests
//=======================================================================================

TEST_F(GeometryProcessorTest, BasicGeometryOperations) {
    ASSERT_NE(m_processor, nullptr);
    
    // Test point transformation
    Point3d point(1, 2, 3);
    Transform3d transform = Transform3d::Translation(Vector3d(10, 20, 30));
    
    GeometryData geometry;
    geometry.type = GeometryData::Type::Point;
    geometry.points = {point};
    geometry.transform = transform;
    
    auto transformedGeometry = m_processor->TransformGeometry(geometry, transform);
    ASSERT_FALSE(transformedGeometry.points.empty());
    
    Point3d transformedPoint = transformedGeometry.points[0];
    EXPECT_NEAR(transformedPoint.x, 11.0, 1e-6);
    EXPECT_NEAR(transformedPoint.y, 22.0, 1e-6);
    EXPECT_NEAR(transformedPoint.z, 33.0, 1e-6);
}

TEST_F(GeometryProcessorTest, GeometryAnalysis) {
    // Create test geometry - a triangle
    std::vector<Point3d> trianglePoints = {
        Point3d(0, 0, 0),
        Point3d(1, 0, 0),
        Point3d(0.5, 1, 0)
    };
    
    GeometryData triangle;
    triangle.type = GeometryData::Type::Mesh;
    triangle.points = trianglePoints;
    triangle.indices = {0, 1, 2};
    
    auto properties = m_processor->AnalyzeGeometryProperties(triangle);
    
    EXPECT_GT(properties.area, 0.0);
    EXPECT_TRUE(properties.isPlanar);
    EXPECT_TRUE(properties.boundingBox.IsValid());
    
    // Validate geometry
    std::vector<std::string> errors;
    bool isValid = m_processor->ValidateGeometry(triangle, errors);
    EXPECT_TRUE(isValid);
    EXPECT_TRUE(errors.empty());
}

TEST_F(GeometryProcessorTest, CurveProcessing) {
    // Create a simple line curve
    CurveData line;
    line.type = CurveData::CurveType::Line;
    line.controlPoints = {Point3d(0, 0, 0), Point3d(10, 0, 0)};
    line.degree = 1;
    
    // Test curve length calculation
    double length = m_processor->CalculateCurveLength(line);
    EXPECT_NEAR(length, 10.0, 1e-6);
    
    // Test curve evaluation
    Point3d midPoint = m_processor->EvaluateCurve(line, 0.5);
    EXPECT_NEAR(midPoint.x, 5.0, 1e-6);
    EXPECT_NEAR(midPoint.y, 0.0, 1e-6);
    EXPECT_NEAR(midPoint.z, 0.0, 1e-6);
    
    // Test curve tessellation
    auto tessellatedPoints = m_processor->TessellateCurve(line, 0.1);
    EXPECT_GE(tessellatedPoints.size(), 2);
    EXPECT_NEAR(tessellatedPoints.front().x, 0.0, 1e-6);
    EXPECT_NEAR(tessellatedPoints.back().x, 10.0, 1e-6);
}

//=======================================================================================
// Material Manager Tests
//=======================================================================================

TEST_F(MaterialManagerTest, MaterialRegistration) {
    ASSERT_NE(m_materialManager, nullptr);
    
    // Create test material
    Material material;
    material.name = "Test Material";
    material.diffuseColor = Color(1.0f, 0.0f, 0.0f, 1.0f); // Red
    material.specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    material.shininess = 0.5f;
    material.transparency = 0.0f;
    
    // Register material
    std::string materialId = m_materialManager->RegisterMaterial(material);
    EXPECT_FALSE(materialId.empty());
    
    // Retrieve material
    Material retrievedMaterial;
    bool found = m_materialManager->GetMaterial(materialId, retrievedMaterial);
    EXPECT_TRUE(found);
    EXPECT_EQ(retrievedMaterial.name, material.name);
    EXPECT_NEAR(retrievedMaterial.diffuseColor.r, material.diffuseColor.r, 1e-6f);
    EXPECT_NEAR(retrievedMaterial.diffuseColor.g, material.diffuseColor.g, 1e-6f);
    EXPECT_NEAR(retrievedMaterial.diffuseColor.b, material.diffuseColor.b, 1e-6f);
    
    // Check material exists
    EXPECT_TRUE(m_materialManager->HasMaterial(materialId));
    EXPECT_FALSE(m_materialManager->HasMaterial("non_existent_material"));
}

TEST_F(MaterialManagerTest, MaterialSearch) {
    // Register multiple materials
    Material redMaterial;
    redMaterial.name = "Red Material";
    redMaterial.diffuseColor = Color(1.0f, 0.0f, 0.0f, 1.0f);
    
    Material blueMaterial;
    blueMaterial.name = "Blue Material";
    blueMaterial.diffuseColor = Color(0.0f, 0.0f, 1.0f, 1.0f);
    
    std::string redId = m_materialManager->RegisterMaterial(redMaterial);
    std::string blueId = m_materialManager->RegisterMaterial(blueMaterial);
    
    // Test search by name
    std::string foundId = m_materialManager->FindMaterialByName("Red Material");
    EXPECT_EQ(foundId, redId);
    
    foundId = m_materialManager->FindMaterialByName("Non Existent Material");
    EXPECT_TRUE(foundId.empty());
    
    // Test similar material search
    Material similarToRed;
    similarToRed.diffuseColor = Color(0.9f, 0.1f, 0.1f, 1.0f); // Slightly different red
    
    std::string similarId = m_materialManager->FindSimilarMaterial(similarToRed, 0.2);
    EXPECT_EQ(similarId, redId);
    
    // Get all material IDs
    auto allIds = m_materialManager->GetAllMaterialIds();
    EXPECT_EQ(allIds.size(), 2);
    EXPECT_TRUE(std::find(allIds.begin(), allIds.end(), redId) != allIds.end());
    EXPECT_TRUE(std::find(allIds.begin(), allIds.end(), blueId) != allIds.end());
}

TEST_F(MaterialManagerTest, MaterialConversion) {
    // Create test material
    Material sourceMaterial;
    sourceMaterial.name = "Source Material";
    sourceMaterial.diffuseColor = Color(0.5f, 0.5f, 0.5f, 1.0f);
    sourceMaterial.shininess = 0.8f;
    
    // Test conversion for different formats
    Material dwgMaterial = m_materialManager->ConvertMaterialForFormat(sourceMaterial, ExportFormat::DWG);
    EXPECT_EQ(dwgMaterial.name, sourceMaterial.name);
    
    Material ifcMaterial = m_materialManager->ConvertMaterialForFormat(sourceMaterial, ExportFormat::IFC);
    EXPECT_EQ(ifcMaterial.name, sourceMaterial.name);
    
    // Test validation
    std::vector<std::string> warnings;
    bool isValid = m_materialManager->ValidateMaterialForFormat(sourceMaterial, ExportFormat::USD, warnings);
    EXPECT_TRUE(isValid);
}

//=======================================================================================
// Integration Tests
//=======================================================================================

TEST_F(ExportFrameworkTest, EndToEndExportTest) {
    // This test simulates a complete export workflow
    
    // Create mock iModel data
    ElementInfo testElement;
    testElement.id = "test_element_001";
    testElement.type = ElementType::GeometricElement;
    testElement.classFullName = "TestGeometricElement";
    testElement.userLabel = "Test Element";
    testElement.description = "A test element for unit testing";
    
    // Set up export options
    DWGExportOptions options;
    options.outputPath = "test_output.dwg";
    options.version = DWGExportOptions::DWGVersion::R2021;
    options.levelOfDetail = ExportLOD::Medium;
    options.includeMetadata = true;
    options.geometryTolerance = 1e-6;
    
    // Validate options
    std::vector<std::string> errors;
    bool optionsValid = m_exportManager->ValidateExportOptions(ExportFormat::DWG, options, errors);
    EXPECT_TRUE(optionsValid);
    
    // Note: In a real test, we would create a mock iModel and perform actual export
    // For now, we just verify that the framework components are properly initialized
    EXPECT_NE(m_exportManager, nullptr);
    EXPECT_NE(m_context, nullptr);
}

//=======================================================================================
// Performance Tests
//=======================================================================================

TEST_F(ExportFrameworkTest, PerformanceTest) {
    // Test processing performance with a large number of elements
    const size_t numElements = 1000;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < numElements; ++i) {
        std::string elementId = "element_" + std::to_string(i);
        std::string exportId = "export_" + std::to_string(i);
        
        m_context->MapElementId(elementId, exportId);
        m_context->MarkElementProcessed(elementId);
        m_context->IncrementProcessedCount();
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    // Verify all elements were processed
    EXPECT_EQ(m_context->GetProcessedCount(), numElements);
    
    // Performance should be reasonable (less than 1 second for 1000 elements)
    EXPECT_LT(duration.count(), 1000);
    
    std::cout << "Processed " << numElements << " elements in " << duration.count() << " ms" << std::endl;
}

//=======================================================================================
// Error Handling Tests
//=======================================================================================

TEST_F(ExportFrameworkTest, ErrorHandlingTest) {
    // Test error tracking
    std::string elementId = "error_element";
    std::string errorMessage = "Test error message";
    
    m_context->AddError(elementId, errorMessage);
    
    auto allErrors = m_context->GetErrors();
    EXPECT_FALSE(allErrors.empty());
    EXPECT_TRUE(std::find(allErrors.begin(), allErrors.end(), errorMessage) != allErrors.end());
    
    auto elementErrors = m_context->GetElementErrors(elementId);
    EXPECT_FALSE(elementErrors.empty());
    EXPECT_EQ(elementErrors[0], errorMessage);
    
    // Test warning tracking
    std::string warningMessage = "Test warning message";
    m_context->AddWarning(elementId, warningMessage);
    
    auto allWarnings = m_context->GetWarnings();
    EXPECT_FALSE(allWarnings.empty());
    
    auto elementWarnings = m_context->GetElementWarnings(elementId);
    EXPECT_FALSE(elementWarnings.empty());
    EXPECT_EQ(elementWarnings[0], warningMessage);
}

//=======================================================================================
// Main Test Runner
//=======================================================================================

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "Running iModel Export Framework Tests..." << std::endl;
    std::cout << "=========================================" << std::endl;
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "\nTest execution completed." << std::endl;
    
    return result;
}
