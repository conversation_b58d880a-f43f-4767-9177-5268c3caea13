#----------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/realDwgFileIO.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#----------------------------------------------------------------------------------------

%if !defined (RealDwgVersion)
RealDwgVersion = 2023
%endif

%if defined (BUILD_DWGPLATFORM)
appName             = DwgDgnIO
OutContextDelivery  = $(BuildContext)Delivery/
OutDlls             = $(mstation)mdlapps/$(appName)/
%else
appName             = RealDwgFileIO
OutContextDelivery  = $(BuildContext)Delivery/RealDwg$(RealDwgVersion)/
OutDlls             = $(RealDwgFileHandlerDir)
%endif
NOSTRICT    = 1

%if RealDwgVersion == 2014
BUILD_USING_VS2010  = 1
%elif RealDwgVersion >= 2015 && RealDwgVersion <= 2016
BUILD_USING_VS2012  = 1
%elif RealDwgVersion >= 2017 && RealDwgVersion <= 2018
#BUILD_USING_VS2015  = 1
%elif RealDwgVersion == 2019
BUILD_USING_VS2017  = 1
%elif RealDwgVersion >= 2020 && RealDwgVersion <= 2023
BUILD_USING_VS2019  = 1
%endif

%if defined (CHECK_SECURITY)
ENABLE_COMPILER_ANALYZE = 1
SECURITY_ONLY_COMPILER_ANALYZE = 1
ANALYZE_WARNINGS_TO_ERRORS = 1
%endif

%include mdl.mki
%if BUILD_USING_VS2010
PublicApiIncludes    = -I$(build)VC10Api/PublicAPI/ -I${BuildContext}/VendorAPI/
PublicApiRscIncludes = -i$(build)VC10Api/PublicAPI/
%endif
%include corelibs.mki
%include realdwg.mki

%if defined(BUILD_USING_VS2010) && !defined(BUILD_DWGPLATFORM) && !defined(SKIPVS2010PYTHONSRIPT)
always:
    python $(SrcRoot)PowerPlatform/MstnPlatform/mstn/mdlapps/RealDwgFileIO/VS2010Enums.py $(OutBuildContexts)/PowerPlatform/PublicAPI $(OutBuildDir)/PowerPlatform/VC10Api/PublicAPI/ rebuild
%endif

oldObjDir %= $(o)
objDir   = $(oldObjDir)$(RealDwgVersion)/

#----------------------------------------------------------------------
# Defines for using DLLIMPORT/DLLEXPORT
#----------------------------------------------------------------------
nameToDefine = __REALDWGFILEIO_BUILD__
%include cdefapnd.mki

%if BUILD_DWGPLATFORM
nameToDefine = BUILD_DWGPLATFORM
%include cdefapnd.mki
%endif

%if REALDWG_NOISY_DEBUG
nameToDefine = REALDWG_NOISY_DEBUG
%include cdefapnd.mki

REALDWG_DIAGNOSTICS = 1
REALDWG_FILER_DEBUG = 1
%endif

%if REALDWG_DIAGNOSTICS
nameToDefine = REALDWG_DIAGNOSTICS
%include cdefapnd.mki
%endif

%if REALDWG_DIAGNOSTICS_VERBOSE
nameToDefine = REALDWG_DIAGNOSTICS_VERBOSE
%include cdefapnd.mki
%endif

%if REALDWG_FILER_DEBUG
nameToDefine = REALDWG_FILER_DEBUG
%include cdefapnd.mki
%endif

%if REALDWG_FILER_DEBUG_VERBOSE
nameToDefine = REALDWG_FILER_DEBUG_VERBOSE
%include cdefapnd.mki
%endif

cDefs +% -bigobj -DRealDwgVersion=$(RealDwgVersion)

#----------------------------------------------------------------------
#   Define search directories. The RealDWG include subdirectories must
#   be cincapnd'ed here because the base RealDWG #include files include
#   them without subdirectory specifications.
#----------------------------------------------------------------------
dirToSearch = $(BuildContext)VendorAPI/RealDwg/Base/
%include cincapnd.mki

dirToSearch = $(BuildContext)VendorAPI/RealDwg/Atil/
%include cincapnd.mki

dirToSearch = $(BuildContext)VendorAPI/RealDwg/Atil/codec_properties/
%include cincapnd.mki

dirToSearch = $(BuildContext)VendorAPI/RealDwg/Atil/format_codecs/
%include cincapnd.mki

# As of R24, interop includes parasolid_kernel.h directly.
dirToSearch=$(BuildContext)VendorAPI/Psolid
%include cincapnd.mki


#----------------------------------------------------------------------
#   Add boost
#----------------------------------------------------------------------
dirToSearch = $(BuildContext)VendorAPI/Boost_1_55/
%include cincapnd.mki

#----------------------------------------------------------------------
#   Create output directories.
#----------------------------------------------------------------------
always:
    !~@mkdir $(o)
    ~mkdir $(rscObjects)
    ~mkdir $(OutDlls)

#----------------------------------------------------------------------
#   Set up to use dlmlink.mki
#
#   Note: we need to use the obscure DLM_DELAYLOADHOOK feature because we
#         build this .mke file with different compilers at different times.
#         Without that, it can leave DelayLoadHook.obj in the common directory
#         compiled with the wrong compiler.
#----------------------------------------------------------------------
DLM_NAME                    =   $(appName)
DLM_OBJECT_DEST             =   $(o)
DLM_LIBDEF_SRC              =   $(baseDir)
DLM_OBJECT_FILES            =   $(appObjects)
DLM_EXPORT_DEST             =   $(o)
DLM_NOENTRY                 =   1
DLM_NO_INITIALIZE_FUNCTION  =   1
DLM_NO_DEF                  =   1
DLM_NO_DLS                  =   1
DLM_DEST                    =   $(OutDlls)
DLM_DELAYLOADHOOK           =   $(tools)delayLoadHook.cpp
DLM_CREATE_LIB_CONTEXT_LINK =   1
DLM_CONTEXT_LOCATION        =   $(OutContextDelivery)
%if defined (BUILD_DWGPLATFORM)
DLM_NOMSBUILTINS            =   1
%else
DLM_LIB_CONTEXT_LOCATION    =   $(OutContextDelivery)
%endif

LINKER_LIBRARIES            =   $(RealDwgCoreLibs)                              \
                                $(DgnPlatformElementHandlerLibs)                \
                                $(ContextSubpartsLibs)$(ECNativeObjectsLib)     \
                                $(ContextSubpartsLibs)$(DgnViewLib)             \
                                $(ContextSubpartsLibs)pdffileioImp.lib          \
                                $(ContextSubpartsLibs)BeJsonCpp.lib             \
                                $(ContextSubpartsLibs)BeSQLite.lib              \
                                $(buildPrivateLib)MultiPointPlacementDll.lib    \
                                shell32.lib shlwapi.lib

LINKER_LIBRARIES_DELAYLOADED  = $(DgnViewExportLib)                                 \
                                $(RealDwgSceneOELib)                                \
                                $(RealDwgAtilLibs)                                  \
                                $(RealDwgBrepLibs)                                  \
                                $(BuildContextSubPartsLib)pskernel.lib              \
                                $(BuildContextSubPartsLib)$(PSolidCoreLib)          \
                                $(BuildContextSubPartsLib)$(PSolidAcisInteropLib)   \
                                $(BuildContextSubPartsLib)RText.lib                 \
                                $(RasterCoreExportLib)                              \
                                $(RealDwgLibDir)AcDbPointCloudObj.lib               \
                                $(ContextSubpartsLibs)$(BaseGeoCoordLib) \
                                $(ContextSubpartsLibs)$(DgnGeoCoordLib) \
                                $(ContextSubpartsLibs)BeXml.lib \
                                msi.lib urlmon.lib wininet.lib odbc32.lib odbccp32.lib version.lib
#----------------------------------------------------------------------
#   Build code modules.
#----------------------------------------------------------------------
#-----------------------------------------------------------------------------
# Compile DLM Source Files
#-----------------------------------------------------------------------------
# Files includes in the rDwgDgnExtension compiland:
includedSourceFiles = $(baseDir)rdModelIndexItem.cpp          \
                      $(baseDir)rdResbuf.cpp                  \
                      $(baseDir)rdXDataUtil.cpp               \
                      $(baseDir)rdUtil.cpp                    \
                      $(baseDir)rdExtractionFiler.cpp         \
                      $(baseDir)rdRecordingFiler.cpp          \
                      $(baseDir)rdTextStyleConvert.cpp        \
                      $(baseDir)rdLineStyleConvert.cpp        \
                      $(baseDir)rdMlineStyleConvert.cpp       \
                      $(baseDir)rdDimStyleConvert.cpp         \
                      $(baseDir)rdViewConvert.cpp             \
                      $(baseDir)rdLayerConvert.cpp            \
                      $(baseDir)rdUcsConvert.cpp              \
                      $(baseDir)rdMaterialConvert.cpp         \
                      $(baseDir)rdBrepConvert.cpp             \
                      $(baseDir)rdLine.cpp                    \
                      $(baseDir)rdPoint.cpp                   \
                      $(baseDir)rdPointCloud.cpp              \
                      $(baseDir)rdCircle.cpp                  \
                      $(baseDir)rdArc.cpp                     \
                      $(baseDir)rdEllipse.cpp                 \
                      $(baseDir)rdShape.cpp                   \
                      $(baseDir)rdSolid.cpp                   \
                      $(baseDir)rdSurface.cpp                 \
                      $(baseDir)rdWipeout.cpp                 \
                      $(baseDir)rdTrace.cpp                   \
                      $(baseDir)rdPolyline.cpp                \
                      $(baseDir)rd2dPolyline.cpp              \
                      $(baseDir)rd3dPolyline.cpp              \
                      $(baseDir)rdMline.cpp                   \
                      $(baseDir)rdAttribute.cpp               \
                      $(baseDir)rdBlock.cpp                   \
                      $(baseDir)rdBlockReference.cpp          \
                      $(baseDir)rdDictionary.cpp              \
                      $(baseDir)rdText.cpp                    \
                      $(baseDir)rdMText.cpp                   \
                      $(baseDir)rdRText.cpp                   \
                      $(baseDir)rdDimension.cpp               \
                      $(baseDir)dgnDimension.cpp              \
                      $(baseDir)rdFace.cpp                    \
                      $(baseDir)rdGroup.cpp                   \
                      $(baseDir)rdHatch.cpp                   \
                      $(baseDir)rdImage.cpp                   \
                      $(baseDir)rdLeader.cpp                  \
                      $(baseDir)rdMultiLeader.cpp             \
                      $(baseDir)rdPolyFaceMesh.cpp            \
                      $(baseDir)rdPolygonMesh.cpp             \
                      $(baseDir)rdSubDMesh.cpp                \
                      $(baseDir)rdSpline.cpp                  \
                      $(baseDir)rdOle2Frame.cpp               \
                      $(baseDir)rdAcisData.cpp                \
                      $(baseDir)rdViewport.cpp                \
                      $(baseDir)rdRegApp.cpp                  \
                      $(baseDir)rdXRecord.cpp                 \
                      $(baseDir)rdField.cpp                   \
                      $(baseDir)rdLight.cpp                   \
                      $(baseDir)rdUnderlay.cpp                \
                      $(baseDir)rdTable.cpp                   \
                      $(baseDir)rDwgBaseContext.cpp           \
                      $(baseDir)rDwgToDgnContext.cpp          \
                      $(baseDir)rDwgFromDgnContext.cpp        \
                      $(baseDir)rDwgWorldDraw.cpp             \
                      $(baseDir)rdViewportDrawEntity.cpp      \
                      $(baseDir)dgnMesh.cpp                   \
                      $(baseDir)dgnLinear.cpp                 \
                      $(baseDir)dgnArcs.cpp                   \
                      $(baseDir)dgnMline.cpp                  \
                      $(baseDir)dgnCone.cpp                   \
                      $(baseDir)dgnSolids.cpp                 \
                      $(baseDir)dgnTexts.cpp                  \
                      $(baseDir)dgnCells.cpp                  \
                      $(baseDir)dgnImage.cpp                  \
                      $(baseDir)dgnExtElement.cpp             \
                      $(baseDir)dgnNonGraphics.cpp


MultiCompileDepends=$(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)rDwgFileIO$(oext)               : $(baseDir)rDwgFileIO.cpp $(baseDir)rDwgFileIO.h $(baseDir)rDwgInternal.h $(MstnRealDwgApi)rDwgUtil.h ${MultiCompileDepends}

$(o)rDwgFileHolder$(oext)           : $(baseDir)rDwgFileHolder.cpp $(baseDir)rDwgFileIO.h $(baseDir)rDwgFileHolder.h $(baseDir)rDwgInternal.h $(MstnRealDwgApi)rDwgUtil.h ${MultiCompileDepends}

$(o)rDwgSymbologyData$(oext)        : $(baseDir)rDwgSymbologyData.cpp $(baseDir)rDwgFileIO.h $(baseDir)rDwgFileHolder.h $(baseDir)rDwgInternal.h ${MultiCompileDepends}
    
$(o)rDwgTableIndex$(oext)           : $(baseDir)rDwgTableIndex.cpp $(baseDir)rDwgFileIO.h $(baseDir)rDwgFileHolder.h $(baseDir)rDwgInternal.h ${MultiCompileDepends}

$(o)rDwgDgnExtension$(oext)         : $(baseDir)rDwgDgnExtension.cpp $(baseDir)rDwgInternal.h $(MstnRealDwgApi)rDwgConvertContext.h $(MstnRealDwgApi)rDwgUtil.h $(includedSourceFiles) ${MultiCompileDepends}

$(o)DwgPlatformHost$(oext)          : $(baseDir)DwgPlatformHost.cpp $(MstnRealDwgApi)rDwgUtil.h $(baseDir)rDwgFileIO.h ${MultiCompileDepends}

$(o)realDwgExported$(oext)          : $(baseDir)realDwgExported.cpp $(baseDir)realDwgExported.h $(MstnRealDwgApi)rDwgUtil.h $(baseDir)rDwgInternal.h ${MultiCompileDepends}

$(o)DwgECProperties$(oext)          : $(baseDir)DwgECProperties.cpp $(baseDir)realDwgExported.h ${MultiCompileDepends}

$(o)rdViewportDrawEntity$(oext)     : $(baseDir)rdViewportDrawEntity.cpp $(baseDir)rDwgInternal.h ${MultiCompileDepends}

$(o)rdSatFileConversionMP$(oext)    : $(baseDir)rdSatFileConversionMP.cpp $(MstnRealDwgApi)rSatToPs.h ${MultiCompileDepends}

$(o)rdPsdFileConversionMP$(oext)    : $(baseDir)rdPsdFileConversionMP.cpp $(MstnRealDwgApi)rSatToPs.h ${MultiCompileDepends}

$(o)rDwgConvertEvents$(oext)        : $(baseDir)rDwgConvertEvents.cpp $(MstnRealDwgApi)rDwgConvertEvents.h ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

%include $(SharedMki)linklibrary.mki

#----------------------------------------------------------------------
# Symlink RealDwgFileIO.lib to RealDwg#Version#FileIO.lib for
# other applications to link with it.
#----------------------------------------------------------------------
%if defined (BUILD_DWGPLATFORM)
$(BuildContextSubPartsLib)$(appName)$(DLM_API_NUMBER).lib : $(o)$(appName).lib
    $(LinkFirstDepToFirstTarget)

always:
    ~linkdir "$(BuildContext)PublicAPI/$(appName)Api=$(SrcMstnPlatform)PublicAPI\Mstn\RealDwg"

%else

$(RealDwgFileIOLib)                      : $(o)$(appName).lib
    $(LinkFirstDepToFirstTarget)

%endif

#----------------------------------------------------------------------
# Create localizable resources
#----------------------------------------------------------------------
MuiBaseName             = $(DLM_NAME)$(DLM_API_NUMBER).dll
MuiTranskitDeliveryDir  = $(ContextDeliveryDir)TransKit/MdlApps/$(appName)/
%if defined (BUILD_DWGPLATFORM)
MuiTranskitSourceDir    = $(_MakeFilePath)transkit2/
%endif

%include $(SharedMki)CreateAndSymlinkMui.mki
