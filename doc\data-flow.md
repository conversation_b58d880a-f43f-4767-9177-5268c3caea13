# Data Flow Architecture

## Overview

This document describes the complete data flow through the RealDwgFileIO framework, from file opening to entity conversion and final output. Understanding these flows is crucial for debugging, optimization, and extending the framework.

## File Opening Flow (DWG → DGN)

```mermaid
graph TD
    A[DWG File] --> B[File Format Detection]
    B --> C[RealDwgFileType::ValidateFile]
    C --> D[RealDwgFileIO::Factory]
    D --> E[RealDwgFileIO Instance]
    E --> F[FileHolder Creation]
    F --> G[AcDbDatabase Loading]
    G --> H[Model Index Creation]
    H --> I[Symbology Data Initialization]
    I --> J[Ready for Conversion]
```

### Detailed File Opening Steps

1. **Format Detection**
   ```cpp
   bool RealDwgFileType::ValidateFile(DgnFileFormatType *pFormat, 
                                     int *pMajorVersion, int *pMinorVersion, 
                                     bool *pDefaultModelIs3D, 
                                     IThumbnailPropertyValuePtr*, 
                                     const WChar *pName)
   ```

2. **Database Initialization**
   ```cpp
   StatusInt RealDwgFileIO::LoadFile(DesignFileHeader* pHdr, 
                                    bool* openedReadonly, 
                                    StatusInt* rwStatus, 
                                    DgnFileOpenMode openMode, 
                                    DgnFileLoadContextP)
   ```

3. **Model Indexing**
   ```cpp
   void FileHolder::IndexAcadModels()
   {
       // Iterate through block table
       // Create RealDwgModelIndexItem for each layout
       // Build model hierarchy
   }
   ```

## Conversion Flow (DWG → DGN)

```mermaid
graph TD
    A[Model Load Request] --> B[ConvertToDgnContext Creation]
    B --> C[Coordinate Transform Setup]
    C --> D[Symbology Cache Initialization]
    D --> E[Entity Iteration Start]
    E --> F[Entity Type Dispatch]
    F --> G[Geometry Conversion]
    G --> H[Symbology Application]
    H --> I[DGN Element Creation]
    I --> J[Element Addition to Model]
    J --> K{More Entities?}
    K -->|Yes| E
    K -->|No| L[View Conversion]
    L --> M[Model Finalization]
```

### Entity Processing Pipeline

```cpp
StatusInt ConvertToDgnContext::LoadModelFromDatabase(DgnModelP seedModel, 
                                                    RealDwgModelIndexItem* pModelIndexItem)
{
    // 1. Setup phase
    InitializeConversionContext();
    SetupCoordinateTransforms();
    InitializeSymbologyMappings();
    
    // 2. Entity processing phase
    AcDbBlockTableRecordIterator* iterator;
    for (iterator->start(); !iterator->done(); iterator->step()) {
        AcDbEntity* entity;
        iterator->getEntity(entity, AcDb::kForRead);
        
        // Entity conversion pipeline
        MSElementDescrP elementDescr = nullptr;
        try {
            SaveElementToDgn(entity, &elementDescr);
            if (elementDescr) {
                AddElementToModel(elementDescr, seedModel);
            }
        } catch (DiscardInvalidEntityException& e) {
            LogConversionError(entity, e.ErrorMessage());
            continue; // Skip invalid entity
        }
        
        UpdateProgress();
    }
    
    // 3. Post-processing phase
    ProcessViewports();
    ProcessReferences();
    FinalizeModel();
    
    return SUCCESS;
}
```

## Reverse Conversion Flow (DGN → DWG)

```mermaid
graph TD
    A[DGN Model] --> B[ConvertFromDgnContext Creation]
    B --> C[Database Preparation]
    C --> D[Element Iteration]
    D --> E[Element Type Analysis]
    E --> F[Geometry Extraction]
    F --> G[AutoCAD Entity Creation]
    G --> H[Symbology Application]
    H --> I[Database Insertion]
    I --> J{More Elements?}
    J -->|Yes| D
    J -->|No| K[View Processing]
    K --> L[Database Finalization]
```

### Element Processing Pipeline

```cpp
StatusInt ConvertFromDgnContext::SaveNonCacheChanges(bool doFullSave, DgnFileP dgnFile)
{
    // 1. Preparation phase
    PrepareDatabase();
    InitializeSymbologyMappings();
    
    // 2. Element processing
    DgnModel::ElementsCollection elements = m_model->GetElementsCollection();
    for (ElementHandle eh : elements) {
        if (ShouldProcessElement(eh)) {
            AcDbObjectId objectId;
            try {
                SaveElementToDatabase(eh, &objectId);
                if (objectId.isValid()) {
                    TrackConvertedElement(eh.GetElementId(), objectId);
                }
            } catch (RealDwgException& e) {
                LogConversionError(eh, e.ErrorMessage());
                continue;
            }
        }
        UpdateProgress();
    }
    
    // 3. Post-processing
    ProcessViews();
    ProcessReferences();
    FinalizeDatabase();
    
    return SUCCESS;
}
```

## Symbology Data Flow

```mermaid
graph LR
    A[Source Symbology] --> B[Symbology Cache Lookup]
    B --> C{Cache Hit?}
    C -->|Yes| D[Return Cached Result]
    C -->|No| E[Convert Symbology]
    E --> F[Cache Result]
    F --> G[Return Converted Result]
    D --> H[Apply to Element]
    G --> H
```

### Color Conversion Flow

```cpp
ColorDef ConvertColor(const AcCmColor& acColor)
{
    // 1. Check cache first
    auto cacheIter = m_colorCache.find(acColor);
    if (cacheIter != m_colorCache.end()) {
        return cacheIter->second;
    }
    
    // 2. Perform conversion
    ColorDef dgnColor;
    if (acColor.isByACI()) {
        // ACI color conversion
        dgnColor = ConvertACIColor(acColor.colorIndex());
    } else if (acColor.isByColor()) {
        // RGB color conversion
        dgnColor = ConvertRGBColor(acColor.red(), acColor.green(), acColor.blue());
    } else {
        // Layer/block color
        dgnColor = GetDefaultColor();
    }
    
    // 3. Cache result
    m_colorCache[acColor] = dgnColor;
    
    return dgnColor;
}
```

## Multi-Threading Data Flow

```mermaid
graph TD
    A[Main Thread] --> B[Work Queue Creation]
    B --> C[Entity Distribution]
    C --> D[Worker Thread 1]
    C --> E[Worker Thread 2]
    C --> F[Worker Thread N]
    D --> G[Entity Conversion]
    E --> H[Entity Conversion]
    F --> I[Entity Conversion]
    G --> J[Result Collection]
    H --> J
    I --> J
    J --> K[Synchronization Point]
    K --> L[Final Assembly]
```

### Thread Synchronization

```cpp
class ConvertToDgnMultiProcessingContext : public ConvertToDgnContext
{
    std::queue<AcDbEntity*>     m_entityQueue;
    std::mutex                  m_queueMutex;
    std::condition_variable     m_queueCondition;
    std::vector<MSElementDescrP> m_results;
    std::mutex                  m_resultsMutex;
    
    void ProcessEntityBatch()
    {
        while (true) {
            AcDbEntity* entity = nullptr;
            
            // Get next entity from queue
            {
                std::unique_lock<std::mutex> lock(m_queueMutex);
                m_queueCondition.wait(lock, [this] { 
                    return !m_entityQueue.empty() || m_finished; 
                });
                
                if (m_entityQueue.empty() && m_finished) break;
                
                entity = m_entityQueue.front();
                m_entityQueue.pop();
            }
            
            // Convert entity
            MSElementDescrP elementDescr = nullptr;
            SaveElementToDgn(entity, &elementDescr);
            
            // Store result
            if (elementDescr) {
                std::lock_guard<std::mutex> lock(m_resultsMutex);
                m_results.push_back(elementDescr);
            }
        }
    }
};
```

## Memory Management Flow

```mermaid
graph TD
    A[Object Creation] --> B[Reference Counting]
    B --> C[Smart Pointer Management]
    C --> D[Automatic Cleanup]
    D --> E[Memory Pool Return]
    
    F[Large Object] --> G[Streaming Processing]
    G --> H[Chunk Processing]
    H --> I[Immediate Cleanup]
```

### Resource Management

```cpp
class ResourceManager
{
    // Smart pointer management
    std::vector<AcDbSmartObjectPointer<AcDbEntity>> m_entities;
    std::vector<MSElementDescrP> m_elements;
    
    // Memory pools
    MemoryPool<GeometryData> m_geometryPool;
    MemoryPool<SymbologyData> m_symbologyPool;
    
public:
    ~ResourceManager() {
        // Automatic cleanup of all managed resources
        CleanupEntities();
        CleanupElements();
        CleanupPools();
    }
    
    void ProcessLargeFile() {
        // Stream processing for memory efficiency
        while (HasMoreData()) {
            auto chunk = LoadNextChunk();
            ProcessChunk(chunk);
            ReleaseChunk(chunk); // Immediate cleanup
        }
    }
};
```

## Error Propagation Flow

```mermaid
graph TD
    A[Error Occurrence] --> B[Exception Thrown]
    B --> C[Context Handler]
    C --> D{Critical Error?}
    D -->|Yes| E[Abort Conversion]
    D -->|No| F[Log Error]
    F --> G[Continue Processing]
    E --> H[Cleanup Resources]
    G --> I[Update Progress]
    H --> J[Report Failure]
    I --> K[Next Entity]
```

### Error Handling Strategy

```cpp
void ConvertToDgnContext::SaveElementToDgn(AcDbEntity* pEntity, MSElementDescrP* ppDescr)
{
    try {
        // Attempt conversion
        PerformEntityConversion(pEntity, ppDescr);
    }
    catch (DiscardInvalidEntityException& e) {
        // Non-critical error - log and continue
        LogError(L"Skipping invalid entity: %s", e.ErrorMessage());
        *ppDescr = nullptr;
        m_statistics.invalidEntities++;
    }
    catch (FileHolderException& e) {
        // Critical error - abort conversion
        LogError(L"Database error: %s", e.ErrorMessage());
        throw; // Re-throw to abort conversion
    }
    catch (std::exception& e) {
        // Unexpected error - log and continue with fallback
        LogError(L"Unexpected error: %s", e.what());
        CreateFallbackElement(pEntity, ppDescr);
        m_statistics.fallbackEntities++;
    }
}
```

## Progress Monitoring Flow

```mermaid
graph LR
    A[Processing Start] --> B[Progress Meter Creation]
    B --> C[Total Work Estimation]
    C --> D[Work Unit Processing]
    D --> E[Progress Update]
    E --> F{User Cancelled?}
    F -->|Yes| G[Cleanup & Exit]
    F -->|No| H{More Work?}
    H -->|Yes| D
    H -->|No| I[Completion]
```

### Progress Tracking

```cpp
class ProgressTracker
{
    AcDbProgressMeterP m_progressMeter;
    int m_totalEntities;
    int m_processedEntities;
    
public:
    void Initialize(int totalEntities) {
        m_totalEntities = totalEntities;
        m_processedEntities = 0;
        
        if (m_progressMeter) {
            m_progressMeter->setLimit(totalEntities);
            m_progressMeter->start(L"Converting entities...");
        }
    }
    
    bool UpdateProgress() {
        m_processedEntities++;
        
        if (m_progressMeter) {
            m_progressMeter->meterProgress();
            
            // Check for user cancellation
            if (m_progressMeter->isInterruptRequested()) {
                return false; // User cancelled
            }
        }
        
        return true; // Continue processing
    }
};
```
