/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnLinear.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/11
+===============+===============+===============+===============+===============+======*/
struct          ILinestringElementMapping
{
virtual ClosedMapping   GetClosedElementMapping (bool useFilledMapping, size_t numPoints, ConvertFromDgnContextR context) const { return Closed_Polyline; }
virtual AcRxClass*      GetPlannerObjectType (ElementHandleCR inElement, ConvertFromDgnContextR context) const { return AcDbPolyline::desc(); }
virtual bool            IsElementLinear (ElementHandleCR inElement) const { return true; }
};  // ILinestringElementMapping


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtLine : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, ConvertFromDgnContextR context) const
    {
    MSElementCP         elem       = elemHandle.GetElementCP();

    // see if it has infinite length.
    bool                infiniteStart, infiniteEnd;
    if (mdlLinkage_getInfiniteLine (&infiniteStart, &infiniteEnd, elem))
        {
        if (infiniteStart)
            return AcDbXline::desc();
        else
            return AcDbRay::desc();
        }

    // see if is a point.
    if ( ((DgnElementClass)elem->hdr.dhdr.props.b.elementClass != DgnElementClass::Dimension) && context.GetFileHolder().DisplayPointAsDot() )
        {
        DPoint3d        startPoint, endPoint;
        CurveVectorPtr  pathCurve = ICurvePathQuery::ElementToCurveVector (elemHandle);

        if (pathCurve.IsValid () && pathCurve->GetStartEnd (startPoint, endPoint))
            {
            if (startPoint.IsEqual (endPoint))
                return AcDbPoint::desc();
            }
        }

    // if non-zero width, needs to be represented by an AcDbPolyLine.
    bool            hasConstantWidth;
    bool            hasNonZeroWidth;
    double          constantWidth;
    context.ExtractPolylineWidth (hasConstantWidth, constantWidth, hasNonZeroWidth, elemHandle);
    if (hasNonZeroWidth)
        return AcDbPolyline::desc();

    // not infinite, not a point, zero width -> make a regular line.
    return AcDbLine::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbLineFromLine (AcDbLine* acLine, ElementHandleR elemHandle, ConvertFromDgnContextR context, DPoint3d* points) const
    {
    MSElementDescrCP        inElement   = elemHandle.GetElementDescrCP();

    double                  linkageThickness;
    DPoint3d                linkageNormal, currentNormal;
    RealDwgStatus           status      = MstnElementUnacceptable;

    if (context.GetEntityThicknessFromElementLinkage (linkageThickness, linkageNormal, &RealDwgUtil::DPoint3dFromGeVector3d (currentNormal, acLine->normal()), elemHandle))
        {
        acLine->setThickness (linkageThickness);
        acLine->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (linkageNormal));
        }

    acLine->setStartPoint (RealDwgUtil::GePoint3dFromDPoint3d (points[0]));
    acLine->setEndPoint (RealDwgUtil::GePoint3dFromDPoint3d (points[1]));

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbRayFromLine (AcDbRay* acRay, ElementHandleR elemHandle, ConvertFromDgnContextR context, DPoint3d* points) const
    {
    DPoint3d        direction;
    direction.NormalizedDifference (points[1], points[0]);
    acRay->setBasePoint (RealDwgUtil::GePoint3dFromDPoint3d (points[0]));
    acRay->setUnitDir (RealDwgUtil::GeVector3dFromDPoint3d (direction));

    return      RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbXLineFromLine (AcDbXline* acXLine, ElementHandleR elemHandle, ConvertFromDgnContextR context, DPoint3d* points) const
    {
    DPoint3d        direction, origin;
    direction.NormalizedDifference (points[1], points[0]);
    origin.sumOf (NULL, &points[0], .5, &points[1], .5);

    acXLine->setBasePoint (RealDwgUtil::GePoint3dFromDPoint3d (origin));
    acXLine->setUnitDir (RealDwgUtil::GeVector3dFromDPoint3d (direction));

    return      RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbPointFromLine (AcDbPoint* acPoint, ElementHandleR elemHandle, ConvertFromDgnContextR context, DPoint3d* points) const
    {
    acPoint->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (points[0]));
    return      RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbPolylineFromLine (AcDbPolyline* acPolyline, ElementHandleR elemHandle, ConvertFromDgnContextR context, DPoint3d* points) const
    {
    // Here we have a line that has a non-zero width, so it must be represented as a polyline.

    // Get the polyline data from the line.
    RealDwgStatus   status;
    double          extrusionDistance;
    double          elevation = 0.0;
    Transform       compositeTransform;
    DPoint3d        normal;
    DPoint3d        headerNormal;

    // get default normal.
    DPoint3d        defaultNormal;
    RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, acPolyline->normal());
    if (RealDwgSuccess != (status = context.ExtractPolylineData (extrusionDistance, compositeTransform, normal, headerNormal, elevation, elemHandle, defaultNormal)))
        return status;

    double                  startWidth, endWidth;
    context.ExtractWidthFromElement (startWidth, endWidth, elemHandle);

    acPolyline->reset (false, 0);
    acPolyline->addVertexAt (0, RealDwgUtil::GePoint2dFromDPoint3d(points[0]));
    acPolyline->addVertexAt (1, RealDwgUtil::GePoint2dFromDPoint3d(points[1]));

    if (startWidth == endWidth)
        acPolyline->setConstantWidth (startWidth);
    else
        acPolyline->setWidthsAt (0, startWidth, endWidth);

    acPolyline->setClosed (false);
    acPolyline->setPlinegen (true);

    // RealDWG must be reseting elevation when setting vertices, so do this after all points being set.
    acPolyline->setElevation (elevation);
    acPolyline->setThickness (extrusionDistance);
    acPolyline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (headerNormal));

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // The conversion from DWG creates a top-level line from AcDbLine, AcDbRay, AcDbXLine, and AcDbPoint methods.
    // (Line Elements are also created as part of complex elements generated from AcDbHatch, AcDbMText, and dimensions.)

    // First find out which kind of AcDbObject we want.
    AcRxClass*              typeNeeded = GetTypeNeeded (elemHandle, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    // get the points
    DPoint3d        points[2];
    CurveVectorPtr  pathCurve = ICurvePathQuery::ElementToCurveVector (elemHandle);

    if (!pathCurve.IsValid () || !pathCurve->GetStartEnd (points[0], points[1]))
        return NoConversionMethod;

    context.GetTransformFromDGN().Multiply (points, 2);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (context.GetSettings().IsZeroZCoordinateEnforced())
        points[0].z = points[1].z = 0.0;

    // here we have created an AcDbObject of some type. Populate it according to type.
    AcDbLine*   acLine;
    if (NULL != (acLine = AcDbLine::cast (acObject)))
        return SetAcDbLineFromLine (acLine, elemHandle, context, points);

    AcDbRay*    acRay;
    if (NULL != (acRay = AcDbRay::cast (acObject)))
        return SetAcDbRayFromLine (acRay, elemHandle, context, points);

    AcDbXline*  acXLine;
    if (NULL != (acXLine = AcDbXline::cast (acObject)))
        return SetAcDbXLineFromLine (acXLine, elemHandle, context, points);

    AcDbPoint*  acPoint;
    if (NULL != (acPoint = AcDbPoint::cast (acObject)))
        return SetAcDbPointFromLine (acPoint, elemHandle, context, points);

    AcDbPolyline*   acPolyline;
    if (NULL != (acPolyline = AcDbPolyline::cast (acObject)))
        return SetAcDbPolylineFromLine (acPolyline, elemHandle, context, points);

    // should have taken one of the paths above.
    assert (false);
    return NoConversionMethod;
    }
};  // ToDwgExtLine



/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   02/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtLineString : public ToDwgExtension, public ILinestringElementMapping
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, AcDbObjectP existingObject, ConvertFromDgnContextR context, PolylineInfoCR polylineInfo) const
    {
    // if only one point, turn it into a point.
    if (1 == polylineInfo.GetPointCount())
        return AcDbPoint::desc();

    // try to preserve the type of the existing object, which might be a 2D or 3D "heavyweight" polyline.
    if (NULL != existingObject)
        {
        if (existingObject->isKindOf(AcDb3dPolyline::desc()))
            return AcDb3dPolyline::desc();
        else if (existingObject->isKindOf(AcDb2dPolyline::desc()))
            return AcDb2dPolyline::desc();
        }

    // if it's nonplanar, we have to make a heavyweight 3d polyline.
    MSElementCP elem = elemHandle.GetElementCP();
    if ( (context.GetThreeD() && context.IsNonPlanarPolyline (elemHandle, LineString_LWPolyline == context.GetSettings().GetLineStringMapping(false))) ||
              ( (LineString_3DPolyline == context.GetSettings().GetLineStringMapping (true)) && ((0 != elem->hdr.dhdr.range.zlowlim) || (0 != elem->hdr.dhdr.range.zhighlim)) ) )
        return AcDb3dPolyline::desc();
    else 
        return this->GetPlannerObjectType(elemHandle, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*              GetPlannerObjectType (ElementHandleCR inElement, ConvertFromDgnContextR context) const override
    {
    // simple linestring element contains only linear segments - always a polyline.
    return  AcDbPolyline::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    IsElementLinear (ElementHandleCR inElement)
    {
    // a linestring is always linear
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbPointFromLineString (AcDbPoint* acPoint, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoCR polylineInfo) const
    {
    // transform the point to target units
    DPoint3d    point = *polylineInfo.FirstPoint ();
    context.GetTransformFromDGN().Multiply (point);

    acPoint->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (point));
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbPolylineFromLineString (AcDbPolyline* acPolyline, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    // Get the polyline data from the line string.
    RealDwgStatus   status;
    double          extrusionDistance;
    double          elevation = 0.0;
    Transform       compositeTransform;
    DPoint3d        normal;
    DPoint3d        headerNormal;

    // get default normal.
    DPoint3d        defaultNormal;
    RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, acPolyline->normal());
    if (RealDwgSuccess != (status = context.ExtractPolylineData (extrusionDistance, compositeTransform, normal, headerNormal, elevation, elemHandle, defaultNormal)))
        return status;

    // transform 3D polyline points to 2D polyline points in target units
    DPoint3dP       points = polylineInfo.FirstPointP ();
    compositeTransform.Multiply (points, points, (int)polylineInfo.GetPointCount());

    // set the points from the polyline info.
    SetFromPolylineInfo* setFromPolyline = (SetFromPolylineInfo*) dynamic_cast <ToDgnExtPolyline*> (ACRX_X_CALL (acPolyline, ToDgnExtension));
    assert (NULL != setFromPolyline);
    if (RealDwgSuccess != (status = setFromPolyline->VerticesFromPolylineInfo (acPolyline, polylineInfo, elemHandle, context)))
        return status;

    // figure out the linestyle stuff
    Int32           style = 0;
    bool            continueLt = false;
    context.GetLinestyleFromElement (style, continueLt, elemHandle);

    acPolyline->setPlinegen (continueLt);

    // RealDWG must be resetting elevation when setting vertices, so do this after all points being set.
    acPolyline->setElevation (elevation);
    acPolyline->setThickness (extrusionDistance);
    acPolyline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (headerNormal));

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDb3dPolylineFromLineString (AcDb3dPolyline* acPolyline, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    size_t  numPoints = polylineInfo.GetPointCount();
    bool    closed    = polylineInfo.IsClosed();
    if (closed)
        numPoints--;

    if (numPoints < 2)
        {
#if defined (DIAGNOSTIC)
        if (com.bentley.sys.Diagnostic.INSTRUMENTED)
            System.out.println ("Ignoring 3DPolyline with less than 2 vertices");
#endif
        return InvalidPolygonVertexCount;
        }

    // transform 3D polyline points to target units
    DPoint3dP   points = polylineInfo.FirstPointP ();
    context.GetTransformFromDGN().Multiply (points, points, (int)numPoints);

    acPolyline->setPolyType (AcDb::k3dSimplePoly);

    if ( (false != closed) != (Adesk::kFalse != acPolyline->isClosed()) )
        closed ? acPolyline->makeClosed() : acPolyline->makeOpen();

    // Need header as symbology template for vertices
    context.UpdateEntityPropertiesFromElement (acPolyline, elemHandle);

    // set the points from the polyline info.
    SetFromPolylineInfo* setFromPolyline = (SetFromPolylineInfo*) dynamic_cast <ToDgnExt3dPolyline*> (ACRX_X_CALL (acPolyline, ToDgnExtension));
    assert (NULL != setFromPolyline);

    return setFromPolyline->VerticesFromPolylineInfo (acPolyline, polylineInfo, elemHandle, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDb2dPolylineFromLineString (AcDb2dPolyline* acPolyline, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    if (polylineInfo.GetPointCount() < 2)
        {
#if defined (DIAGNOSTIC)
        if (com.bentley.sys.Diagnostic.INSTRUMENTED)
            System.out.println ("Ignoring 2DPolyline with less than 2 vertices");
#endif
        return InvalidPolygonVertexCount;
        }

    // Get the polyline data from the line string.
    RealDwgStatus   status;
    double          extrusionDistance;
    double          elevation;
    Transform       compositeTransform;
    DPoint3d        normal;
    DPoint3d        headerNormal;
    // get default normal.
    DPoint3d        defaultNormal;
    RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, acPolyline->normal());

    if (RealDwgSuccess != (status = context.ExtractPolylineData (extrusionDistance, compositeTransform, normal, headerNormal, elevation, elemHandle, defaultNormal)))
        return status;

    // transform 3D polyline points to 2D polyline points in target units
    DPoint3dP       points = polylineInfo.FirstPointP ();
    compositeTransform.Multiply (points, points, (int)polylineInfo.GetPointCount());

    // Need header as symbology template for vertices!!
    context.UpdateEntityPropertiesFromElement (acPolyline, elemHandle);

    acPolyline->setElevation (elevation);
    acPolyline->setThickness (extrusionDistance);
    acPolyline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (headerNormal));

    // set the points from the polyline info.
    SetFromPolylineInfo* setFromPolyline = (SetFromPolylineInfo*) dynamic_cast <ToDgnExt2dPolyline*> (ACRX_X_CALL (acPolyline, ToDgnExtension));
    assert (NULL != setFromPolyline);
    if (RealDwgSuccess != (status = setFromPolyline->VerticesFromPolylineInfo (acPolyline, polylineInfo, elemHandle, context)))
        return status;

    // figure out the linestyle stuff
    Int32           style = 0;
    bool            continueLt = false;
    context.GetLinestyleFromElement (style, continueLt, elemHandle);

    continueLt ? acPolyline->setLinetypeGenerationOn() : acPolyline->setLinetypeGenerationOff ();

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbSplineFromLinestring (AcDbSpline* acSpline, ElementHandleR inElement, ConvertFromDgnContextR context, PolylineInfoCR polylineInfo) const
    {
    CurveVectorPtr  curveVector = ICurvePathQuery::ElementToCurveVector (inElement);
    if (curveVector.IsNull() || curveVector->size() < 1)
        return  MstnElementUnacceptable;

    ToDwgExtSpline  splineExt;
    return splineExt.SetAcDbSplineFromCurveVector (acSpline, *curveVector.get(), context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertLinestringChainToDwg
(
AcDbObjectP&            acObject,
AcDbObjectP             existingObject,
MSElementDescrP         chainElmdscr,
ConvertFromDgnContextR  context
) const
    {
    RealDwgStatus   status = RealDwgSuccess;
    UInt32          count = 0;

    for (MSElementDescrP elmdscr = chainElmdscr; NULL != elmdscr; elmdscr = elmdscr->h.next)
        {
        AutoRestore<MSElementDescrP> saveNext (&elmdscr->h.next, nullptr);
        EditElementHandle   currElem(elmdscr, false, false, context.GetModel());
        if (0 == count++)
            {
            // first linestring preserves header ID:
            status = this->ConvertLinestringToDwg (acObject, existingObject, currElem, context);
            }
        else
            {
            // add rest of the segments to database
            AcDbObjectP newObject = NULL;
            if (RealDwgSuccess != this->ConvertLinestringToDwg(newObject, NULL, currElem, context))
                DIAGNOSTIC_PRINTF ("Failed to convert a linestring segment borken up from element ID=%I64d\n", chainElmdscr->el.ehdr.uniqueId);

            if (nullptr != newObject)
                {
                AcDbEntityP entity = AcDbEntity::cast (newObject);
                if (nullptr != entity)
                    {
                    if (!newObject->objectId().isValid())
                        context.AddEntityToCurrentBlock (entity, 0);
                    context.UpdateEntityPropertiesFromElement (entity, currElem);
                    }
                newObject->close ();
                }
            }
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertLinestringToDwg
(
AcDbObjectP&            acObject,
AcDbObjectP             existingObject,
ElementHandleR          inElement,
ConvertFromDgnContextR  context
) const
    {
    DPoint3dP       rawPoints = NULL;
    UInt32          numPoints = RealDwgUtil::ExtractPointsFromElement (rawPoints, inElement);
    if (0 == numPoints)
        return  MstnElementUnacceptable;
    
    // make them an array which can be passed into PolylineInfo. Do not transform points here - will decide on 2D vs 3D per entity type.
    DPoint3dArray   points;
    points.SetPoints (rawPoints, numPoints, NULL, context.GetSettings().IsZeroZCoordinateEnforced());

    free (rawPoints);

    // get the widths.
    double          startWidth, endWidth;
    context.ExtractWidthFromElement (startWidth, endWidth, inElement);

    PolylineInfo    polylineInfo (points, startWidth, endWidth, false);

    return  this->ConvertPolylineInfoToObject (acObject, existingObject, polylineInfo, inElement, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertPolylineInfoToObject
(
AcDbObjectP&            acObject,
AcDbObjectP             existingObject,
PolylineInfoR           polylineInfo,
ElementHandleR          inElement,
ConvertFromDgnContextR  context
) const
    {
    // find the type needed, and instantiate it.
    AcRxClass*      typeNeeded = GetTypeNeeded (inElement, existingObject, context, polylineInfo);
    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    // here we have created an AcDbObject of some type. Populate it according to type.
    AcDbPoint*      acPoint;
    if (NULL != (acPoint = AcDbPoint::cast (acObject)))
        return SetAcDbPointFromLineString (acPoint, inElement, context, polylineInfo);

    AcDbPolyline*   acPolyline;
    if (NULL != (acPolyline = AcDbPolyline::cast (acObject)))
        return SetAcDbPolylineFromLineString (acPolyline, inElement, context, polylineInfo);

    AcDb3dPolyline* ac3dPolyline;
    if (NULL != (ac3dPolyline = AcDb3dPolyline::cast (acObject)))
        return SetAcDb3dPolylineFromLineString (ac3dPolyline, inElement, context, polylineInfo);

    AcDb2dPolyline* ac2dPolyline;
    if (NULL != (ac2dPolyline = AcDb2dPolyline::cast (acObject)))
        return SetAcDb2dPolylineFromLineString (ac2dPolyline, inElement, context, polylineInfo);

    AcDbSpline*     acSpline;
    if (NULL != (acSpline = AcDbSpline::cast (acObject)))
        return  SetAcDbSplineFromLinestring (acSpline, inElement, context, polylineInfo);

    // should have taken one of the paths above.
    assert (false);
    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     HasDiscontinuities (ElementHandleCR elementIn)
    {
    DPoint3dP   rawPoints = NULL;
    UInt32      numPoints = RealDwgUtil::ExtractPointsFromElement (rawPoints, elementIn);
    if (0 == numPoints)
        return  false;

    for (UInt32 i = 0; i < numPoints; i++)
        if (DISCONNECT == rawPoints[i].x || DISCONNECT == rawPoints[i].y)
            return  true;

    free (rawPoints);

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ParseDiscontinuities (MSElementDescrH chainElemsOut, ElementHandleCR elementIn)
    {
    // break down discontinuities to separated line string elements.
    DPoint3dP           rawPoints = NULL;
    UInt32              numPoints = RealDwgUtil::ExtractPointsFromElement (rawPoints, elementIn);
    if (0 == numPoints)
        return  false;

    DgnModelRefP        modelRef = elementIn.GetModelRef ();
    MSElementDescrP     chainTail = NULL;
    MSElementCP         elem = elementIn.GetElementCP ();
    DPoint3dCP          start = &rawPoints[0];
    bool                isDisconnected = false;
    UInt32              count = 0;

    for (UInt32 i = 0; i < numPoints; i++, count++)
        {
        if (DISCONNECT == rawPoints[i].x || DISCONNECT == rawPoints[i].y)
            {
            isDisconnected = true;

            if (count > 1)
                {
                EditElementHandle   newSegment;
                if (SUCCESS == LineStringHandler::CreateLineStringElement (newSegment, &elementIn, start, count, elem->hdr.dhdr.props.b.is3d, *modelRef))
                    MSElementDescr::InitOrAddToChainWithTail (chainElemsOut, &chainTail, newSegment.ExtractElementDescr());
                }

            start = &rawPoints[i+1];
            count = -1;
            }
        }

    if (count > 1 && isDisconnected)
        {
        EditElementHandle   lastSegment;

        if (SUCCESS == LineStringHandler::CreateLineStringElement (lastSegment, &elementIn, start, count, elem->hdr.dhdr.props.b.is3d, *modelRef))
            MSElementDescr::InitOrAddToChainWithTail (chainElemsOut, &chainTail, lastSegment.ExtractElementDescr());
        }

    free (rawPoints);

    if (NULL != chainElemsOut && NULL != *chainElemsOut)
        {
        // use the same elementRef for the new element such that an adaptor(e.g. MfmDwgExporter) can still get its Handler from the new element.
        (*chainElemsOut)->h.elementRef = elementIn.GetElementRef ();
        (*chainElemsOut)->h.dgnModelRef = modelRef;
        }

    return  isDisconnected;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    MSElementDescrP     chainElmdscr = NULL;
    if (ToDwgExtLineString::ParseDiscontinuities (&chainElmdscr, elemHandle))
        {
        if (NULL == chainElmdscr)
            return  MstnElementUnacceptable;

        // preserve the input linestring's unique ID on the first polyline segment we will add to DWG:
        chainElmdscr->el.ehdr.uniqueId = elemHandle.GetElementCP()->ehdr.uniqueId;
        // save the chain of linestring to dwg
        RealDwgStatus   status = this->ConvertLinestringChainToDwg (acObject, existingObject, chainElmdscr, context);

        chainElmdscr->Release ();
        return  status;
        }

    return this->ConvertLinestringToDwg (acObject, existingObject, elemHandle, context);
    }

};  // ToDwgExtLineString



/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   02/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtShape : public ToDwgExtLineString
{
    mutable bool    m_isFilled;         // either solid or gradient fill
    mutable bool    m_isSolidFilled;    // filled with a solid color
    mutable bool    m_isGradientFilled; // filled with a gradient color
    mutable bool    m_isOutlined;       // display outline
    mutable bool    m_isPatterned;      // has pattern

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, AcDbObjectP existingObject, ConvertFromDgnContextR context, PolylineInfoCR polylineInfo) const
    {
    // the DWG element we can use depends on whether the element is filled, etc.
    GradientSymbPtr     gradientSymb = GradientSymb::Create();
    UInt32              fillColor = 0, outlineColor = 0;

    context.IsFilledOrPatterned (elemHandle, &m_isSolidFilled, &m_isGradientFilled, &m_isOutlined, &m_isPatterned, &fillColor, &outlineColor, gradientSymb.get());
    
    // either solid or gradient fill is a fill
    m_isFilled = m_isSolidFilled || m_isGradientFilled;

    // if the shape element is from surface extrusion, create it as a 2D polyline:
    if (0 != (context.GetSaveElementPurpose() & SAVEELEMENTPURPOSE_ForExtrusion))
        return  AcDb2dPolyline::desc();

    // if the element has thickness, we use the line string case, except for existing SOLID or TRACE entities.
    double          thickness;
    DPoint3d        extrusionDirection;
    if (context.GetEntityThicknessFromElementLinkage(thickness, extrusionDirection, NULL, elemHandle))
        {
        // extruded SOLID or TRACE entities stay as they are - no mapping takes place on them:
        if (NULL != existingObject && existingObject->isKindOf(AcDbTrace::desc()))
            return  AcDbTrace::desc();
        else if (NULL != existingObject && existingObject->isKindOf(AcDbSolid::desc()))
            return  AcDbSolid::desc();

        return __super::GetTypeNeeded (elemHandle, existingObject, context, polylineInfo);
        }

    // if the element has width, also use the line string case.
    double          width0;
    double          width1;
    if (context.ExtractWidthFromElement (width0, width1, elemHandle))
        return __super::GetTypeNeeded (elemHandle, existingObject, context, polylineInfo);

    bool            isLinear = this->IsElementLinear (elemHandle);

    // A solid filled element with background fill color should be a wipeout:
    if (m_isSolidFilled && DWG_COLOR_Background255 == fillColor && isLinear)
        return AcDbWipeout::desc();

    // don't try to map entity types when editing DWG elements
    if (NULL != existingObject)
        return  existingObject->isA();

    bool            useFilledMapping    = m_isFilled && !m_isPatterned;
    bool            isNonPlanar         = context.IsNonPlanarPolyline (elemHandle, false);
    size_t          numPoints           = polylineInfo.GetPointCount();
    ClosedMapping   elementMapping      = this->GetClosedElementMapping (useFilledMapping, numPoints, context);

    switch (elementMapping)
        {
        case Closed_SolidOrFace:
            if (m_isFilled)
                return m_isGradientFilled ? AcDbHatch::desc() : AcDbSolid::desc();
            else
                return AcDbFace::desc();
            break;

        case Closed_Polyface:
            if (isLinear)
                return AcDbPolyFaceMesh::desc();
            else if (isNonPlanar)
                return AcDb3dPolyline::desc();
            else
                return this->GetPlannerObjectType (elemHandle, context);
            break;

        case Closed_Hatch:
            return isNonPlanar ? AcDbPolyFaceMesh::desc() : AcDbHatch::desc();

        case Closed_Region:
            return isNonPlanar ? AcDbPolyFaceMesh::desc() : AcDbRegion::desc();

        default:
        case Closed_Polyline:
            if (isNonPlanar)
                return AcDb3dPolyline::desc();
            else
                return this->GetPlannerObjectType (elemHandle, context);
            break;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
ClosedMapping           GetClosedElementMapping (bool useFilledMapping, size_t numPoints, ConvertFromDgnContextR context) const override
    {
    return (numPoints <= 5) ? context.GetSettings().GetTriOrQuadMapping (useFilledMapping, context.GetThreeD()) : context.GetSettings().GetPolygonMapping (useFilledMapping, context.GetThreeD());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*              GetPlannerObjectType (ElementHandleCR inElement, ConvertFromDgnContextR context) const override
    {
    // shape element contains only linear segments - always a polyline.
    return  AcDbPolyline::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    IsElementLinear (ElementHandleCR inElement) const override
    {
    // a shape element is always linear
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbWipeoutFromShape (AcDbWipeout* acWipeout, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    int             numPoints = (int) polylineInfo.GetPointCount ();
    DPoint3dArray   points;
    points.SetPoints (polylineInfo.FirstPoint(), numPoints, &context.GetTransformFromDGN(), context.GetSettings().IsZeroZCoordinateEnforced());

    return  context.SetAcDbWipeoutFromPoints (acWipeout, &points.front(), numPoints);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbHatchFromShape (AcDbHatch* acHatch, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    RealDwgStatus       status = BadHatch;
    AcDbObjectId        fillHatchId;

    // determine if we need to create 2 hatches for a filled + patterned case:
    bool                create2Hatches = context.ShouldSeperateFillAndPattern(m_isSolidFilled, m_isGradientFilled, m_isPatterned);

    if (create2Hatches)
        {
        // add a filled hatch first
        AcDbHatch*      filledHatch = new AcDbHatch ();
        if (context.AddEntityToCurrentBlock(filledHatch, 0).isValid())
            status = context.HatchFromElement (filledHatch, elemHandle, NULL, false, true);

        if (RealDwgSuccess != status)
            {
            if (NULL != filledHatch && filledHatch->objectId().isValid())
                filledHatch->erase ();
            else if (NULL != filledHatch)
                delete filledHatch;
            }
        else if ((fillHatchId = filledHatch->objectId()).isValid())
            {
            if (m_isOutlined)
                filledHatch->setAssociative (true);
            filledHatch->close ();
            }

        // set this hatch as the patterned hatch
        status = context.HatchFromElement (acHatch, elemHandle, NULL, true, false);
        }
    else
        {
        // a single fill/pattern or a composite hatch for ACAD R2011 and up
        status = context.HatchFromElement (acHatch, elemHandle, NULL, m_isPatterned, m_isFilled);
        }

    // set the fill hatch in lower priority display order than pattern hatch, which shall be set to default relative order later on:
    PrioritySorter*     prioritySorter = context.GetPrioritySorter ();
    if (RealDwgSuccess == status && NULL != prioritySorter)
        {
        // first draw the new filled hatch
        if (fillHatchId.isValid())
            prioritySorter->AddElement (elemHandle, -1, context.ElementIdFromObjectId(fillHatchId));
        // then draw the pattern hatch (needed for element sequential sorting in a cell)
        prioritySorter->AddElement (elemHandle);
        }

    // add an outlined boundary
    if (m_isOutlined)
        {
        AcDbObjectId    polylineId = this->AddFramePolyline (elemHandle, context, polylineInfo);
        if (polylineId.isValid())
            {
            // make the hatch associative to the new polyline boundary
            if (RealDwgSuccess == status && NULL != acHatch)
                {
                AcDbObjectIdArray   idArray;
                idArray.append (polylineId);

                UInt32  loopIndex = acHatch->numLoops ();
                if (loopIndex > 0)
                    loopIndex -= 1;

                if (RealDwgSuccess == SetAssociativeIdsForLoop(acHatch, loopIndex, idArray) && !acHatch->objectId().isValid())
                    context.AddEntityToCurrentBlock (acHatch, elemHandle.GetElementId());
                }
            
            // add both the fill & pattern hatches as the reactors to the polyline
            AcDbEntityPointer   polylineEntity (polylineId, AcDb::kForWrite);
            if (Acad::eOk == polylineEntity.openStatus())
                {
                if (RealDwgSuccess == status && NULL != acHatch && acHatch->objectId().isValid())
                    polylineEntity->addPersistentReactor (acHatch->objectId());
                if (fillHatchId.isValid())
                    polylineEntity->addPersistentReactor (fillHatchId);
                }

            // draw the outlined frame at the last
            if (NULL != prioritySorter)
                prioritySorter->AddElement (elemHandle, 1, context.ElementIdFromObjectId(polylineId));
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/13
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    AddFramePolyline (ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    AcDbObjectId    newId;
    AcDbPolyline*   newPolyline = new AcDbPolyline ();

    RealDwgStatus   status = this->SetAcDbPolylineFromLineString (newPolyline, elemHandle, context, polylineInfo);

    if (RealDwgSuccess == status)
        {
        if (newPolyline->isNewObject())
            context.AddEntityToCurrentBlock (newPolyline, 0);

        context.UpdateEntityPropertiesFromElement (newPolyline, elemHandle);

        newId = newPolyline->objectId ();

        if (newId.isValid())
            newPolyline->close ();

        return  newId;
        }

    delete newPolyline;

    return  newId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbSolidFromShape (AcDbSolid* acSolid, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    size_t              numPoints = polylineInfo.GetPointCount();
    bool                closed = polylineInfo.IsClosed();

    if (closed && (numPoints == 4 || numPoints == 5))
        {
        numPoints--;          // SCP.

        DPoint3dArray   points;
        points.SetPoints (polylineInfo.FirstPoint(), numPoints, &context.GetTransformFromDGN(), context.GetSettings().IsZeroZCoordinateEnforced());

        DPoint3d        origin, normal;
        if (bsiGeom_planeThroughPoints (&normal, &origin, &points.front(), (int)numPoints))
            {
            normal.normalize ();
            acSolid->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (normal));
            }

        if (4 == numPoints)
            RealDwgUtil::SwapPoints (points[2], points[3]);

        for (int iPoint = 0; iPoint < (int)numPoints; iPoint++)
            acSolid->setPointAt ((Adesk::UInt16)iPoint, RealDwgUtil::GePoint3dFromDPoint3d (points[iPoint]));

        if (3 == numPoints)
            acSolid->setPointAt (3, RealDwgUtil::GePoint3dFromDPoint3d (points[2]));

        double          thickness = 0.0;
        DPoint3d        thicknessDirection;
        if (context.GetEntityThicknessFromElementLinkage(thickness, thicknessDirection, &normal, elemHandle))
            {
            acSolid->setNormal (RealDwgUtil::GeVector3dFromDPoint3d(normal));
            acSolid->setThickness (thickness);
            }

        // add an outlined boundary
        if (m_isOutlined)
            {
            AcDbObjectId    objectId = this->AddFramePolyline (elemHandle, context, polylineInfo);

            if (objectId.isValid())
                {
                PrioritySorter*     prioritySorter = context.GetPrioritySorter ();
                if (NULL != prioritySorter)
                    {
                    // draw solid first
                    prioritySorter->AddElement (elemHandle);
                    // draw outline frame
                    prioritySorter->AddElement (elemHandle, 1, context.ElementIdFromObjectId(objectId));
                    }
                }
            }

        return RealDwgSuccess;
        }

    return MstnElementUnacceptable;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbTraceFromShape(AcDbTrace* acTrace, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    // A TRACE entity has exactly 4 vertices!
    size_t          numPoints = polylineInfo.GetPointCount ();
    if (numPoints < 4 || numPoints > 5)
        return MstnElementUnacceptable;

    DPoint3dP       points = polylineInfo.FirstPointP ();
    RealDwgUtil::SwapPoints (points[2], points[3]);

    DPoint3d        normal;
    DPoint3d        origin;
    if (bsiGeom_planeThroughPoints (&normal, &origin, points, (int)numPoints))
        {
        context.GetLocalTransform().MultiplyMatrixOnly (normal);
        normal.Normalize ();
        acTrace->setNormal (RealDwgUtil::GeVector3dFromDPoint3d(normal));
        }

    // force close at the 4th vertex:
    if (numPoints > 4)
        numPoints = 4;

    bool            zeroZ = context.GetSettings().IsZeroZCoordinateEnforced ();
    for (size_t i = 0; i < numPoints; i++)
        {
        // transform DGN points to DWG
        context.GetTransformFromDGN().Multiply (points[i]);
        
        if (zeroZ)
            points[i].z = 0.0;
        acTrace->setPointAt ((UInt16) i, RealDwgUtil::GePoint3dFromDPoint3d(points[i]));
        }

    double          thickness;
    DPoint3d        thicknessDirection;
    if (context.GetEntityThicknessFromElementLinkage(thickness, thicknessDirection, &normal, elemHandle))
        {
        acTrace->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (normal));
        acTrace->setThickness (thickness);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbFaceFromShape (AcDbFace* acFace, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    size_t              numPoints = polylineInfo.GetPointCount();
    bool                closed = polylineInfo.IsClosed();

    // A FACE entity has exactly 4 vertices!
    if (numPoints < 4 || numPoints > 5 || !closed)
        return MstnElementUnacceptable;

    bool                zeroZ = context.GetSettings().IsZeroZCoordinateEnforced();
    DPoint3dP           points = polylineInfo.FirstPointP();
    for (size_t iPoint = 0; iPoint < numPoints - 1; iPoint++)
        {
        // transform DGN points to DWG
        context.GetTransformFromDGN().Multiply (points[iPoint]);

        // if user wants to remove z-coordinates in DWG file, do it now:
        if (zeroZ)
            points[iPoint].z = 0.0;

        acFace->setVertexAt ((UInt16) iPoint, RealDwgUtil::GePoint3dFromDPoint3d (points[iPoint]));
        acFace->makeEdgeVisibleAt ((UInt16) iPoint);
        }

    if (numPoints == 4)
        acFace->setVertexAt ((UInt16) 3, RealDwgUtil::GePoint3dFromDPoint3d (points[2]));

    return RealDwgSuccess;;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbRegionFromShape (AcDbObjectP& acObject, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    AcDbObjectP     newObject = NULL;
    RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, elemHandle);
    if (RealDwgSuccess == status)
        {
        if (nullptr == acObject)
            acObject = newObject;
        return  status;
        }

    // REGION could have been replaced with a different object type - a valid status
    if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
        {
        if (acObject->objectId().isValid())
            {
            // if newObject is also checked in the database, erase acObject - TFS 776378.
            if (Acad::eOk != acObject->handOverTo(newObject))
                acObject->erase ();
            }
        else if (acObject->isNewObject())
            {
            delete acObject;
            }
        acObject = newObject;
        return  RealDwgSuccess;
        }

    // handle ACIS failure
    AcDbPolyline*   acPolyline = new AcDbPolyline ();
    status = this->SetAcDbPolylineFromLineString (acPolyline, elemHandle, context, polylineInfo);

    if (RealDwgSuccess == status)
        {
        if (NULL != acObject && acObject->objectId().isValid())
            {
            if (Acad::eOk != acObject->handOverTo(acPolyline))
                acObject->erase ();
            }
        else
            {
            context.AddEntityToCurrentBlock (acPolyline, elemHandle.GetElementId());
            }
        acObject = acPolyline;
        }
    else
        {
        delete acPolyline;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbPolyFaceMeshFromShape (AcDbPolyFaceMesh* acPolyFaceMesh, ElementHandleR elemHandle, ConvertFromDgnContextR context, PolylineInfoR polylineInfo) const
    {
    // acPolyFaceMesh->appendVertex prerequisites database residency:
    if (!acPolyFaceMesh->objectId().isValid())
        context.AddEntityToCurrentBlock (acPolyFaceMesh, elemHandle.GetElementCP()->ehdr.uniqueId);

    // transform DGN points to DWG
    size_t          numPoints = polylineInfo.GetPointCount ();
    DPoint3dP       points = polylineInfo.FirstPointP ();
    context.GetTransformFromDGN().Multiply (points, points, (int)numPoints);

    return  ToDwgExtMeshHeader::SetAcDbPolyFaceMeshFromPolygon(acPolyFaceMesh, points, numPoints);
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertPolylineInfoToObject
(
PolylineInfoR           polylineInfo,       // polyline info from the input element
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const
    {
    m_isFilled = false;
    m_isSolidFilled = false;
    m_isGradientFilled = false;
    m_isOutlined = false;
    m_isPatterned = false;

    // find the type needed, and instantiate it.
    AcRxClass*              typeNeeded = GetTypeNeeded (elemHandle, existingObject, context, polylineInfo);
    
    // do not create a new region - it shall be created by AcDbBody::acisIn, but preserve existing type
    if (nullptr != existingObject || typeNeeded->isA() != AcDbRegion::desc())
        acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    else
        acObject = nullptr;

    // here we have created an AcDbObject of some type. Populate it according to type.
    // the first four cases are the same as for a line string, except for the closed flag carried in polylineInfo.
    AcDbPoint*  acPoint;
    if (NULL != (acPoint = AcDbPoint::cast (acObject)))
        return SetAcDbPointFromLineString (acPoint, elemHandle, context, polylineInfo);

    AcDbPolyline*   acPolyline;
    if (NULL != (acPolyline = AcDbPolyline::cast (acObject)))
        {
        if (m_isFilled || m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, m_isFilled, m_isPatterned, m_isOutlined);
        return SetAcDbPolylineFromLineString (acPolyline, elemHandle, context, polylineInfo);
        }

    AcDb3dPolyline* ac3dPolyline;
    if (NULL != (ac3dPolyline = AcDb3dPolyline::cast (acObject)))
        {
        if (m_isFilled || m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, m_isFilled, m_isPatterned, m_isOutlined);
        return SetAcDb3dPolylineFromLineString (ac3dPolyline, elemHandle, context, polylineInfo);
        }

    AcDb2dPolyline* ac2dPolyline;
    if (NULL != (ac2dPolyline = AcDb2dPolyline::cast (acObject)))
        {
        if (m_isFilled || m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, m_isFilled, m_isPatterned, m_isOutlined);
        return SetAcDb2dPolylineFromLineString (ac2dPolyline, elemHandle, context, polylineInfo);
        }

    // These cases are unique to shapes
    AcDbWipeout*  acWipeout;
    if (NULL != (acWipeout = AcDbWipeout::cast (acObject)))
        {
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, m_isPatterned, m_isOutlined);
        return SetAcDbWipeoutFromShape (acWipeout, elemHandle, context, polylineInfo);
        }

    AcDbHatch*  acHatch;
    if (NULL != (acHatch = AcDbHatch::cast (acObject)))
        return SetAcDbHatchFromShape (acHatch, elemHandle, context, polylineInfo);

    AcDbSolid*  acSolid;
    if (NULL != (acSolid = AcDbSolid::cast (acObject)))
        {
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, m_isPatterned, m_isOutlined);
        return SetAcDbSolidFromShape (acSolid, elemHandle, context, polylineInfo);
        }

    AcDbFace*   acFace;
    if (NULL != (acFace = AcDbFace::cast (acObject)))
        {
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, m_isPatterned, m_isOutlined);

        size_t              numPoints = polylineInfo.GetPointCount();

        // A FACE entity has exactly 4 vertices!
        if (numPoints < 4 || numPoints > 5)
            {
            AcDbPolyline*   polyline = new AcDbPolyline ();
            if (Acad::eOk != acObject->handOverTo(polyline))
                acObject->erase ();
            acObject = polyline;
            if (nullptr != (polyline = AcDbPolyline::cast(acObject)))
                return SetAcDbPolylineFromLineString (polyline, elemHandle, context, polylineInfo);
            }
        else
            return SetAcDbFaceFromShape (acFace, elemHandle, context, polylineInfo);
        }

    AcDbPolyFaceMesh*    acMesh;
    if (NULL != (acMesh = AcDbPolyFaceMesh::cast (acObject)))
        return SetAcDbPolyFaceMeshFromShape (acMesh, elemHandle, context, polylineInfo);

    AcDbRegion* acRegion;
    if (nullptr != (acRegion = AcDbRegion::cast(acObject)) || typeNeeded->isA() == AcDbRegion::desc())
        {
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, m_isPatterned, m_isOutlined);
        return SetAcDbRegionFromShape (acObject, elemHandle, context, polylineInfo);
        }

    AcDbTrace* acTrace;
    if (NULL != (acTrace = AcDbTrace::cast (acObject)))
        {
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, m_isPatterned, m_isOutlined);
        return SetAcDbTraceFromShape (acTrace, elemHandle, context, polylineInfo);
        }

    // should have taken one of the paths above.
    DIAGNOSTIC_PRINTF ("Unexpected entity type from a Shape element(ID=%I64d): %ls\n", elemHandle.GetElementId(), acObject->isA()->name());
    assert (false);
    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // The conversion from DWG creates a shape string from a number of different AcDbEntities.
    // extract raw points from the element
    DPoint3dP       rawPoints = NULL;
    UInt32          numPoints = RealDwgUtil::ExtractPointsFromElement (rawPoints, elemHandle);
    if (0 == numPoints)
        return  MstnElementUnacceptable;

    // make a point array to pass into PolylineInfo.  Do not transform points - will do that based on 2D/3D entity type.
    DPoint3dArray   points;
    points.SetPoints (rawPoints, numPoints, NULL, context.GetSettings().IsZeroZCoordinateEnforced());

    free (rawPoints);

    // get the widths.
    double          startWidth, endWidth;
    context.ExtractWidthFromElement (startWidth, endWidth, elemHandle);

    PolylineInfo    polylineInfo (points, startWidth, endWidth, true);

    return  ConvertPolylineInfoToObject (polylineInfo, elemHandle, acObject, existingObject, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToObjectPostProcess (ElementHandleR inElement, AcDbObjectP acObject, ConvertFromDgnContextR context) const override
    {
   if (context.IsFilledOrPatterned(inElement))
        {
        ToDwgExtAssocRegion     assocRegion;
        return assocRegion.ToObjectPostProcess (inElement, acObject, context);
        }

    return  RealDwgSuccess;
    }

};  // ToDwgExtShape



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          02/11
+===============+===============+===============+===============+===============+======*/
class    ToDwgExtComplexString : public ToDwgExtLineString
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     HasDiscontinuities (ElementHandleCR elementIn)
    {
    switch (elementIn.GetElementType())
        {
        case LINE_STRING_ELM:
            return  ToDwgExtLineString::HasDiscontinuities (elementIn);

        case CMPLX_STRING_ELM:
            for (ChildElemIter child(elementIn, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
                if (ToDwgExtComplexString::HasDiscontinuities(child))
                    return  true;
            break;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ParseDiscontinuities (MSElementDescrH chainElemsOut, ElementHandleCR elementIn)
    {
    // walk through children and recursively break down the complex string to separated line strings.
    if (!elementIn.IsValid())
        return  false;

    DgnModelRefP        modelRef = elementIn.GetModelRef ();
    if (NULL == modelRef)
        return  false;
        
    MSElementCP         elem = elementIn.GetElementCP ();
    if (NULL == elem)
        return  false;

    if (!elem->ehdr.isComplexHeader)
        return  ToDwgExtLineString::ParseDiscontinuities (chainElemsOut, elementIn);

    if (ToDwgExtComplexString::HasDiscontinuities (elementIn))
        {
        MSElementDescrP         chainTail = NULL;

        for (ChildElemIter child(elementIn, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
            {
            MSElementDescrP     newSegments = NULL;
            MSElementTypes      childType = static_cast<MSElementTypes>(child.GetElementType());

            if (LINE_STRING_ELM == childType && ToDwgExtLineString::ParseDiscontinuities(&newSegments, child))
                {
                // a linestring w/ discontinuity is broken up to segments.  Add them to the output collection.
                MSElementDescr::InitOrAddToChainWithTail (chainElemsOut, &chainTail, newSegments);
                }
            else if (child.GetElementCP()->ehdr.isComplexHeader && ToDwgExtComplexString::ParseDiscontinuities(&newSegments, child))
                {
                // a complex string w/discontinuity is broken up to segments.  Add them to the output collection.
                MSElementDescr::InitOrAddToChainWithTail (chainElemsOut, &chainTail, newSegments);
                }
            else
                {
                // all other types and complex elements with no discontinuity shall be copied and added to output collection.
                MSElementDescrCP    childElmdscr = child.GetElementDescrCP ();
                if (NULL == childElmdscr)
                    continue;

                MSElementDescrP     dupElmdscr = NULL;
                if (childElmdscr->el.ehdr.isComplexHeader && nullptr != childElmdscr->h.firstElem && childType != BSPLINE_CURVE_ELM)
                    childElmdscr->h.firstElem->Duplicate (&dupElmdscr, true, false);
                else                
                    childElmdscr->Duplicate (&dupElmdscr, true, false);
                MSElementDescr::InitOrAddToChainWithTail (chainElemsOut, &chainTail, dupElmdscr);
                }
            }

        if (NULL != chainElemsOut && NULL != *chainElemsOut)
            {
            (*chainElemsOut)->h.elementRef = elementIn.GetElementRef ();
            (*chainElemsOut)->h.dgnModelRef = modelRef;
            }

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertElementChainToDwg
(
AcDbObjectP&            acObject,
AcDbObjectP             existingObject,
MSElementDescrP         chainElmdscr,
ConvertFromDgnContextR  context
) const
    {
    RealDwgStatus   status = RealDwgSuccess;
    UInt32          count = 0;

    for (MSElementDescrP elmdscr = chainElmdscr; NULL != elmdscr; elmdscr = elmdscr->h.next)
        {
        EditElementHandle       currElem(elmdscr, false, false, context.GetModel());

        ToDwgExtension*         toDwg = ToDwgExtension::Cast (currElem.GetHandler());
        if (NULL == toDwg)
            {
            DIAGNOSTIC_PRINTF ("Missing ToDwg extension for the child element(type=%d) broken up from complex element ID=%I64d\n", elmdscr->el.ehdr.type, chainElmdscr->el.ehdr.uniqueId);
            continue;
            }

        if (0 == count++)
            {
            // first element preserves the complex element's header ID:
            status = toDwg->ToObject (currElem, acObject, existingObject, context);
            }
        else
            {
            // add rest of the segments to database
            AcDbObjectP         newObject = NULL;

            if (RealDwgSuccess != toDwg->ToObject(currElem, newObject, NULL, context))
                DIAGNOSTIC_PRINTF ("Failed converting a child element(type=%d) borken up from complex element ID=%I64d\n", elmdscr->el.ehdr.type, chainElmdscr->el.ehdr.uniqueId);

            if (NULL != newObject)
                {
                auto entity = AcDbEntity::cast(newObject);
                if (RealDwgUtil::CanPostSetEntityProperties(entity))
                    context.UpdateEntityPropertiesFromElement (entity, currElem);
                if (!newObject->objectId().isValid())
                    context.AddEntityToCurrentBlock (entity, 0);
                newObject->close ();
                }
            }
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*          GetPlannerObjectType (ElementHandleCR inElement, ConvertFromDgnContextR context) const override
    {
    if (!context.CanElementBeRepresentedByLWPolyline(inElement, true))
        {
        if (context.GetTargetVersion() < DwgFileVersion_13)
            return AcDb2dPolyline::desc();
        else
            return AcDbSpline::desc();
        }

    return  AcDbPolyline::desc();
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool         AreAllChidrenLinear (ElementHandleCR inElement)
    {
    // if the complex shape contains only lines & linestrings, it is a linear complex shape.
    for (ChildElemIter child(inElement, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        switch (child.GetElementType())
            {
            case LINE_ELM:
            case LINE_STRING_ELM:
                continue;
            default:
                return  false;
            }
        }
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool                IsElementLinear (ElementHandleCR inElement) const override
    {
    return  ToDwgExtComplexString::AreAllChidrenLinear (inElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    MSElementDescrP     chainElmdscr = NULL;
    if (ToDwgExtComplexString::ParseDiscontinuities(&chainElmdscr, elemHandle))
        {
        if (NULL == chainElmdscr)
            return  MstnElementUnacceptable;

        // preserve the input complex string's unique ID on the first polyline segment we will add to DWG:
        chainElmdscr->el.ehdr.uniqueId = elemHandle.GetElementCP()->ehdr.uniqueId;

        RealDwgStatus   status = this->ConvertElementChainToDwg (acObject, existingObject, chainElmdscr, context);

        chainElmdscr->Release ();
        return  status;
        }

    // this complex string has no DISCONNECT points, directly create PolylineInfo from input the element:
    DPoint3dArray       points;
    DPoint2dArray       widths;
    DoubleArray         bulges;
    bool                hasConstantWidth = false, closed = false, continueLt = false;
    double              constantWidth = 0.0;
    ToDwgExtSpline      splineExt;

    // get untransformed polyline data.  Handle errors by converting the element to a spline.  Allow nonplanar points.
    if (BSISUCCESS != context.ExtractPolylineFromElement(points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt, elemHandle, existingObject, false))
        return splineExt.ToObject (elemHandle, acObject, existingObject, context);

    PolylineInfo        polylineInfo (points, widths.empty() ? NULL : &widths, bulges.empty() ? NULL : &bulges, closed);
    
    RealDwgStatus   status = __super::ConvertPolylineInfoToObject (acObject, existingObject, polylineInfo, elemHandle, context);

    if (RealDwgSuccess != status)
        status = splineExt.ToObject (elemHandle, acObject, existingObject, context);

    return  status;
    }

};  // ToDwgExtComplexString



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/11
+===============+===============+===============+===============+===============+======*/
class   ToDwgExtComplexShape : public ToDwgExtShape
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
ClosedMapping       GetClosedElementMapping (bool useFilledMapping, size_t numPoints, ConvertFromDgnContextR context) const override
    {
    return context.GetSettings().GetComplexShapeMapping (useFilledMapping, context.GetThreeD());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*          GetPlannerObjectType (ElementHandleCR inElement, ConvertFromDgnContextR context) const override
    {
    ClosedMapping   desiredMapping = Closed_Polyline;
    bool            isNonPlanar = context.IsNonPlanarPolyline (inElement, false);
    bool            isLinear = this->IsElementLinear (inElement);
    IAreaFillPropertiesQuery*   areaQuery = dynamic_cast<IAreaFillPropertiesQuery*>(&inElement.GetHandler());
    if (NULL != areaQuery)
        {
        UInt32      fillColor = 0;
        bool        hole = false, alwaysFill = false;
        bool        isFilled = areaQuery->GetSolidFill (inElement, &fillColor, &alwaysFill);
        if (areaQuery->GetAreaType(inElement, &hole) && !hole && isFilled && DWG_COLOR_Background255 == fillColor && !isLinear)
            {
            // this is a curved complex shape with solid background fill - convert it to wipeout
            return  AcDbWipeout::desc();
            }

        if (!hole)
            {
            // if mapped to a Region, don't bother to check for lwpolyline - TFS 589357:
            desiredMapping = context.GetSettings().GetComplexShapeMapping (isFilled, context.GetThreeD());
            if (Closed_Region == desiredMapping && !isNonPlanar)
                return  AcDbRegion::desc();
            // we may not be able to create a polyface or polyline - fall through to check for that.
            }
        }
    
    if (!context.CanElementBeRepresentedByLWPolyline(inElement, true))
        {
        // can't create polyface mesh - try region:
        if (Closed_Polyface == desiredMapping && !isNonPlanar)
            return  AcDbRegion::desc();

        if (context.GetTargetVersion() < DwgFileVersion_13)
            {
            if (RealDwgUtil::GetElementNormal(nullptr, nullptr, inElement))
                return  AcDb2dPolyline::desc ();
            else
                return  AcDbPolyline::desc ();
            }
        else
            {
            // prefer a polyline over a spline for a linear complex shape:
            if (isLinear)
                return  AcDbPolyline::desc ();
            else
                return  AcDbSpline::desc ();
            }
        }
    else if (Closed_Polyface == desiredMapping && isLinear)
        {
        // should be able to create a polyface from the super class:
        return  AcDbPolyFaceMesh::desc();
        }

    // default to polyline cases - final entity type will be later determined in ConvertPolylineInfoToObject
    return  AcDbPolyline::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool                IsElementLinear (ElementHandleCR inElement) const override
    {
    return  ToDwgExtComplexString::AreAllChidrenLinear (inElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToWipeout (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // stroke curved complex shape to linear segments and create a single wipeout from them
    DPoint3dArray   points;
    size_t          numPoints = RealDwgUtil::StrokeElementsToPoints(points, inElement, 2.0);
    if (numPoints < 4)
        return  CantCreateWipeout;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbWipeout::desc());

    AcDbWipeout*    acWipeout = AcDbWipeout::cast (acObject);
    if (NULL == acWipeout)
        return  NullObject;

    context.GetTransformFromDGN().Multiply (points, points);
        
    return  context.SetAcDbWipeoutFromPoints (acWipeout, &points.front(), numPoints);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/16
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToRegion (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbRegion::desc());

    AcDbObjectP     newObject = nullptr;
    RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, inElement);

    if (RealDwgSuccess == status)
        {
        if (nullptr == acObject)
            acObject = newObject;
        return  status;
        }

    // REGION could have been replaced with a different object type - a valid status
    if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
        {
        if (acObject->objectId().isValid())
            {
            if (newObject->objectId().isValid() || Acad::eOk != acObject->handOverTo(newObject))
                acObject->erase ();
            }
        else
            {
            delete acObject;
            }
        acObject = newObject;
        return  RealDwgSuccess;
        }

    // fallback to creating a spline entity
    return this->ReplaceFailedObjectWithSpline (acObject, existingObject, inElement, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/16
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ReplaceFailedObjectWithSpline (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    // try to replace the failed object with a new spline entity:
    AcDbObjectP     newObject = nullptr;
    ToDwgExtSpline  splineExt;
    RealDwgStatus   status = splineExt.ToObject (inElement, newObject, existingObject, context);

    if (nullptr != newObject)
        {
        if (nullptr != acObject)
            {
            // close or delete the failed object before assigned the new one to return:
            if (acObject->objectId().isValid())
                {
                if (newObject->objectId().isValid() || Acad::eOk != acObject->handOverTo(newObject))
                    acObject->erase ();
                }
            else
                {
                delete acObject;
                }
            }
        acObject = newObject;
        status = RealDwgStatus::RealDwgSuccess;
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    ToDwgExtSpline      splineExt;
    AcRxClass*          typeNeeded = this->GetPlannerObjectType (elemHandle, context);
    if (AcDbSpline::desc() == typeNeeded)
        return splineExt.ToObject (elemHandle, acObject, existingObject, context);

    if (AcDbWipeout::desc() == typeNeeded && RealDwgSuccess == this->ToWipeout(acObject, existingObject, elemHandle, context))
        return  RealDwgSuccess;
        
    if (AcDbRegion::desc() == typeNeeded && RealDwgSuccess == this->ToRegion(acObject, existingObject, elemHandle, context))
        return  RealDwgSuccess;

    DPoint3dArray       points;
    DPoint2dArray       widths;
    DoubleArray         bulges;
    bool                hasConstantWidth = false, closed = false, continueLt = false;
    double              constantWidth = 0.0;

    // get untransformed polyline data.  Handle errors by converting the element to a spline.  Allow nonplanar points.
    if (BSISUCCESS != context.ExtractPolylineFromElement(points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt, elemHandle, existingObject, false))
        return splineExt.ToObject (elemHandle, acObject, existingObject, context);

    PolylineInfo        polylineInfo (points, widths.empty() ? NULL : &widths, bulges.empty() ? NULL : &bulges, true);
    
    RealDwgStatus       status = __super::ConvertPolylineInfoToObject (polylineInfo, elemHandle, acObject, existingObject, context);

    if (RealDwgSuccess != status)
        status = this->ReplaceFailedObjectWithSpline (acObject, existingObject, elemHandle, context);

    return  status;
    }

}; // ToDwgExtComplexShape



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          03/12
+===============+===============+===============+===============+===============+======*/
class   ToDwgExtPointString : public ToDwgExtLineString
{
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    MSElementCP elem = elemHandle.GetElementCP ();
    if (nullptr == elem)
        return  OutOfMemoryError;

    // default to linestring
    if (!elem->hdr.dhdr.props.b.h)
        return  __super::ToObject (elemHandle, acObject, existingObject, context);

    // drop element to individual points:
    DPoint3dP   points = nullptr;
    UInt32      numPoints = RealDwgUtil::ExtractPointsFromElement (points, elemHandle);
    if (0 == numPoints)
        return  MstnElementUnacceptable;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbPoint::desc());
    AcDbPoint*  acPoint0 = AcDbPoint::cast (acObject);
    if (nullptr == acPoint0)
        return  NullObject;

    context.GetTransformFromDGN().Multiply (points, points, numPoints);

    // set first point to return:
    acPoint0->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (points[0]));
    context.UpdateEntityPropertiesFromElement (acPoint0, elemHandle);

    // create new entities from rest of the points:
    for (UInt32 i = 1; i < numPoints; i++)
        {
        AcDbSmartObjectPointer<AcDbPoint>   newPoint;
        if (Acad::eOk != newPoint.create() || Acad::eOk != newPoint->copyFrom(acPoint0))
            break;

        newPoint->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(points[i]));
        context.AddEntityToCurrentBlock (newPoint, 0);
        }

    return  RealDwgSuccess;
    }

}; // ToDwgPointLineString


