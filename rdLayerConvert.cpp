/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdLayerConvert.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp


struct LayerFilterTypeString
    {
    Int16               m_filterType;
    const ACHAR*        m_typeName;
    };

static LayerFilterTypeString    s_layerFilterTypes[] =
    {
    {1, L"NAME"},
    {2, L"COLOR"},
    {3, L"LINETYPE"},
    {4, L"LINEWEIGHT"},
    {5, L"OFF"},
    {6, L"FROZEN"},
    {7, L"LOCKED"},
    {8, L"PLOTTABLE"},
    {9, L"TRANSPARENCY"},    
    };
static Int16        s_numberOfFilterTypes = sizeof(s_layerFilterTypes) / sizeof(LayerFilterTypeString);


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddLevelFromLayer (LevelCacheR levelCache, AcDbLayerTableRecord* layer, ConvertToDgnContextR context)
    {
    const ACHAR*        layerName;
    layer->getName (layerName);

    WChar               wideName[MAX_LEVEL_NAME_LENGTH], *pSeparator;
    wcscpy (wideName, layerName);

    ElementId           elementId                   = context.ElementIdFromObject (layer);
    UInt32              levelId                     = 0;
    bool                isZero                      = false;
    bool                useMasterFileLineStyleIds   = false;
    bool                useMasterFileColorBook      = false;
    if (NULL != (pSeparator = wcschr (wideName, '|')))
        {
        // Layer is from an X-Ref - Set levelId to invalid so that it will be looked up by name.
        wcscpy (wideName, pSeparator+1);
        isZero = 0 == wcscmp (wideName, L"0");
        levelId = isZero ? LEVEL_DEFAULT_LEVEL_ID : INVALID_LEVEL;
        // Use master file's level symbology if VIRETAIN=1; otherwise use xref file level symbology:
        if (context.GetFileHolder().GetDatabase()->visretain())
            useMasterFileLineStyleIds = useMasterFileColorBook = true;
        }
    else
        {
        isZero = 0 == wcscmp (wideName, L"0");
        levelId = isZero ? LEVEL_DEFAULT_LEVEL_ID : context.GetFileHolder().GetLayerIndex()->GetOrNextDgnId (elementId);
        if (isZero)
            context.GetFileHolder().GetLayerIndex()->AddEntry (levelId, elementId, true);
        }

    // chances are that the level already exists, override it with our layer - TFS#15731
    EditLevelHandle     newLevel;
    LevelHandle         existingLevel = levelCache.GetLevel (levelId);
    if (existingLevel.IsValid())
        {
        newLevel = EditLevelHandle (existingLevel);
        if (newLevel.IsValid())
            newLevel.SetName (wideName);
        }
    else
        {
        newLevel = levelCache.DoCreateLevel (wideName, LEVEL_NULL_CODE, levelId, false, true);
        }

    if (!newLevel.IsValid())
        return BSIERROR;

    newLevel.SetElementId (elementId);
    newLevel.SetDescription (layer->description());
    newLevel.SetDisplay (!layer->isOff());
    newLevel.SetFrozen (layer->isFrozen());
    newLevel.SetPlot (layer->isPlottable());
    newLevel.SetElementAccess (layer->isLocked() ? LevelElementAccess::Locked : LevelElementAccess::All);
    newLevel.SetLineStyleDefinitionSource (useMasterFileLineStyleIds ? LEVEL_STYLE_DEFINITION_SOURCE_Parent : LEVEL_STYLE_DEFINITION_SOURCE_Source);
    newLevel.SetExtendedColorDefinitionSource (useMasterFileColorBook ? LEVEL_STYLE_DEFINITION_SOURCE_Parent : LEVEL_STYLE_DEFINITION_SOURCE_Source);

    newLevel.SetOverrideColorOn (false);
    newLevel.SetOverrideLineStyleOn (false);
    newLevel.SetOverrideWeightOn (false);
    newLevel.SetOverrideMaterialOn (false);

    // We do not know the creation time of levels from DWG files, so set the CreateTimeStamp to 0.
    newLevel.SetCreateTimeStamp (0);
    // Added hidden configuration variable _USTN_SKIP_READ_LEVEL_ELEMENTCOLOR_FROM_DWG to skip element color from DWG layers.
    // If  this variable is defined and true  then the display behavior equivalent to V8i (White).
    // else we read element color from DWG file layers and this will change the display behavior.
    bool skipcolor = ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_SKIP_READ_LEVEL_ELEMENTCOLOR_FROM_DWG");
    LevelDefinitionColor    newColor (skipcolor ? 0 : context.GetDgnColor (layer->color()), context.GetFile());
    newLevel.SetByLevelColor (newColor);

    LevelDefinitionLineStyle newLineStyle (context.GetDgnStyle (layer->linetypeObjectId()), NULL, context.GetFile());
    newLevel.SetByLevelLineStyle (newLineStyle);

    newLevel.SetByLevelWeight (context.GetDgnWeight (layer->lineWeight()));

    LevelDefinitionPlotStyle newPlotStyle (context.ElementIdFromObjectId (layer->plotStyleNameId()), context.GetFile());
    newLevel.SetPlotStyle (newPlotStyle);

    // see if there is transparency data.
    double              transparency = 0.0;
    RealDwgResBuf*      microStationXData;
    if (NULL != (microStationXData = static_cast <RealDwgResBuf*> (layer->xData (StringConstants::RegAppName_MicroStation))))
        {
        RealDwgResBuf*      transparencyData;
        if (NULL != (transparencyData = RealDwgXDataUtil::FindXDataByKey (microStationXData, AcDb::kDxfXdReal, StringConstants::XDataKey_Transparency, false)))
            transparency = transparencyData->GetDouble();

        RealDwgResBuf::Free (microStationXData);
        newLevel.SetTransparency (transparency);
        }
    else
        {
        // R2011 starts to support transparency.  Honor our legacy xdata(above) but remove that during saving.
        AcCmTransparency    acTransparency = layer->transparency ();
        if (acTransparency.isByAlpha())
            {
            transparency = context.GetDgnTransparency (acTransparency);
            newLevel.SetTransparency (transparency);
            }
        }

    if (layer->materialId().isValid())
        {
        LevelDefinitionMaterial    newMaterial (context.ElementIdFromObjectId (layer->materialId()), context.GetFile());
        newLevel.SetByLevelMaterial (newMaterial);
        }

    return BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley         07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveDwgFileLayersToDgn (AcDbLayerTableIterator* layerIterator, AcDbBlockTableRecord* xRefBlock)
    {
    WChar                   namePrefix[MAX_CELLNAME_LENGTH + 2];
    size_t                  prefixLength = 0;
    PersistentLevelCache*   levelCache = NULL;

    if (NULL == xRefBlock)
        {
        levelCache = &this->GetFile()->GetLevelCacheR ();
        namePrefix[0] = 0;
        }
    else
        {
        ACHAR const*    pName;
        xRefBlock->getName (pName);
        BeAssert ( (NULL != pName) && (0 != *pName) );

        levelCache = TempXRefLevelCache::Create (*this->GetFile(), pName);
        swprintf (namePrefix, L"%ls|", pName);
        prefixLength = wcslen (namePrefix);
        }

    if (NULL == levelCache)
        return  OutOfMemoryError;

    // inform level system that the level is inConstruction.
    levelCache->SetInConstruction (true);

    // DWG files have only one layer table. The layers for particular blocks are distinguished by having a prefix of the format "BlockName|".
    for (layerIterator->start(); !layerIterator->done(); layerIterator->step())
        {
        AcDbObjectId    layerId;
        if (Acad::eOk != layerIterator->getRecordId (layerId))
            continue;

        AcDbLayerTableRecordPointer layer (layerId, AcDb::kForRead);
        if (Acad::eOk != layer.openStatus())
            continue;

        ACHAR const*    layerName;
        layer->getName (layerName);

        // if we're looking for master file layers, and there's a prefix, it's not applicable
        if ( (NULL == xRefBlock) && (0 != wcschr (layerName, '|')) )
            continue;

        // if we're looking for XRef layers, see if it matches.
        if ((NULL != xRefBlock) && (0 != _wcsnicmp (namePrefix, layerName, prefixLength)))
            continue;

        /*------------------------------------------------------------------------------------------------
        Save off hidden layers to a local cache as they are not visible in GUI.

        Hidden layers are used by applications to control display of their objects at run-time, bypassing
        layer display for standard entities.  Inventer OE displays its objects in layout viewports but not
        in modelspace(TFS 421548).  Constraints does not display their dims at all (TFS 7426).  To do this
        right, we either have to allow RealDWG to draw objects at runtime instead of at file open time to
        create DGN elements, or we have to provide an element handler to emulate the ACAD's display of these
        objects.  In addition to hidden layer control, C3D per-viewport rotation of notes is another example.
        For now, we work the problem around by saving contraint's layers in a local cache for later display
        check, but keep Inventor's as is (i.e. save it to DgnFile). Not a pretty workaround but enough to 
        fix both TFS's.

        Added to the growing hidden layer list that we do not want to save to the DgnFile:
            *ADSK_CONSTRAINTS           - TFS 7426
            *DgnUnderlayNormalLayer 
            *DgnUnderlayFrozenLayer     - TFS 472172

        Hidden layers we have to save or otherwise we lose elements:
            *IDW_BlockReferenceLayer    -TFS 421548
        ------------------------------------------------------------------------------------------------*/
        if (layer->isHidden() && (
            _wcsicmp(layerName, L"*ADSK_CONSTRAINTS") == 0 ||
            _wcsicmp(layerName, L"*DgnUnderlayNormalLayer") == 0 ||
            _wcsicmp(layerName, L"*DgnUnderlayFrozenLayer") == 0 ||
            _wcsicmp(layerName, L"*ADSK_ASSOC_ENTITY_BACKUPS") == 0))
            {
            m_hiddenLayers.append (layerId);
            continue;
            }

        // found a layer we need.
        AddLevelFromLayer (*levelCache, layer, *this);
        }

    levelCache->SetInConstruction (false);

    // a missing xRef block may not have any layer
    if (0 == levelCache->GetLevelCount())
        return  MstnElementUnacceptable;

    // set element ID from layer table ID
    if (NULL == xRefBlock)
        {
        AcDbObjectId    layertableId = this->GetFileHolder().GetDatabase()->layerTableId ();
        if (layertableId.isValid())
            levelCache->SetElementId (this->ElementIdFromObjectId(layertableId));
        }

    EditElementHandle   levelEeh;
    if (LevelCacheErrorCode::None != levelCache->ToElement(levelEeh))
        return BSIERROR;

    this->LoadElementIntoCache (levelEeh);

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt   ConvertToDgnContext::SaveDwgLayersToDgn ()
    {
    AcDbDatabase*               database = m_pFileHolder->GetDatabase();
    AcDbLayerTablePointer       layerTable (database->layerTableId(), AcDb::kForRead);

    AcDbLayerTableIterator*     layerIterator;
    if (Acad::eOk != layerTable->newIterator (layerIterator))
        return BSIERROR;

    // need to collect hidden layers
    layerIterator->setSkipHidden (false);

    // Load master file layers.
    this->SaveDwgFileLayersToDgn (layerIterator, NULL);

    // Load layers for XRefs.
    AcDbBlockTablePointer       blockTable (database->blockTableId(), AcDb::kForRead);

    AcDbBlockTableIterator*     blockIterator;
    blockTable->newIterator (blockIterator);

    for (blockIterator->start(); ! blockIterator->done(); blockIterator->step())
        {
        AcDbObjectId                blockId;
        if (Acad::eOk != blockIterator->getRecordId (blockId))
            continue;
        AcDbBlockTableRecordPointer     pBlock (blockId, AcDb::kForRead);

        if (pBlock->isFromExternalReference())
            this->SaveDwgFileLayersToDgn (layerIterator, pBlock);
        }
    delete blockIterator;

    delete layerIterator;

    // at this point, we need to write the LevelNameDictionary and the ExtendedColorMap to the file.
    DgnFileP                dgnFile = this->GetFile();
    LevelNameDictionaryR    levelNameDictionary = dgnFile->GetLevelNameDictionary();
    EditElementHandle       nameDictionaryEeh;
    if (SUCCESS == levelNameDictionary.ToElement (nameDictionaryEeh, &dgnFile->GetDictionaryModel()))
        this->LoadElementIntoCache (nameDictionaryEeh);

    return SUCCESS;
    }




/*=================================================================================**//**
* class that contains only static methods related to the level -> layer translation.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           LayerFromLevel
{
friend          ConvertFromDgnContext;

private:


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetActiveViewGroupId
(
ElementId&                  activeViewGroupId,
ModelId&                    activeViewGroupModelId,
DgnModelP                   modelRef
)
    {
    activeViewGroupId = 0;
    if (SUCCESS != modelRef->GetTCBData (&activeViewGroupId, offsetof (Tcb, activeViewGroup), sizeof(ElementId)) )
        return;

    ViewGroupCollectionCR   vgc         = modelRef->GetDgnFileP()->GetViewGroups();
    ViewGroupPtr            viewGroup   = vgc.FindByElementId (activeViewGroupId);
    if (viewGroup.IsNull())
        return;

    activeViewGroupModelId = viewGroup->GetViewInfo(0).GetRootModelId();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/05
+---------------+---------------+---------------+---------------+---------------+------*/
static bool              IsNonDefaultModel (ModelId modelId, DgnFileP dgnFile)
    {
    if (modelId == dgnFile->GetDefaultModelId() || modelId == RealDwgUtil::GetDwgModelSpaceId(dgnFile))
        return false;

    ModelIndexItemCP    modelItem;
    if (NULL != (modelItem = dgnFileObj_getModelItemByID (dgnFile, modelId, false)))
        return modelItem->GetModelType() != DgnModelType::Sheet;

    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static  ViewGroupPtr        GetFirstSheetViewGroup (DgnFileP dgnFile)
    {
    // first look for one where all the views point to the same model.
    ViewGroupCollectionCR   vgc    = dgnFile->GetViewGroups();

    for each (ViewGroupPtr viewGroup in vgc)
        {
        ModelId     onlyModel;
        if (!viewGroup->AllViewsOneModel (onlyModel))
            continue;

        ModelIndexItemCP    modelItem;
        if (NULL == (modelItem = dgnFileObj_getModelItemByID (dgnFile, onlyModel, false)))
            continue;

        if ((DgnModelType::Sheet != modelItem->GetModelType()) && modelItem->IsHidden())
            return viewGroup;
        }

    // didn't find one with all views one model, look for one where first view is points to a sheet.
    for each (ViewGroupPtr viewGroup in vgc)
        {
        for (int iView=0; iView < MAX_VIEWS; iView++)
            {
            ViewInfoCR  viewInfo = viewGroup->GetViewInfo(iView);
            if (!viewInfo.GetViewFlags().on_off)
                continue;
            ModelId modelId = modelId = viewInfo.GetRootModelId();

            ModelIndexItemCP    modelItem;
            if (NULL == (modelItem = dgnFileObj_getModelItemByID (dgnFile, modelId, false)))
                continue;

            if (DgnModelType::Sheet != modelItem->GetModelType() && modelItem->IsHidden())
                return viewGroup;
            }
        }
    return NULL;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static BitMaskP             ExtractByViewLevelDisplayMask
(
DgnModelP                   masterModelRef,             /* => master file modelRef  */
DgnModelRefP                modelRef,                   /* => input modelRef */
int                         view,                       /* => input view number */
WCharCP                     sourceViewGroupName,        /* => user selected view group name for global layer display */
bool                        useActiveViewGroup
)
    {
    DgnFileP                dgnFile     = masterModelRef->GetDgnFileP();
    ViewGroupCollectionCR   vgc         = dgnFile->GetViewGroups();
    ViewGroupPtr            sourceViewGroup;

    // this method should only be called if the view is specified.
    BeAssert (view >= 0 && L"Source view unexpecified when calling ExtractByViewLevelDisplayMask!");
    if (view < 0)
        return NULL;

    // first find the view group we want to use:
    if (useActiveViewGroup && (NULL != sourceViewGroupName))
        {
        /*-------------------------------------------------------------------------------
        User has selected a desired source view group over active view group to convert
        level mask to global layer display.  This option also implies that when desired
        view group does not exist, the first sheet model encountered will be used for global
        layer display.  This effective adds an additional traversal:
        -------------------------------------------------------------------------------*/
        sourceViewGroup = vgc.FindByName (sourceViewGroupName);
        if (sourceViewGroup.IsNull())
            sourceViewGroup = GetFirstSheetViewGroup (dgnFile);
        }

    // didn't find (or didn't look for) view group by name. Find it by the model Id.
    if (sourceViewGroup.IsNull())
        {
        ModelId     searchForModelId        = masterModelRef->GetModelId();
        ElementId   searchForViewGroupId    = 0;

        ElementId   activeViewGroupId       = 0;
        ModelId     activeViewGroupModelId  = 0;
        GetActiveViewGroupId (activeViewGroupId, activeViewGroupModelId, masterModelRef);

        if (useActiveViewGroup && !IsNonDefaultModel (activeViewGroupModelId, dgnFile))
            {
            // If we're exporting the default model - (not wBlock of nondefaults, or viewport freeze) then look for the active view group ID.
            // This may point to a sheet as in the test case for TR# 13509 - but we'll
            // adopt the convention that if the user is exporting from view level masks
            // then they are interested in the active view group. - RBB 12/04
            searchForViewGroupId = activeViewGroupId;
            searchForModelId = activeViewGroupModelId;
            }
        else
            {
            // If the active view group does point to the root model, prefer this view group over others that may
            // point to the same model.
            if (searchForModelId == activeViewGroupModelId)
                searchForViewGroupId = activeViewGroupId;
            }
        sourceViewGroup = vgc.FindLastModifiedMatchingModel (searchForViewGroupId, searchForModelId, false, view);
        }

    // if we didn't find a view group, can't make any progress.
    if (sourceViewGroup.IsNull())
        {
        DIAGNOSTIC_PRINTF ("Error getting a valid source view group\n");
        return NULL;
        }

    // find the level bit mask for the desired model.
    ViewInfoR           viewInfo        = sourceViewGroup->GetViewInfoR (view);
    LevelMaskTreeR      levelMaskTree   = viewInfo.GetLevelMasksR();

    // make sure there is an entry for the modelRef we're looking for (gets the level mask from the DgnAttachment if necessary).
    levelMaskTree.EnsureEntryExists (modelRef, false, view);

    LMTreeEntryP        levelMaskEntry  = levelMaskTree.FindEntry (NULL, modelRef, false, NULL);

    if (NULL == levelMaskEntry)
        {
        DIAGNOSTIC_PRINTF ("Error extracting view level display mask!\n");
        return NULL;
        }

    bool        foundNewLevel;
    levelMaskEntry->CorrectViewLevelMask (foundNewLevel, false, modelRef);
    BitMaskCP   viewLevelMask;
    if (NULL == (viewLevelMask = levelMaskEntry->GetViewLevelMaskCP()))
        {
        DIAGNOSTIC_PRINTF ("Error extracting view level display mask!\n");
        return NULL;
        }

    return BitMask::Clone (*viewLevelMask);
    }
};

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertFromDgnContext::IsValidMasterLinetype
(
AcDbObjectId&               linetypeId
)
    {
    /*-----------------------------------------------------------------------------------
    Make sure a linetype exists in the master file, and it is NOT a dependent to an XRef.
    A dependent linetype is a copy from an XRef and flag 70's 5th bit is thus tuened on.
    A dependent linetype will not be saved back to DWG when it is no longer referenced,
    for example from reference file merging.  For a dependent linetype, we need to create
    a new linetype entry with a new object ID because existing linetype prevents us from
    re-using the same object ID.  To do that, we set input linetypeId to null.

    Currently a level merged from a reference file does not seem to deep clone linestyle
    like other elements do, but this is a whole different issue that should be addressed
    separately in level merge code.
    -----------------------------------------------------------------------------------*/
    AcDbLinetypeTableRecordPointer  linetype (linetypeId, AcDb::kForRead);
    if (Acad::eOk == linetype.openStatus())
        {
        if (linetype->isDependent())
            {
            linetypeId.setNull ();
            return  false;
            }

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/12
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId       ConvertFromDgnContext::GetMaterialIdFromLevel (const LevelHandle& level)
    {
    LevelDefinitionMaterial levelMaterial = (this->UseLevelSymbologyOverrides() && level.GetOverrideMaterialOn()) ? level.GetOverrideMaterial() : level.GetByLevelMaterial();

    ElementId               materialId = levelMaterial.GetId ();
    if (0 == materialId)
        materialId = INVALID_ELEMENTID;

    return  materialId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsExtendedColor(UInt32 rawColor)
    {
    if (0 == (rawColor >> 8) || COLOR_BYCELL == rawColor || COLOR_BYLEVEL == rawColor || INVALID_COLOR == rawColor)
        return  false;

    if (0 == ((rawColor >> 8) & MAX_ExtendedColorIndex))
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::UpdateLayerFromLevel
(
AcDbLayerTableRecord*       layer,
AcDbBlockTableRecord*       pXRefBTR,       // must be NULL or open for read.
DgnModelRefP                modelRef,
LevelHandle&                levelHandle,
bool                        isNew
)
    {
    bool                        useOverrides = this->UseLevelSymbologyOverrides();
    bool                        forceOn      = this->GetLevelDisplayView() >= 0 && VPFreeze_ThawGlobalLayers == this->GetSettings().GetViewportFreezeMode();

    LevelDefinitionColor        colorDef    = (useOverrides && levelHandle.GetOverrideColorOn()) ? levelHandle.GetOverrideColor() : levelHandle.GetByLevelColor();
    LevelDefinitionLineStyle    styleDef    = (useOverrides && levelHandle.GetOverrideLineStyleOn()) ? levelHandle.GetOverrideLineStyle() : levelHandle.GetByLevelLineStyle();
    UInt                        weight      = (useOverrides && levelHandle.GetOverrideWeightOn()) ? levelHandle.GetOverrideWeight() : levelHandle.GetByLevelWeight();
    LevelDefinitionMaterial     materialDef = (useOverrides && levelHandle.GetOverrideMaterialOn()) ? levelHandle.GetOverrideMaterial() : levelHandle.GetByLevelMaterial();
    bool                        display     = forceOn || levelHandle.GetDisplay();
    bool                        frozen      = !forceOn && levelHandle.GetFrozen();
    bool                        locked      = (LevelElementAccess::All != levelHandle.GetElementAccess());
    bool                        plot        = levelHandle.GetPlot();
    WCharCP                     description = levelHandle.GetDescription();
    WCharCP                     levelName   = levelHandle.GetName();
    LevelId                     levelId     = levelHandle.GetLevelId();

    try
        {
        DgnModelP           colorModel  = colorDef.GetDefinitionModel();
        UInt32              colorIndex  = colorDef.GetColor ();
        Int32               lineStyleId = styleDef.GetStyle ();
        AcDbObjectId        linetypeObjectId;
        Acad::ErrorStatus   es;

        if (NULL == pXRefBTR)
            {
            if ( (linetypeObjectId = this->GetLineTypeFromDgn (lineStyleId)).isNull() || !this->IsValidMasterLinetype (linetypeObjectId))
                linetypeObjectId = this->CreateLinetypeFromStyleId (m_model, NULL, NULL, !linetypeObjectId.isNull(), lineStyleId);

            if (layer->objectId() != m_pFileHolder->GetDatabase()->layerZero())
                {
                AcString    nameString(levelName);
                this->ValidateName (nameString);

                // Make sure that there isn't a non-default level named "0" (Doug Glasgow problem from NewsGroup).
                // This will cause problems.
                if ( (LEVEL_BYCELL != levelId) && (0 == nameString.compare (L"0")))
                    nameString = "_0";

                es = layer->setName (nameString);
                if (Acad::eOk != es)
                    DIAGNOSTIC_PRINTF ("Cannot update layer name %ls [%ls]!\n", nameString.kwszPtr(), acadErrorStatusText(es));
                }
            }
        else
            {
            // This is the case when the level is from a reference file.

            // Only one "O" per file.
            if (levelId == LEVEL_DEFAULT_LEVEL_ID)
                return BSIERROR;
            // stop if there is no file attached
            if (NULL == modelRef->GetDgnFileP())
                return  BSIERROR;

            DgnFileFormatType   refFormat           = modelRef->GetDgnFileP()->GetOriginalFormat();
            DgnFileFormatType   masterFormat        = this->GetFile()->GetOriginalFormat();
            bool                refIsAutoCAD        = (DgnFileFormatType::DWG == refFormat || DgnFileFormatType::DXF == refFormat);
            bool                masterIsAutoCAD     = (DgnFileFormatType::DWG == masterFormat || DgnFileFormatType::DXF == masterFormat);
            DgnModelP           lineStyleModel      = styleDef.GetDefinitionModel();

            if (!IsExtendedColor(colorIndex) ||
                ((!refIsAutoCAD || !masterIsAutoCAD) && (DWG_OVERRIDE_SYMBOLOGY_MAP != (0xffffff00 & colorIndex))))
                {
                RgbColorDef     rgbColor;
                IntColorDef     intColor;

                // We've got color and style IDs from another reference, so we have to remap them here.
                if (BSISUCCESS == DgnColorMap::ExtractElementColorInfo(&intColor, NULL, NULL, NULL, NULL, colorIndex < 0 ? DgnColorMap::INDEX_Background : colorIndex, *modelRef->GetDgnFileP()))
                    {
                    memcpy (&rgbColor, &intColor.m_rgb, sizeof rgbColor);

                    // Fix for TR# 141711 - Dont do remap if DGNS are using same color table...
                    if (colorIndex < 0 ||
                        colorIndex > 255 ||
                        0 != memcmp ( m_pFileHolder->GetDgnSymbologyData()->GetColor (colorIndex), &rgbColor, sizeof (rgbColor)))
                        {
                        colorIndex = m_pFileHolder->GetDgnSymbologyData()->GetColorIndex (&rgbColor, *this);
                        }
                    }
                }
            linetypeObjectId = this->CreateLinetypeFromStyleId (lineStyleModel, pXRefBTR, modelRef, true, lineStyleId);
            }


        AcDb::LineWeight    lineWeight = this->GetLineWeightFromDgn (weight, isNew ? AcDb::kLnWtByLayer : layer->lineWeight());

        layer->setColor (this->GetColorFromDgn (colorIndex, layer->color().colorIndex(), colorModel));
        layer->setLineWeight (lineWeight);
        layer->setLinetypeObjectId (linetypeObjectId);
        layer->setIsLocked (locked);
        layer->setIsOff (!display);
        layer->setIsFrozen (frozen);
        layer->setIsPlottable (plot);
        if (this->GetTargetVersion() >= DwgFileVersion_2004 && '\0' != description[0])
            layer->setDescription (description);

        // set or remove bylevel material
        ElementId           materialId = this->GetMaterialIdFromLevel (levelHandle);
        AcDbObjectId        materialObjectId;
        if (INVALID_ELEMENTID != materialId && (materialObjectId = this->GetOrCreateDwgMaterial(materialId)).isValid())
            layer->setMaterialId (materialObjectId);
        else if (!(materialObjectId = layer->materialId()).isNull() && INVALID_ELEMENTID == materialId)
            layer->setMaterialId (AcDbObjectId::kNull);

        double              transparency = levelHandle.GetTransparency();
       // R2011 supports transparency.  Only save it to xdata for R2007 & below.
        if (this->GetTargetVersion() < DwgFileVersion_2010)
            {
            RealDwgResBuf*  microStationXData  = static_cast <RealDwgResBuf*> (layer->xData (StringConstants::RegAppName_MicroStation));
            if (RealDwgXDataUtil::SetTransparency (&microStationXData, transparency, m_pFileHolder->GetDatabase()))
                layer->setXData (microStationXData);

            RealDwgResBuf::Free (microStationXData);
            }
        else
            {
            layer->setTransparency (this->GetTransparencyFromDgn(transparency, INVALID_LEVEL));
            }

        // NEEDS_WORK - Plot style.
        }
    catch (...)
        {
        const ACHAR*            layerName;
        layer->getName (layerName);
        DIAGNOSTIC_PRINTF ("Exception thrown updating layer: %ls from Level ID: %d\n", layerName, levelId);
        return BSIERROR;
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId     FindNonLayer0ToActivate (AcDbDatabase* dwg)
    {
    AcDbObjectId        layerId;

    if (nullptr == dwg)
        return  layerId;

    AcDbLayerTablePointer   layerTable (dwg->layerTableId(), AcDb::kForRead);
    if (Acad::eOk != layerTable.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Error opening layer table!\n");
        return  layerId;
        }

    AcDbLayerTableIterator* iter = nullptr;
    if (Acad::eOk != layerTable->newIterator(iter))
        {
        DIAGNOSTIC_PRINTF ("Error creating layer table iterator!\n");
        return  layerId;
        }

    for (iter->start(); !iter->done(); iter->step())
        {
        if (Acad::eOk != iter->getRecordId(layerId))
            {
            DIAGNOSTIC_PRINTF ("Error getting layer table record object ID!\n");
            continue;
            }

        if (layerId == dwg->layerZero())
            continue;

        AcDbLayerTableRecordPointer layer(layerId, AcDb::kForRead);
        if (Acad::eOk != layer.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening layer for temporary activation!\n");
            continue;
            }

        // RealDWG does not allow a frozon or an xRef layer to be activated:
        const ACHAR*    layerName = nullptr;
        if (layer->isFrozen() || (Acad::eOk == layer->getName(layerName) && acdbSymUtil()->hasVerticalBar(layerName)))
            continue;

        // this is it - the first activatable layer in the layer table!
        return  layerId;
        }

    return  layerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Somnath.More    07/21
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::RemappLevelElementIds(LevelCacheCR levelCache)
    {
    bmap<ElementId , ElementId> replacedMap;
    for each (LevelHandle levelHandle in levelCache)
        {
        LevelId     levelId = levelHandle.GetLevelId();
        ElementId   elementId = levelHandle.GetElementId();
        auto dbHandle = m_pFileHolder->GetLayerIndex()->GetDBHandle(levelId);

        if (!dbHandle.isNull())
            {
            if (elementId != dbHandle)
                {
                replacedMap[dbHandle] = elementId;
                EditLevelHandle(levelHandle).SetElementId(dbHandle);
                }
            }
        }

    if (replacedMap.empty())
        return;


    for each (LevelHandle levelHandle in levelCache)
        {
        LevelId     levelId = levelHandle.GetLevelId();
        ElementId   elementId = levelHandle.GetElementId();

        auto id = replacedMap[elementId];
        if (id == 0)
            continue;

        auto dbHandle = m_pFileHolder->GetLayerIndex()->GetDBHandle(levelId);

        if (dbHandle.isNull())
            {
            EditLevelHandle(levelHandle).SetElementId(id);
            replacedMap.erase(elementId);
            }

        if (replacedMap.empty())
            break;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::CreateLayersForModelRef
(
AcDbBlockTableRecord*       pXRefBTR,               // must be NULL, or open for read when this is called.
DgnModelRefP                layerModelRef,
DgnModelP                   rootModelRef
)
    {
    BitMaskP                pDisplayBitMask = NULL;
    ViewportFreezeMode      vpFreeze = this->GetSettings().GetViewportFreezeMode ();

    // Dont change levels if reference is not loaded (level cache is not available.
    if (NULL == layerModelRef)
        return;

    AvlTree                 *currentIdTree = ((this->SavingChanges()) && (NULL == pXRefBTR)) ? mdlAvlTree_init (AVLKEY_UINT64) : NULL;

    // If the view visibility has been selected, turn off layers that are not on in the selected view.
    if (this->GetLevelDisplayView() >= 0 && VPFreeze_ViewportsAndGlobal == vpFreeze)
        pDisplayBitMask  = LayerFromLevel::ExtractByViewLevelDisplayMask (rootModelRef, layerModelRef, this->GetLevelDisplayView(), m_sourceViewGroupName.c_str(), true);

    AcDbDatabase*   dwg = this->GetDatabase ();
    bool            clayerReset = false;
    AcDbObjectId    clayer = dwg->clayer ();
    LevelCacheCR    levelCache = layerModelRef->GetLevelCache();
    ElementId       highestUsedElementId = 0;

    this->RemappLevelElementIds(levelCache);

    for each (LevelHandle levelHandle in levelCache)
        {
        LevelId     levelId     = levelHandle.GetLevelId();
        ElementId   elementId   = levelHandle.GetElementId ();
        AcString    levelName (levelHandle.GetName());
        this->ValidateName (levelName);

        if (NULL != currentIdTree)
            mdlAvlTree_insertNode (currentIdTree, &elementId, sizeof(elementId));

        // Both layer "0" (levelId == LEVEL_BYCELL) and "DefPoints" cannot be controlled independently in an X-Ref.
        if ( (NULL != pXRefBTR) && (LEVEL_BYCELL == levelId || 0 == levelName.compare (L"defpoints")))
            continue;

        bool            turnLayerOff = ( (NULL != pDisplayBitMask) && !pDisplayBitMask->Test (levelId-1));
        AcDbObjectId    layerId;
        if (!(layerId = m_pFileHolder->GetLayerByXRefAndLevelHandle(levelHandle, pXRefBTR, layerModelRef, this->SavingChanges())).isNull())
            {
            AcDbLayerTableRecordPointer layer (layerId, AcDb::kForWrite);
            if (Acad::eOk != layer.openStatus())
                {
                DIAGNOSTIC_PRINTF ("Error opening layer \"0\", ID=%I64d.  [%ls]\n", this->ElementIdFromObjectId(layerId), acadErrorStatusText(layer.openStatus()));
                continue;
                }
            else if (layerId == clayer && (levelHandle.GetFrozen() || !levelHandle.GetDisplay()))
                {
                /*---------------------------------------------------------------------------------------------------
                When layer 0 is active in the seed file, RealDWG 2015 no longer permits us to freeze or turn it off.
                Find the first none "0", none xRef and thawed layer to be temporarily activated prior to setting its 
                properties.  Will restore CLAYER afterwards.  TFS# 175467.
                ---------------------------------------------------------------------------------------------------*/
                AcDbObjectId    nextLayer = FindNonLayer0ToActivate (dwg);
                if (nextLayer.isValid() && Acad::eOk == dwg->setClayer(nextLayer))
                    clayerReset = true;
                }

            this->UpdateLayerFromLevel (layer.object(), pXRefBTR, layerModelRef, levelHandle, false);

            if (turnLayerOff)
                layer->setIsOff(true);
            if (clayerReset)
                dwg->setClayer (clayer);
            }
        else
            {
            // Make sure that there isn't a non-default level named "0" (Doug Glasgow problem from NewsGroup). This will cause problems.
            if ( (LEVEL_BYCELL != levelId) && (0 == levelName.compare(L"0")) )
                levelName = L"_0";

            layerId = this->AddLayer (levelName, elementId, pXRefBTR);
            if (layerId.isNull())
                continue;

            AcDbLayerTableRecordPointer layer (layerId, AcDb::kForWrite);
            if (Acad::eOk == layer.openStatus())
                {
                this->UpdateLayerFromLevel (layer, pXRefBTR, layerModelRef, levelHandle, true);

                if (turnLayerOff)
                    layer->setIsOff (true);
                }

            // we have added a new layer from a level in a reference file, track the highest element ID we have used
            if ((layerModelRef != rootModelRef) && (highestUsedElementId == 0 || elementId > highestUsedElementId))
                highestUsedElementId = elementId;
            }

        bool                useOverrides = UseLevelSymbologyOverrides();
        if ( (NULL == pXRefBTR) && (useOverrides || (m_pFileHolder->GetLayerIndex()->GetDBHandle (levelId)).isNull()) )
            {
            bool        colorOverride   = levelHandle.GetOverrideColorOn();
            bool        weightOverride  = levelHandle.GetOverrideWeightOn();
            bool        styleOverride   = levelHandle.GetOverrideLineStyleOn();

            m_pFileHolder->GetLayerIndex()->AddEntry (levelId, layerId.handle(), false, colorOverride, styleOverride, weightOverride);
            }
        }

    BitMask::FreeAndClear (&pDisplayBitMask);

    // compare the current set of levels with the ones we started with, and delete those that are no longer there.
    if (NULL != currentIdTree)
        {
        LayerTableIndex*        tableIndex = m_pFileHolder->GetLayerIndex();
        ElementIdArrayR         originatedInDwgList = tableIndex->GetOriginatedInDwgList();

        // the "ExportedIdIterator" contains all of the elementIDs of the layers we started with. Since we read from DWG, their AcDbHandle == ElementId.
        if (!originatedInDwgList.empty())
            {
            for each (ElementId originalId in originatedInDwgList)
                {
                // if the ID originated in DWG does not exist in current list, the entry has been deleted.
                if (NULL == mdlAvlTree_search (currentIdTree, &originalId))
                    {
                    AcDbObjectId objectId = this->ExistingObjectIdFromElementId (originalId);
                    if (!objectId.isNull() && acdbSymUtil()->layerZeroId(this->GetFileHolder().GetDatabase()) != objectId)
                        {
                        m_pFileHolder->ScheduleLayerErase (objectId);
                        tableIndex->RemoveEntry (objectId.handle());
                        }
                    }
                }
            }
        mdlAvlTree_free (&currentIdTree, NULLFUNC, NULL);
        }

    // if we have copied a level from a reference file into the master DWG file, make sure the handseed covers the highest ID we have used, TFS 1061562
    if (rootModelRef != layerModelRef && highestUsedElementId > 0)
        {
        UInt64  highestDwgId = RealDwgUtil::CastDBHandle (dwg->handseed());
        if (highestDwgId < highestUsedElementId)
            dwg->setHandseed (AcDbHandle(highestUsedElementId));
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddLayer
(
AcString&                   inputName,
ElementId                   uniqueId,
AcDbBlockTableRecord*       pXRefBTR
)
    {
    AcDbObjectId            layerTableId = m_pFileHolder->GetDatabase()->layerTableId();
    AcDbLayerTablePointer   layerTable (layerTableId, AcDb::kForRead);
    if (Acad::eOk != layerTable.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Error opening layer table before adding a new layer! [%ls]\n", acadErrorStatusText(layerTable.openStatus()));
        return AcDbObjectId::kNull;
        }

    AcString                layerName;
    if (NULL != pXRefBTR)
        {
        RealDwgUtil::GetXRefDependentSymbolTableName (layerName, inputName.kwszPtr(), pXRefBTR);

        // if there is already a layer of this name in the layer table, nothing we can do.
        AcDbObjectId    existingLayerId;
        if (Acad::eOk == layerTable->getAt (layerName.kwszPtr(), existingLayerId, false))
            {
            DIAGNOSTIC_PRINTF ("Ignoring duplicate reference file layer: %ls\n", layerName.kwszPtr());
            return AcDbObjectId::kNull;
            }
        }
    else
        {
        layerName.assign (inputName);

        bool    validated = false;
        if (this->SavingChanges())
            {
            /*----------------------------------------------------------------------------------------------
            When editing a DWG file, do not try deduplicating new level name - in case of dup names, we opt
            to deleting existing DWG layer so we will be able to replace it with the new one.  As a case in
            TFS 722202, a level was first deleted, then later on a new level of the same name was added back 
            into the cache, deduplicating the name would mean to change the new level name which we do not want.

            Erased layers can still be scheduled for erasing at next step in above method CreateLayersForModelRef.
            When it is time to execute actual erasing in FileHolder::PostSaveToDatabase, these already erased 
            layers will simply not open and be treated as erased, rightfully.
            -----------------------------------------------------------------------------------------------*/
            AcDbObjectId    existingLayerId;
            if (Acad::eOk == layerTable->getAt(layerName.kwszPtr(), existingLayerId, false))
                {
                AcDbLayerTableRecordPointer layer(existingLayerId, AcDb::kForWrite);
                if (Acad::eOk == layer.openStatus())
                    {
                    layer->setIsLocked (false);
                    layer->erase ();
                    // still need a valid name
                    this->ValidateName (layerName);
                    validated = true;
                    }
                }
            }

        if (!validated)
            this->DeduplicateTableName (layerTableId, layerName);
        }

    // upgrade open of layerTable to writeable
    layerTable->upgradeOpen ();

    AcDbLayerTableRecord* layer = new AcDbLayerTableRecord();
    layer->setName (layerName);

    AcDbObjectId            layerObjectId = this->AddRecordToSymbolTable (layerTable, layer, uniqueId);
    if (layerObjectId.isNull())
        {
        // handle error by adding the layer with a new object ID.
        layerObjectId = this->AddRecordToSymbolTable (layerTable, layer, 0);
        if (layerObjectId.isNull())
            {
            delete layer;
            return layerObjectId;
            }
        }

    if (NULL != pXRefBTR)
        RealDwgUtil::MakeSymbolTableRecordDependentOnXRef (layer, pXRefBTR->objectId());

    layer->close();

    return layerObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::AddChildLayer
(
UInt32      levelId,
char        *pNameSuffix,
bool        isOn
) const
    {
    AcDbHandle  parentHandle;
    if ( (parentHandle = m_pFileHolder->GetLayerIndex()->GetDBHandle (levelId)).isNull())
        return 0;

    AcDbObjectId                parentObjectId;
    if ( (parentObjectId = this->ExistingObjectIdFromDBHandle (parentHandle)).isNull())
        return 0;

    AcDbLayerTableRecordPointer parentLayer (parentObjectId, AcDb::kForWrite);
    if (Acad::eOk != parentLayer.openStatus())
        return 0;

    // if the layer with same name exists, return that layer
    const ACHAR*            layerChars;
    parentLayer->getName (layerChars);
    AcString                layerName    = AcString (layerChars) + AcString (pNameSuffix);
    AcDbObjectId            layerTableId = m_pFileHolder->GetDatabase()->layerTableId();
    AcDbLayerTablePointer   layerTable (layerTableId, AcDb::kForWrite);

    AcDbObjectId        childLayerId;
    if (Acad::eOk == layerTable->getAt (layerName, childLayerId, AcDb::kForWrite))
        return childLayerId;

    AcDbLayerTableRecord*     childLayer = new AcDbLayerTableRecord();

    childLayer->copyFrom (parentLayer);

    childLayer->setName (layerName);
    if (!isOn)
        childLayer->setIsOff (true);

    if (Acad::eOk == layerTable->add (childLayer))
        childLayer->close ();
    else
        delete childLayer;

    childLayerId = childLayer->objectId ();
    m_pFileHolder->GetLayerIndex()->SetConstructionClassChild (levelId, childLayerId);

    return childLayerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetConstructionClassLayer
(
UInt32      levelId
) const
    {
    AcDbObjectId            childLayerId;

    if ((childLayerId = m_pFileHolder->GetLayerIndex()->GetConstructionClassChild (levelId)).isNull())
         m_pFileHolder->GetLayerIndex()->SetConstructionClassChild (levelId, childLayerId = this->AddChildLayer (levelId, " (Construction)", this->GetViewDisplayFlags().constructs));

    return childLayerId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetPatternClassLayer
(
UInt32      levelId
) const
    {
    AcDbObjectId            childLayerId;

    if ((childLayerId = m_pFileHolder->GetLayerIndex()->GetPatternClassChild (levelId)).isNull())
         m_pFileHolder->GetLayerIndex()->SetPatternClassChild (levelId, childLayerId = this->AddChildLayer (levelId, " (Pattern)", this->GetViewDisplayFlags().patterns));

    return childLayerId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetLinearPatternedClassLayer
(
UInt32      levelId
) const
    {
    AcDbObjectId            childLayerId;

    if ((childLayerId = m_pFileHolder->GetLayerIndex()->GetLinearPatternedClassChild (levelId)).isNull())
         m_pFileHolder->GetLayerIndex()->SetLinearPatternedClassChild (levelId, childLayerId = this->AddChildLayer (levelId, " (Linear Patterned)", !this->GetViewDisplayFlags().patterns));

    return childLayerId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetDefPointsLayer
(
)
    {
    AcString defPointsLayerName (L"DEFPOINTS");

    if ((m_defPointsLayerId = m_pFileHolder->GetLayerByName (defPointsLayerName)).isNull())
        m_defPointsLayerId = this->AddLayer (defPointsLayerName, 0, NULL);

    return m_defPointsLayerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::GetMSInsertLayer ()
    {
    WStringCR   layerName = GetSettings().GetInsertLayerName();

    if (layerName.length() > 0)
        {
        AcString    validLayerName = layerName.c_str();
        this->ValidateName (validLayerName);

        if ((m_msInsertLayerId = m_pFileHolder->GetLayerByName (validLayerName)).isNull())
            m_msInsertLayerId = this->AddLayer (validLayerName, 0, NULL);
        }

    return m_msInsertLayerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapColorTokenToDgn
(
AcString                    token,
ConvertToDgnContext&        dgnContext
)
    {
    // remap color index to dgn index - level filter does not seem to support true color & color book
    if (!token.isEmpty())
        {
        int     dwgIndex = 0;
        if (1 == swscanf(token.kwszPtr(), L"%d", &dwgIndex))
            {
            UInt32  dgnIndex = dgnContext.GetDgnColor (dwgIndex);
            char    newToken[48];
            sprintf (newToken, "%d", dgnIndex);
            token.assign (newToken, AcString::Utf8);
            }
        }
    return  token;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapWeightTokenToDgn
(
AcString                    token,
ConvertToDgnContext&        dgnContext
)
    {
    if (!token.isEmpty())
        {
        AcDb::LineWeight    dwgWeight;
        bool                hasValue = false;
        const ACHAR*        pTokenStart = token.kwszPtr ();
        if (0 == token.compareNoCase(L"Default") || L'-' == *pTokenStart)
            {
            dwgWeight = RealDwgHostApp::Instance().workingAppSysvars()->lwdefault();
            hasValue = true;
            }
        else if (iswdigit(*pTokenStart))
            {
            // remove spaces & units in line weight token:
            ACHAR*          pEndAt = NULL;
            double          mmValue = wcstod (pTokenStart, &pEndAt);
            if (HUGE_VAL != fabs(mmValue) && (0.0 != mmValue || NULL != pEndAt))
                {
                int         intValue = (int)(100 * mmValue);
                dwgWeight = (AcDb::LineWeight)intValue;
                hasValue = true;
                }
            }

        if (hasValue)
            {
            UInt32      dgnIndex = dgnContext.GetDgnWeight (dwgWeight);
            char        newToken[48];
            sprintf (newToken, "%d", dgnIndex);
            token.assign (newToken, AcString::Utf8);
            }
        }
    return  token;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapFilterTokenToDgn
(
WCharCP                     pToken,
size_t                      length,
bool                        isNameString
)
    {
    // TR# 144240 - Only include the quotes if processing the name string.
    if (isNameString)
        {
        WCharP    temp = (WCharP) _alloca ((length + 3) * sizeof (WChar));
        temp[0] = '"';
        wcsncpy (&temp[1], pToken, length);
        temp[length+1] = '"';
        temp[length+2] = 0;
        return AcString (temp);
        }
    else
        return AcString (pToken);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapFilterOperatorToDgn
(
const WChar               token
)
    {
    switch (token)
        {
        case '~':
            return AcString (L"-");
        case '.':
            return AcString (L"&");

        default:
            return AcString (token);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetLevelFilterStringExpression
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
WCharCP                   pExpression,
WCharP                    pMemberName
)
    {
#ifndef BUILD_DWGPLATFORM
    UInt            memberId;
    bool            isNameString = 0 == wcscmp (pMemberName, LEVEL_FILTER_MEMBER_NAME);
    static WChar* s_pOperatorString = L"&|,~. ";

    if ( ('\0' != pExpression[0]) && (0 != wcscmp (L"*", pExpression)) &&
         (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, pFilterTable, pMemberName)) )
        {
        AcString          outputString;
        for (WCharCP pInputChar = pExpression, pEnd = pInputChar + wcslen (pExpression); pInputChar < pEnd; )
            {
            if ('"' == *pInputChar)
                {
                const WChar   *pTokenStart = pInputChar++;
                while (pInputChar < pEnd && *pInputChar != '"')
                    pInputChar ++;

                RealDwgUtil::AppendCharsToAcString (outputString, pTokenStart, (UInt32)(++pInputChar - pTokenStart));
                }
            else if (NULL != wcschr (s_pOperatorString, *pInputChar))
                {
                outputString += RemapFilterOperatorToDgn (*pInputChar++);
                }
            else
                {
                WCharCP   pTokenStart = pInputChar;
                while (pInputChar < pEnd && NULL == wcschr (s_pOperatorString, *pInputChar))
                    pInputChar++;

                outputString += RemapFilterTokenToDgn (pTokenStart, pInputChar - pTokenStart, isNameString);
                }
            }

        UInt32  numChars = outputString.length ();
        if (numChars > 0)
            {
            WCharP    wideString = (WCharP)_alloca ((numChars + 1) * sizeof(WChar));
            RealDwgUtil::AcStringToMSWChar (wideString, outputString, numChars + 1);
            mdlFilterDescr_setExpression (ppFilterEntry, memberId, wideString);
            }
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetLevelFilterBooleanExpression
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
int                         value,
WCharP                    pMemberName
)
    {
#ifndef BUILD_DWGPLATFORM
    UInt32          memberId;

    if ( (0 != value) && (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, pFilterTable, pMemberName)) )
        mdlFilterDescr_setExpression (ppFilterEntry, memberId, value == 3 ? L"0" : L"1");
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetCompositeLevelFilterExpression
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
const AcString&             expression
)
    {
    if (expression.isEmpty())
        return  CantCreateLevelFilter;

#ifndef BUILD_DWGPLATFORM
    UInt32          memberId;
    if (SUCCESS != mdlFilterTableDescr_getMemberIdFromName(&memberId, pFilterTable, FILTER_MEMBER_COMPOSE))
        return  CantCreateLevelFilter;

    UInt32          numChars = expression.length() + 1;
    WCharP        wString = (WCharP)_alloca (numChars * sizeof(WChar));
    RealDwgUtil::AcStringToMSWChar (wString, expression.kwszPtr(), numChars);

    StatusInt       status = mdlFilterDescr_setExpression (ppFilterEntry, memberId, wString);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
#else
    return  CantAccessMstnElement;
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetLevelGroupExpression
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
UInt32                      entryId,
UInt32                      parentId,
RealDwgResBuf*              pResbufIn
)
    {
    /*-----------------------------------------------------------------------------------
    This method harvests layer object ID's directly stored on an ACLYDICTIONARY xrecord,
    find layer names and set them in a level group.
    -----------------------------------------------------------------------------------*/
    if (NULL == ppFilterEntry || NULL == *ppFilterEntry)
        return  CantCreateLevelFilter;

    AcString    layerNames;
    for (RealDwgResBuf* pResbuf = pResbufIn; NULL != pResbuf; pResbuf = pResbuf->GetNext())
        {
        if (AcDb::kDxfSoftPointerId == pResbuf->GetResType())
            {
            AcDbObjectId    layerId = pResbuf->GetObjectId ();
            if (layerId.isValid())
                {
                AcDbLayerTableRecordPointer layer(layerId, AcDb::kForRead);
                if (Acad::eOk == layer.openStatus())
                    {
                    AcString    name;
                    if (Acad::eOk == layer->getName(name))
                        {
                        if (!layerNames.isEmpty())
                            layerNames += L" | ";
                        layerNames += L'\"' + name + L'\"';
                        }
                    }
                }
            }
        }

    if (layerNames.isEmpty())
        return  CantCreateLevelFilter;

#ifndef BUILD_DWGPLATFORM
    UInt32      memberId = 0;
    StatusInt   status = mdlFilterTableDescr_getMemberIdFromName (&memberId, pFilterTable, LEVEL_FILTER_MEMBER_LEVEL_GROUP);
    if (SUCCESS == status)
        {
        UInt32          numChars = layerNames.length() + 1;
        WCharP        wString = (WCharP)_alloca (numChars * sizeof(WChar));
        RealDwgUtil::AcStringToMSWChar (wString, layerNames.kwszPtr(), numChars);

        status = mdlFilterDescr_setExpression (ppFilterEntry, memberId, wString);
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
#else
    return  CantAccessMstnElement;
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SetLevelFilterExpression
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
AcDbXrecord*                pXrecord
)
    {
    int             index;
    RealDwgResBuf*  pResBuf;

    if (Acad::eOk != pXrecord->rbChain (reinterpret_cast <struct resbuf **> (&pResBuf)))
        return  XDataError;

    RealDwgResBuf*  pResbufHead = pResBuf;

    for (index = 0; NULL != pResBuf; pResBuf = pResBuf->GetNext(), index++)
        {
        switch (pResBuf->GetResType())
            {
            case AcDb::kDxfText:
                {
                ACHAR const*    pString = pResBuf->GetString();
                AcString        remappedString;
                switch (index)
                    {
                    case 1:
                        SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, pString, LEVEL_FILTER_MEMBER_NAME);
                        break;

                    case 2:
                        remappedString = RemapColorTokenToDgn (pString, *this);
                        SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, remappedString.kwszPtr(), LEVEL_FILTER_MEMBER_ELEMENT_COLOR);
                        break;

                    case 3:
                        SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, pString, LEVEL_FILTER_MEMBER_ELEMENT_STYLE);
                        break;

                    case 5:
                        remappedString = RemapWeightTokenToDgn (pString, *this);
                        SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, remappedString.kwszPtr(), LEVEL_FILTER_MEMBER_ELEMENT_WEIGHT);
                        break;

                    case 6:
                        // Needs work... plot style.
                        break;
                    }
                break;
                }

            case AcDb::kDxfInt16:
                {
                int filterBooleanValueFlags = pResBuf->GetInt16();
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 0), LEVEL_FILTER_MEMBER_DISPLAY);
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 1), LEVEL_FILTER_MEMBER_FROZEN);
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 4), LEVEL_FILTER_MEMBER_ELEMENT_ACCESS);
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 5), LEVEL_FILTER_MEMBER_PLOT);
#if defined (NOT_IMPLEMENTED_IN_MICROSTATION)
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 2), LEVEL_FILTER_MEMBER_CURRENT_FROZEN);
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, FILTERVALUE(filterBooleanValueFlags, 3), LEVEL_FILTER_MEMBER_NEW_FROZEN);
#endif
                break;
                }
            }
        }
    RealDwgResBuf::Free (pResbufHead);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 FindLayerFilterInfoFromResbuf
(
AcString&                   filterNameOut,
AcString&                   filterStringOut,
bool*                       pHasLayerGroupOut,
RealDwgResBuf*              pResbufIn,
const ACHAR*                filterKeyIn
)
    {
    if (NULL != pHasLayerGroupOut)
        *pHasLayerGroupOut = false;

    // the parent level filter name is stored as DXF group code 300 in the xrecord:
    for (RealDwgResBuf* pResbuf = pResbufIn; NULL != pResbuf; pResbuf = pResbuf->GetNext())
        {
        if (AcDb::kDxfXTextString == pResbuf->GetResType())
            {
            ACHAR const*    filterName = pResbuf->GetString ();
            if (NULL != filterName)
                {
                // this is the nested filter xrecord - return both filter name and string
                filterNameOut.assign (filterName);
                if (NULL == filterKeyIn || 0 == wcscmp(filterName, filterKeyIn))
                    {
                    // filter string is the entry right after filter name:
                    pResbuf = pResbuf->GetNext ();
                    if (NULL != pResbuf && DXFCODE_LayerFilterString == pResbuf->GetResType())
                        filterStringOut.assign (pResbuf->GetString());

                    // followed by grouped layer ID's
                    if (NULL == pHasLayerGroupOut)
                        return  true;
                    for (; NULL != pResbuf; pResbuf = pResbuf->GetNext())
                        {
                        if (AcDb::kDxfSoftPointerId == pResbuf->GetResType())
                            {
                            *pHasLayerGroupOut = true;
                            return  true;
                            }
                        }
                    if (NULL == pResbuf)
                        break;
                    }
                }
            }
        }

    return  !filterNameOut.isEmpty();
    }
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static Int16                GetFilterType (const AcString& typeString)
    {
    if (!typeString.isEmpty())
        {
        for (Int16 i = 0; i < s_numberOfFilterTypes; i++)
            {
            if (0 == typeString.compare(s_layerFilterTypes[i].m_typeName))
                return  s_layerFilterTypes[i].m_filterType;
            }
        }

    return  -1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendSubsequentNameFilters
(
AcString&                   valueString,
unsigned&                   valueEnd,
const AcString&             filterString
)
    {
    unsigned        nextStart = valueEnd, numChars = 0;
    ACHAR           operatorChar;
    AcString        nextString = filterString.substr (valueEnd, -1);
    while (!nextString.isEmpty())
        {
        if (0 == nextString.find(L"AND "))
            {
            operatorChar = L'&';
            numChars = 4;
            }
        else if (0 == nextString.find(L"OR "))
            {
            operatorChar = L'|';
            numChars = 3;
            }
        else
            {
            break;
            }

        nextString = nextString.substr (numChars, -1);
        nextStart += numChars;

        AcString    exclusiveOp;

        // if next filter is not of type NAME, we are done:
        if (0 != nextString.find(L"NAME==\""))
            {
            if (0 != nextString.find(L"~NAME==\""))
                break;
            else
                exclusiveOp.assign (L"~");
            }

        // move over NAME=="
        numChars = nextString.find(L'\"') + 1;
        nextString = nextString.substr (numChars, -1);
        nextStart += numChars;
        if (nextString.isEmpty())
            break;

        // harvest next value string
        if ((numChars = nextString.find (L'\"')) <= 0)
            break;
        valueString += AcString (operatorChar);
        if (!exclusiveOp.isEmpty())
            valueString += exclusiveOp;
        valueString += nextString.substr (numChars++);
        nextStart += numChars;

        // move on to next filter
        nextString = nextString.substr (numChars, -1);

        // remove front white spaces for above checks of operators AND and OR:
        for (numChars = 0; nextString[numChars] == 0x20; numChars++)
            nextStart++;
        if (numChars > 0)
            nextString = nextString.substr (numChars, -1);

        valueEnd = nextStart;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SetLevelFilterExpressionFromFilterString
(
MSElementDescrP             pFilterTable,
MSElementDescrH             ppFilterEntry,
AcString                    filterString
)
    {
    /*-----------------------------------------------------------------------------------
    Create and set level filters that are chained by logical AND's.  Name filters may 
    contain OR's, AND's or both.
    -----------------------------------------------------------------------------------*/
    int             nextAt = -1;
    while (!filterString.isEmpty() && (nextAt = filterString.find(L"==\"")) >= 0)
        {
        // get filter type before & value after ==", for example COLOR=="25":
        AcString    typeString = filterString.substr (nextAt);
        nextAt += 3;
        filterString = filterString.substr (nextAt, -1);

        unsigned    valueEnd = filterString.find (L'\"');
        AcString    valueString = filterString.substr (0, valueEnd);

        // remove white spaces after the value string:
        valueEnd++;
        while (valueEnd < filterString.length() && L' ' == filterString.kwszPtr()[valueEnd])
            valueEnd++;
        if (valueEnd > filterString.length())
            break;

        // insert the exclusive operator into value string, and remove it from type string:
        if ('~' == typeString[0])
            {
            valueString = AcString(typeString[0]) + valueString;
            typeString = typeString.substr (1, -1);
            }

        switch (GetFilterType(typeString))
            {
            case 1:
                AppendSubsequentNameFilters (valueString, valueEnd, filterString);
                SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, valueString, LEVEL_FILTER_MEMBER_NAME);
                break;
            case 2:
                valueString = RemapColorTokenToDgn (valueString, *this);
                SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, valueString, LEVEL_FILTER_MEMBER_ELEMENT_COLOR);
                break;
            case 3:
                SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, valueString, LEVEL_FILTER_MEMBER_ELEMENT_STYLE);
                break;
            case 4:
                valueString = RemapWeightTokenToDgn (valueString, *this);
                SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, valueString, LEVEL_FILTER_MEMBER_ELEMENT_WEIGHT);
                break;
            case 5:
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, 0==valueString.compareNoCase(L"FALSE") ? 1:3, LEVEL_FILTER_MEMBER_DISPLAY);
                break;
            case 6:
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, 0==valueString.compareNoCase(L"TRUE") ? 1:3, LEVEL_FILTER_MEMBER_FROZEN);
                break;
            case 7:
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, 0==valueString.compareNoCase(L"TRUE") ? 1:3, LEVEL_FILTER_MEMBER_ELEMENT_ACCESS);
                break;
            case 8:
                SetLevelFilterBooleanExpression (pFilterTable, ppFilterEntry, 0==valueString.compareNoCase(L"TRUE") ? 1:3, LEVEL_FILTER_MEMBER_PLOT);
                break;
            case 9:
                SetLevelFilterStringExpression (pFilterTable, ppFilterEntry, valueString, LEVEL_FILTER_MEMBER_TRANSPARENCY);
                break;
            default:
                DIAGNOSTIC_PRINTF ("Unknown level filter type = %ls\n", typeString);
            }

        // move on to next filter value
        filterString = filterString.substr (valueEnd, -1);
        if (0 == filterString.find(L"AND "))
            filterString = filterString.substr (4, -1);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 HasOnlyNameFilters (const AcString& stringIn)
    {
    // if it does not start with a name filter, don't bother to check.
    if (stringIn.isEmpty() || 0 != stringIn.find(L"NAME==\""))
        return  false;

    AcString    nextString = stringIn;
    int         index = -1;
    while (!nextString.isEmpty() && (index = nextString.find(L" AND ")) >= 0)
        {
        nextString = nextString.substr (index + 5, -1);
        if (!nextString.isEmpty() && 0 != nextString.find(L"NAME==\""))
            return  false;
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 CanKeepNameFilter (int& filterEnd, const AcString& stringIn)
    {
    /*-----------------------------------------------------------------------------------
    Check input string to see if it starts with a name filter which can be added to a 
    string of all name filters.  We need to check 2 consective filters after the 1st one.
    This is such a name filter that can only be followed by a name filter, which is 
    followed by an OR operator or AND operator that is followed by yet another name 
    filter.  For instance:

        NAME="a*" OR NAME="b*" AND COLOR="3" ...

    should fail this test because NAME="b*" AND COLOR="3" shall be grouped in one which
    consists of two different filters(i.e. NAME + COLOR).
    -----------------------------------------------------------------------------------*/
    if (stringIn.isEmpty() || 0 != stringIn.find(L"NAME==\""))
        return  false;
    
    // the 1st of the consecutive entries is a name filter - a candidate to be kept.
    AcString    nextString = stringIn.substr (7, -1);
    int         endAt = 0;
    if (!nextString.isEmpty() && (endAt = nextString.find(L'\"')) >= 0)
        {
        endAt++;
        filterEnd = 7 + endAt;
        nextString = nextString.substr (endAt, -1);
        if (nextString.isEmpty())
            return  true;

        if (0 == nextString.find(L" AND "))
            {
            nextString = nextString.substr (5, -1);

            // this is the 2nd entry - if it is NOT a name filter, we cannot keep it in the same group!
            return  !nextString.isEmpty() && 0 == nextString.find(L"NAME==\"");
            }
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 DeleteFilterEntryDescr
(
MSElementDescrH             ppFilterEntry,
MSElementDescrP             pFilterTable
)
    {
    if (pFilterTable->h.firstElem == *ppFilterEntry)
        pFilterTable->h.firstElem = NULL;
    else if (NULL != (*ppFilterEntry)->h.previous)
        (*ppFilterEntry)->h.previous->h.next = NULL;
    (*ppFilterEntry)->Release ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             GetUniqueSubFilterName
(
bool&                       createNewFilter,
const AcString&             rootFilterName,
const AcString&             rootFilterString,
int                         suffixIndex,        
const AcDbDictionary*       pDictionary
)
    {
    /*-----------------------------------------------------------------------------------
    We'd like to make a unique sub-filter name by appending an index to the root filter
    name, but we'd also want to be sure that the new name is not already in use.
    -----------------------------------------------------------------------------------*/
    AcString    childFilterName = rootFilterName;
    int         iTry = 0;

    createNewFilter = true;

    for (; iTry < 20; iTry++)
        {
        childFilterName += AcString(L'_') + AcString(AcString::kSigned, suffixIndex);        

        bool    isNameUsed = false, canReuseName = false;
        AcDbDictionaryIterator* pIterator = pDictionary->newIterator ();
        for (; !pIterator->done(); pIterator->next())
            {
            AcDbXrecordPointer  pXrecord(pIterator->objectId(), AcDb::kForRead);
            if (Acad::eOk != pXrecord.openStatus())
                continue;
            RealDwgResBuf*      pResbuf = NULL;
            if (Acad::eOk != pXrecord->rbChain (reinterpret_cast <struct resbuf **> (&pResbuf)))
                continue;
            AcString            filterName, filterString;
            if (FindLayerFilterInfoFromResbuf(filterName, filterString, NULL, pResbuf, NULL) && 0 == filterName.compareNoCase(childFilterName))
                {
                isNameUsed = true;
                // filter name is used, but if the filter content is the same, we can re-use it, and do not need to create a new one.
                canReuseName = 0 == filterString.compareNoCase(rootFilterString);
                if (canReuseName)
                    createNewFilter = false;                
                break;
                }
            RealDwgResBuf::Free (pResbuf);
            }
        delete pIterator;

        if (!isNameUsed || canReuseName)
            break;
        }

    BeAssert (iTry < 20 && L"failed to build child filter name!");

    return  childFilterName;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::BreakDownLevelFilters
(
MSElementDescrP             pFilterTable,       // <=>
MSElementDescrH             ppFilterEntry,      // <=>
UInt32&                     entryId,            // <=>
UInt32                      parentId,           //  =>
AcString                    filterString,       //  =>
const AcString&             filterName,         //  =>
const AcString&             filterPath,         //  =>
const AcDbDictionary*       pDictionary         //  =>
)
    {
    /*-----------------------------------------------------------------------------------
    Due to lack of union support for cross type filters(for example: color | weight) in
    our filter sub-system, we need to parse logical OR's in filter string and break it 
    down to multiple filters.  Then we union these new filters as a Filter Group.
    We do not need to break down the OR operators within a name filter, which supports 
    all operators.
    -----------------------------------------------------------------------------------*/
    int             charIndex = -1, unionCount = 0, wholeStringLength = (int)filterString.length();
    bool            createSubFilter = true;
    UInt32          unionEntryId = entryId;
    AcString        unionFilterString, subFilterName;
    RealDwgStatus   status = CantCreateLevelFilter;

    while (!filterString.isEmpty() && (charIndex = filterString.find(L"\" OR ")) >= 0)
        {
        charIndex += 1;
        AcString    stringBefore = filterString.substr (0, charIndex);
        charIndex += 4;
        AcString    stringAfter = filterString.substr (charIndex, -1);

        if (HasOnlyNameFilters(stringBefore))
            {
            // collect all name filters and keep their original logical operators by just moving charIndex:
            int     operatorSize = 0, filterEnd = 0, numFiltersToAdd = 0;
            while (CanKeepNameFilter(filterEnd, stringAfter))
                {
                charIndex += filterEnd + operatorSize;
                // update stringAfter for next run - do not update charIndex:
                stringAfter = stringAfter.substr (filterEnd, -1);
                if (!stringAfter.isEmpty())
                    {
                    if (0 == stringAfter.find(L" OR "))
                        operatorSize = 4;
                    else if (0 == stringAfter.find(L" AND "))
                        operatorSize = 5;
                    else
                        operatorSize = 0;
                    stringAfter = stringAfter.substr (operatorSize, -1);
                    }
                // break out on string exhaustion
                if (numFiltersToAdd++ > 1000 || charIndex >= wholeStringLength)
                    break;
                }

            // in case the whole string consists of only name filters, break out to set the string as one piece filter.
            if (charIndex >= wholeStringLength)
                break;

            // get all name filter strings:
            if (numFiltersToAdd > 0)
                stringBefore = filterString.substr (0, charIndex);

            // move to next string(post name filter strings):
            filterString = charIndex < 0 ? L"" : filterString.substr(charIndex+operatorSize, -1);
            }
        else
            {
            // move to next string(after the OR operator):
            filterString = filterString.substr (charIndex, -1);
            }

        // make the sub-filter name by appending a counter at the end of currently filter name:
        subFilterName = GetUniqueSubFilterName (createSubFilter, filterName, stringBefore, ++unionCount, pDictionary);
        // append the sub filter name to union filter string:
        if (unionCount > 1)
            unionFilterString += L" | ";
        unionFilterString += L'\"' + filterPath + L'.' + subFilterName + L'\"';

        if (!createSubFilter)
            continue;

        // create a new sub-filter entry for the union filter:
#ifndef BUILD_DWGPLATFORM
        MSElementDescrP pSubFilter = NULL;
        if (SUCCESS == mdlFilterDescr_createFilterEntryWithId(&pSubFilter, 0, ++entryId, parentId, subFilterName.kwszPtr(), FILTER_TYPE_USER, pFilterTable))
            {
            status = this->SetLevelFilterExpressionFromFilterString (pFilterTable, &pSubFilter, stringBefore);
            if (RealDwgSuccess != status)
                {
                entryId--;
                DeleteFilterEntryDescr (&pSubFilter, pFilterTable);
                }
            }
#endif
        }

    // Create the last sub-filter or the original level filter as a whole if there is no union:
    if (!filterString.isEmpty())
        {
        MSElementDescrP     pLastFilter = *ppFilterEntry;

        if (unionCount > 0)
            {
            subFilterName = GetUniqueSubFilterName (createSubFilter, filterName, filterString, ++unionCount, pDictionary);
            unionFilterString += L" | \"" + filterPath + L'.' + subFilterName + L'\"';
            
            // create a new sub-filter entry for the last sub-filter:
            if (!createSubFilter)
                {
#ifndef BUILD_DWGPLATFORM
                MSElementDescrP pLastSubFilter = NULL;
                if (SUCCESS == mdlFilterDescr_createFilterEntryWithId(&pLastSubFilter, 0, ++entryId, parentId, subFilterName.kwszPtr(), FILTER_TYPE_USER, pFilterTable))
                    pLastFilter = pLastSubFilter;
                else
#endif
                    pLastFilter = NULL;
                }
            else
                {
                pLastFilter = NULL;
                }
            }

        if (NULL != pLastFilter)
            {
            status = this->SetLevelFilterExpressionFromFilterString (pFilterTable, &pLastFilter, filterString);

            if (unionCount > 0 && RealDwgSuccess != status)
                {
                entryId--;
                DeleteFilterEntryDescr (&pLastFilter, pFilterTable);
                }
            }

        // if there is no union, make sure to return the possibly replaced entry element descriptor:
        if (0 == unionCount && RealDwgSuccess == status)
            *ppFilterEntry = pLastFilter;

        // the caller shall update entryId and/or free element if there is no union.
        }

    // Finally, set union filter with our union expression:
    if (unionCount > 0)
        status = SetCompositeLevelFilterExpression (pFilterTable, ppFilterEntry, unionFilterString);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateNestedLevelFilters
(
MSElementDescrP             pFilterTable,       // <=>
UInt32&                     entryId,            // <=>
UInt32                      parentId,           //  =>
AcDbObjectId                filterId,           //  =>
const AcString&             parentPathIn        //  =>
)
    {
    AcDbDictionaryPointer   pNestedFilterDictionary (filterId, AcDb::kForRead);
    if (Acad::eOk != pNestedFilterDictionary.openStatus())
        return  CantOpenObject;
    
    AcDbDictionaryIterator* pIterator = pNestedFilterDictionary->newIterator ();
    for (; !pIterator->done(); pIterator->next())
        {
        AcDbXrecordPointer  pXrecord(pIterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != pXrecord.openStatus())
            continue;

        RealDwgResBuf*      pResbuf = NULL;
        if (Acad::eOk != pXrecord->rbChain (reinterpret_cast <struct resbuf **> (&pResbuf)))
            continue;

        AcString            filterName, filterString;
        bool                hasLayerGroup = false;
        FindLayerFilterInfoFromResbuf (filterName, filterString, &hasLayerGroup, pResbuf, NULL);

        // save off current table entry ID as the parent ID for nested filters.
        UInt32              nextParentId = entryId;

        if (!filterString.isEmpty() || hasLayerGroup)
            {
#ifndef BUILD_DWGPLATFORM
            MSElementDescrP pNestedFilter = NULL;
            if (SUCCESS == mdlFilterDescr_createFilterEntryWithId(&pNestedFilter, 0, entryId, parentId, filterName.kwszPtr(), FILTER_TYPE_USER, pFilterTable))
                {
                bool    addedLevelGroup = false, addedLevelFilter = false;
                if (hasLayerGroup)
                    addedLevelGroup = RealDwgSuccess == SetLevelGroupExpression(pFilterTable, &pNestedFilter, entryId, parentId, pResbuf);

                if (!filterString.isEmpty())
                    addedLevelFilter = RealDwgSuccess == this->BreakDownLevelFilters(pFilterTable, &pNestedFilter, entryId, parentId, filterString, filterName, parentPathIn, pNestedFilterDictionary);
                else if (!hasLayerGroup)
                    addedLevelFilter = true;    // need to add a default filter

                if (addedLevelGroup || addedLevelFilter)
                    {
                    entryId++;
                    pNestedFilter->el.ehdr.uniqueId = this->ElementIdFromObject (pXrecord);
                    }
                else
                    {
                    DeleteFilterEntryDescr (&pNestedFilter, pFilterTable);
                    RealDwgResBuf::Free (pResbuf);
                    continue;
                    }
                }
#endif
            }

        RealDwgResBuf::Free (pResbuf);

        AcDbObjectId        childFilterId = pXrecord->extensionDictionary();
        if (!childFilterId.isValid())
            continue;

        // update filter path
        AcString            nextParentPath = parentPathIn + L'.' + filterName;

        AcDbDictionaryPointer   pExtensionDictionary (childFilterId, AcDb::kForRead);
        if (Acad::eOk == pExtensionDictionary.openStatus() && Acad::eOk == pExtensionDictionary->getAt(StringConstants::XDictionaryItem_AcLyDictionary, childFilterId) && childFilterId.isValid())
            CreateNestedLevelFilters (pFilterTable, entryId, nextParentId, childFilterId, nextParentPath);
        }

    delete pIterator;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateLevelFiltersFromAclyDictionary
(
MSElementDescrP             pFilterTable,       // <=>
MSElementDescrH             ppOldFilterEntry,   // <=>
UInt32&                     entryId,            // <=>
UInt32                      parentId,           //  =>
const ACHAR*                filterKey,          //  =>
AcDbObjectId                filterId,           //  =>
AcDbDictionary*             pOldDictionary      //  =>
)
    {
    /*-----------------------------------------------------------------------------------
    This method starts from root ACLYDICTIONARY and drills down on its hierachy to get
    and create nested layer filters and layer groups.

    For layer filters, it processes ACLYDICTIONARY for 
        1) only the filters which are also present in flat ACAD_LAYERFILTERS, or 
        2) only those that are not part of the flat dictionary.
    
    For layer groups, it adds level groups from each filter that contains layer ID's.

    The name "old" refers to ACAD_LAYERFILTERS.
    -----------------------------------------------------------------------------------*/
    if (FILTER_NULL_ID != parentId && (NULL == filterKey || 0 == *filterKey))
        return  InvalidName;

    AcDbDictionaryPointer   pTopFilterDictionary (filterId, AcDb::kForRead);
    if (Acad::eOk != pTopFilterDictionary.openStatus())
        return  CantOpenObject;

    UInt32      nextParentId;

    AcDbDictionaryIterator* pIterator = pTopFilterDictionary->newIterator ();
    for (; !pIterator->done(); pIterator->next())
        {
        AcDbXrecordPointer  pXrecord(pIterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != pXrecord.openStatus())
            continue;

        RealDwgResBuf*      pResbuf = NULL;
        if (Acad::eOk != pXrecord->rbChain (reinterpret_cast <struct resbuf **> (&pResbuf)))
            continue;

        AcString            filterName, filterString;
        bool                hasLevelGroup = false;
        if (!FindLayerFilterInfoFromResbuf (filterName, filterString, &hasLevelGroup, pResbuf, filterKey))
            {
            RealDwgResBuf::Free (pResbuf);
            continue;
            }

        AcDbObjectId        childFilterId = pXrecord->extensionDictionary ();

        if (FILTER_NULL_ID != parentId)
            {
            /*-------------------------------------------------------------------------------
            Process 1 - harvest child layer filters from ACLYDICTIONARY for the input parent.

            In this process, the top level of layer filter in ACLYDICTIONARY has the same 
            information as that in ACAD_LAYERFILTERS we have already processed, so we do not 
            need to parse filer string for top level of layer filters.  Just keep drilling 
            down on the hierachy.  However, we still need to set the filter entry already
            created from ACAD_LAYERFILTERS with the layer group that also only exists in
            ACLYDICTIONARY.
            -------------------------------------------------------------------------------*/
            if (hasLevelGroup && NULL != ppOldFilterEntry)
                SetLevelGroupExpression (pFilterTable, ppOldFilterEntry, entryId, parentId, pResbuf);
            RealDwgResBuf::Free (pResbuf);

            if (filterString.isEmpty() || !childFilterId.isValid())
                continue;
            nextParentId = parentId;
            }
        else
            {
            /*---------------------------------------------------------------------------
            Process 2 - harvest ACLYDICTIONARY filters that do not overlap with the ones
                        found in ACAD_LAYERFILTERS.
            ---------------------------------------------------------------------------*/
            if (pOldDictionary->has(filterName))
                {
                RealDwgResBuf::Free (pResbuf);
                continue;
                }
            nextParentId = entryId;

#ifndef BUILD_DWGPLATFORM
            MSElementDescrP pTopFilter = NULL;
            if (SUCCESS == mdlFilterDescr_createFilterEntryWithId(&pTopFilter, 0, entryId, parentId, filterName.kwszPtr(), FILTER_TYPE_USER, pFilterTable))
                {
                bool        addedLevelGroup = false, addedLevelFilter = false;

                // set level group on new filter entry element:
                if (hasLevelGroup)
                    addedLevelGroup = RealDwgSuccess == SetLevelGroupExpression(pFilterTable, &pTopFilter, entryId, parentId, pResbuf);
                // break down the OR operators into multiple sub-filters and build a new union filter that can be supported by our filter sub-system:
                if (!filterString.isEmpty())
                    addedLevelFilter = RealDwgSuccess == this->BreakDownLevelFilters(pFilterTable, &pTopFilter, entryId, parentId, filterString, filterName, L"", pTopFilterDictionary);
                else if (!hasLevelGroup)
                    addedLevelFilter = true;    // need to add a default filter

                if (addedLevelGroup || addedLevelFilter)
                    {
                    entryId++;
                    pTopFilter->el.ehdr.uniqueId = this->ElementIdFromObject (pXrecord);
                    }
                else
                    {
                    DeleteFilterEntryDescr (&pTopFilter, pFilterTable);
                    RealDwgResBuf::Free (pResbuf);
                    continue;
                    }
                }
#endif
            RealDwgResBuf::Free (pResbuf);
            }
        
        // now recurve into nested filters:
        AcDbDictionaryPointer   pExtensionDictionary (childFilterId, AcDb::kForRead);
        if (Acad::eOk == pExtensionDictionary.openStatus() && Acad::eOk == pExtensionDictionary->getAt (StringConstants::XDictionaryItem_AcLyDictionary, childFilterId) && childFilterId.isValid())
            CreateNestedLevelFilters (pFilterTable, entryId, nextParentId, childFilterId, filterName);
        }

    delete pIterator;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveLayerFiltersToDgn ()
    {
    AcDbLayerTablePointer   pLayers (m_pFileHolder->GetDatabase()->layerTableId(), AcDb::kForRead);
    AcDbObjectId            extDictId;

    if ( (extDictId = pLayers->extensionDictionary()).isNull() )
        return BSIERROR;

    AcDbDictionaryPointer   pExtensionDictionary (extDictId, AcDb::kForRead);
    if (Acad::eOk != pExtensionDictionary.openStatus())
        return BSIERROR;

    AcDbObjectId            layerFilterId;
    if ( Acad::eOk != pExtensionDictionary->getAt (StringConstants::XDictionaryItem_LayerFilters, layerFilterId) )
        return BSIERROR;

    if (layerFilterId.isNull())
        return BSIERROR;

    AcDbDictionaryPointer   pLayerFilterDictionary (layerFilterId, AcDb::kForRead);
    if (Acad::eOk != pLayerFilterDictionary.openStatus())
        return BSIERROR;

    // Don't attempt to export if there is an excessive number of entries (TR# 117722).
    if (pLayerFilterDictionary->numEntries() > this->GetDefaultConversionSettings()->GetMaxDictionaryItems())
        {
        DIAGNOSTIC_PRINTF ("Ignoring Excessive Layer Filters (%d)\n", pLayerFilterDictionary->numEntries());
        return BSIERROR;
        }

    /*-----------------------------------------------------------------------------------
    There are two extension dictionaries under layer table both of which are used for
    layer filters.  ACAD_LAYERFILTERS appears as a legacy filter dictionary that only has
    flat filters whereas as ACLYDICTIONARY contains full information of hierarchical layer
    filters.  Theoretically we probably only need the hierarchical ACLYDICTIONARY and ignore 
    the flat ACAD_LAYERFILTERS, but it is not clear to me ever since when ACAD_LAYERFILTERS
    has become a legacy.  There is a good chance that DWG files created from old versions
    of AutoCAD may still only have ACAD_LAYERFILTERS.  Thus it is safer to leave our old 
    code that processes the flat ACAD_LAYERFILTERS in place.  What has been added now is
    to use ACLYDICTIONARY for nested layer filters and layer groups that are not present
    in ACAD_LAYERFILTERS.
    -----------------------------------------------------------------------------------*/
    AcDbObjectId            aclyDictionaryId;
    if (Acad::eOk != pExtensionDictionary->getAt(StringConstants::XDictionaryItem_AcLyDictionary, aclyDictionaryId))
        DIAGNOSTIC_PRINTF ("ACLYDICTIONARY in the layer table cannot be opened!\n");

#ifndef BUILD_DWGPLATFORM
    MSElementDescr          *pFilterTable = NULL;
    UInt32                  entryId = 2;

    mdlLevelFilterTableDescr_create (&pFilterTable, TRUE, this->ElementIdFromObjectId(pLayerFilterDictionary->objectId()));

    AcDbDictionaryIterator*     pIterator = pLayerFilterDictionary->newIterator();
    for ( ; !pIterator->done(); pIterator->next())
        {
        AcDbObjectId        entityId;

        if ( (entityId = pIterator->objectId()).isNull() )
            continue;

        AcDbXrecordPointer  pXrecord (entityId, AcDb::kForRead);
        if (Acad::eOk != pXrecord.openStatus())
            continue;

        MSElementDescrP     pFilterEntryDescr = NULL;
        if (SUCCESS == mdlFilterDescr_createFilterEntryWithId (&pFilterEntryDescr, 0, entryId, FILTER_NULL_ID, pIterator->name(), FILTER_TYPE_USER, pFilterTable))
            {
            // Set the top parent level filter from current ACAD_LAYERFILTERS which contains only flat layer filters.
            if (RealDwgSuccess == this->SetLevelFilterExpression(pFilterTable, &pFilterEntryDescr, pXrecord))
                {
                entryId++;
                pFilterEntryDescr->el.ehdr.uniqueId = this->ElementIdFromObject (pXrecord);
                // Create nested level filters for the new parent filter.  These filters are in ACLYDICTIONARY.
                if (aclyDictionaryId.isValid())
                    CreateLevelFiltersFromAclyDictionary (pFilterTable, &pFilterEntryDescr, entryId, entryId-1, pIterator->name(), aclyDictionaryId, pLayerFilterDictionary);
                }
            else
                {
                DeleteFilterEntryDescr (&pFilterEntryDescr, pFilterTable);
                continue;
                }
            }
        }

    delete pIterator;

    // Now process layer groups or layer filters which are not present in ACAD_LAYERFILTERS.
    if (aclyDictionaryId.isValid())
        CreateLevelFiltersFromAclyDictionary (pFilterTable, NULL, entryId, FILTER_NULL_ID, NULL, aclyDictionaryId, pLayerFilterDictionary);

    if (NULL != pFilterTable->h.firstElem)
        this->LoadElementIntoCache (pFilterTable);

    pFilterTable->Release ();
#endif

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapFilterTokenToDwg
(
const ACHAR*                pToken,
int                         length,
ACHAR                       quoteChar
)
    {
    AcString    tempString  = AcString (pToken);
    AcString    token       = tempString.substr (0, length);

    if (*pToken != quoteChar && *(pToken + length - 1) != quoteChar)
        token = quoteChar + token + quoteChar;

    return  token;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             RemapFilterOperatorToDwg
(
const ACHAR                 token,
bool                        forFlatFilters
)
    {
    switch (token)
        {
        case '-':
            return AcString (L"~");

        case '|':
            return forFlatFilters ? AcString (L",") : AcString(L" OR ");

        case '&':
            return forFlatFilters ? AcString (L".") : AcString(L" AND ");

        case ' ':
            return AcString();          // Ignore spaces. - AutoCAD doesn't handle them well.

        default:
            if (!forFlatFilters)
                {
                if (',' == token)
                    return  AcString(" OR ");
                else
                    return  AcString(" AND ");
                }
            return AcString (token);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static const ACHAR*         RemapColorTokenToDwg
(
AcString&                   token,
int&                        length,
ConvertFromDgnContext&      dgnContext
)
    {
    int     dgnIndex = 0;
    if (1 == swscanf(token.kwszPtr(), L"%d", &dgnIndex))
        {
        UInt16  dwgIndex = dgnContext.GetColorIndexFromDgn (dgnIndex, dgnIndex);
        ACHAR   newToken[48];
        wsprintf (newToken, L"%d", dwgIndex);

        token.assign (newToken);
        length = token.length ();
        }
    return  token.kwszPtr();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static const ACHAR*         RemapWeightTokenToDwg
(
AcString&                   token,
int&                        length,
ConvertFromDgnContext&      dgnContext
)
    {
    int     dgnIndex = 0;
    if (1 == swscanf(token.kwszPtr(), L"%d", &dgnIndex))
        {
        AcDb::LineWeight    dwgIndex = dgnContext.GetLineWeightFromDgn (dgnIndex, AcDb::kLnWt000);
        double              mmValue = 0.01 * (double)dwgIndex;
        char                newToken[48];
        sprintf (newToken, "%.2f", mmValue);

        token.assign (newToken, AcString::Utf8);
        length = token.length ();
        }
    return  token.kwszPtr();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             GetFilterExpressionString
(
RealDwgResBuf*              pResBuf,
MSElementDescrCP            pEntryDescr,
MSElementDescrCP            pTableDescr,
WCharCP                   pMemberName,
WCharCP                   pDwgMemberName,
ConvertFromDgnContext&      dgnContext
)
    {
#ifndef BUILD_DWGPLATFORM
    UInt32          memberId;
    WChar         entryExpression[MAX_LINKAGE_STRING_LENGTH];
    AcString        outputString;
    static WCharP s_pSupportedOperators = L"|,-& ";
    static WCharP s_pUnsupportedOperators = L"()";

    if (NULL != pResBuf)
        pResBuf->SetResType (AcDb::kDxfText);

    if (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, const_cast <MSElementDescr*> (pTableDescr), const_cast <WChar *> (pMemberName)) &&
        SUCCESS == mdlFilterDescr_getExpression (entryExpression, MAX_LINKAGE_STRING_LENGTH, const_cast <MSElementDescr*> (pEntryDescr), memberId))
        {
        AcString        inputString = entryExpression;

        for (const ACHAR *pInputChar = inputString, *pEnd = pInputChar + inputString.length(); pInputChar < pEnd; )
            {
            if ('"' == *pInputChar)
                {
                const ACHAR *pTokenStart = pInputChar++;
                while (pInputChar < pEnd && *pInputChar != '"')
                    pInputChar ++;

                if (NULL != pDwgMemberName)
                    outputString += pDwgMemberName;

                int         length = (int)(++pInputChar - pTokenStart);
                outputString += AcString (pTokenStart).substr (length);
                }
            else if (NULL != wcschr (s_pSupportedOperators, *pInputChar))
                {
                outputString += RemapFilterOperatorToDwg (*pInputChar++, NULL==pDwgMemberName);
                }
            else if (NULL != wcschr (s_pUnsupportedOperators, *pInputChar))
                {
                break;
                }
            else
                {
                const ACHAR *pTokenStart = pInputChar;
                while (pInputChar < pEnd && NULL == wcschr (s_pUnsupportedOperators, *pInputChar) && NULL == wcschr (s_pSupportedOperators, *pInputChar))
                    pInputChar++;

                if (NULL != pDwgMemberName)
                    outputString += pDwgMemberName;

                    // remap color and weight numbers to DWG values:
                    int         length = (int)(pInputChar - pTokenStart);
                    AcString    token(pTokenStart);
                    if (0 == wcscmp(pMemberName, LEVEL_FILTER_MEMBER_ELEMENT_COLOR))
                        pTokenStart = RemapColorTokenToDwg (token, length, dgnContext);
                    else if (0 == wcscmp(pMemberName, LEVEL_FILTER_MEMBER_ELEMENT_WEIGHT))
                        pTokenStart = RemapWeightTokenToDwg (token, length, dgnContext);

                /*-----------------------------------------------------------------------
                Wrap around only name filter with *'s for ACAD_LAYERFILTERS. This is only
                needed for a sub-string name filter, i.e. a name string without double
                quotes around it, compared to an exact-string name filter which has no
                double quotes.
                For ACLYDICTIONARY, however, we need to wrap all types of filters with
                double quotes.
                -----------------------------------------------------------------------*/
                if (0 == wcscmp(pMemberName, LEVEL_FILTER_MEMBER_NAME) || NULL != pDwgMemberName)
                    outputString += RemapFilterTokenToDwg (pTokenStart, length, NULL==pDwgMemberName ? L'*':L'\"');
                else
                    outputString += pTokenStart;

                if (dgnContext.GetTargetVersion() <= DwgFileVersion_2004)        // One token only prior to R2005.
                    break;
                }
            }
        }
    else if (NULL != pResBuf)
        {
        outputString = L"*";
        }

    if (NULL != pResBuf)
        pResBuf->SetString (outputString);

    return  outputString;
#else
    return  AcString();
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             GetLevelFilterBooleanExpression
(
Int16*                      pFlagValue,
MSElementDescrCP            pEntryDescr,
MSElementDescrCP            pTableDescr,
int                         shift,
WCharCP                   pMemberName,
WCharCP                   pDwgMemberName
)
    {
#ifndef BUILD_DWGPLATFORM
    int         shiftBits = shift * 2;
    UInt32      memberId;
    WChar     entryExpression[MAX_LINKAGE_STRING_LENGTH];
    AcString    boolString;

    if (NULL != pFlagValue)
        *pFlagValue &= ~(0x0003 << shiftBits);
    if (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, const_cast <MSElementDescr*> (pTableDescr), const_cast <WChar *> (pMemberName)) &&
        SUCCESS == mdlFilterDescr_getExpression (entryExpression, MAX_LINKAGE_STRING_LENGTH, const_cast <MSElementDescr*> (pEntryDescr), memberId))
        {
        if (0 == wcscmp (entryExpression, L"0"))
            {
            if (NULL != pFlagValue)
                *pFlagValue |= 0x0003 << shiftBits;
            if (NULL != pDwgMemberName)
                boolString = AcString(pDwgMemberName) + (0==shift ? L"\"TRUE\"" : L"\"FALSE\"");
            }
        else if (0 == wcscmp (entryExpression, L"1"))
            {
            if (NULL != pFlagValue)
                *pFlagValue |= 0x0001 << shiftBits;
            if (NULL != pDwgMemberName)
                boolString = AcString(pDwgMemberName) + (0==shift ? L"\"FALSE\"" : L"\"TRUE\"");
            }
        }

    return  boolString;
#else
    return  AcString();
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddFilterString (AcString& filterString, AcString newString)
    {
    if (newString.isEmpty() || 0 == newString.compare(L"*"))
        return;

    if (!filterString.isEmpty())
        filterString += L" AND ";

    filterString += newString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetFilterStringFromEntryElement
(
AcString&                   filterString,
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable,
ConvertFromDgnContext&      dgnContext
)
    {
    AddFilterString (filterString, GetFilterExpressionString(NULL, pFilterEntry, pFilterTable, LEVEL_FILTER_MEMBER_NAME, L"NAME==", dgnContext));
    AddFilterString (filterString, GetFilterExpressionString(NULL, pFilterEntry, pFilterTable, LEVEL_FILTER_MEMBER_ELEMENT_COLOR, L"COLOR==", dgnContext));
    AddFilterString (filterString, GetFilterExpressionString(NULL, pFilterEntry, pFilterTable, LEVEL_FILTER_MEMBER_ELEMENT_STYLE, L"STYLE==", dgnContext));
    AddFilterString (filterString, GetFilterExpressionString(NULL, pFilterEntry, pFilterTable, LEVEL_FILTER_MEMBER_ELEMENT_WEIGHT, L"LINEWEIGHT==", dgnContext));
    AddFilterString (filterString, GetFilterExpressionString(NULL, pFilterEntry, pFilterTable, LEVEL_FILTER_MEMBER_TRANSPARENCY, L"TRANSPARENCY==", dgnContext));
    AddFilterString (filterString, GetLevelFilterBooleanExpression(NULL, pFilterEntry, pFilterTable, 0, LEVEL_FILTER_MEMBER_DISPLAY, L"OFF=="));
    AddFilterString (filterString, GetLevelFilterBooleanExpression(NULL, pFilterEntry, pFilterTable, 1, LEVEL_FILTER_MEMBER_FROZEN, L"FROZEN=="));
    AddFilterString (filterString, GetLevelFilterBooleanExpression(NULL, pFilterEntry, pFilterTable, 4, LEVEL_FILTER_MEMBER_ELEMENT_ACCESS, L"LOCKED=="));
    AddFilterString (filterString, GetLevelFilterBooleanExpression(NULL, pFilterEntry, pFilterTable, 5, LEVEL_FILTER_MEMBER_PLOT, L"PLOTTABLE=="));

    if (filterString.isEmpty())
        return  CantCreateLevelFilter;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
DgnPlatform::ElementId      ConvertFromDgnContext::GetOrCreateFilterXrecord
(
AcDbXrecord*&               pXrecord,
AcDbDictionary*             pAclyDictionary,
DgnPlatform::ElementId      filterElementId,
UInt32                      entryId
)
    {
    /*-----------------------------------------------------------------------------------
    Find existing xrecord entry on ACLYDICTIONARY with the same element id (when editing
    DWG), or add it with a new id (when saving from DGN).
    -----------------------------------------------------------------------------------*/
    pXrecord = NULL;
    AcDbDictionaryIterator* iterator = pAclyDictionary->newIterator ();
    for (; !iterator->done(); iterator->next())
        {
        AcDbObjectId        objectId = iterator->objectId();
        if (filterElementId != this->ElementIdFromObjectId(objectId))
            continue;

        if (Acad::eOk == acdbOpenObject(pXrecord, objectId, AcDb::kForWrite))
            break;
        }

    if (NULL == pXrecord)
        {
        // a new entry is needed
        pXrecord = new AcDbXrecord ();
        // make an anonymouse entry name:
        ACHAR               entryName[128];
        wsprintf (entryName, L"*A%d", entryId);

        // use the id if it is a new entry created in editing mode; otherwise create a new id.
        if (this->ElementIdFromDBHandle(this->GetFileHolder().GetDatabase()->handseed()) > filterElementId)
            filterElementId = 0;

        this->AddObjectToDictionary (pAclyDictionary, pXrecord, entryName, filterElementId);
        filterElementId = this->ElementIdFromObject (pXrecord);
        }

    return  filterElementId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddFilterStringFromComponentFilterId
(
AcString&                   filterString,
AvlTree*                    pExistingIdsTree,
UInt32                      componentId,
MSElementDescrCP            pFilterTable,
ConvertFromDgnContext&      dgnContext
)
    {
    MSElementDescrP pComponentDescr = NULL;
    if (SUCCESS == DgnTableUtilities::FindEntryById (&pComponentDescr, componentId, *pFilterTable))
        {
        AcString    componentString;
        if (RealDwgSuccess == GetFilterStringFromEntryElement(componentString, pComponentDescr, pFilterTable, dgnContext))
            {
            if (!filterString.isEmpty())
                filterString += L" OR ";
            filterString += componentString;
            mdlAvlTree_insertNode (pExistingIdsTree, &pComponentDescr->el.ehdr.uniqueId, sizeof(pComponentDescr->el.ehdr.uniqueId));
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcString                    ConvertFromDgnContext::DecomposeCompositeLevelFilter
(
AcDbDictionary*             pAclyDictionary,
AvlTree*                    pExistingIdsTree,
UInt32                      entryId,
WCharCP                   entryName,
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable
)
    {
    AcString        filterString;
    WChar           idsExpression[MAX_LINKAGE_STRING_LENGTH];
#ifndef BUILD_DWGPLATFORM
    if (SUCCESS != mdlFilterDescr_getExpression(idsExpression, MAX_LINKAGE_STRING_LENGTH, const_cast<MSElementDescr*>(pFilterEntry), FILTER_MEMBER_COMPOSE_ID))
        return  filterString;
#else
    return  AcString();
#endif

    WCharP          pPathStart = idsExpression, pExpressionEnd = idsExpression + wcslen(idsExpression);
    FilterID        componentId = 0;
    DgnModelRefP    modelRef = this->GetModel ();

    // if the expression smells like filter path name, try getting the ids from path names:
    while (pPathStart + 1 < pExpressionEnd && L'\"' == *pPathStart)
        {
        pPathStart++;
        WCharP    pPathEnd = wcschr (pPathStart, L'\"');
        if (NULL != pPathEnd)
            {
            *pPathEnd = 0;
            componentId = 0;

#ifndef BUILD_DWGPLATFORM
            if (SUCCESS == mdlFilter_getIdFromPathName(&componentId, modelRef, LEVEL_FILTER_TABLE_NAME, pPathStart))
                AddFilterStringFromComponentFilterId (filterString, pExistingIdsTree, componentId, pFilterTable, *this);
#endif

            pPathStart = pPathEnd + 1;
            if (pPathStart < pExpressionEnd)
                pPathStart = wcschr (pPathStart, L'\"');
            if (NULL == pPathStart)
                break;
            }
        }

    if (!filterString.isEmpty())
       return  filterString;

    // if the expression contains component filter ID's, just use them:
    WCharP        pIdStart = idsExpression;
    while  (pIdStart < pExpressionEnd)
        {
        WCharP    pIdEnd = NULL;
        if (0 == (componentId = wcstoul(pIdStart, &pIdEnd, 10)) || ULONG_MAX == componentId)
            {
            pIdStart++;
            continue;
            }

        AddFilterStringFromComponentFilterId (filterString, pExistingIdsTree, componentId, pFilterTable, *this);

        if (NULL == pIdEnd || pIdEnd >= pExpressionEnd)
            break;

        pIdStart = pIdEnd + 1;
        while (pIdStart < pExpressionEnd && !iswdigit(*pIdStart))
            pIdStart++;
        }

    return  filterString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::AppendLayerIdsFromLevelGroup
(
RealDwgResBuf*&             pResbufTail,
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable
)
    {
#ifndef BUILD_DWGPLATFORM
    UInt32          memberId = 0;
    DgnModelRefP    model = this->GetModel ();
    WChar           expression[MAX_LINKAGE_STRING_LENGTH];
    if (NULL == model ||
        SUCCESS != mdlFilterTableDescr_getMemberIdFromName(&memberId, const_cast<MSElementDescrP>(pFilterTable), LEVEL_FILTER_MEMBER_LEVEL_GROUP) ||
        SUCCESS != mdlFilterDescr_getExpression(expression, MAX_LINKAGE_STRING_LENGTH, const_cast<MSElementDescrP>(pFilterEntry), memberId))
        return;

    WCharP        pNameStart = expression, pExpressionEnd = expression + wcslen(expression);
    while (pNameStart < pExpressionEnd)
        {
        while (L'\"' == *pNameStart || L' ' == *pNameStart)
            pNameStart++;

        WCharP    pNameEnd = wcschr (pNameStart, L'\"');
        if (NULL != pNameEnd)
            *pNameEnd = 0;
        UInt32              levelId = 0;
        LevelHandle         level = model->GetLevelCacheR().GetLevelByName (pNameStart);
        if (level.IsValid() && INVALID_LEVEL != (levelId = level.GetLevelId()))
            {
            AcDbObjectId    layerId = this->GetFileHolder().GetLayerByLevelHandle (level);

            if (!layerId.isNull())
                pResbufTail = pResbufTail->Append (RealDwgResBuf::CreateObjectId(AcDb::kDxfSoftPointerId, layerId));
            else
                DIAGNOSTIC_PRINTF ("Layer filter cannot add layer object ID for level ID=%I32d\n", levelId);
            }

        if (NULL == pNameEnd || ++pNameEnd >= pExpressionEnd)
            break;
        pNameStart = wcschr (pNameEnd, L'\"');
        if (NULL == pNameStart)
            break;
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsLevelGroupEntryElement
(
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable
)
    {
#ifndef BUILD_DWGPLATFORM
    UInt32          memberId = 0;
    WChar         expression[MAX_LINKAGE_STRING_LENGTH];
    return  SUCCESS == mdlFilterTableDescr_getMemberIdFromName(&memberId, const_cast<MSElementDescrP>(pFilterTable), LEVEL_FILTER_MEMBER_LEVEL_GROUP) &&
            SUCCESS == mdlFilterDescr_getExpression(expression, MAX_LINKAGE_STRING_LENGTH, const_cast<MSElementDescrP>(pFilterEntry), memberId);
#else
    return  false;
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::AddLayerFilterToAclyDictionary
(
AcDbDictionary*             pAclyDictionary,
AvlTree*                    pExistingIdsTree,
UInt32                      entryId,
WCharCP                   entryName,
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable
)
    {
    /*-----------------------------------------------------------------------------------
    This method builds the hierachy of layer filter for the input level filter entry
    element, whose ID gets saved in the AVL tree so the caller can skip from processing
    the same entry again.  If the entry element is a level group, it also gets converted
    as a filter group.  If the entry element is a composite filter with OR's, which is
    likely the union filter we created from DWG, it decompose and round trip the filter
    back to DWG.
    -----------------------------------------------------------------------------------*/
    AcString        filterString = this->DecomposeCompositeLevelFilter(pAclyDictionary, pExistingIdsTree, entryId, entryName, pFilterEntry, pFilterTable);
    if (filterString.isEmpty())
        {
        // retrieve level property filter strings. when nothing retrieved, default it to all to keep this filter.
        // however, for a layer group, this extra name filter will override layer grouped.
        if (RealDwgSuccess != GetFilterStringFromEntryElement(filterString, pFilterEntry, pFilterTable, *this)&& !IsLevelGroupEntryElement(pFilterEntry, pFilterTable))
            filterString = L"NAME==\"*\"";
        }

    ElementId       elementId = pFilterEntry->el.ehdr.uniqueId;
    AcDbXrecord*    pXrecord = NULL;
    elementId = GetOrCreateFilterXrecord (pXrecord, pAclyDictionary, elementId, entryId);
    if (NULL == pXrecord)
        return  CantCreateLevelFilter;

    mdlAvlTree_insertNode (pExistingIdsTree, &elementId, sizeof(elementId));

    // start creating the xrecord with dxf group code 1, AcLyDictionary:
    RealDwgResBuf*  pResbuf = RealDwgResBuf::CreateString (AcDb::kDxfText, filterString.isEmpty() ? "AcLyLayerGroup" : "AcLyLayerFilter");
    RealDwgResBuf*  pNextResbuf = pResbuf;
    // 90, 1
    pNextResbuf = pNextResbuf->Append (RealDwgResBuf::CreateInt32(AcDb::kDxfInt32, 1));
    // 300, filter name
    pNextResbuf = pNextResbuf->Append (RealDwgResBuf::CreateString(AcDb::kDxfXTextString, entryName));
    // 301, filter string
    if (!filterString.isEmpty())
        pNextResbuf = pNextResbuf->Append (RealDwgResBuf::CreateString(DXFCODE_LayerFilterString, filterString.kwszPtr()));
    // append layer object ID's if this filter has a level group member on:
    this->AppendLayerIdsFromLevelGroup (pNextResbuf, pFilterEntry, pFilterTable);
    // save to xrecord
    pXrecord->setFromRbChain (*pResbuf);
    RealDwgResBuf::Free (pResbuf);

    /*-----------------------------------------------------------------------------------
    Now iterate through child filters to process nested filters.
    -----------------------------------------------------------------------------------*/
    DgnModelRefP    modelRef = this->GetModel ();
#ifndef BUILD_DWGPLATFORM
    FilterIteratorP iterator = mdlFilterIterator_create (modelRef, LEVEL_FILTER_TABLE_NAME);
    if (NULL == iterator)
        {
        pXrecord->close ();
        return  CantCreateLevelFilter;
        }

    mdlFilterIterator_setParentFilterId (iterator, entryId);
    mdlFilterIterator_setIterateType (iterator, FILTER_ITERATE_TYPE_CHILD_FILTERS);
    mdlFilterIterator_setIterateOrder (iterator, FILTER_ITERATE_ORDER_BY_NAME);

    for (UInt32 childId = mdlFilterIterator_getFirst(iterator); FILTER_NULL_ID != childId; childId = mdlFilterIterator_getNext(iterator))
        {
        WChar     childName[MAX_LINKAGE_STRING_LENGTH];
        if (!mdlFilter_isValid(modelRef, LEVEL_FILTER_TABLE_NAME, childId) || 
            SUCCESS != mdlFilter_getName(childName, MAX_LINKAGE_STRING_LENGTH, modelRef, LEVEL_FILTER_TABLE_NAME, childId))
            continue;

        MSElementDescrP         pChildDescr = NULL;
        if (SUCCESS == DgnTableUtilities::FindEntryById (&pChildDescr, childId, *pFilterTable))
            {
            Acad::ErrorStatus   es = pXrecord->createExtensionDictionary ();
            if (Acad::eOk == es || Acad::eAlreadyInDb == es)
                {
                AcDbDictionary*         pChildDictionary = NULL;
                AcDbDictionaryPointer   pExtDictionary(pXrecord->extensionDictionary(), AcDb::kForWrite);
                if (Acad::eOk == pExtDictionary.openStatus())
                    {
                    AcDbObject*         pObject = NULL;
                    AcDbObjectId        objectId;
                    if (Acad::eOk != pExtDictionary->getAt (StringConstants::XDictionaryItem_AcLyDictionary, pObject, AcDb::kForWrite))
                        pExtDictionary->setAt (StringConstants::XDictionaryItem_AcLyDictionary, pChildDictionary = new AcDbDictionary(), objectId);
                    else
                        pChildDictionary = AcDbDictionary::cast (pObject);
                    if (NULL != pChildDictionary)
                        {
                        this->AddLayerFilterToAclyDictionary (pChildDictionary, pExistingIdsTree, childId, childName, pChildDescr, pFilterTable);
                        pChildDictionary->close ();
                        }
                    }
                }
            }
        }

    mdlFilterIterator_free (&iterator);
#endif
    pXrecord->close ();

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::AddLayerFilterToLayerFilterDictionary
(
AcDbDictionary*             pLayerFilterDictionary,
WCharCP                   entryNameWChars,
MSElementDescrCP            pEntryDescr,
MSElementDescrCP            pTableDescr
)
    {
    AcDbObject*             pObject = NULL;
    if (Acad::eOk != pLayerFilterDictionary->getAt (entryNameWChars, pObject, AcDb::kForWrite))
        {
        pObject = new AcDbXrecord();
        // We will use element ID's for ACLYDICTIONARY which is better aligned with our fitler hierachy.
        this->AddObjectToDictionary (pLayerFilterDictionary, pObject, entryNameWChars, 0);
        }

    AcDbXrecord*            pFilterXRecord;
    if (NULL != (pFilterXRecord = AcDbXrecord::cast(pObject)) )
        {
        RealDwgResBuf*      pResBuf;
        pFilterXRecord->rbChain (reinterpret_cast <struct resbuf **> (&pResBuf));

        RealDwgResBuf*      pFirstResBuf = pResBuf;
        RealDwgResBuf*      pLastResBuf = NULL;

        for (int index = 0; index < 7; pResBuf = pResBuf->GetNext(), index++)
            {
            if (NULL == pResBuf)
                {
                pResBuf = RealDwgResBuf::Create (AcDb::kDxfText);

                if (NULL == pFirstResBuf)
                    pFirstResBuf = pResBuf;
                else
                    pLastResBuf->SetNext (pResBuf);
                }
            pLastResBuf = pResBuf;

            switch (index)
                {
                case 0:
                    pResBuf->SetString (entryNameWChars);
                    break;

                case 1:
                    GetFilterExpressionString (pResBuf, pEntryDescr, pTableDescr, LEVEL_FILTER_MEMBER_NAME, NULL, *this);
                    break;

                case 2:
                    GetFilterExpressionString (pResBuf, pEntryDescr, pTableDescr, LEVEL_FILTER_MEMBER_ELEMENT_COLOR, NULL, *this);
                    break;

                case 3:
                    GetFilterExpressionString (pResBuf, pEntryDescr, pTableDescr, LEVEL_FILTER_MEMBER_ELEMENT_STYLE, NULL, *this);
                    break;

                case 4:
                    {
                    Int16           flagValue = (pResBuf->GetResType() == AcDb::kDxfInt16) ? pResBuf->GetInt16() : 0;

                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 0, LEVEL_FILTER_MEMBER_DISPLAY, NULL);
                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 1, LEVEL_FILTER_MEMBER_FROZEN, NULL);
                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 4, LEVEL_FILTER_MEMBER_ELEMENT_ACCESS, NULL);
                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 5, LEVEL_FILTER_MEMBER_PLOT, NULL);
#if defined (NOT_IMPLEMENTED_IN_MICROSTATION)
                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 2, LEVEL_FILTER_MEMBER_CURRENT_FROZEN, NULL);
                    GetLevelFilterBooleanExpression (&flagValue, pEntryDescr, pTableDescr, 3, LEVEL_FILTER_MEMBER_NEW_FROZEN, NULL);
#endif
                    pResBuf->SetResType (AcDb::kDxfInt16);
                    pResBuf->SetInt16 (flagValue);
                    break;
                    }

                case 5:
                   GetFilterExpressionString (pResBuf, pEntryDescr, pTableDescr, LEVEL_FILTER_MEMBER_ELEMENT_WEIGHT, NULL, *this);
                   break;

                case 6:                                                     // Plot style not supported in DGN.
                    pResBuf->SetResType (AcDb::kDxfText);
                    pResBuf->SetString (L"*");
                    break;
                }
            }
        pFilterXRecord->setFromRbChain (*pFirstResBuf);
        // setFromRbChain copies the input resbuf, we need to free the original.
        RealDwgResBuf::Free (pFirstResBuf);
        }
    // the FilterTableRecord must be closed.
    pObject->close();

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static UInt32               RemoveUnusedAclyDictionaryEntries
(
AcDbDictionary*             pAclyDictionary,
AvlTree*                    pExistingIdsTree,
ConvertFromDgnContext&      dgnContext
)
    {
    UInt32      numRemoved = 0;
    if (NULL == pAclyDictionary)
        return  numRemoved;

    AcDbDictionaryIterator* iterator = pAclyDictionary->newIterator();
    for (; !iterator->done(); iterator->next())
        {
        AcDbXrecord*        pXrecord = NULL;
        if (Acad::eOk == acdbOpenObject(pXrecord, iterator->objectId(), AcDb::kForWrite))
            {
            // this is an xrecord - remove it only if all its children are removed AND itself is not used either.
            AcDbObjectId        objectId = pXrecord->extensionDictionary ();
            if (!objectId.isNull())
                {
                // the filter entry has chidren, try getting and removing them:
                AcDbDictionaryPointer   pExtDictionary (objectId, AcDb::kForWrite);
                if (Acad::eOk == pExtDictionary.openStatus())
                    {
                    AcDbObject*         pObject = NULL;
                    if (Acad::eOk == pExtDictionary->getAt(StringConstants::XDictionaryItem_AcLyDictionary, pObject, AcDb::kForWrite))
                        {
                        // this is a child ACLYDICTIONARY, remove the children that are no longer used:
                        AcDbDictionary*     pChildAcly = AcDbDictionary::cast (pObject);
                        numRemoved += RemoveUnusedAclyDictionaryEntries (pChildAcly, pExistingIdsTree, dgnContext);
                        if (0 == pChildAcly->numEntries())
                            {
                            // All children have been removed - remove this ACLYDICTIONARY entry from the extension dictionary.
                            pExtDictionary->remove (pChildAcly->objectId());
                            pChildAcly->erase ();
                            }
                        pChildAcly->close ();
                        }

                    // remove the extension dictionary only if it contains no more children.
                    if (0 == pExtDictionary->numEntries())
                        pExtDictionary->erase ();
                    }
                }

            if ((objectId = pXrecord->extensionDictionary()).isNull() || objectId.isErased())
                {
                // this filter entry has no children, or children have all been removed - a candidate for removal.
                DgnPlatform::ElementId  elementId = dgnContext.ElementIdFromObject (pXrecord);
                if (NULL == mdlAvlTree_search(pExistingIdsTree, (const void*)&elementId))
                    {
                    // filter ID not found in our record, remove it.
                    pXrecord->erase ();
                    numRemoved++;
                    continue;
                    }
                }
            pXrecord->close ();
            }
        }

    return  numRemoved;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 CanCreateFlatLayerFilter
(
MSElementDescrCP            pFilterEntry,
MSElementDescrCP            pFilterTable
)
    {
#ifndef BUILD_DWGPLATFORM
    /*-----------------------------------------------------------------------------------
    Filters ACAD_LAYERFILTERS dictionary does not support and therefore must exclude:

    composite filter
    layer group
    transparency
    -----------------------------------------------------------------------------------*/
    WChar               expression[MAX_LINKAGE_STRING_LENGTH];
    MSElementDescrP     pEntryElem = const_cast <MSElementDescrP> (pFilterEntry);

    if (SUCCESS == mdlFilterDescr_getExpression(expression, MAX_LINKAGE_STRING_LENGTH, pEntryElem, FILTER_MEMBER_COMPOSE_ID))
        return  false;

    UInt32              memberId = 0;
    if (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, const_cast<MSElementDescrP>(pFilterTable), LEVEL_FILTER_MEMBER_LEVEL_GROUP) &&
        SUCCESS == mdlFilterDescr_getExpression(expression, MAX_LINKAGE_STRING_LENGTH, pEntryElem, memberId))
        return  false;

    if (SUCCESS == mdlFilterTableDescr_getMemberIdFromName (&memberId, const_cast<MSElementDescrP>(pFilterTable), LEVEL_FILTER_MEMBER_TRANSPARENCY) &&
        SUCCESS == mdlFilterDescr_getExpression(expression, MAX_LINKAGE_STRING_LENGTH, pEntryElem, memberId))
        return  false;
#endif

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 HasFilterEntry
(
MSElementDescrCP            pFilterTable,
const AcString&             entryNameIn
)
    {
    for (MSElementDescrCP pFilterEntry = pFilterTable->h.firstElem; NULL != pFilterEntry; pFilterEntry = pFilterEntry->h.next)
        {
        WChar             entryName[MAX_LINKAGE_STRING_LENGTH];
        if (SUCCESS == DgnTableUtilities::GetEntryName (entryName, pFilterEntry->el) && 0 == wcscmp(entryNameIn.kwszPtr(), entryName))
            return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveLayerFiltersToDatabase ()
    {
    // Create dictionary if it doesn't already exist.
    AcDbLayerTablePointer   pLayers (m_pFileHolder->GetDatabase()->layerTableId(), AcDb::kForWrite);
    if (Acad::eOk == pLayers.openStatus())
        pLayers->createExtensionDictionary ();
    AcDbDictionaryPointer   pExtensionDictionary (pLayers->extensionDictionary(), AcDb::kForWrite);
    if (Acad::eOk != pExtensionDictionary.openStatus())
        return  BSIERROR;

    pLayers->close ();

    // If the number of  layer filter entries already exceeds the maximum, don't bother
    // saving them.... we don't export so they won't appear at the next open and
    // delete would end up deleting all of the entries. (TR#
    if (pExtensionDictionary->numEntries() > this->GetSettings().GetMaxDictionaryItems())
        return SUCCESS;

    AvlTree*                pExistingIdsTree = mdlAvlTree_init (AVLKEY_ELEMENTID);
    AvlTree*                pExistingNamesTree = mdlAvlTree_init (AVLKEY_WSTRING);

    // Add layer filter dictionary to the layer table extension dictionary if it isn't already there.
    AcDbObject*             pRetrievedObject;
    AcDbObjectId            newFilterObjectId;
    AcDbDictionary          *pLayerFilterDictionary;
    if (Acad::eOk != pExtensionDictionary->getAt (StringConstants::XDictionaryItem_LayerFilters, pRetrievedObject, AcDb::kForWrite))
        pExtensionDictionary->setAt (StringConstants::XDictionaryItem_LayerFilters, pLayerFilterDictionary = new AcDbDictionary(), newFilterObjectId);
    else
        pLayerFilterDictionary = AcDbDictionary::cast (pRetrievedObject);
    if (NULL == pLayerFilterDictionary)
        return  BSIERROR;

    // Add ACLYDICTIONARY if not already exists:
    AcDbDictionary          *pAclyDictionary;
    if (Acad::eOk != pExtensionDictionary->getAt (StringConstants::XDictionaryItem_AcLyDictionary, pRetrievedObject, AcDb::kForWrite))
        pExtensionDictionary->setAt (StringConstants::XDictionaryItem_AcLyDictionary, pAclyDictionary = new AcDbDictionary(), newFilterObjectId);
    else
        pAclyDictionary = AcDbDictionary::cast (pRetrievedObject);
    if (NULL == pAclyDictionary)
        {
        if (NULL != pLayerFilterDictionary)
            pLayerFilterDictionary->close ();
        return  BSIERROR;
        }

    MSElementDescrP                     pElmDscr = NULL;
    PersistentElementRefList*           elemList = this->GetFile()->GetDictionaryModel().GetControlElementsP ();
    PersistentElementRefListIterator    iter;

    for (PersistentElementRefP elemRef = iter.GetFirstElementRef(*elemList); NULL != elemRef; elemRef = iter.GetNextElementRef())
        {
        Elm_hdr const*      ehdr = elemRef->GetElementHeaderCP ();
        if (TABLE_ELM == ehdr->type && MS_FILTER_TABLE_LEVEL == ehdr->level)
            {
            elemRef->GetElementDescr (pElmDscr, &this->GetFile()->GetDictionaryModel());
            break;
            }
        }
    if (NULL == pElmDscr)
        {
        if (NULL != pLayerFilterDictionary)
            pLayerFilterDictionary->close ();
        if (NULL != pAclyDictionary)
            pAclyDictionary->close ();
        return  BSIERROR;
        }

    for (MSElementDescrP pEntryDescr = pElmDscr->h.firstElem; NULL != pEntryDescr; pEntryDescr = pEntryDescr->h.next)
        {
        WChar         entryNameWChars[MAX_LINKAGE_STRING_LENGTH];

        if (SUCCESS == DgnTableUtilities::GetEntryName (entryNameWChars, pEntryDescr->el) &&
            0 != wcscmp (L"Default", entryNameWChars) &&
            0 != wcscmp (L"All Levels", entryNameWChars))
            {
            UInt32      entryId = DgnTableUtilities::GetEntryId (pEntryDescr->el);
#ifndef BUILD_DWGPLATFORM
            if (FILTER_NULL_ID == entryId || 0 == entryId || !mdlFilter_isValid(this->GetModel(), LEVEL_FILTER_TABLE_NAME, entryId))
                continue;

            // Don't bother saving system filters
            UShort      filterType = FILTER_TYPE_UNKNOWN;
            if (BSISUCCESS == mdlFilter_getType(&filterType, this->GetModel(), LEVEL_FILTER_TABLE_NAME, entryId) && FILTER_TYPE_SYSTEM == filterType)
                continue;

            // only process top level filter hierarchy, i.e. parentId = FILTER_NULL_ID
            UInt32      parentId = 0;
            if (BSISUCCESS != mdlFilterDescr_getParentFilterId(&parentId, pEntryDescr) || (FILTER_NULL_ID != parentId && 0 != parentId))
#endif
                continue;

            // an unlikely but still needs to be handled scenario: the ACAD hard coded name "All Used Layers" needs to be renamed, although not in DWG.
            if (0 == wcscmp (NAME_AllUsedLayers, entryNameWChars))
                {
                int         count = 1;
                AcString    newName;
                do
                    {
                    newName = entryNameWChars + AcString(L'_') + AcString(AcString::kSigned, count++);
                    } while (HasFilterEntry(pElmDscr, newName));

                if (newName.length() >= sizeof(entryNameWChars))
                    newName = newName.substr (sizeof(entryNameWChars) - 1);
                wcscpy (entryNameWChars, newName.kwszPtr());
                }

            // save/create dictionary ACAD_LAYERFILTERS for flat/top level filters:
            if (CanCreateFlatLayerFilter(pEntryDescr, pElmDscr))
                {
                mdlAvlTree_insertNode (pExistingNamesTree, entryNameWChars, sizeof(entryNameWChars));
                AddLayerFilterToLayerFilterDictionary (pLayerFilterDictionary, entryNameWChars, pEntryDescr, pElmDscr);
                }
            // save/create ACLYDICTIONARY for nested and/or level group filters:
            AddLayerFilterToAclyDictionary (pAclyDictionary, pExistingIdsTree, entryId, entryNameWChars, pEntryDescr, pElmDscr);
            }
        }

    pElmDscr->Release ();

    for (bool deletedFiltersFound = true; deletedFiltersFound; )
        {
        // Delete entries that no longer exist.
        deletedFiltersFound = false;
        for (AcDbDictionaryIterator* pIterator = pLayerFilterDictionary->newIterator(); !pIterator->done(); pIterator->next())
            {
            AcString    str = pIterator->name();
            if (NULL == mdlAvlTree_search (pExistingNamesTree, (void*) str.kwszPtr ()))
                {
                pLayerFilterDictionary->remove (str);
                deletedFiltersFound = true;
                break;
                }
            }
        }
    pLayerFilterDictionary->close ();

    // delete hierarchical ACLYDICTIONARY entries that no longer exist
    RemoveUnusedAclyDictionaryEntries (pAclyDictionary, pExistingIdsTree, *this);

    if (NULL != pAclyDictionary)
        pAclyDictionary->close ();

    mdlAvlTree_free (&pExistingNamesTree, NULLFUNC, NULL);
    mdlAvlTree_free (&pExistingIdsTree, NULLFUNC, NULL);
    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SetLayerDisplayFromViewGroup (AcDbDatabase* acDatabase)
    {
    // if we're not setting it from a view, simply ignore this call.
    if (this->GetLevelDisplayView() < 0)
        return;

    // get view level display mask
    BitMaskP            viewLevelMask = LayerFromLevel::ExtractByViewLevelDisplayMask (this->GetModel(), this->GetModel(), this->GetLevelDisplayView(), m_sourceViewGroupName.c_str(), false);
    if (NULL == viewLevelMask)
        return;

    // get dwg layer table
    AcDbLayerTablePointer   acLayerTable (acDatabase->layerTableId(), AcDb::kForRead);

    /*-----------------------------------------------------------------------------------
    Iterate through dgn levels and try to find matching dwg layer to reset layer on/off
    flag.  I don't like the way to find layer by name, but at this point there is no other
    way around.  The input database is produced by wblock which rewrites layer table with
    completely different layer object id set.
    -----------------------------------------------------------------------------------*/
    LevelCacheCR  levelCache = this->GetModel()->GetLevelCache();
    for each (LevelHandle levelHandle in levelCache)
        {
        LevelId     levelId     = levelHandle.GetLevelId();
        AcString    levelName (levelHandle.GetName());
        this->ValidateName (levelName);

        AcDbObjectId    objId;
        if (Acad::eOk == acLayerTable->getAt (levelName, objId, AcDb::kForRead))
            {
            AcDbLayerTableRecordPointer layer (objId, AcDb::kForWrite);
            layer->setIsOff (!viewLevelMask->Test (levelId-1));
            }
        }

    BitMask::FreeAndClear (&viewLevelMask);
    }

