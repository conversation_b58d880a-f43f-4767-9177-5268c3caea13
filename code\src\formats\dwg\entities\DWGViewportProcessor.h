#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbplotset.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gematrix3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// Viewport and Layout Data Structures (Based on RealDwgFileIO rdViewport.cpp)
//=======================================================================================

struct ViewportGeometry {
    // Viewport boundary (paper space)
    Point3d center;                     // Viewport center
    double width = 100.0;               // Viewport width
    double height = 100.0;              // Viewport height
    
    // Model space view
    Point3d viewCenter;                 // View center in model space
    Point3d viewTarget;                 // View target point
    Vector3d viewDirection = Vector3d(0, 0, 1); // View direction
    Vector3d upVector = Vector3d(0, 1, 0);      // Up vector
    
    // View properties
    double viewHeight = 100.0;          // View height in model space
    double viewWidth = 100.0;           // View width in model space
    double scale = 1.0;                 // Viewport scale
    double rotation = 0.0;              // View rotation angle
    
    // Viewport properties
    bool isOn = true;                   // Viewport is on
    bool isLocked = false;              // Viewport is locked
    bool isTransparent = false;         // Viewport background is transparent
    
    // Clipping
    bool hasCustomClipping = false;     // Has custom clipping boundary
    std::vector<Point3d> clippingBoundary; // Custom clipping boundary
    
    // Visual style and rendering
    std::string visualStyleName = "2D Wireframe"; // Visual style name
    bool isShadeplotOn = false;         // Shadeplot is on
    
    // Layer and property overrides
    std::vector<std::string> frozenLayers;      // Frozen layers in viewport
    std::vector<std::string> lockedLayers;      // Locked layers in viewport
    
    // Annotation scale
    double annotationScale = 1.0;       // Annotation scale
    bool useAnnotationScale = false;    // Use annotation scale
    
    // Grid and snap
    bool gridOn = false;                // Grid is on
    bool snapOn = false;                // Snap is on
    Point3d gridSpacing = Point3d(10, 10, 0); // Grid spacing
    Point3d snapSpacing = Point3d(10, 10, 0);  // Snap spacing
    
    // UCS (User Coordinate System)
    Point3d ucsOrigin;                  // UCS origin
    Vector3d ucsXAxis = Vector3d(1, 0, 0); // UCS X axis
    Vector3d ucsYAxis = Vector3d(0, 1, 0); // UCS Y axis
    std::string ucsName;                // UCS name
    
    bool IsValid() const;
    bool HasClipping() const { return hasCustomClipping && !clippingBoundary.empty(); }
    double GetAspectRatio() const { return width / height; }
    BoundingBox3D CalculateViewBounds() const;
};

struct LayoutGeometry {
    std::string name = "Layout1";       // Layout name
    std::string plotConfigurationName; // Plot configuration name
    std::string plotStyleTableName;    // Plot style table name
    
    // Paper properties
    double paperWidth = 297.0;          // Paper width (mm)
    double paperHeight = 210.0;         // Paper height (mm)
    std::string paperUnits = "mm";      // Paper units
    
    // Plot area
    enum class PlotType {
        Display,        // Display
        Extents,        // Extents
        Limits,         // Limits
        View,           // View
        Window,         // Window
        Layout          // Layout
    } plotType = PlotType::Layout;
    
    Point3d plotOrigin;                 // Plot origin
    double plotScale = 1.0;             // Plot scale
    bool scaleToFit = false;            // Scale to fit
    
    // Plot settings
    bool plotCentered = true;           // Plot centered
    bool plotHidden = false;            // Plot hidden lines
    bool plotPlotStyles = true;         // Plot with plot styles
    bool plotLineweights = true;        // Plot lineweights
    bool plotTransparency = false;      // Plot transparency
    
    // Plot rotation
    enum class PlotRotation {
        None = 0,       // No rotation
        Rotate90 = 1,   // 90 degrees
        Rotate180 = 2,  // 180 degrees
        Rotate270 = 3   // 270 degrees
    } plotRotation = PlotRotation::None;
    
    // Margins
    double leftMargin = 7.5;            // Left margin (mm)
    double rightMargin = 7.5;           // Right margin (mm)
    double topMargin = 20.0;            // Top margin (mm)
    double bottomMargin = 7.5;          // Bottom margin (mm)
    
    // Layout viewports
    std::vector<ViewportGeometry> viewports; // Layout viewports
    
    // Layout properties
    bool isModelLayout = false;         // Is model space layout
    int tabOrder = 0;                   // Tab order
    
    bool IsValid() const;
    bool HasViewports() const { return !viewports.empty(); }
    size_t GetViewportCount() const { return viewports.size(); }
    double GetPaperAspectRatio() const { return paperWidth / paperHeight; }
};

//=======================================================================================
// Viewport and Layout Validation Results
//=======================================================================================

struct ViewportValidationResult : public DWGValidationResult {
    bool hasValidBoundary = false;
    bool hasValidView = false;
    bool hasValidScale = false;
    bool hasValidClipping = false;
    bool hasValidUCS = false;
    double calculatedScale = 0.0;
    
    void AddViewportError(const std::string& error) {
        AddError("Viewport: " + error);
    }
    
    void AddViewportWarning(const std::string& warning) {
        AddWarning("Viewport: " + warning);
    }
};

struct LayoutValidationResult : public DWGValidationResult {
    bool hasValidName = false;
    bool hasValidPaper = false;
    bool hasValidPlotSettings = false;
    bool hasValidViewports = false;
    int viewportCount = 0;
    
    void AddLayoutError(const std::string& error) {
        AddError("Layout: " + error);
    }
    
    void AddLayoutWarning(const std::string& warning) {
        AddWarning("Layout: " + warning);
    }
};

//=======================================================================================
// DWG Viewport and Layout Processor (Based on RealDwgFileIO rdViewport.cpp)
//=======================================================================================

class DWGViewportProcessor : public DWGEntityProcessor {
public:
    DWGViewportProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGViewportProcessor"; }

    // Viewport and layout processing methods
    DWGProcessingStatus ProcessViewport(const ViewportGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessLayout(const LayoutGeometry& geometry);

    // Validation methods
    ViewportValidationResult ValidateViewportGeometry(const ViewportGeometry& geometry) const;
    LayoutValidationResult ValidateLayoutGeometry(const LayoutGeometry& geometry) const;
    bool ValidateViewportBoundary(const ViewportGeometry& geometry) const;
    bool ValidateViewportView(const ViewportGeometry& geometry) const;
    bool ValidateViewportScale(const ViewportGeometry& geometry) const;
    bool ValidateLayoutName(const std::string& name) const;
    bool ValidatePaperSize(double width, double height) const;

    // Viewport calculations
    double CalculateViewportScale(const ViewportGeometry& geometry) const;
    Point3d CalculateViewCenter(const ViewportGeometry& geometry) const;
    BoundingBox3D CalculateViewBounds(const ViewportGeometry& geometry) const;
    std::vector<Point3d> CalculateViewportCorners(const ViewportGeometry& geometry) const;
    
    // View transformations
    Point3d ModelToViewport(const Point3d& modelPoint, const ViewportGeometry& geometry) const;
    Point3d ViewportToModel(const Point3d& viewportPoint, const ViewportGeometry& geometry) const;
    Vector3d ModelToViewportVector(const Vector3d& modelVector, const ViewportGeometry& geometry) const;
    Vector3d ViewportToModelVector(const Vector3d& viewportVector, const ViewportGeometry& geometry) const;

    // Viewport clipping
    bool ProcessViewportClipping(const ViewportGeometry& geometry) const;
    bool ValidateClippingBoundary(const std::vector<Point3d>& boundary) const;
    std::vector<Point3d> OptimizeClippingBoundary(const std::vector<Point3d>& boundary) const;
    bool IsPointInViewport(const Point3d& point, const ViewportGeometry& geometry) const;

    // Layer management in viewports
    bool ProcessViewportLayers(const ViewportGeometry& geometry) const;
    bool FreezeLayerInViewport(const std::string& layerName, const ViewportGeometry& geometry) const;
    bool ThawLayerInViewport(const std::string& layerName, const ViewportGeometry& geometry) const;
    bool LockLayerInViewport(const std::string& layerName, const ViewportGeometry& geometry) const;
    bool UnlockLayerInViewport(const std::string& layerName, const ViewportGeometry& geometry) const;

    // Layout management
    bool CreateLayout(const LayoutGeometry& geometry);
    bool UpdateLayout(const LayoutGeometry& geometry);
    bool DeleteLayout(const std::string& layoutName);
    bool LayoutExists(const std::string& layoutName) const;
    LayoutGeometry GetLayout(const std::string& layoutName) const;
    std::vector<std::string> GetAvailableLayouts() const;

    // Plot settings
    bool ProcessPlotSettings(const LayoutGeometry& geometry) const;
    bool ValidatePlotConfiguration(const std::string& configName) const;
    bool ValidatePlotStyleTable(const std::string& tableName) const;
    std::vector<std::string> GetAvailablePlotConfigurations() const;
    std::vector<std::string> GetAvailablePlotStyleTables() const;

    // Viewport optimization and repair
    bool RepairViewportGeometry(ViewportGeometry& geometry) const;
    bool RepairLayoutGeometry(LayoutGeometry& geometry) const;
    bool OptimizeViewportView(ViewportGeometry& geometry) const;
    bool ValidateAndFixViewportScale(ViewportGeometry& geometry) const;

private:
    // Layout storage
    std::unordered_map<std::string, LayoutGeometry> m_layouts;
    
    // Viewport processing helpers
    bool ProcessViewportBoundary(const ViewportGeometry& geometry) const;
    bool ProcessViewportView(const ViewportGeometry& geometry) const;
    bool ProcessViewportProperties(const ViewportGeometry& geometry) const;
    bool ProcessViewportUCS(const ViewportGeometry& geometry) const;
    
    // View calculation helpers
    void CalculateViewMatrix(const ViewportGeometry& geometry, double matrix[4][4]) const;
    void CalculateProjectionMatrix(const ViewportGeometry& geometry, double matrix[4][4]) const;
    Point3d ApplyViewTransform(const Point3d& point, const double matrix[4][4]) const;
    Vector3d ApplyViewTransform(const Vector3d& vector, const double matrix[4][4]) const;
    
    // Layout processing helpers
    bool ProcessLayoutPaper(const LayoutGeometry& geometry) const;
    bool ProcessLayoutPlotSettings(const LayoutGeometry& geometry) const;
    bool ProcessLayoutViewports(const LayoutGeometry& geometry) const;
    bool ProcessLayoutMargins(const LayoutGeometry& geometry) const;
    
    // Geometric calculations
    double CalculateScaleFromView(const ViewportGeometry& geometry) const;
    Point3d CalculateViewCenterFromTarget(const ViewportGeometry& geometry) const;
    Vector3d CalculateViewDirectionFromAngles(double azimuth, double elevation) const;
    void CalculateViewAngles(const Vector3d& direction, double& azimuth, double& elevation) const;
    
    // Clipping helpers
    bool ClipLineToViewport(const Point3d& start, const Point3d& end, 
                           const ViewportGeometry& geometry, std::vector<Point3d>& clippedLine) const;
    bool IsLineInViewport(const Point3d& start, const Point3d& end, const ViewportGeometry& geometry) const;
    std::vector<Point3d> ClipPolygonToViewport(const std::vector<Point3d>& polygon, 
                                              const ViewportGeometry& geometry) const;
    
    // UCS processing helpers
    bool ProcessUCSTransformation(const ViewportGeometry& geometry) const;
    void CalculateUCSMatrix(const ViewportGeometry& geometry, double matrix[4][4]) const;
    Point3d UCSToWCS(const Point3d& ucsPoint, const ViewportGeometry& geometry) const;
    Point3d WCSToUCS(const Point3d& wcsPoint, const ViewportGeometry& geometry) const;
    
    // Validation helpers
    bool ValidateViewportDimensions(const ViewportGeometry& geometry) const;
    bool ValidateViewDirection(const Vector3d& direction) const;
    bool ValidateUpVector(const Vector3d& up, const Vector3d& direction) const;
    bool ValidateLayoutName(const std::string& name) const;
    bool ValidatePaperUnits(const std::string& units) const;
    
    // Name validation and sanitization
    std::string SanitizeLayoutName(const std::string& name) const;
    std::string GenerateUniqueLayoutName(const std::string& baseName) const;
    bool IsReservedLayoutName(const std::string& name) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbViewport* CreateDWGViewport(const ViewportGeometry& geometry) const;
    AcDbLayout* CreateDWGLayout(const LayoutGeometry& geometry) const;
    
    // Viewport creation helpers
    bool SetViewportBoundary(AcDbViewport* viewport, const ViewportGeometry& geometry) const;
    bool SetViewportView(AcDbViewport* viewport, const ViewportGeometry& geometry) const;
    bool SetViewportProperties(AcDbViewport* viewport, const ViewportGeometry& geometry) const;
    bool SetViewportClipping(AcDbViewport* viewport, const ViewportGeometry& geometry) const;
    bool SetViewportLayers(AcDbViewport* viewport, const ViewportGeometry& geometry) const;
    
    // Layout creation helpers
    bool SetLayoutPaper(AcDbLayout* layout, const LayoutGeometry& geometry) const;
    bool SetLayoutPlotSettings(AcDbLayout* layout, const LayoutGeometry& geometry) const;
    bool SetLayoutMargins(AcDbLayout* layout, const LayoutGeometry& geometry) const;
    bool AddLayoutToDatabase(AcDbLayout* layout, const std::string& name) const;
    
    // Plot settings helpers
    bool SetPlotConfiguration(AcDbLayout* layout, const std::string& configName) const;
    bool SetPlotStyleTable(AcDbLayout* layout, const std::string& tableName) const;
    bool SetPlotArea(AcDbLayout* layout, const LayoutGeometry& geometry) const;
    bool SetPlotScale(AcDbLayout* layout, const LayoutGeometry& geometry) const;
    
    // Error handling for RealDWG operations
    bool HandleViewportCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedViewports = 0;
    mutable size_t m_processedLayouts = 0;
    mutable size_t m_repairedViewports = 0;
    mutable size_t m_optimizedViews = 0;
    
    // Configuration
    double m_viewportTolerance = 1e-6;
    double m_scaleTolerance = 1e-8;
    double m_angleTolerance = 1e-8;
    bool m_enableViewportOptimization = true;
    bool m_enableLayoutValidation = true;
    bool m_autoRepairViewports = true;
    double m_minViewportSize = 1.0;
    double m_maxViewportSize = 10000.0;
    double m_minScale = 1e-6;
    double m_maxScale = 1e6;
    
    // Default settings
    std::string m_defaultPlotConfiguration = "None";
    std::string m_defaultPlotStyleTable = "acad.ctb";
    std::string m_defaultVisualStyle = "2D Wireframe";
};

} // namespace IModelExport
