#include "../src/formats/dwg/entities/DWGEntityProcessorFactory.h"
#include "../src/formats/dwg/entities/DWG3DEntityProcessor.h"
#include "../src/formats/dwg/entities/DWGDimensionProcessor.h"
#include "../src/formats/dwg/entities/DWGBlockProcessor.h"
#include "../src/formats/dwg/geometry/DWGGeometryProcessor.h"
#include "../src/formats/dwg/styles/DWGStyleManager.h"
#include "../src/formats/dwg/DWGExporter.h"

#include <iostream>
#include <memory>
#include <vector>

using namespace IModelExport;

//=======================================================================================
// Example DWG Exporter Implementation
//=======================================================================================

class ExampleDWGExporter : public DWGExporter {
public:
    ExampleDWGExporter() : DWGExporter() {
        std::cout << "Initializing Example DWG Exporter..." << std::endl;
    }
    
    Point3d TransformPoint(const Point3d& point) const override {
        // Apply simple scaling transformation
        return Point3d(point.x * m_scale, point.y * m_scale, point.z * m_scale);
    }
    
    Vector3d TransformVector(const Vector3d& vector) const override {
        // Apply simple scaling transformation
        return Vector3d(vector.x * m_scale, vector.y * m_scale, vector.z * m_scale);
    }
    
    double TransformLength(double length) const override {
        return length * m_scale;
    }
    
    void LogError(const std::string& message) override {
        std::cout << "[ERROR] " << message << std::endl;
    }
    
    void LogWarning(const std::string& message) override {
        std::cout << "[WARNING] " << message << std::endl;
    }
    
    void LogInfo(const std::string& message) override {
        std::cout << "[INFO] " << message << std::endl;
    }
    
    void SetScale(double scale) { m_scale = scale; }
    double GetScale() const { return m_scale; }

private:
    double m_scale = 1.0;
};

//=======================================================================================
// Example Functions
//=======================================================================================

void DemonstrateGeometryProcessor() {
    std::cout << "\n=== Geometry Processor Example ===" << std::endl;
    
    DWGGeometryProcessor geometryProcessor;
    
    // Test point validation and repair
    Point3d validPoint(10.0, 20.0, 30.0);
    Point3d invalidPoint(std::numeric_limits<double>::infinity(), 20.0, 30.0);
    
    std::cout << "Valid point: " << (geometryProcessor.ValidatePoint(validPoint) ? "YES" : "NO") << std::endl;
    std::cout << "Invalid point: " << (geometryProcessor.ValidatePoint(invalidPoint) ? "YES" : "NO") << std::endl;
    
    Point3d repairedPoint = geometryProcessor.RepairPoint(invalidPoint);
    std::cout << "Repaired point: (" << repairedPoint.x << ", " << repairedPoint.y << ", " << repairedPoint.z << ")" << std::endl;
    
    // Test distance calculations
    Point3d p1(0, 0, 0);
    Point3d p2(3, 4, 0);
    double distance = geometryProcessor.DistancePointToPoint(p1, p2);
    std::cout << "Distance between points: " << distance << std::endl;
    
    // Test vector operations
    Vector3d v1(1, 0, 0);
    Vector3d v2(0, 1, 0);
    Vector3d cross = geometryProcessor.CrossProduct(v1, v2);
    std::cout << "Cross product: (" << cross.x << ", " << cross.y << ", " << cross.z << ")" << std::endl;
    
    // Test bounding box
    BoundingBox3D bbox;
    bbox.AddPoint(Point3d(0, 0, 0));
    bbox.AddPoint(Point3d(10, 10, 10));
    bbox.AddPoint(Point3d(-5, 5, 15));
    
    Point3d center = bbox.GetCenter();
    Vector3d size = bbox.GetSize();
    std::cout << "Bounding box center: (" << center.x << ", " << center.y << ", " << center.z << ")" << std::endl;
    std::cout << "Bounding box size: (" << size.x << ", " << size.y << ", " << size.z << ")" << std::endl;
    std::cout << "Bounding box volume: " << bbox.GetVolume() << std::endl;
}

void DemonstrateStyleManager() {
    std::cout << "\n=== Style Manager Example ===" << std::endl;
    
    DWGStyleManager styleManager;
    
    // Create a custom layer
    LayerStyle customLayer;
    customLayer.name = "CustomLayer";
    customLayer.color = Color(255, 0, 0, 255); // Red
    customLayer.lineTypeName = "Dashed";
    customLayer.lineWeight = 0.5;
    customLayer.isVisible = true;
    customLayer.isLocked = false;
    customLayer.isPlottable = true;
    
    auto result = styleManager.CreateLayer(customLayer);
    if (result.success) {
        std::cout << "Successfully created custom layer" << std::endl;
    } else {
        std::cout << "Failed to create custom layer" << std::endl;
        for (const auto& error : result.errors) {
            std::cout << "  Error: " << error << std::endl;
        }
    }
    
    // Create a custom text style
    TextStyle customTextStyle;
    customTextStyle.name = "CustomText";
    customTextStyle.fontName = "Arial";
    customTextStyle.height = 5.0;
    customTextStyle.widthFactor = 1.2;
    customTextStyle.obliqueAngle = 15.0; // 15 degrees
    customTextStyle.isBackward = false;
    customTextStyle.isUpsideDown = false;
    customTextStyle.isVertical = false;
    
    auto textResult = styleManager.CreateTextStyle(customTextStyle);
    if (textResult.success) {
        std::cout << "Successfully created custom text style" << std::endl;
    }
    
    // Create a custom material
    MaterialStyle customMaterial;
    customMaterial.name = "CustomMaterial";
    customMaterial.diffuseColor = Color(0.2f, 0.8f, 0.2f, 1.0f); // Green
    customMaterial.specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f); // White
    customMaterial.emissiveColor = Color(0.0f, 0.0f, 0.0f, 1.0f); // Black
    customMaterial.shininess = 64.0;
    customMaterial.transparency = 0.1; // 10% transparent
    customMaterial.reflectivity = 0.3; // 30% reflective
    
    auto materialResult = styleManager.CreateMaterial(customMaterial);
    if (materialResult.success) {
        std::cout << "Successfully created custom material" << std::endl;
    }
    
    // List all available layers
    auto layerNames = styleManager.GetLayerNames();
    std::cout << "Available layers: ";
    for (const auto& name : layerNames) {
        std::cout << name << " ";
    }
    std::cout << std::endl;
    
    // Validate all styles
    bool allValid = styleManager.ValidateAllStyles();
    std::cout << "All styles valid: " << (allValid ? "YES" : "NO") << std::endl;
}

void Demonstrate3DEntityProcessor() {
    std::cout << "\n=== 3D Entity Processor Example ===" << std::endl;
    
    auto exporter = std::make_unique<ExampleDWGExporter>();
    DWG3DEntityProcessor processor(exporter.get());
    
    // Create a 3D face
    Face3DGeometry face;
    face.vertices = {
        Point3d(0, 0, 0),
        Point3d(10, 0, 0),
        Point3d(10, 10, 0),
        Point3d(0, 10, 0)
    };
    face.normal = Vector3d(0, 0, 1);
    face.isVisible = true;
    
    std::cout << "Face is valid: " << (face.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Face is quad: " << (face.IsQuad() ? "YES" : "NO") << std::endl;
    std::cout << "Face area: " << face.CalculateArea() << std::endl;
    
    auto status = processor.ProcessFace3D(face, "3D_Entities");
    std::cout << "Face processing status: " << static_cast<int>(status) << std::endl;
    
    // Create a mesh
    MeshGeometry mesh;
    mesh.type = MeshGeometry::Type::PolygonMesh;
    mesh.vertices = {
        Point3d(0, 0, 0), Point3d(10, 0, 0), Point3d(20, 0, 0),
        Point3d(0, 10, 0), Point3d(10, 10, 0), Point3d(20, 10, 0),
        Point3d(0, 20, 0), Point3d(10, 20, 0), Point3d(20, 20, 0)
    };
    mesh.faces = {
        {0, 1, 4, 3}, {1, 2, 5, 4},
        {3, 4, 7, 6}, {4, 5, 8, 7}
    };
    mesh.meshM = 3;
    mesh.meshN = 3;
    
    std::cout << "Mesh is valid: " << (mesh.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Mesh has valid topology: " << (mesh.HasValidTopology() ? "YES" : "NO") << std::endl;
    std::cout << "Mesh vertex count: " << mesh.GetVertexCount() << std::endl;
    std::cout << "Mesh face count: " << mesh.GetFaceCount() << std::endl;
    
    auto meshValidation = processor.ValidateMeshGeometry(mesh);
    std::cout << "Mesh validation result: " << (meshValidation.isValid ? "VALID" : "INVALID") << std::endl;
    
    auto meshStatus = processor.ProcessMesh(mesh, "3D_Meshes");
    std::cout << "Mesh processing status: " << static_cast<int>(meshStatus) << std::endl;
}

void DemonstrateDimensionProcessor() {
    std::cout << "\n=== Dimension Processor Example ===" << std::endl;
    
    auto exporter = std::make_unique<ExampleDWGExporter>();
    DWGDimensionProcessor processor(exporter.get());
    
    // Create a linear dimension
    DimensionGeometry dimension;
    dimension.type = DimensionGeometry::Type::Linear;
    dimension.defPoint1 = Point3d(0, 0, 0);
    dimension.defPoint2 = Point3d(100, 0, 0);
    dimension.dimLinePoint = Point3d(50, 20, 0);
    dimension.textPosition = Point3d(50, 20, 0);
    dimension.textHeight = 2.5;
    dimension.arrowSize = 2.5;
    dimension.precision = 2;
    dimension.units = "mm";
    dimension.showUnits = true;
    
    std::cout << "Dimension is valid: " << (dimension.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Measured value: " << dimension.CalculateMeasuredValue() << std::endl;
    std::cout << "Formatted text: " << dimension.FormatDimensionText() << std::endl;
    
    auto validation = processor.ValidateDimensionGeometry(dimension);
    std::cout << "Dimension validation: " << (validation.isValid ? "VALID" : "INVALID") << std::endl;
    
    auto status = processor.ProcessDimension(dimension, "Dimensions");
    std::cout << "Dimension processing status: " << static_cast<int>(status) << std::endl;
    
    // Create a leader
    LeaderGeometry leader;
    leader.type = LeaderGeometry::Type::Straight;
    leader.vertices = {
        Point3d(0, 0, 0),
        Point3d(50, 50, 0),
        Point3d(100, 50, 0)
    };
    leader.arrowPoint = Point3d(0, 0, 0);
    leader.textPoint = Point3d(100, 50, 0);
    leader.text = "Leader Text";
    leader.hasArrowHead = true;
    leader.arrowName = "_ClosedFilled";
    
    std::cout << "Leader is valid: " << (leader.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Leader length: " << leader.CalculateLength() << std::endl;
    
    auto leaderValidation = processor.ValidateLeaderGeometry(leader);
    std::cout << "Leader validation: " << (leaderValidation.isValid ? "VALID" : "INVALID") << std::endl;
    
    auto leaderStatus = processor.ProcessLeader(leader, "Leaders");
    std::cout << "Leader processing status: " << static_cast<int>(leaderStatus) << std::endl;
}

void DemonstrateBlockProcessor() {
    std::cout << "\n=== Block Processor Example ===" << std::endl;
    
    auto exporter = std::make_unique<ExampleDWGExporter>();
    DWGBlockProcessor processor(exporter.get());
    
    // Create a block definition
    BlockDefinition blockDef;
    blockDef.name = "TestBlock";
    blockDef.description = "Test block for demonstration";
    blockDef.basePoint = Point3d(0, 0, 0);
    blockDef.explodable = true;
    
    // Add some entities to the block
    BlockDefinition::BlockEntity line1;
    line1.entityType = "Line";
    line1.geometry = {Point3d(0, 0, 0), Point3d(10, 0, 0)};
    line1.layer = "0";
    line1.color = Color(1.0f, 0.0f, 0.0f, 1.0f); // Red
    
    BlockDefinition::BlockEntity line2;
    line2.entityType = "Line";
    line2.geometry = {Point3d(0, 0, 0), Point3d(0, 10, 0)};
    line2.layer = "0";
    line2.color = Color(0.0f, 1.0f, 0.0f, 1.0f); // Green
    
    blockDef.entities = {line1, line2};
    
    // Add an attribute definition
    AttributeDefinition attDef;
    attDef.tag = "PART_NUMBER";
    attDef.prompt = "Enter part number:";
    attDef.defaultValue = "P001";
    attDef.position = Point3d(5, 5, 0);
    attDef.height = 2.0;
    attDef.isInvisible = false;
    attDef.isConstant = false;
    
    blockDef.attributeDefinitions = {attDef};
    
    std::cout << "Block definition is valid: " << (blockDef.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Block has geometry: " << (blockDef.HasGeometry() ? "YES" : "NO") << std::endl;
    std::cout << "Block entity count: " << blockDef.GetEntityCount() << std::endl;
    std::cout << "Block attribute count: " << blockDef.GetAttributeCount() << std::endl;
    
    auto validation = processor.ValidateBlockDefinition(blockDef);
    std::cout << "Block validation: " << (validation.isValid ? "VALID" : "INVALID") << std::endl;
    
    auto status = processor.ProcessBlockDefinition(blockDef);
    std::cout << "Block definition processing status: " << static_cast<int>(status) << std::endl;
    
    // Create a block reference
    BlockReference blockRef;
    blockRef.blockName = "TestBlock";
    blockRef.position = Point3d(100, 100, 0);
    blockRef.scale = Vector3d(2.0, 2.0, 1.0); // Scale 2x in X and Y
    blockRef.rotation = M_PI / 4; // 45 degrees
    blockRef.normal = Vector3d(0, 0, 1);
    
    // Add attribute instance
    AttributeInstance attInst;
    attInst.tag = "PART_NUMBER";
    attInst.value = "P123";
    attInst.position = Point3d(105, 105, 0);
    attInst.height = 2.0;
    
    blockRef.attributes = {attInst};
    
    std::cout << "Block reference is valid: " << (blockRef.IsValid() ? "YES" : "NO") << std::endl;
    std::cout << "Block reference has attributes: " << (blockRef.HasAttributes() ? "YES" : "NO") << std::endl;
    
    auto refValidation = processor.ValidateBlockReference(blockRef);
    std::cout << "Block reference validation: " << (refValidation.isValid ? "VALID" : "INVALID") << std::endl;
    std::cout << "Referenced block exists: " << (refValidation.blockExists ? "YES" : "NO") << std::endl;
    
    auto refStatus = processor.ProcessBlockReference(blockRef, "Blocks");
    std::cout << "Block reference processing status: " << static_cast<int>(refStatus) << std::endl;
}

void DemonstrateEntityProcessorFactory() {
    std::cout << "\n=== Entity Processor Factory Example ===" << std::endl;
    
    auto exporter = std::make_unique<ExampleDWGExporter>();
    
    // Get supported entity types
    auto supportedTypes = DWGEntityProcessorFactory::GetSupportedEntityTypes();
    std::cout << "Supported entity types (" << supportedTypes.size() << "):" << std::endl;
    for (const auto& type : supportedTypes) {
        std::cout << "  " << type << std::endl;
    }
    
    // Get processor categories
    auto categories = DWGEntityProcessorFactory::GetProcessorCategories();
    std::cout << "\nProcessor categories:" << std::endl;
    for (const auto& category : categories) {
        auto processorsInCategory = DWGEntityProcessorFactory::GetProcessorsInCategory(category);
        std::cout << "  " << category << " (" << processorsInCategory.size() << " processors)" << std::endl;
    }
    
    // Create specific processors
    auto lineProcessor = DWGEntityProcessorFactory::CreateProcessor("Line", exporter.get());
    if (lineProcessor) {
        std::cout << "\nCreated processor: " << lineProcessor->GetProcessorName() << std::endl;
    }
    
    auto splineProcessor = DWGEntityProcessorFactory::CreateProcessor("Spline", exporter.get());
    if (splineProcessor) {
        std::cout << "Created processor: " << splineProcessor->GetProcessorName() << std::endl;
    }
    
    // Create all processors
    auto allProcessors = DWGEntityProcessorFactory::CreateAllProcessors(exporter.get());
    std::cout << "\nCreated " << allProcessors.size() << " processors in total" << std::endl;
    
    // Test processor capabilities
    auto capabilities = DWGEntityProcessorFactory::GetProcessorCapabilities("Spline");
    std::cout << "\nSpline processor capabilities:" << std::endl;
    for (const auto& capability : capabilities) {
        std::cout << "  " << capability << std::endl;
    }
}

//=======================================================================================
// Main Function
//=======================================================================================

int main() {
    std::cout << "DWG Complete Implementation Example" << std::endl;
    std::cout << "===================================" << std::endl;
    
    try {
        DemonstrateGeometryProcessor();
        DemonstrateStyleManager();
        Demonstrate3DEntityProcessor();
        DemonstrateDimensionProcessor();
        DemonstrateBlockProcessor();
        DemonstrateEntityProcessorFactory();
        
        std::cout << "\n=== Example Completed Successfully ===" << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "Exception occurred: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
