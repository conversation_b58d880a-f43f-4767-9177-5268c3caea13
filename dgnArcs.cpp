/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnArcs.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          07/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtElliptic : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    bool        closed = false;
    AcRxClass*  filledType = NULL;
    if (context.IsElementCircular(&closed, NULL, inElement))
        {
        bool    hasConstantWidth = false, hasNonZeroWidth = false;
        double  constantWidth = 0.0;
        context.ExtractPolylineWidth (hasConstantWidth, constantWidth, hasNonZeroWidth, inElement);

        if (hasNonZeroWidth)
            return AcDbPolyline::desc ();
        else if (closed && NULL != (filledType = this->GetFilledType(inElement, context)))
            return  filledType;
        else
            return  closed ? AcDbCircle::desc() : AcDbArc::desc();
        }
    else if (closed && NULL != (filledType = this->GetFilledType(inElement, context)))
        {
        return  filledType;
        }

    // default to ellipse
    return AcDbEllipse::desc ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetFilledType (ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    UInt32      fillColor = 0;
    bool        framelessFill = false;
    bool        filled = this->IsEllipseFilled (fillColor, framelessFill, inElement, context);

    if (filled)
        {
        // create a single wipeout for a circle filled with background color. Don't add wipeout outline yet - we can't set WIPEOUTFRAMES at the moment.
        if (DWG_COLOR_Background255 == fillColor)
            return  AcDbWipeout::desc();

        // create a single hatch for an opaque filled ellipse
        if (Closed_Hatch == context.GetSettings().GetComplexShapeMapping(framelessFill, inElement.GetElementCP()->hdr.dhdr.props.b.is3d))
            return  AcDbHatch::desc();
        }

    return  NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsEllipseFilled (UInt32& fillColor, bool& framelessFill, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // find out if the element is filled with no boundary displayed, in which case we only create a hatch and skip the element itself:
    bool        outlined = false, patterned = false;
    bool        filled = context.IsFilledOrPatterned (inElement, NULL, NULL, &outlined, &patterned, &fillColor, NULL, NULL);

    if (filled)
        framelessFill = !patterned && !outlined;
    else
        framelessFill = false;

    return  filled;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddHatchOrWipeout (AcDbEntityP acEntity, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    bool        isPatterned = false, isSolidFill = false, isGradientFill = false, isOutlined = false;
    UInt32      fillColor = 0;

    if (context.IsFilledOrPatterned(inElement, &isSolidFill, &isGradientFill, &isOutlined, &isPatterned, &fillColor))
        {
        if (isSolidFill && DWG_COLOR_Background255 == fillColor)
            {
            // add a wipeout
            RealDwgStatus   status = context.AddWipeoutEntity (inElement);
            // add a pattern hatch
            if (RealDwgSuccess == status && isPatterned)
                status = context.AddHatchEntity (acEntity, inElement, false, true, isOutlined);
            return  status;
            }

        return context.AddHatchEntity (acEntity, inElement, isSolidFill || isGradientFill, isPatterned, isOutlined);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbHatchFromClosedArcElement (AcDbHatch* acHatch, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // a filled hatch entity, maybe with a patterned hatch as well
    bool        isPatterned = false, isSolidFill = false, isGradientFill = false, isOutlined = false;

    if (context.IsFilledOrPatterned(inElement, &isSolidFill, &isGradientFill, &isOutlined, &isPatterned))
        {
        // determin if seperate fill + pattern are needed
        bool            create2Hatches = context.ShouldSeperateFillAndPattern (isSolidFill, isGradientFill, isPatterned);

        // create either a composite or a filled hatch
        RealDwgStatus   status = context.HatchFromElement (acHatch, inElement, NULL, create2Hatches ? false : isPatterned, isSolidFill || isGradientFill);

        // add a patterned hatch as necessary
        if (RealDwgSuccess == status && create2Hatches && isPatterned)
            context.AddSingleHatchEntity (inElement, true, false);

        return  status;
        }

    return  WrongMstnElementType;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbArcFromArcElement
(
AcDbArc*                acArc,
DPoint3dCR              center,
RotMatrixCR             matrix,
double                  r0,
double                  r1,
double                  start, 
double                  sweep, 
double                  extrusionDistance,
ConvertFromDgnContextR  context
) const
    {
    double      extrusionAngle;
    DPoint3d    roundedNormal;
    RealDwgUtil::ExtractExtrusion (roundedNormal, extrusionAngle, matrix);

    acArc->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (roundedNormal));
    acArc->setCenter (RealDwgUtil::GePoint3dFromDPoint3d (center));
    acArc->setRadius (r0);
    acArc->setThickness (extrusionDistance);

    double      end = start + sweep;

    if (0.0 != extrusionAngle)
        {
        start += extrusionAngle;
        end   += extrusionAngle;
        }

    if (! Angle::IsFullCircle(sweep))
        {
        start = bsiTrig_getPositiveNormalizedAngle (start);
        end   = bsiTrig_getPositiveNormalizedAngle (end);
        }
    else
        {
        start = 0.0;
        end   = msGeomConst_2pi;
        /*---------------------------------------------------------------------------
        AutoCAD can't handle an arc with end angle of 2PI.  Commands like MOVE, COPY
        etc on such an arc would blow away the end angle to 0, making the arc look
        like a point at its center. Also R12 and below DXF's failed on these arcs as
        seen in TR 139665.  For these reasons, DD v2.03 has changed the method
        acArc->setEndAngle(angle) to drop angle=2PI to 0.
        So, to workaround this AutoCAD bug we have to trim the end angle by a small
        tolerance.  This will make AutoCAD to display full circle which functions
        correctly, although its report tools will still report end angle to be 0.
        ---------------------------------------------------------------------------*/
        end -= MINIMUM_SweepAngle;
        }
    acArc->setStartAngle (start);
    acArc->setEndAngle (end);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbCircleFromArcElement
(
AcDbCircle*             acCircle,
DPoint3dCR              center,
RotMatrixCR             matrix,
double                  r0,
double                  r1,
double                  start, 
double                  sweep, 
double                  extrusionDistance,
ElementHandleCR         inElement,
ConvertFromDgnContextR  context
) const
    {
    DPoint3d    roundedNormal;
    DVec3d      column2;
    matrix.GetColumn (column2, 2);
    RealDwgUtil::RoundDirectionVector (roundedNormal, column2);

    acCircle->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (roundedNormal));
    acCircle->setCenter (RealDwgUtil::GePoint3dFromDPoint3d (center));
    acCircle->setRadius (r0);
    acCircle->setThickness (extrusionDistance);

    // add a solid fill/hatch for filled circle
    this->AddHatchOrWipeout (acCircle, inElement, context);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbEllipseFromArcElement
(
AcDbEllipse*            acEllipse,
DPoint3dCR              center,
RotMatrixCR             matrix,
double                  r0,
double                  r1,
double                  start, 
double                  sweep, 
double                  extrusionDistance,
ElementHandleCR         inElement,
ConvertFromDgnContextR  context
) const
    {
    DMatrix3d   dMatrix;
    dMatrix.initFromRotMatrix (&matrix);
    dMatrix.squareAndNormalizeColumns (&dMatrix, 2, 1);

    for (int iColumn=0; iColumn<3; iColumn++)
        {
        if (RealDwgUtil::RoundDirectionVector (dMatrix.column[iColumn], dMatrix.column[iColumn]))
            dMatrix.squareAndNormalizeColumns (&dMatrix, iColumn, (iColumn + 1) % 3);
        }

    DVec3d      majorAxis = dMatrix.column[0];
    majorAxis.scale (r0);

    double      radiusRatio = (0.0 == r0) ? 1.0 : r1 / r0;
    bool        isClosed = Angle::IsFullCircle (sweep);
    double      end = start + sweep;

    // The Dickless wonders can't handle an ellipse with yRadius > xRadius
    if (radiusRatio > 1.0)
        {
        DVec3d  normal = dMatrix.column[2], xVector;

        xVector.Normalize (majorAxis);
        majorAxis.CrossProduct (normal, xVector);
        RealDwgUtil::RoundDirectionVector (majorAxis, majorAxis);
        majorAxis.Scale (r0 * radiusRatio);
        radiusRatio = 1.0 / radiusRatio;

        if (!isClosed)
            {
            start -= msGeomConst_piOver2;
            end   -= msGeomConst_piOver2;
            }
        }

    if (!isClosed)
        {
        start = Angle::AdjustToSweep (start, 0, msGeomConst_2pi);
        end   = Angle::AdjustToSweep (end, 0, msGeomConst_2pi);
        }
    else
        {
        start = 0.0;
        end = msGeomConst_2pi;
        }

    acEllipse->set (RealDwgUtil::GePoint3dFromDPoint3d (center),
                    RealDwgUtil::GeVector3dFromDPoint3d (dMatrix.column[2]),
                    RealDwgUtil::GeVector3dFromDPoint3d (majorAxis),
                    radiusRatio, start, end);

    if (!isClosed)
        {
        // Angle and param are different, but no set method for params, so set correctly here.
        acEllipse->setStartParam (start);
        acEllipse->setEndParam (end);
        }

    // add a solid fill/hatch for filled circle
    this->AddHatchOrWipeout (acEllipse, inElement, context);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbWipeoutFromClosedArcElement (AcDbWipeout* acWipeout, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    DPoint3dArray   points;
    if (RealDwgSuccess != context.StrokeClosedArcToPoints(points, 100, inElement))
        return  CantCreateWipeout;

    context.GetTransformFromDGN().Multiply (points, points);

    RealDwgStatus   status = context.SetAcDbWipeoutFromPoints(acWipeout, &points.front(), points.size());

    if (RealDwgSuccess == status)
        {
        // add a patterned hatch as necessary
        bool        patterned = false;
        if (context.IsFilledOrPatterned(inElement, NULL, NULL, NULL, &patterned, NULL, NULL, NULL) && patterned)
            context.AddSingleHatchEntity (inElement, true, false);
        }

    return  status;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          inElement,          // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // drop degenerated arc/ellipse to a line:
    DPoint3d            points[2];
    if (context.IsDegenerateArc(points, inElement))
        {
        DSegment3d          lineSeg = DSegment3d::From (points[0], points[1]);
        EditElementHandle   lineElem;
        ToDwgExtension*     toDwg;
        if (BSISUCCESS != LineHandler::CreateLineElement(lineElem, NULL, lineSeg, context.GetThreeD(), *context.GetModel()) &&
            NULL != (toDwg = ToDwgExtension::Cast(lineElem.GetHandler())))
            {
            return  toDwg->ToObject (lineElem, acObject, existingObject, context);
            }
        return  MstnElementUnacceptable;
        }
    
    AcRxClass*          typeNeeded = GetTypeNeeded (inElement, context);
    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    AcDbPolyline*       acPolyline = AcDbPolyline::cast (acObject);
    if (NULL != acPolyline)
        return  context.SetAcDbPolylineFromElement (acPolyline, inElement);

    bool                closed, circular;
    DPoint3d            center;
    RotMatrix           matrix;
    double              r0, r1, start, sweep, extrusion;

    StatusInt   status = context.ExtractArcFromElement (closed, circular, center, matrix, r0, r1, start, sweep, extrusion, inElement);
    if (SUCCESS != status)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (context.GetSettings().IsZeroZCoordinateEnforced())
        center.z = 0.0;

    AcDbCircle*         acCircle = AcDbCircle::cast (acObject);
    if (NULL != acCircle)
        {
        if (closed && circular)
            return  SetAcDbCircleFromArcElement (acCircle, center, matrix, r0, r1, start, sweep, extrusion, inElement, context);
        else
            return  MstnElementUnacceptable;
        }

    AcDbArc*            acArc = AcDbArc::cast (acObject);
    if (NULL != acArc)
        {
        if (circular)
            return  SetAcDbArcFromArcElement (acArc, center, matrix, r0, r1, start, sweep, extrusion, context);
        else
            return  MstnElementUnacceptable;
        }

    AcDbEllipse*        acEllipse = AcDbEllipse::cast (acObject);
    if (NULL != acEllipse)
        return  SetAcDbEllipseFromArcElement (acEllipse, center, matrix, r0, r1, start, sweep, extrusion, inElement, context);

    AcDbHatch*          acHatch = AcDbHatch::cast (acObject);
    if (NULL != acHatch && closed)
        return  SetAcDbHatchFromClosedArcElement (acHatch, inElement, context);

    AcDbWipeout*        acWipeout = AcDbWipeout::cast (acObject);
    if (NULL != acWipeout && closed)
        return  SetAcDbWipeoutFromClosedArcElement (acWipeout, inElement, context);

    // should never reach here!
    assert (false && L"Unsupported arc type in ToDwgExtArc/Ellipse");
    return NoConversionMethod;
    }

};      // ToDwgExtElliptic


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          07/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtArc : public ToDwgExtElliptic
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          inElement,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    return  __super::ToObject (inElement, acObject, existingObject, context);
    }

};  // ToDwgExtArc



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          07/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtEllipse : public ToDwgExtElliptic
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          inElement,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    return  __super::ToObject (inElement, acObject, existingObject, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObjectPostProcess (ElementHandleR inElement, AcDbObjectP acObject, ConvertFromDgnContextR context) const override
    {
    // associate hatch with boundary entities
    if (context.IsFilledOrPatterned(inElement))
        {
        ToDwgExtAssocRegion     assocRegion;
        return assocRegion.ToObjectPostProcess (inElement, acObject, context);
        }

    return  RealDwgSuccess;
    }

};  // ToDwgExtEllipse


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::StrokeClosedArcToPoints (DPoint3dArrayR outPoints, size_t numPoints, ElementHandleCR inElement)
    {
    DEllipse3d      ellipse;
    if (numPoints < 3 || BSISUCCESS != this->ExtractDEllipse3dFromElement(ellipse, inElement) || !ellipse.IsFullEllipse())
        return MstnElementUnacceptable;

    outPoints.resize (numPoints);

    size_t          numSegments = numPoints - 1;
    double          delta = Angle::TwoPi() / numSegments;
    size_t          i = 0;
    for (double theta = 0.0; i < numSegments; i++, theta += delta)
        ellipse.Evaluate (outPoints[i], theta);

    // close the loop
    outPoints[i] = outPoints[0];

    return  RealDwgSuccess;
    }
