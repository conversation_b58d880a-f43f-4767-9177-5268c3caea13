#include "DWGEntityProcessor.h"
#include "DWGSplineProcessor.h"
#include "DWGPolylineProcessor.h"
#include "DWGHatchProcessor.h"
#include "DWG3DEntityProcessor.h"
#include "DWGDimensionProcessor.h"
#include "DWGBlockProcessor.h"
#include "DWGImageProcessor.h"
#include "DWGTableProcessor.h"
#include "DWGMlineProcessor.h"
#include "DWGViewportProcessor.h"

namespace IModelExport {

//=======================================================================================
// Static member definitions
//=======================================================================================

std::unordered_map<std::string, DWGEntityProcessorFactory::ProcessorInfo> DWGEntityProcessorFactory::s_processorRegistry;
bool DWGEntityProcessorFactory::s_initialized = false;

// Entity type categories
const std::vector<std::string> DWGEntityProcessorFactory::BASIC_2D_ENTITIES = {
    "Line", "Point", "Circle", "Arc", "Ellipse", "Polyline", "LWPolyline", 
    "Polyline2D", "Polyline3D", "Spline", "Ray", "XLine", "Shape", "Solid", "Trace", "Wipeout"
};

const std::vector<std::string> DWGEntityProcessorFactory::TEXT_ENTITIES = {
    "Text", "MText", "AttributeDefinition", "Attribute", "RText"
};

const std::vector<std::string> DWGEntityProcessorFactory::DIMENSION_ENTITIES = {
    "AlignedDimension", "AngularDimension", "DiametricDimension", "LinearDimension",
    "OrdinateDimension", "RadialDimension", "RotatedDimension", "ArcDimension",
    "Leader", "MLeader", "Tolerance"
};

const std::vector<std::string> DWGEntityProcessorFactory::HATCH_ENTITIES = {
    "Hatch", "Gradient", "Region"
};

const std::vector<std::string> DWGEntityProcessorFactory::BLOCK_ENTITIES = {
    "BlockReference", "MInsertBlock", "BlockBegin", "BlockEnd"
};

const std::vector<std::string> DWGEntityProcessorFactory::MESH_3D_ENTITIES = {
    "Face3D", "PolyFaceMesh", "PolygonMesh", "SubDMesh"
};

const std::vector<std::string> DWGEntityProcessorFactory::SURFACE_3D_ENTITIES = {
    "Surface", "PlaneSurface", "NurbSurface", "RevolvedSurface", 
    "ExtrudedSurface", "SweptSurface", "LoftedSurface"
};

const std::vector<std::string> DWGEntityProcessorFactory::SOLID_3D_ENTITIES = {
    "Solid3D", "Body", "Region3D"
};

const std::vector<std::string> DWGEntityProcessorFactory::IMAGE_ENTITIES = {
    "RasterImage", "OLE2Frame", "UnderlayReference", "DwfUnderlay", 
    "DgnUnderlay", "PdfUnderlay", "PointCloud", "PointCloudEx"
};

const std::vector<std::string> DWGEntityProcessorFactory::TABLE_ENTITIES = {
    "Table", "Field"
};

const std::vector<std::string> DWGEntityProcessorFactory::VIEWPORT_ENTITIES = {
    "Viewport", "PaperSpaceViewport"
};

const std::vector<std::string> DWGEntityProcessorFactory::SPECIALTY_ENTITIES = {
    "MLine", "Light", "DistantLight", "PointLight", "SpotLight", "WebLight", "Sun",
    "Section", "SectionSettings", "ProxyEntity", "ProxyObject"
};

//=======================================================================================
// Factory Implementation
//=======================================================================================

std::unique_ptr<DWGEntityProcessor> DWGEntityProcessorFactory::CreateProcessor(const std::string& entityType, DWGExporter* exporter) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    auto it = s_processorRegistry.find(entityType);
    if (it != s_processorRegistry.end()) {
        return it->second.factory(exporter);
    }
    
    return nullptr;
}

std::vector<std::string> DWGEntityProcessorFactory::GetSupportedEntityTypes() {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    std::vector<std::string> types;
    types.reserve(s_processorRegistry.size());
    
    for (const auto& pair : s_processorRegistry) {
        types.push_back(pair.first);
    }
    
    std::sort(types.begin(), types.end());
    return types;
}

bool DWGEntityProcessorFactory::IsEntityTypeSupported(const std::string& entityType) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    return s_processorRegistry.find(entityType) != s_processorRegistry.end();
}

std::vector<std::string> DWGEntityProcessorFactory::GetProcessorCategories() {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    std::set<std::string> categories;
    for (const auto& pair : s_processorRegistry) {
        categories.insert(pair.second.category);
    }
    
    return std::vector<std::string>(categories.begin(), categories.end());
}

std::vector<std::string> DWGEntityProcessorFactory::GetProcessorsInCategory(const std::string& category) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    std::vector<std::string> processors;
    for (const auto& pair : s_processorRegistry) {
        if (pair.second.category == category) {
            processors.push_back(pair.first);
        }
    }
    
    std::sort(processors.begin(), processors.end());
    return processors;
}

bool DWGEntityProcessorFactory::RegisterProcessor(const std::string& entityType,
                                                 std::function<std::unique_ptr<DWGEntityProcessor>(DWGExporter*)> factory,
                                                 const std::string& description,
                                                 const std::string& category,
                                                 const std::vector<std::string>& capabilities) {
    ProcessorInfo info;
    info.factory = factory;
    info.description = description;
    info.category = category;
    info.capabilities = capabilities;
    
    s_processorRegistry[entityType] = info;
    return true;
}

bool DWGEntityProcessorFactory::UnregisterProcessor(const std::string& entityType) {
    return s_processorRegistry.erase(entityType) > 0;
}

std::string DWGEntityProcessorFactory::GetProcessorDescription(const std::string& entityType) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    auto it = s_processorRegistry.find(entityType);
    return (it != s_processorRegistry.end()) ? it->second.description : "";
}

std::string DWGEntityProcessorFactory::GetProcessorCategory(const std::string& entityType) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    auto it = s_processorRegistry.find(entityType);
    return (it != s_processorRegistry.end()) ? it->second.category : "";
}

std::vector<std::string> DWGEntityProcessorFactory::GetProcessorCapabilities(const std::string& entityType) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    auto it = s_processorRegistry.find(entityType);
    return (it != s_processorRegistry.end()) ? it->second.capabilities : std::vector<std::string>();
}

std::unordered_map<std::string, std::unique_ptr<DWGEntityProcessor>> 
DWGEntityProcessorFactory::CreateAllProcessors(DWGExporter* exporter) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    std::unordered_map<std::string, std::unique_ptr<DWGEntityProcessor>> processors;
    
    for (const auto& pair : s_processorRegistry) {
        auto processor = pair.second.factory(exporter);
        if (processor) {
            processors[pair.first] = std::move(processor);
        }
    }
    
    return processors;
}

std::unordered_map<std::string, std::unique_ptr<DWGEntityProcessor>> 
DWGEntityProcessorFactory::CreateProcessorsForCategory(const std::string& category, DWGExporter* exporter) {
    if (!s_initialized) {
        InitializeFactories();
    }
    
    std::unordered_map<std::string, std::unique_ptr<DWGEntityProcessor>> processors;
    
    for (const auto& pair : s_processorRegistry) {
        if (pair.second.category == category) {
            auto processor = pair.second.factory(exporter);
            if (processor) {
                processors[pair.first] = std::move(processor);
            }
        }
    }
    
    return processors;
}

//=======================================================================================
// Factory Initialization
//=======================================================================================

void DWGEntityProcessorFactory::InitializeFactories() {
    if (s_initialized) {
        return;
    }
    
    RegisterBasicProcessors();
    RegisterAdvancedProcessors();
    Register3DProcessors();
    RegisterSpecialtyProcessors();
    
    s_initialized = true;
}

void DWGEntityProcessorFactory::RegisterBasicProcessors() {
    // Basic 2D entities
    RegisterProcessor("Line", 
        [](DWGExporter* exporter) { return std::make_unique<DWGLineProcessor>(exporter); },
        "Processes lines, rays, and construction lines",
        "Basic2D",
        {"Line", "Ray", "XLine", "InfiniteLine"});
    
    RegisterProcessor("Circle", 
        [](DWGExporter* exporter) { return std::make_unique<DWGCircleProcessor>(exporter); },
        "Processes circles, arcs, and ellipses",
        "Basic2D",
        {"Circle", "Arc", "Ellipse", "EllipticalArc"});
    
    RegisterProcessor("Text", 
        [](DWGExporter* exporter) { return std::make_unique<DWGTextProcessor>(exporter); },
        "Processes single-line and multi-line text",
        "Text",
        {"Text", "MText", "TextFormatting", "Unicode"});
    
    RegisterProcessor("Spline", 
        [](DWGExporter* exporter) { return std::make_unique<DWGSplineProcessor>(exporter); },
        "Processes NURBS curves and splines",
        "Advanced2D",
        {"Spline", "NURBS", "BezierCurve", "InterpolationCurve"});
    
    RegisterProcessor("Polyline", 
        [](DWGExporter* exporter) { return std::make_unique<DWGPolylineProcessor>(exporter); },
        "Processes polylines with arcs and variable width",
        "Advanced2D",
        {"Polyline", "LWPolyline", "2DPolyline", "3DPolyline", "ArcSegments", "VariableWidth"});
}

void DWGEntityProcessorFactory::RegisterAdvancedProcessors() {
    // Hatch and fill entities
    RegisterProcessor("Hatch", 
        [](DWGExporter* exporter) { return std::make_unique<DWGHatchProcessor>(exporter); },
        "Processes hatch patterns, gradients, and regions",
        "Fill",
        {"Hatch", "Gradient", "Region", "Boundary", "Pattern"});
    
    // Dimension entities
    RegisterProcessor("Dimension", 
        [](DWGExporter* exporter) { return std::make_unique<DWGDimensionProcessor>(exporter); },
        "Processes all types of dimensions and leaders",
        "Dimension",
        {"LinearDimension", "AngularDimension", "RadialDimension", "Leader", "MLeader", "Tolerance"});
    
    // Block entities
    RegisterProcessor("Block", 
        [](DWGExporter* exporter) { return std::make_unique<DWGBlockProcessor>(exporter); },
        "Processes block references and definitions",
        "Block",
        {"BlockReference", "MInsertBlock", "Attributes", "XRef", "DynamicBlocks"});
    
    // Multiline entities
    RegisterProcessor("MLine", 
        [](DWGExporter* exporter) { return std::make_unique<DWGMlineProcessor>(exporter); },
        "Processes multilines with custom styles",
        "Advanced2D",
        {"MLine", "MLineStyle", "ParallelLines", "Caps", "Joints"});
}

void DWGEntityProcessorFactory::Register3DProcessors() {
    // 3D entities
    RegisterProcessor("3DEntity", 
        [](DWGExporter* exporter) { return std::make_unique<DWG3DEntityProcessor>(exporter); },
        "Processes 3D faces, meshes, surfaces, and solids",
        "3D",
        {"Face3D", "PolygonMesh", "PolyFaceMesh", "SubDMesh", "Surface", "Solid3D", "ACIS"});
    
    // Viewport entities
    RegisterProcessor("Viewport", 
        [](DWGExporter* exporter) { return std::make_unique<DWGViewportProcessor>(exporter); },
        "Processes viewports and layouts",
        "Layout",
        {"Viewport", "Layout", "PlotSettings", "ViewTransformation", "Clipping"});
}

void DWGEntityProcessorFactory::RegisterSpecialtyProcessors() {
    // Image and media entities
    RegisterProcessor("Image", 
        [](DWGExporter* exporter) { return std::make_unique<DWGImageProcessor>(exporter); },
        "Processes raster images, OLE objects, and underlays",
        "Media",
        {"RasterImage", "OLE2Frame", "Underlay", "PointCloud", "Wipeout"});
    
    // Table entities
    RegisterProcessor("Table", 
        [](DWGExporter* exporter) { return std::make_unique<DWGTableProcessor>(exporter); },
        "Processes tables and fields",
        "Data",
        {"Table", "Field", "CellStyle", "Formula", "DataLink"});
}

} // namespace IModelExport
