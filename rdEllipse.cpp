/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdEllipse.cpp $
|
|  $Copyright: (c) 2011 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtEllipse : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbEllipse*        pEllipse = AcDbEllipse::cast (acObject);

    double      startParam = 0.0, endParam = 0.0;
    pEllipse->getStartParam (startParam);
    pEllipse->getEndParam (endParam);

    DVec3d      column0, column1, column2;
    RealDwgUtil::DPoint3dFromGeVector3d (column0, pEllipse->majorAxis());
    RealDwgUtil::DPoint3dFromGeVector3d (column1, pEllipse->minorAxis());
    RealDwgUtil::DPoint3dFromGeVector3d (column2, pEllipse->normal());

    double      xRadius     = column0.normalize();
    double      yRadius     = column1.normalize();
    double      sweepAngle  = endParam - startParam;

    if (!Angle::IsFullCircle(sweepAngle))
        sweepAngle = Angle::AdjustToSweep (sweepAngle, 0, msGeomConst_2pi);

    RotMatrix   rotMatrix;
    rotMatrix.InitFromColumnVectors (column0, column1, column2);
    return context.CreateElementFromArcParams (outElement, NULL, pEllipse->center(), &rotMatrix, xRadius, yRadius, startParam, sweepAngle,
                                               0.0, Angle::IsFullCircle(sweepAngle), pEllipse);
    }

};
