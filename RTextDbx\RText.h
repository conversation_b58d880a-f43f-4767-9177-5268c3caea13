/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/RTextDbx/RText.h $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once
#include <map>
#include <Realdwg\Base\dbmain.h>
#include <Realdwg\Base\gepnt3d.h>

#ifdef BUILD_RTEXT_DBX
    #define RTEXT_EXPORT  __declspec(dllexport)  
#else
    #define RTEXT_EXPORT
#endif

/*=================================================================================**//**
* A cheater object enabler for RTEXT entity
* @bsiclass                                                     Don.Fu          11/19
+===============+===============+===============+===============+===============+======*/
class RTEXT_EXPORT RText : public AcDbEntity
{
public:
    ACRX_DECLARE_MEMBERS(RText);

    virtual Acad::ErrorStatus	dwgInFields(AcDbDwgFiler* filer);
    virtual Acad::ErrorStatus	dxfInFields(AcDbDxfFiler* filer);

    struct Data 
        {
    public:
        AcGePoint3d         m_position;
        AcGeVector3d        m_normal;
        double              m_angle;
        double              m_height;
        Adesk::Int16        m_flags;
        AcString            m_contents;
        AcDbHardPointerId   m_textstyleId;      // DWG
        AcString            m_textstyleName;    // DXF
    public:
        RTEXT_EXPORT Data ();
        bool IsTextFile () const { return m_flags == 0; }
        bool IsDieselExpression () const { return m_flags == 1; }
        }; // Data
};  // RText

typedef std::map<AcDbObjectId,RText::Data>   RTextCollection;
typedef std::pair<AcDbObjectId,RText::Data>  RTextEntry;

RTEXT_EXPORT void               InitializeRTextCollection ();
RTEXT_EXPORT RTextCollection&   GetRTextCollection ();

