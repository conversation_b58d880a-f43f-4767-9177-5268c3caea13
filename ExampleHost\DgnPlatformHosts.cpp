/*--------------------------------------------------------------------------------------+
|
| $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DgnPlatformHosts.cpp $
|
| $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    <windows.h>
#include    <DgnPlatform\DgnPlatformApi.h>
#include    "DgnPlatformHosts.h"

BEGIN_HOSTINTERFACE_NAMESPACE
DgnConsoleAppHost* g_host;
END_HOSTINTERFACE_NAMESPACE

USING_NAMESPACE_BENTLEY_DGNPLATFORM
USING_NAMESPACE_HOSTINTERFACE

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt DgnConsoleAppNotificationAdmin::_OutputMessage (DgnPlatform::NotifyMessageDetails const& notification)
    {
    switch (notification.GetPriority ())
        {
        case OutputMessagePriority::Error:
            {
            printf ("\nERROR: %ls\n%ls", notification.GetBriefMsg ().c_str (), notification.GetDetailedMsg ().c_str ());
            break;
            }
        case OutputMessagePriority::Warning:
            {
            printf ("\nWarning: %ls", notification.GetBriefMsg ().c_str ());
            break;
            }
        case OutputMessagePriority::Info:
            {
            printf ("\nIfo: %ls", notification.GetBriefMsg ().c_str ());
            break;
            }
        case OutputMessagePriority::Debug:
            {
            printf ("\nDebug: %ls", notification.GetBriefMsg ().c_str ());
            break;
            }
        }
    
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
void DgnConsoleAppNotificationAdmin::_OutputPrompt (WCharCP prompt)
    {
    printf ("\nPrompt: %ls received", prompt);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
void DgnConsoleAppHost::Initialize ()
    {
    WString cfgFileSpec;
    WChar exeFileName[MAX_PATH];

    ::GetModuleFileNameW (NULL, exeFileName, MAX_PATH);

    // make sure location of font config file is defined
    if (SUCCESS != ConfigurationManager::GetVariable (cfgFileSpec, L"MS_FONTCONFIGFILE"))
        {
        WChar       cfgFileName[MAX_PATH];
        WString     filePath;
        WChar       drive[MAXDIRLENGTH], dir[MAXDIRLENGTH];

        ::_wsplitpath (exeFileName, drive, dir, NULL, NULL);
        ::_wmakepath (cfgFileName, drive, dir, L"..\\..\\Data\\Fonts\\MstnFontConfig", L".xml");

        ConfigurationManager::DefineVariable (L"MS_FONTCONFIGFILE", cfgFileName);
        }

    // make sure location of font config file is defined
    if (SUCCESS != ConfigurationManager::GetVariable (cfgFileSpec, L"MS_FONTPATH"))
        {
        WChar     cfgFileName[MAX_PATH];
        WString   filePath;
        WChar     drive[MAXDIRLENGTH], dir[MAXDIRLENGTH];

        ::_wsplitpath (exeFileName, drive, dir, NULL, NULL);
        ::_wmakepath (cfgFileName, drive, dir, L"..\\..\\Data\\Fonts\\", L"");

        ConfigurationManager::DefineVariable (L"MS_FONTPATH", cfgFileName);
        }

    DgnPlatformLib::Initialize (*(g_host = new DgnConsoleAppHost), true);
    }

