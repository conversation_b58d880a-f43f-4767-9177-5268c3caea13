# Core Components

## File I/O Management

### RealDwgFileIO
**Location**: `rDwgFileIO.h`, `rDwgFileIO.cpp`

The main file I/O coordinator that implements the DgnPlatform::DgnFileIO interface.

**Key Responsibilities**:
- File format detection and validation
- Model loading and caching coordination
- Database transaction management
- Progress monitoring and error handling

**Key Methods**:
```cpp
virtual StatusInt LoadFile(DesignFileHeader* pHdr, bool* openedReadonly, 
                          StatusInt* rwStatus, DgnFileOpenMode openMode, 
                          DgnFileLoadContextP);
virtual StatusInt GetModelReader(DgnModelReader** ppStream, DgnModelP pCache);
virtual StatusInt _WriteChanges(bool doFullSave, double timestamp, 
                               DesignFileHeader* hdr);
```

### FileHolder
**Location**: `rDwgFileHolder.h`, `rDwgFileHolder.cpp`

Central database management component that bridges RealDWG and DGN systems.

**Key Responsibilities**:
- AcDbDatabase lifecycle management
- Model indexing and metadata tracking
- ID mapping between DWG handles and DGN element IDs
- Symbology data caching and management

**Key Components**:
```cpp
class FileHolder {
    AcDbDatabase*                   m_database;
    DgnFileP                        m_dgnFile;
    RealDwgModelIndexItemArray      m_models;
    DwgSymbologyData*               m_dwgSymbologyData;
    DgnSymbologyData*               m_dgnSymbologyData;
    // ... ID mapping and caching structures
};
```

### RealDwgModelIndexItem
**Location**: `rdModelIndexItem.cpp`

Represents individual models/layouts within a DWG file.

**Key Responsibilities**:
- Model metadata storage
- Block table record association
- Layout information management
- Model loading state tracking

## Conversion Engine

### ConvertToDgnContext
**Location**: `rDwgToDgnContext.cpp`

Manages DWG → DGN conversion process.

**Key Responsibilities**:
- Entity iteration and conversion coordination
- DGN element creation and placement
- Coordinate system transformation
- Progress tracking and error handling

**Key Methods**:
```cpp
StatusInt LoadModelFromDatabase(DgnModelP seedModel, 
                               RealDwgModelIndexItem* pModelIndexItem);
void SaveElementToDgn(AcDbEntity* pEntity, MSElementDescrP* ppDescr);
```

### ConvertFromDgnContext
**Location**: `rDwgFromDgnContext.cpp`

Manages DGN → DWG conversion process.

**Key Responsibilities**:
- DGN element iteration and processing
- AutoCAD entity creation and database insertion
- Coordinate system transformation
- Reference file handling

**Key Methods**:
```cpp
StatusInt SaveNonCacheChanges(bool doFullSave, DgnFileP dgnFile);
void SaveElementToDatabase(ElementHandleCR eh, AcDbObjectId* outId);
```

### Multi-Processing Support
Both conversion contexts have multi-processing variants:
- **ConvertToDgnMultiProcessingContext**
- **ConvertFromDgnMultiProcessingContext**

These provide parallel processing capabilities for large file conversions.

## Entity Conversion System

### Entity Converter Files
Each AutoCAD entity type has a dedicated converter file:

**Geometric Entities**:
- `rdLine.cpp` - Line entities
- `rdArc.cpp` - Arc entities  
- `rdCircle.cpp` - Circle entities
- `rdEllipse.cpp` - Ellipse entities
- `rdPolyline.cpp` - Polyline entities
- `rdSpline.cpp` - Spline/NURBS entities

**Complex Entities**:
- `rdHatch.cpp` - Hatch/fill patterns
- `rdMText.cpp` - Multiline text
- `rdDimension.cpp` - Dimension entities
- `rdBlock.cpp` - Block definitions
- `rdBlockReference.cpp` - Block insertions

**3D Entities**:
- `rdSolid.cpp` - 3D solid entities
- `rdSurface.cpp` - Surface entities
- `rdMesh.cpp` - Mesh entities
- `rdAcisData.cpp` - ACIS solid data

### DGN Entity Creation
Corresponding DGN entity creation files:
- `dgnLinear.cpp` - Linear elements
- `dgnArcs.cpp` - Arc elements
- `dgnTexts.cpp` - Text elements
- `dgnCells.cpp` - Cell elements
- `dgnSolids.cpp` - Solid elements

## Symbology Management

### DwgSymbologyData
**Location**: `rDwgSymbologyData.cpp`

Manages AutoCAD symbology (colors, linetypes, layers).

**Key Features**:
- Color table management
- Linetype definition caching
- Layer property mapping
- Material definition handling

### DgnSymbologyData  
**Location**: `rDwgSymbologyData.cpp`

Manages MicroStation symbology (levels, line styles, colors).

**Key Features**:
- Level table management
- Line style resource handling
- Color palette management
- Material library integration

### Conversion Modules
- `rdLayerConvert.cpp` - Layer ↔ Level conversion
- `rdLineStyleConvert.cpp` - Linetype ↔ Line Style conversion
- `rdMaterialConvert.cpp` - Material conversion
- `rdDimStyleConvert.cpp` - Dimension style conversion

## View System

### ViewConvert
**Location**: `rdViewConvert.cpp`

Handles viewport and view conversion between formats.

**Key Features**:
- Viewport parameter mapping
- Camera and projection conversion
- Display style management
- View group creation and organization

**Key Functions**:
```cpp
void SetDgnViewFromDwgViewParams(ViewInfoR viewInfo, ViewPortInfoR viewPortInfo, ...);
void SetDgnViewFromViewportTableRecord(ViewInfoR viewInfo, ...);
RealDwgStatus SaveViewGroupsToDgn();
```

## Extension Framework

### DwgDgnExtension
**Location**: `rDwgDgnExtension.cpp`

Base class for entity conversion extensions.

**Key Features**:
- Virtual conversion methods
- Entity type registration
- Custom property handling
- Error reporting mechanisms

### Host Integration

### DwgPlatformHost
**Location**: `DwgPlatformHost.cpp`

Provides host application integration points.

**Key Features**:
- Application-specific settings
- Custom display style provision
- Progress monitoring integration
- File path resolution

### MstnInterfaceHelper
**Location**: `rDwgInternal.h`

Singleton helper for MicroStation integration.

**Key Features**:
- Adapter registration and management
- Protocol extension loading
- Settings management
- Host application coordination

## Utility Components

### RealDwgUtil
Utility functions for common conversion tasks:
- Coordinate transformation
- Geometry conversion
- String handling
- Error code mapping

### Progress Monitoring
- **AcDbProgressMeter** integration
- **IDgnProgressMeter** support
- Multi-threaded progress coordination
- User cancellation handling

### Error Handling
- **RealDwgException** hierarchy
- **FileHolderException** for database errors
- **DiscardInvalidEntityException** for entity errors
- Graceful degradation strategies
