#include "DWGViewportProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbviewport.h>
#include <realdwg/base/dblayout.h>
#include <realdwg/base/dbviewtablerecord.h>
#include <realdwg/base/dbplotsettings.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gematrix3d.h>
#endif

#include <algorithm>
#include <cmath>

namespace IModelExport {

//=======================================================================================
// ViewportGeometry Implementation
//=======================================================================================

bool ViewportGeometry::IsValid() const {
    // Validate center point
    if (!std::isfinite(centerPoint.x) || !std::isfinite(centerPoint.y) || !std::isfinite(centerPoint.z)) {
        return false;
    }
    
    // Validate dimensions
    if (!std::isfinite(width) || !std::isfinite(height) || width <= 0.0 || height <= 0.0) {
        return false;
    }
    
    // Validate view parameters
    if (!std::isfinite(viewHeight) || !std::isfinite(lensLength) || !std::isfinite(frontClip) || 
        !std::isfinite(backClip) || !std::isfinite(snapAngle) || !std::isfinite(twistAngle)) {
        return false;
    }
    
    // Validate view center and target
    if (!std::isfinite(viewCenter.x) || !std::isfinite(viewCenter.y) || !std::isfinite(viewCenter.z) ||
        !std::isfinite(viewTarget.x) || !std::isfinite(viewTarget.y) || !std::isfinite(viewTarget.z)) {
        return false;
    }
    
    // Validate view direction
    if (!std::isfinite(viewDirection.x) || !std::isfinite(viewDirection.y) || !std::isfinite(viewDirection.z)) {
        return false;
    }
    
    // Validate positive values
    if (viewHeight <= 0.0 || lensLength <= 0.0) {
        return false;
    }
    
    // Validate clipping planes
    if (frontClip >= backClip) {
        return false;
    }
    
    return true;
}

double ViewportGeometry::GetAspectRatio() const {
    return (height > 0.0) ? (width / height) : 1.0;
}

double ViewportGeometry::CalculateScale() const {
    // Calculate scale based on view height and viewport height
    return (viewHeight > 0.0) ? (height / viewHeight) : 1.0;
}

BoundingBox3D ViewportGeometry::CalculateBounds() const {
    BoundingBox3D bounds;
    
    // Calculate viewport corners
    double halfWidth = width * 0.5;
    double halfHeight = height * 0.5;
    
    Point3d corner1(centerPoint.x - halfWidth, centerPoint.y - halfHeight, centerPoint.z);
    Point3d corner2(centerPoint.x + halfWidth, centerPoint.y - halfHeight, centerPoint.z);
    Point3d corner3(centerPoint.x + halfWidth, centerPoint.y + halfHeight, centerPoint.z);
    Point3d corner4(centerPoint.x - halfWidth, centerPoint.y + halfHeight, centerPoint.z);
    
    bounds.AddPoint(corner1);
    bounds.AddPoint(corner2);
    bounds.AddPoint(corner3);
    bounds.AddPoint(corner4);
    
    return bounds;
}

//=======================================================================================
// LayoutGeometry Implementation
//=======================================================================================

bool LayoutGeometry::IsValid() const {
    // Validate name
    if (name.empty()) {
        return false;
    }
    
    // Validate paper size
    if (!std::isfinite(paperWidth) || !std::isfinite(paperHeight) || 
        paperWidth <= 0.0 || paperHeight <= 0.0) {
        return false;
    }
    
    // Validate margins
    if (!std::isfinite(leftMargin) || !std::isfinite(rightMargin) || 
        !std::isfinite(topMargin) || !std::isfinite(bottomMargin) ||
        leftMargin < 0.0 || rightMargin < 0.0 || topMargin < 0.0 || bottomMargin < 0.0) {
        return false;
    }
    
    // Validate plot origin
    if (!std::isfinite(plotOrigin.x) || !std::isfinite(plotOrigin.y)) {
        return false;
    }
    
    // Validate plot scale
    if (!std::isfinite(plotScale) || plotScale <= 0.0) {
        return false;
    }
    
    // Validate rotation
    if (!std::isfinite(plotRotation)) {
        return false;
    }
    
    // Check that margins don't exceed paper size
    if (leftMargin + rightMargin >= paperWidth || topMargin + bottomMargin >= paperHeight) {
        return false;
    }
    
    return true;
}

double LayoutGeometry::GetPlotAreaWidth() const {
    return paperWidth - leftMargin - rightMargin;
}

double LayoutGeometry::GetPlotAreaHeight() const {
    return paperHeight - topMargin - bottomMargin;
}

//=======================================================================================
// DWGViewportProcessor Implementation
//=======================================================================================

DWGViewportProcessor::DWGViewportProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_viewportTolerance(1e-6)
    , m_layoutTolerance(1e-3)
    , m_enableViewportValidation(true)
    , m_enableLayoutValidation(true)
    , m_autoRepairViewports(true)
    , m_maxViewports(100)
    , m_minViewportSize(1.0)
    , m_maxViewportSize(10000.0)
{
}

DWGProcessingStatus DWGViewportProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // This is a simplified example - real implementation would extract viewport geometry from element
        // For demonstration, we'll create a sample viewport
        
        if (element.type == ElementType::GeometricElement) {
            ViewportGeometry viewport;
            viewport.centerPoint = Point3d(100, 100, 0);
            viewport.width = 200.0;
            viewport.height = 150.0;
            viewport.viewHeight = 300.0;
            viewport.viewCenter = Point3d(0, 0, 0);
            viewport.viewTarget = Point3d(0, 0, 0);
            viewport.viewDirection = Vector3d(0, 0, 1);
            viewport.lensLength = 50.0;
            viewport.frontClip = 1.0;
            viewport.backClip = 1000.0;
            viewport.isActive = true;
            viewport.isOn = true;
            
            return ProcessViewport(viewport, "Viewports");
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception processing viewport entity " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGViewportProcessor::CanProcessEntity(const ElementInfo& element) const {
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGViewportProcessor::ProcessViewport(const ViewportGeometry& geometry, const std::string& layer) {
    // Validate viewport geometry
    auto validation = ValidateViewportGeometry(geometry);
    if (!validation.isValid) {
        LogError("Viewport geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
    // Transform geometry
    ViewportGeometry transformedGeometry = geometry;
    TransformViewportGeometry(transformedGeometry);
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbViewport* viewport = CreateDWGViewport(transformedGeometry);
        if (!viewport) {
            LogError("Failed to create DWG viewport entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetViewportProperties(viewport, transformedGeometry)) {
            delete viewport;
            LogError("Failed to set viewport properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(viewport, layer)) {
            delete viewport;
            LogError("Failed to set viewport entity properties");
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(viewport)) {
            delete viewport;
            LogError("Failed to add viewport to model space");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedViewports++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG viewport: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - viewport creation skipped");
    m_processedViewports++;
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGViewportProcessor::ProcessLayout(const LayoutGeometry& geometry) {
    // Validate layout geometry
    auto validation = ValidateLayoutGeometry(geometry);
    if (!validation.isValid) {
        LogError("Layout geometry validation failed");
        for (const auto& error : validation.errors) {
            LogError("  " + error);
        }
        return DWGProcessingStatus::ValidationError;
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbLayout* layout = CreateDWGLayout(geometry);
        if (!layout) {
            LogError("Failed to create DWG layout");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetLayoutProperties(layout, geometry)) {
            delete layout;
            LogError("Failed to set layout properties");
            return DWGProcessingStatus::Failed;
        }
        
        m_processedLayouts++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG layout: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - layout creation skipped");
    m_processedLayouts++;
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

ViewportValidationResult DWGViewportProcessor::ValidateViewportGeometry(const ViewportGeometry& geometry) const {
    ViewportValidationResult result;
    result.isValid = true;
    
    // Validate dimensions
    result.hasValidDimensions = ValidateViewportDimensions(geometry);
    if (!result.hasValidDimensions) {
        result.AddViewportError("Invalid viewport dimensions");
    }
    
    // Validate view parameters
    result.hasValidViewParameters = ValidateViewParameters(geometry);
    if (!result.hasValidViewParameters) {
        result.AddViewportError("Invalid view parameters");
    }
    
    // Validate position
    result.hasValidPosition = std::isfinite(geometry.centerPoint.x) && 
                             std::isfinite(geometry.centerPoint.y) && 
                             std::isfinite(geometry.centerPoint.z);
    if (!result.hasValidPosition) {
        result.AddViewportError("Invalid center point");
    }
    
    // Calculate metrics
    result.aspectRatio = geometry.GetAspectRatio();
    result.calculatedScale = geometry.CalculateScale();
    result.viewportArea = geometry.width * geometry.height;
    
    // Check size limits
    if (geometry.width < m_minViewportSize || geometry.height < m_minViewportSize) {
        result.AddViewportWarning("Viewport is very small");
    }
    
    if (geometry.width > m_maxViewportSize || geometry.height > m_maxViewportSize) {
        result.AddViewportWarning("Viewport is very large");
    }
    
    return result;
}

LayoutValidationResult DWGViewportProcessor::ValidateLayoutGeometry(const LayoutGeometry& geometry) const {
    LayoutValidationResult result;
    result.isValid = true;
    
    // Validate name
    result.hasValidName = !geometry.name.empty() && ValidateLayoutName(geometry.name);
    if (!result.hasValidName) {
        result.AddLayoutError("Invalid layout name: " + geometry.name);
    }
    
    // Validate paper size
    result.hasValidPaperSize = ValidatePaperSize(geometry);
    if (!result.hasValidPaperSize) {
        result.AddLayoutError("Invalid paper size");
    }
    
    // Validate margins
    result.hasValidMargins = ValidateMargins(geometry);
    if (!result.hasValidMargins) {
        result.AddLayoutError("Invalid margins");
    }
    
    // Validate plot settings
    result.hasValidPlotSettings = ValidatePlotSettings(geometry);
    if (!result.hasValidPlotSettings) {
        result.AddLayoutError("Invalid plot settings");
    }
    
    // Calculate metrics
    result.plotAreaWidth = geometry.GetPlotAreaWidth();
    result.plotAreaHeight = geometry.GetPlotAreaHeight();
    result.paperAspectRatio = (geometry.paperHeight > 0.0) ? (geometry.paperWidth / geometry.paperHeight) : 1.0;
    
    return result;
}

bool DWGViewportProcessor::ValidateViewportDimensions(const ViewportGeometry& geometry) const {
    // Check basic dimensions
    if (geometry.width <= 0.0 || geometry.height <= 0.0) {
        return false;
    }
    
    // Check view height
    if (geometry.viewHeight <= 0.0) {
        return false;
    }
    
    // Check lens length
    if (geometry.lensLength <= 0.0) {
        return false;
    }
    
    return true;
}

bool DWGViewportProcessor::ValidateViewParameters(const ViewportGeometry& geometry) const {
    // Validate view direction (should not be zero vector)
    double dirLength = std::sqrt(geometry.viewDirection.x * geometry.viewDirection.x + 
                                geometry.viewDirection.y * geometry.viewDirection.y + 
                                geometry.viewDirection.z * geometry.viewDirection.z);
    
    if (dirLength < m_viewportTolerance) {
        return false;
    }
    
    // Validate clipping planes
    if (geometry.frontClip >= geometry.backClip) {
        return false;
    }
    
    return true;
}

bool DWGViewportProcessor::ValidateLayoutName(const std::string& name) const {
    // Check for reserved names
    if (name == "Model" || name == "*Model_Space" || name == "*Paper_Space") {
        return false;
    }
    
    // Check for invalid characters
    const std::string invalidChars = "<>:\"/\\|?*";
    for (char c : name) {
        if (invalidChars.find(c) != std::string::npos) {
            return false;
        }
    }
    
    return true;
}

bool DWGViewportProcessor::ValidatePaperSize(const LayoutGeometry& geometry) const {
    // Check basic paper dimensions
    if (geometry.paperWidth <= 0.0 || geometry.paperHeight <= 0.0) {
        return false;
    }
    
    // Check reasonable paper size limits (in mm)
    const double minPaperSize = 10.0;   // 10mm
    const double maxPaperSize = 2000.0; // 2000mm (2 meters)
    
    if (geometry.paperWidth < minPaperSize || geometry.paperHeight < minPaperSize ||
        geometry.paperWidth > maxPaperSize || geometry.paperHeight > maxPaperSize) {
        return false;
    }
    
    return true;
}

bool DWGViewportProcessor::ValidateMargins(const LayoutGeometry& geometry) const {
    // Check that margins are non-negative
    if (geometry.leftMargin < 0.0 || geometry.rightMargin < 0.0 ||
        geometry.topMargin < 0.0 || geometry.bottomMargin < 0.0) {
        return false;
    }
    
    // Check that margins don't exceed paper size
    if (geometry.leftMargin + geometry.rightMargin >= geometry.paperWidth ||
        geometry.topMargin + geometry.bottomMargin >= geometry.paperHeight) {
        return false;
    }
    
    return true;
}

bool DWGViewportProcessor::ValidatePlotSettings(const LayoutGeometry& geometry) const {
    // Check plot scale
    if (geometry.plotScale <= 0.0) {
        return false;
    }
    
    // Check plot rotation (should be 0, 90, 180, or 270 degrees)
    double normalizedRotation = std::fmod(std::abs(geometry.plotRotation), 2.0 * M_PI);
    double tolerance = 1e-6;
    
    bool validRotation = (std::abs(normalizedRotation) < tolerance) ||
                        (std::abs(normalizedRotation - M_PI/2) < tolerance) ||
                        (std::abs(normalizedRotation - M_PI) < tolerance) ||
                        (std::abs(normalizedRotation - 3*M_PI/2) < tolerance);
    
    return validRotation;
}

//=======================================================================================
// Helper Methods
//=======================================================================================

void DWGViewportProcessor::TransformViewportGeometry(ViewportGeometry& geometry) const {
    geometry.centerPoint = TransformPoint(geometry.centerPoint);
    geometry.viewCenter = TransformPoint(geometry.viewCenter);
    geometry.viewTarget = TransformPoint(geometry.viewTarget);
    geometry.viewDirection = TransformVector(geometry.viewDirection);
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Implementation
//=======================================================================================

AcDbViewport* DWGViewportProcessor::CreateDWGViewport(const ViewportGeometry& geometry) const {
    try {
        AcDbViewport* viewport = new AcDbViewport();
        
        // Set viewport center and size
        AcGePoint3d center(geometry.centerPoint.x, geometry.centerPoint.y, geometry.centerPoint.z);
        viewport->setCenterPoint(center);
        viewport->setWidth(geometry.width);
        viewport->setHeight(geometry.height);
        
        // Set view parameters
        AcGePoint3d viewCenter(geometry.viewCenter.x, geometry.viewCenter.y, geometry.viewCenter.z);
        AcGePoint3d viewTarget(geometry.viewTarget.x, geometry.viewTarget.y, geometry.viewTarget.z);
        AcGeVector3d viewDirection(geometry.viewDirection.x, geometry.viewDirection.y, geometry.viewDirection.z);
        
        viewport->setViewCenter(viewCenter);
        viewport->setViewTarget(viewTarget);
        viewport->setViewDirection(viewDirection);
        viewport->setViewHeight(geometry.viewHeight);
        viewport->setLensLength(geometry.lensLength);
        
        // Set clipping
        if (geometry.frontClipEnabled) {
            viewport->setFrontClipAtEye(true);
            viewport->setFrontClipDistance(geometry.frontClip);
        }
        
        if (geometry.backClipEnabled) {
            viewport->setBackClipDistance(geometry.backClip);
        }
        
        // Set viewport state
        viewport->setOn(geometry.isOn);
        
        return viewport;
    }
    catch (...) {
        return nullptr;
    }
}

AcDbLayout* DWGViewportProcessor::CreateDWGLayout(const LayoutGeometry& geometry) const {
    try {
        AcDbLayout* layout = new AcDbLayout();
        
        // Set layout name
        layout->setLayoutName(geometry.name.c_str());
        
        return layout;
    }
    catch (...) {
        return nullptr;
    }
}

bool DWGViewportProcessor::SetViewportProperties(AcDbViewport* viewport, const ViewportGeometry& geometry) const {
    if (!viewport) {
        return false;
    }
    
    try {
        // Set additional viewport properties
        viewport->setTwistAngle(geometry.twistAngle);
        viewport->setSnapAngle(geometry.snapAngle);
        
        // Set UCS if specified
        if (geometry.hasCustomUCS) {
            AcGePoint3d origin(geometry.ucsOrigin.x, geometry.ucsOrigin.y, geometry.ucsOrigin.z);
            AcGeVector3d xAxis(geometry.ucsXAxis.x, geometry.ucsXAxis.y, geometry.ucsXAxis.z);
            AcGeVector3d yAxis(geometry.ucsYAxis.x, geometry.ucsYAxis.y, geometry.ucsYAxis.z);
            
            viewport->setUcs(origin, xAxis, yAxis);
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGViewportProcessor::SetLayoutProperties(AcDbLayout* layout, const LayoutGeometry& geometry) const {
    if (!layout) {
        return false;
    }
    
    try {
        // Set plot settings would require AcDbPlotSettings
        // This is a simplified implementation
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGViewportProcessor::SetEntityProperties(AcDbEntity* entity, const std::string& layer) const {
    if (!entity) {
        return false;
    }
    
    try {
        // Set layer
        if (!layer.empty()) {
            entity->setLayer(layer.c_str());
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool DWGViewportProcessor::AddEntityToModelSpace(AcDbEntity* entity) const {
    // This would be implemented by the DWGExporter
    // For now, just return true to indicate success
    return true;
}

#endif // REALDWG_AVAILABLE

} // namespace IModelExport
