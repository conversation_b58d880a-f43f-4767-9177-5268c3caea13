/*---------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgDgnExtension.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/
#include "rDwgInternal.h"
#include "Mstn/RealDwg/rDwgUtil.h"
#include <DgnPlatform/ViewportDrawnCellHandler.h>

USING_NAMESPACE_BENTLEY_DGNPLATFORM
USING_NAMESPACE_BENTLEY_ECOBJECT

BEGIN_BENTLEY_NAMESPACE
namespace RealDwg {

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/21
+===============+===============+===============+===============+===============+======*/
struct ViewportDrawnCellFactory : NonCopyableClass
{
private:
    ConvertToDgnContextR    m_toDgnContext;
    AcDbObjectIdArray       m_masterFileViewports;
    AcDbDatabaseP   m_masterDwgFile;
    DgnFileP        m_masterDgnFile;
    AcDbEntityP     m_sourceEntity;
    AcGeVector3d    m_savedViewDirection;
    AcGeVector3d    m_savedCameraUp;
    AcDbObjectId    m_savedVisualStyleId;
    bool    m_tilemodeChanged;
    UInt32  m_drawnCellCount;

public:
    // the constructor
    ViewportDrawnCellFactory(AcDbEntityP entity, AcDbObjectIdArray const& viewportIds, ConvertToDgnContextR context)
        : m_toDgnContext(context), m_masterFileViewports(viewportIds), m_sourceEntity(entity)
        {
        m_savedViewDirection = context.GetCustomObjectViewDirection ();
        m_savedCameraUp = context.GetCustomObjectCameraUp ();
        m_savedVisualStyleId = context.GetCustomObjectVisualStyle ();
        m_tilemodeChanged = false;
        m_drawnCellCount = 0;
        if (!DwgPlatformHost::Instance()._GetMasterDwgFile(m_masterDwgFile, &m_masterDgnFile))
            {
            m_masterDwgFile = nullptr;
            m_masterDgnFile = nullptr;
            }
        }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          11/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus GetViewParamsFromViewport (AcGeVector3d& viewDirection, double& viewTwist, AcDbObjectId& visualstyleId, AcDbObjectId viewportId)
    {
    if (viewportId.objectClass() == AcDbViewport::desc())
        {
        AcDbViewportPointer viewportEntity(viewportId, AcDb::kForRead);
        if (Acad::eOk == viewportEntity.openStatus())
            {
            viewDirection = viewportEntity->viewDirection ();
            viewTwist = viewportEntity->twistAngle ();
            visualstyleId = viewportEntity->visualStyle ();
            return  RealDwgSuccess;
            }
        }
    else if (viewportId.objectClass() == AcDbViewportTableRecord::desc())
        {
        AcDbViewportTableRecordPointer vptableRecord(viewportId, AcDb::kForRead);
        if (Acad::eOk == vptableRecord.openStatus())
            {
            viewDirection = vptableRecord->viewDirection ();
            viewTwist = vptableRecord->viewTwist ();
            visualstyleId = vptableRecord->visualStyle ();
            return  RealDwgSuccess;
            }
        }
    return  RealDwgStatus::NotApplicable;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          11/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus SetDrawParamsFromViewport (AcDbObjectId viewportId)
    {
    double  viewTwist = 0.0;
    AcGeVector3d    viewDirection;
    AcDbObjectId    visualstyleId;

    auto status = this->GetViewParamsFromViewport (viewDirection, viewTwist, visualstyleId, viewportId);
    if (RealDwgSuccess == status)
        {
        RotMatrix   viewMatrix;
        RealDwgUtil::RotMatrixFromArbitraryGeAxis (viewMatrix, viewDirection);

        RotMatrix   twistMatrix = RotMatrix::FromAxisAndRotationAngle (2, -viewTwist);
        viewMatrix.InitProduct (viewMatrix, twistMatrix);

        DVec3d  yAxis;
        viewMatrix.GetColumn (yAxis, 1);
        AcGeVector3d cameraUp = RealDwgUtil::GeVector3dFromDVec3d (yAxis);

        m_toDgnContext.SetCustomObjectViewDirection (viewDirection);
        m_toDgnContext.SetCustomObjectCameraUp (cameraUp);
        m_toDgnContext.SetCustomObjectVisualStyle (visualstyleId);

        m_tilemodeChanged = false;
        if (nullptr != m_masterDwgFile)
            {
            // change to master file paperspace such that C3D object enablers will apply viewport's effective annotation scale
            if (m_masterDwgFile->tilemode() && Acad::eOk == m_masterDwgFile->setTilemode(false))
                m_tilemodeChanged = true;
            }
        }
    return  status;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          11/21
+---------------+---------------+---------------+---------------+---------------+------*/
void RestoreDrawParams ()
    {
    m_toDgnContext.SetCustomObjectViewDirection (m_savedViewDirection);
    m_toDgnContext.SetCustomObjectCameraUp (m_savedCameraUp);
    m_toDgnContext.SetCustomObjectVisualStyle (m_savedVisualStyleId);
    if (m_tilemodeChanged)
        {
        if (nullptr != m_masterDwgFile)
            m_masterDwgFile->setTilemode (true);
        m_tilemodeChanged = false;
        }
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus GetMasterDgnFileId (FileIdentity& fileId, bool xrefEntityOnly) const
    {
    ::memset(&fileId, 0, sizeof(fileId));

    if (nullptr == m_masterDwgFile || nullptr == m_masterDgnFile || nullptr == m_sourceEntity)
        return  RealDwgStatus::FileNotFound;

    // if caller wants xref entity only, check it now:
    auto entityDwg = m_sourceEntity->database ();
    if (xrefEntityOnly && m_masterDwgFile == entityDwg)
        return  RealDwgSuccess;

    // need master DGN file ID only if the source entity is in an xRef file:
    if (m_masterDwgFile != entityDwg)
        {
        auto dgnHeader = m_masterDgnFile->GetHeader ();
        if (nullptr == dgnHeader)
            return  RealDwgStatus::BadData;

        if (0 == dgnHeader->fileID.guid[0])
            {
            // DgnFile has not been set with a GUID
            RealDwgUtil::SetDgnFileGuidFromDwg (dgnHeader, m_masterDwgFile);
            }
        else
            {
            // the seed file already has fileID set, use it.
            fileId = dgnHeader->fileID;
            }
        }
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
bool NeedEmptyDrawnCell (AcDbObjectId viewportId) const
    {
    // if the entity layer is frozen in the viewport, but is globally turned on, will create an empty drawn cell to denote the same no display effect
    bool isLayerFrozenInViewport = false;
    AcDbObjectIdArray frozenLayers;
    AcDbViewportPointer viewport(viewportId, AcDb::kForRead);
    if (Acad::eOk == viewport.openStatus() && Acad::eOk == viewport->getFrozenLayerList(frozenLayers))
        {
        auto entityLayer = m_sourceEntity->layerId ();
        for (int i = 0; i < frozenLayers.length(); i++)
            {
            if (entityLayer == frozenLayers[i])
                {
                isLayerFrozenInViewport = true;
                break;
                }
            }
        }
    if (isLayerFrozenInViewport)
        {
        AcDbLayerTableRecordPointer layer(m_sourceEntity->layerId(), AcDb::kForRead);
        if (Acad::eOk == layer.openStatus() && !layer->isOff() && !layer->isFrozen())
            return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus DrawEntityThroughViewportEntity (EditElementHandleR eeh, AcDbObjectId viewportId)
    {
    // viewportDraw the source entity through the input layout(sheet model) viewport. First get the master DGN file ID, if the source entity is in an xRef file:
    FileIdentity masterFileId;
    auto status = this->GetMasterDgnFileId (masterFileId, false);
    if (RealDwgSuccess != status)
        return  status;

    // set the custom object options for this viewport
    this->SetDrawParamsFromViewport (viewportId);

    // get the DgnAttachment ID that has been or will be converted from the viewport entity in the master file
    auto viewportElementId = m_toDgnContext.ElementIdFromObjectId (viewportId);

    // create a snapshot cell by drawing the source entity in the input papserspace viewport
    EditElementHandle drawnEeh;
    if (!this->NeedEmptyDrawnCell(viewportId))
        status = m_toDgnContext.WorldDrawToElements (m_sourceEntity, drawnEeh, viewportId);

    if (status == RealDwgSuccess)
        {
        auto elmdscr = drawnEeh.GetElementDescrP ();
        if (nullptr != elmdscr)
            elmdscr->Validate (drawnEeh.GetModelRef());
        // an invalid cell tells the handler that the cell is not displayed in the viewport
        ViewportDrawnCellHandler::SetViewportDrawnCell (eeh, drawnEeh, -1, viewportElementId, masterFileId, ++m_drawnCellCount);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus DrawEntityThroughVPortTableRecord (EditElementHandleR eeh, AcDbObjectId layoutId)
    {
    // worldDraw/viewportDraw the source entity, which is in an xRef, through modelspace viewports
    FileIdentity masterFileId;
    auto status = this->GetMasterDgnFileId (masterFileId, true);
    if (RealDwgSuccess != status)
        return  status;

    // get the ViewGroup ID that has been or will be converted from the modelspace layout in the master file
    auto viewgroupElementId = m_toDgnContext.ElementIdFromObjectId (layoutId);

    // create a snapshot cell by drawing the xRef entity in all viewport table records
    AcDbViewportTablePointer vportTable(m_masterDwgFile->viewportTableId(), AcDb::kForRead);
    if (Acad::eOk != vportTable.openStatus())
        return  CantOpenObject;

    AcDbViewportTableIterator* iter = nullptr;
    if (Acad::eOk != vportTable->newIterator(iter))
        return  OutOfMemoryError;

    // follow SetDgnViewgroupFromModelspaceVPortTable to have the same view index sequence:
    int viewNumber = 0;
    for (iter->start(); !iter->done(); iter->step())
        {
        AcDbObjectId    viewportId;
        if (Acad::eOk != iter->getRecordId (viewportId))
            continue;
        AcDbViewportTableRecordPointer  vptableRecord(viewportId, AcDb::kForRead);
        if (Acad::eOk != vptableRecord.openStatus())
            continue;
        AcString    name;
        if (Acad::eOk != vptableRecord->getName(name) || !acdbSymUtil()->isViewportActiveName(name.kwszPtr()))
            continue;

        // set the custom object options for this viewport
        this->SetDrawParamsFromViewport (viewportId);

        MSElementDescrP elmdscr;
        EditElementHandle drawnEeh;

        // now create a snapshot cell by drawing the source entity in this modelspace viewport:
        status = m_toDgnContext.WorldDrawToElements (m_sourceEntity, drawnEeh, viewportId);
        if (status == RealDwgSuccess && nullptr != (elmdscr = drawnEeh.GetElementDescrP()))
            {
            elmdscr->Validate (drawnEeh.GetModelRef());
            ViewportDrawnCellHandler::SetViewportDrawnCell (eeh, drawnEeh, viewNumber, viewgroupElementId, masterFileId, ++m_drawnCellCount);
            }
        viewNumber++;
        }
    delete iter;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus ToElement (EditElementHandleR eeh)
    {
    // first, create the base/host cell from drawing the entity through the default world/viewport draw
    auto status = m_toDgnContext.WorldDrawToElements (m_sourceEntity, eeh);
    if (status == RealDwgStatus::RealDwgSuccess)
        {
        // then create cells by drawing the entity in all collected viewports
        for (int i = 0; i < m_masterFileViewports.length(); i++)
            {
            if (m_masterFileViewports[i].objectClass() == AcDbLayout::desc())
                this->DrawEntityThroughVPortTableRecord (eeh, m_masterFileViewports[i]);
            else
                this->DrawEntityThroughViewportEntity (eeh, m_masterFileViewports[i]);
            }
        this->RestoreDrawParams ();
        }
    return  status;
    }

};  // ViewportDrawnCellFactory

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                    Ashrafali.Jahagirdar      02/22
+---------------+---------------+---------------+---------------+---------------+------*/
bool ConvertToDgnContext::IsEntityViewportDependent (AcDbEntityCP entity) const
   {
   if (nullptr == entity)
       return false;

   AcString arxName (entity->isA ()->name ());

   if (0 == arxName.compareNoCase (L"AeccDbAlignmentStationLabeling"))
       return true;

   if (0 == arxName.compareNoCase (L"AeccDbSurfaceContourLabeling"))
       return true;

   if (0 == arxName.compareNoCase (L"AeccDbStaOffsetLabel"))
       return true;

   return false;
   }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          10/21
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus ConvertToDgnContext::DrawViewportDependentEntity (AcDbEntityP entity, EditElementHandleR eeh)
    {
    // honor user request if he does not want to see elements behave differently across different views and ref attachments
    if (m_skipViewportDrawnCellCreation || nullptr == entity || nullptr == entity->isA())
        return  RealDwgStatus::NotApplicable;

    // only selected entity types will go through this process
    if (!IsEntityViewportDependent (entity))
        return  RealDwgStatus::NotApplicable;

    // only if creating elements from modelspace entities
    auto dgnFile = this->GetFile ();
    auto modelIndex = this->GetModelIndexItem ();
    if (nullptr == dgnFile || nullptr == modelIndex || modelIndex->GetRealDwgModelType() != RDWGMODEL_TYPE_DefaultModel)
        return  RealDwgStatus::NotApplicable;
    
    // only if the viewports in the master file have been cached
    AcDbObjectIdArray   objectIds;
    if (DwgPlatformHost::Instance()._GetMasterFileObjectIdsInCache(objectIds) <= 0)
        return  RealDwgStatus::NotApplicable;
    
    // try drawing the entity through all collected viewports
    Bentley::RealDwg::ViewportDrawnCellFactory factory(entity, objectIds, *this);
    return factory.ToElement(eeh);
    }


}   // end RealDwg namespace
END_BENTLEY_NAMESPACE

