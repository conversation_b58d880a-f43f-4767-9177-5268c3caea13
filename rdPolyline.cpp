/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdPolyline.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     NeedToFitPolylineAsCurve (AcDbEntityCP acEntity)
    {
    // fit polyline to curve only when PLINEGEN is turned on:
    AcDbPolyline*       lwPolyline = AcDbPolyline::cast (acEntity);
    AcDb2dPolyline*     polyline2d = AcDb2dPolyline::cast (acEntity);
    if ((nullptr != lwPolyline && !lwPolyline->hasPlinegen()) || (nullptr != polyline2d && !polyline2d->isLinetypeGenerationOn()))
        return  false;

    AcDbDatabase*   dwg = acEntity->database ();
    AcDbObjectId    ltypeId = acEntity->linetypeId ();
    if (ltypeId == acdbSymUtil()->linetypeContinuousId(dwg))
        return  false;

    if (ltypeId == acdbSymUtil()->linetypeByBlockId(dwg))
        {
        AcDbObjectId    ownerId = acEntity->ownerId ();
        return  ownerId != acdbSymUtil()->blockModelSpaceId(dwg) && ownerId != acdbSymUtil()->blockPaperSpaceId(dwg);
        }

    if (ltypeId == acdbSymUtil()->linetypeByLayerId(dwg))
        {
        AcDbLayerTableRecordPointer layer(acEntity->layerId(), AcDb::kForRead);
        if (Acad::eOk == layer.openStatus())
            return layer->linetypeObjectId() != acdbSymUtil()->linetypeContinuousId(dwg);
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CreateElementFromCurveFitPolyline
(
EditElementHandleR  eeh,
DPoint3dArrayR      fitPoints,
AcGeVector3dCR      normal,
double              elevation,
double              lineWidth,
double              thickness,
AcDbEntityCP        acEntity
)
    {
    int             nPoints = static_cast <int> (fitPoints.size());
    if (nPoints < 3)
        return  NotApplicable;

    MSBsplineCurve  bCurve;
    StatusInt       status = bCurve.InitFromInterpolatePoints (&fitPoints[0], nPoints, 2, false, nullptr, nullptr, true, 3);

    if (BSISUCCESS == status)
        {
        MSInterpolationCurve    interpolationCurve;
        memset (&interpolationCurve, 0, sizeof(interpolationCurve));

        interpolationCurve.params.order = 3;
        interpolationCurve.params.isPeriodic = 0;
        interpolationCurve.params.isChordLenKnots = 1;
        interpolationCurve.params.isColinearTangents = 0;
        interpolationCurve.params.isChordLenTangents = 1;
        interpolationCurve.params.isNaturalTangents = 1;
        interpolationCurve.params.numPoints = nPoints;
        interpolationCurve.params.numKnots = BsplineParam::NumberAllocatedKnots (bCurve.params.numPoles, bCurve.params.order, false);

        interpolationCurve.fitPoints = &fitPoints[0];
        interpolationCurve.knots = bCurve.knots;
        interpolationCurve.display.curveDisplay = true;

        Transform   extrusion, composite;
        if (nullptr != RealDwgUtil::GetExtrusionTransform(extrusion, normal, elevation))
            composite.InitProduct (this->GetTransformToDGN(), extrusion);
        else
            composite = this->GetTransformToDGN ();

        composite.Multiply (interpolationCurve.fitPoints, nPoints);

        status = BSplineCurveHandler::CreateBSplineCurveElement (eeh, nullptr, interpolationCurve, this->GetThreeD(), *this->GetModel());

        bCurve.ReleaseMem ();
        }

    if (BSISUCCESS == status && eeh.IsValid())
        {
        this->ElementHeaderFromEntity (eeh, acEntity);

        if (fabs(lineWidth) > TOLERANCE_ZeroSize)
            this->SetLineWidth (eeh, lineWidth, lineWidth, &normal);

        if (fabs(thickness) > TOLERANCE_ZeroSize)
            this->ApplyThickness (eeh, thickness, normal, false);
        }

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/15
+---------------+---------------+---------------+---------------+---------------+------*/
static void     UpdateCurveFitPoints (DPoint3dArrayR fitPoints, DPoint2dCR newPoint, double newBulge, DPoint3dR priorPoint, double& priorBulge)
    {
    DPoint3d    point3d = DPoint3d::From (newPoint);

    // apply bulging factor by inserting a mid-arc point:
    if (fabs(priorBulge) > TOLERANCE_BulgeFactor)
        fitPoints.push_back (RealDwgUtil::ComputeBulgePoint(priorBulge, priorPoint, point3d));

    fitPoints.push_back (point3d);

    // set or remove this bulge for next point
    priorBulge = fabs(newBulge) > TOLERANCE_BulgeFactor ? newBulge : 0.0;
    // save this point before moving on to the next
    priorPoint = point3d;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromPolylineEntity
(
EditElementHandleR          eeh,
const AcDbPolyline*         pPolyline,
TransformCR                 transformToDGN,
bool                        makeLineString
)
    {
    int                     nVertices = pPolyline->numVerts();
    MSElementDescrP         pDescr = NULL;
    DPoint2dArray           points;
    DPoint2dArray           widths;
    DoubleArray             bulges;
    DPoint3dArray           fitPoints;

    double                  lineWidth;
    bool                    hasBulges       = (Adesk::kTrue == pPolyline->hasBulges());
    bool                    constantWidth   = (Acad::eOk == pPolyline->getConstantWidth (lineWidth));
    bool                    hasWidths       = !constantWidth && (Adesk::kTrue == pPolyline->hasWidth());

    // complex chain does not apply line style param Non-Segment Mode across arc segments - fit it as a curve. TFS 8061.
    bool        fitAsCurve = hasBulges && constantWidth && nVertices > 2 && this->IsPlinegenAsCurve() && NeedToFitPolylineAsCurve(pPolyline);
    double      priorBulge = 0.0;
    DPoint3d    priorPoint;

    for (int iVert = 0; iVert < nVertices; iVert++)
        {
        AcGePoint2d     gePoint;
        DPoint2d        dPoint;

        pPolyline->getPointAt (iVert, gePoint);
        points.push_back (RealDwgUtil::DPoint2dFromGePoint2d (dPoint, gePoint));

        double          bulge = 0.0;
        if (hasBulges)
            {
            pPolyline->getBulgeAt (iVert, bulge);
            bulges.push_back (bulge);
            }

        if (fitAsCurve)
            UpdateCurveFitPoints (fitPoints, dPoint, bulge, priorPoint, priorBulge);

        if (hasWidths)
            {
            pPolyline->getWidthsAt (iVert, dPoint.x, dPoint.y);
            widths.push_back (dPoint);
            }
        }

    // add end condition to fit a closed curve
    if (fitAsCurve && pPolyline->isClosed())
        {
        if (fabs(priorBulge) > TOLERANCE_BulgeFactor)
            fitPoints.push_back (RealDwgUtil::ComputeBulgePoint(priorBulge, priorPoint, fitPoints[0]));
        fitPoints.push_back (fitPoints[0]);
        }

    RealDwgStatus   status = NotApplicable;

    if (fitAsCurve)
        status = this->CreateElementFromCurveFitPolyline (eeh, fitPoints, 
                            pPolyline->normal(),
                            pPolyline->elevation(),
                            lineWidth,
                            pPolyline->thickness(),
                            pPolyline);

    if (RealDwgSuccess == status)
        return  status;

    if (RealDwgSuccess == (status = this->CreateElementFromPolylineParams (eeh, points, hasBulges ? &bulges : NULL, hasWidths ? &widths : NULL, (Adesk::kTrue == pPolyline->isClosed()),
                                        makeLineString, pPolyline->normal(), pPolyline->elevation(), pPolyline->thickness(), pPolyline, transformToDGN)))
        {
        auto normal = pPolyline->normal ();
        if (constantWidth)
            this->SetLineWidth (eeh, lineWidth, lineWidth, &normal);

        if (pPolyline->hasPlinegen())
            this->AddLineStyleContinuousLinkage (eeh);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromPolylineParams
(
EditElementHandleR          eeh,
DPoint2dArrayR              vertices,
DoubleArrayP                bulges,
DPoint2dArrayP              widths,
bool                        closed,
bool                        makeLineString,
AcGeVector3d const&         normal,
double                      elevation,
double                      thickness,
AcDbEntityCP                pEntity,
TransformCR                 transformToDGN
)
    {
    size_t              nVertices   = vertices.size();
    size_t              nSegments   = closed ? nVertices : (nVertices - 1);

    if (nVertices < 2)
        return EntityError;

    if (nVertices < 3)      // Degenerate.
        closed = false;
    else if (!closed && RealDwgUtil::IsViewportClippingEntity (pEntity))
        closed = true;

    MSElementDescrP     pDescr      = NULL;
    MSElementDescrP     pLastChild  = NULL;
    DPoint2dP           pVertices   = &vertices.front();
    DPoint2dP           pWidths     = (NULL == widths) ? NULL : &widths->front();
    double*             pBulges     = (NULL == bulges) ? NULL : &bulges->front();

    Transform           extrusionTransform;
    Transform           compositeTransform;
    if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, normal, elevation))
        compositeTransform.InitProduct (transformToDGN, extrusionTransform);
    else
        compositeTransform = transformToDGN;

    if (NULL != widths)
        {
        // In this case, make a seperate element from each segment as we have to set separate start and end widths for each line.
        if (nSegments > 1)
            {
            ChainHeaderHandler::CreateChainHeaderElement (eeh, NULL, closed && !makeLineString, m_threeD, *m_model);
            pDescr = eeh.ExtractElementDescr ();
            }

        for (size_t iSegment = 0; iSegment < nSegments; iSegment++)
            {
            DPoint2d            p0 = pVertices[iSegment];
            DPoint2d            p1 = pVertices[(iSegment+1) % nVertices];
            MSElementDescrP     pChildDescr = NULL;
            EditElementHandle   childEeh;

            if (closed && p0.IsEqual (p1))
                continue;

            if (NULL != pBulges && fabs(pBulges[iSegment]) >= TOLERANCE_BulgeFactor && ! p0.isEqual (&p1))
                {
                this->CreateElementFromPolylineBulge (childEeh, p0, p1, pBulges[iSegment], compositeTransform);
                }
            else
                {
                DSegment3d        line;

                line.point[0].init (&p0);
                line.point[1].init (&p1);

                compositeTransform.Multiply (line.point, line.point, 2);
                this->ValidatePoints (line.point, 2);

                LineHandler::CreateLineElement (childEeh, NULL, line, m_threeD, *m_model);
                }

            this->SetLineWidth (childEeh, pWidths[iSegment].x, pWidths[iSegment].y, &normal);
            pChildDescr = childEeh.ExtractElementDescr();

            if (NULL == pDescr)
                pDescr = pChildDescr;
            else
                pDescr->AppendChild (&pLastChild, pChildDescr);
            }
        eeh.SetElementDescr (pDescr, true, false);
        }
    else if (NULL != bulges)
        {
        // has no widths, but does have bulges.
        DPoint3dArray   noBulgePointArray;

        ChainHeaderHandler::CreateChainHeaderElement (eeh, NULL, closed && !makeLineString, m_threeD, *m_model);
        pDescr = eeh.ExtractElementDescr ();
        if (NULL == pDescr)
            return  CantAccessMstnElement;

        for (size_t iSegment = 0; iSegment < nSegments; iSegment++)
            {
            DPoint2d            p0 = pVertices[iSegment];
            DPoint2d            p1 = pVertices[(iSegment+1) % nVertices];
            MSElementDescrP     pChildDescr = NULL;
            EditElementHandle   childEeh;

            if (closed && p0.IsEqual (p1))
                continue;

            if (fabs(pBulges[iSegment]) >= TOLERANCE_BulgeFactor && !p0.isEqual (&p1))
                {
                if (!noBulgePointArray.empty())
                    {
                    if (RealDwgSuccess == this->CreateElementFromVertices (childEeh, &noBulgePointArray.front(), noBulgePointArray.size(), false, compositeTransform))
                        {
                        pChildDescr = childEeh.ExtractElementDescr();
                        pDescr->AppendChild (&pLastChild, pChildDescr);
                        }

                    noBulgePointArray.clear();
                    }

                if (RealDwgSuccess == this->CreateElementFromPolylineBulge (childEeh, p0, p1, pBulges[iSegment], compositeTransform))
                    {
                    pChildDescr = childEeh.ExtractElementDescr();
                    pDescr->AppendChild (&pLastChild, pChildDescr);
                    }
                }
            else
                {
                // no bulge - put it into the noBulgeArray.
                DPoint3d    tmpPoint;
                if (noBulgePointArray.empty())
                    noBulgePointArray.push_back (RealDwgUtil::DPoint3dFromDPoint2d (tmpPoint, p0));

                noBulgePointArray.push_back (RealDwgUtil::DPoint3dFromDPoint2d (tmpPoint, p1));
                }

            if (noBulgePointArray.size() > (MAX_VERTICES-1))
                {
                // this will probably never happen, but we do not want a complex chain within a complex chain.
                if (RealDwgSuccess == this->CreateElementFromVertices (childEeh, &noBulgePointArray.front(), noBulgePointArray.size(), false, compositeTransform))
                    {
                    pChildDescr = childEeh.ExtractElementDescr();
                    pDescr->AppendChild (&pLastChild, pChildDescr);
                    }

                noBulgePointArray.clear();
                }

            }

        if (!noBulgePointArray.empty())
            {
            EditElementHandle childEeh;
            if (RealDwgSuccess == this->CreateElementFromVertices (childEeh, &noBulgePointArray.front(), noBulgePointArray.size(), false, compositeTransform))
                {
                MSElementDescrP pChildDescr = childEeh.ExtractElementDescr();
                pDescr->AppendChild (&pLastChild, pChildDescr);
                }

            noBulgePointArray.clear();
            }
        eeh.SetElementDescr (pDescr, true, false);
        }
    else
        {
        // no bulges and no widths.
        DPoint3d                tmpPoint;
        DPoint3dArray           tmpPointArray;

        // put all points into the array.
        for (size_t iVertex = 0; iVertex < nVertices; iVertex++, pVertices++)
            tmpPointArray.push_back (RealDwgUtil::DPoint3dFromDPoint2d (tmpPoint, *pVertices));

        // if closed, make sure last point is the same.
        if (closed)
            {
            DPoint3d        startPoint = *tmpPointArray.begin();
            if (!startPoint.IsEqual (tmpPointArray[nVertices - 1]))
                tmpPointArray.push_back (startPoint);
            }

        this->CreateElementFromVertices (eeh, &tmpPointArray.front(), tmpPointArray.size(), closed && !makeLineString, compositeTransform);
        }
    this->ApplyThickness (eeh, thickness, normal, false);

    if (NULL != pEntity)
        this->ElementHeaderFromEntity (eeh, pEntity);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromPolylineBulge
(
EditElementHandleR          eeh,
DPoint2dCR                  start,
DPoint2dCR                  end,
double                      bulge,
TransformCR                 transform
)
    {
    DEllipse3d dEllipse;
    RealDwgUtil::BulgeFactorToDEllipse3d (dEllipse, start, end, bulge);
    return this->CreateElementFromDEllipse (eeh, dEllipse, transform);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromVertices
(
EditElementHandleR          eeh,
DPoint3dP                   points,
size_t                      numPoints,
bool                        closed,
TransformCR                 transform
)
    {
    if ( (0 == numPoints) || (NULL == points) )
        return EntityError;

    transform.Multiply (points, points, (UInt32) numPoints);

    this->ValidatePoints (points, numPoints);

    return  this->CreateElementFromVertices (eeh, points, numPoints, closed);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromVertices
(
EditElementHandleR          eeh,
DPoint3dP                   points,
size_t                      numPoints,
bool                        closed
)
    {
    if (0 == numPoints || NULL == points)
        return CantExtractPoints;

    StatusInt   status = BSIERROR;

    if (numPoints > MAX_VERTICES)
        {
        ChainHeaderHandler::CreateChainHeaderElement (eeh, NULL, closed, m_threeD, *m_model);
        while (numPoints > 1)
            {
            size_t pointsThisLineString = (numPoints >= MAX_VERTICES) ? MAX_VERTICES : numPoints;

            EditElementHandle  component;
            status = LineStringHandler::CreateLineStringElement (component, NULL, points, pointsThisLineString, m_threeD, *m_model);
            if (BSISUCCESS != status)
                break;

            ChainHeaderHandler::AddComponentElement (eeh, component);

            // the last point in a line string is the first point in the next line string.
            numPoints -= (pointsThisLineString-1);
            points    += (pointsThisLineString-1);
            }
        }
    else if (numPoints < 3)
        {
        DSegment3d      line = DSegment3d::From (points[0], numPoints < 2 ? points[0] : points[1]);
        status = LineHandler::CreateLineElement (eeh, NULL, line, m_threeD, *m_model);
        }
    else
        {
        if (closed)
            status = ShapeHandler::CreateShapeElement (eeh, NULL, points, numPoints, m_threeD, *m_model);
        else
            status = LineStringHandler::CreateLineStringElement (eeh, NULL, points, numPoints, m_threeD, *m_model);
        }

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPolyline : public ToDgnExtension, SetFromPolylineInfo
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    return context.CreateElementFromPolylineEntity (outElement, AcDbPolyline::cast (acObject), context.GetTransformToDGN(), false);
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
* This method is called from the ToDwgExtension::ToObject methods of several elements.
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           VerticesFromPolylineInfo (AcDbObjectP acObject, PolylineInfoCR polylineInfo, ElementHandleR sourceElement, ConvertFromDgnContext& context) const override
    {
    size_t          numPoints = polylineInfo.GetPointCount();

    if (numPoints < 2)
        return InvalidPolygonVertexCount;

    AcDbPolyline*   acPolyline = AcDbPolyline::cast (acObject);
    if (NULL == acPolyline)
        return  NullObject;

    DPoint3dCP      points = polylineInfo.FirstPoint();
    DPoint2dCP      widths = polylineInfo.FirstWidth();
    double const*   bulges = polylineInfo.FirstBulge();
    bool            closed = polylineInfo.IsClosed();

    // remove the closure point
    if (numPoints > 2 && closed && points[0].IsEqual(points[numPoints-1], TOLERANCE_UORPointEqual))
        numPoints--;

    double  startWidth      = 0.0;
    double  endWidth        = 0.0;
    bool    constantWidth   = false;
    bool    hasWidth        = false;
    double  widthIncrement  = 0.0;
    if (NULL == widths)
        {
        startWidth      = polylineInfo.GetStartWidth();
        endWidth        = polylineInfo.GetEndWidth();
        if (! (constantWidth = (startWidth == endWidth)))
            widthIncrement = (endWidth - startWidth) / (numPoints - 1);
        hasWidth        = (startWidth != 0.0) || (endWidth != 0.0);
        }

    // clear out old points.
    /*----------------------------------------------------------------------------------------------------
    According to RealDWG Document, AcDbPolyline::reset can optionally preserve numPoints vertices for us,
    but unfortunately that is not true.  Autodesk has responded us with their investigation:
    
    Looking at the code for this method, the docs are wrong.  The way reset() is implemented, if num_verts 
    is not the same as the actual number of vertices in the polyline, then the result is the same as if 
    reuse == false.  So, reuse and num_verts can only be used to actually preserve all of the vertices not 
    just some of them.  AND, in that case, it's only the vertex coordinate information that's preserved - 
    all bulge and segment width information will always be wiped out by reset().  But, if reuse is true, 
    then overall polyline information (i.e. elevation, thickness, normal vector, and constant width) will 
    be retained.

    Also, for AcDbPolyline, there are no objectIds for vertices.  In an AcDbPolyline, the vertices are simply 
    point coordinates in an array in the AcDbPolyline's data.  It's the old style AcDb2dPolyline that has 
    vertices that are themselves actual AcDbVertices and have objectIds.

    Based on our investigation, if we ever called reset with reuse=true, setPointAt would always fail with 
    an error eInvalidIndex.  But if we do not need to change number of vertices, we don't bother to call reset 
    - we simply update each and every one of these vertices since there is no vertice object ID involved.
    ----------------------------------------------------------------------------------------------------*/
    bool            reusePoints = acPolyline->numVerts() == numPoints;
    if (!reusePoints)
        acPolyline->reset (false, 0);

    Acad::ErrorStatus   eStatus;
    double              width = startWidth; 
    for (size_t iPoint=0; iPoint < numPoints; iPoint++)
        {
        if (reusePoints)
            eStatus = acPolyline->setPointAt ((unsigned int) iPoint, RealDwgUtil::GePoint2dFromDPoint3d (points[iPoint]));
        else
            eStatus = acPolyline->addVertexAt ((unsigned int) iPoint, RealDwgUtil::GePoint2dFromDPoint3d (points[iPoint]));
        if (Acad::eOk != eStatus)
            {
            DIAGNOSTIC_PRINTF ("Error adding polyline vertex at %d from ID=%I64d! [%ls]\n", iPoint, sourceElement.GetElementCP()->ehdr.uniqueId, acadErrorStatusText(eStatus));
            continue;
            }
        if (NULL != bulges)
            acPolyline->setBulgeAt ((unsigned int) iPoint, bulges[iPoint]);

        if (NULL != widths)
            acPolyline->setWidthsAt ((unsigned int) iPoint, widths[iPoint].x, widths[iPoint].y);
        else if (hasWidth && !constantWidth)
            {
            double nextWidth = width + widthIncrement;
            acPolyline->setWidthsAt ((unsigned int) iPoint, width, nextWidth);
            width = nextWidth;
            }
        }

    if (constantWidth)
        acPolyline->setConstantWidth (startWidth);

    acPolyline->setClosed (polylineInfo.IsClosed());

    return RealDwgSuccess;
    }
};


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SetAcDbPolylineFromElement
(
AcDbPolyline*           acPolyline,
ElementHandleR          inElement
)
    {
    if (NULL == acPolyline)
        return  NullObject;

    // get default normal.
    DPoint3d            defaultNormal;
    RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, acPolyline->normal());

    EditElementHandle   newElement (inElement, false);

    double              extrusionDistance = 0.0, elevation = 0.0;
    DPoint3d            normal, headerNormal;
    Transform           compositeTransform;
    if (RealDwgSuccess != this->ExtractPolylineData(extrusionDistance, compositeTransform, normal, headerNormal, elevation, newElement, defaultNormal))
        return  CantCreatePolyline;

    bool                closed, hasConstantWidth, continueLt;
    double              constantWidth = 0.0;
    DoubleArray         bulges;
    DPoint3dArray       points;
    DPoint2dArray       widths;

    // Remove existing vertices
    acPolyline->reset (false, 0);

    RealDwgStatus status = this->ExtractLWPolylineFromElement (points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt, newElement, normal, &compositeTransform, TOLERANCE_UORPointEqual, false);
    if (RealDwgSuccess == status)
        {
        size_t          nPoints = points.size ();

        if (nPoints > 1)
            {
            size_t      nBulges = bulges.size();
            size_t      nWidths = widths.size();

            acPolyline->reset (true, (unsigned int)nPoints);
            UInt32      nOldPoints = acPolyline->numVerts ();

            bool        hasBulges = nBulges > 0, hasWidths = nWidths > 0;
            for (UInt32 i = 0; i < nPoints; i++)
                {
                if (this->GetSettings().IsZeroZCoordinateEnforced())
                    points[i].z = 0.0;

                if (i >= nOldPoints)
                    acPolyline->addVertexAt (i, RealDwgUtil::GePoint2dFromDPoint3d(points[i]));
                else
                    acPolyline->setPointAt (i, RealDwgUtil::GePoint2dFromDPoint3d (points[i]));
                if (hasBulges)
                    acPolyline->setBulgeAt (i, bulges[i]);
                if (hasWidths)
                    acPolyline->setWidthsAt (i, widths[i].x, widths[i].y);
                }

            acPolyline->setClosed (closed ? true : false);
            if (hasConstantWidth)
                acPolyline->setConstantWidth (constantWidth);

            acPolyline->setPlinegen (continueLt ? true : false);
            }
        else
            {
            // do not create a polyline with less than 2 vertices
            status = CantCreatePolyline;
            }
        }
    // RealDWG must be reseting elevation when setting vertices, so do this after all points being set.
    acPolyline->setElevation (elevation);
    acPolyline->setThickness (extrusionDistance);
    acPolyline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (headerNormal));

    return  status;
    }

