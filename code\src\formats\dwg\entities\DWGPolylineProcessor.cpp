#include "DWGPolylineProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbpline.h>
#include <realdwg/base/db2dpolyline.h>
#include <realdwg/base/db3dpolyline.h>
#include <realdwg/base/dbpolygonmesh.h>
#include <realdwg/base/dbpolyface.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// PolylineGeometry Implementation
//=======================================================================================

bool PolylineGeometry::IsValid() const {
    return HasValidVertices() && vertices.size() >= 2;
}

bool PolylineGeometry::HasValidVertices() const {
    for (const auto& vertex : vertices) {
        if (!std::isfinite(vertex.position.x) || 
            !std::isfinite(vertex.position.y) || 
            !std::isfinite(vertex.position.z)) {
            return false;
        }
    }
    return true;
}

bool PolylineGeometry::IsSimplePolyline() const {
    return !IsPolygonMesh() && !IsPolyfaceMesh();
}

bool PolylineGeometry::IsPolygonMesh() const {
    return meshM > 0 && meshN > 0 && !isPolyfaceMesh;
}

bool PolylineGeometry::IsPolyfaceMesh() const {
    return isPolyfaceMesh;
}

//=======================================================================================
// DWG Polyline Processor Implementation
//=======================================================================================

DWGPolylineProcessor::DWGPolylineProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_vertexTolerance(1e-10)
    , m_bulgeTolerance(1e-10)
    , m_widthTolerance(1e-10)
    , m_simplificationTolerance(1e-6)
    , m_maxVertices(32767)
    , m_enableAutoRepair(true)
    , m_enableSimplification(true)
    , m_enableOptimization(true)
{
}

DWGProcessingStatus DWGPolylineProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // Extract polyline geometry from element
        // This is a placeholder - real implementation would extract from iModel
        PolylineGeometry geometry;
        
        // Sample polyline data
        geometry.vertices = {
            {{0, 0, 0}, 0.0, 0.0, 0.0},
            {{100, 0, 0}, 0.5, 0.0, 0.0},  // Arc segment with bulge
            {{100, 100, 0}, 0.0, 0.0, 0.0},
            {{0, 100, 0}, 0.0, 0.0, 0.0}
        };
        geometry.isClosed = true;
        geometry.is3D = false;
        geometry.hasArcs = true;
        geometry.constantWidth = 0.0;
        geometry.elevation = 0.0;
        geometry.normal = Vector3d(0, 0, 1);
        
        std::string layer = "Polylines";
        
        // Validate and process the polyline
        auto validation = ValidatePolylineGeometry(geometry);
        if (!validation.isValid) {
            LogError("Polyline geometry validation failed for element " + element.id);
            for (const auto& error : validation.errors) {
                LogError("  " + error);
            }
            
            // Attempt repair if enabled
            if (m_enableAutoRepair) {
                LogInfo("Attempting to repair polyline geometry");
                if (RepairPolylineGeometry(geometry)) {
                    LogInfo("Polyline geometry repaired successfully");
                    m_repairedPolylines++;
                } else {
                    LogError("Failed to repair polyline geometry");
                    return DWGProcessingStatus::ValidationError;
                }
            } else {
                return DWGProcessingStatus::ValidationError;
            }
        }
        
        // Process the polyline
        auto status = ProcessPolyline(geometry, layer);
        
        if (status == DWGProcessingStatus::Success) {
            m_processedCount++;
            m_processedPolylines++;
            LogInfo("Successfully processed polyline element " + element.id);
        }
        
        return status;
    }
    catch (const std::exception& e) {
        LogError("Exception processing polyline element " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGPolylineProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a polyline-type element
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGPolylineProcessor::ProcessPolyline(const PolylineGeometry& geometry, const std::string& layer) {
    // Create a copy for processing
    PolylineGeometry processedGeometry = geometry;
    
    // Transform geometry
    TransformPolylineGeometry(processedGeometry);
    
    // Validate the transformed geometry
    auto validation = ValidatePolylineGeometry(processedGeometry);
    if (!validation.isValid) {
        LogError("Invalid polyline geometry after transformation");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Optimize if enabled
    if (m_enableOptimization) {
        OptimizeVertices(processedGeometry.vertices);
    }
    
    // Simplify if enabled and needed
    if (m_enableSimplification && processedGeometry.vertices.size() > 100) {
        LogInfo("Simplifying polyline with " + std::to_string(processedGeometry.vertices.size()) + " vertices");
        if (SimplifyPolyline(processedGeometry, m_simplificationTolerance)) {
            m_simplifiedPolylines++;
            LogInfo("Simplified to " + std::to_string(processedGeometry.vertices.size()) + " vertices");
        }
    }
    
    // Determine polyline type and process accordingly
    if (processedGeometry.IsPolygonMesh()) {
        return ProcessPolygonMesh(processedGeometry, layer);
    } else if (processedGeometry.IsPolyfaceMesh()) {
        return ProcessPolyfaceMesh(processedGeometry, layer);
    } else if (processedGeometry.is3D) {
        return Process3DPolyline(processedGeometry, layer);
    } else {
        return Process2DPolyline(processedGeometry, layer);
    }
}

DWGProcessingStatus DWGPolylineProcessor::Process2DPolyline(const PolylineGeometry& geometry, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    try {
        // Create DWG polyline entity
        AcDbPolyline* polyline = CreateDWGPolyline(geometry);
        if (!polyline) {
            LogError("Failed to create DWG polyline entity");
            return DWGProcessingStatus::Failed;
        }
        
        // Set properties
        if (!SetEntityProperties(polyline, layer)) {
            delete polyline;
            LogError("Failed to set polyline properties");
            return DWGProcessingStatus::Failed;
        }
        
        // Add to model space
        if (!AddEntityToModelSpace(polyline)) {
            delete polyline;
            LogError("Failed to add polyline to model space");
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG polyline: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - polyline creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGPolylineProcessor::Process3DPolyline(const PolylineGeometry& geometry, const std::string& layer) {
#ifdef REALDWG_AVAILABLE
    try {
        AcDb3dPolyline* polyline = CreateDWG3DPolyline(geometry);
        if (!polyline) {
            LogError("Failed to create DWG 3D polyline entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(polyline, layer)) {
            delete polyline;
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(polyline)) {
            delete polyline;
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG 3D polyline: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - 3D polyline creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGPolylineProcessor::ProcessPolygonMesh(const PolylineGeometry& geometry, const std::string& layer) {
    // Validate mesh data
    if (!ValidateMeshData(geometry)) {
        LogError("Invalid polygon mesh data");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbPolygonMesh* mesh = CreateDWGPolygonMesh(geometry);
        if (!mesh) {
            LogError("Failed to create DWG polygon mesh entity");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(mesh, layer)) {
            delete mesh;
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(mesh)) {
            delete mesh;
            return DWGProcessingStatus::Failed;
        }
        
        m_convertedToMesh++;
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG polygon mesh: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - polygon mesh creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

//=======================================================================================
// Validation Methods
//=======================================================================================

PolylineValidationResult DWGPolylineProcessor::ValidatePolylineGeometry(const PolylineGeometry& geometry) const {
    PolylineValidationResult result;
    result.isValid = true;
    
    // Validate basic structure
    if (geometry.vertices.empty()) {
        result.AddPolylineError("No vertices found");
        return result;
    }
    
    if (geometry.vertices.size() < 2) {
        result.AddPolylineError("Insufficient vertices (minimum 2 required)");
        return result;
    }
    
    if (geometry.vertices.size() > m_maxVertices) {
        result.AddPolylineError("Too many vertices (maximum " + std::to_string(m_maxVertices) + ")");
        return result;
    }
    
    // Validate vertices
    result.hasValidVertices = ValidateVertices(geometry.vertices);
    if (!result.hasValidVertices) {
        result.AddPolylineError("Invalid vertex data");
    }
    
    // Validate bulges
    result.hasValidBulges = ValidateBulges(geometry.vertices);
    if (!result.hasValidBulges) {
        result.AddPolylineError("Invalid bulge values");
    }
    
    // Validate widths
    result.hasValidWidths = ValidateWidths(geometry.vertices);
    if (!result.hasValidWidths) {
        result.AddPolylineError("Invalid width values");
    }
    
    // Validate mesh data if applicable
    if (geometry.IsPolygonMesh() || geometry.IsPolyfaceMesh()) {
        result.hasValidMeshData = ValidateMeshData(geometry);
        if (!result.hasValidMeshData) {
            result.AddPolylineError("Invalid mesh data");
        }
    }
    
    // Calculate total length
    result.totalLength = CalculatePolylineLength(geometry);
    if (result.totalLength < m_vertexTolerance) {
        result.AddPolylineWarning("Polyline has very small total length");
    }
    
    // Check for self-intersections
    if (HasSelfIntersections(geometry)) {
        result.AddPolylineWarning("Polyline has self-intersections");
    }
    
    return result;
}

bool DWGPolylineProcessor::ValidateVertices(const std::vector<PolylineVertex>& vertices) const {
    int invalidCount = 0;
    
    for (const auto& vertex : vertices) {
        if (!ValidateVertexPosition(vertex.position)) {
            invalidCount++;
        }
    }
    
    // Allow some invalid vertices but not too many
    return invalidCount <= static_cast<int>(vertices.size() * 0.1);
}

bool DWGPolylineProcessor::ValidateBulges(const std::vector<PolylineVertex>& vertices) const {
    for (const auto& vertex : vertices) {
        if (!ValidateBulgeValue(vertex.bulge)) {
            return false;
        }
    }
    return true;
}

bool DWGPolylineProcessor::ValidateWidths(const std::vector<PolylineVertex>& vertices) const {
    for (const auto& vertex : vertices) {
        if (!ValidateVertexWidth(vertex.startWidth, vertex.endWidth)) {
            return false;
        }
    }
    return true;
}

bool DWGPolylineProcessor::ValidateMeshData(const PolylineGeometry& geometry) const {
    if (geometry.IsPolygonMesh()) {
        return ValidateMeshDimensions(geometry.meshM, geometry.meshN) &&
               ValidateMeshVertexCount(geometry.vertices, geometry.meshM, geometry.meshN);
    }
    return true;
}

//=======================================================================================
// Repair and Optimization Methods
//=======================================================================================

bool DWGPolylineProcessor::RepairPolylineGeometry(PolylineGeometry& geometry) const {
    bool repaired = false;
    
    // Fix vertices
    size_t originalVertexCount = geometry.vertices.size();
    ValidateAndFixVertices(geometry.vertices);
    if (geometry.vertices.size() != originalVertexCount) {
        repaired = true;
    }
    
    // Fix bulges
    if (ValidateAndFixBulges(geometry.vertices)) {
        repaired = true;
    }
    
    // Remove redundant vertices
    if (RemoveRedundantVertices(geometry.vertices, m_vertexTolerance)) {
        repaired = true;
    }
    
    // Normalize data
    NormalizePolylineData(geometry);
    
    return repaired;
}

bool DWGPolylineProcessor::SimplifyPolyline(PolylineGeometry& geometry, double tolerance) const {
    size_t originalCount = geometry.vertices.size();
    
    // Apply Douglas-Peucker simplification
    if (PolylineUtils::SimplifyDouglasPeucker(geometry.vertices, tolerance)) {
        LogInfo("Simplified polyline from " + std::to_string(originalCount) + 
                " to " + std::to_string(geometry.vertices.size()) + " vertices");
        return true;
    }
    
    return false;
}

bool DWGPolylineProcessor::OptimizeVertices(std::vector<PolylineVertex>& vertices) const {
    bool optimized = false;
    
    // Remove duplicate vertices
    if (PolylineUtils::RemoveDuplicateVertices(vertices, m_vertexTolerance)) {
        optimized = true;
    }
    
    // Merge collinear segments
    if (MergeCollinearSegments(vertices, m_vertexTolerance)) {
        optimized = true;
    }
    
    // Optimize arc segments
    if (SimplifyArcs(vertices, m_vertexTolerance)) {
        optimized = true;
    }
    
    return optimized;
}

//=======================================================================================
// Arc Processing Methods (Based on RealDwgFileIO)
//=======================================================================================

bool DWGPolylineProcessor::ProcessArcSegment(const PolylineVertex& start, const PolylineVertex& end, std::vector<Point3d>& arcPoints) const {
    if (!start.hasArc()) {
        return false;
    }
    
    // Calculate arc geometry
    Point3d center;
    double radius;
    double startAngle, endAngle;
    
    if (!PolylineUtils::CalculateArcGeometry(start.position, end.position, start.bulge,
                                           center, radius, startAngle, endAngle)) {
        return false;
    }
    
    // Tessellate arc
    arcPoints = PolylineUtils::TesselateArc(start.position, end.position, start.bulge, 16);
    m_processedArcs++;
    
    return !arcPoints.empty();
}

double DWGPolylineProcessor::CalculateArcRadius(const Point3d& start, const Point3d& end, double bulge) const {
    if (std::abs(bulge) < m_bulgeTolerance) {
        return 0.0;
    }
    
    double dx = end.x - start.x;
    double dy = end.y - start.y;
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (chordLength < m_vertexTolerance) {
        return 0.0;
    }
    
    return chordLength * (1.0 + bulge * bulge) / (4.0 * std::abs(bulge));
}

Point3d DWGPolylineProcessor::CalculateArcCenter(const Point3d& start, const Point3d& end, double bulge) const {
    if (std::abs(bulge) < m_bulgeTolerance) {
        return Point3d((start.x + end.x) / 2.0, (start.y + end.y) / 2.0, (start.z + end.z) / 2.0);
    }
    
    double dx = end.x - start.x;
    double dy = end.y - start.y;
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (chordLength < m_vertexTolerance) {
        return start;
    }
    
    double radius = CalculateArcRadius(start, end, bulge);
    double sagitta = radius - chordLength / (2.0 * std::abs(bulge));
    
    // Calculate perpendicular direction
    double perpX = -dy / chordLength;
    double perpY = dx / chordLength;
    
    if (bulge < 0) {
        perpX = -perpX;
        perpY = -perpY;
    }
    
    Point3d midpoint((start.x + end.x) / 2.0, (start.y + end.y) / 2.0, (start.z + end.z) / 2.0);
    
    return Point3d(
        midpoint.x + perpX * sagitta,
        midpoint.y + perpY * sagitta,
        midpoint.z
    );
}

bool DWGPolylineProcessor::ValidateBulgeValue(double bulge) const {
    return std::isfinite(bulge) && std::abs(bulge) <= 1000.0; // Reasonable bulge limit
}

} // namespace IModelExport
