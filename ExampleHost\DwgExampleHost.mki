#----------------------------------------------------------------------------------------
#
#  $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DwgExampleHost.mki $
#
#  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
#
#----------------------------------------------------------------------------------------
SrcMstnPlatform=$(SrcRoot)PowerPlatform/MstnPlatform/

%include $(SrcBsiCommon)sharedmki/InternalSystemPolicy.mki

PublicApiIncludes    = -I${BuildContext}/PublicAPI/ -I${BuildContext}/VendorAPI/
PublicApiRscIncludes = -i${BuildContext}/PublicAPI/

%include DefaultToolSet.mki

# Get created along the way or from LKGs
%if exists ($(BuildContext)SubParts/mki/BentleyDlls.mki)
    %include $(BuildContext)SubParts/mki/BentleyDlls.mki
%endif

%if exists ($(BuildContext)SubParts/mki/DgnPlatformDlls.mki)
    %include $(BuildContext)SubParts/mki/DgnPlatformDlls.mki
%endif
