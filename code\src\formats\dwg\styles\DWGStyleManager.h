#pragma once

#include "../../../include/ExportTypes.h"
#include "../../../core/MaterialManager.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbsymtb.h>
#include <realdwg/base/dblayer.h>
#include <realdwg/base/dblinetype.h>
#include <realdwg/base/dbtextstyle.h>
#include <realdwg/base/dbdimstyle.h>
#include <realdwg/base/dbmlinestyle.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbvisualstyle.h>
#include <realdwg/base/acgimaterial.h>
#endif

#include <memory>
#include <unordered_map>
#include <string>
#include <vector>

namespace IModelExport {

//=======================================================================================
// Style Data Structures (Based on RealDwgFileIO style handling)
//=======================================================================================

struct LayerStyle {
    std::string name = "0";
    Color color = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string lineTypeName = "Continuous";
    double lineWeight = 0.25;      // Line weight in mm
    bool isVisible = true;
    bool isLocked = false;
    bool isFrozen = false;
    bool isPlottable = true;
    std::string description;
    
    // Advanced properties
    std::string materialName;
    std::string plotStyleName;
    double transparency = 0.0;     // 0.0 = opaque, 1.0 = transparent
    
    bool IsValid() const;
    bool IsDefault() const { return name == "0"; }
};

struct LineTypeStyle {
    std::string name = "Continuous";
    std::string description;
    std::vector<double> pattern;   // Dash pattern (positive = dash, negative = gap)
    double patternLength = 0.0;    // Total pattern length
    bool isScaled = true;
    
    // Text and shape elements in linetype
    struct LineTypeElement {
        enum Type { Dash, Text, Shape };
        Type type = Dash;
        double length = 0.0;
        std::string text;
        std::string shapeName;
        double scale = 1.0;
        double rotation = 0.0;
        Vector3d offset = Vector3d(0, 0, 0);
    };
    std::vector<LineTypeElement> elements;
    
    bool IsValid() const;
    bool IsContinuous() const { return name == "Continuous" || pattern.empty(); }
    
    // Predefined linetypes
    static LineTypeStyle CreateContinuous();
    static LineTypeStyle CreateDashed();
    static LineTypeStyle CreateDotted();
    static LineTypeStyle CreateDashDot();
    static LineTypeStyle CreateDashDotDot();
    static LineTypeStyle CreateCenter();
    static LineTypeStyle CreatePhantom();
};

struct TextStyle {
    std::string name = "Standard";
    std::string fontName = "Arial";
    std::string bigFontName;       // For Asian fonts
    double height = 0.0;           // 0.0 = variable height
    double widthFactor = 1.0;
    double obliqueAngle = 0.0;     // In radians
    bool isBackward = false;
    bool isUpsideDown = false;
    bool isVertical = false;
    
    // Font properties
    bool isBold = false;
    bool isItalic = false;
    int charset = 0;               // Font charset
    int pitchAndFamily = 0;        // Font pitch and family
    
    bool IsValid() const;
    bool IsStandard() const { return name == "Standard"; }
};

struct DimensionStyle {
    std::string name = "Standard";
    
    // Text properties
    std::string textStyleName = "Standard";
    double textHeight = 2.5;
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    double textGap = 0.625;
    
    // Arrow properties
    std::string arrowBlockName = "_ClosedFilled";
    double arrowSize = 2.5;
    
    // Line properties
    Color dimLineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    Color extLineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    double dimLineExtend = 0.0;
    double extLineExtend = 1.25;
    double extLineOffset = 0.625;
    
    // Precision and units
    int linearPrecision = 2;
    int angularPrecision = 0;
    std::string linearUnit = "mm";
    std::string angularUnit = "degrees";
    double linearScale = 1.0;
    double angularScale = 1.0;
    
    // Tolerances
    bool showTolerances = false;
    double upperTolerance = 0.0;
    double lowerTolerance = 0.0;
    
    bool IsValid() const;
    bool IsStandard() const { return name == "Standard"; }
};

struct MaterialStyle {
    std::string name = "Global";
    std::string description;
    
    // Basic properties
    Color ambientColor = Color(0.2f, 0.2f, 0.2f, 1.0f);
    Color diffuseColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
    Color specularColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    double shininess = 0.5;
    double transparency = 0.0;
    double reflectivity = 0.0;
    
    // Texture maps
    std::string diffuseMapName;
    std::string normalMapName;
    std::string specularMapName;
    std::string bumpMapName;
    std::string reflectionMapName;
    
    // Texture properties
    double textureScaleU = 1.0;
    double textureScaleV = 1.0;
    double textureRotation = 0.0;
    Vector3d textureOffset = Vector3d(0, 0, 0);
    
    bool IsValid() const;
    bool IsGlobal() const { return name == "Global"; }
    bool HasTextures() const;
};

//=======================================================================================
// Style Conversion Results
//=======================================================================================

struct StyleConversionResult {
    bool success = false;
    std::string originalName;
    std::string convertedName;
    std::vector<std::string> warnings;
    std::vector<std::string> errors;
    
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    void AddError(const std::string& error) {
        errors.push_back(error);
        success = false;
    }
};

//=======================================================================================
// DWG Style Manager (Based on RealDwgFileIO style conversion)
//=======================================================================================

class DWGStyleManager {
public:
    DWGStyleManager();
    ~DWGStyleManager();

    //===================================================================================
    // Style Management Interface
    //===================================================================================

    // Layer management
    StyleConversionResult CreateLayer(const LayerStyle& layerStyle);
    StyleConversionResult GetLayer(const std::string& name, LayerStyle& layerStyle) const;
    StyleConversionResult UpdateLayer(const LayerStyle& layerStyle);
    bool DeleteLayer(const std::string& name);
    std::vector<std::string> GetLayerNames() const;
    
    // LineType management
    StyleConversionResult CreateLineType(const LineTypeStyle& lineTypeStyle);
    StyleConversionResult GetLineType(const std::string& name, LineTypeStyle& lineTypeStyle) const;
    StyleConversionResult UpdateLineType(const LineTypeStyle& lineTypeStyle);
    bool DeleteLineType(const std::string& name);
    std::vector<std::string> GetLineTypeNames() const;
    
    // TextStyle management
    StyleConversionResult CreateTextStyle(const TextStyle& textStyle);
    StyleConversionResult GetTextStyle(const std::string& name, TextStyle& textStyle) const;
    StyleConversionResult UpdateTextStyle(const TextStyle& textStyle);
    bool DeleteTextStyle(const std::string& name);
    std::vector<std::string> GetTextStyleNames() const;
    
    // DimensionStyle management
    StyleConversionResult CreateDimensionStyle(const DimensionStyle& dimStyle);
    StyleConversionResult GetDimensionStyle(const std::string& name, DimensionStyle& dimStyle) const;
    StyleConversionResult UpdateDimensionStyle(const DimensionStyle& dimStyle);
    bool DeleteDimensionStyle(const std::string& name);
    std::vector<std::string> GetDimensionStyleNames() const;
    
    // Material management
    StyleConversionResult CreateMaterial(const MaterialStyle& materialStyle);
    StyleConversionResult GetMaterial(const std::string& name, MaterialStyle& materialStyle) const;
    StyleConversionResult UpdateMaterial(const MaterialStyle& materialStyle);
    bool DeleteMaterial(const std::string& name);
    std::vector<std::string> GetMaterialNames() const;

    //===================================================================================
    // Style Conversion (Based on RealDwgFileIO conversion logic)
    //===================================================================================

    // Layer conversion (from rdLayerConvert.cpp)
    StyleConversionResult ConvertLayer(const LayerStyle& sourceLayer, LayerStyle& targetLayer) const;
    bool ValidateLayerName(const std::string& name) const;
    std::string SanitizeLayerName(const std::string& name) const;
    Color ConvertLayerColor(const Color& sourceColor) const;
    
    // LineType conversion (from rdLineStyleConvert.cpp)
    StyleConversionResult ConvertLineType(const LineTypeStyle& sourceLineType, LineTypeStyle& targetLineType) const;
    bool ValidateLineTypePattern(const std::vector<double>& pattern) const;
    std::vector<double> OptimizeLineTypePattern(const std::vector<double>& pattern) const;
    double CalculatePatternLength(const std::vector<double>& pattern) const;
    
    // TextStyle conversion (from rdTextStyleConvert.cpp)
    StyleConversionResult ConvertTextStyle(const TextStyle& sourceTextStyle, TextStyle& targetTextStyle) const;
    bool ValidateFontName(const std::string& fontName) const;
    std::string FindAlternativeFont(const std::string& fontName) const;
    bool ValidateTextStyleProperties(const TextStyle& textStyle) const;
    
    // Material conversion (from rdMaterialConvert.cpp)
    StyleConversionResult ConvertMaterial(const MaterialStyle& sourceMaterial, MaterialStyle& targetMaterial) const;
    bool ValidateMaterialProperties(const MaterialStyle& material) const;
    Color ConvertMaterialColor(const Color& sourceColor) const;
    bool ValidateTexturePath(const std::string& texturePath) const;

    //===================================================================================
    // Style Validation and Repair
    //===================================================================================

    // Comprehensive validation
    bool ValidateAllStyles() const;
    std::vector<std::string> GetValidationErrors() const;
    std::vector<std::string> GetValidationWarnings() const;
    
    // Style repair
    bool RepairInvalidStyles();
    bool RepairLayer(LayerStyle& layer) const;
    bool RepairLineType(LineTypeStyle& lineType) const;
    bool RepairTextStyle(TextStyle& textStyle) const;
    bool RepairMaterial(MaterialStyle& material) const;
    
    // Dependency checking
    bool CheckStyleDependencies() const;
    std::vector<std::string> GetMissingDependencies() const;
    bool ResolveDependencies();

    //===================================================================================
    // Default Styles Creation
    //===================================================================================

    // Create standard styles
    void CreateDefaultStyles();
    void CreateDefaultLayers();
    void CreateDefaultLineTypes();
    void CreateDefaultTextStyles();
    void CreateDefaultDimensionStyles();
    void CreateDefaultMaterials();
    
    // Style templates
    LayerStyle GetDefaultLayer() const;
    LineTypeStyle GetDefaultLineType() const;
    TextStyle GetDefaultTextStyle() const;
    DimensionStyle GetDefaultDimensionStyle() const;
    MaterialStyle GetDefaultMaterial() const;

    //===================================================================================
    // Import/Export
    //===================================================================================

    // Style import/export
    bool ImportStylesFromFile(const std::string& filename);
    bool ExportStylesToFile(const std::string& filename) const;
    bool ImportStylesFromDWG(const std::string& dwgFilename);
    bool ExportStylesToDWG(const std::string& dwgFilename) const;
    
    // Bulk operations
    bool ImportLayers(const std::vector<LayerStyle>& layers);
    bool ImportLineTypes(const std::vector<LineTypeStyle>& lineTypes);
    bool ImportTextStyles(const std::vector<TextStyle>& textStyles);
    bool ImportMaterials(const std::vector<MaterialStyle>& materials);

#ifdef REALDWG_AVAILABLE
    //===================================================================================
    // RealDWG Integration
    //===================================================================================

    // Database integration
    bool InitializeWithDatabase(AcDbDatabase* database);
    bool SynchronizeWithDatabase();
    bool CommitChangesToDatabase();
    
    // Symbol table access
    AcDbLayerTableRecord* GetLayerRecord(const std::string& name) const;
    AcDbLinetypeTableRecord* GetLineTypeRecord(const std::string& name) const;
    AcDbTextStyleTableRecord* GetTextStyleRecord(const std::string& name) const;
    AcDbDimStyleTableRecord* GetDimensionStyleRecord(const std::string& name) const;
    
    // Material dictionary access
    AcDbMaterial* GetMaterialRecord(const std::string& name) const;
    bool SetMaterialRecord(const std::string& name, AcDbMaterial* material);
    
    // Conversion helpers
    AcDbLayerTableRecord* CreateLayerRecord(const LayerStyle& layer) const;
    AcDbLinetypeTableRecord* CreateLineTypeRecord(const LineTypeStyle& lineType) const;
    AcDbTextStyleTableRecord* CreateTextStyleRecord(const TextStyle& textStyle) const;
    AcDbMaterial* CreateMaterialRecord(const MaterialStyle& material) const;
#endif

private:
    //===================================================================================
    // Internal Storage
    //===================================================================================

    std::unordered_map<std::string, LayerStyle> m_layers;
    std::unordered_map<std::string, LineTypeStyle> m_lineTypes;
    std::unordered_map<std::string, TextStyle> m_textStyles;
    std::unordered_map<std::string, DimensionStyle> m_dimensionStyles;
    std::unordered_map<std::string, MaterialStyle> m_materials;
    
    // Validation state
    mutable std::vector<std::string> m_validationErrors;
    mutable std::vector<std::string> m_validationWarnings;
    
    // Configuration
    bool m_autoRepair = true;
    bool m_strictValidation = false;
    bool m_allowDuplicateNames = false;
    
    // Statistics
    mutable size_t m_conversionsPerformed = 0;
    mutable size_t m_validationsPerformed = 0;
    mutable size_t m_repairsPerformed = 0;

#ifdef REALDWG_AVAILABLE
    AcDbDatabase* m_database = nullptr;
#endif

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Name validation and sanitization
    bool IsValidStyleName(const std::string& name) const;
    std::string SanitizeStyleName(const std::string& name) const;
    bool IsReservedName(const std::string& name) const;
    std::string GenerateUniqueName(const std::string& baseName) const;
    
    // Color conversion helpers
    Color ValidateAndRepairColor(const Color& color) const;
    bool IsValidColor(const Color& color) const;
    Color ClampColor(const Color& color) const;
    
    // Pattern validation helpers
    bool IsValidDashPattern(const std::vector<double>& pattern) const;
    std::vector<double> NormalizePattern(const std::vector<double>& pattern) const;
    bool HasValidPatternLength(const std::vector<double>& pattern) const;
    
    // Font validation helpers
    bool IsFontAvailable(const std::string& fontName) const;
    std::vector<std::string> GetAvailableFonts() const;
    std::string GetSystemDefaultFont() const;
    
    // Dependency resolution
    void BuildDependencyGraph();
    bool ResolveDependency(const std::string& styleName, const std::string& dependencyName);
    std::vector<std::string> GetStyleDependencies(const std::string& styleName) const;
    
    // Error handling
    void LogError(const std::string& error) const;
    void LogWarning(const std::string& warning) const;
    void ClearValidationMessages() const;
};

} // namespace IModelExport
