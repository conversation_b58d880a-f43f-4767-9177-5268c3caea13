#pragma once

#include "../../../core/GeometryProcessor.h"
#include "../../../include/ExportTypes.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#include <realdwg/ge/gematrix3d.h>
#include <realdwg/ge/getol.h>
#include <realdwg/ge/geplane.h>
#include <realdwg/ge/geline3d.h>
#include <realdwg/ge/gecircle3d.h>
#include <realdwg/ge/geellipse3d.h>
#include <realdwg/ge/genurb3d.h>
#endif

#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>

namespace IModelExport {

//=======================================================================================
// Advanced Geometry Data Structures
//=======================================================================================

struct GeometryTolerance {
    double distance = 1e-10;      // Distance tolerance
    double angle = 1e-8;          // Angle tolerance (radians)
    double curvature = 1e-6;      // Curvature tolerance
    double area = 1e-12;          // Area tolerance
    double volume = 1e-15;        // Volume tolerance
    
    static GeometryTolerance Default();
    static GeometryTolerance Precise();
    static GeometryTolerance Coarse();
    
    bool IsValid() const;
    void Normalize();
};

struct Transform3D {
    double matrix[4][4];          // 4x4 transformation matrix
    
    Transform3D();                // Identity transform
    Transform3D(const double m[4][4]);
    
    // Factory methods
    static Transform3D Identity();
    static Transform3D Translation(const Vector3d& translation);
    static Transform3D Rotation(const Vector3d& axis, double angle);
    static Transform3D Scale(double scale);
    static Transform3D Scale(double sx, double sy, double sz);
    static Transform3D Mirror(const Point3d& point, const Vector3d& normal);
    
    // Operations
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;
    Transform3D Multiply(const Transform3D& other) const;
    Transform3D Inverse() const;
    
    // Properties
    bool IsIdentity(double tolerance = 1e-10) const;
    bool IsUniform(double tolerance = 1e-10) const;
    bool IsOrthogonal(double tolerance = 1e-10) const;
    double GetDeterminant() const;
    Vector3d GetTranslation() const;
    Vector3d GetScale() const;
};

struct BoundingBox3D {
    Point3d min;
    Point3d max;
    bool isEmpty = true;
    
    BoundingBox3D();
    BoundingBox3D(const Point3d& minPt, const Point3d& maxPt);
    
    // Operations
    void AddPoint(const Point3d& point);
    void AddBox(const BoundingBox3D& box);
    void Transform(const Transform3D& transform);
    void Expand(double distance);
    
    // Queries
    bool Contains(const Point3d& point, double tolerance = 1e-10) const;
    bool Intersects(const BoundingBox3D& other, double tolerance = 1e-10) const;
    Point3d GetCenter() const;
    Vector3d GetSize() const;
    double GetVolume() const;
    double GetDiagonal() const;
    
    // Validation
    bool IsValid() const;
    void Normalize();
};

//=======================================================================================
// Geometry Validation Results
//=======================================================================================

struct GeometryValidationResult {
    bool isValid = false;
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    
    // Geometric properties
    BoundingBox3D boundingBox;
    double complexity = 0.0;
    double quality = 1.0;
    
    // Validation flags
    bool hasValidCoordinates = false;
    bool hasValidTopology = false;
    bool hasValidDimensions = false;
    bool hasValidOrientation = false;
    
    void AddError(const std::string& error) {
        errors.push_back(error);
        isValid = false;
    }
    
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    void Clear() {
        isValid = false;
        errors.clear();
        warnings.clear();
        complexity = 0.0;
        quality = 1.0;
        hasValidCoordinates = false;
        hasValidTopology = false;
        hasValidDimensions = false;
        hasValidOrientation = false;
    }
};

//=======================================================================================
// DWG Geometry Processor
//=======================================================================================

class DWGGeometryProcessor : public GeometryProcessor {
public:
    DWGGeometryProcessor();
    ~DWGGeometryProcessor() override;

    // Configuration
    void SetTolerance(const GeometryTolerance& tolerance);
    const GeometryTolerance& GetTolerance() const { return m_tolerance; }
    
    void SetTransform(const Transform3D& transform);
    const Transform3D& GetTransform() const { return m_transform; }

    //===================================================================================
    // Coordinate Processing (Enhanced from RealDwgFileIO)
    //===================================================================================

    // Point validation and repair
    bool ValidatePoint(const Point3d& point) const;
    bool ValidatePoints(const std::vector<Point3d>& points) const;
    Point3d RepairPoint(const Point3d& point) const;
    void RepairPoints(std::vector<Point3d>& points) const;
    
    // Coordinate transformation
    Point3d TransformPoint(const Point3d& point) const override;
    Vector3d TransformVector(const Vector3d& vector) const override;
    void TransformPoints(std::vector<Point3d>& points) const;
    void TransformVectors(std::vector<Vector3d>& vectors) const;
    
    // Coordinate validation (based on RealDwgFileIO coordinate handling)
    bool IsValidCoordinate(double value) const;
    bool CoerceInvalidElevation(double& elevation) const;
    bool CoerceInvalidCoordinate(double& coordinate) const;
    void ValidateAndFixCoordinates(Point3d& point) const;

    //===================================================================================
    // Geometric Analysis
    //===================================================================================

    // Distance calculations
    double DistancePointToPoint(const Point3d& p1, const Point3d& p2) const;
    double DistancePointToLine(const Point3d& point, const Point3d& lineStart, const Point3d& lineEnd) const;
    double DistancePointToPlane(const Point3d& point, const Point3d& planePoint, const Vector3d& planeNormal) const;
    
    // Angle calculations
    double AngleBetweenVectors(const Vector3d& v1, const Vector3d& v2) const;
    double AngleBetweenLines(const Point3d& line1Start, const Point3d& line1End,
                           const Point3d& line2Start, const Point3d& line2End) const;
    
    // Area and volume calculations
    double CalculateTriangleArea(const Point3d& p1, const Point3d& p2, const Point3d& p3) const;
    double CalculatePolygonArea(const std::vector<Point3d>& vertices) const;
    double CalculateTetrahedronVolume(const Point3d& p1, const Point3d& p2, const Point3d& p3, const Point3d& p4) const;
    
    // Curvature analysis
    double CalculateCurvature(const Point3d& p1, const Point3d& p2, const Point3d& p3) const;
    Vector3d CalculateNormal(const Point3d& p1, const Point3d& p2, const Point3d& p3) const;
    bool ArePointsCollinear(const std::vector<Point3d>& points, double tolerance = -1) const;
    bool ArePointsCoplanar(const std::vector<Point3d>& points, double tolerance = -1) const;

    //===================================================================================
    // Geometric Validation
    //===================================================================================

    // Comprehensive geometry validation
    GeometryValidationResult ValidateGeometry(const GeometryData& geometry) const;
    GeometryValidationResult ValidateLineGeometry(const Point3d& start, const Point3d& end) const;
    GeometryValidationResult ValidateCircleGeometry(const Point3d& center, double radius, const Vector3d& normal) const;
    GeometryValidationResult ValidatePolylineGeometry(const std::vector<Point3d>& vertices) const;
    GeometryValidationResult ValidateSplineGeometry(const std::vector<Point3d>& controlPoints, 
                                                   const std::vector<double>& knots, int degree) const;
    
    // Topology validation
    bool ValidateTopology(const GeometryData& geometry) const;
    bool ValidateConnectivity(const std::vector<Point3d>& vertices, const std::vector<std::vector<int>>& faces) const;
    bool ValidateOrientation(const std::vector<Point3d>& vertices) const;
    
    // Dimension validation
    bool ValidateDimensions(const GeometryData& geometry) const;
    bool IsWithinTolerances(const GeometryData& geometry) const;
    bool HasValidScale(const GeometryData& geometry) const;

    //===================================================================================
    // Geometric Repair and Optimization
    //===================================================================================

    // Geometry repair
    bool RepairGeometry(GeometryData& geometry) const;
    bool RepairDegenerateGeometry(GeometryData& geometry) const;
    bool RepairSelfIntersections(std::vector<Point3d>& vertices) const;
    bool RepairInvalidNormals(std::vector<Vector3d>& normals) const;
    
    // Geometry optimization
    bool OptimizeGeometry(GeometryData& geometry, double tolerance = -1) const;
    bool SimplifyPolyline(std::vector<Point3d>& vertices, double tolerance = -1) const;
    bool RemoveDuplicateVertices(std::vector<Point3d>& vertices, double tolerance = -1) const;
    bool MergeCollinearSegments(std::vector<Point3d>& vertices, double tolerance = -1) const;
    
    // Tessellation and approximation
    std::vector<Point3d> TessellateCircle(const Point3d& center, double radius, const Vector3d& normal, int segments = 32) const;
    std::vector<Point3d> TessellateEllipse(const Point3d& center, const Vector3d& majorAxis, double radiusRatio, int segments = 32) const;
    std::vector<Point3d> TessellateSpline(const std::vector<Point3d>& controlPoints, 
                                         const std::vector<double>& knots, int degree, double tolerance = -1) const;

    //===================================================================================
    // Intersection and Collision Detection
    //===================================================================================

    // Line intersections
    bool LineLineIntersection(const Point3d& line1Start, const Point3d& line1End,
                             const Point3d& line2Start, const Point3d& line2End,
                             Point3d& intersection) const;
    bool LineCircleIntersection(const Point3d& lineStart, const Point3d& lineEnd,
                               const Point3d& center, double radius,
                               std::vector<Point3d>& intersections) const;
    bool LinePlaneIntersection(const Point3d& lineStart, const Point3d& lineEnd,
                              const Point3d& planePoint, const Vector3d& planeNormal,
                              Point3d& intersection) const;
    
    // Collision detection
    bool BoundingBoxIntersection(const BoundingBox3D& box1, const BoundingBox3D& box2) const;
    bool PointInBoundingBox(const Point3d& point, const BoundingBox3D& box) const;
    bool SphereIntersection(const Point3d& center1, double radius1,
                           const Point3d& center2, double radius2) const;

    //===================================================================================
    // Utility Methods
    //===================================================================================

    // Bounding box calculations
    BoundingBox3D CalculateBoundingBox(const std::vector<Point3d>& points) const;
    BoundingBox3D CalculateBoundingBox(const GeometryData& geometry) const;
    void UpdateBoundingBox(BoundingBox3D& box, const Point3d& point) const;
    
    // Vector operations
    Vector3d CrossProduct(const Vector3d& v1, const Vector3d& v2) const;
    double DotProduct(const Vector3d& v1, const Vector3d& v2) const;
    Vector3d Normalize(const Vector3d& vector) const;
    double Length(const Vector3d& vector) const;
    
    // Projection operations
    Point3d ProjectPointOnLine(const Point3d& point, const Point3d& lineStart, const Point3d& lineEnd) const;
    Point3d ProjectPointOnPlane(const Point3d& point, const Point3d& planePoint, const Vector3d& planeNormal) const;
    Vector3d ProjectVectorOnPlane(const Vector3d& vector, const Vector3d& planeNormal) const;

#ifdef REALDWG_AVAILABLE
    //===================================================================================
    // RealDWG Integration
    //===================================================================================

    // Conversion to AcGe types
    AcGePoint3d ToAcGePoint3d(const Point3d& point) const;
    AcGeVector3d ToAcGeVector3d(const Vector3d& vector) const;
    AcGeMatrix3d ToAcGeMatrix3d(const Transform3D& transform) const;
    AcGeTol ToAcGeTol(const GeometryTolerance& tolerance) const;
    
    // Conversion from AcGe types
    Point3d FromAcGePoint3d(const AcGePoint3d& point) const;
    Vector3d FromAcGeVector3d(const AcGeVector3d& vector) const;
    Transform3D FromAcGeMatrix3d(const AcGeMatrix3d& matrix) const;
    GeometryTolerance FromAcGeTol(const AcGeTol& tolerance) const;
    
    // Advanced geometric operations using AcGe
    bool AdvancedLineIntersection(const Point3d& line1Start, const Point3d& line1End,
                                 const Point3d& line2Start, const Point3d& line2End,
                                 Point3d& intersection) const;
    bool AdvancedCurveIntersection(const std::vector<Point3d>& curve1,
                                  const std::vector<Point3d>& curve2,
                                  std::vector<Point3d>& intersections) const;
#endif

private:
    //===================================================================================
    // Internal State
    //===================================================================================

    GeometryTolerance m_tolerance;
    Transform3D m_transform;
    
    // Caching for performance
    mutable std::unordered_map<std::string, BoundingBox3D> m_boundingBoxCache;
    mutable std::unordered_map<std::string, double> m_distanceCache;
    mutable std::unordered_map<std::string, bool> m_validationCache;
    
    // Statistics
    mutable size_t m_validationCount = 0;
    mutable size_t m_repairCount = 0;
    mutable size_t m_optimizationCount = 0;
    mutable size_t m_transformationCount = 0;
    
    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Cache management
    std::string GenerateCacheKey(const std::string& operation, const void* data, size_t size) const;
    void ClearCache() const;
    void UpdateStatistics(const std::string& operation) const;
    
    // Tolerance helpers
    double GetDistanceTolerance() const { return m_tolerance.distance; }
    double GetAngleTolerance() const { return m_tolerance.angle; }
    double GetCurvatureTolerance() const { return m_tolerance.curvature; }
    double GetAreaTolerance() const { return m_tolerance.area; }
    
    // Validation helpers
    bool IsValidDistance(double distance) const;
    bool IsValidAngle(double angle) const;
    bool IsValidArea(double area) const;
    bool IsValidVolume(double volume) const;
    
    // Geometric computation helpers
    double ComputeTriangleArea(const Point3d& p1, const Point3d& p2, const Point3d& p3) const;
    Vector3d ComputeTriangleNormal(const Point3d& p1, const Point3d& p2, const Point3d& p3) const;
    bool ComputeLinePlaneIntersection(const Point3d& lineStart, const Point3d& lineEnd,
                                     const Point3d& planePoint, const Vector3d& planeNormal,
                                     Point3d& intersection, double& parameter) const;
};

} // namespace IModelExport
