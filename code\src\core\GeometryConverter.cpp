#include "GeometryConverter.h"
#include "../../include/ExportTypes.h"

#include <cmath>
#include <algorithm>

namespace IModelExport {

//=======================================================================================
// Geometry Converter Implementation
//=======================================================================================

class GeometryConverter {
public:
    GeometryConverter() = default;
    ~GeometryConverter() = default;

    //===================================================================================
    // Point and Vector Operations
    //===================================================================================

    Point3d TransformPoint(const Point3d& point, const Transform3d& transform) const {
        Point3d result;
        
        // Apply 4x4 transformation matrix
        double w = transform.matrix[3][0] * point.x + 
                   transform.matrix[3][1] * point.y + 
                   transform.matrix[3][2] * point.z + 
                   transform.matrix[3][3];
        
        if (std::abs(w) < 1e-10) {
            w = 1.0; // Avoid division by zero
        }
        
        result.x = (transform.matrix[0][0] * point.x + 
                    transform.matrix[0][1] * point.y + 
                    transform.matrix[0][2] * point.z + 
                    transform.matrix[0][3]) / w;
        
        result.y = (transform.matrix[1][0] * point.x + 
                    transform.matrix[1][1] * point.y + 
                    transform.matrix[1][2] * point.z + 
                    transform.matrix[1][3]) / w;
        
        result.z = (transform.matrix[2][0] * point.x + 
                    transform.matrix[2][1] * point.y + 
                    transform.matrix[2][2] * point.z + 
                    transform.matrix[2][3]) / w;
        
        return result;
    }

    Vector3d TransformVector(const Vector3d& vector, const Transform3d& transform) const {
        Vector3d result;
        
        // Apply only rotation and scale (ignore translation)
        result.x = transform.matrix[0][0] * vector.x + 
                   transform.matrix[0][1] * vector.y + 
                   transform.matrix[0][2] * vector.z;
        
        result.y = transform.matrix[1][0] * vector.x + 
                   transform.matrix[1][1] * vector.y + 
                   transform.matrix[1][2] * vector.z;
        
        result.z = transform.matrix[2][0] * vector.x + 
                   transform.matrix[2][1] * vector.y + 
                   transform.matrix[2][2] * vector.z;
        
        return result;
    }

    std::vector<Point3d> TransformPoints(const std::vector<Point3d>& points, const Transform3d& transform) const {
        std::vector<Point3d> result;
        result.reserve(points.size());
        
        for (const auto& point : points) {
            result.push_back(TransformPoint(point, transform));
        }
        
        return result;
    }

    //===================================================================================
    // Distance and Angle Calculations
    //===================================================================================

    double Distance(const Point3d& p1, const Point3d& p2) const {
        double dx = p2.x - p1.x;
        double dy = p2.y - p1.y;
        double dz = p2.z - p1.z;
        return std::sqrt(dx * dx + dy * dy + dz * dz);
    }

    double Length(const Vector3d& vector) const {
        return std::sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    }

    Vector3d Normalize(const Vector3d& vector) const {
        double len = Length(vector);
        if (len < 1e-10) {
            return Vector3d(0, 0, 0);
        }
        return Vector3d(vector.x / len, vector.y / len, vector.z / len);
    }

    double DotProduct(const Vector3d& v1, const Vector3d& v2) const {
        return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
    }

    Vector3d CrossProduct(const Vector3d& v1, const Vector3d& v2) const {
        return Vector3d(
            v1.y * v2.z - v1.z * v2.y,
            v1.z * v2.x - v1.x * v2.z,
            v1.x * v2.y - v1.y * v2.x
        );
    }

    double Angle(const Vector3d& v1, const Vector3d& v2) const {
        Vector3d n1 = Normalize(v1);
        Vector3d n2 = Normalize(v2);
        double dot = DotProduct(n1, n2);
        
        // Clamp to avoid numerical errors
        dot = std::max(-1.0, std::min(1.0, dot));
        
        return std::acos(dot);
    }

    //===================================================================================
    // Bounding Box Operations
    //===================================================================================

    BoundingBox CalculateBoundingBox(const std::vector<Point3d>& points) const {
        BoundingBox bbox;
        
        if (points.empty()) {
            return bbox;
        }
        
        bbox.min = bbox.max = points[0];
        
        for (size_t i = 1; i < points.size(); ++i) {
            bbox.Extend(points[i]);
        }
        
        return bbox;
    }

    BoundingBox TransformBoundingBox(const BoundingBox& bbox, const Transform3d& transform) const {
        if (!bbox.IsValid()) {
            return bbox;
        }
        
        // Transform all 8 corners of the bounding box
        std::vector<Point3d> corners = {
            Point3d(bbox.min.x, bbox.min.y, bbox.min.z),
            Point3d(bbox.max.x, bbox.min.y, bbox.min.z),
            Point3d(bbox.min.x, bbox.max.y, bbox.min.z),
            Point3d(bbox.max.x, bbox.max.y, bbox.min.z),
            Point3d(bbox.min.x, bbox.min.y, bbox.max.z),
            Point3d(bbox.max.x, bbox.min.y, bbox.max.z),
            Point3d(bbox.min.x, bbox.max.y, bbox.max.z),
            Point3d(bbox.max.x, bbox.max.y, bbox.max.z)
        };
        
        std::vector<Point3d> transformedCorners = TransformPoints(corners, transform);
        return CalculateBoundingBox(transformedCorners);
    }

    //===================================================================================
    // Curve Operations
    //===================================================================================

    std::vector<Point3d> TessellateCurve(const std::vector<Point3d>& controlPoints, 
                                        int segments = 32) const {
        if (controlPoints.size() < 2) {
            return controlPoints;
        }
        
        std::vector<Point3d> result;
        result.reserve(segments + 1);
        
        // Simple linear interpolation for now
        // In a real implementation, this would handle different curve types
        for (int i = 0; i <= segments; ++i) {
            double t = static_cast<double>(i) / segments;
            Point3d point = InterpolateLinear(controlPoints, t);
            result.push_back(point);
        }
        
        return result;
    }

    Point3d InterpolateLinear(const std::vector<Point3d>& points, double t) const {
        if (points.empty()) {
            return Point3d();
        }
        
        if (points.size() == 1) {
            return points[0];
        }
        
        // Clamp t to [0, 1]
        t = std::max(0.0, std::min(1.0, t));
        
        // Find segment
        double segmentLength = 1.0 / (points.size() - 1);
        int segment = static_cast<int>(t / segmentLength);
        segment = std::min(segment, static_cast<int>(points.size()) - 2);
        
        // Local parameter within segment
        double localT = (t - segment * segmentLength) / segmentLength;
        
        // Linear interpolation
        const Point3d& p1 = points[segment];
        const Point3d& p2 = points[segment + 1];
        
        return Point3d(
            p1.x + localT * (p2.x - p1.x),
            p1.y + localT * (p2.y - p1.y),
            p1.z + localT * (p2.z - p1.z)
        );
    }

    //===================================================================================
    // Surface Operations
    //===================================================================================

    std::vector<Point3d> TessellateSurface(const std::vector<std::vector<Point3d>>& controlGrid,
                                          int uSegments = 16, int vSegments = 16) const {
        std::vector<Point3d> result;
        
        if (controlGrid.empty() || controlGrid[0].empty()) {
            return result;
        }
        
        result.reserve((uSegments + 1) * (vSegments + 1));
        
        for (int v = 0; v <= vSegments; ++v) {
            double vParam = static_cast<double>(v) / vSegments;
            
            for (int u = 0; u <= uSegments; ++u) {
                double uParam = static_cast<double>(u) / uSegments;
                
                Point3d point = InterpolateBilinear(controlGrid, uParam, vParam);
                result.push_back(point);
            }
        }
        
        return result;
    }

    Point3d InterpolateBilinear(const std::vector<std::vector<Point3d>>& grid, 
                               double u, double v) const {
        if (grid.empty() || grid[0].empty()) {
            return Point3d();
        }
        
        // Clamp parameters
        u = std::max(0.0, std::min(1.0, u));
        v = std::max(0.0, std::min(1.0, v));
        
        // Find grid indices
        int rows = static_cast<int>(grid.size());
        int cols = static_cast<int>(grid[0].size());
        
        double vIndex = v * (rows - 1);
        double uIndex = u * (cols - 1);
        
        int v0 = static_cast<int>(vIndex);
        int u0 = static_cast<int>(uIndex);
        int v1 = std::min(v0 + 1, rows - 1);
        int u1 = std::min(u0 + 1, cols - 1);
        
        double vFrac = vIndex - v0;
        double uFrac = uIndex - u0;
        
        // Bilinear interpolation
        const Point3d& p00 = grid[v0][u0];
        const Point3d& p10 = grid[v0][u1];
        const Point3d& p01 = grid[v1][u0];
        const Point3d& p11 = grid[v1][u1];
        
        Point3d p0 = Point3d(
            p00.x + uFrac * (p10.x - p00.x),
            p00.y + uFrac * (p10.y - p00.y),
            p00.z + uFrac * (p10.z - p00.z)
        );
        
        Point3d p1 = Point3d(
            p01.x + uFrac * (p11.x - p01.x),
            p01.y + uFrac * (p11.y - p01.y),
            p01.z + uFrac * (p11.z - p01.z)
        );
        
        return Point3d(
            p0.x + vFrac * (p1.x - p0.x),
            p0.y + vFrac * (p1.y - p0.y),
            p0.z + vFrac * (p1.z - p0.z)
        );
    }

    //===================================================================================
    // Utility Functions
    //===================================================================================

    bool IsPointOnLine(const Point3d& point, const Point3d& lineStart, 
                      const Point3d& lineEnd, double tolerance = 1e-6) const {
        Vector3d lineVec(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
        Vector3d pointVec(point.x - lineStart.x, point.y - lineStart.y, point.z - lineStart.z);
        
        Vector3d cross = CrossProduct(lineVec, pointVec);
        double crossLength = Length(cross);
        double lineLength = Length(lineVec);
        
        if (lineLength < tolerance) {
            return Distance(point, lineStart) < tolerance;
        }
        
        return (crossLength / lineLength) < tolerance;
    }

    double DistancePointToLine(const Point3d& point, const Point3d& lineStart, 
                              const Point3d& lineEnd) const {
        Vector3d lineVec(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
        Vector3d pointVec(point.x - lineStart.x, point.y - lineStart.y, point.z - lineStart.z);
        
        double lineLength = Length(lineVec);
        if (lineLength < 1e-10) {
            return Distance(point, lineStart);
        }
        
        Vector3d cross = CrossProduct(lineVec, pointVec);
        return Length(cross) / lineLength;
    }
};

} // namespace IModelExport
