#pragma once

#include "DWGEntityProcessor.h"
#include <vector>
#include <memory>

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbdim.h>
#include <realdwg/base/dblead.h>
#include <realdwg/base/dbmleader.h>
#include <realdwg/base/dbfcf.h>
#include <realdwg/base/dbdimassoc.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

namespace IModelExport {

//=======================================================================================
// Dimension Geometry Data Structures (Based on RealDwgFileIO rdDimension.cpp)
//=======================================================================================

struct DimensionGeometry {
    enum class Type {
        Linear,             // Linear dimension
        Aligned,            // Aligned dimension
        Angular,            // Angular dimension
        Radial,             // Radial dimension
        Diametric,          // Diametric dimension
        Ordinate,           // Ordinate dimension
        Rotated,            // Rotated dimension
        Arc                 // Arc length dimension
    };
    
    Type type = Type::Linear;
    
    // Common dimension points
    Point3d defPoint1;              // First definition point
    Point3d defPoint2;              // Second definition point
    Point3d textPosition;           // Text position
    Point3d dimLinePoint;           // Dimension line point
    
    // Dimension text
    std::string dimensionText;      // Override text (empty = measured)
    std::string prefix;             // Text prefix
    std::string suffix;             // Text suffix
    double textRotation = 0.0;      // Text rotation angle
    bool textInside = true;         // Text inside extension lines
    
    // Dimension style properties
    std::string dimStyleName = "Standard";
    double textHeight = 2.5;
    double arrowSize = 2.5;
    double extLineOffset = 0.625;
    double extLineExtend = 1.25;
    double dimLineExtend = 0.0;
    double textGap = 0.625;
    
    // Colors and line types
    Color dimLineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    Color extLineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string dimLineType = "Continuous";
    std::string extLineType = "Continuous";
    
    // Arrow and symbol properties
    std::string arrow1Name = "_ClosedFilled";
    std::string arrow2Name = "_ClosedFilled";
    bool suppressArrow1 = false;
    bool suppressArrow2 = false;
    bool suppressExtLine1 = false;
    bool suppressExtLine2 = false;
    bool suppressDimLine = false;
    
    // Measurement properties
    double measuredValue = 0.0;     // Actual measured value
    double linearScale = 1.0;       // Linear scale factor
    double roundValue = 0.0;        // Rounding value
    int precision = 2;              // Decimal precision
    bool showUnits = true;          // Show units
    std::string units = "mm";       // Units string
    
    // Type-specific data
    union {
        struct {
            double angle;           // Rotation angle for rotated dimension
            Vector3d direction;     // Dimension direction
        } linear;
        
        struct {
            Point3d vertex;         // Angle vertex
            Point3d point1;         // First angle point
            Point3d point2;         // Second angle point
            double angle;           // Measured angle
        } angular;
        
        struct {
            Point3d center;         // Circle/arc center
            double radius;          // Radius value
            Point3d chordPoint;     // Chord point for radial
        } radial;
        
        struct {
            Point3d center;         // Circle center
            double diameter;        // Diameter value
            Point3d farChordPoint;  // Far chord point
        } diametric;
        
        struct {
            Point3d origin;         // Ordinate origin
            Point3d feature;        // Feature point
            bool isXOrdinate;       // X or Y ordinate
        } ordinate;
        
        struct {
            Point3d arcCenter;      // Arc center
            Point3d arcStart;       // Arc start point
            Point3d arcEnd;         // Arc end point
            double arcLength;       // Arc length
        } arc;
    };
    
    bool IsValid() const;
    double CalculateMeasuredValue() const;
    std::string FormatDimensionText() const;
};

struct LeaderGeometry {
    enum class Type {
        Spline,             // Splined leader
        Straight            // Straight line leader
    };
    
    Type type = Type::Spline;
    std::vector<Point3d> vertices;      // Leader vertices
    Point3d arrowPoint;                 // Arrow point (first vertex)
    Point3d textPoint;                  // Text attachment point
    
    // Leader properties
    bool hasArrowHead = true;
    bool hasHookLine = false;
    std::string arrowName = "_ClosedFilled";
    double arrowSize = 2.5;
    
    // Text properties
    std::string text;
    std::string textStyleName = "Standard";
    double textHeight = 2.5;
    double textWidth = 0.0;             // 0 = auto width
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    
    // Line properties
    Color lineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string lineType = "Continuous";
    double lineWeight = 0.25;
    
    bool IsValid() const;
    double CalculateLength() const;
};

struct MLeaderGeometry {
    struct LeaderLine {
        std::vector<Point3d> vertices;
        Point3d arrowPoint;
        bool hasArrow = true;
        std::string arrowName = "_ClosedFilled";
    };
    
    std::vector<LeaderLine> leaderLines;
    Point3d textLocation;
    Point3d blockLocation;
    
    // Content type
    enum class ContentType {
        None,
        MText,
        Block,
        Tolerance
    } contentType = ContentType::MText;
    
    // Text content
    std::string text;
    std::string textStyleName = "Standard";
    double textHeight = 2.5;
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    
    // Block content
    std::string blockName;
    std::vector<std::string> blockAttributes;
    double blockScale = 1.0;
    double blockRotation = 0.0;
    
    // Leader properties
    bool enableLanding = true;
    double landingGap = 0.36;
    bool enableDogleg = true;
    double doglegLength = 8.0;
    
    // Arrow properties
    double arrowSize = 4.0;
    Color lineColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    std::string lineType = "Continuous";
    
    bool IsValid() const;
    size_t GetLeaderCount() const { return leaderLines.size(); }
};

struct ToleranceGeometry {
    enum class Type {
        Geometric,          // Geometric tolerance (FCF)
        Dimensional        // Dimensional tolerance
    };
    
    Type type = Type::Geometric;
    Point3d position;
    Vector3d direction = Vector3d(1, 0, 0);
    Vector3d normal = Vector3d(0, 0, 1);
    
    // Tolerance content
    std::string toleranceText;
    std::string symbol;             // Geometric tolerance symbol
    std::string materialCondition; // Material condition modifier
    std::string datum1, datum2, datum3; // Datum references
    
    // Tolerance values
    double tolerance1 = 0.0;
    double tolerance2 = 0.0;
    
    // Display properties
    double textHeight = 2.5;
    std::string textStyleName = "Standard";
    Color textColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    Color frameColor = Color(1.0f, 1.0f, 1.0f, 1.0f);
    
    bool IsValid() const;
    std::string FormatToleranceText() const;
};

//=======================================================================================
// Dimension Validation Results
//=======================================================================================

struct DimensionValidationResult : public DWGValidationResult {
    bool hasValidPoints = false;
    bool hasValidText = false;
    bool hasValidStyle = false;
    bool hasValidMeasurement = false;
    double measuredValue = 0.0;
    
    void AddDimensionError(const std::string& error) {
        AddError("Dimension: " + error);
    }
    
    void AddDimensionWarning(const std::string& warning) {
        AddWarning("Dimension: " + warning);
    }
};

struct LeaderValidationResult : public DWGValidationResult {
    bool hasValidVertices = false;
    bool hasValidText = false;
    bool hasValidArrow = false;
    double leaderLength = 0.0;
    
    void AddLeaderError(const std::string& error) {
        AddError("Leader: " + error);
    }
    
    void AddLeaderWarning(const std::string& warning) {
        AddWarning("Leader: " + warning);
    }
};

//=======================================================================================
// DWG Dimension Processor (Based on RealDwgFileIO rdDimension.cpp)
//=======================================================================================

class DWGDimensionProcessor : public DWGEntityProcessor {
public:
    DWGDimensionProcessor(DWGExporter* exporter);

    DWGProcessingStatus ProcessEntity(const ElementInfo& element) override;
    bool CanProcessEntity(const ElementInfo& element) const override;
    std::string GetProcessorName() const override { return "DWGDimensionProcessor"; }

    // Dimension processing methods
    DWGProcessingStatus ProcessDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessLeader(const LeaderGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessMLeader(const MLeaderGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessTolerance(const ToleranceGeometry& geometry, const std::string& layer = "");

    // Specific dimension types
    DWGProcessingStatus ProcessLinearDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessAlignedDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessAngularDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessRadialDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessDiametricDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessOrdinateDimension(const DimensionGeometry& geometry, const std::string& layer = "");
    DWGProcessingStatus ProcessArcDimension(const DimensionGeometry& geometry, const std::string& layer = "");

    // Validation methods
    DimensionValidationResult ValidateDimensionGeometry(const DimensionGeometry& geometry) const;
    LeaderValidationResult ValidateLeaderGeometry(const LeaderGeometry& geometry) const;
    bool ValidateDimensionPoints(const DimensionGeometry& geometry) const;
    bool ValidateLeaderVertices(const std::vector<Point3d>& vertices) const;
    bool ValidateDimensionText(const std::string& text) const;
    bool ValidateToleranceData(const ToleranceGeometry& geometry) const;

    // Dimension calculation and formatting
    double CalculateLinearDistance(const Point3d& p1, const Point3d& p2) const;
    double CalculateAngularMeasurement(const Point3d& vertex, const Point3d& p1, const Point3d& p2) const;
    double CalculateRadialDistance(const Point3d& center, const Point3d& point) const;
    double CalculateArcLength(const Point3d& center, const Point3d& start, const Point3d& end, double radius) const;
    
    std::string FormatDimensionValue(double value, int precision, const std::string& units) const;
    std::string FormatAngularValue(double angle, int precision) const;
    std::string ApplyDimensionFormatting(const DimensionGeometry& geometry) const;

    // Dimension repair and optimization
    bool RepairDimensionGeometry(DimensionGeometry& geometry) const;
    bool RepairLeaderGeometry(LeaderGeometry& geometry) const;
    bool OptimizeDimensionPlacement(DimensionGeometry& geometry) const;
    bool ValidateAndFixDimensionPoints(DimensionGeometry& geometry) const;

    // Text and arrow processing
    bool ProcessDimensionText(const DimensionGeometry& geometry) const;
    bool ProcessDimensionArrows(const DimensionGeometry& geometry) const;
    bool ProcessExtensionLines(const DimensionGeometry& geometry) const;
    bool ProcessDimensionLine(const DimensionGeometry& geometry) const;

private:
    // Dimension calculation helpers
    Point3d CalculateDimensionLinePoint(const DimensionGeometry& geometry) const;
    Point3d CalculateTextPosition(const DimensionGeometry& geometry) const;
    std::vector<Point3d> CalculateExtensionLines(const DimensionGeometry& geometry) const;
    std::vector<Point3d> CalculateArrowPositions(const DimensionGeometry& geometry) const;
    
    // Geometric calculations
    Vector3d CalculateDimensionDirection(const DimensionGeometry& geometry) const;
    Vector3d CalculateNormalVector(const DimensionGeometry& geometry) const;
    double CalculateTextRotation(const DimensionGeometry& geometry) const;
    bool IsTextInsideExtensionLines(const DimensionGeometry& geometry) const;
    
    // Leader processing helpers
    bool ProcessLeaderVertices(const std::vector<Point3d>& vertices) const;
    bool ProcessLeaderArrow(const LeaderGeometry& geometry) const;
    bool ProcessLeaderText(const LeaderGeometry& geometry) const;
    Point3d CalculateLeaderTextPosition(const LeaderGeometry& geometry) const;
    
    // MLeader processing helpers
    bool ProcessMLeaderLines(const MLeaderGeometry& geometry) const;
    bool ProcessMLeaderContent(const MLeaderGeometry& geometry) const;
    bool ProcessMLeaderLanding(const MLeaderGeometry& geometry) const;
    Point3d CalculateMLeaderContentPosition(const MLeaderGeometry& geometry) const;
    
    // Tolerance processing helpers
    bool ProcessToleranceSymbol(const ToleranceGeometry& geometry) const;
    bool ProcessToleranceFrame(const ToleranceGeometry& geometry) const;
    std::string FormatToleranceString(const ToleranceGeometry& geometry) const;
    
    // Validation helpers
    bool ValidateDimensionStyle(const std::string& styleName) const;
    bool ValidateArrowBlock(const std::string& arrowName) const;
    bool ValidateTextStyle(const std::string& styleName) const;
    bool ValidateUnitsFormat(const std::string& units) const;
    
    // Coordinate and transformation helpers
    void TransformDimensionGeometry(DimensionGeometry& geometry) const;
    void TransformLeaderGeometry(LeaderGeometry& geometry) const;
    Point3d ProjectPointOnDimensionLine(const Point3d& point, const DimensionGeometry& geometry) const;

#ifdef REALDWG_AVAILABLE
    // RealDWG specific methods
    AcDbDimension* CreateDWGDimension(const DimensionGeometry& geometry) const;
    AcDbAlignedDimension* CreateDWGAlignedDimension(const DimensionGeometry& geometry) const;
    AcDbRotatedDimension* CreateDWGRotatedDimension(const DimensionGeometry& geometry) const;
    AcDbAngularDimension* CreateDWGAngularDimension(const DimensionGeometry& geometry) const;
    AcDbRadialDimension* CreateDWGRadialDimension(const DimensionGeometry& geometry) const;
    AcDbDiametricDimension* CreateDWGDiametricDimension(const DimensionGeometry& geometry) const;
    AcDbOrdinateDimension* CreateDWGOrdinateDimension(const DimensionGeometry& geometry) const;
    AcDbArcDimension* CreateDWGArcDimension(const DimensionGeometry& geometry) const;
    
    AcDbLeader* CreateDWGLeader(const LeaderGeometry& geometry) const;
    AcDbMLeader* CreateDWGMLeader(const MLeaderGeometry& geometry) const;
    AcDbFcf* CreateDWGTolerance(const ToleranceGeometry& geometry) const;
    
    // Property setting helpers
    bool SetDimensionProperties(AcDbDimension* dimension, const DimensionGeometry& geometry) const;
    bool SetLeaderProperties(AcDbLeader* leader, const LeaderGeometry& geometry) const;
    bool SetMLeaderProperties(AcDbMLeader* mleader, const MLeaderGeometry& geometry) const;
    bool SetToleranceProperties(AcDbFcf* tolerance, const ToleranceGeometry& geometry) const;
    
    // Style and formatting helpers
    bool ApplyDimensionStyle(AcDbDimension* dimension, const std::string& styleName) const;
    bool SetDimensionText(AcDbDimension* dimension, const std::string& text) const;
    bool SetDimensionArrows(AcDbDimension* dimension, const DimensionGeometry& geometry) const;
    
    // Error handling for RealDWG operations
    bool HandleDimensionCreationError(Acad::ErrorStatus status, const std::string& operation) const;
#endif

    // Statistics and debugging
    mutable size_t m_processedDimensions = 0;
    mutable size_t m_processedLeaders = 0;
    mutable size_t m_processedMLeaders = 0;
    mutable size_t m_processedTolerances = 0;
    mutable size_t m_repairedDimensions = 0;
    
    // Configuration
    double m_dimensionTolerance = 1e-6;
    double m_textTolerance = 1e-3;
    double m_angleTolerance = 1e-8;
    bool m_enableAutoTextPlacement = true;
    bool m_enableDimensionRepair = true;
    bool m_enableTextFormatting = true;
    bool m_enableAssociativity = false;
};

} // namespace IModelExport
