/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdLight.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/09
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtLight : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* 将AutoCAD(DWG)格式的灯光对象转换为MicroStation(DGN)格式的灯光元素
* 
* @param acObject    要转换的AutoCAD对象指针
* @param outElement  输出的DGN元素句柄引用
* @param context     转换上下文的引用
* 
* @return RealDwgStatus 转换状态:
*         - RealDwgSuccess: 转换成功
*         - SkipCreatingLights: 跳过灯光创���
*         - CantCreateLight: 无法创建灯光
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    // 检查设置是否允许创建DGN灯光
    if (!context.GetSettings().CreateDGNLights())
        return  SkipCreatingLights;

    // 将输入对象转换为AutoCAD灯光对象
    AcDbLight*          light = AcDbLight::cast (acObject);

    // 调用辅助方法创建DGN灯光元素
    // 如果创建失败则返回错误状态
    if (RealDwgSuccess != this->CreateDgnLightFromDwgLight(outElement, light, context))
        return  CantCreateLight;

    // 从AutoCAD实体复制元素头信息到DGN元素
    // HEADERRESTOREMASK_CONSTRUCTIONCLASS指定要复制的头信息类型
    context.ElementHeaderFromEntity (outElement, light, HEADERRESTOREMASK_CONSTRUCTIONCLASS);

    // 遍历所有子元素
    // 处理灯光相关的文本元素,将它们设置为不可见
    for (ChildEditElemIter child(outElement, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
        {
        MSElementP  elem = child.GetElementP ();
        // 清除复杂头标志
        elem->ehdr.isComplexHeader = false;
        // 如果是文本元素且当前可见,则将其设置为不可见
        if (TEXT_ELM == elem->ehdr.type && !elem->hdr.dhdr.props.b.invisible)
            elem->hdr.dhdr.props.b.invisible = true;
        }

    // 返回成功状态
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
LightElementPtr createDgnLight (EditElementHandleR outElement, const AcDbLight* dwgLight, ConvertToDgnContextR context) const
    {
    WCharCP         cellName = NULL;
    LightElementPtr dgnLight;
    switch (dwgLight->lightType())
        {
        case AcGiDrawable::kPointLight:
            {
            dgnLight = PointLight::Create ();
            cellName = NAME_PointLight;
            break;
            }

        case AcGiDrawable::kDistantLight:
            {
            dgnLight = DistantLight::Create ();
            cellName = NAME_DistanceLight;
            break;
            }

        case AcGiDrawable::kSpotLight:
            {
            dgnLight = SpotLight::Create ();
            cellName = NAME_SpotLight;
            break;
            }

        case AcGiDrawable::kWebLight:
            {
            dgnLight = PointLight::Create ();
            cellName = NAME_PointLight;
            break;
            }

        default:
            DIAGNOSTIC_PRINTF ("Unknown DWG light type %d, %ls, not converted!\n", dwgLight->lightType(), dwgLight->name().kwszPtr());
            return  nullptr;
        }

    dgnLight->SetModelRef (context.GetModel());

    auto contextState = LightCellCreateContextState::Create (outElement);
    BentleyStatus   status = dgnLight->CreateNewLightCell (contextState->GetContext(), cellName);
    if (BSISUCCESS != status)
        return  nullptr;

    outElement.SetIElementState (contextState.get());
    return dgnLight;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateDgnLightFromDwgLight (EditElementHandleR outElement, const AcDbLight* dwgLight, ConvertToDgnContextR context) const
    {
    // get light transformation matrix & light direction
    Transform       lightTransform;
    AcGeVector3d    lightVector = this->GetLightVector (lightTransform, dwgLight, context);

    // light size from the light vector
    double  lightSize = GetLightGlyphSize (dwgLight);
    if (lightSize <= 0.0)
        lightSize = INVISIBLELIGHTGLYPH_UORS;
    else
        lightSize *= lightVector.length() * context.GetScaleToDGN();

    // light intensity
    double          lightIntensity = dwgLight->intensity ();

    // create DGN light based on DWG light type
    LightElementPtr dgnLight = createDgnLight (outElement, dwgLight, context);

    if (dgnLight.IsNull ())
        return CantCreateLight;

    switch (dwgLight->lightType())
        {
        case AcGiDrawable::kPointLight:
            {
            PointLightP   pointLight = dynamic_cast <PointLightP> (dgnLight.get ());

            pointLight->SetBulbCount ((UInt32)(lightIntensity > 1.0 ? 1 : floor(lightIntensity)));
            pointLight->SetBulbSizeInUors (lightSize);

            dgnLight = pointLight;
            break;
            }

        case AcGiDrawable::kDistantLight:
            {
            DistantLightP   distantLight = dynamic_cast <DistantLightP> (dgnLight.get ());
            lightTransform.GetMatrixColumn ((DVec3dR)distantLight->GetDirectionR(), 0);
            
            dgnLight = distantLight;
            break;
            }

        case AcGiDrawable::kSpotLight:
            {
            SpotLightP   spotLight = dynamic_cast <SpotLightP> (dgnLight.get ());

            spotLight->SetOuterAngleInRadians (dwgLight->falloffAngle());
            spotLight->SetDeltaAngleInRadians (dwgLight->falloffAngle() - dwgLight->hotspotAngle());

            double  intensity = dwgLight->intensity();
            spotLight->SetBulbCount ((UInt32)(intensity > 1.0 ? 1 : floor(intensity)));
            spotLight->SetBulbSizeInUors (lightSize);

            lightTransform.GetMatrixColumn ((DVec3dR)spotLight->GetDirectionR(), 0);

            dgnLight = spotLight;
            break;
            }

        case AcGiDrawable::kWebLight:
            {
            PointLightP   pointLight = dynamic_cast <PointLightP> (dgnLight.get ());

            AcString      webFile;
            AcGeVector3d  webRotation;
            if (Acad::eOk == dwgLight->webFile(webFile))
                {
                pointLight->SetUsesIesData (true);

                if (webFile.isEmpty() && !this->FindIesFile(webFile, dwgLight, context.GetFileHolder().GetFileName().c_str()))
                    DIAGNOSTIC_PRINTF ("Weblight %ls does not have an IES file name.\n", dwgLight->name().kwszPtr());
                pointLight->SetIesFileName (webFile.kwszPtr());

                if (Acad::eOk == dwgLight->webRotation(webRotation) && !webRotation.isZeroLength())
                    {
                    /*-------------------------------------------------------------------
                    webRotation.z       => rotation angle about light direction
                    webRotation.x       => rotation angle about web normal
                    dgnLight->reference => web normal
                    dgnlight->refAngle  => rotation angle about light direction

                    FUTUREWORK: convert X and Y rotations to effective light parameters?
                    Or maybe we should support full 3D rotation like ACAD?
                    -------------------------------------------------------------------*/
                    pointLight->SetIesRotation (webRotation.z);
                    }
                }
            break;
            }

        default:
            DIAGNOSTIC_PRINTF ("Unknown DWG light type %d, %ls, not converted!\n", dwgLight->lightType(), dwgLight->name().kwszPtr());
            return  CantCreateLight;
        }

    // now set DGN light intensity scale
    dgnLight->SetIntensity (lightIntensity > 1.0 ? 1.0 : lightIntensity);

    // convert rest of DWG light parameters to DGN:
    dgnLight->SetIsEnabled (dwgLight->isOn());
    dgnLight->SetName (dwgLight->name().kwszPtr());

    // physical intensity

    double      lightBrightness = 1.0e+6;
    int         lightingUnits = context.GetFileHolder().GetDatabase()->lightingUnits ();
    if (LIGHTINGUNITS_Generic != lightingUnits)
        {
        /*---------------------------------------------------------------------------
        Photometric units are used: 1=American, 2=International.

        DGN brightness is the luminous flux in lumen.  Candelas and lux need to be
        converted to lumen.
        ---------------------------------------------------------------------------*/
        switch (dwgLight->physicalIntensityMethod())
            {
            case AcDbLight::kFlux:                  // lumen
                lightBrightness = dwgLight->physicalIntensity ();
                break;
            case AcDbLight::kIlluminance:           // lux or foot-candles
                if (AcGiDrawable::kWebLight != dwgLight->lightType())
                    lightBrightness = this->ConvertCandelasToLumen(dwgLight) / UnitAreaFromLightingUnits(context.GetTargetUnits(), lightingUnits);
                break;
            case AcDbLight::kPeakIntensity:         // candelas
            default:
                lightBrightness = this->ConvertCandelasToLumen (dwgLight);
                break;
            }
        }
    dgnLight->SetBrightness (lightBrightness);

    // filter color - We probably should also support both filter and lamp colors in DGN.
    dgnLight->GetColorR() = context.GetRGBFactor (dwgLight->lightColor().entityColor());
    // lamp color
    dgnLight->SetTemperatureInKelvin ((UInt32)dwgLight->lampColorTemp());
    // lamp name
    if (AcDbLight::kPreset == dwgLight->lampColorType())
        {
        WString     presetString;
        RmgrResource::LoadWString (presetString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgPresetLights, (RealDwgPresetLightId)dwgLight->lampColorPreset());
        dgnLight->SetPresetName (presetString.c_str());
        }

    // attenuation
    AcGiLightAttenuation    attenuation = dwgLight->lightAttenuation ();
    switch (attenuation.attenuationType())
        {
        case AcGiLightAttenuation::kInverseLinear:      // we only have inverse squared?
        case AcGiLightAttenuation::kInverseSquare:
            dgnLight->SetLightAttenuates (attenuation.useLimits());
            dgnLight->SetAttenuationDistance (attenuation.endLimit() * context.GetScaleToDGN());
            break;

        case AcGiLightAttenuation::kNone:
        default:
            dgnLight->SetLightAttenuates (false);
            break;
        }

    // shadows
    AcGiShadowParameters    dwgShadow;
    if (Acad::eOk == dwgLight->shadowParameters(dwgShadow))
        {
        dgnLight->SetCastsShadows (dwgShadow.shadowsOn());
        /*-----------------------------------------------------------------------------------------------------------
        dgnLight.samples is for map size. According to Paul, dgnLight.shadowMapResolution was for Phong shading but
        no longer used.  BSILight::m_shadowResolution and m_shadowType are for raytracing or depthmap shadows.
        -----------------------------------------------------------------------------------------------------------*/
        switch (dwgShadow.shadowType())
            {
            case AcGiShadowParameters::kShadowsRayTraced:
                dgnLight->SetShadowType (AdvancedLight::SHADOWTYPE_RayTrace);
                dgnLight->SetShadowSamples (Light::SHADOWQUALITY_Sharp);
                break;
            case AcGiShadowParameters::kShadowMaps:
                dgnLight->SetShadowType (AdvancedLight::SHADOWTYPE_DeepShadowMap);
                dgnLight->SetShadowSamples (dwgShadow.shadowMapSize());
                break;
            case AcGiShadowParameters::kAreaSampled:
                dgnLight->SetShadowType (AdvancedLight::SHADOWTYPE_RayTrace);
                dgnLight->SetShadowSamples (dwgShadow.shadowSamples());
                break;
            default:        // should never hit this though
                dgnLight->SetShadowSamples (dwgShadow.shadowMapSize());
            }
        dgnLight->SetDeepShadowSamples (dwgShadow.shadowMapSize());
        }

    dgnLight->SaveToElement (outElement);
    outElement.GetDisplayHandler()->ValidateElementRange (outElement, true);
    
    // scale the cell range to the light size
    if (lightSize > 0.0)
        {
        DRange3d    cellRange;
        DataConvert::ScanRangeToDRange3d (cellRange, outElement.GetElementCP()->hdr.dhdr.range);

        double      cellSize = sqrt (cellRange.ExtentSquared());
        if (cellSize > 0.0)
            {
            lightSize /= cellSize;
            if (fabs(1 - lightSize) > TOLERANCE_ZeroScale)
                lightTransform.ScaleMatrixColumns (lightSize, lightSize, lightSize);
            }
        }

    outElement.GetHandler (MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (outElement, TransformInfo(lightTransform));

    context.SetLightSourceFound (true);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcGeVector3d    GetLightVector (TransformR lightTransform, const AcDbLight* dwgLight, ConvertToDgnContextR context) const
    {
    // set the cell's origin and matrix
    AcGeMatrix3d        ecsMatrix;
    dwgLight->getEcs (ecsMatrix);

    AcGePoint3d         ecsOrigin;
    AcGeVector3d        xAxis, yAxis, zAxis;
    ecsMatrix.getCoordSystem (ecsOrigin, xAxis, yAxis, zAxis);

    // get light origin & direction
    DPoint3d            lightOrigin;
    RealDwgUtil::DPoint3dFromGePoint3d (lightOrigin, dwgLight->position());
    context.GetTransformToDGN().Multiply (lightOrigin);

    /*-----------------------------------------------------------------------------------
    RealDWG Doc days that lightDirection is valid only for distant light, and targetLocation
    valid only for spot light.  But lightDirection does not seem to return a valid vector
    for distant light and targetLocation seems to always return a valid point.
    -----------------------------------------------------------------------------------*/
    xAxis = dwgLight->targetLocation() - dwgLight->position();
    xAxis.normalize ();

    // build cell matrix
    RotMatrix   lightMatrix;
    DVec3d      xVec, zVec;
    lightMatrix.SetColumn (RealDwgUtil::DVec3dFromGeVector3d(xVec, xAxis), 0);
    lightMatrix.SetColumn (RealDwgUtil::DVec3dFromGeVector3d(zVec, zAxis), 2);
    lightMatrix.SquareAndNormalizeColumns (lightMatrix, 0, 1);

    lightTransform.InitFrom (lightMatrix, lightOrigin);

    return  zAxis;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertCandelasToLumen (const AcDbLight* dwgLight) const
    {
    double      candelas = dwgLight->physicalIntensity ();

    switch (dwgLight->lightType())
        {
        case AcGiDrawable::kPointLight:
        case AcGiDrawable::kDistantLight:
            // The luminous flux is 4*PI*candelas in lumens on a full sphere:
            return  CANDELASTOLUMEN(candelas);

        case AcGiDrawable::kSpotLight:
            /*---------------------------------------------------------------------------
            The luminous flux is the product of the intensity and the solid angle of the 
            hotspot cone, plus the incremental solid angle of the fall-off region. 

            FUTUREWORK: I can't find the increamental solid angle of the fall-off region 
            in the object's photometric info xrecord.  I leave this 2nd part not applied 
            until we can figure out how to get the value.
            ---------------------------------------------------------------------------*/
            return  dwgLight->hotspotAngle() * candelas;

        case  AcGiDrawable::kWebLight:
            /*---------------------------------------------------------------------------
            There is no analytical formula for Luminous flux that I know of.  Rendering 
            system should numerically integrate the intensities provided in the IES file.
            ---------------------------------------------------------------------------*/
            break;
        }

    return  100.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    don.fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FindIesFile (AcString& iesFileName, const AcDbLight* dwgLight, WCharCP dwgFileName) const
    {
    AcDbXrecord*    xRecord = RealDwgUtil::GetXRecord(AcDbObject::cast(dwgLight), L"ADSK_XREC_PHOTOMETRICLIGHTINFO", AcDb::kForRead);
    if (NULL != xRecord)
        {
        RealDwgXDataUtil::GetIesFileNameFromWebLight (iesFileName, xRecord, dwgLight->database());
        xRecord->close ();
        }
    if (iesFileName.isEmpty())
        return  false;

    // if file is found from original path, look no further:
    struct __stat64 statBuf;
    if (0 == _wstat64 (iesFileName.kwszPtr(), &statBuf))
        return  true;

    // perhaps ies file is on the same folder as DWG?
    WString         dev, path, name, ext, fullSpec;
    BeFileName::ParseName (NULL, NULL, &name, &ext, iesFileName.kwszPtr());
    BeFileName::ParseName (&dev, &path, NULL, NULL, dwgFileName);
    BeFileName::BuildName (fullSpec, dev.GetWCharCP(), path.GetWCharCP(), name.GetWCharCP(), ext.GetWCharCP());
    if (0 == _wstat64 (fullSpec.c_str(), &statBuf))
        iesFileName = AcString (fullSpec.c_str());

    // otherwise leave original path there.
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool                 IsLightGlyphDisplayed (const AcDbLight* dwgLight) const
    {
    switch (dwgLight->glyphDisplay())
        {
        case AcDbLight::kGlyphDisplayOn:            return  true;
        case AcDbLight::kGlyphDisplayOff:           return  false;
        case AcDbLight::kGlyphDisplayAuto:          return  0 != dwgLight->database()->lightGlyphDisplay ();
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/10
+---------------+---------------+---------------+---------------+---------------+------*/
double               GetLightGlyphSize (const AcDbLight* dwgLight) const
    {
    // find light glyph size from drawable bounds.  Return 0 to "turn off" the glyph.
    double      glyphSize = 0.0;

    if (IsLightGlyphDisplayed(dwgLight))
        {
        /*-------------------------------------------------------------------------------
        FUTUREWORK: getGeomExtents does not return actual size of light but there are no
        better ways to get the right size at the moment.  The drawable does not work -
        WorldDraw also produced wrong size of geometries.  The diagnal size of the extents
        appears related to portion of viewport size.
        -------------------------------------------------------------------------------*/
        AcDbExtents         extents;
        if (Acad::eOk == dwgLight->getGeomExtents(extents))
            {
            AcGeVector3d    diagnal = extents.maxPoint() - extents.minPoint();
            glyphSize = diagnal.length ();

            AcDbViewportTableRecord*  viewport = NULL;
            if (RealDwgSuccess == RealDwgUtil::OpenActiveViewportTableRecord(viewport, dwgLight->database()))
                {
                //Light size changes dynamically as view size changes. 
                //0.02 factor shows the approximate equal light size as visible in ACAD    
                glyphSize *= 0.02 * viewport->height();
                viewport->close ();
                }
            else
                {
                glyphSize *= 20;
                }
            }
        }

    return  glyphSize;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
static double   UnitAreaFromLightingUnits (UnitDefinitionCR targetUnits, const int& lightingUnits)
    {
    /*-----------------------------------------------------------------------------------
    For American lighting units (foot-candles), convert square target units to square feet.
    For SI lighting units (lux), convert sqaure target units to square meters.
    -----------------------------------------------------------------------------------*/
    StandardUnit    sourceUnitNumber = LIGHTINGUNITS_American == lightingUnits ? StandardUnit::EnglishFeet : StandardUnit::MetricMeters;

    if (sourceUnitNumber == targetUnits.IsStandardUnit())
        return  1.0;

    double          scale = 1.0;
    if (BSISUCCESS == targetUnits.GetConversionFactorFrom(scale, UnitDefinition::GetStandardUnit(sourceUnitNumber)))
        scale *= scale;

    return  scale;
    }

};  // ToDgnExtLight




/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtLight : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    LightElementPtr     dgnLight = LightElement::LoadFromElement (elemHandle.GetElementRef());
    if (!dgnLight.IsValid())
        return  MstnElementUnacceptable;

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbLight::desc());

    AcDbLight*          dwgLight = AcDbLight::cast (acObject);
    Acad::ErrorStatus   errorStatus = dwgLight->setName (dgnLight->GetName().GetWCharCP());
    if (Acad::eOk != errorStatus)
        return  CantCreateLight;

    DPoint3d            origin;
    context.GetTransformFromDGN().Multiply (origin, dgnLight->GetOrigin());
    errorStatus = dwgLight->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(origin));
    if (Acad::eOk != errorStatus)
        return  CantCreateLight;

    DVec3d              lightVector = DVec3d::From(0, 0, 0);
    UInt32              nBulbs = 0;
    if (RealDwgSuccess != this->SetDwgLightTypeFromDgnLight(dwgLight, lightVector, nBulbs, dgnLight.get(), context))
        return  CantCreateLight;

    if (lightVector.Magnitude() > TOLERANCE_VectorEqual)
        {
        dwgLight->setHasTarget (true);
        dwgLight->setLightDirection (RealDwgUtil::GeVector3dFromDVec3d(lightVector));
        dwgLight->setTargetLocation (dwgLight->position() + RealDwgUtil::GeVector3dFromDVec3d(lightVector));
        }
    else
        {
        dwgLight->setHasTarget (false);
        }

    dwgLight->setOn (dgnLight->IsEnabled());
    dwgLight->setLightColor (RealDwgUtil::AcCmColorFromRGBFactor(dgnLight->GetColor()));

    // intensity:
    double  totalIntensity = dgnLight->GetIntensity ();
    if (nBulbs > 1)
        totalIntensity *= nBulbs;
    dwgLight->setIntensity (totalIntensity);

    /*-----------------------------------------------------------------------------------
    DGN lights are always photometric, but DWG can be either photometric or standard.  
    We set LIGHTINGUNITS=2 in seed.dwg as default for DGN to DWG save.
    -----------------------------------------------------------------------------------*/
    int     lightingUnits = context.GetFileHolder().GetDatabase()->lightingUnits ();
    if (LIGHTINGUNITS_Generic != lightingUnits)
        {
        if (RealDwgSuccess == this->SetDwgPresetLampFromDgnLight(dwgLight, dgnLight.get()))
            {
            dwgLight->setLampColorType (AcDbLight::kKelvin);
            dwgLight->setLampColorTemp (dgnLight->GetTemperatureInKelvin());
            }

        // retain default intensity units:
        switch (dwgLight->physicalIntensityMethod())
            {
            case AcDbLight::kFlux:                  // lumen
                dwgLight->setPhysicalIntensity (dgnLight->GetBrightness());
                break;
            case AcDbLight::kIlluminance:           // lux or foot-candles
                dwgLight->setPhysicalIntensity (this->ConvertLumenToCandelas(dgnLight.get()) * ToDgnExtLight::UnitAreaFromLightingUnits(context.GetTargetUnits(), lightingUnits));
                break;
            case AcDbLight::kPeakIntensity:         // candelas
            default:
                dwgLight->setPhysicalIntensity (this->ConvertLumenToCandelas(dgnLight.get()));
                break;
            }
        }

    // attenuation:
    AcGiLightAttenuation    attenuation = dwgLight->lightAttenuation ();
    if (dgnLight->DoesLightAttenuate())
        {
        attenuation.setAttenuationType (AcGiLightAttenuation::kInverseSquare);
        attenuation.setUseLimits (true);
        attenuation.setLimits (0.0, dgnLight->GetAttenuationDistance() * context.GetScaleFromDGN());
        }
    else
        {
        attenuation.setAttenuationType (AcGiLightAttenuation::kNone);
        }
    dwgLight->setLightAttenuation (attenuation);

    // shadows:
    AcGiShadowParameters    dwgShadow;
    dwgLight->shadowParameters (dwgShadow);
    dwgShadow.setShadowsOn (dgnLight->CastsShadows());
    dwgShadow.setShadowSamples (dgnLight->GetShadowSamples());
    dwgShadow.setShadowMapSize (dgnLight->GetDeepShadowSamples());
    if (AdvancedLight::SHADOWTYPE_DeepShadowMap == dgnLight->GetShadowType())
        dwgShadow.setShadowType (AcGiShadowParameters::kShadowMaps);
    else if (AcGiShadowParameters::kShadowsRayTraced != dwgShadow.shadowType() && AcGiShadowParameters::kAreaSampled != dwgShadow.shadowType())
        dwgShadow.setShadowType (AcGiShadowParameters::kShadowsRayTraced);
    errorStatus = dwgLight->setShadowParameters (dwgShadow);

    // glyph display
    MSElementCP inElement = elemHandle.GetElementCP ();
    Int64       cellSize = inElement->hdr.dhdr.range.xhighlim - inElement->hdr.dhdr.range.xlowlim;
    Int64       deltaY   = inElement->hdr.dhdr.range.yhighlim - inElement->hdr.dhdr.range.ylowlim;
    Int64       deltaZ   = inElement->hdr.dhdr.range.zhighlim - inElement->hdr.dhdr.range.zlowlim;
    if (cellSize < deltaY)
        cellSize = deltaY;
    if (cellSize < deltaZ)
        cellSize = deltaZ;
    if (cellSize > INVISIBLELIGHTGLYPH_UORS)
        {
        // only if light glyph is not displayed, we turn it on:
        if (context.GetFileHolder().GetDatabase()->lightGlyphDisplay() && AcDbLight::kGlyphDisplayOff == dwgLight->glyphDisplay())
            dwgLight->setGlyphDisplay (AcDbLight::kGlyphDisplayAuto);
        }
    else
        {
        dwgLight->setGlyphDisplay (AcDbLight::kGlyphDisplayOff);
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetDwgLightTypeFromDgnLight (AcDbLight* dwgLight, DVec3dR lightVector, UInt32& nBulbs, LightElementCP dgnLight, ConvertFromDgnContextR context) const
    {
    Acad::ErrorStatus   errorStatus = Acad::eOk;

    dwgLight->setHasTarget (false);

    // if a valid IES file is used, it is a web light.
    WStringCR           iesFile = dgnLight->GetIesFileName();
    if (dgnLight->UsesIesData() && !iesFile.empty())
        {
        errorStatus = dwgLight->setLightType (AcGiDrawable::kWebLight);
        if (Acad::eOk == errorStatus)
            {
            AcDbDatabase*   database = context.GetFileHolder().GetDatabase ();
            if (LIGHTINGUNITS_Generic == database->lightingUnits())
                database->setLightingUnits (LIGHTINGUNITS_International);
            }
        dwgLight->setWebFile (iesFile.GetWCharCP());

        AcGeVector3d        iesDirection(0, 0, dgnLight->GetIesRotation());
        dwgLight->webRotation (iesDirection);
        }
    else
        {
        switch (dgnLight->GetType())
            {
            case Light::LIGHTTYPE_Point:
                {
                errorStatus = dwgLight->setLightType (AcGiDrawable::kPointLight);
                break;
                }
            case Light::LIGHTTYPE_Spot:
                {
                errorStatus = dwgLight->setLightType (AcGiDrawable::kSpotLight);

                SpotLightCP spotLight = dynamic_cast <SpotLightCP> (dgnLight);
                if (NULL == spotLight)
                    return  WrongMstnElementType;

                dwgLight->setHotspotAndFalloff (spotLight->GetInnerAngleInRadians(), spotLight->GetOuterAngleInRadians());
                lightVector.Init (spotLight->GetDirection());
                nBulbs = spotLight->GetBulbCount ();
                break;
                }
            case Light::LIGHTTYPE_Distant:
                {
                errorStatus = dwgLight->setLightType (AcGiDrawable::kDistantLight);

                DistantLightCP distantLight = dynamic_cast <DistantLightCP> (dgnLight);
                if (NULL == distantLight)
                    return  WrongMstnElementType;

                lightVector.Init (distantLight->GetDirection());
                break;
                }
            default:
                errorStatus = Acad::eNotImplementedYet;
            }
        }

    return Acad::eOk == errorStatus ? RealDwgSuccess : CantCreateLight;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetDwgPresetLampFromDgnLight (AcDbLight* dwgLight, LightElementCP dgnLight) const
    {
    WStringCR   presetName = dgnLight->GetPresetName ();
    if (!presetName.empty())
        {
        int     index = 0;
        WString dwgLampName;

        while (SUCCESS == RmgrResource::LoadWString(dwgLampName, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgPresetLights, index++))
            {
            if (0 == presetName.compare(dwgLampName))
                {
                dwgLight->setLampColorType (AcDbLight::kPreset);
                dwgLight->setLampColorPreset ((AcDbLight::LampColorPreset)index);

                return  RealDwgSuccess;
                }
            }
        }

    return  CantCreateLight;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertLumenToCandelas (LightElementCP dgnLight) const
    {
    if (!dgnLight->UsesIesData() || dgnLight->GetIesFileName().empty())
        {
        Light::LightType    type = dgnLight->GetType ();
        if (Light::LIGHTTYPE_Point == type || Light::LIGHTTYPE_Distant == type)
            {
            return  LUMENTOCANDELAS(dgnLight->GetBrightness());
            }
        else if (Light::LIGHTTYPE_Spot == type )
            {
            SpotLightCP     spotLight = dynamic_cast <SpotLightCP> (dgnLight);
            if (NULL == spotLight || spotLight->GetInnerAngleInRadians() < TOLERANCE_ArcAngle)
                return  1.0;
            return  dgnLight->GetBrightness() / spotLight->GetInnerAngleInRadians();
            }
        }

    return  0.001;
    }

};  // ToDwgExtLight



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::SynchFlashlight ()
    {
    ElementRefP         headerElemref = m_model->FindByFilePos (0, true);
    if (NULL == headerElemref || headerElemref->IsEOF())
        return;

    MSElementDescrP     pHeaderDescr = NULL;
    if (BSISUCCESS != headerElemref->GetElementDescr(pHeaderDescr, m_model, false))
        return;

    if (DGNFIL_HEADER_ELM == pHeaderDescr->el.ehdr.type)
        {
        Tcb* pTcb = (Tcb *) ((Dgn_header *) &pHeaderDescr->el)->tcbinfo;

        // AutoCAD does not have a flash light that is exposed to the user - but they seem to have a full intensity
        // default light that is essentially a flashlight, but enabled only if other sources are not present.
        pTcb->flashIntensity.red = pTcb->flashIntensity.green = pTcb->flashIntensity.blue = pTcb->flashIntensityScale = 1.0;
        pTcb->renderFlags.flashLight = !m_lightSourceFound;
        dgnModel_replaceElemDscrEx (m_model, pHeaderDescr, 0, true, false);
        }

    pHeaderDescr->Release ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static double   AzimuthFromNorthDirection (double northRadians)
    {
    /*-----------------------------------------------------------------------------------
    DWG's north direction goes on the opposite direction against our Azimuth angle, which
    spans from -180 to 180 degrees.
    -----------------------------------------------------------------------------------*/
    double  northDegrees = -northRadians * msGeomConst_degreesPerRadian;

    while (northDegrees < -180.0)
        northDegrees += 360;
    while (northDegrees > 180.0)
        northDegrees -= 360;

    return  northDegrees;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static double   AzimuthToNorthDirection (double northDegrees)
    {
    northDegrees = -northDegrees;

    while (northDegrees > 360.0)
        northDegrees -= 360;
    while (northDegrees < 0.0)
        northDegrees += 360;

    return northDegrees * msGeomConst_radiansPerDegree;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
void            SaveSunlightToSolarlight (LightSetupR lightSetup, const AcDbSun& sun, ConvertToDgnContext& context)
    {
    AcDbDatabase*   database = context.GetFileHolder().GetDatabase ();
    SolarLightR     solar = lightSetup.GetSolarLightR ();

    solar.SetIsEnabled (sun.isOn());

    // solar time
    AcDbDate        date = sun.dateTime ();
    solar.SetSolarType (SolarLight::SOLARTYPE_TimeLocation);
    solar.SetYear (date.year());
    solar.SetMonth (date.month() - 1);
    solar.SetDay (date.day());
    solar.SetHour (date.hour());
    solar.SetMinute (date.minute());
    solar.SetUseDaylightSavings (sun.isDayLightSavingsOn());
    solar.SetGmtOffset (AcDb::kGMT == database->timeZone() ? 0.0 : (double)database->timeZone()/1000.0);

    // solar direction, intensity and color
    // Supposedly we do not need directions when we use solar time, but since we are populating the TCB we might as well do it:
    GeoPoint2dR     geoCoord = solar.GetGeoLocationR ();
    geoCoord.latitude = database->latitude ();
    geoCoord.longitude = database->longitude ();
    solar.SetAzimuthAngle (AzimuthFromNorthDirection(database->northDirection()));

#ifdef NEED_SOLAR_DIRECTION
    AcGeVector3d        sunVector = sun.sunDirection ();
    sunVector.normalize ();
    pTcb->solarDirection = RealDwgUtil::DPoint3dFromGeVector3d (pTcb->solarDirection, sunVector);

    solarLight_extractSunDirection (&pTcb->solarDirection, NULL, NULL,
                                    pTcb->solarYear,
                                    pTcb->solarTime.month,
                                    pTcb->solarTime.day,
                                    pTcb->solarTime.hour,
                                    pTcb->solarTime.minute,
                                    database->longitude()*msGeomConst_radiansPerDegree,
                                    database->latitude()*msGeomConst_radiansPerDegree + database->northDirection(),
                                    context.GetModel()
                                    );
#endif

    bool                isPhotometric = LIGHTINGUNITS_Generic != sun.database()->lightingUnits();
    RgbFactor           sunColor = RGBFACTOR_SunColor, groundColor = RGBFACTOR_GroundColor;

    solar.SetIntensity (sun.intensity () / 100.0);
    solar.GetColorR() = isPhotometric ? sunColor : context.GetRGBFactor(sun.sunColor().entityColor());
    solar.SetIsColorPhysicallyBased (isPhotometric);

    AcGiSkyParameters   skyParams;
    SkyDomeLightR       skyDome = lightSetup.GetSkyDomeLightR ();
    if (Acad::eOk == sun.skyParameters(skyParams))
        {
        skyDome.SetIsEnabled (true);
        skyDome.SetIsColorPhysicallyBased (isPhotometric);
        // haze ranges from 0-15, solor turbidity ranges from 0-5.
        solar.SetTurbidity (skyParams.haze() / 3.0);
        /*-------------------------------------------------------------------------------
        There isn't a match for sky intensity scale.  I'd like approximate it with using
        inverse skyCloudiness but sky intensity scale has no range while sky cloudiness
        ranges from 0-1.
        mpInfo->v3.skyCloudiness = 1.0 / skyParams.intensityFactor ();

        Set ground color (though Paul said it may not be used).
        mpInfo->v3.groundColor = context.GetRGBFactor(skyParams.groundColor().entityColor());
        -------------------------------------------------------------------------------*/
        }
    else
        {
        skyDome.SetIsEnabled (false);
        }

    // shadows
    AcGiShadowParameters    dwgShadow = sun.shadowParameters ();
    solar.SetCastsShadows (dwgShadow.shadowsOn());
    skyDome.SetCastsShadows (dwgShadow.shadowsOn());
    switch (dwgShadow.shadowType())
        {
        case AcGiShadowParameters::kShadowsRayTraced:
            solar.SetShadowType (AdvancedLight::SHADOWTYPE_RayTrace);
            solar.SetShadowSamples (Light::SHADOWQUALITY_Sharp);
            solar.SetShadowQuality (Light::SHADOWQUALITY_Sharp);
            break;
        case AcGiShadowParameters::kShadowMaps:
            solar.SetShadowType (AdvancedLight::SHADOWTYPE_DeepShadowMap);
            solar.SetShadowQuality (Light::SHADOWQUALITY_Custom);
            solar.SetShadowSamples (dwgShadow.shadowMapSize());
            break;
        case AcGiShadowParameters::kAreaSampled:
            solar.SetShadowSamples (dwgShadow.shadowSamples());
            // need to support dwgShadow.shadowMapSoftness ();
            break;
        }
    solar.SetDeepShadowSamples (dwgShadow.shadowMapSize());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveGlobalLightsFromActiveVportToDgn ()
    {
    // find active viewport from viewport table
    AcDbDatabase*               database = this->GetFileHolder().GetDatabase ();
    AcDbViewportTablePointer    vpTable (database->viewportTableId(), AcDb::kForRead);
    
    AcDbViewportTableRecord*    viewport = NULL;
    
    if (RealDwgSuccess != RealDwgUtil::OpenActiveViewportTableRecord(viewport, database))
        return  CantCreateLight;

    // get active/TCB light setup
    DgnModelP       model = this->GetModel ();
    LightManagerR   lightManager = LightManager::GetManagerR ();
    LightSetupPtr   lightSetup = lightManager.LoadSetupFromModel (L"", *model);
    if (!lightSetup.IsValid())
        return  CantCreateLight;

    // set ambient light
    AmbientLightR   ambientLight = lightSetup->GetAmbientLightR ();
    ambientLight.SetIntensity (viewport->brightness());

    RgbFactor&      color = ambientLight.GetColorR ();
    color = this->GetRGBFactor (viewport->ambientLightColor().entityColor());
    ambientLight.SetIsEnabled (0.0 != color.red && 0.0 != color.green && 0.0 != color.blue);

    // turn off solar light by default
    lightSetup->GetSolarLightR().SetIsEnabled (false);

    // when default lighting is on, which is a distant light source, all other lights become inactive.
    if (viewport->isDefaultLightingOn())
        {
        // default AutoCAD represents these with standard distant lights.
        // FUTUREWORK: find two lights if (AcGiViewportTraits::kTwoDistantLights == viewport->defaultLightingType())
        ambientLight.SetIsEnabled (true);
        ambientLight.SetIntensity (1.0);
        viewport->close ();
        lightManager.StoreLightSetupToLegacySettings (*model, *lightSetup.get());
        return  RealDwgSuccess;
        }

    // if the viewport has a sun, set solor light from sun light:
    AcDbSmartObjectPointer<AcDbSun>  sun(viewport->sunId(), AcDb::kForRead);
    if (Acad::eOk == sun.openStatus())
        {
        SaveSunlightToSolarlight (*lightSetup.get(), *sun, *this);
        m_lightSourceFound = true;
        viewport->close ();
        lightManager.StoreLightSetupToLegacySettings (*model, *lightSetup.get());
        return  RealDwgSuccess;
        }

    viewport->close ();

    lightManager.StoreLightSetupToLegacySettings (*model, *lightSetup.get());

    return  CantFindSunLight;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static Adesk::UInt16    ShadowMapSizeInPixels (UInt16 resolutionIn)
    {
    if      (resolutionIn <= 64)    return  64;
    else if (resolutionIn <= 128)   return  128;
    else if (resolutionIn <= 256)   return  256;
    else if (resolutionIn <= 512)   return  512;
    else if (resolutionIn <= 1024)  return  1024;
    else if (resolutionIn <= 2048)  return  2048;
    else if (resolutionIn >= 4069)  return  4096;
    return  DEFAULT_SHADOW_RESOLUTION;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::ExtractSunLightFromActiveSolarLight (AcDbSun* sun, AcDbDatabaseP database)
    {
    if (nullptr == sun || nullptr == database)
        return  NullObject;

    LightManagerR   lightManager = LightManager::GetManagerR ();
    LightSetupCP    lightSetup = lightManager.GetActiveLightSetupForModel (false, *m_model);
    if (NULL == lightSetup)
        return  CantAccessMstnElement;

    SolarLightCR    solar = lightSetup->GetSolarLight ();

    /*-----------------------------------------------------------------------------------
    DGN has one solar light per file, whereas DWG can have many sun objects, separately 
    owned by viewport or view obejcts.  Hence, we extract solar light from TCB and create 
    a non-database resident object of sun, which later on will be added to affected model 
    viewports via viewport's FromElement method.
    -----------------------------------------------------------------------------------*/

    // geo coordinates
    GeoPoint2d      geoCoord = solar.GetGeoLocation ();
    database->setLatitude (geoCoord.latitude);
    database->setLongitude (geoCoord.longitude);
    database->setNorthDirection (AzimuthToNorthDirection(m_model->GetModelInfo().GetAzimuth()));

    /*-----------------------------------------------------------------------------------
    date & time:
    ACAD does not support solar direction.  AcDbSun::setSunDirection, setAltitude and
    setAzimuth are all for internal use only.  They do not seem to have any effect when
    used.  This leaves us with little choice but to always set date & time for the sun.
    Consequently this can produce incorrect result when tcb->renderFlags.solarByVector==true.
    -----------------------------------------------------------------------------------*/
    AcDbDate        date;
    date.setDate (solar.GetMonth() + 1, solar.GetDay(), solar.GetYear());
    date.setTime (solar.GetHour(), solar.GetMinute(), 0, 0);
    sun->setDateTime (date);
    sun->setDayLightSavingsOn (solar.UseDaylightSavings());
    if (AcDb::kGMT != database->timeZone())
        database->setTimeZoneAsUtcOffset (solar.GetGmtOffset());
    
    // sun position
    double  azimuth = 0.0, altitude = 0.0;
    DVec3d  solarDirection;
    if (SolarLight::SOLARTYPE_Direction == solar.GetType())
        {
        solarDirection = solar.GetVectorOverride ();
        SolarUtility::DirectionToAzimuthAngles (azimuth, altitude, solarDirection, *m_model);
        sun->setSunDirection (RealDwgUtil::GeVector3dFromDPoint3d(solarDirection));
        }
    else
        {
        solarDirection = SolarUtility::GetSolarDirection (solar.GetYear(), solar.GetMonth(), solar.GetDay(), 
                                                          solar.GetHour(), solar.GetMinute(), solar.UseDaylightSavings(),
                                                          solar.GetGmtOffset(), solar.GetGeoLocation(), *m_model, 
                                                          &azimuth, &altitude);
        }
    sun->setAltitude (altitude);
    sun->setAzimuth (azimuth);

    // sun intensity & color
    sun->setOn (solar.IsEnabled() && 0.0 != solarDirection.magnitude());
    sun->setIntensity (solar.GetIntensity() * 100.0);

    bool        isPhotometric = LIGHTINGUNITS_Generic != database->lightingUnits();
    RgbFactor   sunColor = RGBFACTOR_SunColor, groundColor = RGBFACTOR_GroundColor;
    
    sun->setSunColor (RealDwgUtil::AcCmColorFromRGBFactor(isPhotometric ? sunColor : solar.GetColor()));

    // shadows
    AcGiShadowParameters    dwgShadow = sun->shadowParameters ();
    dwgShadow.setShadowsOn (solar.CastsShadows());
    dwgShadow.setShadowType (AdvancedLight::SHADOWTYPE_RayTrace == solar.GetShadowType() ? AcGiShadowParameters::kShadowsRayTraced : AcGiShadowParameters::kShadowMaps);
    dwgShadow.setShadowMapSize (ShadowMapSizeInPixels(solar.GetDeepShadowSamples()));
    sun->setShadowParameters (dwgShadow);

    return  RealDwgSuccess;
    }
