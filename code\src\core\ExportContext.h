#pragma once

#include "../../include/ExportTypes.h"
#include <unordered_map>
#include <memory>
#include <mutex>

namespace IModelExport {

// Forward declarations
class GeometryConverter;
class MaterialManager;
class TextureManager;

//=======================================================================================
// Export Context - Shared state and utilities for export operations
//=======================================================================================

class ExportContext {
public:
    ExportContext();
    ~ExportContext();

    //===================================================================================
    // Geometry Conversion
    //===================================================================================

    // Get geometry converter
    std::shared_ptr<GeometryConverter> GetGeometryConverter() const;
    void SetGeometryConverter(std::shared_ptr<GeometryConverter> converter);

    // Coordinate system transformation
    void SetCoordinateTransform(const Transform3d& transform);
    Transform3d GetCoordinateTransform() const;
    Point3d TransformPoint(const Point3d& point) const;
    Vector3d TransformVector(const Vector3d& vector) const;

    // Units conversion
    void SetUnitsConversion(double sourceUnitsPerMeter, double targetUnitsPerMeter);
    double GetUnitsScale() const;
    double ConvertLength(double length) const;

    //===================================================================================
    // Material and Texture Management
    //===================================================================================

    // Material management
    std::shared_ptr<MaterialManager> GetMaterialManager() const;
    void SetMaterialManager(std::shared_ptr<MaterialManager> manager);

    // Texture management
    std::shared_ptr<TextureManager> GetTextureManager() const;
    void SetTextureManager(std::shared_ptr<TextureManager> manager);

    // Material caching
    std::string RegisterMaterial(const Material& material);
    bool GetMaterial(const std::string& materialId, Material& material) const;
    std::vector<std::string> GetAllMaterialIds() const;

    //===================================================================================
    // Element Tracking and Mapping
    //===================================================================================

    // Element ID mapping (iModel ID -> Export format ID)
    void MapElementId(const std::string& imodelId, const std::string& exportId);
    bool GetMappedElementId(const std::string& imodelId, std::string& exportId) const;
    std::unordered_map<std::string, std::string> GetAllElementMappings() const;

    // Element processing tracking
    void MarkElementProcessed(const std::string& imodelId);
    bool IsElementProcessed(const std::string& imodelId) const;
    void ClearProcessedElements();

    // Element statistics
    void IncrementProcessedCount();
    void IncrementSkippedCount();
    void IncrementErrorCount();
    size_t GetProcessedCount() const;
    size_t GetSkippedCount() const;
    size_t GetErrorCount() const;
    void ResetStatistics();

    //===================================================================================
    // Hierarchy and Relationships
    //===================================================================================

    // Parent-child relationships
    void SetElementParent(const std::string& childId, const std::string& parentId);
    bool GetElementParent(const std::string& childId, std::string& parentId) const;
    std::vector<std::string> GetElementChildren(const std::string& parentId) const;

    // Element grouping
    void AddElementToGroup(const std::string& groupName, const std::string& elementId);
    std::vector<std::string> GetGroupElements(const std::string& groupName) const;
    std::vector<std::string> GetAllGroups() const;

    //===================================================================================
    // Progress and Status
    //===================================================================================

    // Progress tracking
    void SetTotalElements(size_t total);
    size_t GetTotalElements() const;
    double GetProgressPercentage() const;

    // Current operation
    void SetCurrentOperation(const std::string& operation);
    std::string GetCurrentOperation() const;

    // Cancellation support
    void SetCancelled(bool cancelled);
    bool IsCancelled() const;

    //===================================================================================
    // Error and Warning Management
    //===================================================================================

    // Error tracking
    void AddError(const std::string& elementId, const std::string& error);
    void AddWarning(const std::string& elementId, const std::string& warning);
    std::vector<std::string> GetErrors() const;
    std::vector<std::string> GetWarnings() const;
    std::vector<std::string> GetElementErrors(const std::string& elementId) const;
    std::vector<std::string> GetElementWarnings(const std::string& elementId) const;

    // Error handling strategy
    enum class ErrorHandling {
        StopOnError,        // Stop export on first error
        SkipOnError,        // Skip problematic elements
        ContinueOnError     // Continue with best effort
    };
    
    void SetErrorHandling(ErrorHandling strategy);
    ErrorHandling GetErrorHandling() const;

    //===================================================================================
    // Caching and Performance
    //===================================================================================

    // Generic caching interface
    template<typename T>
    void CacheValue(const std::string& key, const T& value);
    
    template<typename T>
    bool GetCachedValue(const std::string& key, T& value) const;
    
    void ClearCache();
    size_t GetCacheSize() const;

    // Memory management
    void SetMemoryLimit(size_t limitBytes);
    size_t GetMemoryLimit() const;
    size_t GetMemoryUsage() const;
    void TrimMemory();

    //===================================================================================
    // Configuration and Settings
    //===================================================================================

    // Generic settings storage
    void SetSetting(const std::string& key, const std::string& value);
    std::string GetSetting(const std::string& key, const std::string& defaultValue = "") const;
    bool HasSetting(const std::string& key) const;
    void RemoveSetting(const std::string& key);

    // Tolerance settings
    void SetGeometryTolerance(double tolerance);
    double GetGeometryTolerance() const;
    void SetAngleTolerance(double tolerance);
    double GetAngleTolerance() const;

    //===================================================================================
    // Threading Support
    //===================================================================================

    // Thread safety
    void Lock() const;
    void Unlock() const;
    bool TryLock() const;

    // Thread-local storage
    void SetThreadLocalData(const std::string& key, std::shared_ptr<void> data);
    std::shared_ptr<void> GetThreadLocalData(const std::string& key) const;

private:
    // Core components
    std::shared_ptr<GeometryConverter> m_geometryConverter;
    std::shared_ptr<MaterialManager> m_materialManager;
    std::shared_ptr<TextureManager> m_textureManager;

    // Transformation and units
    Transform3d m_coordinateTransform;
    double m_unitsScale;

    // Element tracking
    std::unordered_map<std::string, std::string> m_elementIdMap;
    std::unordered_set<std::string> m_processedElements;
    std::unordered_map<std::string, std::string> m_elementParents;
    std::unordered_map<std::string, std::vector<std::string>> m_elementChildren;
    std::unordered_map<std::string, std::vector<std::string>> m_elementGroups;

    // Statistics
    std::atomic<size_t> m_totalElements{0};
    std::atomic<size_t> m_processedCount{0};
    std::atomic<size_t> m_skippedCount{0};
    std::atomic<size_t> m_errorCount{0};

    // Progress and status
    std::string m_currentOperation;
    std::atomic<bool> m_cancelled{false};

    // Error management
    std::vector<std::string> m_errors;
    std::vector<std::string> m_warnings;
    std::unordered_map<std::string, std::vector<std::string>> m_elementErrors;
    std::unordered_map<std::string, std::vector<std::string>> m_elementWarnings;
    ErrorHandling m_errorHandling{ErrorHandling::SkipOnError};

    // Caching
    std::unordered_map<std::string, std::shared_ptr<void>> m_cache;
    size_t m_memoryLimit{1024 * 1024 * 1024}; // 1GB default

    // Configuration
    std::unordered_map<std::string, std::string> m_settings;
    double m_geometryTolerance{1e-6};
    double m_angleTolerance{1e-6};

    // Threading
    mutable std::recursive_mutex m_mutex;
    thread_local std::unordered_map<std::string, std::shared_ptr<void>> m_threadLocalData;
};

//=======================================================================================
// Template Implementation
//=======================================================================================

template<typename T>
void ExportContext::CacheValue(const std::string& key, const T& value) {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    m_cache[key] = std::make_shared<T>(value);
}

template<typename T>
bool ExportContext::GetCachedValue(const std::string& key, T& value) const {
    std::lock_guard<std::recursive_mutex> lock(m_mutex);
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        auto typedPtr = std::static_pointer_cast<T>(it->second);
        if (typedPtr) {
            value = *typedPtr;
            return true;
        }
    }
    return false;
}

} // namespace IModelExport
