/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdLine.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtLine : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbLine*               pLine = AcDbLine::cast (acObject);
    DSegment3d              lineSeg;

    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], pLine->startPoint());
    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[1], pLine->endPoint());

    if (RealDwgUtil::CoerceInvalidElevation (lineSeg.point[0].z))
        DIAGNOSTIC_PRINTF ("Ignoring invalid line elevation: %g\n", lineSeg.point[0].z);

    if (RealDwgUtil::CoerceInvalidElevation (lineSeg.point[1].z))
        DIAGNOSTIC_PRINTF ("Ignoring invalid line elevation: %g\n", lineSeg.point[1].z);

    context.GetTransformToDGN().Multiply (lineSeg.point, lineSeg.point, 2);
    context.ValidatePoints (lineSeg.point, 2);

    BentleyStatus   status;
    if (BSISUCCESS != (status = LineHandler::CreateLineElement (outElement, NULL, lineSeg, context.GetThreeD(), *context.GetModel())))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    context.ElementHeaderFromEntity (outElement, pLine);
    context.ApplyThickness (outElement, pLine->thickness(), pLine->normal(), false, true);

    return RealDwgSuccess;
    }
};

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtRay : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbRay*                pRay = AcDbRay::cast (acObject);
    DSegment3d              lineSeg;
    DPoint3d                direction;

    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], pRay->basePoint());
    RealDwgUtil::DPoint3dFromGeVector3d (direction, pRay->unitDir());

    context.GetTransformToDGN().Multiply (lineSeg.point[0], lineSeg.point[0]);
    lineSeg.point[1].SumOf (lineSeg.point[0], direction, 1.0E10);

    context.ValidatePoints (lineSeg.point, 2);

    BentleyStatus   status;
    if (BSISUCCESS != (status = LineHandler::CreateLineElement (outElement, NULL, lineSeg, true, *context.GetModel())))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    context.ElementHeaderFromEntity (outElement, pRay);

    ToDgnExtRay::SetInfiniteLineLinkage (outElement, false, true);

    return RealDwgSuccess;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/12
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SetInfiniteLineLinkage (EditElementHandleR outElement, bool start, bool end)
    {
    InfiniteLineLinkage infiniteLineLinkage;
    size_t              totalSize = sizeof (infiniteLineLinkage);
    size_t              dataSize = (sizeof(InfiniteLineLinkage) + 7) & ~7;

    memset (&infiniteLineLinkage, 0, totalSize);

    // set linkage header
    infiniteLineLinkage.hdr.user = true;
    infiniteLineLinkage.hdr.primaryID = LINKAGEID_InfiniteLine;

    LinkageUtil::SetWords (&infiniteLineLinkage.hdr, (int)(dataSize / sizeof(short)));

    // set linkage data
    infiniteLineLinkage.infiniteStart = start;
    infiniteLineLinkage.infiniteEnd = end;

    // append linkage to the line element
    StatusInt   status = outElement.AppendElementLinkage (NULL, infiniteLineLinkage.hdr, &infiniteLineLinkage.hdr + 1);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }
    
};


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtXLine : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbXline*              pXline = AcDbXline::cast (acObject);
    DSegment3d              lineSeg;
    DPoint3d                direction;

    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], pXline->basePoint());
    RealDwgUtil::DPoint3dFromGeVector3d (direction, pXline->unitDir());

    context.GetTransformToDGN().Multiply (lineSeg.point[0], lineSeg.point[0]);

    lineSeg.point[1].SumOf (lineSeg.point[0], direction, 1.0E10);
    lineSeg.point[0].SumOf (lineSeg.point[0], direction, -1.0E10);
    context.ValidatePoints (lineSeg.point, 2);

    BentleyStatus   status;
    if (BSISUCCESS != (status = LineHandler::CreateLineElement (outElement, NULL, lineSeg, true, *context.GetModel())))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    context.ElementHeaderFromEntity (outElement, pXline);

    ToDgnExtRay::SetInfiniteLineLinkage (outElement, true, true);

    return RealDwgSuccess;
    }
};
