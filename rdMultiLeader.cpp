/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdMultiLeader.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveDwgMultileaderStylesToDgn (DimStyleDgnCacheLoaderCollection& dimStyleCollection)
    {
    /*-----------------------------------------------------------------------------------
    MicroStation currently does not have multileader styles yet, so we will use dimstyles 
    with prefix "MultiLeader:" to denote their use for multileaders.
    -----------------------------------------------------------------------------------*/
    AcDbDictionary*     dictionary = NULL;
    if (Acad::eOk != this->GetFileHolder().GetDatabase()->getMLeaderStyleDictionary(dictionary, AcDb::kForRead))
        return CantOpenObject;

    AcDbDictionaryIterator* iterator = dictionary->newIterator();
    for (; !iterator->done(); iterator->next())
        {
        AcDbObjectP         object = NULL;
        AcDbMLeaderStyle*   mleaderStyle = NULL;
        if (Acad::eOk != iterator->getObject(object, AcDb::kForRead) || NULL == (mleaderStyle = AcDbMLeaderStyle::cast(object)))
            {
            DIAGNOSTIC_PRINTF ("Failed opening multileader style dictionary %ls!\n", iterator->name());
            continue;
            }

        DimensionStylePtr   dgnDimstyle;
        if (RealDwgSuccess == this->CreateDgnDimStyleFromMLeaderStyle(dgnDimstyle, mleaderStyle))
            {
            ElementId       elementId = this->ElementIdFromObject (object);

            dimStyleCollection.Add (*dgnDimstyle, elementId, false);

            // add to the list of DWG originated dimension styles.
            this->GetFileHolder().GetDwgOriginatedDimStyles().push_back (elementId);
            }

        object->close ();
        }
    delete iterator;

    dictionary->close ();

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CreateDgnDimStyleFromMLeaderStyle (DimensionStylePtr& dgnDimstyle, AcDbMLeaderStyle* mleaderStyle)
    {
    WString     dimstyleName(DIMSTYLE_MLeaderPrefix);
    ACHAR*      acName = NULL;
    if (Acad::eOk == mleaderStyle->getName(acName))
        {
        dimstyleName += WString (acName);
        acutDelString (acName);
        }
    else
        {
        // handle error
        dimstyleName += WString(L"None");
        }

    // create a new dimstyle from seed
    if (RealDwgSuccess == this->CreateDimensionStyleFromSeed(dgnDimstyle))
        dgnDimstyle->SetName (dimstyleName.c_str());
    else
        dgnDimstyle = DimensionStyle::Create (dimstyleName.c_str(), *this->GetFile());
    
    if (!dgnDimstyle.IsValid())
        return  CantCreateDimensionStyle;

    // convert dimstyle from settings of mleader style
    return this->SetDgnDimstyleFromMLeaderStyle (dgnDimstyle, mleaderStyle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
static DimStyleProp_MLNote_VerAttachment    GetVerticalJustification (AcDbMLeaderStyle* mleaderStyle, AcDbMLeaderStyle::LeaderDirectionType side)
    {
    switch (mleaderStyle->textAttachmentType(side))
        {
        case AcDbMLeaderStyle::kAttachmentTopOfTop:         
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_Top;

        case AcDbMLeaderStyle::kAttachmentBottomOfTopLine:
        case AcDbMLeaderStyle::kAttachmentBottomOfTop:
        case AcDbMLeaderStyle::kAttachmentMiddleOfTop:      
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_TopLine;

        case AcDbMLeaderStyle::kAttachmentCenter:           // center top/center bottom - we don't have this!
        case AcDbMLeaderStyle::kAttachmentMiddle:           
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_Middle;

        case AcDbMLeaderStyle::kAttachmentMiddleOfBottom:
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_BottomLine;

        case AcDbMLeaderStyle::kAttachmentBottomOfBottom:
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_Bottom;

        case AcDbMLeaderStyle::kAttachmentLinedCenter:      // center top & overline or center bottom underline - don't have it!
        case AcDbMLeaderStyle::kAttachmentAllLine:          // underline all texts - we don't have this yet!!
        case AcDbMLeaderStyle::kAttachmentBottomLine:
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_Underline;

        default:    // handle error - should never hit here!
            return  DIMSTYLE_VALUE_MLNote_VerAttachment_Middle;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsHooklineDisplayed (AcDbMLeaderStyle* mleaderStyle, bool isSpline, DimStyleProp_MLNote_VerAttachment vertLeft, DimStyleProp_MLNote_VerAttachment vertRight)
    {
    /*--------------------------------------------------------------------------------------------------------------
    inline leader toggle:
    ACAD does not display hookline for spline leaders, but always displays underlines.  On the other hand, our Note
    does not display underline if the toggle is off.  In the future we will need a new option for note to display 
    underline when hookline is turned off.  For now, best we can do is to compromise these options in the style and
    override it as necessary on the note element.
    --------------------------------------------------------------------------------------------------------------*/
    if (isSpline)
        {
        // if the spline leader does not use underline on either side, do not display the hookline
        if (DIMSTYLE_VALUE_MLNote_VerAttachment_Underline != vertLeft && DIMSTYLE_VALUE_MLNote_VerAttachment_Underline != vertRight)
            return  false;
        // either left or right side uses underlline, but if the spline leader uses the None content type, do not display the hookline
        if (AcDbMLeaderStyle::kNoneContent == mleaderStyle->contentType())
            return  false;
        }

    /*---------------------------------------------------------------------------------------------------------------
    For all other cases, check the landing flags.  There are 2 flags, enableLanding and enableDogleg.  Autodesk has
    told us that enableLanding turns on/off text gap and enableDogleg turns on/off the dogleg (i.e. the hookline).
    ---------------------------------------------------------------------------------------------------------------*/
    return  mleaderStyle->enableDogleg();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::InitDwgCompatibleDimstyle (DimensionStyleP dgnDimstyle)
    {
    if (nullptr == dgnDimstyle)
        return;

    dgnDimstyle->SetDoubleProp (0.5, DIMSTYLE_PROP_Tolerance_TextVerticalMargin_DOUBLE);
    dgnDimstyle->SetDoubleProp (0.5, DIMSTYLE_PROP_Tolerance_TextHorizontalMargin_DOUBLE);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_ArrowType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_StrokeType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_OriginType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_DotType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_FitOption_MoveEither, DIMSTYLE_PROP_General_FitOption_INTEGER);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_General_TightFitTextAbove_BOOLINT);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_General_FitInclinedTextBox_BOOLINT);
    // ball & chain
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_BallAndChain_Mode_None, DIMSTYLE_PROP_BallAndChain_Mode_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_BallAndChain_ChainType_Line, DIMSTYLE_PROP_BallAndChain_ChainType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Terminator_Type_None, DIMSTYLE_PROP_BallAndChain_ChainTerminator_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_BallAndChain_Alignment_Auto, DIMSTYLE_PROP_BallAndChain_Alignment_INTEGER);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_BallAndChain_ShowTextLeader_BOOLINT);
    // texts
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Text_Font_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Text_OverrideWidth_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Text_OverrideUnderline_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Text_Underline_BOOLINT);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_Text_OverrideStackedFractions_BOOLINT);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Text_StackedFractionType_Diagonal, DIMSTYLE_PROP_Text_StackedFractionType_INTEGER);
    dgnDimstyle->SetDoubleProp (1.0, DIMSTYLE_PROP_Tolerance_TextScale_DOUBLE);
    dgnDimstyle->SetDoubleProp (1.0, DIMSTYLE_PROP_Text_StackedFractionScale_DOUBLE);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Text_Justification_CenterRight, DIMSTYLE_PROP_Text_Justification_INTEGER);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_MLNote_ScaleFrame_BOOLINT);
    dgnDimstyle->SetDoubleProp (1.0, DIMSTYLE_PROP_MLNote_FrameScale_DOUBLE);
    // units
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Value_UseWorkingUnits_BOOLINT);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Value_Format_MU, DIMSTYLE_PROP_Value_Format_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Value_Format_MU, DIMSTYLE_PROP_Value_SecFormat_INTEGER);
    UnitDefinition  subUnit;
    dgnDimstyle->GetOneUnitProp (subUnit, DIMSTYLE_PROP_Value_UnitMaster_ONEUNIT);
    dgnDimstyle->SetOneUnitProp (subUnit, DIMSTYLE_PROP_Value_UnitMaster_ONEUNIT);
    dgnDimstyle->SetOneUnitProp (subUnit, DIMSTYLE_PROP_Value_SecUnitSub_ONEUNIT);
    dgnDimstyle->SetStringProp (L"\0", DIMSTYLE_PROP_Value_UnitLabelMaster_MSWCHAR);
    dgnDimstyle->SetStringProp (L"\0", DIMSTYLE_PROP_Value_UnitLabelSub_MSWCHAR);
    dgnDimstyle->SetStringProp (L"\0", DIMSTYLE_PROP_Value_UnitLabelSecMaster_MSWCHAR);
    dgnDimstyle->SetStringProp (L"\0", DIMSTYLE_PROP_Value_UnitLabelSecSub_MSWCHAR);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Value_AltIsActive_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Value_AltSecIsActive_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Symbol_TolPrefixChar_CHAR);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Symbol_TolSuffixChar_CHAR);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Value_ThousandsSep_None, DIMSTYLE_PROP_Value_ThousandsOpts_INTEGER);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_Value_AngleMeasure_BOOLINT);
    // symbology
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Terminator_OverrideColor_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Terminator_OverrideWeight_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Terminator_OverrideLineStyle_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Text_OverrideWeight_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Terminator_OverrideLineStyle_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_Placement_OverrideLevel_BOOLINT);
    dgnDimstyle->SetBooleanProp (false, DIMSTYLE_PROP_General_IgnoreLevelSymbology_BOOLINT);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_CustomType_Default, DIMSTYLE_PROP_Symbol_DiameterType_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_CustomType_Default, DIMSTYLE_PROP_Symbol_PlusMinusType_INTEGER);
    // templates
    for (int intDimType = 1; intDimType < 20; intDimType++)
        {
        DimensionType dimType = static_cast<DimensionType>(intDimType);
        dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Terminator_Type_None, dimType, DIMSTYLE_PROP_Terminator_First_TEMPLATEFLAG);
        dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Terminator_Type_None, dimType, DIMSTYLE_PROP_Terminator_Joint_TEMPLATEFLAG);

        if (DimensionType::Diameter == dimType || DimensionType::DiameterExtended == dimType)
            dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Symbol_Standard_Diameter, dimType, DIMSTYLE_PROP_Symbol_Prefix_TEMPLATEFLAG);
        else if (DimensionType::Radius == dimType || DimensionType::RadiusExtended == dimType)
            dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Symbol_Standard_Radius, dimType, DIMSTYLE_PROP_Symbol_Prefix_TEMPLATEFLAG);
        else
            dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Symbol_Standard_None, dimType, DIMSTYLE_PROP_Symbol_Prefix_TEMPLATEFLAG);

        dgnDimstyle->SetTemplateFlagProp (DIMSTYLE_VALUE_Symbol_Standard_None, dimType, DIMSTYLE_PROP_Symbol_Suffix_TEMPLATEFLAG);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SetDgnDimstyleFromMLeaderStyle (DimensionStylePtr& dgnDimstyle, AcDbMLeaderStyle* mleaderStyle)
    {
    // set text style ID and get text width factor:
    AcDbTextStyleTableRecordPointer     textstyle(mleaderStyle->textStyleId(), AcDb::kForRead);
    double                              textAspectRatio = 1.0;
    bool                                isTextheightFromTextstyle = false;
    if (Acad::eOk == textstyle.openStatus())
        {
        UInt32      textstyleEntryId = this->GetFileHolder().GetTextStyleIndex()->GetDgnId (textstyle->objectId().handle());
        if (0 != textstyleEntryId)
            {
            DgnTextStylePtr textStyle = DgnTextStyle::GetByID(textstyleEntryId, *dgnDimstyle->GetFile());
            if (textStyle.IsValid())
                dgnDimstyle->SetTextStyleProp(*textStyle, DIMSTYLE_PROP_Text_TextStyle_TEXTSTYLE);
            }

        textAspectRatio = textstyle->xScale ();
        isTextheightFromTextstyle = textstyle->textSize() > TOLERANCE_TextHeight;
        }

    // initialize DWG style after setting text style, which resets text params:
    this->InitDwgCompatibleDimstyle (dgnDimstyle.get());

    double      effectiveTextHeight = mleaderStyle->textHeight ();

    // ensure a non-zero text height:
    if (effectiveTextHeight < TOLERANCE_ZeroSize)
        {
        effectiveTextHeight = this->GetFileHolder().GetDatabase()->textsize ();
        if (effectiveTextHeight < TOLERANCE_ZeroSize)
            effectiveTextHeight = 0.18;
        }

    // set effective value of the text hight
    dgnDimstyle->SetDistanceProp (effectiveTextHeight * this->GetScaleToDGN(), DIMSTYLE_PROP_Text_Height_DISTANCE, m_model);

    // set text width from aspect ratio
    double      textwidth = effectiveTextHeight * textAspectRatio * this->GetScaleToDGN();
    dgnDimstyle->SetDistanceProp (textwidth, DIMSTYLE_PROP_Text_Width_DISTANCE, m_model);

    // set dimscale
    double      dimScale = mleaderStyle->scale ();
    if (dimScale < TOLERANCE_ZeroScale)
        dimScale = 1.0;

    dgnDimstyle->SetDoubleProp (dimScale, DIMSTYLE_PROP_Placement_AnnotationScale_DOUBLE);
    dgnDimstyle->SetBooleanProp (!isTextheightFromTextstyle, DIMSTYLE_PROP_Text_OverrideHeight_BOOLINT);
    dgnDimstyle->SetBooleanProp (!mleaderStyle->annotative(), DIMSTYLE_PROP_Placement_UseStyleAnnotationScale_BOOLINT);

    // terminator settings
    double      termSize = mleaderStyle->arrowSize() / effectiveTextHeight;
    dgnDimstyle->SetDoubleProp (termSize, DIMSTYLE_PROP_Terminator_Width_DOUBLE);
    dgnDimstyle->SetDoubleProp (termSize/3.0, DIMSTYLE_PROP_Terminator_Height_DOUBLE);
    dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_Terminator_UniformCellScale_BOOLINT);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Terminator_Type_Note, DIMSTYLE_PROP_Terminator_Note_INTEGER);
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Terminator_Arrowhead_Filled, DIMSTYLE_PROP_Terminator_Arrowhead_INTEGER);
    dgnDimstyle->SetTemplateFlagProp (true, DimensionType::SizeArrow, DIMSTYLE_PROP_Terminator_Right_TEMPLATEFLAG);
    dgnDimstyle->SetBooleanProp(true, DIMSTYLE_PROP_Terminator_SuppressLargeTerminator_BOOLINT);

    const ACHAR*                blockName = NULL;
    AcDbBlockTableRecordPointer termBlock(mleaderStyle->arrowSymbolId(), AcDb::kForRead);
    if (Acad::eOk == termBlock.openStatus() && Acad::eOk == termBlock->getName(blockName))
        {
        // user terminator
        dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Cell, DIMSTYLE_PROP_Terminator_NoteType_INTEGER);
        dgnDimstyle->SetStringProp (blockName, DIMSTYLE_PROP_Terminator_NoteCellName_MSWCHAR);
        dgnDimstyle->SetBooleanProp (!RealDwgUtil::IsBlockThroughlineTerminator(blockName), DIMSTYLE_PROP_Terminator_NoLineThruArrow_BOOLINT);

        // resolve shared cell definition - the terminator block may appear after the parent block of the mleader in the block table.
        if (!this->ElementIdInUse(this->ElementIdFromObject(termBlock)))
            {
            termBlock->close ();
            this->SaveSharedCellDefinitionToDgn (mleaderStyle->arrowSymbolId());
            }
        }
    else
        {
        // default arrowhead
        dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_NoteType_INTEGER);
        dgnDimstyle->SetStringProp (L"", DIMSTYLE_PROP_Terminator_NoteCellName_MSWCHAR);
        dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_Terminator_NoLineThruArrow_BOOLINT);
        }

    // note specific settings
    // text margins
    double      doubleValue = mleaderStyle->landingGap() / effectiveTextHeight;
    dgnDimstyle->SetDoubleProp (doubleValue, DIMSTYLE_PROP_MLNote_LeftMargin_DOUBLE);
    dgnDimstyle->SetDoubleProp (doubleValue, DIMSTYLE_PROP_MLNote_LowerMargin_DOUBLE);
    // make dimension text margins compatible
    dgnDimstyle->SetDoubleProp (doubleValue, DIMSTYLE_PROP_Text_VerticalMargin_DOUBLE);
    dgnDimstyle->SetDoubleProp (doubleValue, DIMSTYLE_PROP_Text_HorizontalMargin_DOUBLE);
    // text frame
    DimStyleProp_MLNote_FrameType       frameType = mleaderStyle->enableFrameText() ? DIMSTYLE_VALUE_MLNote_FrameType_Box : DIMSTYLE_VALUE_MLNote_FrameType_None;
    dgnDimstyle->SetIntegerProp (frameType, DIMSTYLE_PROP_MLNote_FrameType_INTEGER);
    // make dimension text frame type the same as the note frame type
    DimStyleProp_Text_FrameType         dimTextFrame = DIMSTYLE_VALUE_MLNote_FrameType_Box == frameType ? DIMSTYLE_VALUE_Text_FrameType_Box : DIMSTYLE_VALUE_Text_FrameType_None;
    dgnDimstyle->SetIntegerProp (dimTextFrame, DIMSTYLE_PROP_Text_FrameType_INTEGER);

    // text rotation/angle type: inline or horizontal, and only effective if landing is turned off.  Vertical is not an ACAD's style property.
    DimStyleProp_MLNote_TextRotation    rotation = DIMSTYLE_VALUE_MLNote_TextRotation_Horizontal;
    if (AcDbMLeaderStyle::kHorizontalAngle != mleaderStyle->textAngleType() && !mleaderStyle->enableDogleg())
        rotation = DIMSTYLE_VALUE_MLNote_TextRotation_Inline;
    dgnDimstyle->SetIntegerProp (rotation, DIMSTYLE_PROP_MLNote_TextRotation_INTEGER);

    // TextAlignmentType does not appear to be in ACAD and it always behaves as our auto mode:
    dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_MLNote_HorAttachment_Auto, DIMSTYLE_PROP_MLNote_HorAttachment_INTEGER);

    DimStyleProp_MLNote_Justification   just = mleaderStyle->textAlignAlwaysLeft() ? DIMSTYLE_VALUE_MLNote_Justification_Left : DIMSTYLE_VALUE_MLNote_Justification_Dynamic;
    dgnDimstyle->SetIntegerProp (just, DIMSTYLE_PROP_MLNote_Justification_INTEGER);

    // left & right vertical attachments
    DimStyleProp_MLNote_VerAttachment   vertLeft = GetVerticalJustification(mleaderStyle, AcDbMLeaderStyle::kLeftLeader);
    dgnDimstyle->SetIntegerProp (vertLeft, DIMSTYLE_PROP_MLNote_VerLeftAttachment_INTEGER);
    DimStyleProp_MLNote_VerAttachment   vertRight = GetVerticalJustification(mleaderStyle, AcDbMLeaderStyle::kRightLeader);
    dgnDimstyle->SetIntegerProp (vertRight, DIMSTYLE_PROP_MLNote_VerRightAttachment_INTEGER);

    // inline leader type line/spline
    bool        boolValue = AcDbMLeaderStyle::kSplineLeader == mleaderStyle->leaderLineType();
    dgnDimstyle->SetBooleanProp (boolValue, DIMSTYLE_PROP_MLNote_LeaderType_BOOLINT);
    boolValue = IsHooklineDisplayed (mleaderStyle, boolValue, vertLeft, vertRight);
    dgnDimstyle->SetBooleanProp (boolValue, DIMSTYLE_PROP_MLNote_ShowLeader_BOOLINT);
    // inline leader length
    doubleValue = mleaderStyle->doglegLength() / effectiveTextHeight;
    dgnDimstyle->SetDoubleProp (doubleValue, DIMSTYLE_PROP_MLNote_ElbowLength_DOUBLE);

    return  RealDwgSuccess;
    }



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          09/12
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtMLeader : public ToDgnExtension
{
private:
    mutable ConvertToDgnContextP    m_toDgnContext;
    mutable DgnModelP               m_model;
    mutable DimensionStyleP         m_dimStyle;
    mutable ElementId               m_noteCellId;
    mutable ElementIdArray          m_leaderDimensionIds;
    mutable AcArray<int>            m_leaderClusters;
    mutable AcDbMLeader*            m_mleader;
    mutable AcDbMText*              m_mtext;
    mutable AcGeVector3d            m_defaultTextDirection;
    mutable bool                    m_isNoteCreated;
    mutable bool                    m_isSharedCellCreated;
    mutable bool                    m_isAnnotative;
    mutable bool                    m_isInvisible;
    mutable double                  m_annotationScale;
    mutable bool                    m_hasHyperlink;
    mutable size_t                  m_numberEffectiveClusters;
    mutable std::map<ElementId, DPoint3d>     m_mapPlacementPtIDVsAttachPt;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    m_mleader = AcDbMLeader::cast (acObject);
    if (NULL == m_mleader)
        return  NullObject;

    m_toDgnContext = &context;
    m_model = context.GetModel ();
    m_noteCellId = context.ElementIdFromObject (m_mleader);
    m_isNoteCreated = false;
    m_isSharedCellCreated = false;
    m_isInvisible = AcDb::kInvisible == m_mleader->visibility();

    m_annotationScale = 1.0;
    m_isAnnotative = m_toDgnContext->GetDisplayedAnnotationScale (m_annotationScale, m_mleader);

    // unlike other elements, note dimensions do not respond to custom annotation scale - TFS 1042369
    if (m_isAnnotative && fabs(m_toDgnContext->GetModelspaceAnnotationScale() - m_annotationScale) > TOLERANCE_ZeroScale)
        m_isAnnotative = false;
    m_leaderDimensionIds.clear ();
    m_numberEffectiveClusters = 0;
    m_defaultTextDirection.set (1.0, 0.0, 0.0);

    // bail out on unsupported mleader types
    if (!this->IsMLeaderTypeSupported() || RealDwgSuccess != this->CreateMultileaderNote(outElement))
        return  this->WorlddrawMLeader (outElement);

    if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
        {
        if (m_mleader->contentType() == AcDbMLeaderStyle::kMTextContent)
            this->PostProcessMultileader(outElement);
        }
    return  RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund          06/20
+---------------+---------------+---------------+---------------+---------------+------*/
void ProcessDimensionAssociation(EditElementHandleR labelCell, EditElementHandleR dimElementHandle) const
    {
    MSElementCP     dimElement = dimElementHandle.GetElementCP();
    int numPoints = dimElement->dim.nPoints;
    DPoint3d dimAttachPt = dimElement->dim.GetPoint(numPoints - 1);
    ElementId persistentElmId = INVALID_ELEMENTID;

    /*iterate to the cell to find the dogleg to get the attachpoint*/
    for (ChildElemIter child(labelCell, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        if (LINE_ELM == child.GetElementType())
            {
            LineHandler*   lineHandler = dynamic_cast <LineHandler*> (&child.GetHandler());

            if (NULL == lineHandler)
                continue;

            MSElementCP     lineElm = child.GetElementCP();
            DPoint3d startPt;
            if (lineElm->hdr.dhdr.props.b.is3d)
                startPt = lineElm->line_3d.start;
            else
                {
                startPt.x = lineElm->line_2d.start.x;
                startPt.y = lineElm->line_2d.start.y;
                startPt.z = 0.0;
                }

            bool isPresent = false;

            if (dimAttachPt.IsEqual(startPt, 1E-3))
                isPresent = true;

            if (isPresent)
                {
                persistentElmId = child.GetElementId();
                break;
                }
            }
        }
    /*association with dogline if available*/
    if (persistentElmId != INVALID_ELEMENTID)
        {
        int pointNo = 1;
        if (1 < dimElement->dim.nPoints)
            {
            AssocPoint      assocPoint;
            AssociativePoint::InitKeypoint(assocPoint, 0, 0, 0, 2);

            AssociativePoint::SetRoot(assocPoint, persistentElmId, 0, 0);
            AssociativePoint::InsertPoint(dimElementHandle, assocPoint, pointNo, dimElement->dim.nPoints + 1);
            }
        }
    else   /*add information for placementpoint creation*/
        {
        
        bool isPresent = false; /*avoid duplicate entry*/
        ElementId placementPointId;
        for (std::map<ElementId, DPoint3d>::iterator curr = m_mapPlacementPtIDVsAttachPt.begin();
             curr != m_mapPlacementPtIDVsAttachPt.end(); curr++)
            {
            if (dimAttachPt.IsEqual(curr->second, 1E-3))
                {
                isPresent = true;
                placementPointId = curr->first;
                break;
                }
            }

        if (!isPresent)
            {
            placementPointId = m_toDgnContext->GetAndIncrementNextId();
            m_mapPlacementPtIDVsAttachPt[placementPointId] = dimAttachPt;
            }

        int pointNo = 1;
        if (1 < dimElement->dim.nPoints)
            {
            AssocPoint      assocPoint;
            AssociativePoint::InitOrigin(assocPoint, NULL);

            AssociativePoint::SetRoot(assocPoint, placementPointId, 0, 0);
            AssociativePoint::InsertPoint(dimElementHandle, assocPoint, pointNo, dimElement->dim.nPoints + 1);
            }
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund          06/20
+---------------+---------------+---------------+---------------+---------------+------*/
void   PostProcessMultileader(EditElementHandleR outElement) const
    {

     //m_toDgnContext->LoadElementIntoCache(outElement);

    /*Apply cell property to the placement point e.g. lock*/
    bool bLock = false;
    MSElementP  cellElm = outElement.GetElementP();
    if (NULL != cellElm && (CELL_HEADER_ELM == cellElm->ehdr.type))
        bLock = cellElm->ehdr.locked;


    for (std::map<ElementId, DPoint3d>::iterator curr = m_mapPlacementPtIDVsAttachPt.begin();
         curr != m_mapPlacementPtIDVsAttachPt.end(); curr++)
        {

        DSegment3d  segment;
        segment.Init(curr->second, curr->second);

        EditElementHandle segmentEeh;

        if (SUCCESS == LineHandler::CreateLineElement(segmentEeh, NULL, segment, true, *m_model))
            {
            ElementPropertiesSetterPtr remapper = ElementPropertiesSetter::Create();
            remapper->SetColor(19);
            remapper->SetWeight(11);

            remapper->SetElementClass(DgnElementClass::Construction);
            remapper->Apply(segmentEeh);
            }

        MSElementP  segmentElem = segmentEeh.GetElementP();
        segmentElem->ehdr.uniqueId = curr->first;

        /*update the property base on cell properties here*/
        segmentElem->ehdr.locked = bLock;

        PersistentElementPath pep(outElement.GetModelRef(), m_noteCellId);
        PlacementPointPtr placementPoint = Bentley::MstnPlatform::PlacementPointUtil::Create(L"placementpoint", pep);
        placementPoint->AddXAttribute(segmentEeh);
        segmentEeh.AddToModel();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund          06/20
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateLabelCell(EditElementHandleR outElement, EditElementHandleR orphanCell, DimensionStylePtr dimStyleFromTable) const
    {
    RealDwgStatus       status = CantCreateMleader;
    
    RotMatrix cellRotation;
    cellRotation.InitIdentity();
    this->GetEcsMatrixFromMLeader(cellRotation, 0);
       
    DPoint3d  cellPoint;
    CellUtil::ExtractOrigin(cellPoint, orphanCell);

    LabelCellHeaderHandler::StdDPointVector labelPoints;
    labelPoints.push_back(cellPoint);

    //dummy dimension for label cell creation, it is not associate with any mulileader entity.
    EditElementHandle dummyleader;

    if (SUCCESS == LabelCellHeaderHandler::CreateLabel(outElement, dummyleader, *dimStyleFromTable, orphanCell, m_model->Is3d(), cellRotation, *m_model, labelPoints, false))
        status = RealDwgSuccess;

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateMultileaderNote (EditElementHandleR outElement) const
    {
    RealDwgStatus       status = CantCreateMleader;

    // an mleader may be composed of leaders clusters, each of which may contain multiple leaderlines that share the same hook point.
    if (Acad::eOk != m_mleader->getLeaderIndexes(m_leaderClusters) || m_leaderClusters.isEmpty())
        {
        // this mleader has no leaders - try creating a widowed note cell
        m_mtext = m_mleader->mtext ();
        if (NULL != m_mtext)
            {
            status = this->CreateNoteCellFromMLeader (outElement);
            delete m_mtext;
            }
        return  status;
        }

    // get mleader style of overrides
    AcDbMLeaderStyle    mlStyleFromMleader;
    if (Acad::eOk != m_mleader->getOverridedMLeaderStyle(mlStyleFromMleader))
        return  CantCreateMleader;

    // need to re-apply overrides on the style
    this->FixStyleFromMleader (&mlStyleFromMleader);

    // create a dim ension style from the overridden multi-leader style
    DimensionStylePtr   dimStyleFromMleader;
    if (RealDwgSuccess != m_toDgnContext->CreateDgnDimStyleFromMLeaderStyle(dimStyleFromMleader, &mlStyleFromMleader))
        return  CantCreateDimensionStyle;

    // get the dimension style from the table
    DimensionStylePtr   dimStyleFromTable = DimensionStyle::GetByID (m_toDgnContext->ElementIdFromObjectId(m_mleader->MLeaderStyle()), *m_toDgnContext->GetFile());
    if (!dimStyleFromTable.IsValid())
        return  CantCreateMleader;

    m_dimStyle = dimStyleFromMleader.get();
    m_mtext = m_mleader->mtext ();      // needs to be freed at the end
    m_hasHyperlink = m_toDgnContext->HasHyperlinkXData (m_mleader);
    m_numberEffectiveClusters = this->CountEffectiveClusters ();

    // if we have turned off annotation scale, update mtext as well
    if (!m_isAnnotative)
        RealDwgUtil::SetObjectAnnotative (m_mtext, false);

    // some properties in the overridden style need to be synch'ed with the one from the table:
    UpdateDimstyleForMLeader (dimStyleFromTable.get());

    // build the dimstyle comparision mask from the two styles
    DimStylePropMaskPtr dimStyleCompareMask = DimStylePropMask::CreatePropMask ();
    dimStyleCompareMask = dimStyleFromTable->Compare (*dimStyleFromMleader.get(), 0);

    // try to create the note cell from the mleader
    EditElementHandle orphanCell;

    status = CantCreateText;
    switch (m_mleader->contentType())
        {
        case AcDbMLeaderStyle::kMTextContent:
            if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
                {
                status = this->CreateNoteCellFromMLeader(orphanCell);
                if (RealDwgSuccess == status)
                    status = this->CreateLabelCell(outElement, orphanCell, dimStyleFromTable);
                }
            else
                {
                status = this->CreateNoteCellFromMLeader(outElement);
                }
             m_isNoteCreated = RealDwgSuccess == status;
            break;
        case AcDbMLeaderStyle::kBlockContent:
            status = this->CreateSharedCellFromMLeader (outElement);
            break;
        case AcDbMLeaderStyle::kNoneContent:
        case AcDbMLeaderStyle::kToleranceContent:   // should have been worlddrawn!
        default:
            // for all other cases, create an empty notecell to be associated by the leaders
            status = this->CreateBlankNoteCell (outElement);
            break;
        }

    // loop through leader clusters and recurse into their leader lines.  Create a leader dimension one at a time.

    // TFS# 1059469
    // The is a problem with RealDWG
    // For some strange reason getLeaderIndexes returns invalid number of clusters when DWG file is opened for the first time in a freshly opened MicroStation
    // However, getLeaderIndexes returns correct values when the DWG file is reopened in the same session of MicroStation
    // Calling getLeaderIndexes again before looping resolves this issue
    m_mleader->getLeaderIndexes (m_leaderClusters);
    m_mapPlacementPtIDVsAttachPt.clear();
    MSElementDescrP     dimChain = NULL;
    int                 numClusters = m_leaderClusters.length ();
    for (int clusterNo = 0; clusterNo < numClusters; clusterNo++ )
        {
        AcArray<int>    clusterLeaders;
        if (Acad::eOk != m_mleader->getLeaderLineIndexes(clusterNo, clusterLeaders) || clusterLeaders.isEmpty())
            continue;

        // create rest of the leader dimensions
        for (int j = 0; j < clusterLeaders.length(); j++)
            {
            EditElementHandle   newElement;
            if (RealDwgSuccess == this->CreateDimensionLeader(newElement, clusterNo, clusterLeaders.at(j)))
                {

                if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
                    {
                    if (m_mleader->contentType() == AcDbMLeaderStyle::kMTextContent)
                        this->ProcessDimensionAssociation(outElement, newElement);
                    }

                DimensionHandler::GetInstance().SaveShieldsDirect (newElement, *dimStyleCompareMask);

                MSElementDescrP elmdscr = newElement.ExtractElementDescr ();
                if (NULL == dimChain)
                    dimChain = elmdscr;
                else
                    dimChain->AddToChain (elmdscr);

                m_leaderDimensionIds.push_back (elmdscr->el.ehdr.uniqueId);
                }
            }
        }

    // append dimension dependency linkage to either the note cell or the shared cell:
    if (RealDwgSuccess == status && m_leaderDimensionIds.size() > 0)
        {
        if (RealDwgSuccess != this->AddMLeaderDependency(outElement))
            DIAGNOSTIC_PRINTF ("Error adding dependency linkage to mleader note cell %I64d\n", m_noteCellId);
        }

    // will save all elements(note/shared cell, dimensions, tags, etc) into DGN cache
    if (NULL != dimChain)
        {
        MSElementDescrP     allElems = outElement.GetElementDescrP ();
        if (NULL == allElems)
            outElement.SetElementDescr (dimChain, true, false, m_model);
        else
            allElems->AddToChain (dimChain);

        status = RealDwgSuccess;
        }

    if (NULL != m_mtext)
        delete m_mtext;
    
    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsMLeaderTypeSupported () const
    {
    if (AcDbMLeaderStyle::kToleranceContent == m_mleader->contentType())
        return  false;

    // support all other types
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   WorlddrawMLeader (EditElementHandleR outElement) const
    {
    AcDbObjectId    viewportId;

    if (RDWGMODEL_TYPE_DefaultModel == m_toDgnContext->GetModelIndexItem()->GetRealDwgModelType())
        viewportId = acdbGetCurVportTableRecordId (m_toDgnContext->GetDatabase());
    else
        viewportId = m_toDgnContext->GetModelIndexItem()->GetOverallViewportId ();

    StatusInt   status = m_toDgnContext->WorldDrawToElements (AcDbEntity::cast(m_mleader), outElement, viewportId);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetEffectiveScale () const
    {
    double      mleaderScale = m_mleader->scale ();
    if (0.0 != mleaderScale)
        return  mleaderScale;

    return  m_annotationScale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            FixStyleFromMleader (AcDbMLeaderStyle* mleaderStyle) const
    {
    /*------------------------------------------------------------------------------------------------------
    Somehow AcDbMLeader::getOverridedMLeaderStyle at least in R2013 does not give us true overridden values.
    In particular, the scale seems always wrong, regardless to what its override flag says.

    An annotative mleader returns scale of 0.0, in which case we apply annotation scale in modelspace.

    For rest of the parameters, we still have to re-apply the overrides to the style.  At least they seem 
    to obey their override flags.

    Unlike dimensions, an mleader does not seem to inherit text height from text style throughout the life 
    cycle of the mleader.  The inheritance only affect the mleader style, i.e. the text height in mleader 
    style gets the value from text style.  When creating an mleader, height from text style is correctly
    used.  But after that, changing of text height from text style has no impact on the mleader object 
    that uses the text style.  Apparently the text height is saved as an override in the object, as opposed
    to inheriting from the text style like dimensions do.  Furthermore, the override seems always done
    irrespective to the override flag, as a case shown in TFS 650252.
    ------------------------------------------------------------------------------------------------------*/
    double      scaleFromMleader = this->GetEffectiveScale ();
    if (1.0 != scaleFromMleader || !m_isAnnotative)
        mleaderStyle->setScale (scaleFromMleader);

    // set style annotation scale per mleader entity
    mleaderStyle->setAnnotative (m_isAnnotative);

    // don't bother checking m_mleader->isOverride(AcDbMLeader::kTextHeight) - text height seems always overridden, TFS 650252.
    mleaderStyle->setTextHeight (m_mleader->textHeight());

    mleaderStyle->setEnableDogleg (m_mleader->enableDogleg());

    if (m_mleader->isOverride(AcDbMLeader::kLeaderLineType))
        mleaderStyle->setLeaderLineType (m_mleader->leaderLineType());
    if (m_mleader->isOverride(AcDbMLeader::kLeaderLineColor))
        mleaderStyle->setLeaderLineColor (m_mleader->leaderLineColor());
    if (m_mleader->isOverride(AcDbMLeader::kLeaderLineWeight))
        mleaderStyle->setLeaderLineWeight (m_mleader->leaderLineWeight());
    if (m_mleader->isOverride(AcDbMLeader::kLeaderLineTypeId))
        mleaderStyle->setLeaderLineTypeId (m_mleader->leaderLineTypeId());
    if (m_mleader->isOverride(AcDbMLeader::kEnableLanding))
        mleaderStyle->setEnableLanding (m_mleader->enableLanding());
    if (m_mleader->isOverride(AcDbMLeader::kLandingGap))
        mleaderStyle->setLandingGap (m_mleader->landingGap());
    if (m_mleader->isOverride(AcDbMLeader::kDoglegLength))
        mleaderStyle->setDoglegLength (m_mleader->doglegLength());
    if (m_mleader->isOverride(AcDbMLeader::kArrowSymbolId))
        mleaderStyle->setArrowSymbolId (m_mleader->arrowSymbolId());
    if (m_mleader->isOverride(AcDbMLeader::kArrowSize))
        mleaderStyle->setArrowSize (m_mleader->arrowSize());
    if (m_mleader->isOverride(AcDbMLeader::kTextStyleId))
        mleaderStyle->setTextStyleId (m_mleader->textStyleId());
    if (m_mleader->isOverride(AcDbMLeader::kTextLeftAttachmentType))
        mleaderStyle->setTextAttachmentType (m_mleader->textAttachmentType(AcDbMLeaderStyle::kLeftLeader), AcDbMLeaderStyle::kLeftLeader);
    if (m_mleader->isOverride(AcDbMLeader::kTextRightAttachmentType))
        mleaderStyle->setTextAttachmentType (m_mleader->textAttachmentType(AcDbMLeaderStyle::kRightLeader), AcDbMLeaderStyle::kRightLeader);
    if (m_mleader->isOverride(AcDbMLeader::kTextTopAttachmentType))
        mleaderStyle->setTextAttachmentType (m_mleader->textAttachmentType(AcDbMLeaderStyle::kTopLeader), AcDbMLeaderStyle::kTopLeader);
    if (m_mleader->isOverride(AcDbMLeader::kTextBottomAttachmentType))
        mleaderStyle->setTextAttachmentType (m_mleader->textAttachmentType(AcDbMLeaderStyle::kBottomLeader), AcDbMLeaderStyle::kBottomLeader);
    if (m_mleader->isOverride(AcDbMLeader::kTextAlignmentType))
        mleaderStyle->setTextAlignmentType (m_mleader->textAlignmentType());
    if (m_mleader->isOverride(AcDbMLeader::kTextAngleType))
        mleaderStyle->setTextAngleType (m_mleader->textAngleType());
    if (m_mleader->isOverride(AcDbMLeader::kTextAttachmentDirection))
        mleaderStyle->setTextAttachmentDirection (m_mleader->textAttachmentDirection());
    if (m_mleader->isOverride(AcDbMLeader::kTextColor))
        mleaderStyle->setTextColor (m_mleader->textColor());
    if (m_mleader->isOverride(AcDbMLeader::kEnableFrameText))
        mleaderStyle->setEnableFrameText (m_mleader->enableFrameText());
    if (m_mleader->isOverride(AcDbMLeader::kExtendLeaderToText))
        mleaderStyle->setExtendLeaderToText (m_mleader->extendLeaderToText()); 
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            UpdateDimstyleForMLeader (DimensionStyleCP dimStyleFromTable) const
    {
    // use the same style name and ID in the table
    m_dimStyle->SetName (dimStyleFromTable->GetName().c_str());
    m_dimStyle->SetElemID (dimStyleFromTable->GetID());
    
    // override text height, which often times is 0 in the style from the table
    double      uorHeight = m_mleader->textHeight() * m_toDgnContext->GetScaleToDGN();
    m_dimStyle->SetDistanceProp (uorHeight, DIMSTYLE_PROP_Text_Height_DISTANCE, m_toDgnContext->GetModel());

    double      aspectRatio = 1.0;
    AcDbTextStyleTableRecordPointer textstyle (m_mleader->textStyleId(), AcDb::kForRead);
    if (Acad::eOk == textstyle.openStatus())
        aspectRatio = textstyle->xScale ();

    // override text width
    m_dimStyle->SetDistanceProp (aspectRatio * uorHeight, DIMSTYLE_PROP_Text_Width_DISTANCE, m_toDgnContext->GetModel());

    // we don't have a text rotation to set, but for a horizontal text type with a near 90-degree angle, we apply our vertical rotation:
    if (AcDbMLeaderStyle::kMTextContent == m_mleader->contentType() && nullptr != m_mtext &&
        (m_mleader->enableDogleg() || AcDbMLeaderStyle::kHorizontalAngle == m_mleader->textAngleType()))
        {
        /*----------------------------------------------------------------------------------------------
        The text rotation is the angle between ECS's x-axis to text's horizontal vector projected to the
        active UCS, but the vertical note will be relative to the active view when the note is edited, 
        as in TFS 746438.  We need to compensate the difference:
        ----------------------------------------------------------------------------------------------*/
        AcGeVector3d    xAxis = m_mtext->direction ();
        double  ucsAngle = acos (xAxis.x);
        double  textAngle = m_mtext->rotation ();
        textAngle = fabs (textAngle - ucsAngle);
        if (fabs(textAngle - msGeomConst_piOver2) < 0.25)
            m_dimStyle->SetIntegerProp (DIMSTYLE_VALUE_MLNote_TextRotation_Vertical, DIMSTYLE_PROP_MLNote_TextRotation_INTEGER);
        }

    // override hookline toggle: a spline mleader does not display the hookline but does the underline
    if (m_mleader->enableDogleg() && AcDbMLeaderStyle::kSplineLeader == m_mleader->leaderLineType() && NULL != m_mtext)
        m_dimStyle->SetBooleanProp (this->IsMLeaderUnderlined(), DIMSTYLE_PROP_MLNote_ShowLeader_BOOLINT);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
size_t          CountEffectiveClusters () const
    {
    // an mleader may contain empty clusters of leaders.  Count up to find out effective clusters.
    size_t      numEffectiveClusters = 0;
    int         numClusters = m_leaderClusters.length ();
    for (int clusterNo = 0; clusterNo < numClusters; clusterNo++ )
        {
        AcArray<int>    clusterLeaders;
        if (Acad::eOk == m_mleader->getLeaderLineIndexes(clusterNo, clusterLeaders) && !clusterLeaders.isEmpty())
            numEffectiveClusters++;
        }

    return  numEffectiveClusters;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsMLeaderUnderlined () const
    {
    if (AcDbMLeaderStyle::kAttachmentHorizontal != m_mleader->textAttachmentDirection())
        return  false;

    int         numClusters = m_leaderClusters.length ();
    for (int clusterNo = 0; clusterNo < numClusters; clusterNo++ )
        {
        AcArray<int>    clusterLeaders;
        if (Acad::eOk != m_mleader->getLeaderLineIndexes(clusterNo, clusterLeaders) || clusterLeaders.isEmpty())
            continue;

        DimStyleProp_MLNote_VerAttachment   vertAttachment = DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicCorner;
        if (this->IsHooklineOnXDirection(clusterNo))
            m_dimStyle->GetIntegerProp ((int&)vertAttachment, DIMSTYLE_PROP_MLNote_VerLeftAttachment_INTEGER);
        else
            m_dimStyle->GetIntegerProp ((int&)vertAttachment, DIMSTYLE_PROP_MLNote_VerRightAttachment_INTEGER);

        if (DIMSTYLE_VALUE_MLNote_VerAttachment_Underline == vertAttachment)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsHooklineOnXDirection (int clusterNo) const
    {
    AcGeVector3d    hookXDirection;
    if (NULL == m_mtext || Acad::eOk != m_mleader->getDoglegDirection(clusterNo, hookXDirection))
        return  true;

    AcGeVector3d    textXDirection = m_mtext->direction ();

    return  hookXDirection.dotProduct(textXDirection) >= 0.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
MTextParagraphAlignment         GetMTextAttachmentOverride () const
    {
    MTextParagraphAlignment     pxqMarkup = MTEXTMarkUp_None;
    ACHAR*                      mtextChars = m_mtext->contents ();
    if (NULL != mtextChars)
        {
        ACHAR*      checkChars = mtextChars;

        while (NULL != checkChars && 0 != checkChars[0] && wcslen(checkChars) > 4)
            {
            // we only look for the markup at the beginning of the text string
            if (L'\\' != checkChars[0])
                break;

            // check for \pxq for current markup
            if (L'p' == checkChars[1] && L'x' == checkChars[2] && L'q' == checkChars[3])
                {
                switch (checkChars[4])
                    {
                    case L'l':  pxqMarkup = MTEXTMarkUp_pxql;   break;
                    case L'c':  pxqMarkup = MTEXTMarkUp_pxqc;   break;
                    case L'r':  pxqMarkup = MTEXTMarkUp_pxqr;   break;
                    }
                break;
                }

            // move char pointer to the end of current markup
            while (0 != checkChars[0])
                {
                checkChars++;
                if (L';' == checkChars[0])
                    {
                    checkChars++;
                    break;
                    }
                }

            // move on to check for next markup
            if (NULL == checkChars && 0 != checkChars[0])
                checkChars++;
            }

        acutDelString (mtextChars);
        }

    return  pxqMarkup;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbMText::AttachmentPoint      FindTextAttachment (AcDbMText::AttachmentPoint defaultAttachment) const
    {
    // it is expected that most text strings do not start with the justification override
    MTextParagraphAlignment     mtextOverride = this->GetMTextAttachmentOverride ();
    if (MTEXTMarkUp_None == mtextOverride)
        return  defaultAttachment;

    // map overridden justification to the attachment point
    switch (defaultAttachment)
        {
        case AcDbMText::kTopLeft:
        case AcDbMText::kTopCenter:
        case AcDbMText::kTopRight:
            switch (mtextOverride)
                {
                case MTEXTMarkUp_pxql:      return  AcDbMText::kTopLeft;
                case MTEXTMarkUp_pxqc:      return  AcDbMText::kTopCenter;
                case MTEXTMarkUp_pxqr:      return  AcDbMText::kTopRight;
                }
            break;

        case AcDbMText::kMiddleLeft:
        case AcDbMText::kMiddleCenter:
        case AcDbMText::kMiddleRight:
            switch (mtextOverride)
                {
                case MTEXTMarkUp_pxql:      return  AcDbMText::kMiddleLeft;
                case MTEXTMarkUp_pxqc:      return  AcDbMText::kMiddleCenter;
                case MTEXTMarkUp_pxqr:      return  AcDbMText::kMiddleRight;
                }
            break;

        case AcDbMText::kBottomLeft:
        case AcDbMText::kBottomCenter:
        case AcDbMText::kBottomRight:
            switch (mtextOverride)
                {
                case MTEXTMarkUp_pxql:      return  AcDbMText::kBottomLeft;
                case MTEXTMarkUp_pxqc:      return  AcDbMText::kBottomCenter;
                case MTEXTMarkUp_pxqr:      return  AcDbMText::kBottomRight;
                }
            break;
        }

    // all other cases remain unchanged
    return  defaultAttachment;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FixMTextAttachment () const
    {
    /*---------------------------------------------------------------------------------------------------------------
    Generally, an mtext can have its own attachment point, as well as justification, independent to the mleader.

    Bug#8783 however appears to have an exception: when the mtext has a local justification override which is different
    than the actual attachment point, our note draw logic would use the local override instead of mleader's attached
    point, hence to shift the text.  Here we try to find the first line markup \pxq and compare its justification to
    mleader's attachment point.  If different, update the mtext by resetting the attachment point and synch the origin.
    ---------------------------------------------------------------------------------------------------------------*/
    if (AcDbMLeaderStyle::kAttachmentVertical == m_mleader->textAttachmentDirection())
        return  false;

    AcDbMText::AttachmentPoint  actualAttachment = m_mtext->attachment ();
    AcDbMText::AttachmentPoint  neededAttachment = this->FindTextAttachment (actualAttachment);

    if (actualAttachment != neededAttachment)
        {
        Acad::ErrorStatus       errorStatus = m_mtext->setAttachmentMovingLocation (neededAttachment);
        if (Acad::eOk == errorStatus)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateNoteCellFromMLeader (EditElementHandleR outElement) const
    {
    // try to create a note cell from the mtext part of the mleader - does not depend on leaders.
    if (NULL == m_mtext)
        return  CantCreateText;

    // try to fix potentially inconsistent data in mtext
    this->FixMTextAttachment ();

    // create a TextBlock from the mtext
    TextBlock   textBlock (*m_model);
    RotMatrix   matrix;
    UInt32      color = m_toDgnContext->GetDgnColor (m_mtext->entityColor());
    bool        fieldsPresent = false;

    RealDwgStatus   status = m_toDgnContext->ProcessMText (textBlock, &matrix, m_mtext, color, &fieldsPresent, m_mleader);
    if (RealDwgSuccess != status)
        return  CantCreateText;

    textBlock.SetForceTextNodeFlag (true);

    // if the text contains fields, we will have to post process it
    if (fieldsPresent)
        m_toDgnContext->AddPostProcessObject (m_mleader);

    // create a note cell from the TextBlock and set it as the output element
    if (RealDwgSuccess != this->CreateNoteCellFromTextBlock(outElement, textBlock))
        return  CantCreateText;

    // add linkages etc to the note cell
    m_toDgnContext->ExtractLinkagesAndGraphicGroupFromEntityXData (outElement, m_mleader);
    m_toDgnContext->ExtractXAttributesFromExtensionDictionary (outElement, m_mleader);
    if (m_hasHyperlink && !fieldsPresent)
        m_toDgnContext->AddPostProcessObject (m_mleader);

    // lock the note cell to prevent user from editing, until we will support multi-assoc points in the future
    if (m_numberEffectiveClusters > 1)
        {
        outElement.GetElementP()->ehdr.locked = true;
        // also lock the child text element as otherwise the text editor can still change it - TFS 111991.
        for (ChildEditElemIter child(outElement, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
            {
            MSElementP  elem = child.GetElementP ();
            if (NULL != elem && (TEXT_ELM == elem->ehdr.type || TEXT_NODE_ELM == elem->ehdr.type))
                {
                elem->ehdr.locked = true;
                break;
                }
            }
        }

    if (m_isInvisible)
        outElement.GetElementP()->hdr.dhdr.props.b.invisible = true;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateNoteCellFromTextBlock (EditElementHandleR cellElement, TextBlockCR textBlock) const
    {
    NormalCellHeaderHandler::CreateOrphanCellElement (cellElement, NULL, m_toDgnContext->GetThreeD(), *m_model);
    
    if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
        {
        if (m_mleader->contentType() != AcDbMLeaderStyle::kMTextContent)
            {
            if (BSISUCCESS != mdlNote_addNoteCellLinkage(cellElement))
                return  CantCreateText;
            }
        }
    else
        {
        if (BSISUCCESS != mdlNote_addNoteCellLinkage(cellElement))
            return  CantCreateText;
        }
        
    // create text element from TextBlock, and add it to the cell header
    EditElementHandle   textElement;
    if (BSISUCCESS != TextHandlerBase::CreateElement(textElement, NULL, textBlock) || 
        BSISUCCESS != this->AppendChildToNoteCell(cellElement, textElement, false))
        return  CantCreateText;

    // get the text element back as it may be used as a template element later on
    textElement.Duplicate (ChildElemIter(cellElement, ExposeChildrenReason::Count));

    // persist mleader to the cell header
    MSElementP          cellHeader = cellElement.GetElementP ();
    cellHeader->ehdr.uniqueId = m_noteCellId;

    // calculate text frame size
    DRange3d            textRange = textBlock.GetExactRange ();
    AcDbExtents         acTextBox;
    if (textRange.IsNull() && Acad::eOk == m_mleader->getContentGeomExtents(acTextBox))
        {
        // handle error - don't really expect this to happen though
        DPoint3d        point;
        m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(point, acTextBox.minPoint()));
        textRange.InitFrom (point);
        m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(point, acTextBox.maxPoint()));
        textRange.Extend (point);
        }

    double              textWidth = textRange.isNull() ? 0.0 : (textRange.high.x - textRange.low.x);
    double              landingGap = m_mleader->landingGap() * m_toDgnContext->GetScaleToDGN();
    double              scale = this->GetEffectiveScale ();

    if (scale < TOLERANCE_ZeroScale)
        scale = 1.0;
    else
        landingGap *= scale;

    // add a shape element as the text frame
    if (m_mleader->enableFrameText() && textWidth > TOLERANCE_ZeroSize && landingGap > TOLERANCE_ZeroSize)
        {
        EditElementHandle   shapeElement;
        if (BSISUCCESS == m_toDgnContext->CreateShapeElementByRange(shapeElement, textRange, -landingGap, textElement, textBlock.GetOrientation(), m_mtext))
            this->AppendChildToNoteCell (cellElement, shapeElement);
        }

    // is the mleader set to attach horizontally or vertically?
    AcDbMLeaderStyle::TextAttachmentDirection   attachType = m_mleader->textAttachmentDirection ();
    bool                                        hasCellOrigin = false;

    // loop through leader clusters and add a hookline, underline, or overline for every cluster

    // TFS# 1059469
    // The is a problem with RealDWG
    // For some strange reason getLeaderIndexes returns invalid number of clusters when DWG file is opened for the first time in a freshly opened MicroStation
    // However, getLeaderIndexes returns correct values when the DWG file is reopened in the same session of MicroStation
    // Calling getLeaderIndexes again before looping resolves this issue
    m_mleader->getLeaderIndexes (m_leaderClusters);

    for (int clusterNo = 0; clusterNo < m_leaderClusters.length(); clusterNo++)
        {
        // get leaders in this cluster
        AcArray<int>    clusterLeaders;
        if (Acad::eOk != m_mleader->getLeaderLineIndexes(clusterNo, clusterLeaders))
            continue;

        // get the attachment point from the first leader in this cluster
        AcGePoint3d     attachPoint;
        if (Acad::eOk != m_mleader->getLastVertex(clusterLeaders.first(), attachPoint))
            continue;
    
        DPoint3d        origin;
        m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(origin, attachPoint));

        // set cell header origin the first time around
        if (!hasCellOrigin)
            {
            if (m_toDgnContext->GetThreeD())
                {
                cellHeader->cell_3d.origin = origin;
                }
            else
                {
                cellHeader->cell_2d.origin.x = origin.x;
                cellHeader->cell_2d.origin.y = origin.y;
                }
            hasCellOrigin = true;
            }

        // create a line based on where the attachment point is relative to the text: left, right, top or bottom.
        if (AcDbMLeaderStyle::kAttachmentHorizontal == attachType)
            this->CreateHorizontallyAttachedLine (cellElement, origin, textWidth, landingGap, clusterNo);
        else if (AcDbMLeaderStyle::kAttachmentVertical == attachType)
            this->CreateVerticallyAttachedLine (cellElement, origin, textWidth, textRange, textBlock);
        else
            DIAGNOSTIC_PRINTF ("Unknown mleader attachment type!\n");
        }

    if (m_isInvisible)
        cellHeader->hdr.dhdr.props.b.invisible = true;

    // do not call AddChildComplete to set the range - it forces the center of the cell to be the origin!
    if (hasCellOrigin && BSISUCCESS == NormalCellHeaderHandler::SetCellRange(cellElement))
        return  RealDwgSuccess;

    // handle error
    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(NormalCellHeaderHandler::AddChildComplete(cellElement));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateBlankNoteCell (EditElementHandleR outElement) const
    {
    // we need a host element for leaders that have no texts - TF 618020
    AcDbObjectId    activeStyle = m_toDgnContext->GetDatabase()->textstyle ();
    Int32           dgnStyleId = m_toDgnContext->GetFileHolder().GetTextStyleIndex()->GetDgnId (activeStyle.handle());
    if (dgnStyleId < 1)
        dgnStyleId = 1;
    DgnTextStylePtr textstyle = DgnTextStyle::GetByID (dgnStyleId, *m_toDgnContext->GetFile());
    if (textstyle.IsNull())
        return  OutOfMemoryError;

    TextBlockPtr    blankText = TextBlock::Create (*textstyle.get(), *m_model);
    if (blankText.IsNull())
        return  CantCreateText;

    // get text origin from the attachment point of the first leader line of the first effective cluster:
    DPoint3d        origin = DPoint3d::FromZero();
    this->GetAttachmentPointFromAnyLeader (origin);
    blankText->SetUserOrigin (origin);

    // apply active text height if the style has it as 0:
    DPoint2d        size;
    if (BSISUCCESS != textstyle->GetProperty(TextStyle_Height, size.y) || size.y < TOLERANCE_TextHeight)
        size.y = m_toDgnContext->GetDatabase()->textsize() * m_toDgnContext->GetScaleToDGN();
    // apply text width factor:
    if (BSISUCCESS != textstyle->GetProperty(TextStyle_WidthFactor, size.x) || size.x < TOLERANCE_ZeroScale)
        size.x = 1.0;
    size.x *= size.y;

    // set font size for the first run:
    blankText->GetRunPropertiesForAddR().SetFontSize (size);

    // set blank text as a text node
    blankText->SetType (TEXTBLOCK_TYPE_DwgMText);
    blankText->SetNodeNumber (m_toDgnContext->GetFileHolder().GetAndIncrementNextAvailableTextNodeID());
    blankText->AppendText (L" ");

    if (RealDwgSuccess != this->CreateNoteCellFromTextBlock(outElement, *blankText))
        return  CantCreateText;

    // persist mleader to the cell header
    outElement.GetElementP()->ehdr.uniqueId = m_noteCellId;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetAttachmentPointFromAnyLeader (DPoint3dR outPoint) const
    {
    AcGePoint3d     attachPoint;
    bool            found = false;

    for (int clusterNo = 0; clusterNo < m_leaderClusters.length(); clusterNo++ )
        {
        AcArray<int>    clusterLeaders;
        if (Acad::eOk == m_mleader->getLeaderLineIndexes(clusterNo, clusterLeaders) && !clusterLeaders.isEmpty())
            {
            for (int leaderNo = 0; leaderNo < clusterLeaders.length(); leaderNo++)
                {
                if (Acad::eOk == m_mleader->getLastVertex(leaderNo, attachPoint))
                    {
                    m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(outPoint, attachPoint));

                    // also set default text xdirection from this leader's dogleg direction:
                    if (Acad::eOk != m_mleader->getDoglegDirection(clusterNo, m_defaultTextDirection))
                        m_defaultTextDirection.set (1.0, 0.0, 0.0);

                    found = true;
                    break;
                    }
                }
            }
        if (found)
            break;
        }

    return  found;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            CreateHorizontallyAttachedLine (EditElementHandleR cellElement, DPoint3dCR origin, double textWidth, double landingGap, int clusterNo) const
    {
    // this cluster is horizontally attached to, either on left or right of, the text
    bool            hasHookline = m_mleader->enableDogleg() && AcDbMLeaderStyle::kStraightLeader == m_mleader->leaderLineType();
    AcGeVector3d    textDirection = nullptr == m_mtext ? m_defaultTextDirection : m_mtext->direction ();
    double          lineLength = 0.0;
    AcGeVector3d    lineVector;

    double          dimScale = this->GetEffectiveScale ();
    if (dimScale < TOLERANCE_ZeroScale)
        dimScale = 1.0;

    // get the hookline length
    if (hasHookline && Acad::eOk == m_mleader->doglegLength(clusterNo, lineLength))
        lineLength *= m_toDgnContext->GetScaleToDGN() * dimScale;

    // get the attachment direction
    if (Acad::eOk != m_mleader->getDoglegDirection(clusterNo, lineVector))
        lineVector = textDirection;

    // if it is under/overline, extend it through the text box; otherwise keep the hookline length
    bool            isLeft = lineVector.dotProduct(textDirection) > 0.9;
    if (this->NeedToExtendLineThroughText(isLeft ? AcDbMLeaderStyle::kLeftLeader : AcDbMLeaderStyle::kRightLeader))
        lineLength += landingGap + textWidth;

    // now we are ready to create the line with the data we computed
    if (lineLength > TOLERANCE_UORPointEqual)
        {
        // make it a full size vector
        lineVector *= lineLength;

        // create the hook/under/overline element
        DPoint3d            endVector;
        DSegment3d          lineSegment = DSegment3d::FromOriginAndDirection(origin, RealDwgUtil::DPoint3dFromGeVector3d(endVector, lineVector));
        EditElementHandle   lineElement;

        if (BSISUCCESS == LineHandler::CreateLineElement(lineElement, NULL, lineSegment, m_toDgnContext->GetThreeD(), *m_model))
            this->AppendChildToNoteCell (cellElement, lineElement);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            CreateVerticallyAttachedLine (EditElementHandleR cellElement, DPoint3dCR origin, double textWidth, DRange3dCR textRange, TextBlockCR textBlock) const
    {
    // this cluster is vertically attached to top or bottom of the text
    DPoint3d    textPoint, attachPoint;
    if (!textRange.isNull())
        {
        // use text's center point
        textPoint.SumOf (textRange.low, textRange.high);
        textPoint.Scale (0.5);
        }
    else
        {
        // handle error by using the text's user origin
        textPoint = textBlock.GetUserOrigin();
        }

    // check the relative y values on text plane:
    RotMatrix   textMatrix = textBlock.GetOrientation ();
    textMatrix.MultiplyTranspose (textPoint);
    textMatrix.MultiplyTranspose (attachPoint, origin);

    bool        isTop = attachPoint.y - textPoint.y > TOLERANCE_ZeroSize;
    if (this->NeedToExtendLineThroughText(isTop ? AcDbMLeaderStyle::kTopLeader : AcDbMLeaderStyle::kBottomLeader))
        {
        DVec3d      xDirection = DVec3d::FromColumn(textMatrix, 0);
        xDirection.Scale (textWidth);

        DSegment3d  lineSegment;
        lineSegment.point[0].SumOf (origin, xDirection, -0.5);
        lineSegment.point[1].SumOf (origin, xDirection,  0.5);

        EditElementHandle   lineElement;
        if (BSISUCCESS == LineHandler::CreateLineElement(lineElement, NULL, lineSegment, m_toDgnContext->GetThreeD(), *m_model))
            this->AppendChildToNoteCell (cellElement, lineElement);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       AppendChildToNoteCell (EditElementHandleR outElement, EditElementHandleR childElement, bool setSymbology = true) const
    {
    // children in note cell have the symbology from mleader
    UInt32      level = m_toDgnContext->GetDgnLevel (m_mleader->layerId());
    Symbology   symb = this->GetSymbology ((MSElementTypes)childElement.GetElementType());
    double      lsScale = m_mleader->linetypeScale ();
    double      transparency = m_toDgnContext->GetDgnTransparency (m_mleader->transparency());

    // an exception for textnode which should have been set from mtext, and may have style overrides, TFS 1008497
    UInt32*     color = nullptr;
    UInt32*     weight = nullptr;
    Int32*      lstyle = nullptr;
    if (setSymbology)
        {
        color = &symb.color;
        weight = &symb.weight;
        lstyle = &symb.style;
        }

    m_toDgnContext->SetElementSymbology (childElement, level, color, lstyle, weight, NULL, NULL, lsScale, transparency, DgnElementClass::Dimension);

    childElement.GetElementP()->ehdr.uniqueId = m_toDgnContext->GetAndIncrementNextId ();

    return  NormalCellHeaderHandler::AddChildElement (outElement, childElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            NeedToExtendLineThroughText (AcDbMLeaderStyle::LeaderDirectionType whichSide) const
    {
    // text frame turns off any under/overline
    if (m_mleader->enableFrameText())
        return  false;

    // depending on where the leader is attached (left/right/top/bottom), this setting tells us if an over/underline to be needed:
    switch (m_mleader->textAttachmentType(whichSide))
        {
        case AcDbMLeaderStyle::kAttachmentBottomLine:       // underline, below bottom of last line
        case AcDbMLeaderStyle::kAttachmentBottomOfTopLine:  // underline, below bottom of 1st line
        case AcDbMLeaderStyle::kAttachmentLinedCenter:      // over/bottomline, on top or bottom
            return  true;
        default:
            return  false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateDimensionLeader (EditElementHandleR outElement, int clusterNo, int leaderNo) const
    {
    int                 numPoints = 0;
    if (Acad::eOk != m_mleader->numVertices(leaderNo, numPoints))
        return  CantCreateLeader;

    DgnTextStylePtr     dgnTextStyle;
    if (SUCCESS != m_dimStyle->GetTextStyleProp(dgnTextStyle, DIMSTYLE_PROP_Text_TextStyle_TEXTSTYLE) || !dgnTextStyle.IsValid())
        return  CantCreateLeader;

    ToDgnExtLeader::NoteCreateData  noteCreate(*m_dimStyle, *dgnTextStyle.get());
    noteCreate.m_levelId = m_toDgnContext->GetDgnLevel (m_mleader->layerId());
    noteCreate.m_symbology = this->GetSymbology (DIMENSION_ELM);

    this->SuppressLeaderTerminator (noteCreate.m_dimStyle, leaderNo, numPoints);
    this->GetEcsMatrixFromMLeader (noteCreate.m_dimMatrix, leaderNo);
    
    BentleyStatus       status = DimensionHandler::CreateDimensionElement (outElement, noteCreate, DimensionType::Note, m_toDgnContext->GetThreeD(), *m_model);
    if (BSISUCCESS != status)
        return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

    DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&outElement.GetHandler());
    if (NULL == dimHandler)
        return  BadElementHandler;

    DimText             dimText;
    memset (&dimText, 0, sizeof dimText);
    mdlDim_initDimTextFromStyle (&dimText, m_dimStyle);

    // insert vertices into the dimension
    DPoint3d            first2points[2];
    int                 pointIndex = 0;
    for (int i = 0; i < numPoints; i++)
        {
        AcGePoint3d     acPoint;
        DPoint3d        point;
        
        if (Acad::eOk != m_mleader->getVertex(leaderNo, i, acPoint))
            continue;

        m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(point, acPoint));

        if (SUCCESS != dimHandler->InsertPointDirect(outElement, &point, NULL, dimText, pointIndex))
            return  CantInsertDimensionPoint;

        if (pointIndex < 2)
            first2points[pointIndex] = point;

        pointIndex++;
        }

    // add a dog leg on the dimension for an mleader with none or an empty mtext
    bool                hasHookline = true;
    if (!m_isNoteCreated && BSISUCCESS == m_dimStyle->GetBooleanProp(hasHookline, DIMSTYLE_PROP_MLNote_ShowLeader_BOOLINT) && hasHookline)
        {
        double          lineLength = 0.0;
        AcGeVector3d    lineVector;
        AcGePoint3d     endPoint;

        if (Acad::eOk == m_mleader->doglegLength(clusterNo, lineLength) && 
            Acad::eOk == m_mleader->getDoglegDirection(clusterNo, lineVector) &&
            Acad::eOk == m_mleader->getLastVertex(leaderNo, endPoint))
            {
            lineVector *= lineLength * this->GetEffectiveScale();
            endPoint += lineVector;

            DPoint3d    newPoint;
            m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(newPoint, endPoint));
            
            dimHandler->InsertPointDirect (outElement, &newPoint, NULL, dimText, pointIndex++);
            }
        }
    
    // set the tangents for a spline leader
    if (AcDbMLeaderStyle::kSplineLeader == m_mleader->leaderLineType() && pointIndex > 1)
        {
        DVec3d          xDirection;
        AcGeVector3d    attachDirection;
        if (Acad::eOk == m_mleader->getDoglegDirection(clusterNo, attachDirection))
            RealDwgUtil::DVec3dFromGeVector3d (xDirection, attachDirection);
        else
            noteCreate.m_viewMatrix.GetColumn (xDirection, 0);

        // always set hookline tangent for spline leader
        bool            isAttached = true;
        ToDgnExtLeader::SetSplineEndTangents (outElement, first2points, xDirection, false, isAttached, numPoints > 2);
        }

    // add associative point to the dimension
    if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
        {
        if (INVALID_ELEMENTID != m_noteCellId && 0 != m_noteCellId &&
            m_mleader->contentType() != AcDbMLeaderStyle::kMTextContent)
            mdlNote_setNoteAssocPoint(outElement, m_noteCellId, pointIndex - 1);
        }
    else
        {
        if (INVALID_ELEMENTID != m_noteCellId && 0 != m_noteCellId)
            mdlNote_setNoteAssocPoint(outElement, m_noteCellId, pointIndex - 1);
        }
 
    // disable horizontal auto-attachment if the attachment point is not supported, i.e. top or bottom, to prevent attachment from changing when edited. Bug# 1635.
    if (AcDbMLeaderStyle::kAttachmentVertical == m_mleader->textAttachmentDirection())
        mdlDim_setNoteAllowAutoMode (outElement, false);

    MSElementP  dimElem = outElement.GetElementP ();
    // use a new ID for the dimension element
    dimElem->ehdr.uniqueId = m_toDgnContext->GetAndIncrementNextId ();
    // lock the dimension if the mleader uses a block content, or there are more then one attachment point, as we can't edit it
    if (m_isSharedCellCreated || m_numberEffectiveClusters > 1)
        dimElem->hdr.ehdr.locked = true;
    // set the dimension invisible if the leader display is off
    dimElem->hdr.dhdr.props.b.invisible = m_isInvisible || AcDbMLeaderStyle::kInVisibleLeader == m_mleader->leaderLineType();

    // add linkages from mleader entity
    m_toDgnContext->ExtractLinkagesAndGraphicGroupFromEntityXData (outElement, m_mleader);
    m_toDgnContext->ExtractXAttributesFromExtensionDictionary (outElement, m_mleader);

    status = dimHandler->ValidateElementRange (outElement, true);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
Symbology       GetSymbology (MSElementTypes elementType) const
    {
    // when symbology is ByBlock, it uses entity's symbology
    Symbology           effectiveSymbology;
    AcCmColor           acColor = (TEXT_NODE_ELM == elementType || TEXT_ELM == elementType) ? m_mleader->textColor() : m_mleader->leaderLineColor ();
    if (acColor.isByBlock())
        effectiveSymbology.color = m_toDgnContext->GetDgnColor (m_mleader->entityColor());
    else
        effectiveSymbology.color = m_toDgnContext->GetDgnColor (acColor);

    AcDb::LineWeight    acWeight = m_mleader->leaderLineWeight ();
    if (AcDb::kLnWtByBlock == acWeight)
        effectiveSymbology.weight = m_toDgnContext->GetDgnWeight (m_mleader->lineWeight());
    else
        effectiveSymbology.weight = m_toDgnContext->GetDgnWeight (acWeight);

    effectiveSymbology.style = m_toDgnContext->GetDgnStyle (m_mleader->leaderLineTypeId());
    if (STYLE_BYCELL == effectiveSymbology.style)
        effectiveSymbology.style = m_toDgnContext->GetDgnStyle (m_mleader->linetypeId());

    return  effectiveSymbology;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            SuppressLeaderTerminator (DimensionStyleR outDimstyle, int leaderNo, int numPoints) const
    {
    // ACAD suppresses leader terminator if it is greater than 1/2 the segment length
    if (numPoints < 2)
        return  false;

    AcGePoint3d points[2];
    if (Acad::eOk != m_mleader->getVertex(leaderNo, 0, points[0]) || Acad::eOk != m_mleader->getVertex(leaderNo, 1, points[1]))
        return  false;

    // displayed terminator size
    double      termSize = m_mleader->arrowSize ();
    termSize *= this->GetEffectiveScale ();

    // half length of the last leader segment - it seems that, unlike leader, even with a content it still is 0.5 of the length.
    double      nearHalf = 0.5 * points[0].distanceTo(points[1]);
    
    if (termSize <= nearHalf)
        return  false;

    // suppress terminator display on this leader
    outDimstyle.SetIntegerProp (DIMSTYLE_VALUE_Terminator_Type_None, DIMSTYLE_PROP_Terminator_Note_INTEGER);
    outDimstyle.SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_NoteType_INTEGER);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetEcsMatrixFromMLeader (RotMatrixR outMatrix, int leaderNo) const
    {
    DVec3d          xAxis, yAxis, zAxis;

    // unlike qleader's xdirection, mleader's dogleg direction is not a true xdirection - use plane orientation instead
    AcGePlane       plane = m_mleader->plane ();
    
    RealDwgUtil::DVec3dFromGeVector3d (zAxis, plane.normal());

    AcGePoint3d     origin;
    AcGeVector3d    xDirection, yDirection;
    plane.get (origin, xDirection, yDirection);

    RealDwgUtil::DVec3dFromGeVector3d (xAxis, xDirection);
    RealDwgUtil::DVec3dFromGeVector3d (yAxis, yDirection);

    zAxis.Normalize ();
    xAxis.Normalize ();

    yAxis.CrossProduct (zAxis, xAxis);
    
    outMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddMLeaderDependency (EditElementHandleR cellElement) const
    {

    if (ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ENABLE_MULTILEADER_TO_LABELCELL"))
        {
        if (m_mleader->contentType() == AcDbMLeaderStyle::kMTextContent)
            return RealDwgSuccess;
        }

    if (m_leaderDimensionIds.empty())
        return  DependencyFailure;
        
    if (BSISUCCESS == DependencyManagerLinkage::GetLinkageFromMSElement(NULL, cellElement.GetElementCP(), DEPENDENCYAPPID_Note, DEPENDENCYAPPVALUE_Dimension))
        return  RealDwgSuccess;

    DependencyLinkage   dependencyLinkage;
    DependencyManagerLinkage::InitLinkage (dependencyLinkage, DEPENDENCYAPPID_Note, DEPENDENCY_DATA_TYPE_ELEM_ID, DEPENDENCY_ON_COPY_DeepCopyRootsAcrossFiles);

    dependencyLinkage.nRoots = (UInt16) m_leaderDimensionIds.size ();
    if (dependencyLinkage.nRoots < 1)
        return  DependencyFailure;

    if (dependencyLinkage.nRoots > DEPENDENCY_SOME_ELEMIDS)
        dependencyLinkage.nRoots = DEPENDENCY_SOME_ELEMIDS;

    dependencyLinkage.appValue = DEPENDENCYAPPVALUE_Dimension;

    for (UInt16 i = 0; i < dependencyLinkage.nRoots; i++)
        dependencyLinkage.root.elemid[i] = m_leaderDimensionIds.at (i);

    StatusInt   status = DependencyManagerLinkage::AppendLinkage (cellElement, dependencyLinkage, 0);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

struct  MLeaderBlockMatrix
    {
    int             m_numEntries;
    AcGeMatrix3d    m_matrix;
    MLeaderBlockMatrix ()
        {
        m_numEntries = 0;
        m_matrix = AcGeMatrix3d::kIdentity;
        }
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     GetBlockMatrixCallback (AcDb::DxfCode dxfGroupCode, const void* dataIn, void* dataOut)
    {
    if (47 == dxfGroupCode)
        {
        MLeaderBlockMatrix* matrixOut = (MLeaderBlockMatrix*) dataOut;
        double*             entry = (double*) dataIn;

        if (NULL != entry && NULL != matrixOut && matrixOut->m_numEntries < 16)
            {
            int     column = (int)(matrixOut->m_numEntries % 4);
            int     row    = (int)(matrixOut->m_numEntries / 4);

            matrixOut->m_matrix.entry[row][column] = *entry;

            matrixOut->m_numEntries++;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractBlockMatrixFromMLeader (RotMatrixR cellMatrix) const
    {
    /*---------------------------------------------------------------------------------------------------------------
    This is a temporary workaround for lack of RealDWG API to read block transformation matrix stored in AcDbMLeader.
    The block matrix is saved via DXF group codes 47's, they may be different than the matrix for the mleader itself.
    An example is seen via command SCALE, which results in a scale factor stored in this matrix but not in mleader 
    matrix neither reflected in AcDbMLeader::scale().  Autodesk has promised to add a new API to read the block matrix
    in RealDWG R2015.  We shall replace this workaround when the new API becomes available in the future.
    ---------------------------------------------------------------------------------------------------------------*/
    MLeaderBlockMatrix  blockMatrix;
    ExtractionFiler     filer (GetBlockMatrixCallback, m_mleader->database(), &blockMatrix);

    filer.ExtractFrom (m_mleader);

    if (16 == blockMatrix.m_numEntries)
        {
        RealDwgUtil::RotMatrixFromGeMatrix3d (cellMatrix, blockMatrix.m_matrix);
        return  RealDwgSuccess;
        }
    else
        {
        RealDwgUtil::RotMatrixFromArbitraryGeAxis (cellMatrix, m_mleader->normal());
        return  BadDataSequence;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddTagFromAttribute (EditElementHandleR outTag, UInt32& tagNo, DPoint3dR origin, AcDbAttribute* acAttribute, AcDbObjectId blockId) const
    {
    ElementId       attrsetDefId = m_toDgnContext->GetFileHolder().GetTagSetDefinitionId (blockId);
    RealDwgStatus   status = m_toDgnContext->TagElementFromAttribute (outTag, acAttribute, &origin, m_noteCellId, attrsetDefId, tagNo, m_isAnnotative, m_annotationScale);

    if (RealDwgSuccess == status)
        tagNo++;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddItemTypeFieldFromAttribute (EditElementHandleR outText, CustomItemHost& itemTypeHost, DgnECInstancePtr& itemTypeInstance, AcDbAttribute* acAttribute, AcDbBlockTableRecord* acBlock) const
    {
    RealDwgStatus   status = CantCreateItemType;
    UInt32          color = m_toDgnContext->GetDgnColor (acAttribute->color());
    bool            moreThan1Line = acAttribute->isMTextAttribute ();
    bool            hasFields = false, isEmpty = false;
    TextBlock       textBlock (*m_model);

    if (moreThan1Line)
        status = m_toDgnContext->ProcessMText (textBlock, nullptr, acAttribute->getMTextAttributeConst(), color, &hasFields, m_mleader);
    else
        status = m_toDgnContext->ProcessText (textBlock, nullptr, nullptr, nullptr, nullptr, nullptr, true, &hasFields, AcDbText::cast(acAttribute), m_mleader);

    if (RealDwgSuccess != status && !(isEmpty = (EmptyText == status)))
        return  status;

    if (!itemTypeInstance.IsValid())
        {
        // the first time getting here - create an item type and an EC instance:
        const ACHAR*        blockName = nullptr;
        if (Acad::eOk != acBlock->getName(blockName) || nullptr == blockName)
            return  BadData;

        WChar       itemtypeName[2048] = { 0 };
        if (acBlock->isAnonymous() || 0 == blockName[0])
            CreateTagSetNameWithId (itemtypeName, blockName, acBlock->objectId().handle());
        else
            wcscpy (itemtypeName, blockName);

        // get the attrdef item type that was created from the block definition
        ItemTypeP   itemType = m_toDgnContext->GetItemTypeByName (itemtypeName, false);
        if (nullptr == itemType)
            return  status;

        itemTypeInstance = itemTypeHost.ApplyCustomItem (*itemType);
        if (!itemTypeInstance.IsValid())
            return  status;
        }

    const ACHAR*    attributeTag = acAttribute->tagConst ();
    WString         propName;
    ECNameValidation::EncodeToValidName (propName, WString(attributeTag));
    
    Caret           start = textBlock.Begin ();
    RunCP           run = start.GetCurrentRunCP ();
    if (moreThan1Line)
        {
        // this is from an mtext - get the first run having actual string content:
        while (nullptr != run && run->ContainsOnlyWhitespace())
            {
            if (BSISUCCESS == start.MoveToNextRun())
                run = start.GetCurrentRunCP ();
            else
                break;  // error
            }
        }

    bool            visible = m_mleader->visibility() == AcDb::kVisible && m_toDgnContext->GetDatabase()->attmode() != ATTRIBMODE_INVISIBLE;
    if (visible)
        visible = acAttribute->visibility() == AcDb::kVisible;
    if (visible)
        visible = acAttribute->isInvisible() == Adesk::kFalse;
    if (!visible || nullptr == run)
        moreThan1Line = false;

    // set a string property from this attribute
    ECValue         propValue;
    propValue.SetString (textBlock.ToString().GetWCharCP());
    if (moreThan1Line)
        propValue.SetIsReadOnly (true);

    status = CantCreateItemType;
    if (ECOBJECTS_STATUS_Success == itemTypeInstance->SetValue(propName.GetWCharCP(), propValue))
        {
        DgnElementECInstanceP   ecInstance = nullptr;
        if (!moreThan1Line && nullptr != (ecInstance = itemTypeInstance->GetAsElementInstance()))
            {
            TextFieldPtr        field = TextField::CreateForElement (*ecInstance, propName.GetWCharCP(), nullptr, *m_model);
            if (field.IsValid())
                {
                WString invalidFieldString, fieldString;
                TextField::GetInvalidFieldIndicatorString (invalidFieldString);
                if (field->GetDisplayValue (fieldString) && fieldString.CompareTo (invalidFieldString) != 0)
                    {
                    start = textBlock.Begin ();
                    if (textBlock.SetField (start, *field))
                        status = RealDwgSuccess;
                    }
                }
            }
        }

    // create a field or drop to a text node element
    if (BSISUCCESS == TextHandlerBase::CreateElement(outText, nullptr, textBlock))
        status = RealDwgSuccess;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateSharedCellFromMLeader (EditElementHandleR outElement) const
    {
    AcDbBlockTableRecordPointer     block(m_mleader->blockContentId(), AcDb::kForRead);
    if (Acad::eOk != block.openStatus())
        return  BlockTableRecordMissing;

    // get block insertion origin
    AcGePoint3d     acOrigin;
    if (Acad::eOk != m_mleader->getBlockPosition(acOrigin))
        return  EntityError;

    DPoint3d        origin;
    m_toDgnContext->GetTransformToDGN().Multiply (RealDwgUtil::DPoint3dFromGePoint3d(origin, acOrigin));

    // get block name
    AcString        blockName;
    if (Acad::eOk != block->getName(blockName))
        blockName = AcString (L"*");

    // get compound scale for the block
    double          leaderScale = m_mleader->scale ();
    AcGeScale3d     blockScale = m_mleader->blockScale ();
    if (leaderScale > TOLERANCE_ZeroScale)
        blockScale *= leaderScale;
    
    // build compound cell matrix, which should already contain the compound scale
    RotMatrix       cellMatrix;
    if (RealDwgSuccess != this->ExtractBlockMatrixFromMLeader(cellMatrix))
        cellMatrix.ScaleColumns (blockScale.sx, blockScale.sy, blockScale.sz);

    SharedCellHandler::CreateSharedCellElement (outElement, NULL, blockName, &origin, &cellMatrix, NULL, m_toDgnContext->GetThreeD(), *m_model);
    SharedCellHandler::SetDefinitionID (outElement, m_toDgnContext->ElementIdFromObjectId(m_mleader->blockContentId()));

    MSElementP      sharedCell = outElement.GetElementP ();

    // persist mleader id on the shared cell instance
    sharedCell->ehdr.uniqueId = m_noteCellId;
    // lock the shared cell instance as we are currently unable to edit it
    sharedCell->hdr.ehdr.locked = true;   

    // override symbology etc
    UInt32          level = m_toDgnContext->GetDgnLevel (m_mleader->layerId());
    UInt32          color = m_toDgnContext->GetDgnColor (m_mleader->blockColor().isByBlock() ? m_mleader->color() : m_mleader->blockColor());
    UInt32          weight = m_toDgnContext->GetDgnWeight (m_mleader->lineWeight());
    Int32           lstyle = m_toDgnContext->GetDgnStyle (m_mleader->linetypeId());
    double          lsScale = m_mleader->linetypeScale ();
    double          transparency = m_toDgnContext->GetDgnTransparency (m_mleader->transparency());

    m_toDgnContext->SetElementSymbology (outElement, level, &color, &lstyle, &weight, NULL, NULL, lsScale, transparency, DgnElementClass::Dimension);

    if (BSISUCCESS != SharedCellHandler::CreateSharedCellComplete(outElement))
        {
        DIAGNOSTIC_PRINTF ("Failed to create shared cell %ls for mleader %I64d!\n", blockName.kwszPtr(), m_noteCellId);
        return  CantCreateCell;
        }

    // set annotation scale if the mleader is annotative in modelspace:
    if (m_isAnnotative && BSISUCCESS != RealDwgUtil::SetElementAnnotative(outElement, NULL, &m_annotationScale))
        m_isAnnotative = false;

    m_isSharedCellCreated = true;

    // add tags - also do not allow editing them
    AcDbBlockTableRecordIterator*   iterator = NULL;
    if (block->hasAttributeDefinitions() && Acad::eOk == block->newIterator(iterator))
        {
        DgnECInstancePtr        itemTypeInstance;
        CustomItemHost          itemTypeHost (outElement, true);
        MSElementDescrP         elemChain = nullptr, lastElem = nullptr;
        UInt32                  tagNo = 1;

        for (iterator->start(); !iterator->done(); iterator->step())
            {
            AcDbObjectId        objectId;
            if (Acad::eOk != iterator->getEntityId(objectId) || !objectId.objectClass()->isDerivedFrom(AcDbAttributeDefinition::desc()))
                continue;

            // get attribute from definition id
            AcDbAttribute*      attribute = NULL;
            if (Acad::eOk != m_mleader->getBlockAttributeValue(objectId, attribute) || NULL == attribute)
                continue;

            RealDwgStatus       status = RealDwgSuccess;
            EditElementHandle   eeh;

            if (m_toDgnContext->GetSettings().AttributesAsTags())
                status = this->AddTagFromAttribute (eeh, tagNo, origin, attribute, block->objectId());
            else
                status = this->AddItemTypeFieldFromAttribute (eeh, itemTypeHost, itemTypeInstance, attribute, block);

            if (RealDwgSuccess == status && eeh.IsValid())
                {
                m_toDgnContext->SetElementSymbology (eeh, level, &color, &lstyle, &weight, NULL, NULL, lsScale, transparency, DgnElementClass::Dimension);

                // lock the tag or text from editing
                eeh.GetElementP()->hdr.ehdr.locked = true;
                eeh.GetElementP()->ehdr.uniqueId = m_toDgnContext->GetAndIncrementNextId();

                MSElementDescr::InitOrAddToChainWithTail (&elemChain, &lastElem, eeh.ExtractElementDescr());
                }

            delete attribute;
            }

        if (!m_toDgnContext->GetSettings().AttributesAsTags() && itemTypeInstance.IsValid())
            itemTypeInstance->ScheduleWriteChanges (outElement);

        if (nullptr != elemChain)
            {
            // add the text chain to cell element to be cheked in together
            MSElementDescrP     cellElmdscr = outElement.ExtractElementDescr ();
            if (nullptr != cellElmdscr)
                {
                cellElmdscr->AddToChain (elemChain);
                outElement.SetElementDescr (cellElmdscr, true, false, m_model);
                }
            else
                {
                elemChain->Release ();
                }
            }
        }

    // add linkages etc to the shared cell
    m_toDgnContext->ExtractLinkagesAndGraphicGroupFromEntityXData (outElement, m_mleader);
    m_toDgnContext->ExtractXAttributesFromExtensionDictionary (outElement, m_mleader);
    if (m_hasHyperlink)
        m_toDgnContext->AddPostProcessObject (m_mleader);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CopyAndAttachDgnLinksToTarget (EditElementHandleR newTarget, ElementHandleCR originalTarget) const
    {
    StatusInt               status = BSISUCCESS;
    XAttributeHandlerId     handlerId (XATTRIBUTEID_DesignLinks, 0x00);

    for (ElementHandle::XAttributeIter iter(originalTarget, handlerId); iter.IsValid(); iter.ToNext())
        {
        UInt32      size = iter.GetSize ();
        byte*       data = (byte*)malloc(size);
        if (NULL == data)
            continue;
            
        memcpy (data, iter.PeekData(), size);

        StatusInt   st = newTarget.ScheduleWriteXAttribute (handlerId, iter.GetId(), size, data);
        if (BSISUCCESS != st)
            status = st;
        if (BSISUCCESS != (st = newTarget.ReplaceInModel(newTarget.GetElementRef())))
            status = st;
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    ElementHandle   originalTarget(context.ElementIdFromObject(acObject), context.GetModel());
    if (!originalTarget.IsValid())
        return  CantAccessMstnElement;

    bool            isNote = mdlNote_isNote (originalTarget);

    /*----------------------------------------------------------------------------------------------------
    If we get here bcause of a hyperlink, we may want to add hyperlinks to all leader dimensions.  However,
    we only want to add DgnLinks, not Engineering Links, as tag tools do not support dimension element and 
    it's a waste to duplicate tag elements in the file.
    ----------------------------------------------------------------------------------------------------*/
    if (!context.GetSettings().HyperlinkAsEngineeringLink())
        {
        ElementAgenda   dimensionArray;

        if ((isNote && RealDwgUtil::GetLeaderDimensionsFromNote(&dimensionArray, NULL, originalTarget) > 0) || 
            RealDwgUtil::GetLeaderDimensions(&dimensionArray, originalTarget))
            {
            // copy design links from note or shared cell to leader dimensions
            for each (ElemAgendaEntry dimElement in dimensionArray)
                this->CopyAndAttachDgnLinksToTarget (dimElement, originalTarget);
            }
        }

    // there is no text field to add for shared cells
    if (!isNote)
        return  RealDwgSuccess;

    // we have persisted mleader ID to the note cell header, which can use mtext's ToDgn extension:
    ToDgnExtMText   mtextToDgn;
    return  mtextToDgn.ToElementPostProcess(acObject, context);
    }


}; // ToDgnExtMLeader




/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveDgnDimensionStyleAsMLeaderStyle (DimensionStyleCP dgnDimstyle, AcDbMLeaderStyle const* templateStyle)
    {
    if (NULL == dgnDimstyle)
        return  CantAccessMstnElement;

    WString                 styleName;
    if (!RealDwgUtil::GetMLeaderStyleNameFromDimstyleName(styleName, dgnDimstyle->GetName()))
        return  WrongMstnElementType;

    this->ValidateName (styleName);

    AcDbMLeaderStylePointer mleaderStyle;
    ElementId               dgnStyleId = dgnDimstyle->GetID ();
    AcDbObjectId            existingObjectId = this->ExistingObjectIdFromElementId (dgnStyleId);

    if (existingObjectId.isNull())
        {
        // not in database, may need to create a new style:
        AcDbDictionary*     dictionary = NULL;
        if (Acad::eOk != this->GetFileHolder().GetDatabase()->getMLeaderStyleDictionary(dictionary, AcDb::kForWrite))
            return CantOpenObject;

        AcDbObjectId        styleId;
        if (Acad::eOk == dictionary->getAt(styleName.c_str(), styleId) && styleId.isValid())
            mleaderStyle.open (styleId, AcDb::kForWrite);
        else if (Acad::eOk == mleaderStyle.create())
            styleId = this->AddObjectToDictionary (dictionary, mleaderStyle, styleName.c_str(), dgnStyleId);

        dictionary->close ();
        }
    else
        {
        // found by id, use existing style
        mleaderStyle.open (existingObjectId, AcDb::kForWrite);
        }
    
    if (Acad::eOk != mleaderStyle.openStatus())
        return CantOpenObject;

    if (NULL != templateStyle)
        mleaderStyle->copyFrom (templateStyle);

    if (Acad::eOk != mleaderStyle->setName(styleName.c_str()))
        return  CantCreateMleaderStyle;

    return  this->SetMLeaderStyleFromDgnDimstyle (mleaderStyle, dgnDimstyle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveDgnDimensionStyleAsActiveMLeaderStyle (DimensionStyleCP dgnDimstyle)
    {
    /*---------------------------------------------------------------------------------------------------------------
    Save active DGN dimension style to active DWG mleader style based on these scenarios:

    1) The DGN dimstyle points to a named style as the same as mleaderstyle: do nothing - keep mleaderstyle as is.
    2) The DGN dimstyle points to a named style but different than mleaderstyle: replace mleaderstyle.
    3) The DGN dimstyle is the Unnamed Style: save it to style Standard

    All other cases fall to default handling: find the style or create a new in DWG, then set mleaderstyle.
    ---------------------------------------------------------------------------------------------------------------*/
    if (NULL == dgnDimstyle)
        return  CantAccessMstnElement;

    AcDbDatabase*   database = this->GetFileHolder().GetDatabase ();
    AcDbObjectId    activeStyleId = database->mleaderstyle ();
    WString         dgnStyleName = dgnDimstyle->GetName();
    bool            isInputMLStyle = false;

    if (!dgnStyleName.empty())
        {
        AcDbObjectId    objectId = this->ExistingObjectIdFromElementId (dgnDimstyle->GetID());

        // case 1: do nothing if mleaderstyle exists and is the same as the named DGN style
        if (objectId.isValid() && objectId == activeStyleId)
            return  RealDwgSuccess;

        // case 2: replace existing active mleader style
        AcDbMLeaderStylePointer     dwgStyle (objectId, AcDb::kForRead);
        if (Acad::eOk == dwgStyle.openStatus())
            {
            database->setMLeaderstyle (objectId);
            return  RealDwgSuccess;
            }

        // handle errors by looking for the named style in existing mleader table
        isInputMLStyle = RealDwgUtil::GetMLeaderStyleNameFromDimstyleName (dgnStyleName, dgnStyleName);
        this->ValidateName (dgnStyleName);
        }
    else
        {
        // case 3: Unnamed Style - try Standard or create a new style
        dgnStyleName.assign (NAME_Standard);
        }

    // if we get here, we need to either update existing style found by name or add a new style as the active style
    AcDbSmartDictionaryPointer  tableDictionary (database->mleaderStyleDictionaryId(), AcDb::kForWrite);
    Acad::ErrorStatus           es = tableDictionary.openStatus ();
    if (Acad::eOk != es)
        return  CantOpenObject;

    AcDbMLeaderStylePointer     activeStyle;
    es = tableDictionary->getAt (dgnStyleName.c_str(), activeStyleId);

    if (Acad::eOk != es || Acad::eOk != (es = activeStyle.open(activeStyleId, AcDb::kForWrite)))
        {
        /*-------------------------------------------------------------------------------------------------
        Can't find the mleader style by name - don't bother to create a new style if the inpurt style is of 
        dimstyle type and the DWG file already has an active mleader style.
        -------------------------------------------------------------------------------------------------*/
        if (!isInputMLStyle && activeStyleId.isValid())
            return  RealDwgSuccess;
        // the input style if of type mleader style or active mleader style has never been set.
        activeStyle.create ();
        }

    if (Acad::eOk == (es = activeStyle.openStatus()))
        {
        if (activeStyle->isNewObject())
            {
            // set the new style from active dgn dimstyle
            if (RealDwgSuccess != this->SetMLeaderStyleFromDgnDimstyle(activeStyle, dgnDimstyle))
                {
                delete activeStyle;
                return  CantCreateMleaderStyle;
                }

            const ACHAR*    dwgStyleName = dgnStyleName.c_str ();
            activeStyleId = this->AddObjectToDictionary (tableDictionary, activeStyle, dwgStyleName, 0);
            }

        if (activeStyleId.isValid())
            {
            es = activeStyle.close ();
            es = database->setMLeaderstyle (activeStyleId);
            return  Acad::eOk == es ? RealDwgSuccess : EntityError;
            }
        }

    return  NullObject;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbMLeaderStyle::TextAttachmentType     GetDwgTextAttachmentType (DimStyleProp_MLNote_VerAttachment dgnAttachment)
    {
    switch (dgnAttachment)
        {
        case DIMSTYLE_VALUE_MLNote_VerAttachment_Top:
            return AcDbMLeaderStyle::kAttachmentTopOfTop;

        case DIMSTYLE_VALUE_MLNote_VerAttachment_TopLine:
            return AcDbMLeaderStyle::kAttachmentMiddleOfTop;

        case DIMSTYLE_VALUE_MLNote_VerAttachment_Middle:
            return AcDbMLeaderStyle::kAttachmentMiddle;

        case DIMSTYLE_VALUE_MLNote_VerAttachment_BottomLine:
        case DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicLine:
            return AcDbMLeaderStyle::kAttachmentMiddleOfBottom;

        case DIMSTYLE_VALUE_MLNote_VerAttachment_Bottom:
        case DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicCorner:
            return AcDbMLeaderStyle::kAttachmentBottomOfBottom;

        case DIMSTYLE_VALUE_MLNote_VerAttachment_Underline:
            return AcDbMLeaderStyle::kAttachmentBottomLine;
        }

    // should never hit here!
    return  AcDbMLeaderStyle::kAttachmentMiddle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetMLeaderStyleFromDgnDimstyle (AcDbMLeaderStyle* mleaderStyle, DimensionStyleCP dgnDimstyle)
    {
    // our mleader only supports text content
    Acad::ErrorStatus   es = mleaderStyle->setContentType (AcDbMLeaderStyle::kMTextContent);
    if (Acad::eOk != es)
        return  CantCreateMleaderStyle;

    bool            scaleByModel = false;
    double          scale = this->GetEffectiveAnnotationScale (scaleByModel, dgnDimstyle, NULL, DimensionType::None, this->GetModel());

    // it's an annotative style only if scaled by model
    es = mleaderStyle->setAnnotative (scaleByModel);
    es = mleaderStyle->setScale (scale);

    // set text style and get active text height
    double          activeTextHeight = 0.2;
    AcDbObjectId    textStyleId;
    if (this->FindTextstyleForDimstyle(textStyleId, activeTextHeight, false, scale, dgnDimstyle))
        es = mleaderStyle->setTextStyleId (textStyleId);

    int             iValue = 0;
    UInt32          uValue = 0;
    bool            bValue = false;
    double          dValue = 0.0;
    double          uorScale = this->GetScaleFromDGN ();

    // set proper text height from dimstyle if overridden
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_Text_OverrideHeight_BOOLINT) && bValue &&
        BSISUCCESS == dgnDimstyle->GetDistanceProp(dValue, DIMSTYLE_PROP_Text_Height_DISTANCE, this->GetModel()) && dValue > TOLERANCE_ZeroSize)
        activeTextHeight = dValue * uorScale;
    // make sure we have a valid text height
    if (activeTextHeight < 1.e-8 || std::numeric_limits<double>::infinity() == activeTextHeight)
        activeTextHeight = 0.18;
    // now set the text height
    es = mleaderStyle->setTextHeight (activeTextHeight);

    // set text frame
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_MLNote_FrameType_INTEGER))
        es = mleaderStyle->setEnableFrameText (iValue > DIMSTYLE_VALUE_MLNote_FrameType_Line);

    // Currently DGN only has horizontal attachment - we can't attach leaders vertically to the text
    if (!this->SavingChanges())
        es = mleaderStyle->setTextAttachmentDirection (AcDbMLeaderStyle::kAttachmentHorizontal);

    // text rotation
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_MLNote_TextRotation_INTEGER))
        {
        switch (iValue)
            {
            default:
            case DIMSTYLE_VALUE_MLNote_TextRotation_Horizontal:
                es = mleaderStyle->setTextAngleType (AcDbMLeaderStyle::kHorizontalAngle);
                break;
            case DIMSTYLE_VALUE_MLNote_TextRotation_Vertical:
                // DWG does not have this option - set to insert angle
                es = mleaderStyle->setTextAngleType (AcDbMLeaderStyle::kInsertAngle);
                break;
            case DIMSTYLE_VALUE_MLNote_TextRotation_Inline:
                es = mleaderStyle->setTextAngleType (AcDbMLeaderStyle::kAlwaysRightReadingAngle);
                break;
            }
        }

    // spline or straight leader line
    AcDbMLeaderStyle::LeaderType    leaderType = AcDbMLeaderStyle::kStraightLeader;
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_MLNote_LeaderType_BOOLINT))
        leaderType = bValue? AcDbMLeaderStyle::kSplineLeader : AcDbMLeaderStyle::kStraightLeader;
    mleaderStyle->setLeaderLineType (leaderType);

    // leader line color
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_General_OverrideColor_BOOLINT) && bValue &&
        BSISUCCESS == dgnDimstyle->GetColorProp(uValue, DIMSTYLE_PROP_General_Color_COLOR))
        es = mleaderStyle->setLeaderLineColor (this->GetColorFromDgn(uValue, mleaderStyle->leaderLineColor().colorIndex()));

    // leader line weight
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_General_OverrideWeight_BOOLINT) && bValue &&
        BSISUCCESS == dgnDimstyle->GetWeightProp(uValue, DIMSTYLE_PROP_General_Weight_WEIGHT))
        es = mleaderStyle->setLeaderLineWeight (this->GetLineWeightFromDgn(uValue, mleaderStyle->leaderLineWeight()));

    // leader line linestyle
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_General_OverrideLineStyle_BOOLINT) && bValue &&
        BSISUCCESS == dgnDimstyle->GetLineStyleProp(iValue, DIMSTYLE_PROP_General_LineStyle_LINESTYLE))
        es = mleaderStyle->setLeaderLineTypeId (this->GetLineTypeFromDgn(iValue));

    // left attachment
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_MLNote_VerLeftAttachment_INTEGER))
        es = mleaderStyle->setTextAttachmentType (GetDwgTextAttachmentType((DimStyleProp_MLNote_VerAttachment)iValue), AcDbMLeaderStyle::kLeftLeader);
 
    // right attachment
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_MLNote_VerRightAttachment_INTEGER))
        es = mleaderStyle->setTextAttachmentType (GetDwgTextAttachmentType((DimStyleProp_MLNote_VerAttachment)iValue), AcDbMLeaderStyle::kRightLeader);

    // text justification
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_MLNote_Justification_INTEGER))
        {
        switch (iValue)
            {
            default:
            case DIMSTYLE_VALUE_MLNote_Justification_Left:
                es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kLeftAlignment);
                es = mleaderStyle->setTextAlignAlwaysLeft (true);
                break;
            case DIMSTYLE_VALUE_MLNote_Justification_Right:
                es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kRightAlignment);
                break;
            case DIMSTYLE_VALUE_MLNote_Justification_Center:
                es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kCenterAlignment);
                break;
            }
        }
    
    // text margin - a multiply of text height
    if (BSISUCCESS == dgnDimstyle->GetDoubleProp(dValue, DIMSTYLE_PROP_MLNote_LeftMargin_DOUBLE))
        es = mleaderStyle->setLandingGap (activeTextHeight * dValue);

    // in-line leader toggle
    if (BSISUCCESS == dgnDimstyle->GetBooleanProp(bValue, DIMSTYLE_PROP_MLNote_ShowLeader_BOOLINT))
        es = mleaderStyle->setEnableDogleg (bValue);

    // in-line leader length
    if (BSISUCCESS == dgnDimstyle->GetDoubleProp(dValue, DIMSTYLE_PROP_MLNote_ElbowLength_DOUBLE))
        es = mleaderStyle->setDoglegLength (activeTextHeight * dValue);

    // we don't have the option to extend leader to text - turn it off
    if (!this->SavingChanges())
        es = mleaderStyle->setExtendLeaderToText (false);

    // terminator
    if (BSISUCCESS == dgnDimstyle->GetIntegerProp(iValue, DIMSTYLE_PROP_Terminator_Note_INTEGER))
        {
        DimTerminatorInfo       termInfo;
        this->GetTerminatorInfo (termInfo, iValue, dgnDimstyle);

        // terminator size
        double      termWidth = 0.0, termHeight = 0.0;
        dgnDimstyle->GetDoubleProp (termWidth, DIMSTYLE_PROP_Terminator_Width_DOUBLE);
        dgnDimstyle->GetDoubleProp (termHeight, DIMSTYLE_PROP_Terminator_Height_DOUBLE);

        double      termSize = termWidth * activeTextHeight;
        termHeight *= activeTextHeight;

        if (TERMTYPE_UniformlyScaledCell == termInfo.m_termType)
            {
            double  uorPerStorage = m_model->GetModelInfo().GetUorPerStorage ();

            // terminator size acts as a scale factor, not actual size
            scale = uorPerStorage < TOLERANCE_ZeroScale ? 1.0 : this->GetScaleToDGN() / uorPerStorage;
            termSize *= scale;
            termHeight *= scale;
            }

        if (termSize > TOLERANCE_ZeroScale)
            es = mleaderStyle->setArrowSize (termSize);

        // create a block if needed
        AcDbObjectId    termBlockId;
        this->GetOrCreateTerminatorBlock (termBlockId, termInfo, termSize, termHeight, false);

        if (termBlockId.isValid())
            es = mleaderStyle->setArrowSymbolId (termBlockId);
        }
    
    return  RealDwgSuccess;
    }


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/12
+===============+===============+===============+===============+===============+======*/
class           ConvertMultileaderFromDgnNote
{
private:
    struct LeaderCluster
    {
    private:
        int                         m_clusterIndex;
        DPoint3d                    m_hookPoint;
    public:
        LeaderCluster (int index, DPoint3d point) : m_clusterIndex(index), m_hookPoint(point) {;}
        int                         GetClusterIndex () const { return m_clusterIndex; }
        DPoint3dCR                  GetHookPoint () const { return m_hookPoint; }
    };
    
    ConvertFromDgnContextP          m_fromDgnContext;
    ElementHandleCP                 m_noteCellElement;
    NoteCellHeaderHandler*          m_noteHandler;
    double                          m_noteTextHeight;
    DPoint3d                        m_noteTextOrigin;
    RotMatrix                       m_noteTextMatrix;
    double                          m_lastLineDescender;
    DRange3d                        m_textExactRange;
    DRange3d                        m_textNominalRange;
    double                          m_attachmentTolerance;
    TextElementJustification        m_textJustification;
    DPoint3d                        m_textFrameBottomCenter;
    DPoint3d                        m_textFrameTopCenter;
    bvector<LeaderCluster>          m_leaderCluster;
    bool                            m_isDwgTextBlock;
    bool                            m_isAnnotative;
    double                          m_annotationScale;
    

public:
    // the constructor
    ConvertMultileaderFromDgnNote (ElementHandleCR noteElement, ConvertFromDgnContextR context)
        {
        m_noteCellElement = &noteElement;
        m_fromDgnContext = &context;
        m_noteHandler = dynamic_cast <NoteCellHeaderHandler*> (&noteElement.GetHandler());
        m_noteTextHeight = 0.0;
        m_noteTextOrigin.Init (0.0, 0.0, 0.0);
        m_textFrameBottomCenter.Init (0.0, 0.0, 0.0);
        m_textFrameTopCenter.Init (0.0, 0.0, 0.0);
        m_noteTextMatrix.InitIdentity ();
        m_lastLineDescender = 0.0;
        m_attachmentTolerance = 10.0;
        m_textExactRange = DRange3d::NullRange ();
        m_textNominalRange = DRange3d::NullRange ();
        m_leaderCluster.clear ();
        m_isDwgTextBlock = false;
        m_isAnnotative = false;
        m_annotationScale = 1.0;
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateMLeaderFromNote (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementAgendaCR dimensionArray)
    {
    AcDbMTextPointer    mtext;
    if (Acad::eOk != mtext.create())
        return  OutOfMemoryError;

    RealDwgStatus       status = this->CreateMTextFromNote (mtext);
    if (RealDwgSuccess != status)
        {
        this->DropDimensions (dimensionArray);
        return  CantCreateMleader;
        }

    acObject = m_fromDgnContext->InstantiateOrUseExistingObject (existingObject, AcDbMLeader::desc());

    AcDbMLeader*        mleader = AcDbMLeader::cast (acObject);
    if (NULL == mleader)
        return  CantCreateMleader;

    this->GetAnnotationScale (dimensionArray);

    // set mleader style & overrides
    if (RealDwgSuccess != this->ApplyMLeaderStyleToMLeader(mleader))
        {
        DIAGNOSTIC_PRINTF ("Dropping mleader to single mtext+leaders %I64d\n", m_noteCellElement->GetElementId());
        return  this->CreateMTextAndSingleLeaders (acObject, NULL != existingObject, mtext, dimensionArray);
        }

    // set mleader plane from the first dimension leader
    this->SetMLeaderPlane (mleader);

    // remove existing leader clusters prior to adding new ones
    AcArray<int>        leaderClusters;
    Acad::ErrorStatus   es = mleader->getLeaderIndexes (leaderClusters);
    if (Acad::eOk == es)
        {
        for (int i = 0; i < leaderClusters.length(); i++)
            {
            AcArray<int>    clusterLeaders;
            if (Acad::eOk == (es = mleader->getLeaderLineIndexes(i, clusterLeaders)))
                {
                for (int j = 0; j < clusterLeaders.length(); j++)
                    es = mleader->removeLeaderLine (j);
                }
            es = mleader->removeLeader (i);
            }
        }

    /*------------------------------------------------------------------------------------------------------------
    Add clusters of leaders:

    Currently, a DGN multileader only has a single cluster.  ACAD multileader editor automatically arranges leader 
    lines to the nearest clusters.  Fortunately, RealDWG allows us to force leader lines in a desired cluster, 
    index 0 in our case.  Although ACAD mleader editor will change it upon editing, at least the initial display 
    stays correct.

    To round trip unmodified DWG mleader back to DWG, we collect cluster's hook points as we walk through the leaders 
    and place them to the clusters that match their hook points.
    -------------------------------------------------------------------------------------------------------------*/
    for each (ElemAgendaEntry dimElement in dimensionArray)
        {
        DimensionElmCP  dim = (DimensionElmCP) dimElement.GetElementCP ();
        if (NULL == dim || dim->nPoints < 2)
            continue;

        int         lastPointIndex = dim->nPoints - 1;
        DPoint3d    lastPoint = dim->GetPoint (lastPointIndex);
        int         clusterNo = this->GetLeaderClusterIndex (lastPoint);
        int         leaderNo = 0;
        bool        isLeaderAdded = false;

        for (int i = lastPointIndex; i >= 0; i--)
            {
            DPoint3d    point = dim->GetPoint (i);

            if (i == lastPointIndex)
                {
                // add a new leader line, and probably also a new cluster to the mleader for the 1st point.
                es = this->AddLeaderLine (mleader, leaderNo, clusterNo, dim->GetPoint(--i), point);
                isLeaderAdded = Acad::eOk == es;
                }
            else
                {
                // insert rest of the points on the new leader line
                m_fromDgnContext->GetTransformFromDGN().Multiply (point);
                es = mleader->addFirstVertex (leaderNo, RealDwgUtil::GePoint3dFromDPoint3d(point));
                }

            if (Acad::eOk != es)
                DIAGNOSTIC_PRINTF ("Error adding vertex for mleader %I64d, [%ls]\n", m_noteCellElement->GetElementId(), acadErrorStatusText(es));
            }

        // update our cluster-hookpoint array from the newly added leader line
        if (isLeaderAdded)
            this->UpdateLeaderClusterIndex (clusterNo, lastPoint);
        }

    // set mtext as the last creation step
    if (Acad::eOk != (es = mleader->setMText(mtext)))
        return  this->CreateMTextAndSingleLeaders (acObject, NULL != existingObject, mtext, dimensionArray);

    // from here on we attempt to check the results and handle errors
    AcDbMText*      newMText = mleader->mtext ();
    if (NULL != newMText)
        {
        // try validating the new mtext.  Handle error by dropping the note to mtext + single leaders
        if (!this->ValidateMTextLocation(mleader, newMText, mtext))
            return this->CreateMTextAndSingleLeaders (acObject, NULL != existingObject, mtext, dimensionArray);

        delete newMText;
        }
    else
        {
        this->DropDimensions (dimensionArray);

        // return error to let the caller to delete the mleader object and drop the note cell element to mtext
        return  CantCreateMleader;
        }

    return  RealDwgSuccess;
    }


private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateMTextFromNote (AcDbMText*& acMText)
    {
    // get the text component
    TextBlockPtr    textBlock = m_noteHandler->GetTextPart (*m_noteCellElement, ITextPartId());
    if (textBlock.IsNull())
        return  MstnElementUnacceptable;

    if (textBlock->IsEmpty())
        return EmptyText;

    if (!textBlock->IsMTextCompatible(m_noteCellElement->GetModelRef()))
        return  IncompatibleText;

    // check text block type prior to converting it to mtext
    m_isDwgTextBlock = textBlock->IsMTextType ();

    // try creating an mtext from the text block:
    RealDwgStatus   status = m_fromDgnContext->MTextFromTextBlock (acMText, *textBlock.get(), *m_noteCellElement, false);

    // save text block params before we destroy it
    m_noteTextHeight = m_fromDgnContext->GetTextSizeFromTextBlock(NULL, *textBlock.get());
    m_noteTextOrigin = textBlock->GetTextAutoCADOrigin ();
    // TextBlock's matrix is global - make it local - TFS 614327
    m_noteTextMatrix.InitProduct (textBlock->GetOrientation(), m_fromDgnContext->GetLocalTransform());
    m_textJustification = textBlock->GetProperties().GetNodeJustification ();
    m_textExactRange = textBlock->GetExactRange ();
    m_textNominalRange = textBlock->GetNominalRange ();

    // to get the vertical attachment points at top & bottom, we need text margin + descender
    LineCP          lastLine = textBlock->End().GetCurrentLineCP ();
    if (NULL != lastLine)
        m_lastLineDescender = lastLine->GetExactRange().low.y;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateMTextAndSingleLeaders (AcDbObjectP& acObject, bool updating, AcDbMText const* newMText, ElementAgendaCR dimensionArray)
    {
    Acad::ErrorStatus   es;
    AcDbObjectId        mtextObjId = acObject->objectId ();
    AcDbMText*          acMText = new AcDbMText ();

    acMText->copyFrom (newMText);

    if (mtextObjId.isValid())
        {
        es = acObject->handOverTo (acMText, true, false);
        if (Acad::eObjectToBeDeleted == es && !updating)
            {
            delete acObject;
            acObject = NULL;
            }
        mtextObjId = acMText->objectId ();
        }
    else
        {
        mtextObjId = m_fromDgnContext->AddEntityToCurrentBlock (acMText, m_noteCellElement->GetElementId());
        }

    if (!mtextObjId.isValid())
        {
        delete acMText;
        return  DropFailed;
        }

    // set annotation scale if in the default model:
    double              annoScale = 1.0;
    if (m_noteHandler->HasAnnotationScale(&annoScale, *m_noteCellElement) && m_fromDgnContext->CanSaveAnnotationScale())
        m_fromDgnContext->AddAnnotationScaleToObject (acMText, annoScale, 0);
    else
        RealDwgUtil::SetObjectAnnotative (acMText, false);

    // close mtext object prior to creating leaders
    es = acMText->close ();

    AcDbObjectIdArray   leaderIdArray;

    for each (ElemAgendaEntry dimElement in dimensionArray)
        {
        // create a new leader, and post it to database as required by AcDbLeader::attachAnnotation
        AcDbLeader*     acLeader = new AcDbLeader ();
        if (m_fromDgnContext->AddEntityToCurrentBlock((AcDbEntity*)acLeader, dimElement.GetElementId()).isValid())
            {
            leaderIdArray.append (acLeader->objectId());

            ConvertLeaderFromDgnDimension   leaderFromDgn (dimElement, m_noteCellElement, *m_fromDgnContext);
            leaderFromDgn.SetLeaderFromNoteDimension (acLeader);

            acLeader->close ();
            }
        else
            {
            delete acLeader;
            }
        }

    // re-open mtext to add leader ID reactors
    es = acdbOpenObject (acMText, mtextObjId, AcDb::kForWrite);
    if (Acad::eOk == es)
        {
        for (int i = 0; i < leaderIdArray.length(); i++)
            acMText->addPersistentReactor (leaderIdArray.at(i));

        if (updating)
            acMText->close ();
        else
            acObject = acMText;
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Error opening mtext %I64d, [%ls]\n", m_noteCellElement->GetElementId(), acadErrorStatusText(es));
        // don't want the caller to attempt to drop the note cell
        return  RealDwgIgnoreElement;
        }

    return  ReplacedObjectType;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropDimensions (ElementAgendaCR dimensionArray)
    {
    // drop dimensions to wireframe
    DropGeometry        dropDimOptions (DropGeometry::OPTION_Dimensions);
    dropDimOptions.SetDimensionOptions (DropGeometry::DIMENSION_Geometry);

    RealDwgStatus       status = RealDwgSuccess;
    for each (ElemAgendaEntry dimElement in dimensionArray)
        {
        AcDbObject*     nullObj = NULL;
        RealDwgStatus   dropStatus = m_fromDgnContext->DropElementToDwg (nullObj, NULL, dimElement, dropDimOptions);
        if (RealDwgSuccess != dropStatus)
            {
            DIAGNOSTIC_PRINTF ("Failed dropping leader dimension %I64d!\n", dimElement.GetElementId());
            status = dropStatus;
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidateMTextLocation (AcDbMLeader* mleader, AcDbMText* newMText, AcDbMText* oldMText)
    {
    /*----------------------------------------------------------------------------------------------------
    Autodesk confirms that RealDWG does indeed change the mtext's attachment when creating an mleader based
    on mleader's alignment type:

    MLeader's alignment type:                       MLeader's MText attachment type:
    -------------------------                       --------------------------------
    AcDbMLeaderStyle::kLeftAlignment                AcDbMText::kTopLeft
    AcDbMLeaderStyle::kRightAlignment               AcDbMText::kTopRight
    All other types                                 AcDbMText::kTopCenter

    When this happens, the text becomes out of position.  Until Autodesk fixed this problem we are left 
    with little choice but reset the mtext to match the new attachment such that our text can stay where
    it should be.
    ----------------------------------------------------------------------------------------------------*/
    AcGePoint3d                 oldOrigin = oldMText->location ();
    AcGePoint3d                 newOrigin = newMText->location ();
    AcDbMText::AttachmentPoint  oldAttachment = oldMText->attachment ();
    AcDbMText::AttachmentPoint  newAttachment = newMText->attachment ();
    double                      samePointTol = sqrt(m_textExactRange.ExtentSquared()) * m_fromDgnContext->GetScaleFromDGN() * 1.0E-4;

    if (newAttachment != oldAttachment || !newOrigin.isEqualTo(oldOrigin, RealDwgUtil::GeTolFromDouble(samePointTol)))
        {
        // now change our calculated mtext to match mleader's insisted attachment(above chart), and reset its origin:
        Acad::ErrorStatus       es = oldMText->setAttachmentMovingLocation (newAttachment);

        newOrigin = oldMText->location ();

        /*------------------------------------------------------------------------------------------------
        TextBlock::AdjustOriginForJustification() changes text origin for DGN type but not for DWG type. 
        This method effectively results in a vertical difference in the origin for an otherwise same text.  
        We need to compensate the difference here. Bug# 2610.
        ------------------------------------------------------------------------------------------------*/
        if (m_isDwgTextBlock && (AcDbMText::kBottomLeft == oldAttachment || AcDbMText::kBottomRight == oldAttachment || AcDbMText::kBottomCenter == oldAttachment))
            {
            DPoint3d            shift = DPoint3d::From (0.0, m_textNominalRange.high.y - m_textNominalRange.low.y, 0.0);

            m_noteTextMatrix.Multiply (shift);
            shift.Add (m_noteTextOrigin);
            m_fromDgnContext->GetTransformFromDGN().Multiply (shift);

            newOrigin = RealDwgUtil::GePoint3dFromDPoint3d (shift);
            }

        // reset the mleader with the validated mtext and location
        if (Acad::eOk == es && 
            Acad::eOk == (es = mleader->setMText(oldMText)) && 
            Acad::eOk == (es = mleader->setTextLocation(newOrigin)))
            return  true;

        DIAGNOSTIC_PRINTF ("Dropping multi-note %I64d to mtext+single leaders! [%ls]\n", m_noteCellElement->GetElementId(), acadErrorStatusText(es));
        return  false;
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ComputeAndSaveTextParamsFromDgnDimstyle (DimensionStyleP dgnDimstyle)
    {
    // override text height from the note to ensure correct display
    if (m_noteTextHeight > TOLERANCE_TextHeight)
        {
        dgnDimstyle->SetBooleanProp (true, DIMSTYLE_PROP_Text_OverrideHeight_BOOLINT);
        dgnDimstyle->GetDistanceProp (m_noteTextHeight, DIMSTYLE_PROP_Text_Height_DISTANCE, m_noteCellElement->GetDgnModelP());
        }

    // first get text lower margin
    double      lowerMargin = 0.0;
    if (BSISUCCESS == dgnDimstyle->GetDoubleProp(lowerMargin, DIMSTYLE_PROP_MLNote_LowerMargin_DOUBLE))
        {
        bool    scaleByModel = false;
        double  dimscale = m_fromDgnContext->GetEffectiveAnnotationScale (scaleByModel, dgnDimstyle, NULL, DimensionType::None, m_noteCellElement->GetDgnModelP());
        lowerMargin *= dimscale * m_noteTextHeight;
        m_attachmentTolerance = 0.5 * lowerMargin;
        }

    // set text range origin at lower-left corner of the text frame, on the text's plane
    DPoint3d    frameOrigin = m_noteTextOrigin;
    m_noteTextMatrix.MultiplyTranspose (frameOrigin);
    frameOrigin.y += m_lastLineDescender - lowerMargin;

    // bottom-center is half way from the text frame origin on the x-direction
    m_textFrameBottomCenter = frameOrigin;
    m_textFrameBottomCenter.x += 0.5 * m_textExactRange.XLength ();

    // top-center is the same as bottom-center, with a height of the text frame
    m_textFrameTopCenter = m_textFrameBottomCenter;
    m_textFrameTopCenter.y += m_textExactRange.YLength() + (2 * lowerMargin);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ApplyMLeaderStyleToMLeader (AcDbMLeader* mleader)
    {
    AcDbObjectId        mleaderStyleId;

    // get dgn dimstyle from the first dimension
    DimensionStylePtr   dgnDimstyle = m_noteHandler->GetNoteDimensionStyle (*m_noteCellElement);
    if (!dgnDimstyle.IsValid())
        return  MstnElementUnacceptable;

    // find text params needed for later use
    this->ComputeAndSaveTextParamsFromDgnDimstyle (dgnDimstyle.get());

    // try to get existing mleader style in file and create a new style for the mleader
    mleaderStyleId = m_fromDgnContext->ExistingObjectIdFromElementId (dgnDimstyle->GetID(), AcDbMLeaderStyle::desc());

    AcDbMLeaderStylePointer     styleFromFile(mleaderStyleId, AcDb::kForRead);
    AcDbMLeaderStyle*           styleFromNote = new AcDbMLeaderStyle ();

    // get an equivelent mleader style from the note
    if (RealDwgSuccess != m_fromDgnContext->SetMLeaderStyleFromDgnDimstyle(styleFromNote, dgnDimstyle.get()))
        {
        delete styleFromNote;
        return  CantCreateMleaderStyle;
        }

    // update proprties of the style from the note based on individual mleaders
    this->UpdateMLeaderStyleFromNote (styleFromNote, mleader, dgnDimstyle.get());
    
    // if we don't already have an mleader style from file, we must resolve it for the mleader
    if (Acad::eOk != styleFromFile.openStatus())
        {
        // get mleader style name from dimstyle name
        WString             styleName;
        if (!RealDwgUtil::GetMLeaderStyleNameFromDimstyleName(styleName, dgnDimstyle->GetName()))
            styleName = dgnDimstyle->GetName ();

        m_fromDgnContext->ValidateName (styleName);

        // no style with the same id exists - search mleader dictionary by name
        AcDbDictionary*     dictionary = NULL;
        Acad::ErrorStatus   es = m_fromDgnContext->GetFileHolder().GetDatabase()->getMLeaderStyleDictionary (dictionary, AcDb::kForWrite);
        if (Acad::eOk != es)
            {
            delete styleFromNote;
            return  CantOpenObject;
            }

        if (!styleName.empty())
            {
            // if an mleader style with the same name exists, use it
            es = dictionary->getAt (styleName.c_str(), mleaderStyleId);
            if (Acad::eOk == es)
                styleFromFile.open (mleaderStyleId, AcDb::kForRead);

            // if we did not find the style by name, add the one from the note as a new entry and use it
            if (Acad::eOk != (es = styleFromFile.openStatus()))
                {
                mleaderStyleId = m_fromDgnContext->AddObjectToDictionary (dictionary, styleFromNote, styleName.c_str(), 0);

                if (mleaderStyleId.isValid())
                    {
                    if (Acad::eOk == styleFromFile.acquire(styleFromNote))
                        styleFromNote = NULL;

                    es = styleFromFile->setName (styleName.c_str());
                    }
                }
            }

        if (Acad::eOk != styleFromFile.openStatus())
            {
            // try use the first style in the dictionary if not empty
            AcDbDictionaryIterator* iterator = dictionary->newIterator ();
            if (!iterator->done())
                {
                mleaderStyleId = iterator->objectId ();
                es = styleFromFile.open (mleaderStyleId, AcDb::kForRead);
                }

            delete iterator;
            }

        if (Acad::eOk != styleFromFile.openStatus())
            {
            // the seed file does not not mleader style at all, add "Standard" and use it
            mleaderStyleId = m_fromDgnContext->AddObjectToDictionary (dictionary, styleFromNote, NAME_Standard, 0);

            if (Acad::eOk == styleFromFile.acquire(styleFromNote))
                styleFromNote = NULL;
            }

        dictionary->close ();
        }

    // if we still don't have a style in file, something is terribly wrong - bail it out
    if (Acad::eOk != styleFromFile.openStatus())
        {
        if (NULL != styleFromNote)
            {
            if (styleFromNote->objectId().isValid())
                styleFromNote->close ();
            else
                delete styleFromNote;
            }
        return  CantCreateMleaderStyle;
        }

    // we have a valid mleader style in file, let the mleader use it
    mleader->setMLeaderStyle (mleaderStyleId);

    /*--------------------------------------------------------------------------------------------------
    If we hit here, the style from file may differ from the one from the note, so we want to compare and 
    add overrides as necessary.  In case there the style from the note is the same as that from the table,
    i.e. styleFromNote=NULL, we still have to explicitly set properties for mleader.  RealDWG does not 
    appear to populate them for us in above call.
    ---------------------------------------------------------------------------------------------------*/
    const AcDbMLeaderStyle* overridingStyle = NULL == styleFromNote ? styleFromFile.object() : styleFromNote;
    this->OverrideMLeaderStyleProperties (mleader, styleFromFile, overridingStyle);

    this->SetAnnotationScale (mleader, styleFromFile, dgnDimstyle.get());

    styleFromFile->close ();
    
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetAnnotationScale (ElementAgendaCR dimensionArray)
    {
    // set the mleader to be annotative only if the note cell as well as all dimeanion leaders are annoative
    m_isAnnotative = m_noteHandler->HasAnnotationScale (&m_annotationScale, *m_noteCellElement);
    if (!m_isAnnotative)
        return  false;

    // currently we only support modelspace annotation scale
    if (!m_fromDgnContext->CanSaveAnnotationScale())
        {
        m_isAnnotative = false;
        return  m_isAnnotative;
        }

    // if any leader dimension is not annotative, or has a different scale than the one for the note cell, the mleader is not annotative
    for each (ElementHandleCR dimension in dimensionArray)
        {
        double  dimScale = 1.0;
        if (!mdlDim_overallGetModelAnnotationScale(&dimScale, dimension) || fabs(dimScale - m_annotationScale) > TOLERANCE_ZeroScale)
            {
            m_isAnnotative = false;
            break;
            }
        }

    return  m_isAnnotative;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetAnnotationScale (AcDbMLeader* mleader, const AcDbMLeaderStyle* styleFromFile, DimensionStyleCP dgnDimstyle)
    {
    if (m_isAnnotative)
        {
        m_fromDgnContext->AddAnnotationScaleToObject (mleader, m_annotationScale, m_noteCellElement->GetElementId());

        // reset scaled sizes
        if (fabs(m_annotationScale - 1.0) > TOLERANCE_ZeroScale)
            {
            mleader->setDoglegLength (mleader->doglegLength() * m_annotationScale);
            }

        return;
        }

    // turn annotation scale off otherwise
    mleader->setEnableAnnotationScale (false);

    // reset overall scale if the style is annotative which may have caused previous setting to have failed
    if (styleFromFile->annotative())
        {
        bool    scaleByModel = false;
        double  scale = m_fromDgnContext->GetEffectiveAnnotationScale (scaleByModel, dgnDimstyle, NULL, DimensionType::None, m_noteCellElement->GetDgnModelP());
        mleader->setScale (scale);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            OverrideMLeaderStyleProperties (AcDbMLeader* mleader, const AcDbMLeaderStyle* styleFromFile, const AcDbMLeaderStyle* styleFromNote)
    {
    mleader->setOverride (AcDbMLeader::kLeaderLineType, styleFromFile->leaderLineType() != styleFromNote->leaderLineType());
    mleader->setLeaderLineType (styleFromNote->leaderLineType());

    mleader->setOverride (AcDbMLeader::kLeaderLineColor, styleFromFile->leaderLineColor() != styleFromNote->leaderLineColor());
    mleader->setLeaderLineColor (styleFromNote->leaderLineColor());

    mleader->setOverride (AcDbMLeader::kLeaderLineWeight, styleFromFile->leaderLineWeight() != styleFromNote->leaderLineWeight());
    mleader->setLeaderLineWeight (styleFromNote->leaderLineWeight());

    mleader->setOverride (AcDbMLeader::kLeaderLineTypeId, styleFromFile->leaderLineTypeId() != styleFromNote->leaderLineTypeId());
    mleader->setLeaderLineTypeId (styleFromNote->leaderLineTypeId());

    mleader->setOverride (AcDbMLeader::kEnableLanding, styleFromFile->enableLanding() != styleFromNote->enableLanding());
    mleader->setEnableLanding (styleFromNote->enableLanding());

    mleader->setOverride (AcDbMLeader::kLandingGap, fabs(styleFromFile->landingGap() - styleFromNote->landingGap()) > TOLERANCE_ZeroSize);
    mleader->setLandingGap (styleFromNote->landingGap());

    mleader->setOverride (AcDbMLeader::kEnableDogleg, styleFromFile->enableDogleg() != styleFromNote->enableDogleg());
    mleader->setEnableDogleg (styleFromNote->enableDogleg());

    // workaround a likely RealDWG bug that setting a scaled dogleg length again results in the value ended up being invert scaled - TFS#127500.
    if (fabs(styleFromFile->doglegLength() - styleFromNote->doglegLength()) > TOLERANCE_ZeroSize)
        {
        mleader->setOverride (AcDbMLeader::kDoglegLength, true);
        mleader->setDoglegLength (styleFromNote->doglegLength());
        }

    mleader->setOverride (AcDbMLeader::kArrowSymbolId, styleFromFile->arrowSymbolId() != styleFromNote->arrowSymbolId());
    mleader->setArrowSymbolId (styleFromNote->arrowSymbolId());

    mleader->setOverride (AcDbMLeader::kArrowSize, fabs(styleFromFile->arrowSize() - styleFromNote->arrowSize()) > TOLERANCE_ZeroSize);
    mleader->setArrowSize (styleFromNote->arrowSize());

    mleader->setOverride (AcDbMLeader::kTextStyleId, styleFromFile->textStyleId() != styleFromNote->textStyleId());
    mleader->setTextStyleId (styleFromNote->textStyleId());

    mleader->setOverride (AcDbMLeader::kTextLeftAttachmentType, styleFromFile->textAttachmentType(AcDbMLeaderStyle::kLeftLeader) != styleFromNote->textAttachmentType(AcDbMLeaderStyle::kLeftLeader));
    mleader->setTextAttachmentType (styleFromNote->textAttachmentType(AcDbMLeaderStyle::kLeftLeader), AcDbMLeaderStyle::kLeftLeader);

    mleader->setOverride (AcDbMLeader::kTextRightAttachmentType, styleFromFile->textAttachmentType(AcDbMLeaderStyle::kRightLeader) != styleFromNote->textAttachmentType(AcDbMLeaderStyle::kRightLeader));
    mleader->setTextAttachmentType (styleFromNote->textAttachmentType(AcDbMLeaderStyle::kRightLeader), AcDbMLeaderStyle::kRightLeader);

    mleader->setOverride (AcDbMLeader::kTextTopAttachmentType, styleFromFile->textAttachmentType(AcDbMLeaderStyle::kTopLeader) != styleFromNote->textAttachmentType(AcDbMLeaderStyle::kTopLeader));
    mleader->setTextAttachmentType (styleFromNote->textAttachmentType(AcDbMLeaderStyle::kTopLeader), AcDbMLeaderStyle::kTopLeader);

    mleader->setOverride (AcDbMLeader::kTextBottomAttachmentType, styleFromFile->textAttachmentType(AcDbMLeaderStyle::kBottomLeader) != styleFromNote->textAttachmentType(AcDbMLeaderStyle::kBottomLeader));
    mleader->setTextAttachmentType (styleFromNote->textAttachmentType(AcDbMLeaderStyle::kBottomLeader), AcDbMLeaderStyle::kBottomLeader);

    mleader->setOverride (AcDbMLeader::kTextAlignmentType, styleFromFile->textAlignmentType() != styleFromNote->textAlignmentType());
    mleader->setTextAlignmentType (styleFromNote->textAlignmentType());

    mleader->setOverride (AcDbMLeader::kTextAngleType, styleFromFile->textAngleType() != styleFromNote->textAngleType());
    mleader->setTextAngleType (styleFromNote->textAngleType());

    mleader->setOverride (AcDbMLeader::kTextAttachmentDirection, styleFromFile->textAttachmentDirection() != styleFromNote->textAttachmentDirection());
    mleader->setTextAttachmentDirection (styleFromNote->textAttachmentDirection());

    mleader->setOverride (AcDbMLeader::kTextColor, styleFromFile->textColor() != styleFromNote->textColor());
    mleader->setTextColor (styleFromNote->textColor());

    mleader->setOverride (AcDbMLeader::kTextHeight, fabs(styleFromFile->textHeight() - styleFromNote->textHeight()) > TOLERANCE_TextHeight);
    mleader->setTextHeight (styleFromNote->textHeight());

    mleader->setOverride (AcDbMLeader::kEnableFrameText, styleFromFile->enableFrameText() != styleFromNote->enableFrameText());
    mleader->setEnableFrameText (styleFromNote->enableFrameText());

    mleader->setOverride (AcDbMLeader::kExtendLeaderToText, styleFromFile->extendLeaderToText() != styleFromNote->extendLeaderToText());
    mleader->setExtendLeaderToText (styleFromNote->extendLeaderToText()); 

    mleader->setOverride (AcDbMLeader::kScale, fabs(styleFromFile->scale() - styleFromNote->scale()) > TOLERANCE_ZeroScale);
    mleader->setScale (styleFromNote->scale());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            UpdateMLeaderStyleFromNote (AcDbMLeaderStyle* mleaderStyle, AcDbMLeader* mleader, DimensionStyleCP dgnDimstyle)
    {
    DPoint3d    firstHookpoint = DPoint3d::From(0, 0, 0);
    ElementId   firstDimId = INVALID_ELEMENTID;
    if (BSISUCCESS != m_noteHandler->GetLeaderDimension(firstDimId, &firstHookpoint, *m_noteCellElement))
        return;
    
    Acad::ErrorStatus   es = Acad::eNotApplicable;

    // we don't current support bottom/top-center text attachment type, but we can round trip them
    if (this->IsNearBottomCenter(firstHookpoint) || this->IsNearTopCenter(firstHookpoint))
        {
        // make this leader to be vertically attached and also enable top & bottom attachments
        es = mleaderStyle->setTextAttachmentDirection (AcDbMLeaderStyle::kAttachmentVertical);
        es = mleaderStyle->setTextAttachmentType (AcDbMLeaderStyle::kAttachmentCenter, AcDbMLeaderStyle::kBottomLeader);
        es = mleaderStyle->setTextAttachmentType (AcDbMLeaderStyle::kAttachmentCenter, AcDbMLeaderStyle::kTopLeader);
        }
    else
        {
        // make this leader to be horizontally attached
        es = mleaderStyle->setTextAttachmentDirection (AcDbMLeaderStyle::kAttachmentHorizontal);

        // reset left/right attachment type for dynamically attached notes as DWG does not suuport dynamic attachment:
        this->UpdateTextAttachmentType (mleaderStyle, dgnDimstyle, firstHookpoint);
        }

    // override text justification from note text justification
    switch (m_textJustification)
        {
        default:
        case TextElementJustification::LeftTop:
        case TextElementJustification::LeftMiddle:
        case TextElementJustification::LeftBaseline:
        case TextElementJustification::LeftDescender:
        case TextElementJustification::LeftMarginTop:
        case TextElementJustification::LeftMarginMiddle:
        case TextElementJustification::LeftMarginBaseline:
        case TextElementJustification::LeftMarginDescender:
        case TextElementJustification::LeftCap:
        case TextElementJustification::LeftMarginCap:
            es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kLeftAlignment);
            break;
        case TextElementJustification::CenterTop:
        case TextElementJustification::CenterMiddle:
        case TextElementJustification::CenterBaseline:
        case TextElementJustification::CenterDescender:
        case TextElementJustification::CenterCap:
            es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kCenterAlignment);
            break;
        case TextElementJustification::RightTop:
        case TextElementJustification::RightMiddle:
        case TextElementJustification::RightBaseline:
        case TextElementJustification::RightDescender:
        case TextElementJustification::RightMarginTop:
        case TextElementJustification::RightMarginMiddle:
        case TextElementJustification::RightMarginBaseline:
        case TextElementJustification::RightMarginDescender:
        case TextElementJustification::RightCap:
        case TextElementJustification::RightMarginCap:
            es = mleaderStyle->setTextAlignmentType (AcDbMLeaderStyle::kRightAlignment);
            break;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            UpdateTextAttachmentType (AcDbMLeaderStyle* mleaderStyle, DimensionStyleCP dgnDimstyle, DPoint3dCR hookPoint)
    {
    // get vertical attachments for both left and right leaders
    DimStyleProp_MLNote_VerAttachment   vertLeft, vertRight;
    if (BSISUCCESS != dgnDimstyle->GetIntegerProp((int&)vertRight, DIMSTYLE_PROP_MLNote_VerRightAttachment_INTEGER) ||
        BSISUCCESS != dgnDimstyle->GetIntegerProp((int&)vertLeft, DIMSTYLE_PROP_MLNote_VerLeftAttachment_INTEGER))
        return;

    bool        rightDynamic = DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicLine == vertRight || DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicCorner == vertRight;
    bool        leftDynamic =  DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicLine == vertLeft || DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicCorner == vertLeft;

    // we only need to reset attachment types for dynamically attached notes
    if (!rightDynamic && !leftDynamic)
        return;

    // check the hookpoint against the note info to find where the leader is attached
    DPoint3d    planeHookpoint;
    m_noteTextMatrix.MultiplyTranspose (planeHookpoint, hookPoint);

    // find top/bottom by comparing y distances from the hook point to the text's top/bottom points
    bool        isTop = (m_textFrameTopCenter.y - planeHookpoint.y) < (planeHookpoint.y - m_textFrameBottomCenter.y);

    // find left/right side by comparing the x coordinates of the hook point and the text center
    AcDbMLeaderStyle::LeaderDirectionType   side = planeHookpoint.x > m_textFrameTopCenter.x ? AcDbMLeaderStyle::kRightLeader : AcDbMLeaderStyle::kLeftLeader;

    AcDbMLeaderStyle::TextAttachmentType    attachment;
    if (rightDynamic && AcDbMLeaderStyle::kRightLeader == side)
        attachment = this->AttachmentTypeFromDynamic (vertRight, isTop);
    else if (leftDynamic && AcDbMLeaderStyle::kLeftLeader == side)
        attachment = this->AttachmentTypeFromDynamic (vertLeft, isTop);
    else
        return;

    // reset attachment based on side
    mleaderStyle->setTextAttachmentType (attachment, side);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbMLeaderStyle::TextAttachmentType    AttachmentTypeFromDynamic (DimStyleProp_MLNote_VerAttachment vertAttach, bool isTop)
    {
    // map DGN dynamic attachment to top-top, middle-top, bottom-bottom, or middle-bottom:
    if (DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicLine == vertAttach)
        return  isTop ? AcDbMLeaderStyle::kAttachmentMiddleOfTop : AcDbMLeaderStyle::kAttachmentMiddleOfBottom;
    else if (DIMSTYLE_VALUE_MLNote_VerAttachment_DynamicCorner == vertAttach)
        return  isTop ? AcDbMLeaderStyle::kAttachmentTopOfTop : AcDbMLeaderStyle::kAttachmentBottomOfBottom;

    // should never reach here        
    return  AcDbMLeaderStyle::kAttachmentMiddleOfBottom;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetMLeaderPlane (AcDbMLeader* mleader)
    {
    DPoint3d    point0;
    m_fromDgnContext->GetTransformFromDGN().Multiply (point0, m_noteTextOrigin);

    DVec3d      xAxis, yAxis;
    m_noteTextMatrix.getColumns (&xAxis, &yAxis, NULL);

    AcGePlane   mleaderPlane;
    mleaderPlane.set (RealDwgUtil::GePoint3dFromDPoint3d(point0), RealDwgUtil::GeVector3dFromDPoint3d(xAxis), RealDwgUtil::GeVector3dFromDPoint3d(yAxis));

    mleader->setPlane (mleaderPlane);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsNearBottomCenter (DPoint3dCR hookPoint)
    {
    DPoint3d    planeHookpoint;
    m_noteTextMatrix.MultiplyTranspose (planeHookpoint, hookPoint);

    return  planeHookpoint.IsEqual(m_textFrameBottomCenter, m_attachmentTolerance);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsNearTopCenter (DPoint3dCR hookPoint)
    {
    DPoint3d    planeHookpoint;
    m_noteTextMatrix.MultiplyTranspose (planeHookpoint, hookPoint);

    return  planeHookpoint.IsEqual(m_textFrameTopCenter, m_attachmentTolerance);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcGeVector3d    GetHooklineDirection (DPoint3dCR hookPoint)
    {
    DVec3d      xAxis;
    m_noteTextMatrix.GetColumn (xAxis, 0);
    xAxis.Normalize ();

    bool        isBottomAttached = this->IsNearBottomCenter (hookPoint);
    bool        isTopAttached = this->IsNearTopCenter (hookPoint);

    if (!isBottomAttached && !isTopAttached)
        {
        /*--------------------------------------------------------------------------------------------------------
        This leader line is attached horizontally on left or right side of the text.  Find which side is attached.
        To do that, compare origin and hook point on the text plane, negate the x-direction if hook point on the 
        right hand side.
        --------------------------------------------------------------------------------------------------------*/
        DPoint3d    planeTextorigin, planeHookpoint;
        m_noteTextMatrix.MultiplyTranspose (planeTextorigin, m_noteTextOrigin);
        m_noteTextMatrix.MultiplyTranspose (planeHookpoint, hookPoint);
        if (planeHookpoint.x - planeTextorigin.x > TOLERANCE_UORPointEqual)
            xAxis.Negate ();
        }
    else
        {
        /*--------------------------------------------------------------------------------------------------------
        This leader line is attached vertically on top or bottom of the text.  The top attachment seems to use
        reversed direction of the x-direction.
        --------------------------------------------------------------------------------------------------------*/
        if (isTopAttached)
            xAxis.Negate ();
        }

    return  RealDwgUtil::GeVector3dFromDVec3d(xAxis);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus       AddLeaderLine (AcDbMLeader* mleader, int& leaderNo, int& clusterNo, DPoint3dCR firstPoint, DPoint3dCR lastPoint)
    {
    /*-------------------------------------------------------------------------------------------------------------
    This method adds a new leader into either a new or existing cluster, and return the added leader and/or cluster 
    indices to the caller.  The cluster index is both input and output, with a value of -1 to denote a new cluster
    to be needed and a new cluster index will be created by RealDWG, followed by adding a new leader line to it.
    A value >0 is expected to be a valid existing cluster index, and a new leader will be added to it.
    --------------------------------------------------------------------------------------------------------------*/
    bool                isNewCluster = clusterNo < 0;

    // add a new cluster as needed
    Acad::ErrorStatus   es = isNewCluster ? mleader->addLeader(clusterNo) : Acad::eOk;

    if (Acad::eOk == es)
        {
        // add a new leader line into existing cluster and get the leader index back
        es = mleader->addLeaderLine (clusterNo, leaderNo);

        // set the first point for the new leader line with the newly added leader index
        DPoint3d        point;
        m_fromDgnContext->GetTransformFromDGN().Multiply (point, firstPoint);
        es = mleader->addFirstVertex (leaderNo, RealDwgUtil::GePoint3dFromDPoint3d(point));

        if (isNewCluster)
            {
            // add the hook point for the whole cluster of leaders
            m_fromDgnContext->GetTransformFromDGN().Multiply (point, lastPoint);
            es = mleader->addLastVertex (leaderNo, RealDwgUtil::GePoint3dFromDPoint3d(point));

            // set the hook line direction for the new cluster
            es = mleader->setDoglegDirection (clusterNo, this->GetHooklineDirection(lastPoint));
            }
        }

    return  es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
int             GetLeaderClusterIndex (DPoint3dCR inPoint)
    {
    // return the existing cluster index if the input point is found
    for each (LeaderCluster const& cluster in m_leaderCluster)
        {
        // we are only interested in the nearness of the two points - TOLERANCE_LoopClosure is a better fit for our purpose
        if (cluster.GetHookPoint().IsEqual(inPoint, TOLERANCE_LoopClosure))
            return  cluster.GetClusterIndex();
        }

    // return -1 to let RealDWG add a new cluster as we don't seem allowed to add a new cluster explicitly
    return  -1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            UpdateLeaderClusterIndex (int inClusterNo, DPoint3dCR inPoint)
    {
    // don't bother with an invald index
    if (inClusterNo < 0)
        return;

    // check the input cluster index our local cluster array
    for each (LeaderCluster const& cluster in m_leaderCluster)
        {
        // don't add a new cluster if we already have one in the collection
        if (cluster.GetClusterIndex() == inClusterNo)
            return;
        }

    // the cluster index is new, add it to our cluster array
    LeaderCluster    newCluster(inClusterNo, inPoint);
    m_leaderCluster.push_back (newCluster);
    }

};  // ConvertMultileaderFromDgnNote


