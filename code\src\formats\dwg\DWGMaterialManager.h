#pragma once

#include "../../core/MaterialManager.h"
#include "../../../include/ExportTypes.h"

// RealDWG SDK includes
#ifdef REALDWG_AVAILABLE
#include <realdwg/base/adesk.h>
#include <realdwg/base/dbmain.h>
#include <realdwg/base/dbmaterial.h>
#include <realdwg/base/dbdict.h>
#include <realdwg/base/acgi.h>
#include <realdwg/base/acgimaterial.h>
#include <realdwg/base/acgiutil.h>
#include <realdwg/base/acgitexture.h>
#include <realdwg/base/acgimapper.h>
#include <realdwg/base/acgisubentitytraits.h>
#endif

#include <memory>
#include <unordered_map>
#include <vector>

namespace IModelExport {

//=======================================================================================
// DWG Material Manager - Specialized material management for DWG format
//=======================================================================================

class DWGMaterialManager : public MaterialManager {
public:
    DWGMaterialManager();
    virtual ~DWGMaterialManager();

    //===================================================================================
    // DWG-Specific Material Management
    //===================================================================================

    // Material registration and conversion
    bool RegisterDWGMaterial(const Material& material, std::string& dwgMaterialId);
    bool ConvertToDWGMaterial(const Material& material, std::shared_ptr<void>& dwgMaterial);
    bool ConvertFromDWGMaterial(const std::shared_ptr<void>& dwgMaterial, Material& material);
    
    // Material application
    bool ApplyMaterialToEntity(const std::shared_ptr<void>& entity, const std::string& materialId);
    bool RemoveMaterialFromEntity(const std::shared_ptr<void>& entity);
    bool GetEntityMaterial(const std::shared_ptr<void>& entity, std::string& materialId);
    
    // Material library management
    bool LoadMaterialLibrary(const std::string& libraryPath);
    bool SaveMaterialLibrary(const std::string& libraryPath);
    bool ImportMaterialsFromDWG(const std::string& dwgPath);
    bool ExportMaterialsToDWG(const std::string& dwgPath);

    //===================================================================================
    // Advanced Material Features
    //===================================================================================

    // Texture management
    bool RegisterTexture(const std::string& texturePath, std::string& textureId);
    bool ApplyDiffuseTexture(const std::string& materialId, const std::string& textureId);
    bool ApplyNormalTexture(const std::string& materialId, const std::string& textureId);
    bool ApplySpecularTexture(const std::string& materialId, const std::string& textureId);
    bool ApplyBumpTexture(const std::string& materialId, const std::string& textureId);
    bool ApplyOpacityTexture(const std::string& materialId, const std::string& textureId);
    
    // Texture mapping
    struct TextureMapping {
        enum class MappingType {
            Planar, Cylindrical, Spherical, Box, UV
        } type = MappingType::Planar;
        
        Transform3d transform;
        Vector3d uVector;
        Vector3d vVector;
        double uScale = 1.0;
        double vScale = 1.0;
        double uOffset = 0.0;
        double vOffset = 0.0;
        double rotation = 0.0;
        bool tileU = true;
        bool tileV = true;
    };
    
    bool SetTextureMapping(const std::string& materialId, const TextureMapping& mapping);
    bool GetTextureMapping(const std::string& materialId, TextureMapping& mapping);
    
    // Material properties
    struct DWGMaterialProperties {
        // Basic properties
        Color ambient;
        Color diffuse;
        Color specular;
        Color emission;
        double shininess = 0.0;
        double transparency = 0.0;
        double reflectivity = 0.0;
        double refraction = 1.0;
        
        // Advanced properties
        double roughness = 0.5;
        double metallic = 0.0;
        double subsurface = 0.0;
        double subsurfaceRadius = 1.0;
        Color subsurfaceColor;
        
        // Texture channels
        std::string diffuseTexture;
        std::string normalTexture;
        std::string specularTexture;
        std::string roughnessTexture;
        std::string metallicTexture;
        std::string opacityTexture;
        std::string bumpTexture;
        std::string displacementTexture;
        
        // Rendering properties
        bool castShadows = true;
        bool receiveShadows = true;
        bool selfIllumination = false;
        double selfIlluminationLuminance = 0.0;
        
        // Physical properties
        double density = 1.0;
        double thermalConductivity = 1.0;
        double specificHeat = 1.0;
        std::string materialType;
        std::string manufacturer;
        std::string description;
    };
    
    bool SetMaterialProperties(const std::string& materialId, const DWGMaterialProperties& properties);
    bool GetMaterialProperties(const std::string& materialId, DWGMaterialProperties& properties);

    //===================================================================================
    // Material Categories and Organization
    //===================================================================================

    // Material categories
    enum class MaterialCategory {
        Generic,
        Metal,
        Wood,
        Concrete,
        Glass,
        Plastic,
        Fabric,
        Stone,
        Ceramic,
        Paint,
        Liquid,
        Gas,
        Custom
    };
    
    bool SetMaterialCategory(const std::string& materialId, MaterialCategory category);
    MaterialCategory GetMaterialCategory(const std::string& materialId);
    std::vector<std::string> GetMaterialsByCategory(MaterialCategory category);
    
    // Material tags and metadata
    bool AddMaterialTag(const std::string& materialId, const std::string& tag);
    bool RemoveMaterialTag(const std::string& materialId, const std::string& tag);
    std::vector<std::string> GetMaterialTags(const std::string& materialId);
    std::vector<std::string> FindMaterialsByTag(const std::string& tag);
    
    bool SetMaterialMetadata(const std::string& materialId, const std::string& key, const std::string& value);
    bool GetMaterialMetadata(const std::string& materialId, const std::string& key, std::string& value);
    std::unordered_map<std::string, std::string> GetAllMaterialMetadata(const std::string& materialId);

    //===================================================================================
    // Material Validation and Quality Control
    //===================================================================================

    // Material validation
    bool ValidateMaterial(const Material& material, std::vector<std::string>& issues);
    bool ValidateDWGMaterial(const std::shared_ptr<void>& dwgMaterial, std::vector<std::string>& issues);
    bool ValidateTextureFile(const std::string& texturePath, std::vector<std::string>& issues);
    
    // Material optimization
    bool OptimizeMaterial(Material& material);
    bool OptimizeTextures(const std::string& materialId);
    bool CompressTextures(const std::string& materialId, double quality = 0.8);
    
    // Material analysis
    struct MaterialAnalysis {
        bool hasTextures = false;
        bool hasTransparency = false;
        bool hasReflection = false;
        bool hasRefraction = false;
        bool hasBumpMapping = false;
        bool hasNormalMapping = false;
        size_t textureCount = 0;
        size_t totalTextureSize = 0;
        double complexity = 0.0;
        std::vector<std::string> issues;
        std::vector<std::string> recommendations;
    };
    
    MaterialAnalysis AnalyzeMaterial(const std::string& materialId);

    //===================================================================================
    // Performance and Memory Management
    //===================================================================================

    // Texture streaming and LOD
    bool EnableTextureStreaming(bool enable);
    bool SetTextureLOD(const std::string& textureId, int lodLevel);
    bool GenerateTextureMipmaps(const std::string& textureId);
    
    // Memory management
    bool OptimizeMemoryUsage();
    bool ClearUnusedMaterials();
    bool ClearUnusedTextures();
    size_t GetMaterialMemoryUsage() const;
    size_t GetTextureMemoryUsage() const;
    
    // Batch operations
    bool ProcessMaterialBatch(const std::vector<Material>& materials);
    bool ApplyMaterialBatch(const std::vector<std::shared_ptr<void>>& entities, 
                           const std::vector<std::string>& materialIds);

    //===================================================================================
    // Material Conversion and Compatibility
    //===================================================================================

    // Format conversion
    bool ConvertMaterialFromIFC(const Material& ifcMaterial, Material& dwgMaterial);
    bool ConvertMaterialFromUSD(const Material& usdMaterial, Material& dwgMaterial);
    bool ConvertMaterialFromDGN(const Material& dgnMaterial, Material& dwgMaterial);
    
    // Compatibility checking
    bool CheckMaterialCompatibility(const Material& material, std::vector<std::string>& issues);
    bool UpgradeMaterialVersion(Material& material, const std::string& targetVersion);
    
    // Material mapping
    struct MaterialMapping {
        std::string sourceMaterialId;
        std::string targetMaterialId;
        double similarity = 0.0;
        std::vector<std::string> differences;
    };
    
    std::vector<MaterialMapping> FindSimilarMaterials(const Material& material, double threshold = 0.8);
    bool CreateMaterialMapping(const std::string& sourceMaterialId, const std::string& targetMaterialId);

    //===================================================================================
    // Statistics and Reporting
    //===================================================================================

    struct MaterialStatistics {
        size_t totalMaterials = 0;
        size_t totalTextures = 0;
        size_t materialsWithTextures = 0;
        size_t materialsWithTransparency = 0;
        size_t materialsWithReflection = 0;
        std::unordered_map<MaterialCategory, size_t> categoryCounts;
        std::unordered_map<std::string, size_t> tagCounts;
        size_t totalMemoryUsage = 0;
        size_t textureMemoryUsage = 0;
        double averageComplexity = 0.0;
    };
    
    MaterialStatistics GetMaterialStatistics() const;
    std::string GenerateMaterialReport() const;
    bool ExportMaterialReport(const std::string& filePath) const;

private:
    //===================================================================================
    // Internal State and Configuration
    //===================================================================================

    // Material storage
    std::unordered_map<std::string, Material> m_materials;
    std::unordered_map<std::string, DWGMaterialProperties> m_dwgProperties;
    std::unordered_map<std::string, std::shared_ptr<void>> m_dwgMaterials;
    
    // Texture storage
    std::unordered_map<std::string, std::string> m_textures; // textureId -> filePath
    std::unordered_map<std::string, TextureMapping> m_textureMappings;
    std::unordered_map<std::string, size_t> m_textureMemoryUsage;
    
    // Organization
    std::unordered_map<std::string, MaterialCategory> m_materialCategories;
    std::unordered_map<std::string, std::vector<std::string>> m_materialTags;
    std::unordered_map<std::string, std::unordered_map<std::string, std::string>> m_materialMetadata;
    
    // Performance settings
    bool m_textureStreaming = false;
    std::unordered_map<std::string, int> m_textureLODs;
    
    // Statistics
    mutable MaterialStatistics m_statistics;
    
#ifdef REALDWG_AVAILABLE
    // RealDWG specific state
    AcDbDatabase* m_database = nullptr;
    AcDbDictionary* m_materialDict = nullptr;
    
    // Material creation helpers
    AcDbMaterial* CreateDWGMaterial(const Material& material);
    bool SetDWGMaterialProperties(AcDbMaterial* dwgMaterial, const DWGMaterialProperties& properties);
    bool GetDWGMaterialProperties(const AcDbMaterial* dwgMaterial, DWGMaterialProperties& properties);
    
    // Texture helpers
    bool CreateDWGTexture(const std::string& texturePath, AcGiMaterialTexture& texture);
    bool SetTextureMapping(AcGiMaterialTexture& texture, const TextureMapping& mapping);
    
    // Conversion helpers
    AcCmColor ToAcCmColor(const Color& color);
    Color FromAcCmColor(const AcCmColor& color);
    AcGiMaterialColor ToAcGiMaterialColor(const Color& color);
    Color FromAcGiMaterialColor(const AcGiMaterialColor& color);
#endif

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    // Material ID generation
    std::string GenerateMaterialId(const Material& material);
    std::string GenerateTextureId(const std::string& texturePath);
    
    // Material validation helpers
    bool ValidateColor(const Color& color);
    bool ValidateTextureFile(const std::string& filePath);
    bool ValidateMaterialName(const std::string& name);
    
    // Material processing helpers
    bool ProcessMaterialTextures(const std::string& materialId);
    bool OptimizeMaterialProperties(DWGMaterialProperties& properties);
    bool NormalizeMaterialValues(DWGMaterialProperties& properties);
    
    // Error handling and logging
    void LogMaterialError(const std::string& message);
    void LogMaterialWarning(const std::string& message);
    
    // Utility methods
    std::string GetMaterialCategoryName(MaterialCategory category);
    MaterialCategory ParseMaterialCategory(const std::string& categoryName);
    bool IsMaterialInUse(const std::string& materialId);
    bool IsTextureInUse(const std::string& textureId);
    
    // Cleanup
    void CleanupMaterials();
    void CleanupTextures();
    void UpdateStatistics() const;
};

//=======================================================================================
// DWG Material Utilities
//=======================================================================================

namespace DWGMaterialUtils {
    
    // Material naming conventions
    std::string GenerateStandardMaterialName(const Material& material);
    std::string SanitizeMaterialName(const std::string& name);
    
    // Color conversion utilities
    Color ConvertColorSpace(const Color& color, const std::string& sourceSpace, const std::string& targetSpace);
    Color AdjustColorGamma(const Color& color, double gamma);
    Color BlendColors(const Color& color1, const Color& color2, double factor);
    
    // Texture utilities
    std::string GetTextureFormat(const std::string& filePath);
    bool IsTextureFormatSupported(const std::string& format);
    std::string ConvertTextureFormat(const std::string& sourcePath, const std::string& targetFormat);
    
    // Material comparison
    double CalculateMaterialSimilarity(const Material& material1, const Material& material2);
    bool AreMaterialsEquivalent(const Material& material1, const Material& material2, double tolerance = 0.01);
    
    // Material optimization
    Material OptimizeForDWG(const Material& material);
    bool ReduceMaterialComplexity(Material& material, double targetComplexity);
    
    // Validation helpers
    bool IsValidMaterialName(const std::string& name);
    bool IsValidColorValue(double value);
    bool IsValidTextureCoordinate(double u, double v);
    
    // Performance helpers
    size_t EstimateMaterialMemoryUsage(const Material& material);
    double EstimateMaterialComplexity(const Material& material);
    size_t EstimateTextureMemoryUsage(const std::string& texturePath);
}

} // namespace IModelExport
