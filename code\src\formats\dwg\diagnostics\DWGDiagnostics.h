#pragma once

#include "../../../include/ExportTypes.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <chrono>
#include <functional>

namespace IModelExport {

//=======================================================================================
// Diagnostic Data Structures
//=======================================================================================

enum class DiagnosticLevel {
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
};

enum class DiagnosticCategory {
    General,
    Geometry,
    Entity,
    Style,
    Material,
    Performance,
    Memory,
    Validation,
    Conversion,
    IO
};

struct DiagnosticMessage {
    DiagnosticLevel level = DiagnosticLevel::Info;
    DiagnosticCategory category = DiagnosticCategory::General;
    std::string message;
    std::string details;
    std::string source;          // Source component/processor
    std::string elementId;       // Associated element ID
    std::chrono::system_clock::time_point timestamp;
    
    // Location information
    std::string fileName;
    int lineNumber = 0;
    std::string functionName;
    
    // Additional context
    std::unordered_map<std::string, std::string> context;
    
    DiagnosticMessage();
    DiagnosticMessage(DiagnosticLevel level, DiagnosticCategory category, 
                     const std::string& message, const std::string& source = "");
    
    std::string ToString() const;
    std::string GetLevelString() const;
    std::string GetCategoryString() const;
    bool IsError() const { return level >= DiagnosticLevel::Error; }
    bool IsWarning() const { return level >= DiagnosticLevel::Warning; }
};

struct PerformanceMetrics {
    std::chrono::milliseconds totalProcessingTime{0};
    std::chrono::milliseconds geometryProcessingTime{0};
    std::chrono::milliseconds validationTime{0};
    std::chrono::milliseconds conversionTime{0};
    std::chrono::milliseconds ioTime{0};
    
    size_t elementsProcessed = 0;
    size_t elementsSuccessful = 0;
    size_t elementsSkipped = 0;
    size_t elementsFailed = 0;
    
    size_t entitiesCreated = 0;
    size_t geometryValidations = 0;
    size_t geometryRepairs = 0;
    size_t styleConversions = 0;
    
    size_t memoryPeakUsage = 0;    // Peak memory usage in bytes
    size_t memoryCurrentUsage = 0; // Current memory usage in bytes
    
    double averageProcessingTimePerElement() const;
    double successRate() const;
    double errorRate() const;
    std::string ToString() const;
};

struct DiagnosticStatistics {
    size_t totalMessages = 0;
    size_t infoCount = 0;
    size_t warningCount = 0;
    size_t errorCount = 0;
    size_t criticalCount = 0;
    
    std::unordered_map<DiagnosticCategory, size_t> categoryCount;
    std::unordered_map<std::string, size_t> sourceCount;
    
    void Update(const DiagnosticMessage& message);
    void Reset();
    std::string ToString() const;
};

//=======================================================================================
// DWG Diagnostics Manager
//=======================================================================================

class DWGDiagnostics {
public:
    DWGDiagnostics();
    ~DWGDiagnostics();

    //===================================================================================
    // Configuration
    //===================================================================================

    void SetLogLevel(DiagnosticLevel level) { m_logLevel = level; }
    DiagnosticLevel GetLogLevel() const { return m_logLevel; }
    
    void SetMaxMessages(size_t maxMessages) { m_maxMessages = maxMessages; }
    size_t GetMaxMessages() const { return m_maxMessages; }
    
    void EnableCategory(DiagnosticCategory category, bool enable = true);
    bool IsCategoryEnabled(DiagnosticCategory category) const;
    
    void SetOutputFile(const std::string& filename);
    void SetConsoleOutput(bool enable) { m_consoleOutput = enable; }
    
    void EnablePerformanceMonitoring(bool enable = true) { m_performanceMonitoring = enable; }
    bool IsPerformanceMonitoringEnabled() const { return m_performanceMonitoring; }

    //===================================================================================
    // Message Logging
    //===================================================================================

    void LogInfo(const std::string& message, const std::string& source = "", 
                 DiagnosticCategory category = DiagnosticCategory::General);
    void LogWarning(const std::string& message, const std::string& source = "", 
                    DiagnosticCategory category = DiagnosticCategory::General);
    void LogError(const std::string& message, const std::string& source = "", 
                  DiagnosticCategory category = DiagnosticCategory::General);
    void LogCritical(const std::string& message, const std::string& source = "", 
                     DiagnosticCategory category = DiagnosticCategory::General);
    
    void LogMessage(const DiagnosticMessage& message);
    void LogMessage(DiagnosticLevel level, DiagnosticCategory category,
                   const std::string& message, const std::string& source = "",
                   const std::string& details = "");
    
    // Contextual logging
    void LogGeometryError(const std::string& message, const std::string& elementId = "",
                         const std::string& source = "");
    void LogValidationError(const std::string& message, const std::string& elementId = "",
                           const std::string& source = "");
    void LogConversionError(const std::string& message, const std::string& elementId = "",
                           const std::string& source = "");
    void LogPerformanceWarning(const std::string& message, const std::string& source = "");

    //===================================================================================
    // Message Retrieval and Analysis
    //===================================================================================

    std::vector<DiagnosticMessage> GetMessages() const;
    std::vector<DiagnosticMessage> GetMessages(DiagnosticLevel level) const;
    std::vector<DiagnosticMessage> GetMessages(DiagnosticCategory category) const;
    std::vector<DiagnosticMessage> GetMessages(const std::string& source) const;
    std::vector<DiagnosticMessage> GetMessagesForElement(const std::string& elementId) const;
    
    DiagnosticStatistics GetStatistics() const;
    PerformanceMetrics GetPerformanceMetrics() const;
    
    bool HasErrors() const;
    bool HasWarnings() const;
    bool HasCriticalErrors() const;
    
    size_t GetMessageCount() const { return m_messages.size(); }
    size_t GetErrorCount() const;
    size_t GetWarningCount() const;

    //===================================================================================
    // Performance Monitoring
    //===================================================================================

    // Timing operations
    class Timer {
    public:
        Timer(DWGDiagnostics* diagnostics, const std::string& operation);
        ~Timer();
        
        void Stop();
        std::chrono::milliseconds GetElapsed() const;
        
    private:
        DWGDiagnostics* m_diagnostics;
        std::string m_operation;
        std::chrono::high_resolution_clock::time_point m_startTime;
        bool m_stopped = false;
    };
    
    std::unique_ptr<Timer> StartTimer(const std::string& operation);
    void RecordTiming(const std::string& operation, std::chrono::milliseconds duration);
    
    // Memory monitoring
    void RecordMemoryUsage();
    void RecordPeakMemoryUsage(size_t usage);
    size_t GetCurrentMemoryUsage() const;
    size_t GetPeakMemoryUsage() const;
    
    // Element processing tracking
    void RecordElementProcessed(bool successful = true);
    void RecordElementSkipped();
    void RecordElementFailed();
    void RecordEntityCreated();
    void RecordGeometryValidation();
    void RecordGeometryRepair();
    void RecordStyleConversion();

    //===================================================================================
    // Reporting and Export
    //===================================================================================

    // Report generation
    std::string GenerateReport() const;
    std::string GenerateSummaryReport() const;
    std::string GenerateDetailedReport() const;
    std::string GeneratePerformanceReport() const;
    
    // Export functions
    bool ExportToFile(const std::string& filename) const;
    bool ExportToHTML(const std::string& filename) const;
    bool ExportToJSON(const std::string& filename) const;
    bool ExportToCSV(const std::string& filename) const;
    
    // Real-time monitoring
    void SetRealtimeCallback(std::function<void(const DiagnosticMessage&)> callback);
    void SetPerformanceCallback(std::function<void(const PerformanceMetrics&)> callback);

    //===================================================================================
    // Error Recovery and Suggestions
    //===================================================================================

    // Error analysis
    std::vector<std::string> AnalyzeErrors() const;
    std::vector<std::string> GetRecoverySuggestions() const;
    std::vector<std::string> GetOptimizationSuggestions() const;
    
    // Pattern detection
    std::vector<std::string> DetectErrorPatterns() const;
    std::vector<std::string> DetectPerformanceIssues() const;
    
    // Automated fixes
    bool CanAutoFix(const DiagnosticMessage& message) const;
    std::vector<std::string> GetAutoFixSuggestions(const DiagnosticMessage& message) const;

    //===================================================================================
    // Cleanup and Management
    //===================================================================================

    void Clear();
    void ClearMessages();
    void ClearPerformanceMetrics();
    void ClearStatistics();
    
    void TrimMessages();  // Remove old messages if over limit
    void ArchiveMessages(const std::string& filename);
    
    // Session management
    void StartSession(const std::string& sessionName = "");
    void EndSession();
    std::string GetCurrentSession() const { return m_currentSession; }

private:
    //===================================================================================
    // Internal State
    //===================================================================================

    std::vector<DiagnosticMessage> m_messages;
    DiagnosticStatistics m_statistics;
    PerformanceMetrics m_performanceMetrics;
    
    // Configuration
    DiagnosticLevel m_logLevel = DiagnosticLevel::Info;
    size_t m_maxMessages = 10000;
    std::unordered_map<DiagnosticCategory, bool> m_enabledCategories;
    
    // Output configuration
    std::string m_outputFile;
    bool m_consoleOutput = true;
    bool m_performanceMonitoring = true;
    
    // Session tracking
    std::string m_currentSession;
    std::chrono::system_clock::time_point m_sessionStartTime;
    
    // Callbacks
    std::function<void(const DiagnosticMessage&)> m_realtimeCallback;
    std::function<void(const PerformanceMetrics&)> m_performanceCallback;
    
    // Timing tracking
    std::unordered_map<std::string, std::chrono::milliseconds> m_operationTimings;
    std::unordered_map<std::string, size_t> m_operationCounts;

    //===================================================================================
    // Internal Helper Methods
    //===================================================================================

    void WriteToOutput(const DiagnosticMessage& message);
    void WriteToConsole(const DiagnosticMessage& message);
    void WriteToFile(const DiagnosticMessage& message);
    
    void UpdateStatistics(const DiagnosticMessage& message);
    void TriggerCallbacks(const DiagnosticMessage& message);
    
    std::string FormatMessage(const DiagnosticMessage& message) const;
    std::string FormatTimestamp(const std::chrono::system_clock::time_point& timestamp) const;
    
    // Error pattern analysis
    std::vector<std::string> AnalyzeGeometryErrors() const;
    std::vector<std::string> AnalyzeValidationErrors() const;
    std::vector<std::string> AnalyzeConversionErrors() const;
    std::vector<std::string> AnalyzePerformanceIssues() const;
    
    // Memory monitoring helpers
    size_t GetSystemMemoryUsage() const;
    void UpdateMemoryMetrics();
    
    // File I/O helpers
    bool WriteJSONReport(const std::string& filename) const;
    bool WriteHTMLReport(const std::string& filename) const;
    bool WriteCSVReport(const std::string& filename) const;
    
    // Thread safety (if needed)
    mutable std::mutex m_mutex;
    void Lock() const { m_mutex.lock(); }
    void Unlock() const { m_mutex.unlock(); }
};

//=======================================================================================
// Diagnostic Macros for Convenient Logging
//=======================================================================================

#define DWG_LOG_INFO(diagnostics, message, source) \
    if (diagnostics) diagnostics->LogInfo(message, source, DiagnosticCategory::General)

#define DWG_LOG_WARNING(diagnostics, message, source) \
    if (diagnostics) diagnostics->LogWarning(message, source, DiagnosticCategory::General)

#define DWG_LOG_ERROR(diagnostics, message, source) \
    if (diagnostics) diagnostics->LogError(message, source, DiagnosticCategory::General)

#define DWG_LOG_CRITICAL(diagnostics, message, source) \
    if (diagnostics) diagnostics->LogCritical(message, source, DiagnosticCategory::General)

#define DWG_LOG_GEOMETRY_ERROR(diagnostics, message, elementId, source) \
    if (diagnostics) diagnostics->LogGeometryError(message, elementId, source)

#define DWG_LOG_VALIDATION_ERROR(diagnostics, message, elementId, source) \
    if (diagnostics) diagnostics->LogValidationError(message, elementId, source)

#define DWG_LOG_CONVERSION_ERROR(diagnostics, message, elementId, source) \
    if (diagnostics) diagnostics->LogConversionError(message, elementId, source)

#define DWG_TIMER(diagnostics, operation) \
    auto timer = diagnostics ? diagnostics->StartTimer(operation) : nullptr

//=======================================================================================
// Global Diagnostics Instance
//=======================================================================================

class DWGDiagnosticsManager {
public:
    static DWGDiagnostics& GetInstance();
    static void SetInstance(std::unique_ptr<DWGDiagnostics> diagnostics);
    static void ResetInstance();

private:
    static std::unique_ptr<DWGDiagnostics> s_instance;
    static std::mutex s_mutex;
};

} // namespace IModelExport
