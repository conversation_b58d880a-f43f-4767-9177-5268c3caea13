/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdLineStyleConvert.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

// =======================================================================================
// Opening DWG file:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static UInt32               GetDgnStyleAttributes
(
MSElementDescrP             definitionEdP
)
    {
    unsigned int attributes =  LSATTR_UNITUOR | LSATTR_NOSNAP | LSATTR_SHAREDCELL_SCALE_INDEPENDENT;
    // If it is a line code only, don't try to calculate the range
    if (nullptr != definitionEdP && TABLE_ENTRY_ELM == definitionEdP->el.ehdr.type && MS_LSTYLE_DEF_LEVEL == definitionEdP->el.ehdr.level &&
        LsElementType::LineCode == static_cast<LsElementType>(definitionEdP->el.lineStyleDefEntry.type))
        attributes |= LSATTR_NORANGE;

    return attributes;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsSymbolSegment
(
const AcDbLinetypeTableRecord*  acLinetype,
int                             segmentAt
)
    {
    // check if current segment is of a shape number or of a text.
    if (0 != acLinetype->shapeNumberAt(segmentAt))
        return  true;

    const ACHAR*    segmentName = NULL;
    if (Acad::eOk == acLinetype->textAt(segmentAt, segmentName) && 0 != segmentName[0])
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* Looks to see if we've already added this element
*
* @bsimethod                                                    ChuckKirschman      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
static ElementId            SymbolHasBeenMapped
(
AcDbLinetypeTableRecord*    acLinetype,
int                         index,
ConvertToDgnContextR        context
)
    {
    // Currently we're not creating a single element for repeated shapesymbols like
    // we do in the schema code - that should be done - will need to keep an array
    // of ones used etc.
    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* Convert line style stroke symbol to an element to add to line styles.
*
* @return an element descriptor chain
* @bsimethod                                                    ChuckKirschman      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
static MSElementDescrP      StrokeSymbolToElement
(
AcDbLinetypeTableRecord*    acLinetype,
int                         segmentIndex,
int                         symbolNumber,
DPoint3dCR                  shapeOffset,
ConvertToDgnContextR        context
)
    {
    MSElementDescrP         pDescr      = NULL;
    MSElementDescrP         pTextDescr  = NULL;
    DPoint3d                offset;
    RotMatrix               rMatrix;
    TextSizeParam           textSize;
    TextParamWide           textParams;
    double                  styleHeight = 0;

    /* Initialization stuff */
    memset (&textParams, 0, sizeof(textParams));
    rMatrix.initIdentity ();
    textSize.size.width = textSize.size.height = context.GetScaleToDGN();
    textSize.mode = TXT_BY_TILE_SIZE;
    offset.x = offset.y = offset.z = 0.0;
    textParams.just = TextElementJustification::LeftBaseline;

    AcDbObjectId                    styleObjectId;
    if ((styleObjectId = acLinetype->shapeStyleAt (segmentIndex)).isNull())
        return NULL;

    AcDbTextStyleTableRecordPointer pTextStyle (styleObjectId, AcDb::kForRead, false);
    if (Acad::eOk != pTextStyle.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Failed to open textstyle in linetype. [%ls]\n", acadErrorStatusText(pTextStyle.openStatus()));
        return  NULL;
        }

    context.GetFontIdsFromTextStyle ((int *) &textParams.font, (int *) &textParams.shxBigFont, pTextStyle);

    textParams.textStyleId = context.GetFileHolder().GetTextStyleIndex()->GetDgnId (styleObjectId.handle());
    if (0 != textParams.textStyleId)
        textParams.flags.textStyle = true;

    textParams.overridesFromStyle.just = true;
    textParams.exFlags.styleOverrides  = true;

    // Adjust for size of text style
    if (! pTextStyle->isShapeFile())
        {
        styleHeight = pTextStyle->textSize();
        if (styleHeight > 0.0)
            {
            textSize.size.height *= styleHeight;
            textSize.size.width   = textSize.size.height; // Width factor appears to be ignored in linetypes
            }
        }

    textParams.SetCodePage (context.GetAnsiCodePage ());

    double          scale = acLinetype->shapeScaleAt (segmentIndex);
    DPoint3d        origin = {0.0, 0.0, 0.0};
    origin.x += scale == 0.0 ? 0.0 : shapeOffset.x / scale;
    origin.y += scale == 0.0 ? 0.0 : shapeOffset.y / scale;

    EditElementHandle   editElemHandle;
    const ACHAR*        textString = nullptr;
    UInt8               shapeNumber = 0;
    
    if (Acad::eOk == acLinetype->textAt (segmentIndex, textString) && 0 != *textString)
        {
        TextElemHandler::CreateElement(editElemHandle, NULL, origin, rMatrix, DPoint2d::From (textSize.size.width, textSize.size.height), textParams, textString, context.GetModel ()->Is3d(), *context.GetModel ());
        }
    else if (0 != (shapeNumber = static_cast<UInt8>(0xFF & acLinetype->shapeNumberAt(segmentIndex))))
        {
        UInt8   shapeCodes[] = { shapeNumber, 0 };
        
        TextElemHandler::CreateElementFromShapeCodes(editElemHandle, NULL, origin, rMatrix, DPoint2d::From(textSize.size.width, textSize.size.height), textParams, shapeCodes, 1, context.GetModel()->Is3d(), *context.GetModel());
        if (!editElemHandle.IsValid())
            {
            DIAGNOSTIC_PRINTF ("Failed to create text symbol for linetype ID=%I64d\n", RealDwgUtil::CastDBHandle(acLinetype->objectId().handle()));
            return  nullptr;
            }

        DPoint2d extents = { editElemHandle.GetElementCP()->text_2d.length, editElemHandle.GetElementCP()->text_2d.height };
        
        TextString::ComputeUserOriginOffset(offset, extents, 0.0, textParams.just, false, TEXT_ELM, NULL);
        
        DPoint3d elementOrigin = origin;
        elementOrigin.Subtract(offset);

        TextElemHandler::SetElementOrigin(editElemHandle, elementOrigin, true);
        }
    else
        {
        // should never hit here.
        BeAssert (false && L"Failure extracting text symbol from a linetype!\n");
        }

    pTextDescr = editElemHandle.ExtractElementDescr ();

    WChar         nameChars[512];
    const ACHAR*    lineTypeName;
    acLinetype->getName (lineTypeName);
    swprintf (nameChars, L"symb%d-%ls", symbolNumber, lineTypeName);

    // Create element
    if (NULL != pTextDescr)
        {
        ElementId               ids[2];

        if (0.0 != scale)           // TR: 109004.
            scale = fabs(1.0/scale);

        ids[0] = context.GetAndIncrementNextId();
        ids[1] = context.GetAndIncrementNextId();

        LineStyleUtil::CreateSymbolEntry (&pDescr,                    // <= Returned line style element; this will be complex.
                                          nameChars,                  // => Name of style
                                          NULL,                       // => Description of style - 63 chars
                                          pTextDescr,                 // => Symbol element to add
                                          &offset,                    // => Offset of symbol
                                          scale,                      // => Scale of symbol
                                          ids,                        // => elementIDs
                                          context.GetModel()         // model ref
                                          );
        }

    return pDescr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetOrSetLinetypeTextUpwardFlag (AcDbLinetypeTableRecord* linetype, int iSegment, bool* setFlag=NULL)
    {
    bool                alwaysUpward = false;

#if RealDwgVersion >= 2012
    if (NULL == setFlag)
        alwaysUpward = linetype->shapeIsUprightAt (iSegment);
    else
        linetype->setShapeIsUprightAt (iSegment, *setFlag);
#else
    /*-----------------------------------------------------------------------------------
    Since R2012, AutoCAD R2012 has added a new flag to DXF group code 74(the 3rd bit) in
    DWG to indicate weather an embedded text string should always run upward/readable
    direction.  This new flag is not published in DXF document neither in RealDWG 2012 Doc,
    but the new methods do exist and seem to work!  So for RealDWG 2012 and later we can
    call the new methods.  For older releases, we try to de/encode it via dwgIn/OutFields
    filer.  This new flag apparently is only done in dwgOutFields filer, not in dxfOut filer.
    In dxfOut filer, it gets saved as a round-trip xrecord.  So we cannot use the perferred
    ExtrationFiler, instead we have to RecordingFiler which is dwgOut based, which of course
    is also version dependent.  Below is a recording dump of a linetype by RealDWG 2012:

     0: kDwgSoftPointerId, ff901c28 fd84ae02 AcDbLinetypeTable
     1: kDwgUInt32,    0 (0x0)
     2: kDwgHardOwnershipId, NULL
     3: kDwgString(ACHAR) GAS_LINE
     4: kDwgInt16,    0 (0x0)
     5: kDwgBool false
     6: kDwgHardPointerId, NULL
     7: kDwgString(ACHAR) Gas line ----GAS----GAS----GAS----GAS----GAS---
     8: kDwgReal 0.950000
     9: kDwgUInt8,   65 (0x41)

    10: kDwgUInt8,    3 (0x3)

    // repeat 3 times of segments and each has 7 fields.  The 7th is DXG group code 74:
    11: kDwgReal 0.500000
    12: kDwgHardPointerId, NULL
    13: kDwgInt16,    0 (0x0)
    14: AcGeVector2d, 0.000000, 0.000000, 0.000000
    15: kDwgReal 1.000000
    16: kDwgReal 0.000000
    17: kDwgInt16,    0 (0x0)

    18: kDwgReal -0.200000
    19: kDwgHardPointerId, ff901c88 fd84aea2 AcDbTextStyleTableRecord
    20: kDwgInt16,    0 (0x0)
    21: AcGeVector2d, -0.100000, -0.050000, 0.000000
    22: kDwgReal 0.100000
    23: kDwgReal 0.000000
    24: kDwgInt16,   10 (0xa)

    25: kDwgReal -0.250000
    26: kDwgHardPointerId, NULL
    27: kDwgInt16,    0 (0x0)
    28: AcGeVector2d, 0.000000, 0.000000, 0.000000
    29: kDwgReal 1.000000
    30: kDwgReal 0.000000
    31: kDwgInt16,    0 (0x0)

    32: Byte, 512 bytes
    -----------------------------------------------------------------------------------*/

    RecordingFiler      filer (100);
    filer.RecordData (linetype);

    FilerDataList&      dataList = filer.GetDataList ();
    int                 index = 17 + 7 * iSegment;

    Int16FilerData*     dxfGroupCode74 = dynamic_cast <Int16FilerData*> (dataList[index]);
    if (NULL != dxfGroupCode74)
        {
        // get the 3rd bit value from DXF group code 74:
        Int16           intValue = dxfGroupCode74->GetValue ();
        alwaysUpward = 0 != (intValue & (0x0001 << 3));

        // if we are setting the linetype, update the value as necessary, then set the linetype:
        if (NULL != setFlag && *setFlag != alwaysUpward)
            {
            if (*setFlag)
                intValue |= (0x0001 << 3);
            else
                intValue &= ~(0x0001 << 3);
            dxfGroupCode74->SetValue (intValue);

            filer.PlaybackData (linetype);
            }
        }
#endif

    return  alwaysUpward;
    }

/*---------------------------------------------------------------------------------**//**
* Convert the segments into symbols and a point-line element.
*
* @return an element descriptor chain
* @bsimethod                                                    ChuckKirschman      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
static MSElementDescrP       PointLineToElement
(
AcDbLinetypeTableRecord*     acLinetype,
ElementId                    lineCodeID,
ConvertToDgnContextR         context
)
    {
    UInt32              nSegments           = acLinetype->numDashes();
    MSElementDescrP     pDescr              = NULL;
    MSElementDescrP     pSymbolDescr        = NULL;
    MSElementDescrP     pSymbolDefinitions  = NULL;
    UInt32*             symbolStrokeNumbers = (UInt32 *) _alloca (nSegments * sizeof (UInt32));
    DPoint3dP           symbolOffsets       = (DPoint3d *) _alloca (nSegments * sizeof(DPoint3d));
    DPoint3dP           symbolRotations     = (DPoint3d *)_alloca (nSegments * sizeof(DPoint3d));
    ElementId*          symbolIds           = (ElementId *)_alloca (nSegments * sizeof(ElementId));
    double              scaleToDgn          = context.GetScaleToDGN();
    int                 numSymbols          = 0;
    int                 symbolNumber        = 0;
    int                 status;
    bool                hasRotationAdjust = false;
    std::vector<UInt32> symbolModArray;

    for (UInt32 index = 0; index < nSegments; index++)
        {
        if (IsSymbolSegment (acLinetype, index))
            {
            ElementId       currentSymbolId;
            DPoint3d        shapeOffset = {0.0, 0.0, 0.0};
            bool            adjustRotation = GetOrSetLinetypeTextUpwardFlag(acLinetype, index);

            // For adjusted rotation, we need to move the origin on the text element so that when it is spun about that
            //   origin it will still be about right.
            if (adjustRotation)
                {
                shapeOffset.x = acLinetype->shapeOffsetAt (index).x * scaleToDgn;
                shapeOffset.y = acLinetype->shapeOffsetAt (index).y * scaleToDgn;
                }

            if (0 == (currentSymbolId = SymbolHasBeenMapped (acLinetype, index, context)) &&
                NULL != (pSymbolDescr = StrokeSymbolToElement (acLinetype, index, symbolNumber++, shapeOffset, context)))
                {
                if (NULL == pSymbolDefinitions)
                    pSymbolDefinitions = pSymbolDescr;
                else
                    pSymbolDefinitions->AddToChain (pSymbolDescr);

                currentSymbolId = pSymbolDescr->el.ehdr.uniqueId;
                }

            if (0 != currentSymbolId)
                {
                symbolIds[numSymbols] = currentSymbolId;
                symbolStrokeNumbers[numSymbols] = index;

                // For adjusted rotation, the offsets need to be in the text element.
                symbolOffsets[numSymbols].x = adjustRotation ? 0.0 : acLinetype->shapeOffsetAt (index).x * scaleToDgn;
                symbolOffsets[numSymbols].y = adjustRotation ? 0.0 : acLinetype->shapeOffsetAt (index).y * scaleToDgn;
                symbolOffsets[numSymbols].z = 0.0;

                symbolRotations[numSymbols].x = symbolRotations[numSymbols].y = 0.0;
                symbolRotations[numSymbols].z = (acLinetype->shapeRotationAt (index) +
                                    (acLinetype->shapeScaleAt (index) < 0.0 ? msGeomConst_pi : 0.0)) *
                                    msGeomConst_degreesPerRadian;

                // cook up symbol mods to apply LCPOINT_ADJROT for upward text orientation
                UInt32      symbolMod = LCPOINT_END | LCPOINT_NOSCALE;
                if (adjustRotation)
                    {
                    symbolMod |= LCPOINT_ADJROT;
                    hasRotationAdjust = true;
                    }
                symbolModArray.push_back (symbolMod);

                numSymbols++;
                }
            }
        } // End for

    if (numSymbols > 0)
        {
        WChar         nameChars[512];

        nameChars[0] = 'P';
        nameChars[1] = 'L';
        nameChars[2] = '-';

        const ACHAR *lineTypeName;
        acLinetype->getName (lineTypeName);
        RealDwgUtil::AcStringToMSWChar (nameChars+3, lineTypeName, 509);

        // Pass symbol mods with LCPOINT_ADJROT for upward text orientation.  If we pass NULL, then LCPOINT_END|LCPOINT_NOSCALE is used by default.
        UInt32*  symbolMods = NULL;
        if (hasRotationAdjust)
            symbolMods = &symbolModArray[0];

        status = LineStyleUtil::CreateLinePointEntry (&pDescr,                                        // <= Element
                                                    nameChars,                                      // Name
                                                    NULL,                                           // Description
                                                    lineCodeID,                                     // Unique ID of line code
                                                    numSymbols,                                     // Number of symbols
                                                    NULL,                                           // Types of symbols; NULL means all are Symbol elements
                                                    symbolIds,                                      // Symbol element IDs
                                                    symbolStrokeNumbers,                            // Stroke numbers associated with symbols
                                                    symbolMods,                                     // Stroke modifiers - see mslstyle.h
                                                    symbolOffsets,                                  // Offsets for each symbol
                                                    symbolRotations,                                // Rotations for each symbol
                                                    NULL,                                           // Scales for each symbol
                                                    context.GetAndIncrementNextId (),            // Element ID for this element
                                                    context.GetModel()                       // model ref
                                                    );

        // Accumulate all the elements together into a chain.
        // First elemen must be point-line element with correct Id.
        if (status == SUCCESS && NULL != pDescr)
            pDescr->AddToChain (pSymbolDefinitions);
        }

    return pDescr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveDwgLinetypesToDgn ()
    {
    AcDbObjectId                    tableId;
    if ( (tableId = m_pFileHolder->GetDatabase()->linetypeTableId()).isNull())
        return CantIterateLineTypes;

    AcDbLinetypeTablePointer        lineTypes (tableId, AcDb::kForRead);
    if (Acad::eOk != lineTypes.openStatus())
        return CantIterateLineTypes;

    AcDbLinetypeTableIterator*      iterator;
    if (Acad::eOk != lineTypes->newIterator (iterator))
        return CantIterateLineTypes;

    EditElementHandle       definitionTable;
    LineStyleUtil::CreateDefinitionTableElement (definitionTable, this->ElementIdFromObjectId (tableId), this->GetModel());

    EditElementHandle       nameTable;
    LineStyleUtil::CreateNameTableElement (nameTable, this->GetAndIncrementNextId(), this->GetModel());

    T_DgnLStylesInDwg       dgnlstylesInDwg;

    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId    lineTypeId;
        if (Acad::eOk != iterator->getRecordId (lineTypeId))
            continue;

        AcDbHandle                      dbHandle = lineTypeId.handle();
        AcDbLinetypeTableRecordPointer  acLinetype (lineTypeId, AcDb::kForRead, true);

        Acad::ErrorStatus   es = acLinetype.openStatus ();
        if (Acad::eOk != es)
            {
            // Handle it the ACAD's way: replace linetype with BYLAYER.  Also send out a message so user may take action after file opened.
            // If it is CONTINUOUS, add it anyway because lots of stuff depend on it.
            if (lineTypeId == m_pFileHolder->GetDatabase()->continuousLinetype())
                {
                WChar             name[64];
                wcscpy (name, StringConstants::ContinousLinetypeName);

                // create & add linestyle entry CONTINUOUS:
                MSElementDescrP     tableDescr      = definitionTable.ExtractElementDescr();
                MSElementDescrP     continuousDescr = NULL;
                LineStyleUtil::CreateLineCodeEntry (&continuousDescr, name, NULL, 0.0, LCOPT_NONE, 0, 0.0, 0.0, 0, NULL, NULL, NULL, NULL, NULL,
                                                    RealDwgUtil::CastDBHandle(dbHandle), this->GetModel());
                tableDescr->AppendDescr (continuousDescr);
                definitionTable.SetElementDescr (tableDescr, true, false);

                // create & add name entry CONTINUOUS:
                MSElementDescrP     nameDescr = NULL;
                mdlLineStyle_createNameTableEntry (&nameDescr, continuousDescr, NULL, m_pFileHolder->GetLinetypeIndex()->GetOrNextDgnId(dbHandle),
                                                   0.0, LSATTR_CONTINUOUS, this->GetModel());
                MSElementDescrP     nameTableDescr  = nameTable.ExtractElementDescr();
                nameTableDescr->AppendDescr (nameDescr);
                nameTable.SetElementDescr (nameTableDescr, true, false);

                DIAGNOSTIC_PRINTF ("Failed opening linetype CONTINUOUS: %ls.  Created one as replacement.\n", acadErrorStatusText(es));
                }
            else
                {
                m_pFileHolder->GetLinetypeIndex()->AddEntry (STYLE_BYLEVEL, dbHandle, false);

                WChar     handleValue[16];
                dbHandle.getIntoAsciiBuffer (handleValue);
                RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_ReplacedLinetype, false, acadErrorStatusText(es), handleValue);

                DIAGNOSTIC_PRINTF ("Failed opening linetype (handle=%ls): %ls.\n", handleValue, acadErrorStatusText(es));
                }
            continue;
            }

        ACHAR const*    pName;
        if (Acad::eOk != acLinetype->getName (pName))
            continue;

        Int32           dgnStyleId = 0;
        if (0 == _wcsicmp (pName, L"ByBlock"))
            {
            m_pFileHolder->GetLinetypeIndex()->AddEntry (STYLE_BYCELL, dbHandle, false);
            }
        else if (0 == _wcsicmp (pName, L"ByLayer"))
            {
            m_pFileHolder->GetLinetypeIndex()->AddEntry (STYLE_BYLEVEL, dbHandle, false);
            }
        else if (SUCCESS == this->SaveEmbeddedDgnLinestyleFromLinetype(dgnStyleId, definitionTable, nameTable, acLinetype, dgnlstylesInDwg))
            {
            m_pFileHolder->GetLinetypeIndex()->AddEntry (dgnStyleId, dbHandle, false);
            }
        else if (!definitionTable.IsValid() || !nameTable.IsValid())
            {
            // should not normally happen, but LineStyleUtilInternal::DoImportFromRsc can empty these and fail...
            // I fixed ImportFromRsc; I do not think there is any way you should get here.
            BeAssert (0 && "ERROR: linestyle def or name table has been trashed");
            DIAGNOSTIC_PRINTF ("ERROR: linestyle def or name table has been trashed...bailing out!!\n");
            return  LineStyleTableNotFound;
            }
        else
            {
            EditElementHandle       lineTypeElement;
            if (RealDwgSuccess == this->SaveDwgLinetypeToElement (lineTypeElement, acLinetype))
                {
                MSElementDescrCP    oldDescr        = lineTypeElement.GetElementDescrCP ();
                if (NULL == oldDescr)
                    continue;

                // a definition table may contain more than one element descriptors which prevent us from using ExtractElementDescr().
                MSElementDescrP     lineTypeDescr   = NULL;
                if (BSISUCCESS != oldDescr->Duplicate(&lineTypeDescr, true, false))
                    continue;

                if (DgnPlatform::STYLE_Invalid != dgnStyleId)
                    {
                    MSElementDescrP tableDescr      = definitionTable.ExtractElementDescr ();
                    tableDescr->AppendDescr (lineTypeDescr);
                    definitionTable.SetElementDescr (tableDescr, true, false);
                    }

                UInt32          attributes      = GetDgnStyleAttributes (lineTypeDescr);
                MSElementDescrP nameEntry       = NULL;
                mdlLineStyle_createNameTableEntry (&nameEntry, lineTypeDescr, NULL, m_pFileHolder->GetLinetypeIndex()->GetOrNextDgnId (dbHandle), 0.0, attributes, this->GetModel());

                MSElementDescrP nameTableDescr  = nameTable.ExtractElementDescr();
                nameTableDescr->AppendDescr (nameEntry);
                nameTable.SetElementDescr (nameTableDescr, true, false);

                if (DgnPlatform::STYLE_Invalid == dgnStyleId && NULL != lineTypeDescr)
                    lineTypeDescr->Release ();
                }
            }
        }
    delete iterator;

    this->LoadElementIntoCache (definitionTable);
    this->LoadElementIntoCache (nameTable);

    m_pFileHolder->GetLinetypeIndex()->SetOriginatedInDwgIds (definitionTable.GetElementDescrCP());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveDwgLinetypeToElement
(
EditElementHandleR          lineTypeElement,
AcDbLinetypeTableRecord*    lineType
)
    {
    UInt32          nSegments               = lineType->numDashes();
    double          scaleToDgn              = this->GetScaleToDGN();
    DoubleArray     strokeLengths;
    IntArray        strokeModes;
    IntArray        strokeWidthModes;
    bool            strokeSymbolsRequired   = false;

    for (UInt32 index = 0; index < nSegments; index++)
        {
        if (0 != lineType->shapeNumberAt (index))
            strokeSymbolsRequired = true;

        // This second test is looking for NULL shape files.  In this case, we ignore the symbol.
        // Acad prompts for a shape file in this case.
        const ACHAR* textString;
        if ( (Acad::eOk == lineType->textAt (index, textString)) && (0 != *textString) )
            strokeSymbolsRequired = true;

        // Build line code
        double  segmentLength = lineType->dashLengthAt (index);
        bool    validLength = true;
        if (fabs(segmentLength) == 0.0)
            {
            /*---------------------------------------------------------------------------
            We used to set 1 UOR for all 0-length dashes. That requirement became obsolete
            since 8.11.9 (although on 8.11.9 a config var still served as a backdoor).
            Using 0-UOR dashes had not been an issue until we had TFS 656098 where a valid 
            solid dash was immediately followed by a 0-length solid dash. Such a nonsense 
            dash has sent the draw code into a tailspin when it tried to draw a bulged 
            polyline.  To workaround this problem we still set the segment as a 1-UOR dash,
            but only under such an invalid case.

            Other 0-length dashes are still valid - they are drawn as dots in ACAD.
            ---------------------------------------------------------------------------*/
            if ((index > 0 && lineType->dashLengthAt(index - 1) > 0.0) || (index == 0 && nSegments > 1 && lineType->dashLengthAt(1) > 0.0))
                validLength = false;
            }

        strokeLengths.push_back (validLength ? fabs(segmentLength) * scaleToDgn : 1.0);
        strokeModes.push_back ( segmentLength >= 0.0 ? 1 : 0);
        strokeWidthModes.push_back (3);
        }

    // Create line code
    int             nameIndex = 0;
    WChar         wNameChars[512];

    if (strokeSymbolsRequired)
        {
        wcscpy (wNameChars, L"LC_");
        nameIndex = 3;
        }

    const ACHAR*    lineTypeName;
    lineType->getName (lineTypeName);
    RealDwgUtil::TerminatedStringCopy (wNameChars + nameIndex, lineTypeName, _countof (wNameChars)-nameIndex);

    MSElementDescrP pDefinitionDescr = NULL; 
    AcDbDatabase* pDatabase = m_pFileHolder->GetDatabase();
    int options = pDatabase->plinegen() ? LCOPT_CENTERSTRETCH : (LCOPT_CENTERSTRETCH | LCOPT_SEGMENT);

    LineStyleUtil::CreateLineCodeEntry (&pDefinitionDescr,
                                        wNameChars,
                                        NULL, // (strokeSymbolsRequired ? NULL : getDescription()), // Description
                                        0.0,                                                        // Phase
                                        options,                                                    // Options
                                        0,                                                          // maxIterate
                                        0.0,                                                        // orgAngle
                                        0.0,                                                        // endAngle
                                        (int) strokeLengths.size(),                                 // NStrokes
                                        &strokeLengths.front(),                                     // stroke lengths
                                        NULL,                                                       // Start widths
                                        NULL,                                                       // End widths
                                        &strokeModes.front(),                                       // Stroke modes
                                        &strokeWidthModes.front(),                                  // Width modes
                                        strokeSymbolsRequired ? GetAndIncrementNextId() : ElementIdFromObject (lineType),   // Element ID
                                        this->GetModel()
                                        );

    if (strokeSymbolsRequired)
        {
        MSElementDescrP pLinePointDescr = NULL;
        MSElementDescrP pCompoundDescr = NULL;
        UInt32          componentTypes[2];
        ElementId       componentIds[2];

        // If there are stroke symbols, then we need to create a PointLine element,
        //and include all the symbols
        if (NULL != (pLinePointDescr = PointLineToElement (lineType, pDefinitionDescr->el.ehdr.uniqueId, *this)))
            {
            // Create the compound element
            componentTypes[0] = RTYPE_LinePoint;
            componentIds[0]   = pLinePointDescr->el.ehdr.uniqueId;
            componentTypes[1] = RTYPE_LineCode;
            componentIds[1]   = pDefinitionDescr->el.ehdr.uniqueId;

            RealDwgUtil::TerminatedStringCopy (wNameChars, lineTypeName, _countof (wNameChars));
            LineStyleUtil::CreateCompoundEntry (&pCompoundDescr,                            // <= Element
                                                wNameChars,                                 // Name
                                                NULL,                                       // Description
                                                2,
                                                componentTypes,                             // Types of components
                                                componentIds,                               // Ids of components
                                                NULL,                                       // Offsets of components; NULL means all 0.0
                                                this->ElementIdFromObject (lineType),       // Id for this element
                                                this->GetModel()
                                                );


            // Accumulate all the elements into a single chain
            pCompoundDescr->AddToChain (pDefinitionDescr);
            pCompoundDescr->AddToChain (pLinePointDescr);
            pDefinitionDescr = pCompoundDescr;
            }
        }

    lineTypeElement.SetModelRef (this->GetModel());
    lineTypeElement.SetElementDescr (pDefinitionDescr, true, false);

    return RealDwgSuccess;
    }

#ifdef FUTUREWORK_COPY_SEED_LINESTYLES
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            CopyLinestyleFromSeedDgn
(
EditElementHandleR          defTable,
EditElementHandleR          nameTable,
const AcString&             linetypeName,
const AcString&             dgnFileName,
AcDbLinetypeTableRecord*    linetype,
ConvertToDgnContextR        context
)
    {
    /*-----------------------------------------------------------------------------------
    FUTUREWORK: we need to figure out a way to duplicate all nested and/or dependents of a
    def entry element.  The existing function mdlLineStyle_duplicateStyleDef reads styles
    from seed modelref, clones and writes them to destination modelref, but in our case we
    do not have seed modelref neither do we want it write anything to our modelref.
    -----------------------------------------------------------------------------------*/
    const ACHAR*    styleName = linetypeName.kwszPtr();
    if (NULL == styleName || 0 == styleName[0])
        return  BSIERROR;

    MSElementDescrCP    seedDefTable = context.GetFileHolder()->GetSeedLinestyleDefTable();
    MSElementDescrCP    seedNameTable = context.GetFileHolder()->GetSeedLinestyleNameTable();
    if (NULL == seedDefTable || NULL == seedNameTable)
        return  BSIERROR;

    bool                foundEntry = false;
    for (MSElementDescrP nameEntry = seedNameTable->h.firstElem; NULL != nameEntry; nameEntry = nameEntry->h.next)
        {
        if (0 == wcscmp(styleName, nameEntry->el.lineStyleNameEntry.name) && 0 != nameEntry->el.lineStyleNameEntry.refersTo)
            {
            for (MSElementDescrP defEntry = seedDefTable->h.firstElem; NULL != defEntry; defEntry = defEntry->h.next)
                {
                if (nameEntry->el.lineStyleNameEntry.refersTo == defEntry->el.ehdr.uniqueId)
                    {
                    MSElementDescrP     nextEntry = defEntry->h.next;
                    defEntry->h.next = NULL;

                    MSElementDescrP     newDefEntry = NULL;
                    StatusInt           status = mdlLineStyle_duplicateStyleDef (&newDefEntry, defEntry, context.GetModelRef(), context.GetModelRef());

                    defEntry->h.next = nextEntry;

                    if (SUCCESS == status)
                        {
                        newDefEntry->el.ehdr.uniqueId = context.ElementIdFromObject (linetype);
                        mdlElmdscr_appendDscr (defTable, newDefEntry);

                        nextEntry = nameEntry->h.next;

                        MSElementDescrP newNameEntry = NULL;
                        status = nameEntry->Duplicate (&newNameEntry, true, false);

                        nameEntry->h.next = nextEntry;

                        SetElementIdNull (newNameEntry);
                        newNameEntry->el.lineStyleNameEntry.id = -Int32(newDefEntry->el.ehdr.uniqueId);
                        mdlElmdscr_appendDscr (nameTable, newNameEntry);
                        }

                    newDefEntry->Release ();
                    foundEntry = true;

                    break;
                    }
                }
            }
        if (foundEntry)
            break;
        }

    if (!foundEntry)
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_LinetypeIgnored, false, styleName, dgnFileName.kwszPtr());

    return  status;
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
static void     GetObjectIdCallback (AcDb::DxfCode dxfGroupCode, const void* dataIn, void* dataOut)
    {
    if (AcDb::kDxfHardPointerId == dxfGroupCode)
        {
        AcDbObjectId    objectIdIn = AcDbObjectId ((AcDbStub*)dataIn);
        AcDbObjectId*   objectIdOut = (AcDbObjectId*)dataOut;
        *objectIdOut = objectIdIn;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ExtractLsRscFileName (WString& rscFileName, const AcDbObjectId& dicEntryId, const AcDbDictionary* dgnlscompDictionary)
    {
    rscFileName.clear ();

    if (NULL == dgnlscompDictionary)
        return  false;

    AcDbDictionaryIterator* dicIterator = dgnlscompDictionary->newIterator ();
    if (NULL == dicIterator)
        return  false;

    for (; !dicIterator->done(); dicIterator->next())
        {
        if (dicEntryId == dicIterator->objectId())
            {
            const ACHAR*    dicEntryName = dicIterator->name();
            if (NULL != dicEntryName && 0 != dicEntryName[0])
                {
                const ACHAR*    rscNameEnd = wcsstr (dicEntryName, L".rsc-");
                if (NULL != rscNameEnd)
                    {
                    rscFileName = WString (dicEntryName, rscNameEnd - dicEntryName + 4);
                    break;
                    }
                }
            }
        }

    delete dicIterator;

    return  !rscFileName.empty();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     GetRootDgnLinestyleNames (DgnLStyleAsLType& dgnlsNames, const AcString& linetypeName, const AcString& rscFileName)
    {
    // if the linetype is merged from an xref, remove the file name separated by '$':
    int         linestyleMergedAt = linetypeName.findRev (L'$');
    int         filenameMergedAt = rscFileName.findRev (L'$');
    bool        isMerged = linestyleMergedAt > 1 && filenameMergedAt > 1;

    // only if the linestype name and dictionary entry name both appear to be merged we will remove the file names:
    if (isMerged)
        {
        dgnlsNames.m_linetypeName = linetypeName.substr (linestyleMergedAt + 1, -1);
        dgnlsNames.m_rscFileName = rscFileName.substr (filenameMergedAt + 1, -1);
        }
    else
        {
        dgnlsNames.m_linetypeName = linetypeName;
        dgnlsNames.m_rscFileName = rscFileName;
        }

    // create a string to sort the map
    dgnlsNames.m_compareString = dgnlsNames.m_linetypeName + dgnlsNames.m_rscFileName;

    return  !dgnlsNames.m_linetypeName.isEmpty() && !dgnlsNames.m_rscFileName.isEmpty();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
static Int32                ImportLinestyleFromRscFile
(
EditElementHandleR          defTable,
EditElementHandleR          nameTable,
const AcString&             linetypeName,
const AcString&             rscFileName,
AcDbLinetypeTableRecord*    linetype,
T_DgnLStylesInDwg&          dgnStylesInDwg,
ConvertToDgnContextR        context
)
    {
    /*-----------------------------------------------------------------------------------------------------------
    This method parses DGN linestyle name in input DWG linetype name, together with input RSC linestyle file name.
    If they appear valid, call RSC linestyle importer.  If the import succeeded, return the new style ID which 
    seems the negative value of the linestyle element ID.  The imported line style info is saved in a local cache
    such that when next line type comes in and found in the cache we will simply use it, skipping the all together
    the import process.  The local cache lookup improves large number of linestyles, a case seen in TFS 217372.
    When a line type is bound/merged from an xref in ACAD, the line type name as well as the RSC file name would
    contain DWG/DGN file name with a separator '$'.  Since these linetypes are imported from an RSC file we can
    import the same RSC file for all linestyles found in the DWG file even if they were merged from an xref file.
    -----------------------------------------------------------------------------------------------------------*/
    Int32               dgnlstyleId = DgnPlatform::STYLE_Invalid;
    ElementId           elementId = context.ElementIdFromObject (linetype);
    DgnLStyleAsLType    dgnlsNames;
    if (!GetRootDgnLinestyleNames(dgnlsNames, linetypeName, rscFileName))
        return  dgnlstyleId;

    // use the dgn style index if the style has already been imported.
    auto            entry = dgnStylesInDwg.find (dgnlsNames);
    if (entry != dgnStylesInDwg.end())
        return  entry->second;

    // this is a new linestyle that needs to be imported
    const ACHAR*    lineStyleName = dgnlsNames.m_linetypeName.kwszPtr();
    BeFileName      foundRscFile;
    StatusInt       status = util_findFile (nullptr, &foundRscFile, dgnlsNames.m_rscFileName.kwszPtr(), L"MS_SYMBRSRC", nullptr, 0);
    if (BSISUCCESS != status)
        {
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_DgnLinestyleRscNotFound, false, lineStyleName, dgnlsNames.m_rscFileName.kwszPtr());
        return  dgnlstyleId;
        }

    RscFileHandle   rscHandle;
    if (BSISUCCESS != (status = RmgrResource::OpenFile (&rscHandle, foundRscFile, RSC_READONLY)))
        {
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_DgnLinestyleRscNotFound, false, lineStyleName, foundRscFile);
        return  dgnlstyleId;
        }

    status = LINESTYLE_STATUS_StyleNotFound;

    // find the line style name entry in the resource file. There is the possibility of multiple line style name map resources.
    int     nMaps;
    RmgrResource::QueryClass (&nMaps, rscHandle, RTYPE_LineStyleNames, RSC_QRY_COUNT, NULL);
    for (int iMap=0; iMap < nMaps; iMap++)
        {
        long    rscId;
        RmgrResource::QueryClass (&rscId, rscHandle, RTYPE_LineStyleNames, RSC_QRY_ID, &iMap);

        StringListP thisNameMap;
        if (NULL == (thisNameMap = mdlStringList_loadResourceWithType (rscHandle, RTYPE_LineStyleNames, rscId)))
            continue;

        long    memberIndex;
        if (SUCCESS != mdlStringList_search (&memberIndex, thisNameMap, lineStyleName, NULL, 0, NULL))
            {
            mdlStringList_destroy (thisNameMap);
            continue;
            }

        InfoField*  infoFields;
        if (SUCCESS == mdlStringList_getMember (NULL, &infoFields, thisNameMap, memberIndex))
            {
            LsResourceType  rscType     = (LsResourceType) infoFields[0];
            rscId = infoFields[1];

            UInt32          flags       = (UInt32) infoFields[2];
            Int32           posStyleNum = (Int32) infoFields[3];
            double          unitFactor  = ((double) infoFields[4] / LSUNIT_FACTOR);

            if (SUCCESS != (status = LineStyleUtil::ImportFromRsc (defTable, nameTable, elementId, lineStyleName, Int32(rscHandle), rscType, rscId, flags,
                                                                    posStyleNum, unitFactor, *context.GetFile(), false)) &&
                defTable.IsValid() && nameTable.IsValid())
                {
                // try linetype description as it may be an originally saved linestyle name:
                const ACHAR*    savedName = NULL;
                if (Acad::eOk == linetype->comments(savedName))
                    {
                    status = LineStyleUtil::ImportFromRsc (defTable, nameTable, elementId, savedName, Int32(rscHandle), rscType, rscId, flags,
                                                            posStyleNum, unitFactor, *context.GetFile(), false);
                    }
                }
            }

        // found the name entry, get out of this loop.
        mdlStringList_destroy (thisNameMap);
        break;
        }

    RmgrResource::CloseFile (rscHandle);

    if (SUCCESS != status)
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_DgnLinestyleRscNotFound, false, lineStyleName, foundRscFile);

    // the imported linestyle appears to use -elementID as the styleID:
    if (BSISUCCESS == status)
        dgnlstyleId = -Int32 (elementId);
    else
        dgnlstyleId = DgnPlatform::STYLE_Invalid;

    // add the newly imported DGN linestyle ID into the linestyle map
    dgnStylesInDwg.insert (bpair<DgnLStyleAsLType, Int32>(dgnlsNames, dgnlstyleId));

    return  dgnlstyleId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ExtractDgnFileName (AcString& dgnFileName, AcString const& entryName)
    {
    int         index = entryName.find (L'-');
    if (index > 0)
        {
        dgnFileName = entryName.substr (index);

        // we don't know for sure if the dictionary entry name contains a DGN file name unless we see a .dgn file extension:
        index = dgnFileName.findRev (L".dgn");
        if (index > 0 && index == dgnFileName.length() - 4)
            return  true;
        }

    return  false;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveEmbeddedDgnLinestyleFromLinetype
(
Int32&                      dgnStyleId,
EditElementHandleR          defTable,
EditElementHandleR          nameTable,
AcDbLinetypeTableRecord*    linetype,
T_DgnLStylesInDwg&          dgnStylesInDwg
)
    {
    AcString                linetypeName;
    if (NULL == linetype || Acad::eOk != linetype->getName(linetypeName) || linetypeName.isEmpty())
        return  EntityError;

    AcDbObject*             dgnlsdefObject = NULL;
    AcDbDictionaryPointer   extDictionary(linetype->extensionDictionary(), AcDb::kForRead);
    if (Acad::eOk != extDictionary.openStatus() || Acad::eOk != extDictionary->getAt(L"DGNLSDEF", dgnlsdefObject, AcDb::kForRead))
        return  CantOpenObject;

    // extract objectID of dictionary entry stored in this dgnlsdefObject:
    AcDbObjectId            dicEntryId;
    AcDbDatabase*           database = linetype->database ();
    ExtractionFiler filer (GetObjectIdCallback, database, &dicEntryId);
    filer.ExtractFrom (dgnlsdefObject);
    dgnlsdefObject->close ();
    if (!dicEntryId.isValid())
        return  EntityError;

    // Since we do not have AcDbLS* classes we have to open and iterate through dictionary ACAD_DGNLINESTYLECOMP for ACAD_PROXY_OBJECT's:
    AcDbObjectId            dgnlsCompId;
    AcDbDictionaryPointer   mainDictionary(database->namedObjectsDictionaryId(), AcDb::kForRead);
    if (Acad::eOk == mainDictionary.openStatus() && Acad::eOk == mainDictionary->getAt(L"ACAD_DGNLINESTYLECOMP", dgnlsCompId))
        {
        AcDbDictionaryPointer   dgnlsCompDictionary(dgnlsCompId, AcDb::kForRead);
        if (Acad::eOk != dgnlsCompDictionary.openStatus())
            return  CantOpenObject;

        AcString            entryName;
        if (Acad::eOk != dgnlsCompDictionary->nameAt(dicEntryId, entryName) || entryName.isEmpty())
            return  InvalidName;

        AcString            dgnFileName;
        StatusInt           status = BSIERROR;
        int                 index = 0;
        if (1 == swscanf(entryName.kwszPtr(), L"DGN%d", &index))
            {
            dgnStyleId = (index >= MIN_LINECODE && index <= MAX_LINECODE) ? index : MIN_LINECODE;
            status = SUCCESS;
            }
        else if ((index = entryName.find(L".rsc-")) > 0)
            {
            AcString        rscFileName = entryName.substr (index + 4);
            dgnStyleId = ImportLinestyleFromRscFile (defTable, nameTable, linetypeName, rscFileName, linetype, dgnStylesInDwg, *this);
            if (DgnPlatform::STYLE_Invalid != dgnStyleId)
                status = BSISUCCESS;
            }
        else if (ExtractDgnFileName(dgnFileName, entryName))
            {
            // FUTUREWORK: status = CopyLinestyleFromSeedDgn (defTable, nameTable, linetypeName, dgnFileName, linetype, *this);
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_LinetypeIgnored, false, linetypeName.kwszPtr(), dgnFileName.kwszPtr());
            dgnStyleId = DgnPlatform::STYLE_Invalid;
            }

        return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
        }

    return  CantOpenObject;
    }


// =======================================================================================
// Saving DGN to DWG

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SaveDgnLineStylesToDatabase()
    {
    // there is no convenient linestyle iteration API, so we have to go through the elements. First find the line style table element.
    PersistentElementRefListIterator    elementIterator = m_pFileHolder->GetFile()->GetDictionaryElements()->begin();
    for (ElementRefP elemRef = elementIterator.GetCurrentElementRef(); NULL != elemRef; elemRef = elementIterator.GetNextElementRef())
        {
        // If the element was either deleted or made part of a complex, delete it.
        Elm_hdr const*  ehdr;
        if (NULL == (ehdr = elemRef->GetElementHeaderCP()))
            continue;

        if (TABLE_ELM != ehdr->type || MS_LSTYLE_DEF_LEVEL != ehdr->level)
            continue;

        // add each line style table entry to the AVL tree of currently active line styles.
        ElementHandle  elemHandle (elemRef, m_model);

        // first step is to delete the line styles that have been deleted during the session from the DWG database.
        SignedTableIndex*       tableIndex  = m_pFileHolder->GetLinetypeIndex();
        ElementIdArrayR         originatedInDwgList = tableIndex->GetOriginatedInDwgList();
        if (this->SavingChanges() && !originatedInDwgList.empty())
            {
            AvlTreeP            currentIdTree   = mdlAvlTree_init (AVLKEY_UINT64);

            // build an AvlTree with all of the current Ids in it.
            for (ChildElemIter child (elemHandle, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
                {
                if (TABLE_ENTRY_ELM != child.GetElementType())
                    continue;

                ElementId       elementId = child.GetElementId ();
                mdlAvlTree_insertNode (currentIdTree, (void*)&elementId, sizeof(elementId));
                }

            // now go through the "originatedInDwgIds" (those that were create from DWG elements when we started) and delete those that are no longer in the "current" list.
            this->DeleteSymbolTableRecords (tableIndex, currentIdTree);
            }

        // now go through the line style table entries and save then to the AcDbDatabase.
        for (ChildElemIter child (elemHandle, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
            {
            // Do not process cells in linestyle table.  We do not have a "current block" yet and there is no value saving them.
            if (CELL_HEADER_ELM == child.GetElementType())
                continue;

            this->SaveDgnLineStyleToDatabase (child);
            }

        // found the table, and there's only one, so stop.
        return RealDwgSuccess;
        }

    // some DGN files may not have linestyle table
    DIAGNOSTIC_PRINTF ("DGN file does NOT have a linestyle table!\n");
    return LineStyleTableNotFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SaveDgnLineStyleToDatabase (ElementHandleR elemHandle)
    {
    AcDbLinetypeTableRecordPointer  acLinetype;
    AcDbObjectId                    existingObjectId = this->ExistingObjectIdFromElementId (elemHandle.GetElementId());

    WString     styleName = LineStyleManager::GetStyleNameForDefinition (*m_dgnFile, elemHandle.GetElementId());
    
    if (existingObjectId.isNull())
        {
        // no existing object.
        AcString        name = styleName.c_str();

        // This may be a style component rather than an actual style - in this case, it should not exist in the name map.
        if (name.isEmpty())
            return RealDwgSuccess;

        // TR# 152067, 165172 - Dont import xref dependent.
        if (-1 != name.find (L"|"))
            return RealDwgSuccess;

         if (0 == name.compareNoCase (L"continuous"))
            return RealDwgSuccess;

        // I think this is unnecessary now.    
        Int32       styleNo = LineStyleManager::GetStyleIDForDesignFile (name, *m_dgnFile, false, false);  // They'll be brought in by layer as necessary.
        if (0 == styleNo)
            return RealDwgSuccess;

        // create a new one.
        acLinetype.create();

        AcDbDatabase*               database    = m_pFileHolder->GetDatabase();
        AcDbObjectId                ownerId     = database->linetypeTableId();
        AcString                    lineTypeName (name);

        this->DeduplicateTableName (ownerId, lineTypeName);
        if (Acad::eOk == acLinetype->setName(lineTypeName))
            {
            AcDbLinetypeTablePointer    acLinetypeTable (ownerId, AcDb::kForWrite);
            if (Acad::eOk == acLinetypeTable.openStatus())
                {
                AcDbObjectId newObjectId = this->AddRecordToSymbolTable (acLinetypeTable, acLinetype, elemHandle.GetElementId());
                if (newObjectId.isNull())
                    {
                    DIAGNOSTIC_PRINTF ("Error adding a new linetype to database.\n");
                    return CantCreateObject;
                    }
                }
            }
        }
    else
        {
        // there is an existing object.
        acLinetype.open (existingObjectId, AcDb::kForWrite);
        if (Acad::eOk != acLinetype.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening an existing linetype! [%ls]\n", acadErrorStatusText(acLinetype.openStatus()));
            return CantOpenObject;
            }
        }

    if (0 == acLinetype->numDashes() || this->GetSettings().OverrideExistingLinetypeDefinitions())
        {
        WChar               stringChars[512] = {L'\0'};
        WString             description;
        Int32               nSegments, lineStyleUnitMode, styleNumber;
        StatusInt           status = ERROR;
        DwgLineStyleInfo    styleInfo [MAX_LineStyleSegs];

        WString             suggestedFontPath;
        GetSettings().GetDwgShapeFilePath (suggestedFontPath);
        
        if (STYLE_Invalid != (styleNumber = LineStyleManager::GetNumberFromName (styleName.c_str(), *this->m_pFileHolder->GetFile())) &&
            SUCCESS == (status = LineStyleUtil::ExtractLineStyleEntryAsLinSegments (styleInfo, &nSegments, stringChars, &lineStyleUnitMode, description, styleNumber, suggestedFontPath.c_str(), this->GetModel())) && nSegments >= 2)
            {
            LsDefinitionP   lsDef = LsMap::FindInRef (*m_dgnFile, styleNumber);
            double          lsScale = NULL == lsDef ? 1.0 : lsDef->GetUnitsDefinition ();
            
            status = this->SetLinetypeFromStyleParams (acLinetype, styleInfo, nSegments, stringChars, description.c_str(), lineStyleUnitMode, this->GetModel(), NULL, lsScale);
            }

        if (SUCCESS == status)
            this->GetFileHolder().GetLinetypeIndex()->AddEntry (styleNumber, acLinetype->objectId().handle(), false);

        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::CreateLinetypeFromStyleId
(
DgnModelRefP                sourceModelRef,
AcDbBlockTableRecord*       pXRefBTR,               // must be NULL or open for read.
DgnModelRefP                referenceModelRef,
bool                        useExistingId,
Int32                       styleId
)
    {
    if (0 == styleId)
        return  m_pFileHolder->GetContinuousLinetype();

    // First look for a match among current styles.
    WChar             nameChars[512];
    if (IS_LINECODE (styleId))
        swprintf (nameChars, L"DGN Style %d", styleId);
    else
        {
        WString lsname = LineStyleManager::GetNameFromNumber (styleId, sourceModelRef->GetDgnFileP());
        BeStringUtilities::Wcsncpy (nameChars, lsname.c_str(), _countof(nameChars));
        }

    // Fix for TR# 114964
    WChar*            pSeparator;
    WChar*            pNameChars = nameChars;
    while (NULL != (pSeparator = wcschr (pNameChars, '|')))
        pNameChars = pSeparator + 1;

    this->ValidateName (pNameChars); // Modifies name

    WCharCP             fullName = pNameChars;
    AcString            findName;
    bool                xRefDependent = false;
    if ( (NULL != pXRefBTR) && (0 != wcsicmp (StringConstants::ContinousLinetypeName, nameChars)) )
        {
        xRefDependent = true;
        RealDwgUtil::GetXRefDependentSymbolTableName (findName, pNameChars, pXRefBTR);
        fullName = findName.kwszPtr();
        }

    AcDbObjectId        lineTypeId;
    if (! (lineTypeId = m_pFileHolder->GetLinetypeByName (fullName)).isNull())
        return lineTypeId;

    WString             suggestedFontPath;
    GetSettings().GetDwgShapeFilePath (suggestedFontPath);

    WChar               stringChars[512] = {L'\0'};
    WString             description;
    DwgLineStyleInfo    styleInfo [MAX_LineStyleSegs];
    Int32               nSegments, lineStyleUnitMode;
        
    if (SUCCESS != LineStyleUtil::ExtractLineStyleEntryAsLinSegments (styleInfo, &nSegments, stringChars, &lineStyleUnitMode, description, styleId, suggestedFontPath.c_str(), sourceModelRef) || nSegments < 2)
        return  m_pFileHolder->GetContinuousLinetype ();

    AcDbObjectId                linetypeTableId = m_pFileHolder->GetDatabase()->linetypeTableId();
    AcDbLinetypeTablePointer    pLinetypeTable (linetypeTableId, AcDb::kForWrite);
    if (Acad::eOk != pLinetypeTable.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Error opening linetype table for adding a new linetype! [%ls]\n", acadErrorStatusText(pLinetypeTable.openStatus()));
        return  m_pFileHolder->GetContinuousLinetype ();
        }

    AcDbLinetypeTableRecord*    pLinetype = NULL;
    if (!xRefDependent)
        {
        ElementId                   lineStyleDefinitionId = 0, styleRscId = 0;
        bool                        isElement = false;

        if (useExistingId && SUCCESS == LineStyle_getStyleSource (NULL, NULL, &styleRscId, &isElement, NULL, styleId, sourceModelRef) && isElement && sourceModelRef == m_model)
            lineStyleDefinitionId = styleRscId;

        pLinetype = new AcDbLinetypeTableRecord();
        pLinetype->setName (fullName);

        lineTypeId = this->AddRecordToSymbolTable (pLinetypeTable, pLinetype, lineStyleDefinitionId);
        if (!lineTypeId.isNull())
            {
            m_pFileHolder->GetLinetypeIndex()->AddEntry (styleId, lineTypeId.handle(), false);
            }
        else
            {
            delete pLinetype;
            pLinetype = NULL;
            }
        }
    else
        {
        pLinetype = new AcDbLinetypeTableRecord();
        pLinetype->setName (fullName);

        lineTypeId = this->AddRecordToSymbolTable (pLinetypeTable, pLinetype, 0);
        if (!lineTypeId.isNull())
            {
            RealDwgUtil::MakeSymbolTableRecordDependentOnXRef (pLinetype, pXRefBTR->objectId());
            }
        else
            {
            delete pLinetype;
            pLinetype = NULL;
            }
        }

    if (NULL != pLinetype)
        {
        LsDefinitionP   lsDef = LsMap::FindInRef (*m_dgnFile, styleId);
        double          lsScale = NULL == lsDef ? 1.0 : lsDef->GetUnitsDefinition ();

        this->SetLinetypeFromStyleParams (pLinetype, styleInfo, nSegments, stringChars, description.c_str(), lineStyleUnitMode, sourceModelRef, referenceModelRef, lsScale);
        pLinetype->close();
        }

    return lineTypeId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SetLinetypeFromStyleParams
(
AcDbLinetypeTableRecord*    acLinetype,
DwgLineStyleInfo*           pStyleInfo,
int                         nSegments,
WCharCP                     pStringChars,
WCharCP                     pDescription,
int                         lineStyleUnitMode,
DgnModelRefP                sourceModelRef,
DgnModelRefP                referenceModelRef,
double                      styleScale
)
    {
    double                  segmentScale = 1.0;

    switch (lineStyleUnitMode)
        {
        case LSATTR_UNITDEV:
            if (0.0 == (segmentScale = this->GetSettings().GetLineCodeScale() * this->GetScaleFromDGN()))
                {
                ElementId   activeViewGroupId = this->GetActiveViewGroupId ();
                if (0 != activeViewGroupId && INVALID_ELEMENTID != activeViewGroupId)
                    {
                    ViewGroupCollectionCR   viewGroups = m_model->GetDgnFileP()->GetViewGroups ();
                    for each (ViewGroupPtr viewGroup in viewGroups)
                        {
                        ViewInfoCR  viewInfo = viewGroup->GetViewInfo (0);
                        if (viewGroup->GetElementId() == activeViewGroupId && viewInfo.GetViewFlags().on_off)
                            {
                            segmentScale = RATIO_LineCodePixel * this->GetScaleFromDGN() * MAX(viewInfo.GetDelta().x, viewInfo.GetDelta().y);
                            break;
                            }
                        }
                    }
                if (0.0 == segmentScale)
                    segmentScale = 1.0;
                }
            break;

        case LSATTR_UNITUOR:
            {
            ModelInfoCP     sourceModelInfo = sourceModelRef->GetModelInfoCP();
            if ( (sourceModelRef == this->GetModel()) || (NULL == sourceModelInfo) )
                {
                segmentScale = this->GetScaleFromDGN();
                }
            else
                {
                double              scaleToDGN = 0.0;
                Transform           transformToDGN;

                // We are saving to DWG, so we need to adjust the segment size based on source model and the output DWG units.
                DwgOpenUnitMode     unitMode = this->GetDwgOpenUnitModeFromSaveUnitMode (m_targetUnitMode);

                this->GetTransformToDGNFromModelInfo (&transformToDGN, &scaleToDGN, NULL, *sourceModelInfo, m_model, unitMode);
                segmentScale = 1.0 / scaleToDGN;
                }
            break;
            }

        default:
        case LSATTR_UNITMASTER:
            segmentScale = this->GetScaleFromMasterUnits ();
            break;
        }

    if ( (LSATTR_UNITDEV != lineStyleUnitMode) && (0.0 != styleScale) )
        segmentScale *= styleScale;

    acLinetype->setNumDashes (nSegments);

    double      totalLength = 0.0;
    // Put the data from the array into the segments.
    for (int iSegment = 0; iSegment < nSegments; iSegment++)
        {
        double          dashLength = segmentScale * pStyleInfo[iSegment].length;

        if ( (dashLength > 0.0) && (dashLength < (1.0E-8 + this->GetScaleFromDGN())) )
            dashLength = 0.0;

        // TR# 130572 - The GM Audit will reject a style if it doesn't exact compare - so
        // round to nearest 1000th if it is very close.
        double          dash1000 = dashLength * 1000.0, roundDash1000 = (double) DataConvert::RoundDoubleToLong(dash1000);
        if (fabs (dash1000 - roundDash1000) < 1.0E-4)
            dashLength = roundDash1000 / 1000.0;

        totalLength += fabs (dashLength);
        acLinetype->setDashLengthAt (iSegment, dashLength);
        if (0.0 == pStyleInfo[iSegment].scale)
            acLinetype->setShapeScaleAt (iSegment, 0.0);  // Non-scaled shape
        else
            acLinetype->setShapeScaleAt (iSegment, 1.0/(pStyleInfo[iSegment].scale) * segmentScale);
        acLinetype->setShapeRotationAt (iSegment, pStyleInfo[iSegment].rotAng);

        AcGeVector2d        offset (segmentScale * pStyleInfo[iSegment].xOffset, segmentScale * pStyleInfo[iSegment].yOffset);
        acLinetype->setShapeOffsetAt (iSegment, offset);

        if (0 != pStyleInfo[iSegment].shapeFlag)
            {
            if (0 != (pStyleInfo[iSegment].shapeFlag & SHAPEFLAG_TextSymbol))
                {
                AcString    styleInfo (pStringChars + pStyleInfo[iSegment].strOffset);
                acLinetype->setTextAt (iSegment, styleInfo);
                }
            else
                acLinetype->setShapeNumberAt (iSegment, pStyleInfo[iSegment].complexShapeCode);


            AcDbObjectId    textStyleObjectId;
            if ( (0 == pStyleInfo[iSegment].textStyleId) || (m_model != sourceModelRef) ||   // If from a reference, can't look up by Id.
                (textStyleObjectId = m_pFileHolder->GetTextStyleIndex()->GetObjectId (pStyleInfo[iSegment].textStyleId, m_pFileHolder->GetDatabase())).isNull())
                {
                AcDbTextStyleTableRecord*   pTextStyle = NULL;

                // Have font number; resolve
                if (-1 != pStyleInfo[iSegment].fontNo)
                    {
                    DgnFontR   font = DgnFontManager::ResolveFontNum (pStyleInfo[iSegment].fontNo, sourceModelRef);
                    m_pFileHolder->AddFontToInstallTree (&font);
                    textStyleObjectId = this->AddTextStyleFromFontNumber (pStyleInfo[iSegment].fontNo, 0, false, false, false, 0.0, sourceModelRef);
                    }
                // No font number; use name
                else
                    {
                    RealDwgGeneratedTextStyleItem*      pGeneratedTextStyle;
                    WCharP                              fontNameW = pStyleInfo[iSegment].fontName;

                    if (NULL == (pGeneratedTextStyle = m_pFileHolder->GetGeneratedTextStyle (fontNameW)))
                        {
                        AcString                    fileName    = AcString (fontNameW) + AcString (L".shx");
                        AcDbObjectId                textTableId =  m_pFileHolder->GetDatabase()->textStyleTableId();
                        AcDbTextStyleTablePointer   pTextTable  (textTableId, AcDb::kForWrite);

                        if (Acad::eOk != pTextTable.openStatus())
                            {
                            DIAGNOSTIC_PRINTF ("Error opening textstyle table to add a new textstyle for linestyle! [%ls]\n", acadErrorStatusText(pTextTable.openStatus()));
                            }
                        else
                            {
                            pTextStyle = new AcDbTextStyleTableRecord();
                            pTextStyle->setIsShapeFile (true);
                            pTextStyle->setName (fontNameW);
                            pTextStyle->setFileName (fileName.kwszPtr());
                            pTextStyle->setTextSize (1.0);
                            pTextTable->add (pTextStyle);
                            m_pFileHolder->AddGeneratedTextStyle (new RealDwgGeneratedTextStyleItem (fontNameW, pTextStyle));
                            textStyleObjectId = pTextStyle->objectId();
                            pTextStyle->close();
                            }
                        }
                    else
                        {
                        textStyleObjectId = pGeneratedTextStyle->GetTextStyleObjectId();
                        }
                    }
                if (!textStyleObjectId.isNull())
                    acLinetype->setShapeStyleAt (iSegment, textStyleObjectId);
                }
            else
                {
                acLinetype->setShapeStyleAt (iSegment, textStyleObjectId);
                }

            GetOrSetLinetypeTextUpwardFlag (acLinetype, iSegment, (bool*)&pStyleInfo[iSegment].adjustRotation);
            }
        }


    acLinetype->setPatternLength (totalLength);
    acLinetype->setComments (AcString (pDescription));

    return SUCCESS;
    }
