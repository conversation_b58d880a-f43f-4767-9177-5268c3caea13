#include "DWGSplineProcessor.h"
#include "../DWGExporter.h"

#ifdef REALDWG_AVAILABLE
#include <realdwg/base/dbspline.h>
#include <realdwg/ge/gespline.h>
#include <realdwg/ge/genurb3d.h>
#include <realdwg/ge/geintrvl.h>
#include <realdwg/ge/gepoint3d.h>
#include <realdwg/ge/gevector3d.h>
#endif

#include <algorithm>
#include <cmath>
#include <numeric>

namespace IModelExport {

//=======================================================================================
// DWG Spline Processor Implementation
//=======================================================================================

DWGSplineProcessor::DWGSplineProcessor(DWGExporter* exporter)
    : DWGEntityProcessor(exporter)
    , m_knotTolerance(1e-10)
    , m_geometryTolerance(1e-10)
    , m_curvatureTolerance(1e-6)
    , m_maxIterations(100)
    , m_enableAutoRepair(true)
    , m_enableDegenerationDetection(true)
{
}

DWGProcessingStatus DWGSplineProcessor::ProcessEntity(const ElementInfo& element) {
    if (!CanProcessEntity(element)) {
        return DWGProcessingStatus::UnsupportedEntity;
    }
    
    try {
        // Extract spline geometry from element
        // This is a placeholder - real implementation would extract from iModel
        SplineGeometry geometry;
        
        // Sample B-spline curve data
        geometry.degree = 3;
        geometry.controlPoints = {
            Point3d(0, 0, 0),
            Point3d(33.33, 33.33, 0),
            Point3d(66.67, 33.33, 0),
            Point3d(100, 0, 0)
        };
        geometry.knots = {0, 0, 0, 0, 1, 1, 1, 1}; // Clamped knot vector
        geometry.isRational = false;
        geometry.isPeriodic = false;
        geometry.isClosed = false;
        
        std::string layer = "Curves";
        
        // Validate and process the spline
        auto validation = ValidateSplineGeometry(geometry);
        if (!validation.isValid) {
            LogError("Spline geometry validation failed for element " + element.id);
            for (const auto& error : validation.errors) {
                LogError("  " + error);
            }
            
            // Attempt repair if enabled
            if (m_enableAutoRepair) {
                LogInfo("Attempting to repair spline geometry");
                if (ValidateAndRepairSpline(geometry)) {
                    LogInfo("Spline geometry repaired successfully");
                    m_repairedSplines++;
                } else {
                    LogError("Failed to repair spline geometry");
                    return DWGProcessingStatus::ValidationError;
                }
            } else {
                return DWGProcessingStatus::ValidationError;
            }
        }
        
        // Process the spline
        auto status = ProcessBSplineCurve(geometry, layer);
        
        if (status == DWGProcessingStatus::Success) {
            m_processedCount++;
            m_processedSplines++;
            LogInfo("Successfully processed spline element " + element.id);
        }
        
        return status;
    }
    catch (const std::exception& e) {
        LogError("Exception processing spline element " + element.id + ": " + e.what());
        return DWGProcessingStatus::Failed;
    }
}

bool DWGSplineProcessor::CanProcessEntity(const ElementInfo& element) const {
    // Check if this is a spline/curve-type element
    return element.type == ElementType::GeometricElement; // Simplified check
}

DWGProcessingStatus DWGSplineProcessor::ProcessBSplineCurve(const SplineGeometry& geometry, const std::string& layer) {
    // Create a copy for processing
    SplineGeometry processedGeometry = geometry;
    
    // Transform geometry
    TransformSplineGeometry(processedGeometry);
    
    // Validate the transformed geometry
    auto validation = ValidateSplineGeometry(processedGeometry);
    if (!validation.isValid) {
        LogError("Invalid B-spline geometry after transformation");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Check for degenerated Bezier curves
    if (m_enableDegenerationDetection && IsDegeneratedBezierCurve(processedGeometry)) {
        LogInfo("Detected degenerated Bezier curve, attempting conversion to line");
        if (ReplaceDegeneratedBezierWithLine(processedGeometry)) {
            m_convertedToLine++;
            LogInfo("Successfully converted degenerated Bezier to line");
            
            // Create a line instead of spline
            Point3d start, end;
            if (ConvertBezierToLine(processedGeometry.controlPoints, start, end)) {
                // Use line processor to create the line
                // This would require access to a line processor
                LogInfo("Created line from degenerated Bezier curve");
                return DWGProcessingStatus::Success;
            }
        }
    }
    
    // Remove redundant knots if needed
    if (validation.hasRedundantKnots) {
        LogInfo("Removing redundant knots from spline");
        if (RemoveRedundantKnots(processedGeometry)) {
            LogInfo("Successfully removed " + std::to_string(validation.redundantKnotCount) + " redundant knots");
        } else {
            LogWarning("Failed to remove redundant knots");
        }
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        // Create DWG spline entity
        AcDbSpline* spline = CreateDWGSpline(processedGeometry);
        if (!spline) {
            LogError("Failed to create DWG spline entity");
            return DWGProcessingStatus::Failed;
        }
        
        // Set properties
        if (!SetEntityProperties(spline, layer)) {
            delete spline;
            LogError("Failed to set spline properties");
            return DWGProcessingStatus::Failed;
        }
        
        // Add to model space
        if (!AddEntityToModelSpace(spline)) {
            delete spline;
            LogError("Failed to add spline to model space");
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating DWG spline: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - spline creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGSplineProcessor::ProcessInterpolationCurve(const std::vector<Point3d>& fitPoints, 
                                                                  const Vector3d& startTangent,
                                                                  const Vector3d& endTangent,
                                                                  const std::string& layer) {
    if (fitPoints.size() < 2) {
        LogError("Interpolation curve requires at least 2 fit points");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Validate fit points
    if (!ValidateControlPoints(fitPoints)) {
        LogError("Invalid fit points for interpolation curve");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Create B-spline from interpolation data
    SplineGeometry bsplineGeometry;
    if (!CreateInterpolationSpline(fitPoints, startTangent, endTangent, bsplineGeometry)) {
        LogError("Failed to create B-spline from interpolation data");
        return DWGProcessingStatus::ConversionError;
    }
    
#ifdef REALDWG_AVAILABLE
    try {
        AcDbSpline* spline = new AcDbSpline();
        
        // Set from fit points
        if (!SetSplineFromFitPoints(spline, fitPoints, startTangent, endTangent)) {
            delete spline;
            LogError("Failed to set spline from fit points");
            return DWGProcessingStatus::Failed;
        }
        
        if (!SetEntityProperties(spline, layer)) {
            delete spline;
            return DWGProcessingStatus::Failed;
        }
        
        if (!AddEntityToModelSpace(spline)) {
            delete spline;
            return DWGProcessingStatus::Failed;
        }
        
        return DWGProcessingStatus::Success;
    }
    catch (const std::exception& e) {
        LogError("Exception creating interpolation spline: " + std::string(e.what()));
        return DWGProcessingStatus::Failed;
    }
#else
    LogWarning("RealDWG not available - interpolation spline creation skipped");
    return DWGProcessingStatus::Skipped;
#endif
}

DWGProcessingStatus DWGSplineProcessor::ProcessNURBSCurve(const SplineGeometry& geometry, const std::string& layer) {
    // NURBS curves are rational B-splines with weights
    if (!geometry.isRational) {
        LogError("NURBS curve must be rational");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    if (geometry.weights.empty() || geometry.weights.size() != geometry.controlPoints.size()) {
        LogError("NURBS curve weights must match control points count");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Validate weights
    if (!ValidateWeights(geometry.weights, geometry.controlPoints.size())) {
        LogError("Invalid weights for NURBS curve");
        return DWGProcessingStatus::InvalidGeometry;
    }
    
    // Process as B-spline with weights
    return ProcessBSplineCurve(geometry, layer);
}

//=======================================================================================
// Validation Methods
//=======================================================================================

SplineValidationResult DWGSplineProcessor::ValidateSplineGeometry(const SplineGeometry& geometry) const {
    SplineValidationResult result;
    result.isValid = true;
    
    // Validate basic parameters
    if (geometry.degree < 1 || geometry.degree > 25) {
        result.AddSplineError("Invalid degree: " + std::to_string(geometry.degree));
    }
    
    if (geometry.controlPoints.size() < geometry.degree + 1) {
        result.AddSplineError("Insufficient control points for degree " + std::to_string(geometry.degree));
    }
    
    // Validate control points
    if (!ValidateControlPoints(geometry.controlPoints)) {
        result.AddSplineError("Invalid control points");
    }
    
    // Validate knot vector
    if (!geometry.knots.empty()) {
        result.hasValidKnotVector = ValidateKnotVector(geometry.knots, geometry.degree, geometry.controlPoints.size());
        if (!result.hasValidKnotVector) {
            result.AddSplineError("Invalid knot vector");
        }
        
        // Check for redundant knots
        int redundantCount = 0;
        for (size_t i = 1; i < geometry.knots.size(); ++i) {
            if (IsKnotRedundant(geometry.knots, i, m_knotTolerance)) {
                redundantCount++;
            }
        }
        
        if (redundantCount > 0) {
            result.hasRedundantKnots = true;
            result.redundantKnotCount = redundantCount;
            result.AddSplineWarning("Found " + std::to_string(redundantCount) + " redundant knots");
        }
    }
    
    // Validate weights for rational curves
    if (geometry.isRational) {
        if (!ValidateWeights(geometry.weights, geometry.controlPoints.size())) {
            result.AddSplineError("Invalid weights for rational curve");
        }
    }
    
    // Check for degenerated Bezier curves
    if (m_enableDegenerationDetection) {
        result.isDegeneratedBezier = IsDegeneratedBezierCurve(geometry);
        if (result.isDegeneratedBezier) {
            result.AddSplineWarning("Detected degenerated Bezier curve");
        }
    }
    
    // Calculate complexity
    result.curveComplexity = CalculateCurveComplexity(geometry);
    result.complexity = result.curveComplexity;
    
    return result;
}

bool DWGSplineProcessor::ValidateKnotVector(const std::vector<double>& knots, int degree, int numControlPoints) const {
    // Expected number of knots: numControlPoints + degree + 1
    int expectedKnots = numControlPoints + degree + 1;
    
    if (static_cast<int>(knots.size()) != expectedKnots) {
        return false;
    }
    
    // Check if knots are non-decreasing
    for (size_t i = 1; i < knots.size(); ++i) {
        if (knots[i] < knots[i-1] - m_knotTolerance) {
            return false;
        }
    }
    
    // Check for valid parameter range
    if (knots.front() >= knots.back()) {
        return false;
    }
    
    // Use internal validation for more detailed checks
    return ValidateKnotVectorInternal(knots, degree, m_knotTolerance) >= 0;
}

bool DWGSplineProcessor::ValidateControlPoints(const std::vector<Point3d>& controlPoints) const {
    if (controlPoints.empty()) {
        return false;
    }
    
    // Check each control point
    for (const auto& point : controlPoints) {
        if (!ValidateCoordinate(point)) {
            return false;
        }
    }
    
    // Check for minimum spacing between consecutive points
    for (size_t i = 1; i < controlPoints.size(); ++i) {
        double dx = controlPoints[i].x - controlPoints[i-1].x;
        double dy = controlPoints[i].y - controlPoints[i-1].y;
        double dz = controlPoints[i].z - controlPoints[i-1].z;
        double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
        
        // Allow very close points but not identical ones
        if (distance < m_geometryTolerance * 0.1) {
            // Points are too close, but this might be acceptable in some cases
            // Just log a warning rather than failing validation
        }
    }
    
    return true;
}

bool DWGSplineProcessor::ValidateWeights(const std::vector<double>& weights, size_t numControlPoints) const {
    if (weights.size() != numControlPoints) {
        return false;
    }
    
    // Check that all weights are positive
    for (double weight : weights) {
        if (weight <= 0.0 || !std::isfinite(weight)) {
            return false;
        }
    }
    
    return true;
}

//=======================================================================================
// Knot Vector Processing (Based on RealDwgFileIO/rdSpline.cpp)
//=======================================================================================

bool DWGSplineProcessor::RemoveRedundantKnots(SplineGeometry& geometry) const {
    if (geometry.knots.empty() || geometry.controlPoints.empty()) {
        return false;
    }
    
    std::vector<double> originalKnots = geometry.knots;
    std::vector<Point3d> originalControlPoints = geometry.controlPoints;
    std::vector<double> originalWeights = geometry.weights;
    
    bool success = RemoveRedundantKnotsInternal(geometry.knots, geometry.controlPoints, 
                                               geometry.weights, geometry.degree, m_knotTolerance);
    
    if (success) {
        LogInfo("Removed redundant knots: " + 
                std::to_string(originalKnots.size() - geometry.knots.size()) + " knots removed");
    } else {
        // Restore original data if removal failed
        geometry.knots = originalKnots;
        geometry.controlPoints = originalControlPoints;
        geometry.weights = originalWeights;
        LogWarning("Failed to remove redundant knots, keeping original data");
    }
    
    return success;
}

int DWGSplineProcessor::ValidateKnotVectorInternal(const std::vector<double>& knots, int degree, double tolerance) const {
    // Based on RealDwgFileIO implementation
    // Returns: >= 0 for valid knot vector, -1 for invalid
    
    if (knots.size() < 2) {
        return -1;
    }
    
    // Check for non-decreasing sequence
    for (size_t i = 1; i < knots.size(); ++i) {
        if (knots[i] < knots[i-1] - tolerance) {
            return -1;
        }
    }
    
    // Check multiplicity constraints
    int maxMultiplicity = degree + 1;
    int currentMultiplicity = 1;
    
    for (size_t i = 1; i < knots.size(); ++i) {
        if (std::abs(knots[i] - knots[i-1]) <= tolerance) {
            currentMultiplicity++;
            if (currentMultiplicity > maxMultiplicity) {
                return -1; // Multiplicity too high
            }
        } else {
            currentMultiplicity = 1;
        }
    }
    
    return 0; // Valid knot vector
}

bool DWGSplineProcessor::RemoveRedundantKnotsInternal(std::vector<double>& knots, 
                                                     std::vector<Point3d>& controlPoints,
                                                     std::vector<double>& weights, 
                                                     int degree, 
                                                     double tolerance) const {
    // This is a simplified implementation of the complex algorithm from RealDwgFileIO
    // The full implementation would involve sophisticated knot removal algorithms
    
    if (knots.size() < 2 || controlPoints.empty()) {
        return false;
    }
    
    std::vector<double> newKnots;
    std::vector<Point3d> newControlPoints;
    std::vector<double> newWeights;
    
    // Simple redundant knot removal - remove consecutive identical knots beyond multiplicity
    newKnots.push_back(knots[0]);
    newControlPoints.push_back(controlPoints[0]);
    if (!weights.empty()) {
        newWeights.push_back(weights[0]);
    }
    
    int multiplicity = 1;
    size_t controlPointIndex = 1;
    
    for (size_t i = 1; i < knots.size(); ++i) {
        if (std::abs(knots[i] - knots[i-1]) <= tolerance) {
            multiplicity++;
            // Only keep if multiplicity doesn't exceed degree + 1
            if (multiplicity <= degree + 1) {
                newKnots.push_back(knots[i]);
            }
        } else {
            multiplicity = 1;
            newKnots.push_back(knots[i]);
            
            // Add corresponding control point
            if (controlPointIndex < controlPoints.size()) {
                newControlPoints.push_back(controlPoints[controlPointIndex]);
                if (!weights.empty() && controlPointIndex < weights.size()) {
                    newWeights.push_back(weights[controlPointIndex]);
                }
                controlPointIndex++;
            }
        }
    }
    
    // Update the geometry if we removed any knots
    if (newKnots.size() < knots.size()) {
        knots = newKnots;
        controlPoints = newControlPoints;
        weights = newWeights;
        return true;
    }
    
    return false;
}

bool DWGSplineProcessor::IsKnotRedundant(const std::vector<double>& knots, int index, double tolerance) const {
    if (index <= 0 || index >= static_cast<int>(knots.size())) {
        return false;
    }
    
    // Check if this knot is identical to the previous one within tolerance
    return std::abs(knots[index] - knots[index-1]) <= tolerance;
}

//=======================================================================================
// Bezier Curve Processing and Degeneration Detection
//=======================================================================================

bool DWGSplineProcessor::IsDegeneratedBezierCurve(const SplineGeometry& geometry) const {
    // Check if this is a Bezier curve (uniform knot vector with full multiplicity at ends)
    if (geometry.knots.empty() || geometry.controlPoints.size() != geometry.degree + 1) {
        return false;
    }

    // Check for Bezier knot vector pattern
    bool isBezier = true;
    int expectedMultiplicity = geometry.degree + 1;

    // Check start multiplicity
    for (int i = 1; i < expectedMultiplicity && i < static_cast<int>(geometry.knots.size()); ++i) {
        if (std::abs(geometry.knots[i] - geometry.knots[0]) > m_knotTolerance) {
            isBezier = false;
            break;
        }
    }

    // Check end multiplicity
    if (isBezier) {
        int endStart = geometry.knots.size() - expectedMultiplicity;
        for (int i = endStart + 1; i < static_cast<int>(geometry.knots.size()); ++i) {
            if (std::abs(geometry.knots[i] - geometry.knots.back()) > m_knotTolerance) {
                isBezier = false;
                break;
            }
        }
    }

    if (!isBezier) {
        return false;
    }

    // Check if control points are collinear (degenerated)
    return SplineUtils::ArePointsCollinear(geometry.controlPoints, m_curvatureTolerance);
}

bool DWGSplineProcessor::ReplaceDegeneratedBezierWithLine(SplineGeometry& geometry) const {
    if (!IsDegeneratedBezierCurve(geometry)) {
        return false;
    }

    Point3d start, end;
    if (!ConvertBezierToLine(geometry.controlPoints, start, end)) {
        return false;
    }

    // Replace with linear B-spline (degree 1)
    geometry.degree = 1;
    geometry.controlPoints = {start, end};
    geometry.knots = {0.0, 0.0, 1.0, 1.0}; // Clamped linear knot vector
    geometry.weights.clear(); // Remove weights for linear curve
    geometry.isRational = false;

    return true;
}

bool DWGSplineProcessor::ConvertBezierToLine(const std::vector<Point3d>& controlPoints, Point3d& start, Point3d& end) const {
    if (controlPoints.size() < 2) {
        return false;
    }

    start = controlPoints.front();
    end = controlPoints.back();

    return true;
}

//=======================================================================================
// Geometric Analysis and Complexity Calculation
//=======================================================================================

double DWGSplineProcessor::CalculateCurveComplexity(const SplineGeometry& geometry) const {
    double complexity = 0.0;

    // Factor in degree
    complexity += geometry.degree * 0.1;

    // Factor in number of control points
    complexity += geometry.controlPoints.size() * 0.05;

    // Factor in knot vector complexity
    if (!geometry.knots.empty()) {
        complexity += CalculateKnotSpacing(geometry.knots) * 0.2;
    }

    // Factor in rationality
    if (geometry.isRational) {
        complexity += 0.3;
    }

    // Factor in closure
    if (geometry.isClosed || geometry.isPeriodic) {
        complexity += 0.2;
    }

    return complexity;
}

double DWGSplineProcessor::CalculateKnotSpacing(const std::vector<double>& knots) const {
    if (knots.size() < 2) {
        return 0.0;
    }

    double totalVariation = 0.0;
    double averageSpacing = (knots.back() - knots.front()) / (knots.size() - 1);

    for (size_t i = 1; i < knots.size(); ++i) {
        double spacing = knots[i] - knots[i-1];
        double deviation = std::abs(spacing - averageSpacing);
        totalVariation += deviation;
    }

    return totalVariation / (knots.size() - 1);
}

bool DWGSplineProcessor::HasSufficientCurvature(const std::vector<Point3d>& controlPoints, double tolerance) const {
    if (controlPoints.size() < 3) {
        return true; // Linear segments always have sufficient "curvature" for their purpose
    }

    // Check curvature at several points along the control polygon
    for (size_t i = 1; i < controlPoints.size() - 1; ++i) {
        double curvature = SplineUtils::CalculateCurvature(controlPoints[i-1], controlPoints[i], controlPoints[i+1]);
        if (curvature > tolerance) {
            return true;
        }
    }

    return false;
}

//=======================================================================================
// Transformation and Coordinate Processing
//=======================================================================================

void DWGSplineProcessor::TransformSplineGeometry(SplineGeometry& geometry) const {
    // Transform control points
    for (auto& point : geometry.controlPoints) {
        point = TransformPoint(point);
    }

    // Transform fit points if present
    for (auto& point : geometry.fitPoints) {
        point = TransformPoint(point);
    }

    // Transform tangent vectors
    if (geometry.hasStartTangent) {
        geometry.startTangent = TransformVector(geometry.startTangent);
    }
    if (geometry.hasEndTangent) {
        geometry.endTangent = TransformVector(geometry.endTangent);
    }

    // Validate and fix coordinates after transformation
    ValidateAndFixControlPoints(geometry.controlPoints);
    ValidateAndFixControlPoints(geometry.fitPoints);
    ValidateAndFixKnots(geometry.knots);
}

void DWGSplineProcessor::ValidateAndFixControlPoints(std::vector<Point3d>& controlPoints) const {
    for (auto& point : controlPoints) {
        // Fix invalid elevations
        CoerceInvalidElevation(point.z);

        // Fix invalid coordinates
        if (!IsValidCoordinate(point.x)) point.x = 0.0;
        if (!IsValidCoordinate(point.y)) point.y = 0.0;
        if (!IsValidCoordinate(point.z)) point.z = 0.0;
    }
}

void DWGSplineProcessor::ValidateAndFixKnots(std::vector<double>& knots) const {
    for (auto& knot : knots) {
        if (!std::isfinite(knot)) {
            knot = 0.0;
        }
    }

    // Ensure knots are non-decreasing
    std::sort(knots.begin(), knots.end());
}

//=======================================================================================
// Interpolation Curve Creation
//=======================================================================================

bool DWGSplineProcessor::CreateInterpolationSpline(const std::vector<Point3d>& fitPoints,
                                                   const Vector3d& startTangent,
                                                   const Vector3d& endTangent,
                                                   SplineGeometry& result) const {
    if (fitPoints.size() < 2) {
        return false;
    }

    // Calculate chord parameterization
    std::vector<double> parameters;
    if (!CalculateChordParameterization(fitPoints, parameters)) {
        return false;
    }

    // Set up interpolation problem
    int n = fitPoints.size();
    int degree = std::min(3, n - 1); // Use cubic if possible, otherwise lower degree

    // Create knot vector for interpolation
    std::vector<double> knots;
    int numKnots = n + degree + 1;
    knots.resize(numKnots);

    // Clamped knot vector
    for (int i = 0; i <= degree; ++i) {
        knots[i] = 0.0;
        knots[numKnots - 1 - i] = 1.0;
    }

    // Internal knots based on parameters
    for (int i = 1; i < n - degree; ++i) {
        double sum = 0.0;
        for (int j = i; j < i + degree; ++j) {
            sum += parameters[j];
        }
        knots[i + degree] = sum / degree;
    }

    // Set up result
    result.degree = degree;
    result.knots = knots;
    result.fitPoints = fitPoints;
    result.hasStartTangent = (startTangent.x != 0 || startTangent.y != 0 || startTangent.z != 0);
    result.hasEndTangent = (endTangent.x != 0 || endTangent.y != 0 || endTangent.z != 0);
    result.startTangent = startTangent;
    result.endTangent = endTangent;
    result.isRational = false;
    result.isPeriodic = false;
    result.isClosed = false;

    // For now, use fit points as control points (simplified)
    // A full implementation would solve the interpolation system
    result.controlPoints = fitPoints;

    return true;
}

bool DWGSplineProcessor::CalculateChordParameterization(const std::vector<Point3d>& points, std::vector<double>& parameters) const {
    if (points.size() < 2) {
        return false;
    }

    parameters.resize(points.size());
    parameters[0] = 0.0;

    double totalLength = 0.0;
    std::vector<double> chordLengths(points.size() - 1);

    // Calculate chord lengths
    for (size_t i = 1; i < points.size(); ++i) {
        double dx = points[i].x - points[i-1].x;
        double dy = points[i].y - points[i-1].y;
        double dz = points[i].z - points[i-1].z;
        chordLengths[i-1] = std::sqrt(dx*dx + dy*dy + dz*dz);
        totalLength += chordLengths[i-1];
    }

    if (totalLength < m_geometryTolerance) {
        return false; // All points are essentially the same
    }

    // Calculate normalized parameters
    double cumulativeLength = 0.0;
    for (size_t i = 1; i < points.size(); ++i) {
        cumulativeLength += chordLengths[i-1];
        parameters[i] = cumulativeLength / totalLength;
    }

    return true;
}

//=======================================================================================
// Validation and Repair Methods
//=======================================================================================

bool DWGSplineProcessor::ValidateAndRepairSpline(SplineGeometry& geometry) const {
    bool repaired = false;

    // Fix control points
    size_t originalPointCount = geometry.controlPoints.size();
    ValidateAndFixControlPoints(geometry.controlPoints);
    if (geometry.controlPoints.size() != originalPointCount) {
        repaired = true;
    }

    // Fix knot vector
    if (!geometry.knots.empty()) {
        std::vector<double> originalKnots = geometry.knots;
        ValidateAndFixKnots(geometry.knots);
        if (geometry.knots != originalKnots) {
            repaired = true;
        }

        // Repair knot vector structure if needed
        if (!ValidateKnotVector(geometry.knots, geometry.degree, geometry.controlPoints.size())) {
            if (SplineUtils::RepairKnotVector(geometry.knots, geometry.degree, geometry.controlPoints.size())) {
                repaired = true;
            }
        }
    }

    // Fix weights for rational curves
    if (geometry.isRational && !geometry.weights.empty()) {
        std::vector<double> originalWeights = geometry.weights;
        SplineUtils::RepairWeights(geometry.weights);
        if (geometry.weights != originalWeights) {
            repaired = true;
        }
    }

    // Remove redundant knots
    if (RemoveRedundantKnots(geometry)) {
        repaired = true;
    }

    // Handle degenerated Bezier curves
    if (IsDegeneratedBezierCurve(geometry)) {
        if (ReplaceDegeneratedBezierWithLine(geometry)) {
            repaired = true;
        }
    }

    return repaired;
}

#ifdef REALDWG_AVAILABLE
//=======================================================================================
// RealDWG Specific Implementation
//=======================================================================================

AcDbSpline* DWGSplineProcessor::CreateDWGSpline(const SplineGeometry& geometry) const {
    try {
        AcDbSpline* spline = new AcDbSpline();

        if (geometry.isRational) {
            // Create NURBS curve
            if (!SetSplineFromNURBS(spline, geometry)) {
                delete spline;
                return nullptr;
            }
        } else {
            // Create B-spline curve
            if (!SetSplineFromBSplineCurve(spline, geometry)) {
                delete spline;
                return nullptr;
            }
        }

        return spline;
    }
    catch (const std::exception& e) {
        LogError("Exception in CreateDWGSpline: " + std::string(e.what()));
        return nullptr;
    }
}

bool DWGSplineProcessor::SetSplineFromBSplineCurve(AcDbSpline* spline, const SplineGeometry& geometry) const {
    if (!spline) return false;

    try {
        // Convert to AcGe types
        AcGePoint3dArray controlPoints = ToAcGePoint3dArray(geometry.controlPoints);
        AcGeDoubleArray knots = ToAcGeDoubleArray(geometry.knots);

        // Create NURB curve
        AcGeNurbCurve3d* nurbCurve = CreateAcGeNurbCurve(geometry);
        if (!nurbCurve) {
            return false;
        }

        // Set spline from NURB curve
        Acad::ErrorStatus es = spline->setFromOtherCurve(*nurbCurve);
        delete nurbCurve;

        return HandleSplineCreationError(es, "SetSplineFromBSplineCurve");
    }
    catch (const std::exception& e) {
        LogError("Exception in SetSplineFromBSplineCurve: " + std::string(e.what()));
        return false;
    }
}

AcGeNurbCurve3d* DWGSplineProcessor::CreateAcGeNurbCurve(const SplineGeometry& geometry) const {
    try {
        AcGePoint3dArray controlPoints = ToAcGePoint3dArray(geometry.controlPoints);
        AcGeDoubleArray knots = ToAcGeDoubleArray(geometry.knots);
        AcGeDoubleArray weights;

        if (geometry.isRational && !geometry.weights.empty()) {
            weights = ToAcGeDoubleArray(geometry.weights);
        }

        AcGeInterval interval(geometry.knots.front(), geometry.knots.back());

        if (geometry.isRational) {
            return new AcGeNurbCurve3d(geometry.degree, knots, controlPoints, weights,
                                      geometry.isPeriodic, interval);
        } else {
            return new AcGeNurbCurve3d(geometry.degree, knots, controlPoints,
                                      geometry.isPeriodic, interval);
        }
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

AcGePoint3dArray DWGSplineProcessor::ToAcGePoint3dArray(const std::vector<Point3d>& points) const {
    AcGePoint3dArray acPoints;
    acPoints.setLogicalLength(points.size());

    for (size_t i = 0; i < points.size(); ++i) {
        acPoints[i] = AcGePoint3d(points[i].x, points[i].y, points[i].z);
    }

    return acPoints;
}

AcGeDoubleArray DWGSplineProcessor::ToAcGeDoubleArray(const std::vector<double>& values) const {
    AcGeDoubleArray acValues;
    acValues.setLogicalLength(values.size());

    for (size_t i = 0; i < values.size(); ++i) {
        acValues[i] = values[i];
    }

    return acValues;
}

bool DWGSplineProcessor::HandleSplineCreationError(Acad::ErrorStatus status, const std::string& operation) const {
    if (status == Acad::eOk) {
        return true;
    }

    std::string errorMsg = operation + " failed with status: " + std::to_string(static_cast<int>(status));
    LogError(errorMsg);
    return false;
}
#endif

//=======================================================================================
// Spline Utility Functions Implementation
//=======================================================================================

bool SplineUtils::IsValidKnotVector(const std::vector<double>& knots, int degree, int numControlPoints) {
    // Expected number of knots: numControlPoints + degree + 1
    int expectedKnots = numControlPoints + degree + 1;

    if (static_cast<int>(knots.size()) != expectedKnots) {
        return false;
    }

    // Check if knots are non-decreasing
    for (size_t i = 1; i < knots.size(); ++i) {
        if (knots[i] < knots[i-1]) {
            return false;
        }
    }

    // Check for valid parameter range
    return knots.front() < knots.back();
}

bool SplineUtils::IsUniformKnotVector(const std::vector<double>& knots, double tolerance) {
    if (knots.size() < 2) {
        return false;
    }

    double expectedSpacing = (knots.back() - knots.front()) / (knots.size() - 1);

    for (size_t i = 1; i < knots.size(); ++i) {
        double actualSpacing = knots[i] - knots[i-1];
        if (std::abs(actualSpacing - expectedSpacing) > tolerance) {
            return false;
        }
    }

    return true;
}

bool SplineUtils::IsClampedKnotVector(const std::vector<double>& knots, int degree, double tolerance) {
    if (knots.size() < 2 * (degree + 1)) {
        return false;
    }

    // Check start clamping
    for (int i = 1; i <= degree; ++i) {
        if (std::abs(knots[i] - knots[0]) > tolerance) {
            return false;
        }
    }

    // Check end clamping
    int endStart = knots.size() - degree - 1;
    for (int i = endStart + 1; i < static_cast<int>(knots.size()); ++i) {
        if (std::abs(knots[i] - knots.back()) > tolerance) {
            return false;
        }
    }

    return true;
}

double SplineUtils::CalculateChordLength(const std::vector<Point3d>& points) {
    if (points.size() < 2) {
        return 0.0;
    }

    double totalLength = 0.0;
    for (size_t i = 1; i < points.size(); ++i) {
        double dx = points[i].x - points[i-1].x;
        double dy = points[i].y - points[i-1].y;
        double dz = points[i].z - points[i-1].z;
        totalLength += std::sqrt(dx*dx + dy*dy + dz*dz);
    }

    return totalLength;
}

double SplineUtils::CalculateCurvature(const Point3d& p1, const Point3d& p2, const Point3d& p3) {
    // Calculate curvature at p2 using three points
    Vector3d v1(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Vector3d v2(p3.x - p2.x, p3.y - p2.y, p3.z - p2.z);

    // Cross product magnitude
    double crossX = v1.y * v2.z - v1.z * v2.y;
    double crossY = v1.z * v2.x - v1.x * v2.z;
    double crossZ = v1.x * v2.y - v1.y * v2.x;
    double crossMag = std::sqrt(crossX*crossX + crossY*crossY + crossZ*crossZ);

    // Vector magnitudes
    double v1Mag = std::sqrt(v1.x*v1.x + v1.y*v1.y + v1.z*v1.z);
    double v2Mag = std::sqrt(v2.x*v2.x + v2.y*v2.y + v2.z*v2.z);

    if (v1Mag < 1e-10 || v2Mag < 1e-10) {
        return 0.0;
    }

    // Curvature = |v1 × v2| / (|v1| * |v2|)^(3/2)
    double denominator = std::pow(v1Mag * v2Mag, 1.5);
    return crossMag / denominator;
}

bool SplineUtils::ArePointsCollinear(const std::vector<Point3d>& points, double tolerance) {
    if (points.size() < 3) {
        return true; // Two or fewer points are always collinear
    }

    // Check if all points lie on the line defined by first and last points
    Point3d start = points.front();
    Point3d end = points.back();

    Vector3d lineDir(end.x - start.x, end.y - start.y, end.z - start.z);
    double lineLength = std::sqrt(lineDir.x*lineDir.x + lineDir.y*lineDir.y + lineDir.z*lineDir.z);

    if (lineLength < tolerance) {
        // Start and end points are the same, check if all points are at the same location
        for (const auto& point : points) {
            double dx = point.x - start.x;
            double dy = point.y - start.y;
            double dz = point.z - start.z;
            double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
            if (distance > tolerance) {
                return false;
            }
        }
        return true;
    }

    // Normalize line direction
    lineDir.x /= lineLength;
    lineDir.y /= lineLength;
    lineDir.z /= lineLength;

    // Check each intermediate point
    for (size_t i = 1; i < points.size() - 1; ++i) {
        Vector3d toPoint(points[i].x - start.x, points[i].y - start.y, points[i].z - start.z);

        // Calculate distance from point to line
        double projection = toPoint.x * lineDir.x + toPoint.y * lineDir.y + toPoint.z * lineDir.z;
        Vector3d projectedPoint(start.x + projection * lineDir.x,
                               start.y + projection * lineDir.y,
                               start.z + projection * lineDir.z);

        double dx = points[i].x - projectedPoint.x;
        double dy = points[i].y - projectedPoint.y;
        double dz = points[i].z - projectedPoint.z;
        double distance = std::sqrt(dx*dx + dy*dy + dz*dz);

        if (distance > tolerance) {
            return false;
        }
    }

    return true;
}

bool SplineUtils::ValidateSplineParameters(int degree, int numControlPoints, int numKnots) {
    if (degree < 1 || degree > 25) {
        return false;
    }

    if (numControlPoints < degree + 1) {
        return false;
    }

    int expectedKnots = numControlPoints + degree + 1;
    return numKnots == expectedKnots;
}

bool SplineUtils::ValidateParametricRange(const std::vector<double>& knots, double startParam, double endParam) {
    if (knots.empty()) {
        return false;
    }

    double knotStart = knots.front();
    double knotEnd = knots.back();

    return startParam >= knotStart && endParam <= knotEnd && startParam < endParam;
}

bool SplineUtils::RepairKnotVector(std::vector<double>& knots, int degree, int numControlPoints) {
    int expectedKnots = numControlPoints + degree + 1;

    if (static_cast<int>(knots.size()) != expectedKnots) {
        // Recreate knot vector
        knots.clear();
        knots.resize(expectedKnots);

        // Create clamped uniform knot vector
        for (int i = 0; i <= degree; ++i) {
            knots[i] = 0.0;
            knots[expectedKnots - 1 - i] = 1.0;
        }

        // Internal knots
        int numInternalKnots = expectedKnots - 2 * (degree + 1);
        for (int i = 0; i < numInternalKnots; ++i) {
            knots[degree + 1 + i] = static_cast<double>(i + 1) / (numInternalKnots + 1);
        }

        return true;
    }

    // Sort existing knots
    std::sort(knots.begin(), knots.end());

    // Normalize to [0, 1] range
    double minKnot = knots.front();
    double maxKnot = knots.back();
    double range = maxKnot - minKnot;

    if (range > 1e-10) {
        for (auto& knot : knots) {
            knot = (knot - minKnot) / range;
        }
    }

    return true;
}

bool SplineUtils::RepairControlPoints(std::vector<Point3d>& controlPoints, double tolerance) {
    if (controlPoints.empty()) {
        return false;
    }

    bool repaired = false;

    // Remove duplicate consecutive points
    auto it = std::unique(controlPoints.begin(), controlPoints.end(),
        [tolerance](const Point3d& a, const Point3d& b) {
            double dx = a.x - b.x;
            double dy = a.y - b.y;
            double dz = a.z - b.z;
            double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
            return distance < tolerance;
        });

    if (it != controlPoints.end()) {
        controlPoints.erase(it, controlPoints.end());
        repaired = true;
    }

    // Fix invalid coordinates
    for (auto& point : controlPoints) {
        if (!std::isfinite(point.x)) { point.x = 0.0; repaired = true; }
        if (!std::isfinite(point.y)) { point.y = 0.0; repaired = true; }
        if (!std::isfinite(point.z)) { point.z = 0.0; repaired = true; }
    }

    return repaired;
}

bool SplineUtils::RepairWeights(std::vector<double>& weights, double minWeight) {
    if (weights.empty()) {
        return false;
    }

    bool repaired = false;

    for (auto& weight : weights) {
        if (!std::isfinite(weight) || weight <= 0.0) {
            weight = 1.0;
            repaired = true;
        } else if (weight < minWeight) {
            weight = minWeight;
            repaired = true;
        }
    }

    return repaired;
}

} // namespace IModelExport
